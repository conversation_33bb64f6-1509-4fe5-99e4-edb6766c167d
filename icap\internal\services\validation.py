import io
from datetime import date

import pandas as pd
from cognite.client import Cognite<PERSON>lient
from cognite.client.data_classes import Transformation

from ..constants import RAW_DB, VALIDATION_INFO
from .notification import TeamsNotificationInput, TeamsNotificationService


class ValidationService:
    def __init__(
        self,
        cognite_client: CogniteClient,
        notification_service: TeamsNotificationService,
    ):
        self._cognite_client = cognite_client
        self._notification_service = notification_service

    def validate(self):
        in_memory_fp = io.BytesIO()
        with pd.ExcelWriter(in_memory_fp, engine="xlsxwriter") as writer:
            for (
                transformation_external_id,
                table_name,
                use_job_date,
                reorder_columns,
                clear_table_afterwards,
            ) in VALIDATION_INFO:
                print("Processing ", table_name)

                transformation = self._cognite_client.transformations.retrieve(
                    external_id=transformation_external_id
                )

                if transformation is None:
                    continue

                df = self._get_df(
                    table_name, use_job_date, transformation, reorder_columns
                )

                print(table_name, len(df.index))

                df.to_excel(writer, sheet_name=table_name)

                if clear_table_afterwards:
                    self._clear_raw_table(table_name)

        in_memory_fp.seek(0)

        data_set = self._cognite_client.data_sets.retrieve(
            external_id="AIM-COR-ALL-DAT"
        )
        file = self._cognite_client.files.upload_bytes(
            content=in_memory_fp.getvalue(),
            name=f"AIM-COR-ALL-ICAP-VAL-{date.today().isoformat()}.xlsx",
            external_id=f"AIM-COR-ALL-ICAP-VAL-{date.today().isoformat()}",
            overwrite=True,
            data_set_id=data_set.id if data_set else None,
        )

        self._send_notification_message(file.id, file.name)

    def _send_notification_message(self, file_id: int, file_name: str) -> str:
        title = f"(Data Quality - {self._cognite_client.config.project})"
        activity_title = f"File: {file_name}"
        file_uri = self._generate_file_url(file_id)
        activity_messages: list[str] = [f"Link: {file_uri}"]

        request = TeamsNotificationInput(
            title=title,
            text=activity_title,
            activity_messages=activity_messages,
            button_text="Log File",
            button_action_link=file_uri,
        )

        self._notification_service.send_message(request)

    def _generate_file_url(self, file_id: int) -> str:
        project = self._cognite_client.config.project
        return f"https://celanese.fusion.cognite.com/{project}/explore/search?cluster=az-eastus-1.cognitedata.com&journey=file-{file_id}&workspace=data-management&selected-tab=preview"

    def _get_df(
        self,
        table_name: str,
        use_job_date: bool,
        transformation: Transformation,
        reorder_columns: bool,
    ):
        df = self._cognite_client.raw.rows.retrieve_dataframe(
            RAW_DB,
            table_name,
            min_last_updated_time=(
                transformation.last_finished_job.started_time if use_job_date else None
            ),
            limit=None,
        )

        df = df.fillna("")

        if reorder_columns:
            sorted_columns = sorted(df.columns, key=lambda col: int(col.split("_")[0]))
            df = df[sorted_columns]
            df = df.rename(
                {col: col[col.find("_") + 1 :] for col in df.columns}, axis="columns"
            )

        return df

    def _clear_raw_table(self, table_name: str):
        self._cognite_client.raw.tables.delete(db_name=RAW_DB, name=table_name)
        self._cognite_client.raw.tables.create(db_name=RAW_DB, name=table_name)
