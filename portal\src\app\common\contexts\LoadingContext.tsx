import { createContext, useContext } from 'react'

export type LoadingContextType = {
    showLoading: (value: boolean) => void
}

export const LoadingContext = createContext<LoadingContextType | undefined>(undefined)

export const useLoading = () => {
    const context = useContext(LoadingContext)
    if (!context) {
        throw new Error('useLoading must be used inside LoadingProvider')
    }
    return context
}
