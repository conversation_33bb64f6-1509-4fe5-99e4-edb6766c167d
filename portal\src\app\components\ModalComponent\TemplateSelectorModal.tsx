import React, { useCallback, useContext, useEffect, useState } from 'react'
import { ModalWrapper } from './Modal/ModalWrapper'
import { Box, styled, useMediaQuery, useTheme } from '@mui/material'
import { ClnButton } from '@celanese/ui-lib'
import GenericAutocomplete, { AutocompleteOption } from '../FieldsComponent/GenericAutocomplete'
import { useForm } from 'react-hook-form'
import { z } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'
import { AzureFunctionClient } from '@/app/common/clients/azure-function-client'
import { UserManagementContext, UserManagementContextState } from '@celanese/celanese-ui'
import { translate } from '@/app/common/utils/generate-translate'

interface TemplateSelectorModalProps {
    siteId: string
    onSelectTemplate: (template: string) => void
    onCancel: () => void
}

const templateSchema = z.object({
    template: z.string(),
})

type TemplateSchema = z.infer<typeof templateSchema>

const Form = styled('form')({
    width: '100% !important',
    '@media (max-width:600px)': {
        padding: '1.5rem',
    },
})

const actionButtonsStyle = {
    display: 'flex',
    justifyContent: 'flex-end',
    gap: 1,
    '@media (max-width:600px)': {
        flexDirection: 'column',
        gap: '0.5rem',
        alignItems: 'center',
    },
}

export const TemplateSelectorModal: React.FC<TemplateSelectorModalProps> = ({ siteId, onSelectTemplate, onCancel }) => {
    const { userInfo } = useContext<UserManagementContextState>(UserManagementContext)
    const theme = useTheme()
    const isMobile = useMediaQuery(theme.breakpoints.down('md'))

    const [templates, setTemplates] = useState<AutocompleteOption[]>([])
    const [isLoading, setIsLoading] = useState<boolean>(true)

    const { setValue, getValues, handleSubmit, reset, control } = useForm<TemplateSchema>({
        defaultValues: { template: undefined },
        resolver: zodResolver(templateSchema),
    })

    const handleConfirm = () => {
        const { template } = getValues()
        if (template) {
            onSelectTemplate(template)
        }
    }

    const handleCancel = () => {
        reset({ template: undefined })
        onCancel()
    }

    const fetchTemplates = useCallback(async () => {
        try {
            const client = new AzureFunctionClient()

            const result = await client.getTemplatesByUserId({
                reportingSiteExternalId: siteId,
                activeUserEmail: userInfo.email,
            })

            const transformedTemplates = result.items.map((item: any) => ({
                label: item.name,
                value: item.externalId,
            }))

            setTemplates(transformedTemplates)
        } catch (err) {
        } finally {
            setIsLoading(false)
        }
    }, [userInfo])

    useEffect(() => {
        fetchTemplates()
    }, [fetchTemplates])

    return (
        <ModalWrapper
            title={translate('requestModal.createFromTemplate')}
            openModal={true}
            closeModal={handleCancel}
            sxProps={{ backgroundColor: 'rgba(0, 0, 0, 0.5)' }}
            sxPropsTitle={{ fontSize: '19px', marginRight: 'auto' }}
            content={
                <Box
                    sx={{
                        width: isMobile ? '90vw' : '80vw',
                        maxWidth: '827px',
                        padding: isMobile ? '10px' : '20px',
                        fontFamily: 'Roboto',
                    }}
                >
                    <Form onSubmit={handleSubmit(handleConfirm)} id="template-selector-form">
                        <GenericAutocomplete
                            name="template"
                            control={control}
                            options={templates}
                            onChange={(newValue) => setValue('template', newValue)}
                            label={translate('stepper.form.template.template')}
                            sx={{ marginBottom: isMobile ? 3 : 5 }}
                            loading={isLoading}
                            multiple={false}
                            disableCloseOnSelect={false}
                            data-test="new_action_item_menu_template_modal-template_field"
                            data-origin="aim"
                        />
                        <Box sx={actionButtonsStyle}>
                            <ClnButton
                                variant="text"
                                label={translate('requestModal.cancel')}
                                onClick={handleCancel}
                                color="error"
                                data-test="new_action_item_menu_template_modal-cancel_button"
                                data-origin="ui-lib"
                            />
                            <ClnButton
                                type="submit"
                                variant="contained"
                                label={translate('requestModal.createFromTemplate')}
                                data-test="new_action_item_menu_template_modal-create_from_template_button"
                                data-origin="ui-lib"
                            />
                        </Box>
                    </Form>
                </Box>
            }
            dataTest="new_action_item_menu_template_modal-close_button"
        />
    )
}
