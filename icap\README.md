## Environment setup
### Virtual environment
A virtual environment can be created by executing the following commands in sequence (Windows), before doing that, one needs to ensure to be properly cd'd into the icap folder.

1. 
    ```
    python venv .venv
    ```
2.
    ```cmd
    cd .venv/Scripts
    ```
3. 
    ```cmd
    activate
    ```
4. 
    ```cmd
    cd ../..
    ```
5.
    ```cmd
    pip install -r requirements.txt
    ```

For the full documentation of python's venv library one can follow [this link](https://docs.python.org/3/library/venv.html#creating-virtual-environments).

### .env
Before using any method within this folder, one needs to properly set the credentials in the .env file: CLIENT_ID, SECRET, PROJECT for any changes not related to transformations and TRANSFORMATIONS_CLIENT_ID, TRANSFORMATIONS_CLIENT_SECRET and TRANSFORMATIONS_PROJECT if any changes to transformations will be made.

## Reference Data
Reference data here means any static data that is used in the migration progress. All of them are stored under the AIM-COR database.

The configuration of which raw tables will be created can be found in the config.yml file, under the reference_data section. The reference_data/AIM-COR.xlsx spreadsheet holds the content of each table that will be pushed to AIM-COR.

### How to upsert the reference data
- Existing tables
    1. Update the reference_data/AIM-COR.xlsx file
    2. Run the `python -m main reference-data` command
- New tables
    1. Add a new tab in reference_data/AIM-COR.xlsx with the table's name
    2. Add a new item in the "workbooks" list under the config.yml/reference_data section with the table's name and any struct column (a column that is a json-like object)
    3. Run the `python -m main reference-data` command

### How to ensure non-reference data tables are created
This is specially useful when implementing new flows or pushing new flows to other environments
1. Add the table name to the "ensure_exists" list under the config.yml/reference_data section
2. Run the `python -m main reference-data` command


## Transformations
Transformations are cognite resources that can fetch data from different data sources within cognite and build a resulting query targeting one (and only one) resource such as a raw table, or a data-model type, or files, etc. To understand more about transformations, one can follow [this link](https://docs.cognite.com/pt/cdf/integration/concepts/transformation/).

There are two transformations folder: transformations and transformations_prod, because there are different versions of them on dev/stg and prod, since dev/stg does not have private actions/events migrated.

### How to upsert transformations
This process is applicable to both transformations and transformations_prod, before executing any command ensure that your .env is properly targeting the correct environment.

1. Update the yamls under the transformations folder 
    - To add new yamls (or update the existing) one can use the "Download CLI Manifest" button in the Cognite portal:
    ![alt text](images/download_transformation_cli_manifest.png)
    - For transformations targeting data-models, ensure the "version" field in the yaml have its value as a string, e.g.: version: 5_1_3 needs to be version: "5_1_3". There is a bug in the CLI Manifest download that format the version as a number instead of string
2. Install cognite-transformations-cli by executing `pip install cognite-transformations-cli`
3. cd into the transformations folder
4. Run the `python append_authentication_to_yamls.py` command, which appends an authentication section to all yamls. This change does not need to be versioned and is required by the transformations-cli module but does not come when you download the CLI Manifest through Cognite's UI 
5. Run the `transformations-cli deploy` command
6. If one is using the same virtual environment that was created following the the [Virtual Environment section](#virtual-environment) one will need to uninstall the cognite-transformations-cli, since it uses an older version of cognite-sdk, this can be done by running the following commands:
    1. `pip uninstall cognite-transformations-cli`
    2. `cd ..` (or cd into the root of icap folder)
    3. `pip install -r requirements.txt`

## Cognite Functions
Functions are cognite resources that runs user-defined code using their infrastructure. For more information one can follow [this link](https://docs.cognite.com/pt/cdf/functions/).

### How to deploy functions
- Existing functions
    1. Ensure the function code is properly changed and tested
    2. Execute the `python -m main deploy-functions` command
- New function
    1. Add the function code and ensure it is properly tested (use existing functions as reference)
    2. Create a file with the `handle` method as explained in Cognite's documentation
    3. Add the function configuration as a new item under the functions section within the config.yml file, ensure the "function_path" field points to the file containing the `handle` method
    4. Execute the `python -m main deploy-functions` command
    
## Workflows
Workflows are cognite resources that can be used to create execution flows using mainly transformations and functions, but also other types of cognite resources. By that, one can define an order for each execution and create dependencies between execution tasks, e.g., execute Transformation1 and Transformation2 and only execute Function1 after Transformation1 and Transformation2 are executed and if both have executed successfully.

For more information one can follow [this link](https://docs.cognite.com/pt/cdf/data_workflows/).

Workflows are stored under the "workflows" folder, the content of the "tasks" field can be copied directly from Cognite's UI:
![alt text](images/get_workflow_tasks_json.png)

### How to publish workflows
- Update existing workflows
    1. Change the workflow json under the "workflows" folder, ensure the version is increased, otherwise the workflow will not be updated
    2. Run the `python -m main publish-workflows` command
        - If one wants to override an existing workflow (keep same version) the command can be executed using the `--force` option, e.g., `python -m main publish-workflows --force wf-AIM-COR-ICAP-MIGRATION`
- Creating new workflows
    1. Add the workflow json to the workflows folder (use existing workflows as reference) and ensure it is properly tested
    2. Run the `python -m main publish-workflows` command
    
### Validating workflows
To validate the workflows without trying to publish them, one can just execute the following command:
```cmd
python -m main publish-workflows --dry-run
```


## Migration progress report
The migration progress report is a per-site report that shows all actions and events from each site and their migration status to AIM. It needs to be generated locally as it involves fetching a high volume of data, which would not be possible through cognite functions due to their limited computational resources.

### How to generate the report
Simply run the following command and the reports will be generated under the "outputs" folder. Be aware that it can take a while and highly depends on the machine that is running it:

```cmd
python -m main migration-progress-report 
```