'use client'
import { NoTranslate } from '@celanese/celanese-ui'
import { Box, Tooltip, useMediaQuery, useTheme } from '@mui/material'
import { useCallback, useEffect, useMemo, useState } from 'react'

import { AzureFunctionClient } from '@/app/common/clients/azure-function-client'
import { useBulkUpload } from '@/app/common/contexts/BulkUploadContext'
import { useLoading } from '@/app/common/contexts/LoadingContext'
import { useDebounceFunction } from '@/app/common/hooks/general-functions/useDebounce'
import { ActionDetailItem, ResponseData } from '@/app/common/models/action-detail'
import { AutocompleteOption } from '../FieldsComponent/GenericAutocomplete'
import { DEFAULT_SORT_DIRECTION, DEFAULT_SORT_FIELD, ROWS_PER_PAGE_OPTIONS } from '@/app/common/utils'
import { determineActiveFilterCount } from '@/app/common/utils/active-filter-count'
import { formatDisplayDueDate, generateDueDateColor } from '@/app/common/utils/date'
import { useExportToExcel } from '@/app/common/hooks/export/useExportToExcel '
import { translate } from '@/app/common/utils/generate-translate'
import { IconWithTooltip } from '@/app/common/utils/icons-helper'
import { getNullsLastStringOrNumberComparator, getNullsLastDateComparator } from '@/app/common/utils/sort'
import { extractIcapActionIdFromObjectType } from '@/app/common/utils/utils'
import { ClnButtonProps, MatIcon } from '@celanese/ui-lib'
import {
    GRID_CHECKBOX_SELECTION_COL_DEF,
    GridColDef,
    GridColumnVisibilityModel,
    GridHeaderCheckbox,
    GridRowSelectionModel,
    GridRowsProp,
    GridSortModel,
} from '@mui/x-data-grid-pro'
import dayjs from 'dayjs'
import { useRouter } from 'next/navigation'
import { ActionItemModal } from '../HomeComponent/ActionItemModal'
import { BulkUploadDrawer } from '../HomeComponent/UploadActionsDrawer'
import { DataGridTable } from '../PaginatedTable/DataGridTable'
import { GenerateStatusChip } from '../StatusComponet'
import { FilterInfoProps, HomeFilter } from './HomeFilter'
import { toCamelCase } from '@/app/common/utils/transform-options-for-filter'
import { useAuthGuard } from '@/app/common/hooks/useAuthGuard'
import { UserRolesPermission } from '@celanese/celanese-sdk'
import { PageType, PageTypeValue } from '@/app/common/enums/PageTypeEnum'
import { useActionItemSubCategories } from '@/app/common/hooks/action-item-management/useActionItemSubCategories'
import { useActionItemCategories } from '@/app/common/hooks/action-item-management/useActionItemCategories'
import { useApplications } from '@/app/common/hooks/asset-hierarchy/useApplications'
import {
    ActionStatusExternalIdClearEnum,
    ActionStatusQuery,
    RestrictedStatus,
} from '@/app/common/enums/ActionItemStatusEnum'
import { useActionItemStatuses } from '@/app/common/hooks/action-item-management/useActionItemStatuses'
import { buildTranslations } from '@/app/common/utils/build-translations'

export interface FilterDefault {
    status: (string | undefined)[]
    owner: (string | undefined)[]
    assignee: (string | undefined)[]
}

type ActionTableProps = {
    id: string
    sourceId?: string
    filterInfo: FilterInfoProps
    showOptionsBar?: boolean
    showCustomFilter?: boolean
    showLink?: boolean
    showBulkUpload?: boolean
    additionalButtons?: ClnButtonProps[]
    statusNameListForIncludeOrExclude?: string[]
    actionForStatusFilter?: ActionStatusQuery
    currentPage: number
    memoryPage: number
    isToDoTab?: boolean
    pageType?: PageTypeValue
    employeesName?: AutocompleteOption[]
    ownerName?: AutocompleteOption[]
    assigneeName?: AutocompleteOption[]
    activeUser: UserRolesPermission
    client: AzureFunctionClient
    siteIds?: string[]
    initialSearchValue?: string
    initialChipValue?: string
    setFilterInfo: (value: FilterInfoProps) => void
    setCurrentPage: (value: number) => void
    setAssigneeName?: (value: AutocompleteOption[]) => void
    setOwnerName?: (value: AutocompleteOption[]) => void
    setMemoryPage: (value: number) => void
    handleFetchFailure?: (value: any) => void
    checkboxSelection?: boolean
    rowSelectionModel?: GridRowSelectionModel
    setRowSelectionModel?: (value: GridRowSelectionModel) => void
    infoMessage?: string
    onActionDetailsClick?: (value: string) => void
    disableDefaultFilterFields?: boolean
    defaultFilterInfo?: FilterInfoProps
    disableSearch?: boolean
    refetchActions?: boolean
    setRefetchActions?: (value: boolean) => void
    defaultAssigneeName?: AutocompleteOption[]
    siteSpecificCategories?: AutocompleteOption[]
    reportingUnits?: AutocompleteOption[]
    reportingLocations?: AutocompleteOption[]
    setSiteSpecificCategories?: (value: AutocompleteOption[]) => void
    setReportingLocations?: (value: AutocompleteOption[]) => void
    setReportingUnits?: (value: AutocompleteOption[]) => void
}

export const allActionColumns = [
    'act',
    'externalId',
    'sortTitle',
    'sortCurrentStatus',
    'sortAssignee',
    'sortApplication',
    'assignmentDate',
    'displayDueDate',
    'sortReportingSite',
    'sortReportingUnit',
    'sortCategory',
    'sortSubCategory',
    'sortSiteSpecificCategory',
    'sortOwner',
    'approvalDate',
    'sortApprover',
    'verificationDate',
    'sortVerifier',
    'conclusionDate',
    'sortSourceInformation',
    'sourceEventTitle',
    'isPrivate',
    'sortReportingLocation',
    'icapActionId',
    'priority',
    'sortSourceEventTitle',
    'ext',
    'manager',
]

export function ActionTable({
    id,
    sourceId,
    filterInfo,
    statusNameListForIncludeOrExclude = Object.values(RestrictedStatus) as string[],
    actionForStatusFilter = ActionStatusQuery.EXCLUDE,
    currentPage,
    memoryPage,
    isToDoTab,
    pageType,
    employeesName,
    ownerName,
    assigneeName,
    activeUser,
    client,
    siteIds,
    showOptionsBar = true,
    showCustomFilter = true,
    showLink = true,
    showBulkUpload = true,
    additionalButtons,
    initialSearchValue,
    initialChipValue,
    refetchActions = false,
    setCurrentPage,
    setMemoryPage,
    setOwnerName,
    setAssigneeName,
    setFilterInfo,
    handleFetchFailure,
    checkboxSelection = false,
    rowSelectionModel,
    setRowSelectionModel,
    infoMessage,
    onActionDetailsClick,
    disableDefaultFilterFields,
    defaultFilterInfo,
    disableSearch,
    setRefetchActions,
    siteSpecificCategories,
    reportingUnits,
    reportingLocations,
    setSiteSpecificCategories,
    setReportingLocations,
    setReportingUnits,
}: ActionTableProps) {
    const router = useRouter()
    const { state } = useBulkUpload()
    const { showLoading } = useLoading()

    const { categories: categoriesForFilter } = useActionItemCategories()
    const { subCategories: subcategoriesForFilter } = useActionItemSubCategories()
    const { applications: applicationsForFilter } = useApplications()
    const { allStatus: statusForFilter } = useActionItemStatuses({
        statusNameList: statusNameListForIncludeOrExclude,
        action: actionForStatusFilter,
    })

    const activeUserExternalId = activeUser.externalId ?? ''
    const userName = `${activeUser.lastName}, ${activeUser.firstName}`
    const { checkPermissionsFromComponents } = useAuthGuard()

    const [localPage, setLocalPage] = useState<number>(currentPage ?? 0)
    const [totalPages, setTotalPages] = useState<number>(1)

    const [loadingTable, setLoadingTable] = useState<boolean>(true)

    const [actionResponse, setActionResponse] = useState<ResponseData>()
    const [actionItems, setActionItems] = useState<ActionDetailItem[]>([])
    const [visibilityActionItems, setVisibilityActionItems] = useState<ActionDetailItem[]>([])

    const [sortModel, setSortModel] = useState<GridSortModel>([
        {
            field: DEFAULT_SORT_FIELD,
            sort: DEFAULT_SORT_DIRECTION,
        },
    ])

    const [openAIModal, setOpenAIModal] = useState(false)
    const [openBulkUploadModal, setOpenBulkUploadModal] = useState(false)

    const [selectedActionSiteCode, setSelectedActionSiteCode] = useState<string | undefined>()
    const [detailsExternalId, setDetailsExternalId] = useState<string | undefined>()
    const [sourceTypeId, setSourceTypeId] = useState<string | undefined>()

    const [isExportLoading, setIsExportLoading] = useState<boolean>(false)

    const isAuthorizedToDelete = useMemo(
        () => checkPermissionsFromComponents('BulkDeleteActionsPage').isAuthorized,
        [activeUser]
    )

    const isAuthorizedToEdit = useMemo(
        () => checkPermissionsFromComponents('BulkEditActionsPage').isAuthorized,
        [activeUser]
    )

    const theme = useTheme()
    const isMobile = useMediaQuery(theme.breakpoints.down('md'))

    const columnNames = {
        externalId: translate('table.headers.id'),
        title: translate('table.headers.title'),
        currentStatus: translate('table.headers.status'),
        assignedTo: translate('table.headers.assignee'),
        application: translate('table.headers.app'),
        assignmentDate: translate('table.headers.assignmentDate'),
        dueDate: translate('table.headers.dueDate'),
        conclusionDate: translate('table.headers.conclusionDate'),
        reportingSite: translate('table.headers.site'),
        reportingUnit: translate('table.headers.unit'),
        reportingLocation: translate('table.headers.location'),
        category: translate('table.headers.category'),
        subCategory: translate('table.headers.subcategoryOne'),
        siteSpecificCategory: translate('table.headers.subcategoryTwo'),
        owner: translate('table.headers.owner'),
        approver: translate('table.headers.approver'),
        approvalDate: translate('table.headers.approvalDate'),
        verifier: translate('table.headers.verifier'),
        verificationDate: translate('table.headers.verificationDate'),
        sourceInformation: translate('table.headers.sourceInformation'),
        sourceEventTitle: translate('table.headers.sourceEventTitle'),
        isPrivate: translate('table.headers.private'),
        objectType: translate('table.headers.icapActionId'),
        priority: translate('table.headers.priority'),
        ext: translate('table.headers.ext'),
        manager: translate('table.headers.manager'),
        overdue: translate('table.headers.overdue'),
    }

    const statusTranslations = buildTranslations(statusForFilter ?? [], 'status.fullName')
    const categoryTranslations = buildTranslations(categoriesForFilter, 'category')
    const subCategoryTranslations = buildTranslations(subcategoriesForFilter, 'subCategory1')
    const booleanTranslations = {
        true: translate('common.yes'),
        false: translate('common.no'),
    }

    const { exportToExcel } = useExportToExcel({
        client,
        columnNames,
        fileName: 'Action Item Management Home Export',
        filterInfo,
        activeUser,
        siteIds,
        worksheetName: 'Action',
        translations: {
            ...categoryTranslations,
            ...statusTranslations,
            ...subCategoryTranslations,
            ...booleanTranslations,
        },
        setIsExportLoading,
    })

    const actionButtons: ClnButtonProps[] = useMemo(() => {
        let buttons: ClnButtonProps[] = additionalButtons || []

        const getStartIcon = (isLoading: boolean, iconType: 'download' | 'delete' | 'upload' | 'edit') => {
            if (isLoading) {
                return <MatIcon icon="sync" />
            }

            switch (iconType) {
                case 'download':
                    return <MatIcon icon="download" />
                case 'delete':
                    return (
                        <MatIcon icon="delete" color={actionResponse?.totalActions === 0 ? 'disabled' : 'error.main'} />
                    )
                case 'edit':
                    return (
                        <MatIcon icon="edit" color={actionResponse?.totalActions === 0 ? 'disabled' : 'primary.main'} />
                    )
                case 'upload':
                    return <MatIcon icon="upload_2" />
                default:
                    return null
            }
        }

        const downloadButtonLabel = isExportLoading ? translate('table.exporting') : translate('table.download')

        buttons = buttons.concat([
            {
                startIcon: getStartIcon(isExportLoading, 'download'),
                label: isMobile ? '' : downloadButtonLabel,
                variant: isMobile ? 'text' : 'outlined',
                sxProps: isMobile
                    ? {
                          color:
                              actionResponse?.totalActions === 0 || isExportLoading
                                  ? 'primary.main'
                                  : 'primary.main !important',
                          padding: '8px 12px',
                          minWidth: '12px !important',
                          '& .MuiButton-startIcon': {
                              marginRight: 0,
                          },
                      }
                    : undefined,
                onClick: () => {
                    exportToExcel()
                    setIsExportLoading(true)
                },
                disabled: actionResponse?.totalActions === 0 || isExportLoading,
                hidden: pageType === PageType.BulkDeleteActions || pageType === PageType.EventDetails,
            },
            {
                startIcon: getStartIcon(state.isLoading, 'delete'),
                variant: 'text',
                sxProps: {
                    '& .MuiButton-startIcon': {
                        marginRight: 0,
                    },
                    padding: '8px 12px',
                    ...(isMobile && {
                        color: 'primary.main !important',
                        minWidth: '12px !important',
                    }),
                },
                onClick: () => {
                    router.push('/bulk-delete-actions')
                },
                disabled: actionResponse?.totalActions === 0,
                hidden: !(pageType === PageType.Home && isAuthorizedToDelete && isToDoTab),
            },
            {
                startIcon: getStartIcon(state.isLoading, 'edit'),
                variant: 'text',
                sxProps: {
                    '& .MuiButton-startIcon': {
                        marginRight: 0,
                    },
                    padding: '8px 12px',
                    ...(isMobile && {
                        color: 'primary.main !important',
                        minWidth: '12px !important',
                    }),
                },
                onClick: () => {
                    router.push('/bulk-edit-actions')
                },
                disabled: actionResponse?.totalActions === 0,
                hidden: !(pageType === PageType.Home && isAuthorizedToEdit && isToDoTab),
            },
            {
                startIcon: getStartIcon(state.isLoading, 'upload'),
                variant: 'text',
                sxProps: {
                    '& .MuiButton-startIcon': {
                        marginRight: 0,
                    },
                    padding: '8px 12px',
                    ...(isMobile && {
                        color: 'primary.main !important',
                        minWidth: '12px !important',
                    }),
                },
                onClick: () => setOpenBulkUploadModal(true),
                disabled: state.isLoading,
                hidden: !showBulkUpload,
            },
        ])

        return buttons
    }, [additionalButtons, isMobile, isExportLoading, actionResponse, showBulkUpload, state.isLoading,  isAuthorizedToDelete, isAuthorizedToEdit, isToDoTab, pageType])

    const customPopoverContent = useMemo(() => {
        return (
            <HomeFilter
                onFilter={(value) => {
                    setFilterInfo(value)
                    setRowSelectionModel?.([])
                }}
                categoriesForFilter={categoriesForFilter}
                subcategoriesForFilter={subcategoriesForFilter}
                applicationsForFilter={applicationsForFilter}
                statusForFilter={statusForFilter}
                filterInfo={filterInfo}
                defaultActiveUser={[{ value: activeUserExternalId, label: userName }]}
                activeUserExternalId={activeUserExternalId}
                isToDoTab={isToDoTab}
                pageType={pageType}
                defaultOwnerName={ownerName}
                setDefaultOwnerName={setOwnerName}
                defaultAssigneeName={assigneeName}
                setDefaultAssigneeName={setAssigneeName}
                setPage={(value: number) => {
                    setCurrentPage(value)
                    setMemoryPage(value)
                }}
                icapActionIdFilterAvailable
                sourceEventTitleFilterAvailable
                infoMessage={infoMessage}
                disableDefaultFilterFields={disableDefaultFilterFields}
                defaultFilterInfo={defaultFilterInfo}
                activeUser={activeUser}
                defaultSiteSpecificCategories={siteSpecificCategories}
                defaultReportingUnits={reportingUnits}
                defaultReportingLocations={reportingLocations}
                setDefaultSiteSpecificCategories={setSiteSpecificCategories}
                setDefaultReportingLocations={setReportingUnits}
                setDefaultReportingUnits={setReportingLocations}
            />
        )
    }, [
        categoriesForFilter,
        subcategoriesForFilter,
        applicationsForFilter,
        statusForFilter,
        siteSpecificCategories,
        reportingUnits,
        reportingLocations,
        filterInfo,
        activeUserExternalId,
        isToDoTab,
        translate,
    ])

    const initialStateColumnVisibilityModel: GridColumnVisibilityModel = useMemo(() => {
        return {
            sortReportingSite:
                pageType === PageType.Home &&
                Array.isArray(activeUser.applications?.[0]?.userSites) &&
                activeUser.applications?.[0]?.userSites?.length > 1,
            sortSubCategory: true,
            sortSiteSpecificCategory: false,
            sortSourceEventTitle: false,
            sortSourceInformation: false,
            approvalDate: !isToDoTab,
            verificationDate: !isToDoTab,
            conclusionDate: !isToDoTab,
            icapActionId: false,
            priority: false,
            ext: false,
            manager: false,
        }
    }, [isToDoTab])

    const activeFiltersCount = useMemo(() => {
        const filters = {
            statusExternalIds: filterInfo.statusExternalIds,
            dueDateGte: filterInfo.dueDateGte,
            dueDateLt: filterInfo.dueDateLt,
            updateStatusDate: filterInfo.updateStatusDate,
            onlyPrivate: filterInfo.onlyPrivate,
            reportingUnitExternalIds: filterInfo.reportingUnitExternalIds,
            reportingLocationExternalIds: filterInfo.reportingLocationExternalIds,
            reportingSiteExternalIds: filterInfo.reportingSiteExternalIds,
            ownerExternalId: filterInfo.ownerExternalId,
            assignedToExternalId: filterInfo.assignedToExternalId,
            onSiteManagerToExternalId: filterInfo.onSiteManagerToExternalId,
            categoryExternalId: filterInfo.categoryExternalId,
            subcategoryExternalId: filterInfo.subcategoryExternalId,
            siteSpecificCategoryExternalId: filterInfo.siteSpecificCategoryExternalId,
            applicationExternalId: filterInfo.applicationExternalId,
            externalIdPrefix: filterInfo.externalIdPrefix,
            titlePrefix: filterInfo.titlePrefix,
            sourceInfoPrefix: filterInfo.sourceInfoPrefix,
            icapActionIdPrefix: filterInfo.icapActionIdPrefix,
            sourceEventTitlePrefix: filterInfo.sourceEventTitlePrefix,
            sourceEventTitleEq: filterInfo.sourceEventTitleEq,
        }

        const count = determineActiveFilterCount(filters)
        return count
    }, [filterInfo])

    const getActionIcons = (externalId: any, siteCode: string, sourceId: any, sourceType: any): JSX.Element => {
        return (
            <Box
                sx={{
                    display: 'flex',
                    width: '100%',
                    height: '100%',
                    alignItems: 'center',
                    gap: '0.5rem',
                }}
                data-test="home_table-actions_content"
                data-origin="aim"
            >
                <a
                    href={`/action-item/details/${siteCode}/${externalId}`}
                    target="_self"
                    style={{
                        textDecoration: 'none',
                        color: 'inherit',
                        fontSize: 0,
                        display: 'flex',
                        alignItems: 'center',
                    }}
                >
                    <MatIcon
                        icon="visibility"
                        color="primary.main"
                        onClick={(e) => {
                            e.preventDefault()

                            if (onActionDetailsClick) {
                                return onActionDetailsClick(externalId)
                            }

                            showLoading(true)

                            let formattedId = id
                            if (pageType === PageType.Home) {
                                formattedId = '/'
                            } else if (pageType === PageType.Dashboard) {
                                formattedId = '/dashboard'
                            } else if (pageType === PageType.EventDetails) {
                                formattedId = `/event-source/details/${siteCode}/${sourceId}`
                            }

                            sessionStorage.setItem('lastPage', formattedId)
                            router.push(`/action-item/details/${siteCode}/${externalId}`)
                        }}
                    />
                </a>

                {showLink && sourceId && sourceId !== '' && sourceType && sourceType !== 'AST-AIM-AIMScratch' && (
                    <MatIcon
                        icon="link"
                        color="primary.main"
                        style={{
                            cursor: 'pointer',
                        }}
                        onClick={() => {
                            setSelectedActionSiteCode(siteCode)
                            setDetailsExternalId(sourceId)
                            setSourceTypeId(sourceType)
                            setOpenAIModal(true)
                        }}
                    />
                )}
            </Box>
        )
    }

    const tableRows: GridRowsProp = useMemo(() => {
        function convertActionItemDataToRows(items: ActionDetailItem[]) {
            return items.map((item) => ({
                id: `${item.externalId}`,
                externalId: `${item.externalId}`,
                sortTitle: `${item.title}`,
                assignmentDate: dayjs.utc(item.assignmentDate).format('MM/DD/YYYY'),
                displayDueDate: item.displayDueDate ?? item.dueDate,
                conclusionDate: item?.conclusionDate ? dayjs.utc(item?.conclusionDate).format('MM/DD/YYYY') : '',
                sortApplication: `${item.application?.alias ?? item.application?.name}`,
                sortReportingUnit: item?.reportingUnit?.description ?? '',
                sortReportingSite: item?.reportingSite?.name ?? '',
                reportingSiteCode: item?.reportingSite?.siteCode ?? '',
                sortAssignee: `${
                    item.assignedTo
                        ? item.assignedTo?.user?.displayName ??
                          `${item.assignedTo?.user?.lastName}, ${item.assignedTo?.user?.firstName}`
                        : ''
                }`,
                sortOwner: item.owner
                    ? item.owner?.user?.displayName ?? `${item.owner?.user?.lastName}, ${item.owner?.user?.firstName}`
                    : '',
                sortCategory: item?.category?.name ?? '',
                sortSubCategory: item?.subCategory?.name ?? '',
                sortSiteSpecificCategory: item?.siteSpecificCategory?.name ?? '',
                sortCurrentStatus: item?.currentStatus?.externalId ?? ActionStatusExternalIdClearEnum.Assigned,
                sortApprover: item.approver
                    ? item.approver?.displayName ?? `${item.approver?.lastName}, ${item.approver?.firstName}`
                    : '',
                approvalDate: item?.approvalDate ? dayjs.utc(item?.approvalDate).format('MM/DD/YYYY') : '',
                sortVerifier: item.verifier
                    ? item.verifier?.displayName ?? `${item.verifier?.lastName}, ${item.verifier?.firstName}`
                    : '',
                verificationDate: item?.verificationDate ? dayjs.utc(item?.verificationDate).format('MM/DD/YYYY') : '',
                sortSourceInformation: item.sourceInformation ?? '',
                sourceId: `${item.sourceId ?? ''}`,
                sourceType: `${item.sourceType?.externalId ?? ''}`,
                sortSourceEventTitle:
                    item.sourceType?.externalId !== 'AST-AIM-AIMScratch' ? `${item.sourceEventTitle ?? ''}` : '',
                isPrivate: item.isPrivate ?? false,
                sortReportingLocation: item.reportingLocation ? `${item.reportingLocation?.description}` : '',
                icapActionId: item.objectType ? extractIcapActionIdFromObjectType(item.objectType) : '',
                priority: `${item.priority ?? ''}`,
                ext: item?.ext ?? '-',
                manager: item.manager ? `${item.manager?.lastName}, ${item.manager?.firstName}` : '-',
            }))
        }

        const actionsRows =
            visibilityActionItems != null && visibilityActionItems.length > 0
                ? convertActionItemDataToRows(visibilityActionItems)
                : []
        setLoadingTable(false)
        return actionsRows
    }, [visibilityActionItems])

    const headCells = useMemo(() => {
        const columns: GridColDef[] = [
            {
                ...GRID_CHECKBOX_SELECTION_COL_DEF,
                headerName: translate('table.headers.checkbox'),
                renderHeader: (params) => (
                    <Tooltip title={translate('table.headers.checkboxTooltip')}>
                        <GridHeaderCheckbox {...params} />
                    </Tooltip>
                ),
                hideable: false,
            },
            {
                field: 'act',
                headerName: translate('table.headers.actions'),
                sortable: false,
                filterable: false,
                width: 100,
                renderCell: (params) =>
                    getActionIcons(
                        params.row.id,
                        params.row.reportingSiteCode,
                        params.row.sourceId,
                        params.row.sourceType
                    ),
                renderHeader: () => (
                    <span data-test="home-actions_table_sort" data-origin="aim">
                        {translate('table.headers.actions')}
                    </span>
                ),
            },
            {
                field: 'externalId',
                headerName: translate('table.headers.id'),
                filterable: true,
                flex: 1,
                minWidth: 250,
                renderCell: (params) => {
                    const siteCode = params.row.reportingSiteCode
                    return onActionDetailsClick ? (
                        <span>{params.value}</span>
                    ) : (
                        <a
                            href={`/action-item/details/${siteCode}/${params.value}`}
                            style={{ cursor: 'pointer', textDecoration: 'underline', color: 'inherit' }}
                            onClick={(e) => {
                                e.preventDefault()
                                showLoading(true)

                                let formattedId = id
                                if (pageType === PageType.Home) {
                                    formattedId = '/'
                                } else if (pageType === PageType.Dashboard) {
                                    formattedId = '/dashboard'
                                } else if (pageType === PageType.EventDetails) {
                                    formattedId = `/event-source/details/${siteCode}/${sourceId}`
                                }

                                sessionStorage.setItem('lastPage', formattedId)
                                router.push(`/action-item/details/${siteCode}/${params.value}`)
                            }}
                            data-test="home_table-id_content"
                            data-origin="aim"
                        >
                            {params.value}
                        </a>
                    )
                },
                renderHeader: () => (
                    <span data-test="home-id_table_sort" data-origin="aim">
                        {translate('table.headers.id')}
                    </span>
                ),
            },
            {
                field: 'sortTitle',
                headerName: translate('table.headers.title'),
                filterable: true,
                flex: 1,
                minWidth: 250,
                renderCell: (params) => (
                    <NoTranslate>
                        <span data-test="home_table-title_content" data-origin="aim">
                            {params.row.sortTitle}
                        </span>
                    </NoTranslate>
                ),
                renderHeader: () => (
                    <span data-test="home-title_table_sort" data-origin="aim">
                        {translate('table.headers.title')}
                    </span>
                ),
                getSortComparator: getNullsLastStringOrNumberComparator,
            },
            {
                field: 'sortCurrentStatus',
                headerName: translate('table.headers.status'),
                filterable: true,
                flex: 1,
                minWidth: 150,
                renderCell: (params) => (
                    <Box
                        sx={{
                            display: 'flex',
                            width: '100%',
                            height: '100%',
                            alignItems: 'center',
                        }}
                        data-test="home_table-status_content"
                        data-origin="aim"
                    >
                        <NoTranslate>
                            <GenerateStatusChip statusId={params.value} />
                        </NoTranslate>
                    </Box>
                ),
                renderHeader: () => (
                    <span data-test="home-status_table_sort" data-origin="aim">
                        {translate('table.headers.status')}
                    </span>
                ),
                getSortComparator: getNullsLastStringOrNumberComparator,
            },
            {
                field: 'sortAssignee',
                headerName: translate('table.headers.assignee'),
                filterable: false,
                flex: 1,
                minWidth: 150,
                renderCell: (params) => (
                    <NoTranslate>
                        <span data-test="home_table-assignee_content" data-origin="aim">
                            {params.row.sortAssignee}
                        </span>
                    </NoTranslate>
                ),
                renderHeader: () => (
                    <span data-test="home-assignee_table_sort" data-origin="aim">
                        {translate('table.headers.assignee')}
                    </span>
                ),
                getSortComparator: getNullsLastStringOrNumberComparator,
            },
            {
                field: 'sortReportingSite',
                headerName: translate('table.headers.site'),
                filterable: false,
                sortable: false,
                flex: 1,
                minWidth: 150,
                renderCell: (params) => (
                    <NoTranslate>
                        <span data-test="home_table-site_content" data-origin="aim">
                            {params.row.sortReportingSite}
                        </span>
                    </NoTranslate>
                ),
                renderHeader: () => (
                    <span data-test="home-site_table_sort" data-origin="aim">
                        {translate('table.headers.site')}
                    </span>
                ),
            },
            {
                field: 'sortApplication',
                headerName: translate('table.headers.app'),
                filterable: false,
                width: 200,
                renderCell: (params) => (
                    <NoTranslate>
                        <span data-test="home_table-application_content" data-origin="aim">
                            {params.row.sortApplication}
                        </span>
                    </NoTranslate>
                ),
                renderHeader: () => (
                    <span data-test="home-app_table_sort" data-origin="aim">
                        {translate('table.headers.app')}
                    </span>
                ),
                getSortComparator: getNullsLastStringOrNumberComparator,
            },
            {
                field: 'assignmentDate',
                headerName: translate('table.headers.assignmentDate'),
                filterable: false,
                flex: 1,
                minWidth: 150,
                renderCell: (params) => (
                    <span data-test="home_table-assignment_date_content" data-origin="aim">
                        {params.row.assignmentDate}
                    </span>
                ),
                renderHeader: () => (
                    <div>
                        <Tooltip title={'MM/DD/YYYY'} arrow={true} placement="top">
                            <span data-test="home_table-assignment_date_content" data-origin="aim">
                                {translate('table.headers.assignmentDate')}
                            </span>
                        </Tooltip>
                    </div>
                ),
                getSortComparator: getNullsLastDateComparator,
            },
            {
                field: 'displayDueDate',
                headerName: translate('table.headers.dueDate'),
                filterable: false,
                flex: 1,
                minWidth: 150,
                renderCell: (params) => (
                    <Box
                        sx={{
                            display: 'flex',
                            width: '100%',
                            height: '100%',
                            alignItems: 'center',
                        }}
                        data-test="home_table-dueDate_content"
                        data-origin="aim"
                    >
                        <NoTranslate>
                            {isToDoTab
                                ? generateDueDateColor(params.row.displayDueDate, theme, params.row.sourceType)
                                : formatDisplayDueDate(params.row.displayDueDate, params.row.sourceType)}
                        </NoTranslate>
                    </Box>
                ),
                renderHeader: () => (
                    <div
                        style={{ display: 'flex', alignItems: 'center', flexDirection: 'row', gap: '0.5rem' }}
                        data-test="home-due_date_table_sort"
                        data-origin="aim"
                    >
                        <div>
                            <Tooltip title={'MM/DD/YYYY'} arrow={true} placement="top">
                                {translate('table.headers.dueDate')}
                            </Tooltip>
                        </div>
                        <IconWithTooltip title={translate('table.headers.dueDateTooltip')} fontSizeIcon={'24px'} />
                    </div>
                ),
                getSortComparator: getNullsLastDateComparator,
            },
            {
                field: 'sortReportingUnit',
                headerName: translate('table.headers.unit'),
                filterable: false,
                flex: 1,
                minWidth: 150,
                renderCell: (params) => (
                    <NoTranslate>
                        <span data-test="home_table-unit_content" data-origin="aim">
                            {params.row.sortReportingUnit}
                        </span>
                    </NoTranslate>
                ),
                renderHeader: () => (
                    <span data-test="home-unit_table_sort" data-origin="aim">
                        {translate('table.headers.unit')}
                    </span>
                ),
                getSortComparator: getNullsLastStringOrNumberComparator,
            },
            {
                field: 'sortCategory',
                headerName: translate('table.headers.category'),
                filterable: false,
                flex: 1,
                minWidth: 150,
                renderCell: (params) => (
                    <span data-test="home_table-category_content" data-origin="aim">
                        {translate('category.' + toCamelCase(params.row.sortCategory))}
                    </span>
                ),
                renderHeader: () => (
                    <span data-test="home-category_table_sort" data-origin="aim">
                        {translate('table.headers.category')}
                    </span>
                ),
                getSortComparator: getNullsLastStringOrNumberComparator,
            },
            {
                field: 'sortSubCategory',
                headerName: translate('table.headers.subcategoryOne'),
                filterable: false,
                flex: 1,
                minWidth: 150,
                renderCell: (params) => (
                    <span data-test="home_table-subcategory_1_content" data-origin="aim">
                        {translate('subCategory1.' + toCamelCase(params.row.sortSubCategory))}
                    </span>
                ),
                renderHeader: () => (
                    <span data-test="home-subcategoryOne_table_sort" data-origin="aim">
                        {translate('table.headers.subcategoryOne')}
                    </span>
                ),
                getSortComparator: getNullsLastStringOrNumberComparator,
            },
            {
                field: 'sortSiteSpecificCategory',
                headerName: translate('table.headers.subcategoryTwo'),
                filterable: false,
                flex: 1,
                minWidth: 150,
                renderCell: (params) => (
                    <span data-test="home_table-subcategory_2_content" data-origin="aim">
                        {params.row.sortSiteSpecificCategory}
                    </span>
                ),
                renderHeader: () => (
                    <span data-test="home-subcategoryTwo_table_sort" data-origin="aim">
                        {translate('table.headers.subcategoryTwo')}
                    </span>
                ),
                getSortComparator: getNullsLastStringOrNumberComparator,
            },
            {
                field: 'sortOwner',
                headerName: translate('table.headers.owner'),
                filterable: false,
                flex: 1,
                minWidth: 150,
                renderCell: (params) => (
                    <NoTranslate>
                        <span data-test="home_table-owner_content" data-origin="aim">
                            {params.row.sortOwner}
                        </span>
                    </NoTranslate>
                ),
                renderHeader: () => (
                    <span data-test="home-owner_table_sort" data-origin="aim">
                        {translate('table.headers.owner')}
                    </span>
                ),
                getSortComparator: getNullsLastStringOrNumberComparator,
            },
            {
                field: 'approvalDate',
                headerName: translate('table.headers.approvalDate'),
                filterable: false,
                flex: 1,
                minWidth: 150,
                renderCell: (params) => (
                    <NoTranslate>
                        <span data-test="home_table-approval_date_content" data-origin="aim">
                            {params.row.approvalDate}
                        </span>
                    </NoTranslate>
                ),
                renderHeader: () => (
                    <div>
                        <Tooltip title={'MM/DD/YYYY'} arrow={true} placement="top">
                            <span data-test="home-approval_date_table_sort" data-origin="aim">
                                {translate('table.headers.approvalDate')}
                            </span>
                        </Tooltip>
                    </div>
                ),
                getSortComparator: getNullsLastDateComparator,
            },
            {
                field: 'sortApprover',
                headerName: translate('table.headers.approver'),
                filterable: false,
                flex: 1,
                minWidth: 150,
                renderCell: (params) => (
                    <NoTranslate>
                        <span data-test="home_table-approver_content" data-origin="aim">
                            {params.row.sortApprover}
                        </span>
                    </NoTranslate>
                ),
                renderHeader: () => (
                    <span data-test="home-approver_table_sort" data-origin="aim">
                        {translate('table.headers.approver')}
                    </span>
                ),
                getSortComparator: getNullsLastStringOrNumberComparator,
            },
            {
                field: 'verificationDate',
                headerName: translate('table.headers.verificationDate'),
                filterable: false,
                flex: 1,
                minWidth: 150,
                renderCell: (params) => (
                    <NoTranslate>
                        <span data-test="home_table-verification_date_content" data-origin="aim">
                            {params.row.verificationDate}
                        </span>
                    </NoTranslate>
                ),
                renderHeader: () => (
                    <div>
                        <Tooltip title={'MM/DD/YYYY'} arrow={true} placement="top">
                            <span data-test="home_table-verification_date_content" data-origin="aim">
                                {translate('table.headers.verificationDate')}
                            </span>
                        </Tooltip>
                    </div>
                ),
                getSortComparator: getNullsLastDateComparator,
            },
            {
                field: 'sortVerifier',
                headerName: translate('table.headers.verifier'),
                filterable: false,
                flex: 1,
                minWidth: 150,
                renderCell: (params) => (
                    <NoTranslate>
                        <span data-test="home_table-verifier_content" data-origin="aim">
                            {params.row.sortVerifier}
                        </span>
                    </NoTranslate>
                ),
                renderHeader: () => (
                    <span data-test="home-verifier_table_sort" data-origin="aim">
                        {translate('table.headers.verifier')}
                    </span>
                ),
                getSortComparator: getNullsLastStringOrNumberComparator,
            },
            {
                field: 'conclusionDate',
                headerName: translate('table.headers.conclusionDate'),
                filterable: false,
                flex: 1,
                minWidth: 150,
                renderCell: (params) => (
                    <NoTranslate>
                        <span data-test="home_table-conclusion_date_content" data-origin="aim">
                            {params.row.conclusionDate}
                        </span>
                    </NoTranslate>
                ),
                renderHeader: () => (
                    <div>
                        <Tooltip title={'MM/DD/YYYY'} arrow={true} placement="top">
                            <span data-test="home_table-conclusion_date_content" data-origin="aim">
                                {translate('table.headers.conclusionDate')}
                            </span>
                        </Tooltip>
                    </div>
                ),
                getSortComparator: getNullsLastDateComparator,
            },
            {
                field: 'sortSourceInformation',
                headerName: translate('table.headers.sourceInformation'),
                filterable: false,
                flex: 1,
                minWidth: 200,
                renderCell: (params) => (
                    <span data-test="home_table-source_information_content" data-origin="aim">
                        {params.row.sortSourceInformation}
                    </span>
                ),
                renderHeader: () => (
                    <span data-test="home-sourceInformation_table_sort" data-origin="aim">
                        {translate('table.headers.sourceInformation')}
                    </span>
                ),
                getSortComparator: getNullsLastStringOrNumberComparator,
            },
            {
                field: 'sortSourceEventTitle',
                headerName: translate('table.headers.sourceEventTitle'),
                filterable: false,
                flex: 1,
                minWidth: 200,
                renderCell: (params) => (
                    <span data-test="home_table-source_event_title_content" data-origin="aim">
                        {params.row.sortSourceEventTitle}
                    </span>
                ),
                renderHeader: () => (
                    <span data-test="home-sourceEventTitle_table_sort" data-origin="aim">
                        {translate('table.headers.sourceEventTitle')}
                    </span>
                ),
                getSortComparator: getNullsLastStringOrNumberComparator,
            },
            {
                field: 'isPrivate',
                headerName: translate('table.headers.private'),
                filterable: false,
                flex: 1,
                minWidth: 200,
                renderCell: (params) => (
                    <span data-test="home_table-source_event_title_content" data-origin="aim">
                        {params.row.isPrivate ? translate('common.yes') : translate('common.no')}
                    </span>
                ),
                renderHeader: () => (
                    <span data-test="home-private_table_sort" data-origin="aim">
                        {translate('table.headers.private')}
                    </span>
                ),
            },
            {
                field: 'sortReportingLocation',
                headerName: translate('table.headers.location'),
                filterable: false,
                flex: 1,
                minWidth: 200,
                renderCell: (params) => (
                    <span data-test="home_table-source_event_title_content" data-origin="aim">
                        {params.row.sortReportingLocation}
                    </span>
                ),
                renderHeader: () => (
                    <span data-test="home-location_table_sort" data-origin="aim">
                        {translate('table.headers.location')}
                    </span>
                ),
                getSortComparator: getNullsLastStringOrNumberComparator,
            },
            {
                field: 'icapActionId',
                headerName: translate('table.headers.icapActionId'),
                filterable: false,
                sortable: false,
                flex: 1,
                minWidth: 200,
                renderCell: (params) => (
                    <span data-test="home_table-icap_action_id_content" data-origin="aim">
                        {params.row.icapActionId}
                    </span>
                ),
                renderHeader: () => (
                    <span data-test="home-icapActionId_table_sort" data-origin="aim">
                        {translate('table.headers.icapActionId')}
                    </span>
                ),
            },
            {
                field: 'priority',
                headerName: translate('table.headers.priority'),
                filterable: false,
                flex: 1,
                minWidth: 200,
                renderCell: (params) => (
                    <span data-test="home_table-priority_content" data-origin="aim">
                        {params.row.priority}
                    </span>
                ),
                renderHeader: () => (
                    <span data-test="home-priority_table_sort" data-origin="aim">
                        {translate('table.headers.priority')}
                    </span>
                ),
                getSortComparator: getNullsLastStringOrNumberComparator,
            },
            {
                field: 'ext',
                headerName: translate('table.headers.ext'),
                sortable: false,
                filterable: false,
                flex: 1,
                minWidth: 150,
                renderHeader: () => (
                    <span data-test="home-tab-ext_table_sort" data-origin="aim">
                        {translate('table.headers.ext')}
                    </span>
                ),
            },
            {
                field: 'manager',
                headerName: translate('table.headers.manager'),
                sortable: false,
                filterable: false,
                flex: 1,
                minWidth: 150,
                renderCell: (params) => <NoTranslate>{params.row.manager}</NoTranslate>,
                renderHeader: () => (
                    <span data-test="home-tab-manager_table_sort" data-origin="aim">
                        {translate('table.headers.manager')}
                    </span>
                ),
            },
        ]

        return checkboxSelection ? columns : columns.slice(1)
    }, [translate, theme, actionItems])

    const resetAllPages = () => {
        setMemoryPage(0)
        setCurrentPage(0)
        setLocalPage(0)
        setTotalPages(1)
        setRowSelectionModel?.([])
    }

    const fetchActions = useCallback(async () => {
        try {
            const result: ResponseData = await client.getActionsHome({
                ...filterInfo,
                ownerExternalId:
                    filterInfo.ownerExternalId && filterInfo.ownerExternalId?.length > 0
                        ? filterInfo.ownerExternalId.map((owner) => `UserAzureAttribute_${owner}`)
                        : undefined,
                assignedToExternalId:
                    filterInfo.assignedToExternalId && filterInfo.assignedToExternalId?.length > 0
                        ? filterInfo.assignedToExternalId.map((assignee) => `UserAzureAttribute_${assignee}`)
                        : undefined,
                reportingSiteExternalId: filterInfo.reportingSiteExternalIds ?? siteIds,
                icapActionIdPrefix: filterInfo.icapActionIdPrefix
                    ? `ICAPContinuousMigration-${filterInfo.icapActionIdPrefix}`
                    : undefined,
                activeUserEmail: activeUser.email,
                sourceIds: sourceId ? [sourceId] : undefined,
            })

            const resultData = result.data as ActionDetailItem[]
            const filteredResultData = filterInfo.cursor
                ? resultData.filter(
                      (item) => !actionItems.some((existingItem) => existingItem.externalId === item.externalId)
                  )
                : resultData

            setActionResponse(result)

            const allItems = filterInfo.cursor ? [...actionItems, ...filteredResultData] : filteredResultData
            setActionItems(allItems)
            setVisibilityActionItems(filteredResultData)
        } catch (err) {
            handleFetchFailure?.(err)
        } finally {
            setLoadingTable(false)
        }
    }, [filterInfo])

    const debouncedFetchActions = useCallback(useDebounceFunction(fetchActions, 800), [fetchActions])

    useEffect(() => {
        if (filterInfo !== undefined) {
            if (loadingTable) return

            setLoadingTable(true)
            debouncedFetchActions()
        }
    }, [debouncedFetchActions])

    useEffect(() => {
        if (refetchActions) {
            debouncedFetchActions()
            setRefetchActions && setRefetchActions(false)
        }
    }, [refetchActions])

    useEffect(() => {
        setLocalPage(currentPage ?? 0)
    }, [currentPage])

    useEffect(() => {
        const pages = Math.ceil(actionItems.length / filterInfo.pageSize)
        setTotalPages(actionResponse?.hasNextPage ? pages + 1 : pages)
    }, [actionResponse])

    useEffect(() => {
        if (onActionDetailsClick) return

        const tab = isToDoTab ? 'toDo' : 'closed'
        sessionStorage.setItem(
            `${id}-filterInfo-${id === 'home' ? tab + '-' : ''}tab`,
            JSON.stringify({
                filter: filterInfo,
                ownerName: ownerName,
                assignedName: assigneeName,
                employeesName: employeesName,
                siteSpecificCategories: siteSpecificCategories,
                reportingUnits: reportingUnits,
                reportingLocations: reportingLocations,
            })
        )
    }, [filterInfo])

    return (
        <Box>
            <NoTranslate>
                <DataGridTable
                    id={`${id}-filterInfo-${id === 'home' ? (isToDoTab ? 'toDo' : 'closed') + '-' : ''}tab`}
                    isLoading={loadingTable}
                    rows={tableRows}
                    initialColumnDefs={headCells}
                    initialStateColumnVisibilityModel={initialStateColumnVisibilityModel}
                    rowsPerPageOptions={ROWS_PER_PAGE_OPTIONS}
                    currentPage={localPage}
                    setCurrentPage={(value: number) => {
                        setCurrentPage(value)
                        if (value > memoryPage) {
                            setMemoryPage(value)
                            if (actionResponse?.cursor) {
                                const newFilterInfo = { ...filterInfo, cursor: actionResponse.cursor }
                                setFilterInfo(newFilterInfo)
                            }
                        } else {
                            setVisibilityActionItems(
                                actionItems.slice(
                                    value * filterInfo.pageSize,
                                    value * filterInfo.pageSize + filterInfo.pageSize
                                )
                            )
                        }
                    }}
                    totalPages={totalPages}
                    rowsPerPage={filterInfo.pageSize}
                    initialSearchValue={initialSearchValue}
                    initialChipValue={initialChipValue}
                    setRowsPerPage={(value: number) => {
                        resetAllPages()
                        const newFilterInfo = {
                            ...filterInfo,
                            pageSize: value,
                            cursor: undefined,
                        }
                        setFilterInfo(newFilterInfo)
                    }}
                    onSearchSubmit={(value: string) => {
                        resetAllPages()
                        const newFilterInfo = {
                            ...filterInfo,
                            search: value,
                            cursor: undefined,
                        }
                        setFilterInfo(newFilterInfo)
                    }}
                    customButtons={actionButtons}
                    paginationMode={'server'}
                    searchHelpMessage={translate('table.search.searchForTitleOrSourceInfo')}
                    searchInfoMessage={infoMessage}
                    customPopoverContent={customPopoverContent}
                    sortModel={sortModel}
                    setSortModel={(value: GridSortModel) => {
                        resetAllPages()
                        setSortModel(value)
                        const newFilterInfo = {
                            ...filterInfo,
                            cursor: undefined,
                            sortBy: value?.[0]?.sort ? value?.[0]?.field ?? undefined : undefined,
                            direction: value?.[0]?.sort ? value[0].sort.toUpperCase() : undefined,
                        }
                        setFilterInfo(newFilterInfo)
                    }}
                    activeFiltersCount={activeFiltersCount}
                    checkboxSelection={checkboxSelection}
                    rowSelectionModel={rowSelectionModel}
                    setRowSelectionModel={setRowSelectionModel}
                    disableSearch={disableSearch}
                    showOptionsBar={showOptionsBar}
                    showCustomFilter={showCustomFilter}
                />
            </NoTranslate>
            {openAIModal && (
                <ActionItemModal
                    handleCloseModal={() => setOpenAIModal(false)}
                    sourceId={detailsExternalId}
                    sourceTypeId={sourceTypeId}
                    activeUser={activeUser}
                    siteId={`STS-${selectedActionSiteCode ?? ''}`}
                />
            )}
            {openBulkUploadModal && !state.modalType && (
                <BulkUploadDrawer
                    handleCloseDrawer={() => setOpenBulkUploadModal(false)}
                    debouncedFetchActions={debouncedFetchActions}
                    activeUser={activeUser}
                />
            )}
        </Box>
    )
}
