from clients.core.models import (
    DescribableEntity,
    Node,
)
from clients.reporting_site.models import ReportingSiteResult

from ._models import _Role, _User


class CategoryConfigurationByFilterResult(Node):
    category: DescribableEntity | None
    action_item_sub_category: DescribableEntity | None
    site_specific_category: DescribableEntity | None

    default_approval_role: _Role | None
    default_approval_user: _User | None
    default_verification_role: _Role | None
    default_verification_user: _User | None
    default_extension_approver_role: Node | None

    days_to_approval: int | None
    days_to_verification: int | None
    days_from_assigned_date: int | None

    attachment_required: bool | None
    is_approval_required: bool | None
    is_verification_required: bool | None
    is_extensions_allowed: bool | None
    is_extension_attachment_required: bool | None
    has_email_notification: bool | None


class CategoryResult(Node):
    name: str | None


class SubCategoryResult(Node):
    name: str | None


class SiteSpecificCategoryResult(Node):
    name: str | None
    reporting_site: ReportingSiteResult | None = None
