from datetime import datetime, timedelta, timezone
from http import HTT<PERSON><PERSON>us
from http.client import <PERSON>TT<PERSON><PERSON>xception
from typing import Optional

from clients.action_item_client import ActionItemClient
from clients.category_configuration.requests import (
    GetCategoryConfigurationByFilterRequest,
)
from clients.core.constants import (
    AGGREGATE_LIMIT,
    USER_AZURE_ATTRIBUTE_PREFIX,
    ActionStatusClearEnum,
    ActionStatusOpenEnum,
)
from clients.dashboard_kpis.constants import DashboardTabsEnum
from clients.dashboard_kpis.models import (
    ActionResult,
    GetActionResponse,
    SiteTabKpiResponse,
    SupervisorTabKpiResponse,
)
from clients.dashboard_kpis.requests import GetChartsRequest, GetDashboardKpisRequest
from clients.kpis.models import KpiResponse
from clients.kpis.requests import GetKpisRequest
from clients.user_complement.requests import (
    GetUserRolesAndTeamsRequest,
)
from services.category_configuration_service import CategoryConfigurationService
from services.user_complements_service import UserByEmailResult, UserComplementsService


class AggregateService:
    """Service that aggregates and retrieves KPI and chart data related to action items."""

    def __init__(self, action_item_client: ActionItemClient) -> None:
        """
        Initialize the AggregateService.

        Args:
            action_item_client (ActionItemClient): Client used to interact with action item data.

        """
        self._action_item_client = action_item_client
        self._today = datetime.now(timezone.utc).date()

    async def get_home_kpis(self, request: GetKpisRequest) -> KpiResponse:
        """
        Get KPI data for the home page based on user context.

        Args:
            request (GetKpisRequest): Request containing user context and permission details.

        Returns:
            KpiResponse: KPI data for the action home tab.

        """
        user = await self._get_user_by_email(
            request.active_user_email or "",
            request.reporting_site_external_id or "",
        )
        if not user:
            return KpiResponse.empty()
        (
            request.user_external_id,
            request.active_user_roles_ids,
            request.active_user_teams_ids,
        ) = (
            user.external_id.removeprefix(USER_AZURE_ATTRIBUTE_PREFIX),
            user.active_user_roles_ids,
            user.active_user_teams_ids,
        )

        category_configuration_service = CategoryConfigurationService(
            self._action_item_client,
        )
        categories_configuration = (
            category_configuration_service.get_category_configurations_by_site(
                GetCategoryConfigurationByFilterRequest(
                    reporting_site_external_id=request.reporting_site_external_id,
                    category_id=None,
                    sub_category_id=None,
                    site_specific_category_id=None,
                    non_null_extension_approver_role=True,
                ),
            )
        )

        return self._action_item_client.kpis.get_kpis(
            request,
            categories_configuration,
        )

    async def get_kpis(
        self,
        request: GetDashboardKpisRequest,
    ) -> SiteTabKpiResponse | SupervisorTabKpiResponse:
        """
        Get KPI data for the specified dashboard tab (site or supervisor).

        Args:
            request (GetDashboardKpisRequest): Request with user and tab information.

        Returns:
            SiteTabKpiResponse | SupervisorTabKpiResponse: KPI data for the specified dashboard tab.

        """
        user = await self._get_user_by_email(
            request.active_user_email or "",
            request.filters.reporting_site_external_id or "",
        )
        if not user:
            return None
        (
            request.user_external_id,
            request.active_user_roles_ids,
            request.active_user_teams_ids,
        ) = (
            user.external_id.removeprefix(USER_AZURE_ATTRIBUTE_PREFIX),
            user.active_user_roles_ids,
            user.active_user_teams_ids,
        )

        response = self._action_item_client.dashboard_kpis.get_actions(request)
        mapped_response = self._map_kpi_result(response)

        if request.tab == DashboardTabsEnum.SITE:
            total_action_items = (
                self._action_item_client.dashboard_kpis.count_total_entered(request)
            )
            return self._process_site_tab_kpis(mapped_response, total_action_items)
        employees_request = GetChartsRequest(
            **request.model_dump(),
            group_by=["assignedTo"],
            items_per_page=AGGREGATE_LIMIT,
        )
        total_employees = len(
            self._get_all_grouped_by_actions_limited(employees_request),
        )

        return self._process_supervisor_tab_kpis(mapped_response, total_employees)

    def _process_site_tab_kpis(
        self,
        response: GetActionResponse,
        count_total_entered: int,
    ) -> SiteTabKpiResponse:
        """
        Process site tab KPI metrics from action data.

        Args:
            response (GetActionResponse): Retrieved action data.
            count_total_entered (int): Total number of actions entered.

        Returns:
            SiteTabKpiResponse: Processed site tab KPI results.

        """
        total_entered = count_total_entered
        overdue = response.count_overdue(
            self._today,
            list(ActionStatusClearEnum.__members__.values()),
        )
        open_count = response.count_open(
            list(ActionStatusOpenEnum.__members__.values()),
        )
        pending = response._pending
        overdue_percentage = self._calculate_percentage(overdue, total_entered)

        return SiteTabKpiResponse(
            total_entered=total_entered,
            overdue=overdue,
            open=open_count,
            pending=pending,
            overdue_percentage=overdue_percentage,
        )

    def _process_supervisor_tab_kpis(
        self,
        response: GetActionResponse,
        total_employees: int,
    ) -> SupervisorTabKpiResponse:
        """
        Process supervisor tab KPI metrics from action data.

        Args:
            response (GetActionResponse): Retrieved action data.
            total_employees (int): Number of employees under supervision.

        Returns:
            SupervisorTabKpiResponse: Processed supervisor tab KPI results.

        """
        employees = total_employees
        overdue = response.count_overdue(
            self._today,
            list(ActionStatusClearEnum.__members__.values()),
        )
        tasks_within_7_days = response.count_tasks_within_7_days(
            self._today + timedelta(days=7),
            self._today,
        )
        tasks_within_this_month = response.count_tasks_within_this_month(
            self._today + timedelta(days=30),
            self._today,
        )
        due_date_over_30_days = response.count_due_date_over_30_days(
            self._today + timedelta(days=30),
        )
        pending_approval = response._pending_approval
        pending_verification = response._pending_verification

        return SupervisorTabKpiResponse(
            employees=employees,
            overdue=overdue,
            tasks_within_7_days=tasks_within_7_days,
            tasks_within_this_month=tasks_within_this_month,
            due_date_over_30_days=due_date_over_30_days,
            pending_approval=pending_approval,
            pending_verification=pending_verification,
        )

    def _get_all_grouped_by_actions_limited(
        self,
        initial_request: GetChartsRequest,
    ) -> list:
        """
        Retrieve grouped action items, limiting to 5 paginated requests.

        Args:
            initial_request (GetChartsRequest): Request with filters and grouping info.

        Returns:
            list: All grouped action items collected within the request limit.

        """
        aggregated_results = []
        excluded_assignees = set()
        max_requests = 5

        for _ in range(max_requests):
            if excluded_assignees:
                initial_request.filters.not_in_assignee_external_ids = list(
                    excluded_assignees,
                )

            current_batch = (
                self._action_item_client.dashboard_kpis.get_grouped_by_actions(
                    initial_request,
                )
            )

            if not current_batch:
                break

            aggregated_results.extend(current_batch)

            if len(current_batch) < AGGREGATE_LIMIT:
                break

            excluded_assignees.update(
                item["group"]["assignedTo"]["externalId"] for item in current_batch
            )

        return aggregated_results

    async def get_actions_grouped_by(self, request: GetChartsRequest) -> list:
        """
        Get action items grouped by a specified field (e.g., assignedTo).

        Args:
            request (GetChartsRequest): Request with grouping and filtering parameters.

        Returns:
            list: Grouped action items matching the request.

        """
        user = await self._get_user_by_email(
            request.active_user_email or "",
            request.filters.reporting_site_external_id or "",
        )
        if not user:
            return []
        (
            request.user_external_id,
            request.active_user_roles_ids,
            request.active_user_teams_ids,
        ) = (
            user.external_id.removeprefix(USER_AZURE_ATTRIBUTE_PREFIX),
            user.active_user_roles_ids,
            user.active_user_teams_ids,
        )

        return self._action_item_client.dashboard_kpis.get_grouped_by_actions(request)

    @staticmethod
    def _calculate_percentage(part: int, whole: int) -> float:
        """
        Calculate the percentage of a part in relation to the total.

        Args:
            part (int): The partial value.
            whole (int): The total value.

        Returns:
            float: Percentage (0 if total is zero), rounded to 2 decimal places.

        """
        return round((part / whole * 100), 2) if whole > 0 else 0

    @staticmethod
    def _map_kpi_result(data: dict) -> GetActionResponse:
        """
        Convert raw KPI dictionary data into a structured response.

        Args:
            data (dict): Raw data from KPI service.

        Returns:
            GetActionResponse: Parsed and structured KPI data.

        """
        general_actions = (
            ActionResult.from_node_list(data.get("generalActions", []))
            if data.get("generalActions")
            else []
        )

        return GetActionResponse(general_actions=list(general_actions))

    async def _get_user_by_email(
        self,
        email: str,
        site_external_id: str,
    ) -> Optional[UserByEmailResult]:
        """
        Retrieve a user by email and site.

        This function searches for a user based on their email address and the external ID of the reporting site.
        If the user is found, it returns the user's details; otherwise, it returns None.

        Args:
            email (str): The email address of the user to be retrieved.
            site_external_id (str): The external ID of the reporting site associated with the user.

        Returns:
            Optional[UserByEmailResult]:
                - The user details if the user is found.
                - None if no matching user is found.

        """
        user_by_email_request = GetUserRolesAndTeamsRequest(
            email=email,
            reporting_site_external_id=site_external_id,
        )

        try:
            user_service = UserComplementsService(self._action_item_client)
            user: Optional[UserByEmailResult] = await user_service.get_user_by_email(
                user_by_email_request,
            )

            if user is None:
                self._raise_unauthorized(email)

        except Exception:
            self._raise_unauthorized(email)
        else:
            return user

    def _raise_unauthorized(self, email: str) -> None:
        self._log.error(f"No user found with email: {email}")
        raise HTTPException(status_code=HTTPStatus.UNAUTHORIZED)
