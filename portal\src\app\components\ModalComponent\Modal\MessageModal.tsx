import React from 'react'
import { ClnButton, ClnCircularProgress } from '@celanese/ui-lib'
import { Box, Typography, useMediaQuery, useTheme } from '@mui/material'
import { ModalWrapper } from './ModalWrapper'
import { translate } from '@/app/common/utils/generate-translate'

type MessageModalProps = {
    name?: string
    text?: string
    open: boolean
    title?: string
    isCloseModal?: boolean
    isCancelModal?: boolean
    handleClose: () => void
    handleLeave?: () => void
    handleConfirm?: () => void
    isLoading?: boolean
    children?: React.ReactNode
}

export default function MessageModal({
    name,
    text,
    open,
    isCancelModal = false,
    isCloseModal = false,
    handleClose,
    handleLeave,
    handleConfirm,
    isLoading,
    children,
}: MessageModalProps) {
    const theme = useTheme()
    const isMobile = useMediaQuery(theme.breakpoints.down('md'))

    const styles = {
        modal: {
            position: 'absolute' as 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            width: 400,
            bgcolor: 'background.paper',
            border: '2px solid #000',
            borderRadius: '8px',
            boxShadow: 24,
            padding: 3,
        },
        buttonContainer: {
            display: 'flex',
            justifyContent: isCloseModal ? 'flex-end' : 'center',
            gap: handleConfirm ? 1 : '10px',
            '@media (max-width:600px)': {
                flexDirection: 'column',
                gap: '0.5rem',
                alignItems: 'center',
            },
        },
    }

    const renderContent = () => {
        if (isLoading) {
            return (
                <Box
                    sx={{
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                        width: isMobile ? '90vw' : '20vw',
                        pb: '1.5rem',
                    }}
                >
                    <ClnCircularProgress size={40} value={0} />
                </Box>
            )
        }

        const actionButtons = (
            <Box
                sx={
                    handleConfirm
                        ? styles.buttonContainer
                        : {
                              display: 'flex',
                              justifyContent: isCloseModal ? 'flex-end' : 'center',
                              gap: '10px',
                              marginTop: '2rem',
                              width: '100%',
                          }
                }
            >
                <ClnButton
                    variant={handleConfirm ? 'text' : 'outlined'}
                    label={translate(`requestModal.${isCloseModal ? 'close' : 'cancel'}`)}
                    onClick={() => {
                        handleClose()
                    }}
                    style={handleConfirm ? {} : { width: '10rem' }}
                    data-test="new_action_item_flow_1_cancel_modal-cancel_button"
                    data-origin="ui-lib"
                    color={handleConfirm && 'error'}
                    disabled={isLoading}
                />

                {isCancelModal && (
                    <ClnButton
                        variant="outlined"
                        label={translate('requestModal.leave')}
                        onClick={handleLeave || handleClose}
                        style={handleConfirm ? {} : { width: '10rem' }}
                        data-test="new_action_item_flow_1_cancel_modal-leave_button"
                        data-origin="ui-lib"
                    />
                )}

                {handleConfirm && (
                    <ClnButton
                        variant="contained"
                        label={translate('requestModal.confirm')}
                        onClick={handleConfirm}
                        data-test="new_action_item_flow_1_cancel_modal-confirm_button"
                        data-origin="ui-lib"
                        disabled={isLoading}
                    />
                )}
            </Box>
        )

        function getDisplayText(): string {
            const formatedText = text ?? ''
            if (handleConfirm) {
                return formatedText
            }

            return name ? `${text} ${name}` : formatedText
        }

        return (
            <Box
                sx={
                    handleConfirm
                        ? {
                              maxWidth: isMobile ? '90vw' : '700px',
                              padding: '20px',
                              fontFamily: 'Roboto',
                              paddingTop: 0,
                              display: 'flex',
                              flexDirection: 'column',
                              flexWrap: 'wrap',
                              gap: '1.5rem',
                          }
                        : {
                              padding: '1rem 1.5rem',
                              textAlign: 'flex-start',
                              width: '80vw',
                              maxWidth: '600px',
                              '@media (max-width: 600px)': { width: '100%' },
                          }
                }
            >
                {!children && (
                    <Typography sx={handleConfirm ? {} : { fontWeight: 'bold' }}>{getDisplayText()}</Typography>
                )}

                {children}

                {actionButtons}
            </Box>
        )
    }

    return (
        <ModalWrapper
            title={name}
            openModal={open}
            closeModal={handleClose}
            sxProps={{ backgroundColor: 'rgba(0, 0, 0, 0.5)', minHeight: '200px' }}
            sxPropsTitle={
                handleConfirm
                    ? {
                          fontSize: '24px',
                          marginRight: 'auto',
                          paddingLeft: '20px',
                          maxWidth: '55vw',
                      }
                    : {
                          color: 'primary.dark',
                          fontSize: '19px',
                          marginRight: 'auto',
                      }
            }
            content={renderContent()}
        ></ModalWrapper>
    )
}
