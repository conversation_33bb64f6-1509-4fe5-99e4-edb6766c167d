# Manifest file downloaded from fusion
externalId: tr-AIM-COR-ALL-RAW-ICAP-STG-ACTION-DEL
name: AIM-COR-ALL-RAW-ICAP-STG-ACTION-DEL
query: "SELECT

  \  \tuuid() AS key,

  \  \taim_action.externalId,

  \  \taim_action.space,

  \  \taim_action.objectType,

  \  \ttimestamp(aim_action.`node.createdTime`/1000) AS createdAt,

  \  \tstg_action.externalId AS correctExternalId,

  \  \tstg_action.space AS correctSpace,

  \  \tstg_action.objectType AS correctObjectType,

  \  \tcurrent_timestamp() AS `identifiedAt`,

  \  \t'Mismatch between externalId or space in ICAP-STG-Action' AS reason

  FROM `AIM-COR`.`ICAP-STG-Action` stg_action

  INNER JOIN cdf_data_models(\"AIM-COR-ALL-DMD\", \"ActionItemManagementDOM\",
  \"6_0_0\", \"Action\") AS aim_action

  \  \tON split_part(aim_action.objectType, '-', 2) = stg_action.key\ 

  \  \tAND startswith(aim_action.objectType, 'ICAP')

  WHERE\ 

  \  (

  \    stg_action.externalId <> aim_action.externalId

  \    OR stg_action.space <> aim_action.space

  \  ) AND aim_action.viewOnly


  \ "
destination:
  database: AIM-COR
  table: ICAP-STG-Action-DEL
  type: raw
ignoreNullFields: true
shared: true
action: upsert
dataSetExternalId: AIM-COR-ALL-DAT
# Specify credentials separately like this:
# authentication:
#   read:
#     clientId: ${READ_CLIENT_ID}
#     clientSecret: ${READ_CLIENT_SECRET}
#     tokenUrl: ${READ_TOKEN_URL}
#     cdfProjectName: ${READ_CDF_PROJECT_NAME}
#     # Optional: If idP requires providing the scopes
#     scopes:
#       - ${READ_SCOPES}
#     # Optional: If idP requires providing the audience
#     audience: ${READ_CDF_AUDIENCE}
#   write:
#     clientId: ${WRITE_CLIENT_ID}
#     clientSecret: ${WRITE_CLIENT_SECRET}
#     tokenUrl: ${WRITE_TOKEN_URL}
#     cdfProjectName: ${WRITE_CDF_PROJECT_NAME}
#     # Optional: If idP requires providing the scopes
#     scopes:
#       - ${WRITE_SCOPES}
#     # Optional: If idP requires providing the audience
#     audience: ${WRITE_CDF_AUDIENCE}
# Or together like this:
# authentication:
#   clientId: ${CLIENT_ID}
#   clientSecret: ${CLIENT_SECRET}
#   tokenUrl: ${TOKEN_URL}
#   # Optional: If idP requires providing the scopes
#   cdfProjectName: ${CDF_PROJECT_NAME}
#   scopes:
#    - ${SCOPES}
#   # Optional: If idP requires providing the audience
#   audience: ${AUDIENCE}