trigger:
  branches:
    include:
      - prod
  paths:
    include:
      - action-items-creator-function/*

stages:
  - template: function-template.yml
    parameters:
      azureSubscription: "Action Item Management Deploy - PROD"
      functionAppName: "func-dplantactionitemmgmt-p-ussc-01"
      vmImageName: "ubuntu-latest"
      workingDirectory: "$(System.DefaultWorkingDirectory)/action-items-creator-function/"
