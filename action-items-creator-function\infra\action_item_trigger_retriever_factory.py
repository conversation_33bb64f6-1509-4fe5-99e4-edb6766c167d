from typing import Optional
import os
import sys
from uuid import uuid4
from services.action_item_trigger_retriever import ActionItemTriggerRetriever

from services.cognite_service import CogniteService
from services.graphql_service import GraphqlService
from services.logging_service import LoggingService
from services.notification_service import NotificationService


script_dir = os.path.dirname(os.path.abspath(__file__))
project_dir = os.path.dirname(script_dir)
sys.path.append(project_dir)
from infra.graphql_client_factory import GraphqlClientFactory
from infra.cognite_client_factory import CogniteClientFactory
from models.settings import Settings


class ActionItemTriggerRetrieverFactory:
    @staticmethod
    def retriever(
        settings: Settings = Settings.from_env(),
        call_id: str = str(uuid4()),
        override_token: Optional[str] = None,
    ) -> ActionItemTriggerRetriever:
        log = LoggingService(call_id)
        cognite_client = CogniteClientFactory.create(settings, override_token)
        graphl_client = GraphqlClientFactory.create(cognite_client, settings)

        return ActionItemTriggerRetriever(
            CogniteService(cognite_client, settings, log),
            GraphqlService(graphl_client, log),
            settings,
            log,
        )