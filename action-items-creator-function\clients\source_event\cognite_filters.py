from itertools import product
from typing import Any

from cognite.client.data_classes import data_modeling
from industrial_model import and_, col, or_

from clients.core.constants import (
    ActionSourceTypeEnum,
    DataSpaceEnum,
)
from clients.core.filters import (
    contains_any_filter,
    contains_sdk_filter,
    gte_filter,
    in_filter,
    in_sdk_filter,
    lt_filter,
    node_id_filter,
    prefix_filter,
    prefix_sdk_filter,
    range_sdk_filter,
)
from clients.source_event.models import SourceEventExportView
from utils.list_utils import all_casings

from .requests import (
    BaseSourceEventRequest,
    GetSourceEventRequest,
    GetSourceEventRequestForIndustrialModel,
)


def _create_node_as_dict(external_id: str, space: str) -> dict[str, str]:
    return {"externalId": external_id, "space": space}


def _get_reporting_site_filter(request: GetSourceEventRequest) -> dict[str, Any]:
    external_ids = request.reporting_site_external_ids
    if not external_ids:
        return {}

    return node_id_filter(external_ids, DataSpaceEnum.REF_DATA_SPACE)


def _get_space_filter(request: GetSourceEventRequest) -> dict[str, Any]:
    filter_private = {
        "and": [
            in_filter("space", request.get_filter_spaces(private_space=True)),
            contains_any_filter("views", request.views_private),
        ],
    }
    filter_site = in_filter("space", request.get_filter_spaces(private_space=False))

    if request.only_private:
        return filter_private
    if request.only_private is not None:
        return filter_site
    return {"or": [filter_site, filter_private]}


def _get_space_filter_for_industrial_model(
    request: GetSourceEventRequestForIndustrialModel,
) -> bool:
    filter_private = and_(
        col("space").in_(
            request.get_filter_spaces(private_space=True),
        ),
        col("views").contains_any_(request.views_private),
    )
    filter_site = col("space").in_(
        request.get_filter_spaces(private_space=False),
    )
    if request.only_private:
        return filter_private
    if request.only_private is not None:
        return filter_site
    return or_(filter_site, filter_private)


def _get_source_event_filters(request: GetSourceEventRequest) -> dict[str, Any]:
    filters: list[Any] = []

    if request.reporting_site_external_ids:
        filters.extend(
            [
                _get_space_filter(request),
                {"reportingSite": _get_reporting_site_filter(request)},
            ],
        )

    if request.external_ids:
        filters.append(in_filter("externalId", request.external_ids))

    if request.status_external_ids:
        filters.append(
            {
                "status": node_id_filter(
                    request.status_external_ids,
                    DataSpaceEnum.AIM_REF_DATA_SPACE,
                ),
            },
        )

    if request.reporting_unit_external_ids:
        filters.append(
            {
                "reportingUnit": node_id_filter(
                    request.reporting_unit_external_ids,
                    DataSpaceEnum.REF_DATA_SPACE,
                ),
            },
        )

    if request.reporting_line_external_ids:
        filters.append(
            {
                "reportingLine": node_id_filter(
                    request.reporting_line_external_ids,
                    DataSpaceEnum.REF_DATA_SPACE,
                ),
            },
        )

    if request.event_type_external_ids:
        filters.append(
            {
                "eventType": node_id_filter(
                    request.event_type_external_ids,
                    DataSpaceEnum.AIM_REF_DATA_SPACE,
                ),
            },
        )

    if request.category_external_ids:
        filters.append(
            {
                "category": node_id_filter(
                    request.category_external_ids,
                    DataSpaceEnum.AIM_REF_DATA_SPACE,
                ),
            },
        )

    if request.subcategory_external_ids:
        filters.append(
            {
                "subCategory": node_id_filter(
                    request.subcategory_external_ids,
                    DataSpaceEnum.AIM_REF_DATA_SPACE,
                ),
            },
        )

    if request.site_specific_category_external_ids:
        filters.append(
            {
                "siteSpecificCategory": node_id_filter(
                    request.site_specific_category_external_ids,
                    request.get_filter_spaces(all_sites=True),
                ),
            },
        )

    if request.external_id_prefix:
        filters.append(prefix_filter("externalId", request.external_id_prefix.upper()))

    if request.title_prefix:
        title_prefix_list = all_casings(request.title_prefix)

        title_prefix_filter = [
            prefix_filter("title", title) for title in title_prefix_list
        ]

        filters.append({"or": [*title_prefix_filter]})

    if request.due_date_gte:
        filters.append(gte_filter("displayDueDate", request.due_date_gte))

    if request.due_date_lt:
        filters.append(lt_filter("displayDueDate", request.due_date_lt))

    if request.owner_external_id:
        filters.append(
            {
                "owner": node_id_filter(
                    request.owner_external_id,
                    DataSpaceEnum.UMG_DATA_SPACE,
                ),
            },
        )

    if request.functional_location_external_ids:
        filters.append(
            contains_any_filter("searchTags", request.functional_location_external_ids),
        )

    if request.equipment_external_ids:
        filters.append(
            contains_any_filter("searchTags", request.equipment_external_ids),
        )

    return {"and": filters}


def get_source_event_query_filters(request: GetSourceEventRequest) -> dict[str, Any]:
    """
    Generate a dictionary of filters for retrieving source events based on the provided request.

    Args:
        request (GetSourceEventRequest):
            The request object containing data to generate filters, including pagination details (e.g., page size and cursor),
            source event filters, and sorting preferences (e.g., sorting field and direction).

    Returns:
        dict[str, Any]:
            A dictionary of filters that can be used to query source events, including:
            - "pageSize": The number of results per page for pagination.
            - "cursor": The pagination cursor for fetching the next set of results.
            - "sourceEventFilter": A filter that applies conditions to source event items.
            - "sorting": A dictionary containing the sorting field and the direction of the sort (e.g., ascending or descending).

    """
    return {
        "pageSize": request.page_size,
        "cursor": request.cursor,
        "sourceEventFilter": _get_source_event_filters(request),
        "actionsFilter": {
            "sourceType": {
                "not": {"externalId": {"eq": ActionSourceTypeEnum.AIM_SCRATCH}},
            },
        },
        "sorting": {request.sort_by: request.direction},
    }


def get_source_event_filters_for_aggregate(
    request: BaseSourceEventRequest | GetSourceEventRequestForIndustrialModel,
    source_event_view: data_modeling.ViewId,
) -> list[data_modeling.Filter]:
    """
    Generate a list of filters for aggregating source events based on the provided request.

    Args:
        request (BaseSourceEventRequest):
            The request object containing filter criteria for source events, including external IDs, statuses,
            reporting unit information, and other attributes.

        source_event_view (data_modeling.ViewId):
            The action view used to filter the source event data based on specific views.

    Returns:
        list[data_modeling.Filter]:
            A list of filters that can be applied to query and aggregate source events. These filters include:
            - "SpaceFilter": Filters based on private or site-specific spaces.
            - "Equals": Filters for exact matches on attributes like `reportingSite`, `eventType`, etc.
            - "In": Filters for matching multiple external IDs.
            - "ContainsAny": Filters for checking if the attribute contains any of the provided external IDs.
            - "Range": Filters for date-based conditions, such as `due_date_gte` or `due_date_lt`.
            - "Prefix": Filters for prefix matching on attributes like `externalId` or `title`.

    """

    def _in_filter(
        attribute: str,
        external_ids: list[str],
        spaces: list[str] | None = None,
    ) -> data_modeling.filters.In:
        external_ids_mapping = (
            external_ids
            if spaces is None
            else [
                _create_node_as_dict(external_id, space)
                for external_id, space in product(external_ids, spaces)
            ]
        )

        return in_sdk_filter(
            attribute,
            external_ids_mapping,
            source_event_view,
        )

    def _contains_filter(
        attribute: str,
        external_ids: list[str],
    ) -> data_modeling.filters.ContainsAny:
        return contains_sdk_filter(
            attribute,
            external_ids,
            source_event_view,
        )

    def _get_space_filter_for_aggregate(
        request: BaseSourceEventRequest | GetSourceEventRequestForIndustrialModel,
    ) -> (
        data_modeling.filters.Or
        | data_modeling.filters.And
        | data_modeling.filters.SpaceFilter
    ):
        filter_private = data_modeling.filters.And(
            data_modeling.filters.SpaceFilter(
                request.get_filter_spaces(private_space=True),
            ),
            _contains_filter("views", request.views_private),
        )

        filter_site = data_modeling.filters.SpaceFilter(
            request.get_filter_spaces(private_space=False),
        )

        if request.only_private:
            return filter_private
        if request.only_private is not None:
            return filter_site
        return data_modeling.filters.Or(filter_site, filter_private)

    source_event_filters: list[data_modeling.Filter] = []

    if request.reporting_site_external_ids:
        source_event_filters.extend(
            [
                _get_space_filter_for_aggregate(request),
                _in_filter(
                    "reportingSite",
                    request.reporting_site_external_ids,
                    [DataSpaceEnum.REF_DATA_SPACE],
                ),
            ],
        )

    if request.external_ids:
        source_event_filters.append(
            _in_filter(
                "externalId",
                request.external_ids,
            ),
        )

    if request.status_external_ids:
        source_event_filters.append(
            _in_filter(
                "status",
                request.status_external_ids,
                [DataSpaceEnum.AIM_REF_DATA_SPACE],
            ),
        )

    if request.reporting_unit_external_ids:
        source_event_filters.append(
            _in_filter(
                "reportingUnit",
                request.reporting_unit_external_ids,
                [DataSpaceEnum.REF_DATA_SPACE],
            ),
        )

    if request.event_type_external_ids:
        source_event_filters.append(
            _in_filter(
                "eventType",
                request.event_type_external_ids,
                [DataSpaceEnum.AIM_REF_DATA_SPACE],
            ),
        )

    if request.category_external_ids:
        source_event_filters.append(
            _in_filter(
                "category",
                request.category_external_ids,
                [DataSpaceEnum.AIM_REF_DATA_SPACE],
            ),
        )

    if request.subcategory_external_ids:
        source_event_filters.append(
            _in_filter(
                "subCategory",
                request.subcategory_external_ids,
                [DataSpaceEnum.AIM_REF_DATA_SPACE],
            ),
        )

    if request.site_specific_category_external_ids:
        all_spaces = request.get_filter_spaces(all_sites=True)
        source_event_filters.append(
            _in_filter(
                "siteSpecificCategory",
                request.site_specific_category_external_ids,
                all_spaces,
            ),
        )

    if request.external_id_prefix:
        source_event_filters.append(
            prefix_sdk_filter(
                "externalId",
                request.external_id_prefix.upper(),
                source_event_view,
            ),
        )

    if request.title_prefix:
        tile_prefix_list = all_casings(request.title_prefix)

        title_prefix_filter = [
            prefix_sdk_filter("title", title, source_event_view)
            for title in tile_prefix_list
        ]

        source_event_filters.append(data_modeling.filters.Or(*title_prefix_filter))

    if request.due_date_gte:
        source_event_filters.append(
            range_sdk_filter(
                "displayDueDate",
                source_event_view,
                gte=request.due_date_gte.isoformat(),
            ),
        )

    if request.due_date_lt:
        source_event_filters.append(
            range_sdk_filter(
                "displayDueDate",
                source_event_view,
                lt=request.due_date_lt.isoformat(),
            ),
        )

    if request.owner_external_id:
        source_event_filters.append(
            _in_filter(
                "owner",
                request.owner_external_id,
                [DataSpaceEnum.UMG_DATA_SPACE],
            ),
        )

    if request.functional_location_external_ids:
        source_event_filters.append(
            _contains_filter("searchTags", request.functional_location_external_ids),
        )

    if request.equipment_external_ids:
        source_event_filters.append(
            _contains_filter("searchTags", request.equipment_external_ids),
        )

    return source_event_filters


def get_source_event_filters_for_industrial_model(
    request: GetSourceEventRequestForIndustrialModel,
) -> bool:
    """
    Generate filters for querying source events based on the provided request parameters.

    Args:
        request (GetSourceEventRequestForIndustrialModel): The request object containing filter parameters.

    Returns:
        bool: A boolean expression representing the combined filters for querying source events.

    """
    filters: list[bool] = []

    if request.reporting_site_external_ids:
        filters.extend(
            [
                _get_space_filter_for_industrial_model(request),
                col(SourceEventExportView.reporting_site).nested_(
                    col("externalId").in_(request.reporting_site_external_ids),
                ),
            ],
        )

    if request.site_specific_category_external_ids:
        filters.append(
            col(SourceEventExportView.site_specific_category).nested_(
                and_(
                    col("externalId").in_(request.site_specific_category_external_ids),
                    col("space").in_(request.get_filter_spaces(all_sites=True)),
                ),
            ),
        )

    if request.due_date_gte:
        filters.append(
            col(SourceEventExportView.display_due_date).gte_(
                request.due_date_gte.isoformat(),
            ),
        )

    if request.due_date_lt:
        filters.append(
            col(SourceEventExportView.display_due_date).lt_(
                request.due_date_lt.isoformat(),
            ),
        )

    if request.external_id_prefix:
        filters.append(
            col(SourceEventExportView.external_id).prefix(
                request.external_id_prefix.upper(),
            ),
        )

    if request.title_prefix:
        variants = all_casings(request.title_prefix)
        filters.append(
            or_(*[col(SourceEventExportView.title).prefix(v) for v in variants]),
        )

    return and_(*filters)
