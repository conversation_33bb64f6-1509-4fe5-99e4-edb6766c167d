'use client'
import { NoTranslate } from '@celanese/celanese-ui'
import { Box, Grid, Typography } from '@mui/material'
import { useEffect, useMemo } from 'react'
import dayjs, { Dayjs } from 'dayjs'
import { UploadFiles } from '@/app/components/UploadFiles/uploadFile'
import { GridColDef, GridRowsProp } from '@mui/x-data-grid-pro'
import { DataGridTable } from '@/app/components/PaginatedTable/DataGridTable'
import GenericFieldTitle from '../../FieldsComponent/GenericFieldTitle'
import GenericTextField from '../../FieldsComponent/GenericTextField'
import { useForm } from 'react-hook-form'
import { z } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'
import GenericDateRangePicker from '../../FieldsComponent/GenericDateRangePicker'
import { SourceEvent } from '@/app/common/models/source-event'
import { ClnPanel } from '@celanese/ui-lib'
import { translate } from '@/app/common/utils/generate-translate'

type Props = { sourceEvent?: SourceEvent }

const zodDate = z.instanceof(dayjs as unknown as typeof Dayjs).nullable()

const formSchema = z.object({
    owner: z.string(),
    startDate: zodDate,
    dueDate: zodDate,
})

type FormSchema = z.infer<typeof formSchema>

export function DetailsTab(params: Props) {
    const { sourceEvent } = params

    const { control, getValues, reset } = useForm<FormSchema>({
        resolver: zodResolver(formSchema),
        defaultValues: {
            startDate: undefined,
            dueDate: undefined,
            owner: '-',
        },
    })

    const ownersList: any[] = useMemo(() => {
        function convertListUsersToRows(items: any[], type: string) {
            return items.map((item) => ({
                id: `${item.externalId}`,
                users: type === 'user' ? `${item.user?.lastName}, ${item.user?.firstName}` : `${item.name}`,
                assignments: `${translate(`stepper.form.${type}`)}`,
            }))
        }

        let owners: any[] = []
        if (sourceEvent != null) {
            const ownersUsers =
                sourceEvent.secondaryOwnerUsers && sourceEvent.secondaryOwnerUsers?.length > 0
                    ? convertListUsersToRows(sourceEvent.secondaryOwnerUsers, 'user')
                    : []
            const ownersRoles =
                sourceEvent.secondaryOwnerRoles && sourceEvent.secondaryOwnerRoles?.length > 0
                    ? convertListUsersToRows(sourceEvent.secondaryOwnerRoles, 'role')
                    : []
            const ownersTeams =
                sourceEvent.secondaryOwnerTeams && sourceEvent.secondaryOwnerTeams?.length > 0
                    ? convertListUsersToRows(sourceEvent.secondaryOwnerTeams, 'team')
                    : []

            owners = [...ownersUsers, ...ownersRoles, ...ownersTeams]
        }
        return owners
    }, [sourceEvent])

    const headCellsBySecondaryOwners: GridColDef[] = useMemo(() => {
        const columns: GridColDef[] = [
            {
                field: 'users',
                headerName: translate('table.headers.users'),
                flex: 1,
                renderCell: (params) => <NoTranslate>{params.row.users}</NoTranslate>,
                renderHeader: () => (
                    <span data-test="secondary-owners-details-tab-users_table_sort" data-origin="aim">
                        {translate('table.headers.users')}
                    </span>
                ),
            },
            {
                field: 'assignments',
                headerName: translate('table.headers.assignments'),
                flex: 1,
                renderCell: (params) => <NoTranslate>{params.row.assignments}</NoTranslate>,
                renderHeader: () => (
                    <span data-test="secondary-owners-details-tab-assignments_table_sort" data-origin="aim">
                        {translate('table.headers.assignments')}
                    </span>
                ),
            },
        ]

        return columns
    }, [translate, ownersList])

    const tableRowsBySecondaryOwners: GridRowsProp = useMemo(() => {
        function convertActionItemDataToRows(items: any[]) {
            return items.map((item) => ({
                id: `${item.id}`,
                users: `${item.users}`,
                assignments: `${item.assignments}`,
            }))
        }

        const ownersRows = ownersList != null && ownersList.length > 0 ? convertActionItemDataToRows(ownersList) : []

        return ownersRows
    }, [ownersList])

    const attachmentsUploaded = useMemo(() => {
        return sourceEvent?.attachments || []
    }, [sourceEvent])

    useEffect(() => {
        if (sourceEvent) {
            reset({
                startDate: dayjs(sourceEvent?.assignmentDate),
                dueDate: dayjs(sourceEvent?.dueDate),
                owner: sourceEvent?.owner
                    ? `${sourceEvent?.owner.user?.lastName}, ${sourceEvent?.owner.user?.firstName}`
                    : '-',
            })
        }
    }, [sourceEvent, reset])

    return (
        <Box
            sx={{
                width: '100%',
                height: '100%',
                display: 'grid',
                alignItems: 'start',
                gridTemplateColumns: '1fr',
                marginRight: '10px',
                marginTop: '1rem',
            }}
        >
            <Grid container spacing={2} sx={{ height: '100%' }}>
                <Grid item md={6} xs={12}>
                    <ClnPanel
                        id="event-details-owner"
                        sx={{
                            padding: '1rem',
                            display: 'flex',
                            flexDirection: 'column',
                            marginBottom: '1rem',
                        }}
                    >
                        <GenericFieldTitle fieldName={translate('details.fields.eventOwner')} isSubHeader />
                        <GenericTextField
                            name={'owner'}
                            control={control}
                            valueController={getValues('owner')}
                            disabled
                        />
                    </ClnPanel>
                    <ClnPanel
                        id="event-details-secondary-owners"
                        sx={{
                            padding: '1rem',
                            display: 'flex',
                            flexDirection: 'column',
                            marginBottom: '1rem',
                        }}
                    >
                        <GenericFieldTitle fieldName={translate('details.fields.secondaryOwners')} isSubHeader />
                        <Box>
                            <DataGridTable
                                initialColumnDefs={headCellsBySecondaryOwners}
                                rows={tableRowsBySecondaryOwners}
                                infinitePagination
                                removeOptionsColumnsManager
                                maxHeight={150}
                                showOptionsBar={false}
                            ></DataGridTable>
                        </Box>
                    </ClnPanel>
                    <ClnPanel
                        id="event-details-description"
                        sx={{
                            padding: '1rem',
                            display: 'flex',
                            flexDirection: 'column',
                            marginBottom: '1rem',
                        }}
                    >
                        <Box
                            sx={{
                                gap: 2,
                                display: 'flex',
                                flexDirection: 'column',
                                height: '100%',
                            }}
                        >
                            <GenericFieldTitle fieldName={translate('details.fields.description')} isSubHeader />
                            <Box
                                sx={{
                                    wordWrap: 'break-word !important',
                                    whiteSpace: 'normal !important',
                                    height: '100%',
                                }}
                            >
                                <Typography
                                    sx={{
                                        wordBreak: 'break-word',
                                        whiteSpace: 'normal',
                                        overflowWrap: 'break-word',
                                        flexShrink: 1,
                                    }}
                                >
                                    {sourceEvent?.description}
                                </Typography>
                            </Box>
                        </Box>
                    </ClnPanel>
                </Grid>
                <Grid item md={6} xs={12}>
                    <ClnPanel
                        id="event-details-date"
                        sx={{
                            padding: '1rem',
                            display: 'flex',
                            flexDirection: 'column',
                            marginBottom: '1rem',
                        }}
                    >
                        <Grid container spacing={2}>
                            <Grid item xs={12} md={6}>
                                <Box sx={{ display: 'flex', flexDirection: 'column', gap: '5px' }}>
                                    <GenericFieldTitle
                                        fieldName={translate('stepper.form.assignment.draweStartDate')}
                                        isSubHeader
                                    />
                                    <GenericDateRangePicker
                                        name="startDate"
                                        control={control}
                                        size="small"
                                        isRange={false}
                                        valueController={dayjs(sourceEvent?.assignmentDate)}
                                        label=""
                                        disabled
                                        sxProps={{ width: '100%' }}
                                    />
                                </Box>
                            </Grid>
                            <Grid item xs={12} md={6}>
                                <Box sx={{ display: 'flex', flexDirection: 'column', gap: '5px' }}>
                                    <GenericFieldTitle
                                        fieldName={translate('stepper.form.assignment.dueDate')}
                                        isSubHeader
                                    />
                                    <GenericDateRangePicker
                                        name="dueDate"
                                        control={control}
                                        size="small"
                                        isRange={false}
                                        valueController={dayjs(sourceEvent?.dueDate)}
                                        label=""
                                        disabled
                                        sxProps={{ width: '100%' }}
                                    />
                                </Box>
                            </Grid>
                        </Grid>
                    </ClnPanel>
                    <Box sx={{ height: '75%', paddingBottom: '2rem' }}>
                        <ClnPanel
                            id="event-details-files"
                            sx={{
                                padding: '1rem',
                                display: 'flex',
                                flexDirection: 'column',
                                marginBottom: '1rem',
                                height: '100%',
                            }}
                        >
                            <UploadFiles
                                oldUploadedFiles={attachmentsUploaded}
                                sxProps={{ maxHeight: '215px', overflowY: 'auto' }}
                            />
                        </ClnPanel>
                    </Box>
                </Grid>
            </Grid>
        </Box>
    )
}

export default DetailsTab
