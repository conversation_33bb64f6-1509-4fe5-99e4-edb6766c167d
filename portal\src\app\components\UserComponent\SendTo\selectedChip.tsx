import { ClnButton, MatIcon } from '@celanese/ui-lib'
import { Box, Typography } from '@mui/material'
import * as styles from './sendTo.styles'

interface SelectedChipProps {
    key: string
    label: string
    isGroup?: boolean
    onExpandClick?: () => void
    onRemoveClick?: () => void
}

export default function SelectedChip({ label, isGroup = false, onExpandClick, onRemoveClick }: SelectedChipProps) {
    return (
        <Box sx={styles.changeThisName}>
            {isGroup && (
                <ClnButton
                    onClick={onExpandClick}
                    variant="text"
                    startIcon={<MatIcon icon="add" color={'text.secondary'} />}
                    sx={{
                        '& .MuiButton-startIcon': {
                            marginRight: 0,
                        },
                    }}
                />
            )}
            <Typography sx={styles.buttonLabel} color={'text.secondary'}>
                {label}
            </Typography>
            <ClnButton
                variant="text"
                onClick={onRemoveClick}
                startIcon={<MatIcon icon="close" color={'text.secondary'} />}
                sx={{
                    '& .MuiButton-startIcon': {
                        marginRight: 0,
                    },
                }}
            />
        </Box>
    )
}
