from typing import List, Optional
from dotenv import load_dotenv
from pydantic import Field
from pydantic_settings import BaseSettings, SettingsConfigDict
import os

function_env = os.getenv("FUNCTION_ENV", "dev")


class Settings(BaseSettings):
    model_config = SettingsConfigDict(
        env_file=(".env", ".env." + function_env), extra="ignore"
    )
    cognite_base_uri: str
    cognite_project: str
    cognite_client_name: str
    graphql_uri_raw: str = Field(alias="cognite_graphql_uri")
    cognite_graphql_model_space: str
    cognite_graphql_model_external_id: str
    cognite_graphql_model_version: str
    aim_default_instances_space: str
    aim_ref_instances_space: str
    auth_client_id: str
    auth_tenant_id: str
    auth_secret: str
    notifications_client_id: str
    notifications_secret: str
    auth_scopes: str
    auth_token_uri_template: str
    user_management_instances_space: str
    asset_hierarchy_instances_space: str
    approval_workflow_ref_instances_space: str
    approval_workflow_data_model_space: str
    user_management_api_uri: str
    aim_portal_uri: str
    applications_to_ignore_events_raw: Optional[str] = Field(
        default=None, alias="applications_to_ignore_events"
    )
    applications_using_triggers_raw: Optional[str] = Field(
        default=None, alias="applications_using_triggers"
    )

    @property
    def auth_token_uri(self) -> str:
        return self.auth_token_uri_template.replace("@tenant_id", self.auth_tenant_id)

    @property
    def get_auth_scopes(self) -> List[str]:
        return self.auth_scopes.split(",")

    @property
    def graphql_uri(self) -> str:
        return (
            self.graphql_uri_raw.replace("@base_uri", self.cognite_base_uri)
            .replace("@project", self.cognite_project)
            .replace("@model_space", self.cognite_graphql_model_space)
            .replace("@model_external_id", self.cognite_graphql_model_external_id)
            .replace("@model_version", self.cognite_graphql_model_version)
        )

    @property
    def applications_to_ignore_events(self) -> List[str]:
        return (
            []
            if self.applications_to_ignore_events_raw in (None, "")
            else self.applications_to_ignore_events_raw.split(",")
        )

    @property
    def applications_using_triggers(self) -> List[str]:
        return (
            []
            if self.applications_using_triggers_raw in (None, "")
            else self.applications_using_triggers_raw.split(",")
        )

    @classmethod
    def from_env(cls, **kwargs) -> "Settings":
        load_dotenv(override=True)
        return cls(**kwargs)
