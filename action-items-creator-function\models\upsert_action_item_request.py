import os
import sys
from datetime import date, datetime
from typing import Annotated
from zoneinfo import ZoneInfo

from annotated_types import Len
from pydantic import BaseModel, Field, model_validator

from models.action_item import (
    BaseRecurrenceInstance,
)

script_dir = os.path.dirname(os.path.abspath(__file__))
project_dir = os.path.dirname(script_dir)
sys.path.append(project_dir)
from clients.actions.constants import RecurrenceTypeExternalId
from clients.actions.validators import validate_recurrence
from clients.core.constants import ActionItemKind


class Attachment(BaseModel):
    received_external_id: str = Field(min_length=1)
    target_external_id: str | None = Field(min_length=1, default=None)
    action_item_external_ids: Annotated[list[str], Len(min_length=1)] = Field(
        default_factory=list,
    )
    name: str | None = Field(min_length=1, default=None)
    mime_type: str | None = Field(min_length=1, default=None)
    target_dataset_id: int | None = None
    file_size: str | None = None
    user: str | None = Field(min_length=1, default=None)

    @property
    def is_file_from_aim(self) -> bool:
        """Checks if the file originates from AIM."""
        return self.received_external_id == self.target_external_id


class UpsertActionItemLink(BaseModel):
    link: str = Field(min_length=1)
    description: str | None = Field(min_length=1, default=None)


class UpsertRecurrenceInstance(BaseRecurrenceInstance):
    recurrenceType: RecurrenceTypeExternalId

    @model_validator(mode="after")
    def check_recurrence_consistency(self) -> "UpsertRecurrenceInstance":
        """
        Validate the consistency of recurrence data for an UpsertRecurrenceInstance.

        This method ensures that the recurrence attributes are correctly set by
        calling `validate_recurrence`, which checks the validity of recurrence
        parameters based on the recurrence type.

        Args:
            self (UpsertRecurrenceInstance): The instance being validated.

        Returns:
            UpsertRecurrenceInstance: The validated instance if all checks pass.

        Raises:
            ValueError: If any recurrence attribute is inconsistent with the specified
                        recurrence type.

        """
        validate_recurrence(
            recurrence_type_external_id=self.recurrenceType,
            week_days=self.weekDays,
            months=self.months,
            day_of_the_month=self.dayOfTheMonth,
            quarters=self.quarters,
            month_of_the_year=self.monthOfTheYear,
            next_dates=self.nextDates,
        )
        return self


class UpsertMetadata(BaseModel):
    metadata_field_type_external_id: str = Field(
        min_length=1,
        alias="metadataFieldTypeExternalId",
    )
    value: str = Field(min_length=1)


class UpsertActionItemRequest(BaseModel):
    title: str = Field(min_length=10, max_length=200)
    description: str = Field(min_length=10, max_length=1000)
    application_id: str = Field(min_length=1, alias="applicationId")
    category_id: str = Field(min_length=1, default="ACTC-General", alias="categoryId")
    assigned_to_ids: Annotated[list[str], Len(min_length=1)] = Field(
        alias="assignedToIds",
    )
    due_date: date | None = Field(default=None, alias="dueDate")
    approval_date: date | None = Field(default=None, alias="approvalDate")
    time_zone: str | None = Field(default=None, alias="timeZone")
    verification_date: date | None = Field(default=None, alias="verificationDate")
    conclusion_date: date | None = Field(default=None, alias="conclusionDate")
    owner_id: str | None = Field(min_length=1, alias="ownerId", default=None)
    approver_id: str | None = Field(min_length=1, default=None, alias="approverId")
    verifier_id: str | None = Field(min_length=1, default=None, alias="verifierId")
    priority: str | None = Field(min_length=1, max_length=100, default=None)
    business_line_id: str | None = Field(
        min_length=1,
        default=None,
        alias="businessLineId",
    )
    evidence_required: bool | None = Field(default=None, alias="evidenceRequired")
    is_plant_shutdown_required: str | None = Field(
        min_length=1,
        default=None,
        alias="isPlantShutdownRequired",
    )
    price_currency_key: str | None = Field(
        min_length=1,
        max_length=100,
        default=None,
        alias="priceCurrencyKey",
    )
    estimated_grade: str | None = Field(
        min_length=1,
        max_length=100,
        default=None,
        alias="estimatedGrade",
    )
    site_specific_category_id: str | None = Field(
        min_length=1,
        default=None,
        alias="siteSpecificCategoryId",
    )
    reporting_unit_id: str | None = Field(
        min_length=1,
        default=None,
        alias="reportingUnitId",
    )
    reporting_location_id: str | None = Field(
        min_length=1,
        default=None,
        alias="reportingLocationId",
    )
    links: Annotated[list[UpsertActionItemLink], Len(min_length=1)] | None = None
    attachment_ids: Annotated[list[str], Len(min_length=1)] | None = Field(
        default=None,
        alias="attachmentIds",
    )
    source_information: str | None = Field(
        min_length=1,
        max_length=500,
        default=None,
        alias="sourceInformation",
    )
    sub_category_id: str | None = Field(
        min_length=1,
        default=None,
        alias="subCategoryId",
    )
    action_item_kind_id: ActionItemKind = Field(
        default=ActionItemKind.ONE_TIME,
        alias="actionItemKindId",
    )
    recurrence_instance: UpsertRecurrenceInstance | None = Field(
        default=None,
        alias="recurrenceInstance",
    )
    assignment_date: date | None = Field(default=None, alias="assignmentDate")
    voe_action_item: str | None = Field(
        min_length=1,
        max_length=500,
        default=None,
        alias="voeActionItem",
    )
    estimated_cost: float | None = Field(default=None, alias="estimatedCost")
    price: str | None = Field(min_length=1, default=None)
    action_taken: str | None = Field(
        min_length=1,
        max_length=500,
        default=None,
        alias="actionTaken",
    )
    object_type: str | None = Field(
        min_length=1,
        max_length=100,
        default=None,
        alias="objectType",
    )
    object_id: str | None = Field(
        min_length=1,
        max_length=100,
        default=None,
        alias="objectId",
    )
    reporting_site_id: str = Field(min_length=1, alias="reportingSiteId")
    reporting_line_id: str | None = Field(
        min_length=1,
        default=None,
        alias="reportingLineId",
    )
    created_by_id: str = Field(min_length=1, alias="createdById")
    target_dataset_id: int | None = Field(default=None)
    source_id: str | None = Field(min_length=1, default=None, alias="sourceId")
    source_type_id: str | None = Field(
        default=None,
        alias="sourceTypeId",
    )
    source_event_id: str | None = Field(default=None, alias="sourceEventId")
    private: bool = Field(default=False)
    view_users: list[str] = Field(alias="viewUsers", default_factory=list)
    view_roles: list[str] = Field(alias="viewRoles", default=list)
    view_teams: list[str] = Field(alias="viewTeams", default=list)
    metadatas: Annotated[list[UpsertMetadata], Len(min_length=1)] | None = None
    _event_id: str

    @model_validator(mode="after")
    def validate_action_item_kind(self) -> "UpsertActionItemRequest":
        """
        Validate the consistency of action item kind and its related attributes.

        This method ensures that:
        - A recurring action item (`RECURRING`) must have a `recurrence_instance`.
        - A one-time action item (`ONE_TIME`) must not have a `recurrence_instance`.
        - A one-time action item must have both `assignment_date` and `due_date`.
        - The `assignment_date` must be before the `due_date` for a one-time action item.
        - The `due_date` must be in the future (relative to the provided time zone, if available).

        Args:
            self (UpsertActionItemRequest): The instance being validated.

        Returns:
            UpsertActionItemRequest: The validated instance if all checks pass.

        Raises:
            ValueError: If any of the above validation rules are violated.

        """
        if (
            self.action_item_kind_id == ActionItemKind.RECURRING
            and not self.recurrence_instance
        ):
            msg = "Missing recurrenceInstance for recurring action item."
            raise ValueError(msg)

        if (
            self.action_item_kind_id == ActionItemKind.ONE_TIME
            and self.recurrence_instance
        ):
            msg = "recurrenceInstance should be None for oneTime action item."
            raise ValueError(msg)

        if self.action_item_kind_id == ActionItemKind.ONE_TIME and (
            not self.assignment_date or not self.due_date
        ):
            msg = "Missing assignmentDate or dueDate for oneTime action item."
            raise ValueError(msg)

        if (
            self.action_item_kind_id == ActionItemKind.ONE_TIME
            and self.assignment_date > self.due_date
        ):
            msg = "assignmentDate must be before dueDate."
            raise ValueError(msg)

        if (
            self.action_item_kind_id == ActionItemKind.ONE_TIME
            and self.due_date is not None
        ):
            tz = ZoneInfo(self.time_zone) if self.time_zone is not None else None
            current_date_in_tz = datetime.now(tz) if tz is not None else datetime.now()

            if self.due_date < current_date_in_tz.date():
                msg = "dueDate must be after today."
                raise ValueError(msg)

        return self
