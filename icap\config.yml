version: 1.0

webhook_uri: https://radixengazure.webhook.office.com/webhookb2/d3f9e2e8-17b7-417d-9714-92bededa2ccd@9339fb1c-0944-4fb9-808d-a278e53590e5/IncomingWebhook/14616fbff98b4be8a50b8eff9e98df43/87906db3-1c48-4694-a8a7-f9d46d1424f7/V2MkfzLUZF-wpZ7807W1l6YDIkUuPTMlWbC70PiE4X4AQ1

functions:
  -
    name: "FUNC-AIM-COR-ALL-ICAP-VAL"
    function_path: "validation_function.py"
    ignore_folders: [
            ".venv",
            ".ruff_cache",
            ".mypy_cache",
            "__pycache__",
            "referece_data",
            "workflows",
            "transformations",
            "transformations_prod"
        ]
    ignore_files: [".env", ".env.local"]
  -
    name: "FUNC-AIM-COR-ALL-ICAP-FILE"
    function_path: "file_migration_function.py"
    ignore_folders: [
            ".venv",
            ".ruff_cache",
            ".mypy_cache",
            "__pycache__",
            "referece_data",
            "workflows",
            "transformations",
            "transformations_prod"
        ]
    ignore_files: [".env", ".env.local"]
reference_data:
    - 
      database: AIM-COR
      sheet:
        file: "AIM-COR.xlsx"
        workbooks:
          - 
            name: ICAP-MAP-EmailSanitization
            key: email # default: key
            mode: override # upsert or override (default: upsert)
            destination_table: "ICAP-MAP-EmailSanitization" # default: same name as the workbook name
          - 
            name: ICAP-MAP-ReportingSite
            struct_columns: ["ah_reporting_site"]
          - 
            name: ICAP-MAP-BusinessLine
            struct_columns: ["ah_business_line"]
          - 
            name: ICAP-MAP-ReportingUnit&Location
            struct_columns: ["ah_reporting_unit", "ah_reporting_location", "ah_reporting_line"]
          - 
            name: ICAP-MAP-Category
            struct_columns: ["aim_subcategory", "aim_category"]
          - 
            name: ICAP-MAP-SourceType
            struct_columns: ["source_type"]
          - 
            name: ICAP-MAP-ActionStatus
            struct_columns: ["status"]
          - 
            name: ICAP-MAP-EventStatus
            struct_columns: ["status"]
          - 
            name: ICAP-MAP-NewUser
            key: email

      ensure_exists:
        - ICAP-VAL-Action
        - ICAP-VAL-Event
        - ICAP-VAL-MissingAction
        - ICAP-VAL-MissingEvent
        - ICAP-VAL-MissingAsset
        - ICAP-VAL-MissingUser
        - ICAP-VAL-ActionMigrationProgress
        - ICAP-VAL-EventMigrationProgress
        - ICAP-STG-Action
        - ICAP-STG-Event
        - ICAP-STG-File
        - ICAP-STG-File-DLQ
        - ICAP-STG-Action-DEL