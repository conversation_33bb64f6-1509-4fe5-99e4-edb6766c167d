import { <PERSON><PERSON><PERSON><PERSON><PERSON>, ClnCircularProgress } from '@celanese/ui-lib'
import { zodResolver } from '@hookform/resolvers/zod'
import { Box, Typography, useMediaQuery, useTheme, styled } from '@mui/material'
import { useRouter } from 'next/navigation'
import { useState } from 'react'
import { SubmitHandler, useForm } from 'react-hook-form'
import { z } from 'zod'
import { ActionDetailItem } from '@/app/common/models/action-detail'
import { AzureFunctionClient } from '@/app/common/clients/azure-function-client'
import { ModalWrapper } from '../ModalComponent/Modal/ModalWrapper'
import GenericTextField from '../FieldsComponent/GenericTextField'
import { translate } from '@/app/common/utils/generate-translate'
import { getLocalUserSite } from '@celanese/celanese-ui'
import { SITE_EXTERNAL_ID_REQUIRED_FIELD } from '@/app/common/utils/validate-codes'

type RemoveItemModalProps = {
    routerPush: string
    actionDetails?: ActionDetailItem
    activeUserEmail: string
    openModal: boolean
    handleClose: () => void
    handleAlert: (message: string, error?: boolean) => void
    titleLabel?: string
    questionLabel?: string
    isDelete?: boolean
    comments?: boolean
    challengeRequest?: boolean
    redirectToPage?: () => void
}

const formSchema = z.object({
    comments: z.string().min(10).max(300),
})
type RequestSchema = z.infer<typeof formSchema>

const actionButtonsStyle = {
    display: 'flex',
    justifyContent: 'flex-end',
    gap: 1,
    '@media (max-width:600px)': {
        flexDirection: 'column',
        gap: '0.5rem',
        alignItems: 'center',
    },
}

const Form = styled('form')({
    width: '100% !important',
    '@media (max-width:600px)': {
        padding: '1.5rem',
    },
})

export default function RemoveItemModal({
    routerPush,
    actionDetails,
    activeUserEmail,
    openModal,
    handleClose,
    handleAlert,
    titleLabel,
    questionLabel,
    isDelete = false,
    comments = false,
    challengeRequest,
    redirectToPage,
}: RemoveItemModalProps) {
    const client = new AzureFunctionClient()
    const router = useRouter()
    const theme = useTheme()
    const isMobile = useMediaQuery(theme.breakpoints.down('md'))
    const [loading, setLoading] = useState<boolean>(false)

    const {
        control,
        handleSubmit,
        getValues,
        reset,
        formState: { errors },
    } = useForm<RequestSchema>({
        resolver: zodResolver(formSchema),
        defaultValues: {
            comments: '',
        },
    })

    const handleRemoveItem = async (comments?: string) => {
        setLoading(true)
        try {
            const site = actionDetails?.reportingSite?.externalId || getLocalUserSite()?.siteId

            if (isDelete) {
                await client?.deleteActionRequest({
                    externalIds: [actionDetails?.externalId],
                    activeUserEmail: activeUserEmail,
                    reportingSiteExternalId: site ?? SITE_EXTERNAL_ID_REQUIRED_FIELD,
                    comments: comments ? getValues('comments') : undefined,
                })
            } else {
                await client?.cancelActionRequest({
                    externalIds: [actionDetails?.externalId],
                    activeUserEmail: activeUserEmail,
                    reportingSiteExternalId: site ?? SITE_EXTERNAL_ID_REQUIRED_FIELD,
                    comments: comments,
                    challengeRequest: challengeRequest,
                })
            }

            handleAlert(`${translate('alerts.dataSavedWithSuccess')}: ${actionDetails?.externalId}`)
            localStorage.setItem('currentTabHome', '1')
            handleCloseAndReset()
            if (redirectToPage) {
                redirectToPage()
            } else {
                router.push(routerPush)
            }
        } catch (ex) {
            handleAlert(translate('alerts.unexpectedErrorOcurred'), true)
        } finally {
            setLoading(false)
        }
    }

    const submitFn: SubmitHandler<RequestSchema> = async (data) => {
        await handleRemoveItem(data.comments)
    }

    const getModalTitle = () => {
        if (titleLabel) {
            return `${titleLabel} (${actionDetails?.externalId} - ${actionDetails?.title})`
        }
        const action = isDelete ? 'deleteActionItem' : 'cancelActionItem'
        return `${translate(`requestModal.${action}`)} (${actionDetails?.externalId} - ${actionDetails?.title})`
    }

    const handleCloseAndReset = () => {
        reset()
        handleClose()
    }

    const renderContent = () => {
        if (loading) {
            return (
                <Box
                    sx={{
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                        height: '200px',
                    }}
                >
                    <ClnCircularProgress size={40} value={0} />
                </Box>
            )
        }
        const actionButtons = (
            <Box sx={{ ...actionButtonsStyle, marginTop: '1rem' }}>
                <ClnButton variant="text" label={translate('requestModal.close')} onClick={handleCloseAndReset} />
                <ClnButton
                    variant="contained"
                    label={isDelete ? translate('requestModal.delete') : translate('requestModal.cancel')}
                    color="error"
                    onClick={() => {
                        if (isDelete && !comments) {
                            handleRemoveItem()
                        }
                    }}
                    {...((isDelete && comments) || !isDelete ? { type: 'submit' } : {})}
                />
            </Box>
        )

        return (
            <Box
                sx={{
                    width: isMobile ? '90vw' : '60vw',
                    padding: isMobile ? '10px' : '20px',
                    fontFamily: 'Roboto',
                    paddingTop: 0,
                }}
            >
                {isDelete && !comments ? (
                    <>
                        <Typography
                            sx={{
                                fontWeight: 'bold',
                                color: 'interfaceColor.interface600',
                                fontSize: '16px',
                                marginBottom: '16px',
                            }}
                        >
                            {translate('requestModal.deleteQuestion')}
                        </Typography>

                        {actionButtons}
                    </>
                ) : (
                    <Form onSubmit={handleSubmit(submitFn)} id="template-selector-form">
                        <Box>
                            <Typography
                                sx={{
                                    fontWeight: 'bold',
                                    color: 'grey[600]',
                                    fontSize: '16px',
                                    marginBottom: '16px',
                                }}
                            >
                                {questionLabel || translate('requestModal.cancelQuestion')}
                            </Typography>
                            <GenericTextField
                                name={'comments'}
                                control={control}
                                error={Boolean(errors.comments)}
                                valueController={getValues('comments')}
                                label={translate('requestModal.comments')}
                                helperText={translate('requestModal.textHelperComments')}
                                rows={5}
                                required
                            />
                        </Box>

                        {actionButtons}
                    </Form>
                )}
            </Box>
        )
    }

    return (
        <ModalWrapper
            title={getModalTitle()}
            openModal={openModal}
            closeModal={handleCloseAndReset}
            sxProps={{ backgroundColor: 'rgba(0, 0, 0, 0.5)', minHeight: '200px' }}
            sxPropsTitle={{
                fontSize: '24px',
                marginRight: 'auto',
                paddingLeft: '20px',
                maxWidth: '55vw',
            }}
            content={renderContent()}
        />
    )
}
