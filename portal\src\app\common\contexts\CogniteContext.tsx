import { CogniteClient } from '@cognite/sdk'
import { createContext } from 'react'
import { FdmClient } from '../clients/fdm-client'

interface CogniteContextState {
    cogniteClient: CogniteClient
    fdmClient: FdmClient
}

export const CogniteContext = createContext<CogniteContextState>({
    cogniteClient: new CogniteClient({
        appId: '',
        project: '',
        getToken: () => Promise.resolve(''),
    }),
    fdmClient: new FdmClient({
        cogniteProject: '',
        cogniteBaseUrl: '',
        cogniteFdmInstancesSpace: '',
        cogniteFdmModelSpace: '',
        cogniteFdmModel: '',
        cogniteApiVersion: '',
        getToken: () => Promise.resolve(''),
    }),
})
