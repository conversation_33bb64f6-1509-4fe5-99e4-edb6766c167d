import json
import logging

import azure.functions as func

from clients.core.constants import APPLICATION_JSON
from clients.dashboard_kpis.requests import Get<PERSON><PERSON><PERSON>Request, GetDashboardKpisRequest
from clients.kpis.requests import GetKpisRequest
from infra.action_item_client_factory import ActionItemClientFactory
from services.aggregate_service import AggregateService

bp = func.Blueprint()
logging.basicConfig(format="%(message)s", level=logging.INFO)


@bp.function_name(name="GetHomeKpis")
@bp.route("get-home-kpis", methods=["get"], auth_level=func.AuthLevel.ANONYMOUS)
async def get_home_kpi(req: func.HttpRequest) -> func.HttpResponse:
    """
    Retrieve home KPIs based on the given aggregate request parameters.

    Args:
        req (func.HttpRequest): The HTTP request object containing the aggregate request.

    Returns:
        func.HttpResponse: The HTTP response containing the home KPIs data or an error message.

    """
    try:
        auth_header = req.headers.get("Authorization")

        if auth_header and auth_header.startswith("Bearer "):
            token_request = auth_header[len("Bearer ") :]
        else:
            token_request = auth_header

        aggregate_request_param = req.params.get("aggregateRequest", "{}")
        aggregate_request = json.loads(aggregate_request_param)

        logging.info("Function GetHomeKpis started")

        request = GetKpisRequest.model_validate(aggregate_request)

        service = AggregateService(
            ActionItemClientFactory.retriever(override_token=token_request),
        )

        items = await service.get_home_kpis(request)

        logging.info("Finishing execution - GetHomeKpis")

        response_body = json.dumps(items.model_dump(mode="json", by_alias=True))

        return func.HttpResponse(
            response_body,
            mimetype=APPLICATION_JSON,
            status_code=200,
        )
    except Exception as e:
        logging.error(f"Exception found: {e}", exc_info=True)
        return func.HttpResponse(f"Error: {e}", status_code=500)


@bp.function_name(name="GetSiteTabKpis")
@bp.route("get-site-tab-kpis", methods=["get"], auth_level=func.AuthLevel.ANONYMOUS)
async def get_site_tab_kpis(req: func.HttpRequest) -> func.HttpResponse:
    """
    Retrieve site tab KPIs based on the given aggregate request parameters.

    Args:
        req (func.HttpRequest): The HTTP request object containing the aggregate request.

    Returns:
        func.HttpResponse: The HTTP response containing the site tab KPIs data or an error message.

    """
    try:
        auth_header = req.headers.get("Authorization")

        if auth_header and auth_header.startswith("Bearer "):
            token_request = auth_header[len("Bearer ") :]
        else:
            token_request = auth_header

        aggregate_request_param = req.params.get("aggregateRequest", "{}")
        aggregate_request = json.loads(aggregate_request_param)

        request = GetDashboardKpisRequest.model_validate(aggregate_request)

        logging.info("Function GetSiteTabKpis started")

        service = AggregateService(
            ActionItemClientFactory.retriever(override_token=token_request),
        )

        items = await service.get_kpis(request)

        logging.info("Finishing execution - GetSiteTabKpis")

        response_body = json.dumps(items.model_dump(mode="json", by_alias=True))

        return func.HttpResponse(
            response_body,
            mimetype=APPLICATION_JSON,
            status_code=200,
        )
    except Exception as e:
        logging.error(f"Exception found: {e}", exc_info=True)
        return func.HttpResponse(f"Error: {e}", status_code=500)


@bp.function_name(name="GetSupervisorTabKpis")
@bp.route(
    "get-supervisor-tab-kpis",
    methods=["get"],
    auth_level=func.AuthLevel.ANONYMOUS,
)
async def get_supervisor_tab_kpis(req: func.HttpRequest) -> func.HttpResponse:
    """
    Retrieve supervisor tab KPIs based on the given aggregate request parameters.

    Args:
        req (func.HttpRequest): The HTTP request object containing the aggregate request.

    Returns:
        func.HttpResponse: The HTTP response containing the supervisor tab KPIs data or an error message.

    """
    try:
        auth_header = req.headers.get("Authorization")

        if auth_header and auth_header.startswith("Bearer "):
            token_request = auth_header[len("Bearer ") :]
        else:
            token_request = auth_header

        aggregate_request_param = req.params.get("aggregateRequest", "{}")
        aggregate_request = json.loads(aggregate_request_param)

        request = GetDashboardKpisRequest.model_validate(aggregate_request)

        logging.info("Function GetSupervisorTabKpis started")

        service = AggregateService(
            ActionItemClientFactory.retriever(override_token=token_request),
        )

        items = await service.get_kpis(request)

        logging.info("Finishing execution - GetSupervisorTabKpis")

        response_body = json.dumps(items.model_dump(mode="json", by_alias=True))

        return func.HttpResponse(
            response_body,
            mimetype=APPLICATION_JSON,
            status_code=200,
        )
    except Exception as e:
        logging.error(f"Exception found: {e}", exc_info=True)
        return func.HttpResponse(f"Error: {e}", status_code=500)


@bp.function_name(name="GetActionsGroupedBy")
@bp.route(
    "get-actions-grouped-by",
    methods=["get"],
    auth_level=func.AuthLevel.ANONYMOUS,
)
async def get_actions_grouped_by(req: func.HttpRequest) -> func.HttpResponse:
    """
    Retrieve actions grouped by the specified parameters based on the given aggregate request.

    Args:
        req (func.HttpRequest): The HTTP request object containing the aggregate request.

    Returns:
        func.HttpResponse: The HTTP response containing the grouped actions data or an error message.

    """
    try:
        auth_header = req.headers.get("Authorization")

        if auth_header and auth_header.startswith("Bearer "):
            token_request = auth_header[len("Bearer ") :]
        else:
            token_request = auth_header

        aggregate_request_param = req.params.get("aggregateRequest", "{}")
        aggregate_request = json.loads(aggregate_request_param)

        request = GetChartsRequest.model_validate(aggregate_request)

        logging.info("Function GetActionsGroupedBy started")

        service = AggregateService(
            ActionItemClientFactory.retriever(override_token=token_request),
        )

        items = await service.get_actions_grouped_by(request)

        logging.info("Finishing execution - GetActionsGroupedBy")

        response_body = json.dumps({"items": items})

        return func.HttpResponse(
            response_body,
            mimetype=APPLICATION_JSON,
            status_code=200,
        )
    except Exception as e:
        logging.error(f"Exception found: {e}", exc_info=True)
        return func.HttpResponse(f"Error: {e}", status_code=500)
