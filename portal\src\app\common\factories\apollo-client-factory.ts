import { environment } from '@/app/common/configurations/environment'
import {
    ApolloClient,
    InMemoryCache,
    NormalizedCacheObject,
    ServerError,
    createHttpLink,
    fromPromise,
} from '@apollo/client'
import { setContext } from '@apollo/client/link/context'
import { onError } from '@apollo/client/link/error'

export const createApolloClient = (
    getAuthToken: () => Promise<string>,
    useCase?: string
): ApolloClient<NormalizedCacheObject> => {
    const customGraphApis = new Map<string, string>([
        ['moc', environment.mocGraphUrl],
        ['icap', environment.icapGraphUrl],
        ['gapAssessment', environment.gapAssessmentGraphUrl],
        ['ofwa', environment.ofwaGraphUrl],
    ])

    const httpLink = createHttpLink({
        uri: useCase && customGraphApis.get(useCase) ? customGraphApis.get(useCase) : environment.graphqlUri,
    })

    const authLink = setContext(async (_, { headers }) => {
        const accessToken = await getAuthToken()
        const cogniteAppHeader = environment.cogniteXCdpApp ?? ''
        const cogniteAppId = environment.cogniteAppId
        return {
            headers: {
                ...headers,
                authorization: `Bearer ${accessToken}`,
                [cogniteAppHeader]: cogniteAppId,
            },
        }
    })

    const errorLink = onError(({ networkError, operation, forward }) => {
        const serverError = networkError as ServerError
        if (serverError?.statusCode === 401) {
            return fromPromise(
                getAuthToken().catch((_error) => {
                    return
                })
            )
                .filter((value) => Boolean(value))
                .flatMap((accessToken) => {
                    const oldHeaders = operation.getContext().headers
                    const cogniteAppHeader = environment.cogniteXCdpApp ?? ''
                    const cogniteAppId = environment.cogniteAppId
                    operation.setContext({
                        headers: {
                            ...oldHeaders,
                            authorization: `Bearer ${accessToken}`,
                            [cogniteAppHeader]: cogniteAppId,
                        },
                    })
                    return forward(operation)
                })
        }
    })

    return new ApolloClient({
        link: authLink.concat(errorLink).concat(httpLink),
        cache: new InMemoryCache(),
        ssrMode: typeof window === 'undefined',
    })
}
