'use client'
import './common/utils/polyfills'
import { useContext, useEffect, useState } from 'react'
import React from 'react'
import AuthGuardWrapper from './common/wrapper/AuthGuardWrapper'
import { APP_NAME } from './common/utils'
import dynamic from 'next/dynamic'
import { Box } from '@mui/material'
import {
    TranslationContext,
    TranslationContextState,
    UserManagementContext,
    UserManagementContextState,
} from '@celanese/celanese-ui'
import { HomeComponent } from './components/HomeComponent'
import { ClnPage } from '@celanese/ui-lib'
import { translate } from './common/utils/generate-translate'
import { UserRolesPermission } from '@celanese/celanese-sdk'
import { UserExternalContext, UserExternalContextState } from './common/contexts/UserExternalContext'
import PageHeader from './components/PageHeader'
const ClnJoAIMenuOptions = dynamic(() => import('@celanese/celanese-ui').then((mod) => mod.ClnJoAIMenuOptions), {
    ssr: false,
})
export default function Home() {
    const { locale } = useContext<TranslationContextState>(TranslationContext)

    const { userInfo } = useContext<UserManagementContextState>(UserManagementContext)
    const { userExternalInfo } = useContext<UserExternalContextState>(UserExternalContext)
    const [activeUser, setActiveUser] = useState<UserRolesPermission>()
    const [openModalIA, setOpenModalIA] = useState<boolean>(false)

    useEffect(() => {
        localStorage.removeItem('dashboardTab')
        localStorage.removeItem('isEditForm')
    }, [])

    useEffect(() => {
        if (
            userExternalInfo.externalId &&
            userExternalInfo.externalId !== '' &&
            (activeUser?.externalId !== userExternalInfo.externalId ||
                activeUser?.applications !== userInfo.applications)
        ) {
            setActiveUser({
                ...userInfo,
                externalId: userExternalInfo.externalId,
                roles: userExternalInfo.roles,
                teams: userExternalInfo.teams,
            })
        }
    }, [userInfo, userExternalInfo])

    useEffect(() => {
        document.title = `${translate('pages.home.title')} | ${APP_NAME}`
    }, [locale])

    return (
        <AuthGuardWrapper componentName={Home.name}>
            <ClnPage id="cln-page-tile" sx={{ gap: '0.5px' }}>
                {activeUser && (
                    <>
                        <PageHeader title={translate('home.title')} activeUser={activeUser} />
                        <HomeComponent activeUser={activeUser} />
                        <Box
                            onClick={() => {
                                if (openModalIA) setOpenModalIA(false)
                                else setOpenModalIA(true)
                            }}
                        >
                            <ClnJoAIMenuOptions />
                        </Box>
                    </>
                )}
            </ClnPage>
        </AuthGuardWrapper>
    )
}
