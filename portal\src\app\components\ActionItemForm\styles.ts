import { styled, SxProps, Theme } from '@mui/material'
import { CSSObject } from 'styled-components'

export const itemsForm: CSSObject = {
    display: 'flex',
    flexDirection: 'inherit',
    marginBottom: '1.5rem',
    '& .MuiTextField-root': { width: '100%' },
    '& .MuiFormControl-root': { minWidth: '100%' },
}

export const itemsDateColumnForm: CSSObject = {
    display: 'flex',
    flexDirection: 'column',
    width: '100%',
}

export const toggleStyles: SxProps<Theme> = {
    '& .MuiSwitch-track': {
        backgroundColor: (theme: Theme) => theme.palette.grey[400],
    },
    '& .MuiSwitch-switchBase': {
        color: (theme: Theme) => theme.palette.grey[400],
    },
    '& .MuiSwitch-switchBase.Mui-checked': {
        color: (theme: Theme) => theme.palette.primary.main,
    },
    '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': {
        backgroundColor: (theme: Theme) => `${theme.palette.primary.main} !important`,
    },
}

export const weekdayPicker = {
    display: 'flex',
    width: '100%',
    justifyContent: 'space-between',
    padding: '1rem 2rem',
}

export const WeekdayCircle = styled('div')`
    ${({ theme }) => `
        width: 30px;
        height: 30px;
        border-radius: 50%;
        background-color: ${theme.palette.grey[200]};
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 10px;
        cursor: pointer;

        &.selected {
            background-color: ${theme.palette.primary.main};
            color: ${theme.palette.background.paper};
        }
    `}
`

export const monthPicker = {
    marginTop: 2,
    display: 'flex',
    flexWrap: 'wrap',
    padding: '0px 1rem',
    justifyContent: 'space-between',
}

export const MonthCircle = styled('div')`
    ${({ theme }) => `
        width: 30px;
        height: 30px;
        border-radius: 10%;
        background-color: ${theme.palette.background.default};
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 5px;
        cursor: pointer;
        flex-basis: calc(25% - 10px);

        &.selected {
            background-color: ${theme.palette.primary.main};
            color: ${theme.palette.background.paper};
        }
    `}
`
