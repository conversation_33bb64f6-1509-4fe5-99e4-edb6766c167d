import { MDREquipmentRequest } from '@/app/common/models/integration/master-data-request/mdr-equipment-request'
import { ModalData, ModalDataSection } from './modalData'
import { SourceEvent, SourceEventResponse } from '@/app/common/models/source-event'
import dayjs, { Dayjs } from 'dayjs'
import { ICAPMOCReport } from '@/app/common/models/integration/icap/icap-moc-report'
import { GapAssessmentResponse } from '@/app/common/models/integration/celia-gap-assessment/gap-assessment-execution-data'
import { OFWAEvent } from '@/app/common/models/integration/ofwa/ofwa-sol-event'
import { ICAPMOOCReport } from '@/app/common/models/integration/icap/icap-mooc-report'
import {
    ActionSourceTypeExternalIdEnum,
    ActionSourceTypeExternalIdEnumType,
} from '@/app/common/enums/ActionSourceTypeEnum'
import { translate } from '@/app/common/utils/generate-translate'
import { ExternalSourceDetails } from '@/app/common/models/external-source-details'

type Field = {
    key: string
    label: string
    getValue: (data: ExternalSourceDetails) => string
}

type RawDataField = {
    label: string
    value: string
}

const formatGeneralFieldValue = (
    value: string | number | string[] | null | undefined,
    fallback: string = 'N/A'
): string => {
    if (value === null) return fallback

    if (Array.isArray(value)) {
        return value.length === 0 ? fallback : value.join('; ')
    }

    return value ? String(value) : fallback
}

const formatBooleanFieldValue = (value: boolean | undefined | null): string => {
    if (value) return translate('common.yes')
    return translate('common.no')
}

const formatDateFieldValue = (value: Date | Dayjs | string | null | undefined, fallback = 'N/A'): string => {
    if (value == null) return fallback

    const parsed =
        value instanceof Date || dayjs.isDayjs(value)
            ? dayjs(value)
            : typeof value === 'string'
              ? dayjs(value, 'YYYY-MM-DD', true)
              : null

    return parsed && parsed.isValid() ? parsed.format('MM/DD/YYYY') : fallback
}

export const RCA_SOURCE_TYPES = new Set<string>([
    ActionSourceTypeExternalIdEnum.RCAEvnInvRCACEvent,
    ActionSourceTypeExternalIdEnum.RCAEvnInvRCAABEvent,
    ActionSourceTypeExternalIdEnum.RCAQrEvtyCCI,
    ActionSourceTypeExternalIdEnum.RCAQrEvtyRAR,
    ActionSourceTypeExternalIdEnum.RCAQrEvtyQAR,
    ActionSourceTypeExternalIdEnum.RCAWiEvtyADM,
    ActionSourceTypeExternalIdEnum.RCAWiEvtyENG,
    ActionSourceTypeExternalIdEnum.RCAWiEvtyMNTC,
    ActionSourceTypeExternalIdEnum.RCAWiEvtyQA,
    ActionSourceTypeExternalIdEnum.RCAWiSapQ1,
    ActionSourceTypeExternalIdEnum.RCAWiSapQ3,
])

const rcaFieldMappings: Field[] = [
    {
        key: 'eventType',
        label: 'actionItemModal.modalRcaEventRawData.eventType',
        getValue: (d) => formatGeneralFieldValue(d.general?.eventType),
    },
    {
        key: 'eventTitle',
        label: 'actionItemModal.modalRcaEventRawData.eventTitle',
        getValue: (d) => formatGeneralFieldValue(d.general?.eventTitle),
    },
    {
        key: 'reportingLocation',
        label: 'actionItemModal.modalRcaEventRawData.reportingLocation',
        getValue: (d) => formatGeneralFieldValue(d.general?.reportingLocation),
    },
    {
        key: 'eventDate',
        label: 'actionItemModal.modalRcaEventRawData.eventDate',
        getValue: (d) => formatDateFieldValue(d.specific?.eventDate),
    },
    {
        key: 'reportDate',
        label: 'actionItemModal.modalRcaEventRawData.reportDate',
        getValue: (d) => formatDateFieldValue(d.specific?.reportDate),
    },
    {
        key: 'immediateActionTaken',
        label: 'actionItemModal.modalRcaEventRawData.immediateActionTaken',
        getValue: (d) => formatGeneralFieldValue(d.specific?.immediateActionTaken),
    },
    {
        key: 'problemDescription',
        label: 'actionItemModal.modalRcaEventRawData.problemDescription',
        getValue: (d) => formatGeneralFieldValue(d.specific?.problemDescription),
    },
    {
        key: 'batchNumber',
        label: 'actionItemModal.modalRcaEventRawData.batchNumber',
        getValue: (d) => formatGeneralFieldValue(d.specific?.batchNumber),
    },
    {
        key: 'unit',
        label: 'actionItemModal.commonValues.unit',
        getValue: (d) => formatGeneralFieldValue(d.specific?.reportingUnit),
    },
    {
        key: 'initiator',
        label: 'actionItemModal.modalRcaEventRawData.initiator',
        getValue: (d) => formatGeneralFieldValue(d.specific?.initiator),
    },
    {
        key: 'equipment',
        label: 'actionItemModal.commonValues.equipment',
        getValue: (d) => formatGeneralFieldValue(d.specific?.equipment),
    },
    {
        key: 'reportTitle',
        label: 'actionItemModal.modalRcaEventRawData.reportTitle',
        getValue: (d) => formatGeneralFieldValue(d.specific?.reportTitle),
    },
    {
        key: 'proceduresInvolved',
        label: 'actionItemModal.modalRcaEventRawData.proceduresInvolved',
        getValue: (d) => formatGeneralFieldValue(d.specific?.proceduresInvolved),
    },
    {
        key: 'problemCategory',
        label: 'actionItemModal.modalRcaEventRawData.problemCategory',
        getValue: (d) => formatGeneralFieldValue(d.specific?.problemCategory),
    },
    {
        key: 'eventId',
        label: 'actionItemModal.modalRcaEventRawData.eventId',
        getValue: (d) => formatGeneralFieldValue(d.specific?.eventId),
    },
    {
        key: 'notificationDate',
        label: 'actionItemModal.modalRcaEventRawData.notificationDate',
        getValue: (d) => formatDateFieldValue(d.specific?.notificationDate),
    },
    {
        key: 'materialDescription',
        label: 'actionItemModal.modalRcaEventRawData.materialDescription',
        getValue: (d) => formatGeneralFieldValue(d.specific?.material),
    },
    {
        key: 'returnedQuantity',
        label: 'actionItemModal.modalRcaEventRawData.returnedQuantity',
        getValue: (d) => formatGeneralFieldValue(d.specific?.returnedQuantity),
    },
    {
        key: 'defectDescription',
        label: 'actionItemModal.modalRcaEventRawData.defectDescription',
        getValue: (d) => formatGeneralFieldValue(d.specific?.defectDescription),
    },
    {
        key: 'subjectCode',
        label: 'actionItemModal.modalRcaEventRawData.subjectCode',
        getValue: (d) => formatGeneralFieldValue(d.specific?.subjectCode),
    },
    {
        key: 'notificationDescription',
        label: 'actionItemModal.modalRcaEventRawData.notificationDescription',
        getValue: (d) => formatGeneralFieldValue(d.specific?.notificationDescription),
    },
    {
        key: 'severity',
        label: 'actionItemModal.modalRcaEventRawData.severity',
        getValue: (d) => formatGeneralFieldValue(d.specific?.severity),
    },
    {
        key: 'systemStatus',
        label: 'actionItemModal.modalRcaEventRawData.systemStatus',
        getValue: (d) => formatGeneralFieldValue(d.specific?.systemStatus),
    },

    {
        key: 'functionalLocation',
        label: 'actionItemModal.commonValues.functionalLocation',
        getValue: (d) => formatGeneralFieldValue(d.specific?.functionalLocation),
    },
    {
        key: 'notificationNumber',
        label: 'actionItemModal.modalRcaEventRawData.notificationNumber',
        getValue: (d) => formatGeneralFieldValue(d.specific?.notificationNumber),
    },
    {
        key: 'businessLine',
        label: 'actionItemModal.commonValues.businessLine',
        getValue: (d) => formatGeneralFieldValue(d.specific?.businessLine),
    },
]

function getRcaFieldsByType(type: ActionSourceTypeExternalIdEnumType): string[] {
    switch (type) {
        case ActionSourceTypeExternalIdEnum.RCAQrEvtyQAR:
            return [
                'eventDate',
                'problemCategory',
                'initiator',
                'reportTitle',
                'reportDate',
                'batchNumber',
                'equipment',
                'proceduresInvolved',
                'eventType',
                'unit',
                'immediateActionTaken',
            ]
        case ActionSourceTypeExternalIdEnum.RCAQrEvtyRAR:
            return [
                'eventDate',
                'eventType',
                'unit',
                'reportTitle',
                'reportDate',
                'problemCategory',
                'initiator',
                'equipment',
                'immediateActionTaken',
            ]
        case ActionSourceTypeExternalIdEnum.RCAQrEvtyCCI:
            return [
                'eventDate',
                'eventId',
                'problemCategory',
                'reportDate',
                'batchNumber',
                'reportTitle',
                'eventType',
                'unit',
                'initiator',
                'problemDescription',
            ]
        case ActionSourceTypeExternalIdEnum.RCAWiSapQ1:
        case ActionSourceTypeExternalIdEnum.RCAWiSapQ3:
            return [
                'eventTitle',
                'eventType',
                'notificationNumber',
                'notificationDescription',
                'notificationDate',
                'batchNumber',
                'defectDescription',
                'severity',
                'materialDescription',
                'returnedQuantity',
                'subjectCode',
                'systemStatus',
            ]
        case ActionSourceTypeExternalIdEnum.RCAEvnInvRCACEvent:
        case ActionSourceTypeExternalIdEnum.RCAEvnInvRCAABEvent:
            return [
                'eventTitle',
                'eventType',
                'unit',
                'reportingLocation',
                'functionalLocation',
                'equipment',
                'businessLine',
            ]
        case ActionSourceTypeExternalIdEnum.RCAWiEvtyADM:
        case ActionSourceTypeExternalIdEnum.RCAWiEvtyENG:
        case ActionSourceTypeExternalIdEnum.RCAWiEvtyMNTC:
        case ActionSourceTypeExternalIdEnum.RCAWiEvtyQA:
            return [
                'eventTitle',
                'eventType',
                'unit',
                'functionalLocation',
                'equipment',
                'reportingLocation',
                'problemDescription',
            ]
        default:
            return []
    }
}

function buildRcaFieldsBySource(
    type: ActionSourceTypeExternalIdEnumType,
    details: ExternalSourceDetails
): RawDataField[] {
    const keysToShow = getRcaFieldsByType(type)

    return keysToShow.map((key) => {
        const field = rcaFieldMappings.find((f) => f.key === key)
        if (!field) {
            return { label: key, value: '' }
        }
        return {
            label: translate(field.label),
            value: field.getValue(details),
        }
    })
}

function splitIntoColumns<T>(array: T[], columnsCount: number): T[][] {
    const result: T[][] = Array.from({ length: columnsCount }, () => [])
    array.forEach((item, index) => {
        result[index % columnsCount].push(item)
    })
    return result
}

export const buildModalTitle = (
    MDREquipmentRequestData: MDREquipmentRequest[],
    MDRPdpmRequestData: MDREquipmentRequest[],
    MDRMdmRequestData: MDREquipmentRequest[],
    RCAEventData: ExternalSourceDetails | undefined,
    ICAPMOCReportData: ICAPMOCReport[],
    gapAssessmentExecutionData: GapAssessmentResponse[],
    OFWAEventData: OFWAEvent[],
    ICAPMOOCReportData: ICAPMOOCReport[],
    sourceEventData: SourceEventResponse | undefined,
    sourceTypeId: string | undefined
): string => {
    if (sourceTypeId && RCA_SOURCE_TYPES.has(sourceTypeId) && RCAEventData) {
        return translate('actionItemModal.title.eventAnalysis')
    }

    const titleMap: Record<string, { condition: boolean; translationKey: string }> = {
        [ActionSourceTypeExternalIdEnum.MDREquipmentRequest]: {
            condition: MDREquipmentRequestData.length > 0,
            translationKey: 'actionItemModal.title.MDREquipmentRequest',
        },
        [ActionSourceTypeExternalIdEnum.MDRPdpmRequest]: {
            condition: MDRPdpmRequestData.length > 0,
            translationKey: 'actionItemModal.title.MDRPdpmRequest',
        },
        [ActionSourceTypeExternalIdEnum.MDRMdmRequest]: {
            condition: MDRMdmRequestData.length > 0,
            translationKey: 'actionItemModal.title.MDRMdmRequest',
        },
        [ActionSourceTypeExternalIdEnum.AIMEvent]: {
            condition: Boolean(sourceEventData),
            translationKey: 'actionItemModal.title.AIMEvent',
        },
        [ActionSourceTypeExternalIdEnum.ICAPMOCReport]: {
            condition: ICAPMOCReportData.length > 0,
            translationKey: 'actionItemModal.title.ICAPMOCReport',
        },
        [ActionSourceTypeExternalIdEnum.ICAPMOOCReport]: {
            condition: ICAPMOOCReportData.length > 0,
            translationKey: 'actionItemModal.title.ICAPMOOCReport',
        },
        [ActionSourceTypeExternalIdEnum.OFWAEvent]: {
            condition: OFWAEventData.length > 0,
            translationKey: 'actionItemModal.title.OFWAEvent',
        },
        [ActionSourceTypeExternalIdEnum.GapAssessmentExecutionData]: {
            condition: gapAssessmentExecutionData.length > 0,
            translationKey: 'actionItemModal.title.GapAssessmentExecutionData',
        },
    }

    const entry = sourceTypeId && titleMap[sourceTypeId]
    if (entry && entry.condition) {
        return translate(entry.translationKey)
    }

    return ''
}

export const buildModalBody = (
    MDREquipmentRequestData: MDREquipmentRequest[],
    MDRPdpmRequestData: MDREquipmentRequest[],
    MDRMdmRequestData: MDREquipmentRequest[],
    RCAEventData: ExternalSourceDetails | undefined,
    ICAPMOCReportData: ICAPMOCReport[],
    gapAssessmentExecutionData: GapAssessmentResponse[],
    OFWAEventData: OFWAEvent[],
    ICAPMOOCReportData: ICAPMOOCReport[],
    sourceEventData: SourceEventResponse | undefined,
    sourceTypeId: string | undefined
): ModalData => {
    let modalData: ModalData = { sections: [] }
    const sections: ModalDataSection[] = []

    if (sourceTypeId === ActionSourceTypeExternalIdEnum.MDREquipmentRequest && MDREquipmentRequestData.length > 0) {
        const data = MDREquipmentRequestData[0]

        sections.push({
            subsections: [
                {
                    title: translate('actionItemModal.commonValues.requestInfo'),
                    columns: [
                        {
                            fields: [
                                {
                                    label: translate('actionItemModal.commonValues.requestor'),
                                    value: data.header?.requestor
                                        ? `${formatGeneralFieldValue(
                                              data.header.requestor.lastName,
                                              ''
                                          )}, ${formatGeneralFieldValue(data.header.requestor.firstName, '')}`
                                        : 'N/A',
                                },
                                {
                                    label: translate('actionItemModal.commonValues.unit'),
                                    value: data.header?.plantArea
                                        ? `${formatGeneralFieldValue(
                                              data.header.plantArea.description,
                                              ''
                                          )} - ${formatGeneralFieldValue(data.header.plantArea.name?.slice(3), '')}`
                                        : 'N/A',
                                },
                                {
                                    label: translate('actionItemModal.modalEquipmentRequestRawData.projectNumber'),
                                    value: formatGeneralFieldValue(data.header?.projectNumber),
                                },
                            ],
                        },
                        {
                            fields: [
                                {
                                    label: translate('actionItemModal.commonValues.fcrOriginator'),
                                    value: formatGeneralFieldValue(data.header?.requestor?.email),
                                },
                                {
                                    label: translate('actionItemModal.modalEquipmentRequestRawData.installDate'),
                                    value: formatDateFieldValue(data.header?.installDate),
                                },
                                {
                                    label: translate('actionItemModal.commonValues.status'),
                                    value: formatGeneralFieldValue(data.header?.approvalWorkflow?.status?.name),
                                },
                            ],
                        },
                    ],
                },
            ],
        })

        sections.push({
            subsections: [
                {
                    subtitle: translate('actionItemModal.commonValues.detailsOfItemRequest'),
                    externalId: formatGeneralFieldValue(data?.externalId),
                    columns: [
                        {
                            title: translate('actionItemModal.commonValues.basicInfo'),
                            fields: [
                                {
                                    label: translate('actionItemModal.commonValues.sapNumber'),
                                    value: formatGeneralFieldValue(data.basicInfo?.sapNumberEquipment),
                                },
                                {
                                    label: translate('actionItemModal.commonValues.keynoun'),
                                    value: formatGeneralFieldValue(data.basicInfo?.keyNoun?.name),
                                },
                                {
                                    label: translate('actionItemModal.commonValues.newKeyNounNeeded'),
                                    value: formatBooleanFieldValue(data.basicInfo?.newKeyNounNeeded),
                                },
                                {
                                    label: translate('actionItemModal.commonValues.sapSuperiorEquipment'),
                                    value: formatGeneralFieldValue(data.basicInfo?.sapOfSuperiorEquipment),
                                },
                                {
                                    label: translate('actionItemModal.commonValues.description'),
                                    value: formatGeneralFieldValue(data.basicInfo?.description),
                                },
                                {
                                    label: translate('actionItemModal.commonValues.class'),
                                    value: formatGeneralFieldValue(data.basicInfo?.class?.name),
                                },
                                {
                                    label: translate('actionItemModal.commonValues.additionalInfo'),
                                    value: formatGeneralFieldValue(data.basicInfo?.additionalInto),
                                },
                                {
                                    label: translate('actionItemModal.commonValues.equipmentCriticality'),
                                    value: formatGeneralFieldValue(data.basicInfo?.equipmanetCriticality?.name),
                                },
                                {
                                    label: translate('actionItemModal.commonValues.typeOfCriticality'),
                                    value: formatGeneralFieldValue(data.basicInfo?.typeOfCriticality?.name),
                                },
                                {
                                    label: translate('actionItemModal.commonValues.replacingExistingEquipment'),
                                    value: formatBooleanFieldValue(data.basicInfo?.replacingExistingEquipment),
                                },
                                {
                                    label: translate('actionItemModal.commonValues.sparePartsRequired'),
                                    value: formatBooleanFieldValue(data.basicInfo?.sparePartsRequired),
                                },
                                {
                                    label: translate('actionItemModal.commonValues.permit'),
                                    value: formatGeneralFieldValue(data.basicInfo?.permit),
                                },
                            ],
                        },
                        {
                            title: translate('actionItemModal.commonValues.generalInfo'),
                            fields: [
                                {
                                    label: translate('actionItemModal.commonValues.startupDate'),
                                    value: formatDateFieldValue(data.generalInfo?.startupDate),
                                },
                                {
                                    label: translate('actionItemModal.commonValues.newManufacturerNeeded'),
                                    value: formatBooleanFieldValue(data.generalInfo?.newManufacturerNeeded),
                                },
                            ],
                        },
                        {
                            title: translate('actionItemModal.commonValues.locationData'),
                            fields: [
                                {
                                    label: translate('actionItemModal.commonValues.locationBuilding'),
                                    value: formatGeneralFieldValue(data.locationData?.reportingLocation?.name),
                                },
                                {
                                    label: translate('actionItemModal.commonValues.functionalLocation'),
                                    value: formatGeneralFieldValue(data.locationData?.functionalLocation?.name),
                                },
                                {
                                    label: translate('actionItemModal.commonValues.newFunctionalLocationNeeded'),
                                    value: formatBooleanFieldValue(data.locationData?.newFunctionalLocationNeeded),
                                },
                                {
                                    label: translate('actionItemModal.commonValues.suggestedTechnicalId'),
                                    value: formatGeneralFieldValue(data.locationData?.suggestedTechnicalId),
                                },
                            ],
                        },
                    ],
                },
            ],
        })

        modalData = { sections }
    } else if (sourceTypeId == ActionSourceTypeExternalIdEnum.MDRPdpmRequest && MDRPdpmRequestData.length > 0) {
        const data = MDRPdpmRequestData[0]

        sections.push({
            subsections: [
                {
                    title: translate('actionItemModal.commonValues.requestInfo'),
                    columns: [
                        {
                            fields: [
                                {
                                    label: translate('actionItemModal.commonValues.requestor'),
                                    value: data.header?.requestor
                                        ? `${formatGeneralFieldValue(
                                              data.header.requestor.lastName,
                                              ''
                                          )}, ${formatGeneralFieldValue(data.header.requestor.firstName, '')}`
                                        : 'N/A',
                                },
                                {
                                    label: translate('actionItemModal.commonValues.unit'),
                                    value: data.header?.plantArea
                                        ? `${formatGeneralFieldValue(
                                              data.header.plantArea.description,
                                              ''
                                          )} - ${formatGeneralFieldValue(data.header.plantArea.name?.slice(3), '')}`
                                        : 'N/A',
                                },
                                {
                                    label: translate('actionItemModal.modalPdpmRequestRawData.reliabilityLead'),
                                    value: data.header?.miqaReliabilityLead
                                        ? `${formatGeneralFieldValue(
                                              data.header.miqaReliabilityLead.lastName,
                                              ''
                                          )}, ${formatGeneralFieldValue(data.header.miqaReliabilityLead.firstName, '')}`
                                        : 'N/A',
                                },
                            ],
                        },
                        {
                            fields: [
                                {
                                    label: translate('actionItemModal.commonValues.fcrOriginator'),
                                    value: formatGeneralFieldValue(data.header?.requestor?.email),
                                },
                                {
                                    label: translate('actionItemModal.commonValues.requestDate'),
                                    value: formatDateFieldValue(data.header?.requestDate),
                                },
                                {
                                    label: translate('actionItemModal.commonValues.status'),
                                    value: formatGeneralFieldValue(data.header?.approvalWorkflow?.status?.name),
                                },
                            ],
                        },
                    ],
                },
            ],
        })

        sections.push({
            subsections: [
                {
                    subtitle: translate('actionItemModal.commonValues.detailsOfItemRequest'),
                    externalId: formatGeneralFieldValue(data?.externalId),
                    columns: [
                        {
                            title: translate('actionItemModal.commonValues.basicInfo'),
                            fields: [
                                {
                                    label: translate('actionItemModal.modalPdpmRequestRawData.type'),
                                    value: formatGeneralFieldValue(data.basicInfo?.pdpmType?.name),
                                },
                                {
                                    label: translate('actionItemModal.modalPdpmRequestRawData.activityReason'),
                                    value: formatGeneralFieldValue(data.basicInfo?.activityReason),
                                },
                                {
                                    label: translate('actionItemModal.modalPdpmRequestRawData.newDueDate'),
                                    value: formatDateFieldValue(data.basicInfo?.newDueDate),
                                },
                                {
                                    label: translate('actionItemModal.modalPdpmRequestRawData.newFrequency'),
                                    value: formatGeneralFieldValue(data.basicInfo?.newFrequency),
                                },
                                {
                                    label: translate('actionItemModal.modalPdpmRequestRawData.leadTime'),
                                    value: formatGeneralFieldValue(data.basicInfo?.leadTime),
                                },
                                {
                                    label: translate('actionItemModal.modalPdpmRequestRawData.sapActivityType'),
                                    value: data.basicInfo?.sapActivityType
                                        ? `${formatGeneralFieldValue(
                                              data.basicInfo.sapActivityType.name,
                                              ''
                                          )} - ${formatGeneralFieldValue(
                                              data.basicInfo.sapActivityType.description,
                                              ''
                                          )}`
                                        : 'N/A',
                                },
                                {
                                    label: translate('actionItemModal.commonValues.description'),
                                    value: formatGeneralFieldValue(data.basicInfo?.activityDescription),
                                },
                                {
                                    label: translate('actionItemModal.modalPdpmRequestRawData.skill'),
                                    value: formatGeneralFieldValue(data.basicInfo?.activitySkill?.name),
                                },
                                {
                                    label: translate('actionItemModal.modalPdpmRequestRawData.equipmentShutdown'),
                                    value: formatGeneralFieldValue(data.basicInfo?.equipShutdown?.name),
                                },
                                {
                                    label: translate('actionItemModal.modalPdpmRequestRawData.criticality'),
                                    value: formatGeneralFieldValue(data.basicInfo?.criticatily?.description),
                                },
                                {
                                    label: translate('actionItemModal.modalPdpmRequestRawData.isUrgent'),
                                    value: formatBooleanFieldValue(data.basicInfo?.isthisUrgent),
                                },
                                {
                                    label: translate('actionItemModal.modalPdpmRequestRawData.mpDescription'),
                                    value: formatGeneralFieldValue(data.basicInfo?.mpDescription),
                                },
                                {
                                    label: translate('actionItemModal.modalPdpmRequestRawData.justification'),
                                    value: formatGeneralFieldValue(data.basicInfo?.justification),
                                },
                                {
                                    label: translate('actionItemModal.modalPdpmRequestRawData.toWhatRegulation'),
                                    value: formatGeneralFieldValue(data.basicInfo?.toWhatRegulation),
                                },
                            ],
                        },
                    ],
                },
            ],
        })

        if (Array.isArray(data?.itemsObjects)) {
            modalData.sections[modalData.sections.length - 1].subsections?.[0].columns.push({
                title: translate('actionItemModal.modalPdpmRequestRawData.itemsAndObjects'),
                fields: data?.itemsObjects
                    ?.map((x) => ({
                        label: translate('actionItemModal.commonValues.equipment'),
                        value: formatGeneralFieldValue(x?.equipment?.name),
                    }))
                    .concat(
                        data?.itemsObjects?.map((x) => ({
                            label: translate('actionItemModal.modalPdpmRequestRawData.technicalId'),
                            value: formatGeneralFieldValue(x?.technicalId),
                        }))
                    )
                    .concat(
                        data?.itemsObjects?.map((x) => ({
                            label: translate('actionItemModal.commonValues.functionalLocation'),
                            value: formatGeneralFieldValue(x?.functionalLocation?.name),
                        }))
                    )
                    .concat(
                        data?.itemsObjects?.map((x) => ({
                            label: translate('actionItemModal.modalPdpmRequestRawData.isNewEquipment'),
                            value: formatBooleanFieldValue(x?.isNewEquipment),
                        }))
                    )
                    .concat(
                        data?.itemsObjects?.map((x) => ({
                            label: translate('actionItemModal.modalPdpmRequestRawData.objectsToAdd'),
                            value: formatGeneralFieldValue(x?.objectsToAdd),
                        }))
                    )
                    .concat(
                        data?.itemsObjects?.map((x) => ({
                            label: translate('actionItemModal.modalPdpmRequestRawData.itemsToAdd'),
                            value: formatGeneralFieldValue(x?.itemsToAdd),
                        }))
                    ),
            })
        }

        modalData = { sections }
    } else if (sourceTypeId === ActionSourceTypeExternalIdEnum.MDRMdmRequest && MDRMdmRequestData.length > 0) {
        const data = MDRMdmRequestData[0]

        sections.push({
            subsections: [
                {
                    title: translate('actionItemModal.commonValues.requestInfo'),
                    columns: [
                        {
                            fields: [
                                {
                                    label: translate('actionItemModal.commonValues.requestor'),
                                    value: data.header?.requestor
                                        ? `${formatGeneralFieldValue(
                                              data.header.requestor.lastName,
                                              ''
                                          )}, ${formatGeneralFieldValue(data.header.requestor.firstName, '')}`
                                        : 'N/A',
                                },
                                {
                                    label: translate('actionItemModal.commonValues.unit'),
                                    value: data.header?.plantArea
                                        ? `${formatGeneralFieldValue(
                                              data.header.plantArea.description,
                                              ''
                                          )} - ${formatGeneralFieldValue(data.header.plantArea.name?.slice(3), '')}`
                                        : 'N/A',
                                },
                                {
                                    label: translate('actionItemModal.modalMdmRequestRawData.safetyPPE'),
                                    value: formatBooleanFieldValue(data.header?.safetyPpe),
                                },
                            ],
                        },
                        {
                            fields: [
                                {
                                    label: translate('actionItemModal.commonValues.fcrOriginator'),
                                    value: formatGeneralFieldValue(data.header?.requestor?.email),
                                },
                                {
                                    label: translate('actionItemModal.commonValues.requestDate'),
                                    value: formatDateFieldValue(data.header?.requestDate),
                                },
                                {
                                    label: translate('actionItemModal.commonValues.status'),
                                    value: formatGeneralFieldValue(data.header?.approvalWorkflow?.status?.name),
                                },
                            ],
                        },
                    ],
                },
            ],
        })

        sections.push({
            subsections: [
                {
                    subtitle: translate('actionItemModal.commonValues.detailsOfItemRequest'),
                    externalId: data?.externalId || 'N/A',
                    columns: [
                        {
                            title: translate('actionItemModal.commonValues.basicInfo'),
                            fields: [
                                {
                                    label: translate('actionItemModal.commonValues.sapNumber'),
                                    value: formatGeneralFieldValue(data.basicInfo?.sapNumberEquipment),
                                },
                                {
                                    label: translate('actionItemModal.commonValues.keynoun'),
                                    value: formatGeneralFieldValue(data.basicInfo?.keyNoun?.name),
                                },
                                {
                                    label: translate('actionItemModal.commonValues.newKeyNounNeeded'),
                                    value: formatBooleanFieldValue(data.basicInfo?.newKeyNounNeeded),
                                },
                                {
                                    label: translate('actionItemModal.commonValues.sapSuperiorEquipment'),
                                    value: formatGeneralFieldValue(data.basicInfo?.sapOfSuperiorEquipment),
                                },
                                {
                                    label: translate('actionItemModal.commonValues.description'),
                                    value: formatGeneralFieldValue(data.basicInfo?.description),
                                },
                                {
                                    label: translate('actionItemModal.commonValues.class'),
                                    value: formatGeneralFieldValue(data.basicInfo?.class?.name),
                                },
                                {
                                    label: translate('actionItemModal.commonValues.additionalInfo'),
                                    value: formatGeneralFieldValue(data.basicInfo?.additionalInto),
                                },
                                {
                                    label: translate('actionItemModal.commonValues.equipmentCriticality'),
                                    value: formatGeneralFieldValue(data.basicInfo?.equipmanetCriticality?.name),
                                },
                                {
                                    label: translate('actionItemModal.commonValues.typeOfCriticality'),
                                    value: formatGeneralFieldValue(data.basicInfo?.typeOfCriticality?.name),
                                },
                                {
                                    label: translate('actionItemModal.commonValues.replacingExistingEquipment'),
                                    value: formatBooleanFieldValue(data.basicInfo?.replacingExistingEquipment),
                                },
                                {
                                    label: translate('actionItemModal.commonValues.sparePartsRequired'),
                                    value: formatBooleanFieldValue(data.basicInfo?.sparePartsRequired),
                                },
                                {
                                    label: translate('actionItemModal.commonValues.permit'),
                                    value: formatGeneralFieldValue(data.basicInfo?.permit),
                                },
                            ],
                        },
                        {
                            title: translate('actionItemModal.commonValues.generalInfo'),
                            fields: [
                                {
                                    label: translate('actionItemModal.commonValues.startupDate'),
                                    value: formatDateFieldValue(data.generalInfo?.startupDate),
                                },
                                {
                                    label: translate('actionItemModal.commonValues.newManufacturerNeeded'),
                                    value: formatBooleanFieldValue(data.generalInfo?.newManufacturerNeeded),
                                },
                            ],
                        },
                        {
                            title: translate('actionItemModal.commonValues.locationData'),
                            fields: [
                                {
                                    label: translate('actionItemModal.commonValues.locationBuilding'),
                                    value: formatGeneralFieldValue(data.locationData?.reportingLocation?.name),
                                },
                                {
                                    label: translate('actionItemModal.commonValues.functionalLocation'),
                                    value: formatGeneralFieldValue(data.locationData?.functionalLocation?.name),
                                },
                                {
                                    label: translate('actionItemModal.commonValues.newFunctionalLocationNeeded'),
                                    value: formatBooleanFieldValue(data.locationData?.newFunctionalLocationNeeded),
                                },
                                {
                                    label: translate('actionItemModal.commonValues.suggestedTechnicalId'),
                                    value: formatGeneralFieldValue(data.locationData?.suggestedTechnicalId),
                                },
                            ],
                        },
                    ],
                },
            ],
        })

        modalData = { sections }
    } else if (sourceTypeId === ActionSourceTypeExternalIdEnum.AIMEvent && sourceEventData) {
        if (sourceEventData.access) {
            const data =
                Array.isArray(sourceEventData.data) && sourceEventData.data.length > 0
                    ? sourceEventData.data[0]
                    : (sourceEventData.data as SourceEvent | undefined)

            sections.push({
                subsections: [
                    {
                        title: translate('actionItemModal.modalSourceEventRawData.aimEventInfo'),
                        columns: [
                            {
                                title: formatGeneralFieldValue(data?.externalId),
                                fields: [
                                    {
                                        label: translate('actionItemModal.modalSourceEventRawData.title'),
                                        value: formatGeneralFieldValue(data?.title),
                                    },
                                    {
                                        label: translate('actionItemModal.commonValues.unit'),
                                        value: formatGeneralFieldValue(data?.reportingUnit?.description),
                                    },
                                    {
                                        label: translate('actionItemModal.modalSourceEventRawData.reportingLocation'),
                                        value: formatGeneralFieldValue(data?.reportingLocation?.description),
                                    },
                                ],
                            },
                            {
                                fields: [
                                    {
                                        label: translate('actionItemModal.commonValues.businessLine'),
                                        value: formatGeneralFieldValue(data?.businessLine?.name),
                                    },
                                    {
                                        label: translate('actionItemModal.commonValues.category'),
                                        value: formatGeneralFieldValue(data?.category?.name),
                                    },
                                    {
                                        label: translate('actionItemModal.modalSourceEventRawData.subCategory'),
                                        value: formatGeneralFieldValue(data?.subCategory?.name),
                                    },
                                    {
                                        label: translate(
                                            'actionItemModal.modalSourceEventRawData.siteSpecificCategory'
                                        ),
                                        value: formatGeneralFieldValue(data?.siteSpecificCategory?.name),
                                    },
                                ],
                            },
                        ],
                    },
                ],
            })

            sections.push({
                subsections: [
                    {
                        title: translate('actionItemModal.commonValues.description'),
                        columns: [
                            {
                                fields: [
                                    {
                                        label: '',
                                        value: formatGeneralFieldValue(data?.description),
                                    },
                                ],
                            },
                        ],
                    },
                ],
            })
        } else {
            sections.push({ title: translate('alerts.noAuthorizedAccess') })
        }

        modalData = { sections }
    } else if (sourceTypeId === ActionSourceTypeExternalIdEnum.ICAPMOCReport && ICAPMOCReportData.length > 0) {
        const data = ICAPMOCReportData[0]

        sections.push({
            subsections: [
                {
                    columns: [
                        {
                            fields: [
                                {
                                    label: translate('actionItemModal.modalICAPMOCReportRawData.changeInitiator'),
                                    value: data.event?.owner
                                        ? `${formatGeneralFieldValue(
                                              data.event.owner.lastName,
                                              ''
                                          )} ${formatGeneralFieldValue(data.event.owner.firstName, '')}`
                                        : 'N/A',
                                },
                                {
                                    label: translate('actionItemModal.modalICAPMOCReportRawData.moc'),
                                    value: formatGeneralFieldValue(data.number.slice(0, data.number.indexOf('-'))),
                                },
                                {
                                    label: translate('actionItemModal.commonValues.businessLine'),
                                    value: formatGeneralFieldValue(data.event?.businessLine),
                                },
                                {
                                    label: translate('actionItemModal.modalICAPMOCReportRawData.changeDescription'),
                                    value: formatGeneralFieldValue(data.event?.description),
                                },
                            ],
                        },
                        {
                            fields: [
                                {
                                    label: translate('actionItemModal.modalICAPMOCReportRawData.changeTitle'),
                                    value: formatGeneralFieldValue(data.event?.name),
                                },
                                {
                                    label: translate('actionItemModal.commonValues.creationDate'),
                                    value: formatGeneralFieldValue(data.event?.createdTime),
                                },
                                {
                                    label: translate('actionItemModal.commonValues.category'),
                                    value: 'MOC',
                                },
                            ],
                        },
                        {
                            fields: [
                                {
                                    label: translate('actionItemModal.commonValues.unit'),
                                    value: formatGeneralFieldValue(data.event?.reportingUnit?.name.slice(3)),
                                },
                                {
                                    label: translate('actionItemModal.commonValues.status'),
                                    value: formatGeneralFieldValue(data.status),
                                },
                            ],
                        },
                    ],
                },
            ],
        })

        modalData = { sections }
    } else if (
        sourceTypeId === ActionSourceTypeExternalIdEnum.GapAssessmentExecutionData &&
        gapAssessmentExecutionData.length > 0
    ) {
        const data = gapAssessmentExecutionData[0]

        sections.push({
            subsections: [
                {
                    title: translate('actionItemModal.modalGapAssessmentExecutionDataRawData.info'),
                    columns: [
                        {
                            fields: [
                                {
                                    label: translate('actionItemModal.commonValues.category'),
                                    value: formatGeneralFieldValue(data.categoryName),
                                },
                                {
                                    label: translate('actionItemModal.modalGapAssessmentExecutionDataRawData.topic'),
                                    value: formatGeneralFieldValue(data.topicName),
                                },
                                {
                                    label: translate(
                                        'actionItemModal.modalGapAssessmentExecutionDataRawData.tier2Requirement'
                                    ),
                                    value: formatGeneralFieldValue(data.requirement),
                                },
                                {
                                    label: translate(
                                        'actionItemModal.modalGapAssessmentExecutionDataRawData.systemInPlace'
                                    ),
                                    value: formatGeneralFieldValue(data.systemInPlace),
                                },
                                {
                                    label: translate(
                                        'actionItemModal.modalGapAssessmentExecutionDataRawData.gapDetails'
                                    ),
                                    value: formatGeneralFieldValue(data.gapDetails),
                                },
                                {
                                    label: translate(
                                        'actionItemModal.modalGapAssessmentExecutionDataRawData.verification'
                                    ),
                                    value: formatGeneralFieldValue(data.verification),
                                },
                            ],
                        },
                    ],
                },
            ],
        })

        modalData = { sections }
    } else if (sourceTypeId === ActionSourceTypeExternalIdEnum.OFWAEvent && OFWAEventData.length > 0) {
        const data = OFWAEventData[0]

        sections.push({
            subsections: [
                {
                    title: formatGeneralFieldValue(data?.externalId),
                    subtitle: translate('actionItemModal.commonValues.detailsExternalId'),
                    externalId: formatGeneralFieldValue(data?.externalId),
                    columns: [
                        {
                            fields: [
                                {
                                    label: translate('actionItemModal.commonValues.unit'),
                                    value: formatGeneralFieldValue(data.refUnit?.description),
                                },
                                {
                                    label: translate('actionItemModal.modalOFWAEventRawData.observationType'),
                                    value: formatGeneralFieldValue(data.refSubProcess?.name),
                                },
                                {
                                    label: translate('actionItemModal.modalOFWAEventRawData.createdBy'),
                                    value: formatGeneralFieldValue(data.createdBy),
                                },
                            ],
                        },
                        {
                            fields: [
                                {
                                    label: translate('actionItemModal.commonValues.category'),
                                    value: formatGeneralFieldValue(
                                        data.refCategory?.items
                                            ?.map((x) => x.name)
                                            ?.filter((name) => name !== undefined)
                                    ),
                                },
                                {
                                    label: translate('actionItemModal.commonValues.status'),
                                    value: formatGeneralFieldValue(data.observationStatus),
                                },
                            ],
                        },
                        {
                            fields: [
                                {
                                    label: translate('actionItemModal.modalOFWAEventRawData.subcategory'),
                                    value: formatGeneralFieldValue(
                                        data.refOFWAProcess?.items
                                            ?.map((x) => x.name)
                                            ?.filter((name) => name !== undefined)
                                    ),
                                },
                                {
                                    label: translate('actionItemModal.modalOFWAEventRawData.createdOn'),
                                    value: formatGeneralFieldValue(data.date),
                                },
                            ],
                        },
                    ],
                },
            ],
        })

        modalData = { sections }
    } else if (sourceTypeId === ActionSourceTypeExternalIdEnum.ICAPMOOCReport && ICAPMOOCReportData.length > 0) {
        const data = ICAPMOOCReportData[0]

        sections.push({
            subsections: [
                {
                    columns: [
                        {
                            fields: [
                                {
                                    label: translate('actionItemModal.modalICAPMOOCReportRawData.moocOwner'),
                                    value: data.event?.owner
                                        ? `${formatGeneralFieldValue(
                                              data.event.owner.lastName,
                                              ''
                                          )}, ${formatGeneralFieldValue(data.event.owner.firstName, '')}`
                                        : 'N/A',
                                },
                                {
                                    label: translate('actionItemModal.commonValues.creationDate'),
                                    value: formatGeneralFieldValue(data.event?.createdTime),
                                },
                                {
                                    label: translate('actionItemModal.commonValues.unit'),
                                    value: formatGeneralFieldValue(data.event?.reportingUnit?.description),
                                },
                                {
                                    label: translate('actionItemModal.modalICAPMOOCReportRawData.moocDescription'),
                                    value: formatGeneralFieldValue(data.event.description),
                                },
                            ],
                        },
                        {
                            fields: [
                                {
                                    label: translate('actionItemModal.modalICAPMOOCReportRawData.moocTitle'),
                                    value: formatGeneralFieldValue(data.event.name),
                                },
                                {
                                    label: translate('actionItemModal.commonValues.status'),
                                    value: formatGeneralFieldValue(data.event.status),
                                },
                                {
                                    label: translate('actionItemModal.modalICAPMOOCReportRawData.newEmployee'),
                                    value: data.newEmployee
                                        ? `${formatGeneralFieldValue(
                                              data.newEmployee.lastName,
                                              ''
                                          )}, ${formatGeneralFieldValue(data.newEmployee.firstName, '')}`
                                        : 'N/A',
                                },
                            ],
                        },
                        {
                            fields: [
                                {
                                    label: 'MOOC #',
                                    value: formatGeneralFieldValue(data.event.number),
                                },
                                {
                                    label: translate('actionItemModal.commonValues.businessLine'),
                                    value: formatGeneralFieldValue(data.event.businessLine),
                                },
                                {
                                    label: translate('actionItemModal.modalICAPMOOCReportRawData.positionImpacted'),
                                    value: formatGeneralFieldValue(data.positionImpacted),
                                },
                            ],
                        },
                    ],
                },
            ],
        })

        modalData = { sections: sections }
    } else if (sourceTypeId && RCA_SOURCE_TYPES.has(sourceTypeId) && RCAEventData) {
        const fields = buildRcaFieldsBySource(sourceTypeId as ActionSourceTypeExternalIdEnumType, RCAEventData)

        const problemDescriptionLabel = translate('actionItemModal.modalRcaEventRawData.problemDescription')
        const immediateActionTakenLabel = translate('actionItemModal.modalRcaEventRawData.immediateActionTaken')

        const problemDescriptionField = fields.find((f) => f.label === problemDescriptionLabel)
        const immediateActionTakenField = fields.find((f) => f.label === immediateActionTakenLabel)

        const fieldsFiltered = fields.filter(
            (f) => f.label !== problemDescriptionLabel && f.label !== immediateActionTakenLabel
        )

        const columns = splitIntoColumns(
            problemDescriptionField || immediateActionTakenField ? fieldsFiltered : fields,
            problemDescriptionField ? 3 : 4
        ).map((colFields) => ({ fields: colFields }))

        const rows = [
            ...(problemDescriptionField ? [problemDescriptionField] : []),
            ...(immediateActionTakenField ? [immediateActionTakenField] : []),
        ]

        sections.push({
            subsections: [
                {
                    title: translate('actionItemModal.modalRcaEventRawData.investigationDetails'),
                    rows: [
                        {
                            label: translate('actionItemModal.modalRcaEventRawData.investigationTeam'),
                            value: formatGeneralFieldValue(RCAEventData.general?.investigationTeam),
                        },
                    ],
                    columns: [
                        {
                            fields: [
                                {
                                    label: translate('actionItemModal.modalRcaEventRawData.investigationStartDate'),
                                    value: formatDateFieldValue(RCAEventData.general?.investigationStartDate),
                                },
                                {
                                    label: translate('actionItemModal.modalRcaEventRawData.approvers'),
                                    value: formatGeneralFieldValue(RCAEventData.general?.approvers),
                                },
                            ],
                        },
                        {
                            fields: [
                                {
                                    label: translate('actionItemModal.modalRcaEventRawData.dueDate'),
                                    value: formatDateFieldValue(RCAEventData.general?.dueDate),
                                },
                                {
                                    label: translate('actionItemModal.modalRcaEventRawData.leadInvestigator'),
                                    value: formatGeneralFieldValue(RCAEventData.general?.leadInvestigator),
                                },
                            ],
                        },
                        {
                            fields: [
                                {
                                    label: translate('actionItemModal.modalRcaEventRawData.rcaType'),
                                    value: formatGeneralFieldValue(RCAEventData.general?.rcaType),
                                },
                                {
                                    label: translate('actionItemModal.modalRcaEventRawData.rootCauses'),
                                    value: formatGeneralFieldValue(RCAEventData.general?.rootCauses),
                                },
                            ],
                        },
                    ],
                },
                {
                    title: translate('actionItemModal.modalRcaEventRawData.eventDetails'),
                    columns,
                    rows,
                },
            ],
        })

        modalData = {
            sections: sections,
            subtitle: translate('actionItemModal.commonValues.detailsExternalId'),
            externalId: formatGeneralFieldValue(RCAEventData.general?.externalEventId),
        }
    }

    return modalData
}
