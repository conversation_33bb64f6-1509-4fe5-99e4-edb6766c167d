from cognite.client.data_classes.data_modeling.query import (
    EdgeResultSetExpression,
    NodeResultSetExpression,
    Query,
    Select,
    SourceSelector,
)
from cognite.client.data_classes.filters import And, Equals, Filter, In, SpaceFilter

from clients.core.cognite_mappers import CogniteDep<PERSON>encyMapper
from clients.core.constants import <PERSON><PERSON><PERSON>, SEARCH_LIMIT, DataModelSpaceEnum, ViewEnum
from clients.core.models import ServiceParams
from clients.equipment.models import EquipmentResult
from clients.equipment.requests import GetEquipmentRequest


class EquipmentClient:
    """Client for retrieving equipment data from the Cognite Data Model."""

    def __init__(self, params: ServiceParams) -> None:
        """
        Initialize the EquipmentClient with required service parameters.

        Args:
            params (ServiceParams): Configuration object containing the Cognite client instance,
                data model ID, view mappings, and logging utilities.

        """
        self._cognite_client = params.cognite_client
        self._data_model_id = params.data_model.as_id()
        self._equipment_view = params.get_views()[ViewEnum.EQUIPMENT]
        self._functional_location_view = params.get_views()[
            ViewEnum.FUNCTIONAL_LOCATION
        ]
        self.log = params.logging

    def get_equipments(self, request: GetEquipmentRequest) -> list[EquipmentResult]:
        """
        Retrieve a list of equipment nodes based on the request parameters.

        If a search term is provided, it filters the equipment by matching the term
        against the selected search properties. Otherwise, it returns all equipment
        related to the specified reporting units and functional locations.

        Args:
            request (GetEquipmentRequest): The request object containing filters such as
                reporting unit IDs, functional location IDs, and search terms.

        Returns:
            list[EquipmentResult]: A list of equipment result objects retrieved from the data model.

        """
        if request.search:
            request.external_ids = self._handle_search(request)

        select_clause = self._build_select_clause()
        with_clause = self._build_with_clause(request)

        select_clause_dict = {"equipments": select_clause}

        res = self._cognite_client.data_modeling.instances.query(
            Query(with_=with_clause, select=select_clause_dict),
        )

        return list(EquipmentResult.from_node_list(res.get_nodes("equipments")))

    def _handle_search(self, request: GetEquipmentRequest) -> list[str]:
        """
        Perform a text-based search on equipment instances using the search term and search properties provided in the request.

        Args:
            request (GetEquipmentRequest): The request object containing the search term
                and properties to be used for filtering.

        Returns:
            list[str]: A list of external IDs for the equipment instances that match the search.
                       If no matches are found, returns a list with a placeholder ["-"].

        """
        equipments = self._cognite_client.data_modeling.instances.search(
            view=self._equipment_view,
            query=request.search or "",
            filter=SpaceFilter(request.spaces),
            properties=request.search_properties,
            limit=SEARCH_LIMIT,
        )

        normalized_search_term = (request.search or "").lower()
        matching_equipment_ids: list[str] = [
            equipment["externalId"]
            for equipment in CogniteDependencyMapper.map(equipments)
            if any(
                normalized_search_term in str(equipment[prop]).lower()
                for prop in request.search_properties
                if prop in equipment and isinstance(equipment[prop], str)
            )
        ]

        return matching_equipment_ids or ["-"]

    def _build_with_clause(self, request: GetEquipmentRequest) -> dict:
        """
        Build the WITH clause for the data model query, defining how the nodes and edges are connected in the hierarchy based on the request filters.

        Args:
            request (GetEquipmentRequest): The request object containing IDs and filters
                for reporting units, functional locations, and equipment.

        Returns:
            dict: A dictionary mapping alias names to expressions defining how to traverse
                the data model graph from reporting units to equipment.

        """

        def _build_equipment_filters() -> list[Filter]:
            filters: list[Filter] = [SpaceFilter(request.spaces)]
            if request.external_ids:
                filters.append(In(["node", "externalId"], request.external_ids))
            return filters

        def _optional_in_filter(field: str, values: list[str] | None) -> Filter | None:
            return In(["node", field], values) if values else None

        equipment_filters = _build_equipment_filters()

        if (
            not request.reporting_unit_external_ids
            and not request.functional_location_external_ids
        ):
            return {
                "equipments": NodeResultSetExpression(
                    filter=And(*equipment_filters),
                    limit=LIMIT,
                ),
            }

        return {
            "reporting_units": NodeResultSetExpression(
                filter=_optional_in_filter(
                    "externalId",
                    request.reporting_unit_external_ids,
                ),
                limit=LIMIT,
            ),
            "reporting_units_to_functional_locations": EdgeResultSetExpression(
                from_="reporting_units",
                filter=Equals(
                    ["edge", "type"],
                    {
                        "externalId": "ReportingUnit.refersTo",
                        "space": DataModelSpaceEnum.ASSET_HIERARCHY_DATA_MODEL_SPACE,
                    },
                ),
                limit=LIMIT,
            ),
            "functional_locations": NodeResultSetExpression(
                from_="reporting_units_to_functional_locations",
                filter=_optional_in_filter(
                    "externalId",
                    request.functional_location_external_ids,
                ),
                limit=LIMIT,
            ),
            "equipments": NodeResultSetExpression(
                from_="functional_locations",
                through=self._equipment_view.as_property_ref(
                    "functionalLocationParent",
                ),
                filter=And(*equipment_filters),
                direction="inwards",
                chain_to="source",
                limit=LIMIT,
            ),
        }

    def _build_select_clause(self) -> Select:
        """
        Build the SELECT clause of the data model query, specifying which properties of equipments to retrieve.

        Returns:
            Select: A select expression targeting the configured equipment view
                and its 'name' property.

        """
        return Select(
            sources=[
                SourceSelector(
                    source=self._equipment_view,
                    properties=[
                        "name",
                        "description",
                        "number",
                        "functionalLocationParent",
                    ],
                ),
            ],
        )
