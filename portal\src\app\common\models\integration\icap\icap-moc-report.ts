import { ExternalEntity } from '../../common'

export interface ICAPMOCReport extends ExternalEntity {
    number: string
    status?: string
    event?: Event
}

interface Event extends ExternalEntity {
    owner?: Owner
    name: string
    description: string
    createdTime: string
    businessLine: string
    reportingUnit?: ReportinUnit
}

interface Owner extends ExternalEntity {
    firstName: string
    lastName: string
}

interface ReportinUnit extends ExternalEntity {
    name: string
}
