import { ActionItemLink } from '../models/link'

export function formatLinksToRequest(linkList?: ActionItemLink[]): ActionItemLink[] {
    return (
        linkList?.map((link) => {
            if (link.externalId?.includes('local')) {
                const { externalId, space, ...rest } = link
                return rest
            }
            return link
        }) || []
    )
}

export function getLinks(
    formattedLinkList: ActionItemLink[],
    actionItemLinks: ActionItemLink[] | undefined
): ActionItemLink[] | null | undefined {
    if (formattedLinkList.length === 0 && actionItemLinks) {
        return null
    }

    if (actionItemLinks && JSON.stringify(formattedLinkList) === JSON.stringify(actionItemLinks)) {
        return undefined
    }
    return formattedLinkList
}
