import { translate } from '@/app/common/utils/generate-translate'
import { Theme } from '@mui/material'

export type FormatChartValueOptions = {
    prefix?: string
    suffix?: string
    decimalPlaces?: number
    defaultValue?: number
    titleFormat?: string
    currency?: boolean
}

export interface MetaWithLabel {
    label: string
    value: number
    valueFormatted?: string
}

export const formatChartValue = (value: number, options?: FormatChartValueOptions) => {
    const prefix = options?.prefix || ''
    const suffix = options?.suffix || ''
    const decimalPlaces = options?.decimalPlaces ? 2 : 0
    const defaultValue = options?.defaultValue ?? 0
    const space = '\u202F'

    const preprocessedValue =
        Number.isFinite(value) && value !== 0 && Math.abs(value) < 0.0099999 ? value.toExponential(2) : value

    const formattedValue = formatCompactNumber(
        (preprocessedValue ?? defaultValue).toLocaleString('en-US', {
            style: options?.currency ? 'currency' : undefined,
            currency: options?.currency ? 'USD' : undefined,
            minimumFractionDigits: decimalPlaces,
            maximumFractionDigits: decimalPlaces,
        })
    )

    return `${prefix}${space}${formattedValue}${space}${suffix}`
}

function formatCompactNumber(formatedNumber: string): string {
    const value = Number(formatedNumber.replaceAll(',', ''))

    const sufixes = ['', 'K', 'M', 'B', 'T']
    const numAbs = Math.abs(value)
    const tier = (Math.log10(numAbs) / 3) | 0
    if (tier === 0) {
        return value.toString()
    }
    const sufix = sufixes[tier]
    const scale = Math.pow(10, tier * 3)
    const scaledNum = value / scale
    const formatedValue = scaledNum.toFixed(2)
    return formatedValue + sufix
}

export const doughnutOptions = (
    type: string,
    chartColors: string[],
    textColor: Theme,
    isMobile: boolean,
    eventPopop?: (num: number) => void
): ApexCharts.ApexOptions => {
    const getFontSize = (mobileSize: string, desktopSize: string) => (isMobile ? mobileSize : desktopSize)

    const updateResponsiveFonts = (chart: any) => {
        chart?.updateOptions({
            plotOptions: {
                pie: {
                    donut: {
                        labels: {
                            value: {
                                fontSize: getFontSize('20px', '42px'),
                                offsetY: isMobile ? 5 : 10,
                            },
                        },
                    },
                },
            },
            noData: { style: { fontSize: getFontSize('16px', '25px') } },
            tooltip: { style: { fontSize: getFontSize('12px', '16px') } },
            legend: { fontSize: getFontSize('12px', '16px') },
        })
    }

    return {
        chart: {
            width: 380,
            type: 'donut',
            events: {
                mounted: function (chart) {
                    updateResponsiveFonts(chart)
                },
                dataPointSelection: function (e, chart, opts) {
                    if (eventPopop) eventPopop(opts.dataPointIndex)
                },
            },
        },
        noData: {
            text: `${translate('common.noResultsForFilter')}`,
            align: 'center',
            verticalAlign: 'middle',
            offsetX: 0,
            offsetY: -25,
            style: {
                fontSize: getFontSize('16px', '25px'),
                color: textColor.palette.text.secondary,
                fontFamily: 'Helvetica, Arial, sans-serif',
            },
        },
        colors: chartColors,
        responsive: [
            {
                breakpoint: 600,
                options: {
                    plotOptions: {
                        bar: {
                            horizontal: false,
                        },
                    },
                    legend: {
                        horizontalAlign: 'left',
                        position: 'bottom',
                    },
                },
            },
        ],
        plotOptions: {
            pie: {
                donut: {
                    labels: {
                        show: true,
                        name: {
                            show: false,
                        },
                        value: {
                            show: type === 'donut',
                            fontSize: getFontSize('20px', '42px'),
                            fontFamily: 'Helvetica, Arial, sans-serif',
                            fontWeight: 400,
                            offsetY: isMobile ? 5 : 10,
                            formatter: function (val: any) {
                                return `${val}`
                            },
                        },
                        total: {
                            show: type === 'donut',
                            showAlways: false,
                            color: textColor.palette.text.secondary,
                            formatter: function (w) {
                                return w.globals.seriesTotals.reduce((a: any, b: any) => a + b, 0)
                            },
                        },
                    },
                },
            },
        },
        tooltip: {
            enabled: true,
            style: {
                fontSize: getFontSize('12px', '16px'),
            },
            y: {
                formatter(val) {
                    const value = +val
                    return (
                        `<div style="color:${textColor.palette.background.paper} display:inline">` +
                        formatChartValue(value, {
                            prefix: '',
                            decimalPlaces: 0,
                        }) +
                        '</div>'
                    )
                },
                title: {
                    formatter: function (seriesName) {
                        return (
                            `<div style="color:${textColor.palette.background.paper} display:inline">` +
                            seriesName +
                            ': ' +
                            '</div>'
                        )
                    },
                },
            },
        },
        dataLabels: {
            enabled: false,
            dropShadow: {
                blur: 3,
                opacity: 0.8,
            },
        },
        theme: {
            mode: textColor.palette.mode,
            palette: 'palette1',
        },
        legend: {
            show: true,
            fontSize: getFontSize('12px', '16px'),
            offsetY: -20,
            itemMargin: {
                vertical: 8,
                horizontal: isMobile ? 16 : 0,
            },
            labels: {
                colors: chartColors,
                useSeriesColors: false,
            },
            formatter: function (seriesName: any, opts: any): any {
                return `<div style="display: flex; flex-direction: column; gap: 1px; color: ${
                    opts.w.globals.markers.colors[opts.seriesIndex]
                }">
                            <div>
                                ${formatChartValue(opts.w.globals.series[opts.seriesIndex], {
                                    prefix: '',
                                    decimalPlaces: 0,
                                })}
                            </div>
                            <div style="color: #a4a4a5; max-width: 100px; white-space: normal;">
                                ${seriesName}
                            </div>
                        </div>`
            },
            onItemHover: {
                highlightDataSeries: false,
            },
        },
    }
}
