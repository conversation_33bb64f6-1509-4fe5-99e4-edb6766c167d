import azure.functions as func
from infra.action_item_trigger_retriever_factory import ActionItemTriggerRetrieverFactory

bp = func.Blueprint()

@bp.function_name(name="GetActionItemTriggers")
@bp.route("get-action-item-triggers", methods=["post"], auth_level=func.AuthLevel.ANONYMOUS)
async def main(req: func.HttpRequest) -> func.HttpResponse:
    import logging
    import json as json

    try:
        body = req.get_json()
        logging.info("Function GetActionItemTriggers started")
        application_id= body["application_id"]
        triggers = await ActionItemTriggerRetrieverFactory.retriever().get_triggers(application_id)
        logging.info(f"Finishing execution - Triggers Retrieved : {triggers.count}")
        return func.HttpResponse(json.dumps(triggers))
    except Exception as e:
        logging.info("Exception found!")
        return func.HttpResponse(f"Error: {e}", status_code=500)
    