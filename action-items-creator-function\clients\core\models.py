from dataclasses import dataclass, field
from datetime import datetime
from typing import Any, Generic, Optional, TypeVar

from cognite.client import CogniteClient
from cognite.client.data_classes import data_modeling
from cognite.client.data_classes.data_modeling import (
    NodeId,
    ViewId,
)
from gql import Client
from industrial_model import AsyncEngine
from industrial_model.queries import BasePaginatedQuery
from pydantic import BaseModel, ConfigDict, Field, ValidationError, computed_field
from pydantic.alias_generators import to_camel

from models.settings import Settings
from services.cognite_service import CogniteService
from services.graphql_service import GraphqlService
from services.logging_service import LoggingService
from services.notification_service import NotificationService
from utils.space_utils import (
    get_transactional_space_from_site_id,
    is_valid_reporting_site_id,
)

from .utils import (
    to_edge_apply,
    to_edge_delete,
    to_node_apply,
    to_node_delete,
)


@dataclass
class ServiceParams:
    cognite_client: CogniteClient
    graphql_client: Client
    data_model: data_modeling.DataModel[data_modeling.ViewId]
    settings: Settings
    logging: LoggingService
    notification_service: NotificationService
    cognite_service: CogniteService
    graphql_service: GraphqlService
    engine: AsyncEngine
    token: Optional[str]
    spaces_user_has_access: dict[str, set[str]] = field(default_factory=dict)

    def get_views(self) -> dict[str, data_modeling.ViewId]:
        return {view.external_id: view for view in self.data_model.views}


@dataclass
class ExternalSourceServiceParams:
    cognite_client: CogniteClient
    data_model: data_modeling.DataModel[data_modeling.ViewId]
    settings: Settings
    logging: LoggingService


DEFAULT_MODEL_CONFIG = ConfigDict(
    alias_generator=to_camel,
    populate_by_name=True,
    from_attributes=True,
)


class BaseCamelCaseModel(BaseModel):
    """Base model that uses camel cased property names as aliases."""

    model_config = DEFAULT_MODEL_CONFIG


class BaseEntity(BaseCamelCaseModel):
    external_id: str
    space: str

    def __hash__(self):
        return hash((self.external_id, self.space))

    def __eq__(self, other: object) -> bool:
        """Determine equality by comparing external_id and space attributes."""
        return (
            other is not None
            and isinstance(other, BaseEntity)
            and self.external_id == other.external_id
            and self.space == other.space
        )

    @staticmethod
    def generate_node_hash(external_id: str, space: str) -> str:
        return external_id + ":" + space

    def get_node_hash(self) -> str:
        return BaseEntity.generate_node_hash(self.external_id, self.space)

    def as_base_entity(self) -> "BaseEntity":
        return BaseEntity(external_id=self.external_id, space=self.space)

    def to_node_apply(
        self,
        view_id: ViewId,
        properties_to_include: list[str] | None = None,
    ):
        return to_node_apply(self, view_id, properties_to_include)

    def to_edge_apply(
        self,
        view_id: ViewId,
        edge_name: str,
        reference_type: str,
        additional_suffix: str | None = None,
    ):
        return to_edge_apply(
            self,
            view_id,
            edge_name,
            reference_type,
            additional_suffix,
        )

    def to_node_delete(self, node_name: str):
        return to_node_delete(self, node_name)

    def to_edge_delete(self, edge_name: str):
        return to_edge_delete(self, edge_name)

    def get_prop_value(self, name_or_alias: str, default: Optional[Any] = None):
        value_by_prop_name = self.__dict__.get(name_or_alias, None)
        if value_by_prop_name:
            return value_by_prop_name
        for name, field in self.model_fields.items():
            if field.alias == name_or_alias:
                return getattr(self, name)
        return default

    def to_node_id(self) -> NodeId:
        return NodeId(self.space, self.external_id)

    @staticmethod
    def serialize_as_base_entity(entity: "BaseEntity"):
        return entity.as_base_entity().model_dump(by_alias=True)

    @classmethod
    def from_cognite_node_ids(cls, nodes: list[NodeId]) -> list["BaseEntity"]:
        return [BaseEntity.model_validate(node) for node in nodes]

    @classmethod
    def model_validate_or_none(cls, obj: Any):
        try:
            return cls.model_validate(obj)
        except ValidationError:
            return None

    @classmethod
    def from_list(cls, items: list[Any], skip_on_validation_error: bool = True):
        return list(cls._from_list(items, skip_on_validation_error))

    @classmethod
    def from_single_query_result(
        cls,
        query_result: dict[str, dict[str, Any]],
        skip_on_validation_error: bool = True,
    ):
        result_values = list(query_result.values())
        if len(result_values) == 0:
            raise ValueError("Query result must have at least one key/value pair")

        return list(cls._from_list(result_values[0]["items"], skip_on_validation_error))

    @classmethod
    def _from_list(cls, items: list[Any], skip_on_validation_error: bool):
        for item in items:
            try:
                yield cls.model_validate(item)
            except ValidationError as e:
                if skip_on_validation_error:
                    continue
                raise e


class Node(BaseCamelCaseModel):
    """Represents the base cognite node structure."""

    external_id: str
    space: str

    def hash_node(self) -> str:
        return self.external_id + ":" + self.space

    @classmethod
    def from_node_list(cls, nodes: data_modeling.NodeList):
        for node in nodes:
            assert isinstance(node, data_modeling.Node)
            key = next((key for key in node.properties.keys()), None)
            if not key:
                continue
            entry: dict[str, Any] = dict(external_id=node.external_id, space=node.space)
            entry.update({str(k): v for k, v in node.properties.get(key, {}).items()})
            yield cls.model_validate(entry)

    def __hash__(self):
        return hash((self.external_id, self.space))

    def __eq__(self, other: object) -> bool:
        return (
            other is not None
            and isinstance(other, Node)
            and self.external_id == other.external_id
            and self.space == other.space
        )

    def to_node_id(self):
        return NodeId(external_id=self.external_id, space=self.space)


class DescribableEntity(Node):
    name: str | None = None
    description: str | None = None


class ReportingSiteEntity(DescribableEntity):
    site_code: str | None = None


class ApplicationEntity(DescribableEntity):
    alias: str | None = None


class AttachmentsEntity(BaseCamelCaseModel):

    external_id: str
    name: str | None = None
    mime_type: str | None = None
    metadata: Any | None = None
    source: str | None = None
    uploaded_time: datetime | None = None


class GlobalModel(BaseCamelCaseModel):
    """global model that uses reporting sites external ids and space list."""

    reporting_site_external_id: str | list[str] | None = None

    @computed_field
    @property
    def reporting_site_external_ids(self) -> list[str]:
        """Return a list of reporting site external IDs, regardless of input format."""
        if not self.reporting_site_external_id:
            return []
        return (
            [self.reporting_site_external_id]
            if isinstance(self.reporting_site_external_id, str)
            else self.reporting_site_external_id
        )

    @computed_field
    @property
    def spaces(self) -> list[str]:
        """Compute the data space identifier(s) based on the reporting site(s)."""
        return [
            get_transactional_space_from_site_id(site_id)
            for site_id in self.reporting_site_external_ids
            if is_valid_reporting_site_id(site_id)
        ]


class GlobalModelV2(BasePaginatedQuery):
    """global model that uses reporting sites external ids and space list."""

    reporting_site_external_id: str | list[str] | None = None

    @computed_field
    @property
    def reporting_site_external_ids(self) -> list[str]:
        """Return a list of reporting site external IDs, regardless of input format."""
        if not self.reporting_site_external_id:
            return []
        return (
            [self.reporting_site_external_id]
            if isinstance(self.reporting_site_external_id, str)
            else self.reporting_site_external_id
        )

    @computed_field
    @property
    def spaces(self) -> list[str]:
        """Compute the data space identifier(s) based on the reporting site(s)."""
        return [
            get_transactional_space_from_site_id(site_id)
            for site_id in self.reporting_site_external_ids
            if is_valid_reporting_site_id(site_id)
        ]


T = TypeVar("T", bound=Node)


class _PageInfo(BaseCamelCaseModel):

    has_next_page: bool = False
    end_cursor: str | None = None


class PaginatedData(BaseCamelCaseModel, Generic[T]):
    """Represents a paginated response from a cognite graphql request."""

    data: list[T] = Field(default_factory=list)
    has_next_page: bool = False
    cursor: str | None = None

    @computed_field
    @property
    def total_items(self) -> int:
        return len(self.data)

    @classmethod
    def create_default(cls) -> "PaginatedData[T]":
        return cls(data=[], has_next_page=False, cursor=None)

    def first_or_default(self) -> T | None:
        return self.data[0] if len(self.data) > 0 else None

    @classmethod
    def from_graphql_response(
        cls,
        response: dict[str, Any],
        request_page_size: int,
    ) -> "PaginatedData[T]":
        keys = list(response.keys())
        if not keys:
            return cls.create_default()

        entry = response[keys[0]]
        page_info = _PageInfo.model_validate(entry.get("pageInfo", {}))

        raw_items: list = entry.get("items", [])

        result = cls(
            has_next_page=page_info.has_next_page,
            cursor=page_info.end_cursor,
            data=raw_items,
        )

        # This fixes the Cognite pagination issue of N + 1 requests
        if result.has_next_page and len(raw_items) < request_page_size:
            result.has_next_page = False
            result.cursor = None

        return result
