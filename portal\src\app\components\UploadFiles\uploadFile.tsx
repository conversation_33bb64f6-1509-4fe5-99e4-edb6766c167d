import { Attachment } from '@/app/common/models/action-detail'
import { Box, Typography, SxProps, Theme, Tooltip } from '@mui/material'
import React, { useCallback } from 'react'
import { useDownloadFile } from '@/app/common/hooks/useDownloadFile'
import { DragAndDropUploader } from '../DragAndDrop/DragAndDropUploader'
import { ClnButton, MatIcon } from '@celanese/ui-lib'
import GenericFieldTitle from '../FieldsComponent/GenericFieldTitle'
import * as S from './styles'
import { translate } from '@/app/common/utils/generate-translate'
import dayjs from 'dayjs'
import { toCamelCase } from '@/app/common/utils/transform-options-for-filter'

interface UploadFilesProps {
    oldUploadedFiles?: Attachment[]
    newUploadedFiles?: File[]
    setOldUploadedFiles?: (value: Attachment[]) => void
    setNewUploadedFiles?: (value: File[]) => void
    isEditable?: boolean
    canUpload?: boolean
    sxProps?: SxProps<Theme>
    error?: boolean
    title?: string
    infoTitle?: string
    isErrorInfoTitle?: boolean
    hideFiles?: boolean
    showClearAllButton?: boolean
}

interface FileItemProps {
    row: Attachment | File
    onClickDownload: (id: string) => void
    onClickDelete?: (item: any) => void
    isEditable?: boolean
    canUpload?: boolean
}

const FileItem = ({ row, onClickDownload, onClickDelete, isEditable }: FileItemProps) => {
    const isAttachment = (row as Attachment).externalId !== undefined

    const formatFileSize = (sizeInBytes: number): string => {
        if (sizeInBytes === 0) return '0 Bytes'
        const k = 1024
        const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB']
        const i = Math.floor(Math.log(sizeInBytes) / Math.log(k))
        return `${(sizeInBytes / Math.pow(k, i)).toFixed(1)} ${sizes[i]}`
    }

    const truncateFileName = (fileName: string, maxLength = 30): string =>
        fileName.length > maxLength ? `${fileName.slice(0, maxLength - 3)}...` : fileName

    const getFileSize = (): string => {
        const cleanFileSize = (fileSize: string | undefined): string => {
            return fileSize?.replace(/[\\\/"]/g, '') || '1'
        }

        const size = isAttachment
            ? (row as Attachment).file?.size ?? parseInt(cleanFileSize((row as Attachment).metadata?.fileSize))
            : (row as File).size ?? parseInt(cleanFileSize((row as any).metadata.fileSize))

        return formatFileSize(size)
    }

    const fileName = isAttachment ? (row as Attachment).name : (row as File).name
    const fileSource = isAttachment && (row as Attachment).source
    const fileUploadedAt = isAttachment ? dayjs((row as Attachment).uploadedTime).format('MM/DD/YYYY') : null

    const rawFileUser = isAttachment && (row as Attachment).metadata?.user ? (row as Attachment).metadata?.user : null
    const cleanedFileUser = rawFileUser ? rawFileUser.replace(/['"\\/]/g, '') : null

    const getFileMetadata = (): JSX.Element => {
        const boldFirstWord = (text: string): JSX.Element => {
            const words = text.split(' ')
            return (
                <>
                    <strong>{words[0]} </strong>
                    {words.slice(1).join(' ')}
                </>
            )
        }

        const missingUser = !cleanedFileUser
        const missingDate = !fileUploadedAt
        let metadata = ''

        const uploadedText = translate('details.components.uploadFilesByAndOn.uploaded')
        const onText = translate('details.components.uploadFilesByAndOn.on')
        const byText = translate('details.components.uploadFilesByAndOn.by')
        const stepText = translate(`details.components.uploadFilesSteps.${toCamelCase(fileSource || 'historicalData')}`)

        switch (true) {
            case missingUser && missingDate:
                metadata = `${uploadedText} ${onText} ${stepText}`
                break
            case missingDate:
                metadata = `${uploadedText} ${byText} ${cleanedFileUser} • ${stepText}`
                break
            case missingUser:
                metadata = `${uploadedText} ${onText} ${fileUploadedAt} • ${stepText}`
                break
            default:
                metadata = `${uploadedText} ${byText} ${cleanedFileUser} ${onText} ${fileUploadedAt} • ${stepText}`
        }
        return boldFirstWord(metadata)
    }

    return (
        <Box
            sx={{
                flexGrow: 1,
                display: 'flex',
                textAlign: 'center',
                flexDirection: 'row',
                gap: 1,
                justifyContent: 'flex-start',
            }}
        >
            <Box
                sx={{
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'start',
                    gap: 1,
                    width: '100%',
                }}
            >
                <Box
                    sx={{
                        display: 'flex',
                        flexDirection: 'row',
                        justifyContent: 'space-between',
                        width: '100%',
                    }}
                >
                    <Box
                        sx={{
                            display: 'flex',
                            flexDirection: 'row',
                            alignItems: 'center',
                            cursor: 'pointer',
                            width: '100%',
                        }}
                        onClick={() =>
                            onClickDownload &&
                            onClickDownload(isAttachment ? (row as Attachment).externalId : (row as File).name)
                        }
                    >
                        <S.SaveIcon>
                            <MatIcon icon="download" fontSize="small" maxHeight={100} />
                        </S.SaveIcon>
                        <Box sx={{ maxWidth: '400px', maxHeight: '100px', display: 'flex', flexWrap: 'wrap' }}>
                            <Tooltip title={fileName} arrow>
                                <Typography sx={S.TextUploadDark}>{truncateFileName(fileName)}</Typography>
                            </Tooltip>
                            <Typography sx={S.TextUpload}>{getFileSize()}</Typography>
                        </Box>
                    </Box>

                    {isEditable && onClickDelete && (
                        <S.BoxPointer onClick={() => onClickDelete(row)}>
                            <MatIcon icon="delete" fontSize="small" maxHeight={100} />
                        </S.BoxPointer>
                    )}
                </Box>

                {isAttachment && (
                    <Typography
                        sx={{
                            fontSize: '14px',
                            color: 'text.secondary',
                            fontWeight: 400,
                            textAlign: 'start',
                        }}
                    >
                        {getFileMetadata()}
                    </Typography>
                )}
            </Box>
        </Box>
    )
}

export const UploadFiles = ({
    oldUploadedFiles,
    newUploadedFiles,
    setOldUploadedFiles,
    setNewUploadedFiles,
    isEditable,
    canUpload,
    sxProps,
    error,
    title,
    infoTitle,
    isErrorInfoTitle,
    hideFiles,
    showClearAllButton = true,
}: UploadFilesProps) => {
    const { downloadFiles } = useDownloadFile()

    const handleUploadFile = useCallback(
        (newFiles: File[]) => {
            const updatedFiles = newUploadedFiles ? [...newUploadedFiles, ...newFiles] : [...newFiles]

            setNewUploadedFiles && setNewUploadedFiles(updatedFiles)
        },
        [setNewUploadedFiles]
    )

    const handleDeleteAttachment = useCallback(
        (attachment: Attachment) => {
            const updatedAttachments = (oldUploadedFiles ?? []).filter((a) => a.externalId !== attachment.externalId)

            setOldUploadedFiles && setOldUploadedFiles(updatedAttachments)
        },
        [setOldUploadedFiles]
    )

    const handleDeleteFile = useCallback(
        (file: File) => {
            const updatedFiles = (newUploadedFiles ?? []).filter((f) => f.name !== file.name)

            setNewUploadedFiles && setNewUploadedFiles(updatedFiles)
        },
        [setNewUploadedFiles]
    )

    const renderFiles = (files: any[], onDelete: (file: any) => void) =>
        files.map((file, index) => (
            <FileItem
                key={index}
                row={file}
                onClickDownload={async (id: string) => await downloadFiles([id])}
                onClickDelete={isEditable ? onDelete : undefined}
                isEditable={isEditable}
                canUpload={canUpload}
            />
        ))

    return (
        <Box sx={{ gap: 1, display: 'flex', flexDirection: 'column', height: '100%' }}>
            <Box sx={S.titleUpload}>
                <GenericFieldTitle
                    fieldName={title ?? translate('details.components.uploadFiles')}
                    isSubHeader
                    isError={error}
                />
                {!hideFiles && isEditable && showClearAllButton && (
                    <ClnButton
                        label={translate('stepper.form.assignment.clearAll')}
                        variant="text"
                        size="medium"
                        onClick={() => {
                            setNewUploadedFiles?.([])
                            setOldUploadedFiles?.([])
                        }}
                        data-test="new_action_item_create_new_event-clear_all_upload_button"
                        data-origin="ui-lib"
                    />
                )}
            </Box>
            {infoTitle && <GenericFieldTitle fieldName={infoTitle} isError={isErrorInfoTitle} />}
            {(isEditable || canUpload) && (
                <DragAndDropUploader
                    onFileUpload={handleUploadFile}
                    files={[]}
                    dataTest="new_action_item_create_new_event-upload_file_button"
                />
            )}
            <Box sx={{ display: 'flex', flexDirection: 'column', overflow: 'auto', height: '100%' }}>
                {oldUploadedFiles?.length || newUploadedFiles?.length ? (
                    <Box
                        sx={{
                            display: 'flex',
                            flexDirection: 'column',
                            gap: '1rem',
                            ...sxProps,
                        }}
                    >
                        {renderFiles(oldUploadedFiles ?? [], handleDeleteAttachment)}
                        {renderFiles(newUploadedFiles ?? [], handleDeleteFile)}
                    </Box>
                ) : (
                    (!isEditable || !canUpload) &&
                    !hideFiles && (
                        <S.NoData>
                            <Typography
                                sx={{
                                    textAlign: 'center',
                                    padding: '10px',
                                    color: 'text.secondary',
                                }}
                            >
                                {translate('details.components.noFiles')}
                            </Typography>
                        </S.NoData>
                    )
                )}
            </Box>
        </Box>
    )
}
