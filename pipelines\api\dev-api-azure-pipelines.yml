trigger:
  branches:
    include:
      - dev
  paths:
    include:
      - api/*

variables:
  - group: components-licenses-keys
  - group: templates-common
  - name: azureSubscription
    value: 'Action Item Management Deploy - DEV'
  - name: webAppName
    value: 'app-dplantactionitemmgmtapi-d-ussc-01'
  - name: rootFolderOrFile
    value: 'api'
  - name: appName
    value: 'aim'
  - name: appType
    value: 'api'
  - name: environment
    value: 'dev'

pool:
  name: "GST-Backend-Linux"

resources:
  repositories:
    - repository: templates
      type: git
      name: Templates
      ref: dev
      clean: true

stages:
- template: deploy/template-deploy-all-container.yml@templates
