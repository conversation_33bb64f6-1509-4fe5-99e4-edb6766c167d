import { gql } from '@apollo/client'
import { useEffect, useState } from 'react'
import { useGraphqlQuery } from '../'
import { ActionItemCategory } from '../../models/category'
import { ActionItemSubCategory } from '../../models/sub-category'
import { ArrayEntity } from '../../models/common'
import { EntityType, GetSpace } from '../../utils/space-util'

const buildActionItemCategoryQuery = (): string => {
    const filters: string[] = []

    const space = GetSpace(EntityType.Static)

    filters.push(`{ space: { eq: "${space}" } }`)

    const queryFilter = `{ and: [ ${filters.join(', ')} ] }`

    return `
        query GetActionItemCategory {
            listActionItemCategory(
                filter: ${queryFilter}
                , first: 1000
                , sort: { description: ASC }
            ) {
                items {
                    externalId
                    name
                    description
                    space
                    subCategories {
                        items {
                            externalId
                            name
                            description
                            space
                        }
                    }
                }
            }
        }
    `
}

export const useActionItemCategories = () => {
    const query = buildActionItemCategoryQuery()
    const { data: fdmData } = useGraphqlQuery<ActionItemCategory>(gql(query), 'listActionItemCategory', {})

    const [resultData, setResultData] = useState<{ data: ActionItemCategory[]; loading: boolean }>({
        data: [],
        loading: true,
    })

    useEffect(() => {
        if (fdmData.length == 0) {
            setResultData({ data: [], loading: false })
        } else {
            const fdmDataParsed = fdmData.map((d) => {
                const arrayEntity = d.subCategories as any as ArrayEntity<ActionItemSubCategory>

                if (arrayEntity.items.length) {
                    return {
                        ...d,
                        subCategories: arrayEntity.items,
                    }
                }

                return d
            })
            setResultData({ data: fdmDataParsed, loading: false })
        }
    }, [fdmData])

    return {
        loading: resultData.loading,
        categories: resultData.data,
    }
}
