import { Status } from '@/app/common/models/action'
import { ActionStatusExternalIdHomeDefaultEnum } from '@/app/common/enums/ActionItemStatusEnum'
import { ClnButton, MatIcon } from '@celanese/ui-lib'
import { zodResolver } from '@hookform/resolvers/zod'
import {
    Autocomplete,
    Box,
    Grid,
    InputAdornment,
    TextField,
    Typography,
    styled,
    useMediaQuery,
    useTheme,
} from '@mui/material'
import { useEffect } from 'react'
import { Controller, SubmitHandler, useForm } from 'react-hook-form'
import { z } from 'zod'
import './styles.css'
import { translate } from '@/app/common/utils/generate-translate'
import { FilterInfoProps } from '../../ActionTable/HomeFilter'
import { transformDataToFormInput } from '@/app/common/utils/transform-input'

type FormSchema = z.infer<typeof formSchema>

const formSchema = z.object({
    externalIdPrefix: z.string().optional(),
    titlePrefix: z.string().optional(),
    statusExternalIds: z.array(z.string()).optional(),
})

interface ActionItemFilterProps {
    onFilter: (dataInfo: FilterInfoProps) => void
    statusForFilter: Status[]
    defaultFilter: FilterInfoProps
    activeUserExternalId?: string
}

const Form = styled('form')({
    padding: '1rem',
})

export function ActionsByEventModalFilter({ onFilter, statusForFilter, defaultFilter }: ActionItemFilterProps) {
    const theme = useTheme()
    const isMobile = useMediaQuery(theme.breakpoints.down('sm'))

    const {
        control,
        handleSubmit,
        reset,
        formState: { errors },
    } = useForm<FormSchema>({
        resolver: zodResolver(formSchema),
        defaultValues: {
            externalIdPrefix: '',
            titlePrefix: '',
            statusExternalIds: defaultFilter.statusExternalIds,
        },
    })

    const submitFn: SubmitHandler<FormSchema> = (data) => {
        onFilter({
            ...defaultFilter,
            cursor: undefined,
            externalIdPrefix: data.externalIdPrefix,
            titlePrefix: data.titlePrefix,
            statusExternalIds:
                transformDataToFormInput(data.statusExternalIds) ??
                Object.values(ActionStatusExternalIdHomeDefaultEnum),
        })
    }

    const clearFunction = () => {
        reset({
            externalIdPrefix: '',
            titlePrefix: '',
            statusExternalIds: [],
        })
    }

    const defaultFunction = () => {
        reset({
            externalIdPrefix: '',
            titlePrefix: '',
            statusExternalIds: Object.values(ActionStatusExternalIdHomeDefaultEnum),
        })
    }

    useEffect(() => {
        reset({ ...defaultFilter })
    }, [defaultFilter])

    return (
        <Form onSubmit={handleSubmit(submitFn)}>
            <Grid
                container
                sx={{
                    height: '100%',
                    width: isMobile ? '100%' : '400px',
                    flexDirection: 'column',
                    gap: 1,
                }}
            >
                <Grid item xs={12} sx={{ display: 'flex', justifyContent: 'flex-end', gap: 1 }}>
                    <ClnButton
                        size="medium"
                        variant="outlined"
                        label={translate('table.filter.clear')}
                        onClick={clearFunction}
                    />
                    <ClnButton
                        size="medium"
                        variant="outlined"
                        label={translate('table.filter.default')}
                        onClick={defaultFunction}
                    />
                </Grid>

                <Grid
                    item
                    xs={12}
                    sx={{
                        order: isMobile ? 2 : 1,
                    }}
                >
                    <Typography variant="subtitle1" color="error.main" sx={{ wordBreak: 'break-word' }}>
                        {`* ${translate('requestModal.applyFilterOrSearchSubtitle')}`}
                    </Typography>
                </Grid>

                <Grid
                    item
                    xs={12}
                    sx={{
                        order: isMobile ? 1 : 2,
                    }}
                >
                    <Grid container spacing={2}>
                        <Grid item xs={12}>
                            <Box
                                sx={{
                                    width: '100%',
                                    borderBottom: '1px solid #cccccc',
                                    marginBottom: 1,
                                }}
                            >
                                <Typography>{translate('table.headers.id')}</Typography>
                            </Box>
                            <Typography>{translate('table.headers.startWith')}</Typography>
                            <Controller
                                name="externalIdPrefix"
                                control={control}
                                render={({ field: { value, onChange } }) => (
                                    <TextField
                                        value={value}
                                        onChange={(e) => onChange(e.target.value)}
                                        size="small"
                                        fullWidth
                                        variant="outlined"
                                        placeholder={translate('table.filter.writeHere')}
                                        InputProps={{
                                            startAdornment: (
                                                <InputAdornment position="start">
                                                    <MatIcon icon="search" />
                                                </InputAdornment>
                                            ),
                                        }}
                                        error={Boolean(errors.externalIdPrefix)}
                                    />
                                )}
                            />
                        </Grid>
                        <Grid item xs={12}>
                            <Box
                                sx={{
                                    width: '100%',
                                    borderBottom: '1px solid #cccccc',
                                    marginBottom: 2,
                                }}
                            >
                                <Typography>{translate('table.headers.status')}</Typography>
                            </Box>
                            <Controller
                                name="statusExternalIds"
                                control={control}
                                render={({ field: { value, onChange } }) => (
                                    <Autocomplete
                                        multiple
                                        limitTags={2}
                                        value={
                                            statusForFilter.filter((status) => value?.includes(status.externalId)) || []
                                        }
                                        onChange={(e, newValue) =>
                                            onChange(newValue.map((status) => status.externalId))
                                        }
                                        options={statusForFilter}
                                        getOptionLabel={(option) => option.name || ''}
                                        renderInput={(params) => (
                                            <TextField
                                                {...params}
                                                size="small"
                                                fullWidth
                                                label={translate('table.headers.status')}
                                                error={Boolean(errors.statusExternalIds)}
                                            />
                                        )}
                                    />
                                )}
                            />
                        </Grid>
                        <Grid item xs={12}>
                            <Box
                                sx={{
                                    width: '100%',
                                    borderBottom: '1px solid #cccccc',
                                    marginBottom: 1,
                                }}
                            >
                                <Typography>{translate('table.headers.title')}</Typography>
                            </Box>
                            <Typography>{translate('table.headers.startWith')}</Typography>
                            <Controller
                                name="titlePrefix"
                                control={control}
                                render={({ field: { value, onChange } }) => (
                                    <TextField
                                        value={value}
                                        onChange={(e) => onChange(e.target.value)}
                                        size="small"
                                        fullWidth
                                        variant="outlined"
                                        placeholder={translate('table.filter.writeHere')}
                                        InputProps={{
                                            startAdornment: (
                                                <InputAdornment position="start">
                                                    <MatIcon icon="search" />
                                                </InputAdornment>
                                            ),
                                        }}
                                        error={Boolean(errors.titlePrefix)}
                                    />
                                )}
                            />
                        </Grid>
                    </Grid>
                </Grid>

                <Grid item xs={12} sx={{ order: 3 }}>
                    <ClnButton
                        fullWidth
                        size="medium"
                        variant="contained"
                        label={translate('table.filter.applyFilter')}
                        type="submit"
                    />
                </Grid>
            </Grid>
        </Form>
    )
}
