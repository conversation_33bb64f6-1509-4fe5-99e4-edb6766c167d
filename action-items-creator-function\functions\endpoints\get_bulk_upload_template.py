from http import HTT<PERSON>tatus
import logging
import azure.functions as func

from clients.actions.requests import BulkUploadTemplateRequest
from infra.action_item_client_factory import ActionItemClientFactory
from services.bulk_upload_service import BulkUploadService

bp = func.Blueprint()


@bp.function_name(name="GetBulkUploadTemplate")
@bp.route(
    "get-bulk-upload-template", methods=["post"], auth_level=func.AuthLevel.ANONYMOUS
)
def get_bulk_upload_template(req: func.HttpRequest) -> func.HttpResponse:
    try:
        auth_header = req.headers.get("Authorization", "")
        token_request = (
            auth_header.replace("Bearer ", "").strip()
            if auth_header.startswith("Bearer ")
            else auth_header
        )

        logging.info("Function GetBulkUploadTemplate started")

        try:
            req_body = req.get_json()
        except ValueError:
            return func.HttpResponse(
                "Invalid JSON body", status_code=HTTPStatus.BAD_REQUEST
            )

        request = BulkUploadTemplateRequest.model_validate(req_body)

        service = BulkUploadService(
            ActionItemClientFactory.retriever(override_token=token_request)
        )

        return service.get_bulk_upload_template(request)

    except Exception as e:
        logging.error(f"Error generating bulk upload template: {str(e)}")
        return func.HttpResponse(
            f"Error generating bulk upload template: {str(e)}",
            status_code=HTTPStatus.INTERNAL_SERVER_ERROR,
        )
