'use client'
import React from 'react'
import { Box, useMediaQuery, useTheme } from '@mui/material'
import dynamic from 'next/dynamic'
const ReactApexChart = dynamic(() => import('react-apexcharts'), { ssr: false })

import { doughnutOptions } from './optionsPieChart'
import { useChartDefaultColors } from './styles'

export interface QuantityPieItemChart {
    label: string
    value: number
}

export type FormatChartValueOptions = {
    prefix?: string
    suffix?: string
    decimalPlaces?: number
    defaultValue?: number
    titleFormat?: string
    currency?: boolean
}

export interface ClnPieChartConfig {
    id: string
    data: QuantityPieItemChart[]
    type: 'pie' | 'donut'
    title?: string
    showTotal?: boolean
    totalLabel?: string
    totalChartSize?: string
    width?: string | number
    height?: number | number
    chartWidth?: string | number
    labelSuffix?: string
    labelPrefix?: string
    colors?: string[]
    clickFunction?: (num: number) => void
}

export function PieChart(config: ClnPieChartConfig) {
    const chartColors = useChartDefaultColors()
    
    const theme = useTheme()
    const isMobile = useMediaQuery(theme.breakpoints.down('md'))

    return (
        <Box
            sx={{
                width: '100%',
                height: config.height || 250,
            }}
        >
            <ReactApexChart
                id={config.id}
                options={{
                    ...doughnutOptions(config.type, chartColors, theme, isMobile, config.clickFunction),
                    labels: config.data.map((point) => point.label),
                }}
                series={config.data.map((point) => point.value)}
                type={config.type}
                width={'100%'}
                height={'100%'}
            />
        </Box>
    )
}
