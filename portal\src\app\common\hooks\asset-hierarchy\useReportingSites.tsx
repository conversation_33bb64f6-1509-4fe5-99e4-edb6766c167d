import { gql } from '@apollo/client'
import { useEffect, useState } from 'react'
import { useGraphqlQuery } from '../cognite/useGraphqlQuery'
import { ArrayEntity } from '../../models/common/cognite/entity'
import { ReportingSite } from '../../models/common/asset-hierarchy/reporting-site'
import { ReportingUnit } from '../../models/common/asset-hierarchy/reporting-unit'
import { EntityType, GetSpace } from '../../utils/space-util'
import { ReportingLocation } from '../../models/common/asset-hierarchy/reporting-location'

export interface SiteQueryRequest {
    siteId?: string
}

const buildSiteQuery = (request: SiteQueryRequest): string => {
    const filters: string[] = []

    if (request.siteId && request.siteId != '') {
        filters.push(`{ externalId: { eq: "${request.siteId}" } }`)
    }

    filters.push(`{ space: { eq: "${GetSpace(EntityType.REF)}" } }`)

    const queryFilter = `{ and: [ ${filters.join(', ')} ] }`

    return `
        query GetReportingSite {
            listReportingSite(
                filter: ${queryFilter}
                , first: 1000
            ) {
                items {
                    externalId
                    name
                    description
                    aliases
                    createdTime
                    space
                    reportingUnits {
                        items {
                            externalId
                            name
                            description
                            space
                            isActive
                        }
                    }
                    reportingLocations{
                        items {
                            externalId
                            description
                            name
                            space
                            isActive
                            reportingUnit {
                                externalId
                            }
                        }
                    }
                }
            }
        }
    `
}

export const useReportingSites = (request: SiteQueryRequest) => {
    const query = buildSiteQuery(request)
    const { data: fdmData } = useGraphqlQuery<ReportingSite>(gql(query), 'listReportingSite', {})

    const [resultData, setResultData] = useState<{ data: ReportingSite[]; loading: boolean }>({
        data: [],
        loading: true,
    })

    useEffect(() => {
        const updateResultData = () => {
            if (fdmData.length === 0) {
                return { data: [], loading: false }
            }

            const fdmDataParsed = fdmData.map((d) => {
                const arrayEntity = d.reportingUnits as any as ArrayEntity<ReportingUnit>
                const arrayLocEntity = d.reportingLocations as any as ArrayEntity<ReportingLocation>
                const hasReportingUnits = arrayEntity.items.length > 0
                const hasReportingLocations = arrayLocEntity.items.length > 0

                const updatedData = { ...d }

                if (hasReportingUnits) {
                    updatedData.reportingUnits = arrayEntity.items.filter((item) => item.isActive)
                }

                if (hasReportingLocations) {
                    updatedData.reportingLocations = arrayLocEntity.items.filter((item) => item.isActive)
                }

                return updatedData
            })

            return { data: fdmDataParsed, loading: false }
        }

        setResultData(updateResultData())
    }, [fdmData])

    return {
        loading: resultData.loading,
        sites: resultData.data,
    }
}
