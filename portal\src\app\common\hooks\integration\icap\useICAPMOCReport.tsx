import { useEffect, useState } from 'react'
import { useGetAllResultsFunctionFromCustomClient } from '../../general-functions/useGetAllResultFunction'
import { ICAPMOCReport } from '@/app/common/models/integration/icap/icap-moc-report'
import { Filter } from '@/app/common/models/base-hook-request'

const mocReportQuery = `
    externalId
    space
    number
    status
    event {
        externalId
        space
        owner {
            externalId
            space
            firstName
            lastName
        }
        name
        description
        createdTime
        businessLine
        reportingUnit {
            externalId
            space
            name
        }
    }
`

export const useICAPMOCReport = (externalId?: string) => {
    const [resultData, setResultData] = useState<{ data: ICAPMOCReport[]; loading: boolean }>({
        data: [],
        loading: true,
    })

    const { getAllItems: getAllData } = useGetAllResultsFunctionFromCustomClient<ICAPMOCReport>(
        mocReportQuery,
        'listICAPMOCReport',
        'icap'
    )

    const filter: Filter<ICAPMOCReport> = {
        externalId: { eq: externalId ?? '' },
    }

    useEffect(() => {
        if (!externalId) return

        getAllData(filter).then((res: any) => {
            if (res.length == 0) {
                setResultData({ data: [], loading: false })
            } else {
                setResultData({ data: res, loading: false })
            }
        })
    }, [externalId])

    return {
        loading: resultData.loading,
        data: resultData.data,
    }
}
