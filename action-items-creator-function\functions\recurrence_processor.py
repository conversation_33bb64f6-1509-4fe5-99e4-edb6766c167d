import azure.functions as func


bp = func.Blueprint()


@bp.function_name(name="RecurrenceProcessor")
@bp.schedule(arg_name="timer", schedule="%RECURRENCE_PROCESSOR_CRON%")
async def main(timer: func.TimerRequest):
    import logging
    import os
    import sys

    script_dir = os.path.dirname(os.path.abspath(__file__))
    project_dir = os.path.dirname(script_dir)
    sys.path.append(project_dir)
    from infra.recurrent_action_items_creator_factory import (
        RecurrentActionItemsCreatorFactory,
    )

    if timer.past_due:
        logging.info("The timer is past due.")

    logging.info(await RecurrentActionItemsCreatorFactory.create().execute())
