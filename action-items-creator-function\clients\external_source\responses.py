from pydantic import Field

from clients.core.models import BaseCamelCaseModel
from clients.external_source.quality import QualityNotificationResponse
from clients.external_source.quality_reliability import QualityReliabilityEventResponse
from clients.external_source.root_cause_analysis import (
    EventAnalysisResponse,
    EventInvestigationResponse,
    WorkProcessInvestigationResponse,
)


class ExternalSourceDetailsResponse(BaseCamelCaseModel):
    """Combined details from different external source types."""

    general: EventAnalysisResponse
    specific: (
        EventInvestigationResponse
        | WorkProcessInvestigationResponse
        | QualityReliabilityEventResponse
        | QualityNotificationResponse
        | None
    ) = Field(default=None)
