'use client'
import '../../../common/utils/polyfills'
import { ActionDetailItem, ResponseData } from '@/app/common/models/action-detail'
import { ClnPage, ClnPanel } from '@celanese/ui-lib'
import { Box } from '@mui/material'
import { useRouter } from 'next/navigation'
import { useCallback, useContext, useEffect, useMemo, useState } from 'react'
import { AzureFunctionClient } from '@/app/common/clients/azure-function-client'
import { UserManagementContext, UserManagementContextState } from '@celanese/celanese-ui'
import { useDebounceFunction } from '@/app/common/hooks/general-functions/useDebounce'
import { useSnackbar } from '@/app/common/contexts/SnackbarContext'
import { useAuthGuard } from '@/app/common/hooks/useAuthGuard'
import { translate } from '@/app/common/utils/generate-translate'
import AuthGuardWrapper from '@/app/common/wrapper/AuthGuardWrapper'
import { useLoading } from '@/app/common/contexts/LoadingContext'
import { ActionStatusExternalIdClearEnum, AllActionStatusExternalIdEnum } from '@/app/common/enums/ActionItemStatusEnum'
import { ActionItemForms } from '@/app/components/ActionItemForm'
import { UserRolesPermission } from '@celanese/celanese-sdk'
import { UserExternalContext, UserExternalContextState } from '@/app/common/contexts/UserExternalContext'

type Props = {
    params: {
        slug: string[]
    }
}

export default function ActionItemEditPage({ params }: Props) {
    const { id, siteId } = useMemo(() => {
        return params.slug.length === 2
            ? { id: params.slug[1], siteId: `STS-${params.slug[0]}` }
            : { id: params.slug[0], siteId: undefined }
    }, [params])

    const router = useRouter()
    const client = new AzureFunctionClient()

    const { showSnackbar } = useSnackbar()
    const { showLoading } = useLoading()
    const { checkPermissionsFromComponents } = useAuthGuard()

    const { userInfo } = useContext<UserManagementContextState>(UserManagementContext)
    const { userExternalInfo } = useContext<UserExternalContextState>(UserExternalContext)

    const [activeUser, setActiveUser] = useState<UserRolesPermission>()

    const [actionDetails, setActionDetails] = useState<ActionDetailItem | undefined>()
    const [loggedUserIsOwnerAndCanEdit, setLoggedUserIsOwnerAndCanEdit] = useState<boolean>(true)

    const setShowFormNewAction = (value: boolean, success: boolean) => {
        showSnackbar(translate('stepper.form.finished'), 'success', 'finished-edit-action')
        router.push(`/action-item/details/${actionDetails?.reportingSite?.siteCode}/${id}`)
    }

    const loggedUserIsMasterAdmin: boolean = useMemo(() => {
        const permissions = checkPermissionsFromComponents(
            'DetailEditIconButton',
            siteId ?? actionDetails?.reportingSite?.externalId
        )
        return permissions.isAuthorized
    }, [activeUser, actionDetails])

    const masterAdminCanEditAction = ![
        AllActionStatusExternalIdEnum.Completed,
        ActionStatusExternalIdClearEnum.Cancelled,
        ActionStatusExternalIdClearEnum.ChallengePeriod,
        ActionStatusExternalIdClearEnum.DueDateExtension,
        ActionStatusExternalIdClearEnum.ReassignmentPeriod,
    ]
        .map(String)
        .includes(actionDetails?.currentStatus?.externalId ?? '')

    const loggedUserHasAccess = loggedUserIsOwnerAndCanEdit || (loggedUserIsMasterAdmin && masterAdminCanEditAction)

    const handleLeaveCancelModal = () => {
        if (actionDetails?.recurrenceInstance || actionDetails?.isTemplate) {
            router.push('/admin-settings')
        } else {
            router.push(`/action-item/details/${actionDetails?.reportingSite?.siteCode}/${id}`)
        }
    }

    const handleAlert = (message: string, error: boolean = false) => {
        showSnackbar(message, error ? 'error' : 'success', 'edit-action')
    }

    const fetchAction = useCallback(async () => {
        try {
            if (actionDetails === undefined) {
                showLoading(true)

                if (activeUser?.email) {
                    const result: ResponseData = await client.getActionItemById({
                        externalId: id,
                        activeUserEmail: activeUser.email,
                        reportingSiteExternalId: siteId,
                        isEdition: true,
                    })

                    const actionDetail =
                        Array.isArray(result.data) && result.data.length > 0
                            ? result.data[0]
                            : (result.data as ActionDetailItem | undefined)
                    setActionDetails(actionDetail)
                    setLoggedUserIsOwnerAndCanEdit(result.permissions?.edit ?? false)
                } else {
                    setLoggedUserIsOwnerAndCanEdit(false)
                }
            }
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : translate('alerts.unexpectedErrorOcurred')
            handleAlert(errorMessage, true)
        } finally {
            showLoading(false)
        }
    }, [activeUser])

    const debouncedFetchActions = useCallback(useDebounceFunction(fetchAction, 800), [fetchAction])

    useEffect(() => {
        if (activeUser?.email !== undefined) debouncedFetchActions()
    }, [debouncedFetchActions])

    useEffect(() => {
        if (
            userExternalInfo.externalId &&
            userExternalInfo.externalId !== '' &&
            (activeUser?.externalId !== userExternalInfo.externalId ||
                activeUser?.applications !== userInfo.applications)
        ) {
            setActiveUser({
                ...userInfo,
                externalId: userExternalInfo.externalId,
                roles: userExternalInfo.roles,
                teams: userExternalInfo.teams,
            })
        }
    }, [userInfo, userExternalInfo])

    function getComponentName(paramsLength: number): string {
        if ([1, 2].includes(paramsLength) && loggedUserHasAccess) {
            return ActionItemEditPage.name
        }

        return 'UnknownComponent'
    }

    return (
        <AuthGuardWrapper componentName={getComponentName(params.slug.length)}>
            {activeUser?.email && (
                <ClnPage>
                    <ClnPanel
                        id="action-edit-panel"
                        sx={{
                            padding: '1rem',
                            display: 'flex',
                            flexDirection: 'column',
                            marginBottom: '1rem',
                        }}
                    >
                        <Box>
                            {activeUser && (siteId || actionDetails?.reportingSite?.externalId) && (
                                <ActionItemForms
                                    finishSteppedFunction={setShowFormNewAction}
                                    actionItem={actionDetails}
                                    activeUser={activeUser}
                                    isMasterAdmin={loggedUserIsMasterAdmin}
                                    fromEdit
                                    clickLeaveCancelModal={handleLeaveCancelModal}
                                    siteId={siteId ?? actionDetails?.reportingSite?.externalId!}
                                />
                            )}
                        </Box>
                    </ClnPanel>
                </ClnPage>
            )}
        </AuthGuardWrapper>
    )
}
