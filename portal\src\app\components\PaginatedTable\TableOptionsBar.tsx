import { translate } from '@celanese/celanese-ui'
import { ClnButton, ClnButtonProps, ClnChip, MatIcon } from '@celanese/ui-lib'
import { Badge, Box, Tooltip, Typography, useMediaQuery, useTheme } from '@mui/material'
import { useState } from 'react'
import { PopoverFilterBase } from '../PopoverFilterBase'
import { GridToolbarColumnsButton, GridToolbarFilterButton } from '@mui/x-data-grid-pro'
import * as S from './styles'

type ClnButtonWithTooltip = ClnButtonProps & {
    tooltipTitle?: string
}

type TableOptionsBarProps = {
    customButtons?: ClnButtonWithTooltip[]
    showSearchInput?: boolean
    showCustomFilter?: boolean
    showColumnSelector?: boolean
    showInMemoryFilterButton?: boolean
    initialSearchValue?: string
    activeFiltersCount?: number
    activeSearchChip?: string
    searchHelpMessage?: string
    searchInfoMessage?: string
    customPopoverContent?: JSX.Element
    disableSearchInput?: boolean
    onSearchSubmit?: (search: string) => void
    setActiveSearchChip?: (value: string) => void
}

export function TableOptionsBar({
    customButtons,
    showSearchInput,
    showCustomFilter,
    showColumnSelector,
    showInMemoryFilterButton,
    activeFiltersCount = 0,
    initialSearchValue = '',
    activeSearchChip = '',
    searchHelpMessage = translate('table.search.defaultSearchHelpMessage'),
    searchInfoMessage,
    customPopoverContent,
    disableSearchInput,
    onSearchSubmit,
    setActiveSearchChip,
}: TableOptionsBarProps) {
    const theme = useTheme()
    const isSmallDevice = useMediaQuery(theme.breakpoints.down('md'))

    const [searchText, setSearchText] = useState<string>(initialSearchValue)
    const [currentHelpMessage, setCurrentHelpMessage] = useState<string>(searchHelpMessage)
    const [showSearchHelp, setShowSearchHelp] = useState<boolean>(false)
    const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null)

    const handleSearchKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {
        if (event.key === 'Enter') {
            const targetValue = event.currentTarget.value.trim()
            if (targetValue.length >= 3) {
                onSearchSubmit?.(targetValue)
                setActiveSearchChip?.(targetValue)
                setSearchText('')
                setCurrentHelpMessage(searchHelpMessage)
            } else {
                setCurrentHelpMessage(translate('table.search.enterAtLeast3'))
                setShowSearchHelp(true)
            }
        }
    }

    const clearSearchChip = () => {
        setActiveSearchChip?.('')
        onSearchSubmit?.('')
    }

    const showSearchHelperText = () => {
        setCurrentHelpMessage(searchHelpMessage)
        setShowSearchHelp(true)
    }

    const filterBadge = activeFiltersCount > 99 ? '99+' : activeFiltersCount || undefined

    return (
        <S.Container sx={{ padding: 0 }}>
            <PopoverFilterBase
                anchorEl={anchorEl}
                onClose={() => setAnchorEl(null)}
                sxProps={{ paddingBottom: '10px' }}
            >
                {customPopoverContent}
            </PopoverFilterBase>

            <Box
                sx={{
                    width: '100%',
                    display: 'flex',
                    flexDirection: isSmallDevice ? 'column' : 'row',
                    gap: '1.25rem',
                    justifyContent: 'space-between',
                    padding: '1rem 0px',
                }}
            >
                {showSearchInput && (
                    <Box sx={{ display: 'flex', flexDirection: 'column' }}>
                        <Box>
                            <S.Search data-test="home-search_field" data-origin="aim">
                                <S.StyledInputBase
                                    placeholder={`${translate('common.search')}…`}
                                    value={searchText}
                                    onChange={(event) => setSearchText(event.target.value)}
                                    onKeyDown={handleSearchKeyDown}
                                    inputProps={{ 'aria-label': translate('common.search') }}
                                    onFocus={showSearchHelperText}
                                    onBlur={() => setShowSearchHelp(false)}
                                    disabled={disableSearchInput}
                                />
                                <S.SearchIconWrapper>
                                    <MatIcon icon="search" color="primary.main" fontSize={24} fontWeight={500} />
                                </S.SearchIconWrapper>
                            </S.Search>
                        </Box>

                        {isSmallDevice && (
                            <Box sx={{ mt: 1 }}>
                                {showSearchHelp && (
                                    <Box sx={{ mb: 1 }}>
                                        <Typography variant="body2" color="textSecondary">
                                            {currentHelpMessage}
                                        </Typography>
                                        <Typography
                                            variant="body2"
                                            color="error.main"
                                            sx={{ wordBreak: 'break-word', whiteSpace: 'normal' }}
                                        >
                                            {searchInfoMessage}
                                        </Typography>
                                    </Box>
                                )}

                                {showSearchInput && activeSearchChip && (
                                    <ClnChip
                                        key="chip"
                                        variant="outlined"
                                        color="primary"
                                        label={activeSearchChip}
                                        size="small"
                                        onDelete={clearSearchChip}
                                        disabled={disableSearchInput}
                                    />
                                )}
                            </Box>
                        )}
                    </Box>
                )}

                <Box
                    sx={{
                        display: 'flex',
                        flexDirection: 'row',
                        justifyContent: 'space-between',
                        gap: '0.5rem',
                        width: '100%',
                        '@media (max-width: 600px)': {
                            gap: '0rem',
                        },
                    }}
                >
                    <Box
                        sx={{
                            display: 'flex',
                            flexDirection: 'row',
                            width: isSmallDevice ? '100%' : 'auto',
                            gap: '1.25rem',
                            '@media (max-width: 600px)': {
                                gap: '0rem',
                            },
                        }}
                    >
                        {showCustomFilter && (
                            <Box sx={{ flex: '1 1 auto', width: '100%' }}>
                                <Badge color="primary" badgeContent={filterBadge}>
                                    <ClnButton
                                        variant={isSmallDevice ? 'text' : 'outlined'}
                                        label={translate('common.filters')}
                                        startIcon={!isSmallDevice ? <MatIcon icon="filter_list" /> : undefined}
                                        endIcon={isSmallDevice ? <MatIcon icon="filter_list" /> : undefined}
                                        onClick={(event) => setAnchorEl(event.currentTarget)}
                                        sx={{ width: isSmallDevice ? '100%' : 'auto', padding: '5px 12px' }}
                                        size="medium"
                                        data-test="home-filter_button"
                                        data-origin="ui-lib"
                                    />
                                </Badge>
                            </Box>
                        )}

                        {showInMemoryFilterButton && (
                            <Box sx={{ flex: '1 1 auto' }}>
                                <GridToolbarFilterButton slotProps={{ button: { sx: S.ButtonFilter } }} />
                            </Box>
                        )}

                        {showColumnSelector && (
                            <Box sx={{ flex: '1 1 auto', width: '100%' }}>
                                <GridToolbarColumnsButton
                                    slotProps={{
                                        button: {
                                            startIcon: !isSmallDevice ? <MatIcon icon="view_column" /> : undefined,
                                            endIcon: isSmallDevice ? <MatIcon icon="view_column" /> : undefined,
                                            sx: S.ButtonFilter,
                                        },
                                    }}
                                    data-test="home-columns_button"
                                    data-origin="aim"
                                />
                            </Box>
                        )}
                    </Box>

                    {customButtons && (
                        <Box
                            sx={{
                                display: 'flex',
                                flexDirection: 'row',
                                gap: '0.5rem',
                                '@media (max-width: 600px)': {
                                    borderLeft: `2px solid ${theme.palette.background.default}`,
                                    gap: '0',
                                },
                            }}
                        >
                            {customButtons
                                .filter((button) => !button.hidden)
                                .map((button, index) => {
                                    const buttonElement = (
                                        <ClnButton
                                            key={index}
                                            variant={button.variant}
                                            label={button.label}
                                            startIcon={button.startIcon}
                                            onClick={button.onClick}
                                            disabled={button.disabled}
                                            sx={button.sxProps}
                                            style={{ height: 'fit-content' }}
                                            size="medium"
                                        />
                                    )

                                    return button.tooltipTitle ? (
                                        <Tooltip key={index} title={button.tooltipTitle}>
                                            <span>{buttonElement}</span>
                                        </Tooltip>
                                    ) : (
                                        buttonElement
                                    )
                                })}
                        </Box>
                    )}
                </Box>
            </Box>

            {!isSmallDevice && (
                <Box sx={{ mb: 1 }}>
                    {showSearchHelp && (
                        <Box sx={{ mb: 1 }}>
                            <Typography variant="body2" color="textSecondary">
                                {currentHelpMessage}
                            </Typography>
                            <Typography
                                variant="body2"
                                color="error"
                                sx={{ wordBreak: 'break-word', whiteSpace: 'normal' }}
                            >
                                {searchInfoMessage}
                            </Typography>
                        </Box>
                    )}

                    {showSearchInput && activeSearchChip && (
                        <ClnChip
                            key="chip"
                            variant="outlined"
                            color="primary"
                            label={activeSearchChip}
                            size="small"
                            onDelete={clearSearchChip}
                            disabled={disableSearchInput}
                        />
                    )}
                </Box>
            )}
        </S.Container>
    )
}
