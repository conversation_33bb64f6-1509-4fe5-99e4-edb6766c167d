import { gql } from '@apollo/client'
import { useEffect, useState } from 'react'
import { useGraphqlQuery } from '../cognite/useGraphqlQuery'
import { EntityType, GetSpace } from '../../utils/space-util'
import { ExternalEntityWithName } from '../../models/common'

const buildRecurrenceTypeQuery = (): string => {
    const filters: string[] = []

    filters.push(`{ space: { eq: "${GetSpace(EntityType.Static)}" } }`)

    const queryFilter = `{ and: [ ${filters.join(', ')} ] }`

    return `
        query GetRecurrenceType {
            listRecurrenceType(
                filter: ${queryFilter}
                , first: 1000
                , sort: { name: ASC }
            ) {
                items {
                    name
                    externalId
                    space
                }
            }
        }
    `
}

export const useRecurrenceTypes = () => {
    const query = buildRecurrenceTypeQuery()
    const { data: fdmData } = useGraphqlQuery<ExternalEntityWithName>(gql(query), 'listRecurrenceType', {})

    const [resultData, setResultData] = useState<{ data: ExternalEntityWithName[]; loading: boolean }>({
        data: [],
        loading: true,
    })

    useEffect(() => {
        if (fdmData.length == 0) {
            setResultData({ data: [], loading: false })
        } else {
            setResultData({ data: fdmData, loading: false })
        }
    }, [fdmData])

    return {
        loading: resultData.loading,
        allRecurrenceType: resultData.data,
    }
}
