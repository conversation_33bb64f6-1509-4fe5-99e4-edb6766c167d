from cognite.client.data_classes.data_modeling.query import (
    NodeResultSetExpression,
    Query,
    Select,
    SourceSelector,
    QueryResult,
)
from cognite.client.data_classes.data_modeling.instances import Node
from cognite.client.data_classes.filters import (
    And,
    Equals,
    HasData,
    In,
    InstanceReferences,
)
from cognite.client.data_classes.data_modeling import ViewId

from clients.core.constants import <PERSON>IM<PERSON>, DataSpaceEnum, ViewEnum
from clients.core.models import ServiceParams

from .models import Sort<PERSON>apper
from .requests import GetSortMapperRequest


class SortMapperClient:
    def __init__(
        self,
        params: ServiceParams,
    ):
        self._cognite_client = params.cognite_client
        self._data_model_id = params.data_model.as_id()
        self._views = params.get_views()

    def get_sort_mapper(self, request: GetSortMapperRequest = GetSortMapperRequest()):
        user_azure_attribute_external_ids = self._remove_duplicates(
            request.user_azure_attribute_external_ids
        )
        user_external_ids = self._remove_duplicates(
            request.user_external_ids
            + [
                externalId.replace("UserAzureAttribute_", "")
                for externalId in user_azure_attribute_external_ids
            ]
        )
        application_external_ids = self._remove_duplicates(
            request.application_external_ids
        )
        reporting_unit_external_ids = self._remove_duplicates(
            request.reporting_unit_external_ids
        )
        reporting_location_external_ids = self._remove_duplicates(
            request.reporting_location_external_ids
        )
        category_external_ids = self._remove_duplicates(request.category_external_ids)
        sub_category_external_ids = self._remove_duplicates(
            request.sub_category_external_ids
        )
        site_specific_category_instances = self._remove_duplicates(
            request.site_specific_category_instances
        )

        action_status_view = self._views[ViewEnum.ACTION_STATUS]
        source_event_status_view = self._views[ViewEnum.SOURCE_EVENT_STATUS]
        user_view = self._views[ViewEnum.USER]
        application_view = self._views[ViewEnum.APPLICATION]
        reporting_unit_view = self._views[ViewEnum.REPORTING_UNIT]
        reporting_location_view = self._views[ViewEnum.REPORTING_LOCATION]
        category_view = self._views[ViewEnum.CATEGORY]
        sub_category_view = self._views[ViewEnum.SUB_CATEGORY]
        site_specific_category_view = self._views[ViewEnum.SITE_SPECIFIC_CATEGORY]

        with_clause = {
            "action_status": NodeResultSetExpression(
                filter=And(
                    HasData(views=[action_status_view]),
                    Equals(["node", "space"], DataSpaceEnum.AIM_REF_DATA_SPACE),
                ),
                limit=LIMIT,
            ),
            "source_event_status": NodeResultSetExpression(
                filter=And(
                    HasData(views=[source_event_status_view]),
                    Equals(["node", "space"], DataSpaceEnum.AIM_REF_DATA_SPACE),
                ),
                limit=LIMIT,
            ),
        }

        select_clause = {
            "action_status": Select(
                [SourceSelector(action_status_view, ["name"])], limit=LIMIT
            ),
            "source_event_status": Select(
                [SourceSelector(source_event_status_view, ["name"])], limit=LIMIT
            ),
        }

        if len(user_external_ids) > 0:
            with_clause["user"] = NodeResultSetExpression(
                filter=And(
                    HasData(views=[user_view]),
                    Equals(["node", "space"], DataSpaceEnum.UMG_DATA_SPACE),
                    In(["node", "externalId"], user_external_ids),
                ),
                limit=LIMIT,
            )

            select_clause["user"] = Select(
                [SourceSelector(user_view, ["displayName", "firstName", "lastName"])],
                limit=LIMIT,
            )

        if len(application_external_ids) > 0:
            with_clause["application"] = NodeResultSetExpression(
                filter=And(
                    HasData(views=[application_view]),
                    Equals(["node", "space"], DataSpaceEnum.UMG_DATA_SPACE),
                    In(["node", "externalId"], application_external_ids),
                ),
                limit=LIMIT,
            )

            select_clause["application"] = Select(
                [SourceSelector(application_view, ["alias"])],
                limit=LIMIT,
            )

        if len(reporting_unit_external_ids) > 0:
            with_clause["reporting_unit"] = NodeResultSetExpression(
                filter=And(
                    HasData(views=[reporting_unit_view]),
                    Equals(["node", "space"], DataSpaceEnum.REF_DATA_SPACE),
                    In(["node", "externalId"], reporting_unit_external_ids),
                ),
                limit=LIMIT,
            )

            select_clause["reporting_unit"] = Select(
                [SourceSelector(reporting_unit_view, ["description"])],
                limit=LIMIT,
            )

        if len(reporting_location_external_ids) > 0:
            with_clause["reporting_location"] = NodeResultSetExpression(
                filter=And(
                    HasData(views=[reporting_location_view]),
                    Equals(["node", "space"], DataSpaceEnum.REF_DATA_SPACE),
                    In(["node", "externalId"], reporting_location_external_ids),
                ),
                limit=LIMIT,
            )

            select_clause["reporting_location"] = Select(
                [SourceSelector(reporting_location_view, ["description"])],
                limit=LIMIT,
            )

        if len(category_external_ids) > 0:
            with_clause["category"] = NodeResultSetExpression(
                filter=And(
                    HasData(views=[category_view]),
                    Equals(["node", "space"], DataSpaceEnum.AIM_REF_DATA_SPACE),
                    In(["node", "externalId"], category_external_ids),
                ),
                limit=LIMIT,
            )

            select_clause["category"] = Select(
                [SourceSelector(category_view, ["name"])],
                limit=LIMIT,
            )

        if len(sub_category_external_ids) > 0:
            with_clause["sub_category"] = NodeResultSetExpression(
                filter=And(
                    HasData(views=[sub_category_view]),
                    Equals(["node", "space"], DataSpaceEnum.AIM_REF_DATA_SPACE),
                    In(["node", "externalId"], sub_category_external_ids),
                ),
                limit=LIMIT,
            )

            select_clause["sub_category"] = Select(
                [SourceSelector(sub_category_view, ["name"])],
                limit=LIMIT,
            )

        if len(site_specific_category_instances) > 0:
            with_clause["site_specific_category"] = NodeResultSetExpression(
                filter=And(
                    HasData(views=[site_specific_category_view]),
                    InstanceReferences(site_specific_category_instances),
                ),
                limit=LIMIT,
            )

            select_clause["site_specific_category"] = Select(
                [SourceSelector(site_specific_category_view, ["name"])],
                limit=LIMIT,
            )

        query_result = self._cognite_client.data_modeling.instances.query(
            Query(with_=with_clause, select=select_clause)
        )

        return SortMapper(
            action_status_map=self._get_map(
                query_result, "action_status", action_status_view
            ),
            source_event_status_map=self._get_map(
                query_result, "source_event_status", source_event_status_view
            ),
            user_map=self._get_map(query_result, "user", user_view, "displayName"),
            application_map=self._get_map(
                query_result, "application", application_view, "alias"
            ),
            reporting_unit_map=self._get_map(
                query_result, "reporting_unit", reporting_unit_view, "description"
            ),
            reporting_location_map=self._get_map(
                query_result,
                "reporting_location",
                reporting_location_view,
                "description",
            ),
            category_map=self._get_map(query_result, "category", category_view),
            sub_category_map=self._get_map(
                query_result, "sub_category", sub_category_view
            ),
            site_specific_category_map=self._get_map(
                query_result,
                "site_specific_category",
                site_specific_category_view,
                include_space_in_key=True,
            ),
        )

    @staticmethod
    def _remove_duplicates(items: list):
        return list(set(items))

    @staticmethod
    def _get_map(
        result: QueryResult,
        key: str,
        view: ViewId,
        field: str = "name",
        include_space_in_key: bool = False,
    ) -> dict[str, str]:
        if key not in result:
            return {}

        nodes: list[Node] = result.get_nodes(key).data

        return {
            (n.external_id if not include_space_in_key else n.external_id + n.space): (
                str(value).strip()
                if (value := n.properties[view][field]) is not None
                else None
            )
            for n in nodes
        }
