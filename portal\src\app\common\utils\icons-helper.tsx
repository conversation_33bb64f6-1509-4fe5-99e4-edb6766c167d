import { Box, Tooltip, Typography, useTheme } from '@mui/material'
import { useState } from 'react'
import { ModalWrapper } from '@/app/components/ModalComponent/Modal/ModalWrapper'
import { MatIcon } from '@celanese/ui-lib'
import { translate } from './generate-translate'

type IconWithTooltipProps = {
    title?: string
    fontSizeIcon?: string
}

type IconWithModalProps = {
    title?: string
    secondaryTitle?: string
    fontSizeIcon?: string
    content?: React.ReactNode
    id?: string
}

export function IconWithTooltip({ title, fontSizeIcon }: IconWithTooltipProps) {
    const [open, setOpen] = useState(false)

    const handleClick = () => {
        setOpen(!open)
    }
    return (
        <Tooltip
            arrow={true}
            title={title ?? translate('table.headers.dueDateTooltip')}
            placement="bottom"
            open={open}
            onClose={() => setOpen(false)}
            onOpen={() => setOpen(true)}
        >
            <span style={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
                <MatIcon
                    icon="info"
                    onClick={handleClick}
                    sx={{ cursor: 'pointer' }}
                    fontSize={fontSizeIcon}
                    color="primary.main"
                />
            </span>
        </Tooltip>
    )
}

export function IconWithModal({ title, secondaryTitle, fontSizeIcon, content, id }: IconWithModalProps) {
    const [open, setOpen] = useState(false)
    const theme = useTheme()

    const handleClick = () => {
        setOpen(!open)
    }
    return (
        <>
            <MatIcon
                icon="info"
                onClick={handleClick}
                sx={{ cursor: 'pointer' }}
                color="primary.main"
                data-test={id}
                data-origin="aim"
                fontSize={fontSizeIcon}
            />
            {open ? (
                <ModalWrapper
                    title={title}
                    openModal={true}
                    closeModal={() => setOpen(false)}
                    sxProps={{ backgroundColor: 'rgba(0, 0, 0, 0.5)' }}
                    sxPropsTitle={{ fontSize: '19px', marginRight: 'auto' }}
                    content={
                        <Box sx={{ padding: '0px 16px 16px 16px', maxWidth: '830px' }}>
                            <Typography
                                variant="subtitle1"
                                sx={{ fontWeight: 400, fontSize: '14px', color: theme.palette.grey[600] }}
                            >
                                {secondaryTitle}
                            </Typography>
                            {content}
                        </Box>
                    }
                    dataTest="new_action_item_flow_3-mark_as_private_info_close_button"
                ></ModalWrapper>
            ) : null}
        </>
    )
}
