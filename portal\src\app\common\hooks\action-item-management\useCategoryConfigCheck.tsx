import { useEffect, useState } from 'react'
import { Action } from '../../models/action'
import { useGraphqlQuery } from '..'
import { gql } from '@apollo/client'

export interface CategoryConfigRequest {
    categoryId?: string
    subCategoryId?: string
    siteSpecificCategoryId?: string
}

const buildActionFirst1Query = (request: CategoryConfigRequest | null): string => {
    const filters: string[] = []

    if (request?.categoryId) {
        filters.push(`{ category: { externalId: { eq: "${request.categoryId}" } } }`)
    }

    if (request?.subCategoryId) {
        filters.push(`{ subCategory: { externalId: { eq: "${request.subCategoryId}" } } }`)
    }

    if (request?.siteSpecificCategoryId) {
        filters.push(`{ siteSpecificCategory: { externalId: { eq: "${request.siteSpecificCategoryId}" } } }`)
    }

    filters.push(`{ recurrenceInstance: { externalId: { isNull: false } } }`)

    const queryFilter = `{ and: [ ${filters.join(', ')} ] }`

    return `
        query GetAction {
            listAction(
                filter: ${queryFilter}
                , first: 1
            ) {
                items {
                    externalId
                }
            }
        }
    `
}

export const useCategoryConfigCheck = (request: CategoryConfigRequest) => {
    const [resultData, setResultData] = useState<{ data: boolean; loading: boolean }>({
        data: false,
        loading: true,
    })

    const queryRecurrence = buildActionFirst1Query(request)
    const { data: fdmDataRecurrence, loading: recurrenceLoading } = useGraphqlQuery<Action>(
        gql(queryRecurrence),
        'listAction',
        {}
    )

    useEffect(() => {
        const isRecurrenceNotEmpty = fdmDataRecurrence?.length > 0

        setResultData({
            data: isRecurrenceNotEmpty,
            loading: !isRecurrenceNotEmpty && recurrenceLoading,
        })
    }, [fdmDataRecurrence, recurrenceLoading])

    return {
        loadingConfig: resultData.loading,
        assConfig: resultData.data,
    }
}
