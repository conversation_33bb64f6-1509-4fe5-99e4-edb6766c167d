'use client'
import { Dispatch, SetStateAction, useMemo } from 'react'

import { Box, Grid, Typography } from '@mui/material'
import { ActionDetailItem, Comment } from '@/app/common/models/action-detail'
import { ActionStatusExternalIdClearEnum } from '@/app/common/enums/ActionItemStatusEnum'

import GenericFieldTitle from '../../FieldsComponent/GenericFieldTitle'
import { Comments } from '../../Comments'
import * as S from './styles'
import { ClnPanel } from '@celanese/ui-lib'
import { translate } from '@/app/common/utils/generate-translate'
import { UserRolesPermission } from '@celanese/celanese-sdk'

type DetailsTabProps = {
    actionItem?: ActionDetailItem
    activeUser?: UserRolesPermission
    currentComments: Comment[]
    setCurrentComments: Dispatch<SetStateAction<Comment[]>>
}

export function DetailsTab({ actionItem, activeUser, currentComments, setCurrentComments }: DetailsTabProps) {
    const viewVoeLable: boolean = useMemo(() => {
        return (
            activeUser?.email === actionItem?.verifier?.email &&
            actionItem?.currentStatus?.externalId === ActionStatusExternalIdClearEnum.PendingVerification
        )
    }, [actionItem])

    const fieldData = [
        {
            id: 'sourceInformation',
            fieldName: 'details.fields.sourceInformation',
            value: actionItem?.sourceInformation,
            condition: !!actionItem?.sourceInformation,
        },
        {
            id: 'description',
            fieldName: 'details.fields.description',
            value: actionItem?.description,
        },
        {
            id: 'voeActionItem',
            fieldName: 'details.fields.voeActionItem',
            value: actionItem?.voeActionItem,
            condition: viewVoeLable,
        },
        {
            id: 'assigneeComments',
            fieldName: 'details.fields.assigneeComments',
            value: actionItem?.assigneeComment,
            condition: !!actionItem?.assigneeComment,
        },
    ]

    return (
        <Box
            id="action-details-details-tab"
            sx={{
                width: '100%',
                height: '100%',
                display: 'grid',
                alignItems: 'start',
                gridTemplateColumns: '1fr',
                marginRight: '10px',
                marginTop: '1rem',
            }}
        >
            <Grid container spacing={2} sx={{ height: '100%', minWidth: 0 }}>
                <Grid item md={6} xs={12}>
                    <Box
                        sx={{
                            display: 'flex',
                            flexDirection: 'column',
                            gap: '1rem',
                            height: '100%',
                            width: '100%',
                            marginBottom: '1rem',
                        }}
                    >
                        {fieldData.map(({ id, fieldName, value, condition = true }, index) =>
                            condition && value ? (
                                <ClnPanel
                                    key={index}
                                    id={`action-id-${id}`}
                                    sx={{
                                        padding: '1rem',
                                        display: 'flex',
                                        flexDirection: 'column',
                                        gap: '1rem',
                                        flex: 1,
                                        minHeight: 0,
                                        width: '100%',
                                    }}
                                >
                                    <GenericFieldTitle fieldName={translate(fieldName)} isSubHeader />
                                    <Box sx={S.BoxInformationField}>
                                        <Typography sx={S.TopografyInformation}>{value}</Typography>
                                    </Box>
                                </ClnPanel>
                            ) : null
                        )}
                    </Box>
                </Grid>
                <Grid item md={6} xs={12}>
                    <ClnPanel
                        id="action-id-comments-panel"
                        sx={{
                            padding: '1rem',
                            display: 'flex',
                            flexDirection: 'column',
                            marginBottom: '1rem',
                            maxHeight: '500px',
                            height: '100%',
                        }}
                    >
                        {actionItem?.externalId && (
                            <Comments
                                actionItem={actionItem}
                                activeUser={activeUser}
                                currentComments={currentComments}
                                setCurrentComments={setCurrentComments}
                            />
                        )}
                    </ClnPanel>
                </Grid>
            </Grid>
        </Box>
    )
}
export default DetailsTab
