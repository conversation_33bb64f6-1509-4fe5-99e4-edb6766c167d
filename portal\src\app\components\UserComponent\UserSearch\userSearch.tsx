import { <PERSON>, <PERSON>, <PERSON>Item, <PERSON><PERSON><PERSON><PERSON>utton, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> } from '@mui/material'
import React, { useEffect, useMemo, useState } from 'react'
import LoaderCircular from '../../Loader'
import { ClnButton, ClnTextField, MatIcon } from '@celanese/ui-lib'
import { DataGridTable } from '../../PaginatedTable/DataGridTable'
import { GridColDef, GridRowsProp } from '@mui/x-data-grid-pro'
import { compareUsers, UserSearchRequest, useUsersSearch } from '@/app/common/hooks/user-management/useUsers'
import { Team } from '@/app/common/models/common/user-management/team'
import {
    RoleQueryRequest,
    RoleSearchRequest,
    useRoles,
    useRoleSearch,
} from '@/app/common/hooks/user-management/useRoles'
import { TeamQueryRequest, useTeams, useTeamSearch } from '@/app/common/hooks/user-management/useTeams'
import { Role } from '@/app/common/models/common/user-management/role'

import * as S from './styles'
import { User } from '@/app/common/models/common/user-management/user'
import useDebounce from '@/app/common/hooks/general-functions/useDebounce'
import { NoTranslate } from '@celanese/celanese-ui'
import GenericFieldTitle from '../../FieldsComponent/GenericFieldTitle'
import { translate } from '@/app/common/utils/generate-translate'

export interface UserSearchProps {
    siteId?: string
    subHeader?: string
    label?: string
    hiddenSelectUsers?: boolean
    hiddenHelperText?: boolean
    hiddenTable?: boolean
    hiddenEmptyTable?: boolean
    maxHeight?: number
    searchDefault?: string
    isFilter?: boolean
    isSearch?: boolean
    getUsers?: boolean
    getRoles?: boolean
    getTeams?: boolean
    selectedUsers?: User[]
    selectedRoles?: Role[]
    selectedTeams?: Team[]
    setSelectedUsers?: (value: User[]) => void
    setSelectedRoles?: (value: Role[]) => void
    setSelectedTeams?: (value: Team[]) => void
    changeListUser?: (value: string[]) => void
    changeListRole?: (value: string[]) => void
    changeListTeam?: (value: string[]) => void
    setSearchDefault?: (value: string) => void
    originalUsers?: User[]
    originalRoles?: Role[]
    dataTest?: string
}

export const UserSearch = (props: UserSearchProps) => {
    const {
        siteId,
        subHeader,
        label,
        hiddenSelectUsers = false,
        hiddenHelperText,
        hiddenTable = false,
        hiddenEmptyTable,
        maxHeight,
        searchDefault,
        isFilter = false,
        isSearch = false,
        getUsers = true,
        getRoles,
        getTeams,
        selectedUsers,
        selectedRoles,
        selectedTeams,
        setSelectedUsers,
        setSelectedRoles,
        setSelectedTeams,
        changeListUser,
        changeListRole,
        changeListTeam,
        setSearchDefault,
        originalUsers,
        originalRoles,
        dataTest,
    } = props

    const [usersParam, setUsersParam] = useState<string>(searchDefault ?? 'NULL_PARAM')

    const [ownerAnchorEl, setOwnerAnchorEl] = useState(null)
    const [openOwnerPopper, setOpenOwnerPopper] = useState(false)

    const debouncedSearchValue = useDebounce(usersParam, 500)

    const { users: filteredUsers, loading: userLoader } = useUsersSearch(
        useMemo(() => {
            return getUsers ? debouncedSearchValue : 'NULL_PARAM'
        }, [debouncedSearchValue, getUsers])
    )

    const sortedFilteredUsers = [...filteredUsers].sort(compareUsers)

    const { searchResult: roleSearchResult, loading: isRoleSearchLoading } = useRoleSearch(
        useMemo<RoleSearchRequest>(() => {
            const searchParams: RoleSearchRequest = {
                search: getRoles ? debouncedSearchValue : 'NULL_PARAM',
            }

            return searchParams
        }, [debouncedSearchValue, getRoles])
    )

    const { roles: searchRoles, loading: isRolesLoading } = useRoles(
        useMemo<RoleQueryRequest>(() => {
            const queryParams: RoleQueryRequest = {
                siteId,
                externalIds: roleSearchResult,
            }

            return queryParams
        }, [roleSearchResult, siteId])
    )

    const { searchResult: teamSearchResult, loading: isTeamSearchLoading } = useTeamSearch(
        useMemo<UserSearchRequest>(() => {
            const searchParams: UserSearchRequest = {
                search: getTeams ? debouncedSearchValue : 'NULL_PARAM',
            }

            return searchParams
        }, [debouncedSearchValue, getTeams])
    )

    const { teams: searchTeams, loading: isTeamsLoading } = useTeams(
        useMemo<TeamQueryRequest>(() => {
            const queryParams: TeamQueryRequest = {
                siteId,
                teamIds: teamSearchResult,
            }

            return queryParams
        }, [siteId, teamSearchResult])
    )

    const selectSecondaryOwnersList: any[] = useMemo(() => {
        function convertListUsersToRows(items: any[], type: string) {
            return items.map((item) => ({
                id: item.externalId,
                users: type === 'user' ? `${item.lastName}, ${item.firstName}` : `${item.name}`,
                assignments: `${translate(`stepper.form.${type}`)}`,
            }))
        }

        const ownersUser =
            selectedUsers != null && selectedUsers.length > 0 ? convertListUsersToRows(selectedUsers, 'user') : []
        const ownersRole =
            selectedRoles != null && selectedRoles.length > 0 ? convertListUsersToRows(selectedRoles, 'role') : []
        const ownersTeam =
            selectedTeams != null && selectedTeams.length > 0 ? convertListUsersToRows(selectedTeams, 'team') : []

        return [...ownersUser, ...ownersRole, ...ownersTeam]
    }, [selectedUsers, selectedRoles, selectedTeams])

    const headCells: GridColDef[] = useMemo(() => {
        const columns: GridColDef[] = [
            {
                field: 'users',
                headerName: translate('table.headers.users'),
                flex: 1,
                renderCell: (params) => <NoTranslate>{params.row.users}</NoTranslate>,
                renderHeader: () => (
                    <span data-test="user-search-users_table_sort" data-origin="aim">
                        {translate('table.headers.users')}
                    </span>
                ),
            },
            {
                field: 'assignments',
                headerName: translate('table.headers.assignments'),
                flex: 1,
                renderCell: (params) => <NoTranslate>{params.row.assignments}</NoTranslate>,
                renderHeader: () => (
                    <span data-test="user-search-assignments_table_sort" data-origin="aim">
                        {translate('table.headers.assignments')}
                    </span>
                ),
            },
            {
                field: 'act',
                headerName: translate('table.headers.actions'),
                sortable: false,
                filterable: false,
                flex: 0.7,
                renderCell: (params) => {
                    const isOriginalUser = originalUsers?.some((user) => user.externalId === params.row.id)
                    const isOriginalRole = originalRoles?.some((role) => role.externalId === params.row.id)

                    return (
                        <ClnButton
                            disabled={isOriginalUser || isOriginalRole}
                            variant="text"
                            startIcon={
                                <MatIcon
                                    icon="delete"
                                    color="primary.main"
                                    onClick={() => handleRemoveUser(params.row.id)}
                                />
                            }
                        />
                    )
                },
                renderHeader: () => (
                    <span data-test="user-search-actions_table_sort" data-origin="aim">
                        {translate('table.headers.actions')}
                    </span>
                ),
            },
        ]

        return columns
    }, [translate, selectSecondaryOwnersList, originalUsers, originalRoles])

    const tableRowsBySecondaryOwners: GridRowsProp = useMemo(() => {
        function convertActionItemDataToRows(items: any[]) {
            return items.map((item) => ({
                id: `${item.id}`,
                users: `${item.users}`,
                assignments: `${item.assignments}`,
            }))
        }

        const ownersRows =
            selectSecondaryOwnersList != null && selectSecondaryOwnersList.length > 0
                ? convertActionItemDataToRows(selectSecondaryOwnersList)
                : []

        return ownersRows
    }, [selectSecondaryOwnersList])

    const handleOwnerFocus = (event: any) => {
        setOwnerAnchorEl(event.currentTarget)
        setOpenOwnerPopper(true)
    }

    const handleOwnerBlur = () => {
        setTimeout(() => {
            setOpenOwnerPopper(false)
        }, 500)
    }

    const handleRemoveUser = (externalId: string) => {
        const filteredUsers = selectedUsers?.filter((user) => user?.externalId != externalId) ?? []
        const filteredRoles = selectedRoles?.filter((role) => role.externalId != externalId) ?? []
        const filteredTeams = selectedTeams?.filter((team) => team?.externalId != externalId) ?? []

        setSelectedUsers && setSelectedUsers(filteredUsers)
        setSelectedRoles && setSelectedRoles(filteredRoles)
        setSelectedTeams && setSelectedTeams(filteredTeams)

        changeListUser && changeListUser(filteredUsers.map((user) => user.externalId))
        changeListRole && changeListRole(filteredRoles.map((role) => role.externalId))
        changeListTeam && changeListTeam(filteredTeams.map((team) => team.externalId))
    }

    const handleOwnerChange = (event: any) => {
        setUsersParam(event.target.value)
        setSearchDefault && setSearchDefault(event.target.value)
    }

    useEffect(() => {
        setUsersParam(searchDefault ?? `NULL_PARAM`)
    }, [searchDefault])

    return (
        <Box>
            {subHeader && (
                <Box sx={S.subHeaderBox}>
                    <GenericFieldTitle fieldName={subHeader} isSubHeader />
                </Box>
            )}
            {!hiddenSelectUsers && (
                <Box>
                    <ClnTextField
                        helperText={hiddenHelperText ? '' : `* ${translate('stepper.form.assignment.selectUsersDesc')}`}
                        label={label != null ? label : translate('stepper.form.assignment.usersSearch.name')}
                        size="small"
                        value={usersParam != 'NULL_PARAM' ? usersParam : ''}
                        variant="outlined"
                        onFocus={handleOwnerFocus}
                        onBlur={handleOwnerBlur}
                        onChange={handleOwnerChange}
                        fullWidth
                        inputRef={(node) => {
                            setOwnerAnchorEl(node)
                        }}
                        data-test={dataTest}
                        data-origin="aim"
                    />
                    <Popper
                        open={openOwnerPopper}
                        sx={S.suggestionsContainter}
                        anchorEl={ownerAnchorEl}
                        placement="bottom-start"
                    >
                        {userLoader ||
                        isRolesLoading ||
                        isTeamsLoading ||
                        isTeamSearchLoading ||
                        isRoleSearchLoading ? (
                            LoaderCircular()
                        ) : (
                            <List>
                                {sortedFilteredUsers.length === 0 &&
                                searchRoles.length === 0 &&
                                searchTeams.length === 0 ? (
                                    <ListItem>
                                        <ListItemText primary={translate('stepper.form.typeAtLeast1')} />
                                    </ListItem>
                                ) : (
                                    <>
                                        {searchRoles.map((role, index) => (
                                            <ListItemButton key={`${role.externalId}${index}`}>
                                                <ListItemText
                                                    primary={`${role.name} (${translate('stepper.form.role')})`}
                                                    onClick={() => {
                                                        if (
                                                            !selectedRoles?.some(
                                                                (selectedRole) =>
                                                                    selectedRole.externalId === role.externalId
                                                            )
                                                        ) {
                                                            setSelectedRoles &&
                                                                setSelectedRoles([...(selectedRoles ?? []), role])
                                                            changeListRole &&
                                                                changeListRole([
                                                                    ...(selectedRoles?.map((x) => x.externalId) ?? []),
                                                                    role.externalId,
                                                                ])
                                                        }
                                                        setUsersParam('NULL_PARAM')
                                                        setSearchDefault && setSearchDefault('NULL_PARAM')
                                                    }}
                                                />
                                            </ListItemButton>
                                        ))}
                                        {searchTeams.map((team, index) => (
                                            <ListItemButton key={`${team.externalId}${index}`}>
                                                <ListItemText
                                                    primary={`${team.name} (${translate('stepper.form.team')})`}
                                                    onClick={() => {
                                                        if (
                                                            !selectedTeams?.some(
                                                                (selectedTeam) =>
                                                                    selectedTeam.externalId === team.externalId
                                                            )
                                                        ) {
                                                            setSelectedTeams &&
                                                                setSelectedTeams([...(selectedTeams ?? []), team])
                                                            changeListTeam &&
                                                                changeListTeam([
                                                                    ...(selectedTeams?.map((x) => x.externalId) ?? []),
                                                                    team.externalId,
                                                                ])
                                                        }
                                                        setUsersParam('NULL_PARAM')
                                                        setSearchDefault && setSearchDefault('NULL_PARAM')
                                                    }}
                                                />
                                            </ListItemButton>
                                        ))}
                                        {sortedFilteredUsers.map((user, index) => (
                                            <ListItemButton key={`${user.externalId}${index}`}>
                                                <ListItemText
                                                    primary={`${user.lastName}, ${user.firstName} (${user.email})`}
                                                    onClick={() => {
                                                        if (
                                                            !selectedUsers?.some(
                                                                (selectedUser) =>
                                                                    selectedUser.externalId === user.externalId
                                                            )
                                                        ) {
                                                            setSelectedUsers &&
                                                                setSelectedUsers([...(selectedUsers ?? []), user])
                                                            changeListUser &&
                                                                changeListUser([
                                                                    ...(selectedUsers?.map((x) => x.externalId) ?? []),
                                                                    user.externalId,
                                                                ])
                                                        }
                                                        setUsersParam('NULL_PARAM')
                                                        setSearchDefault && setSearchDefault('NULL_PARAM')
                                                    }}
                                                />
                                            </ListItemButton>
                                        ))}
                                    </>
                                )}
                            </List>
                        )}
                    </Popper>
                </Box>
            )}
            {!hiddenTable && (!hiddenEmptyTable || selectSecondaryOwnersList.length > 0) && (
                <DataGridTable
                    initialColumnDefs={headCells}
                    rows={tableRowsBySecondaryOwners}
                    infinitePagination
                    maxHeight={maxHeight ?? 100}
                    showInMemoryFilterButton={isFilter}
                    isMemorySearch
                    searchHelpMessage={translate('table.search.searchForUsersOrAssignments')}
                    showSearchInput={isSearch}
                    showCustomFilter={false}
                    showColumnSelector={false}
                />
            )}
        </Box>
    )
}
