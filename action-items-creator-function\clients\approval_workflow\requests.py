from pydantic import Field

from clients.core.constants import (
    ApprovalWorkflowStepDescriptionEnum,
    ApprovalWorkflowStepStatusEnum,
    DataSpaceEnum,
)
from clients.core.models import GlobalModel


class GetApprovalWorkflowRequest(GlobalModel):
    """Model representing a request to retrieve approval workflows."""

    user_external_ids: list[str]
    reporting_site_external_id: str | list[str]

    approval_workflow_step_status_external_ids: list[str] = Field(
        default_factory=lambda: [
            ApprovalWorkflowStepStatusEnum.IN_PROGRESS,
        ],
    )

    approval_workflow_ste_descriptions: list[str] = Field(
        default_factory=lambda: [
            ApprovalWorkflowStepDescriptionEnum.VERIFICATION,
            ApprovalWorkflowStepDescriptionEnum.APPROVAL,
        ],
    )

    def get_users(self) -> list[dict[str, str]]:
        """Return a list of user nodes with external IDs and UMG data space."""
        return [
            {"externalId": external_id, "space": DataSpaceEnum.UMG_DATA_SPACE}
            for external_id in self.user_external_ids
        ]

    def get_status(self) -> list[dict[str, str]]:
        """Return a list of status nodes with external IDs and APW reference data space."""
        return [
            {"externalId": external_id, "space": DataSpaceEnum.APW_REF_DATA_SPACE}
            for external_id in self.approval_workflow_step_status_external_ids
        ]

    def get_filter_spaces(
        self,
    ) -> list[str]:
        """Return the applicable data spaces based on site and privacy settings."""
        return [DataSpaceEnum.PRIVATE_SPACE, *self.spaces]
