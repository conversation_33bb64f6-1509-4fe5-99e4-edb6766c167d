from app.core.models import Node


class ReportingUnitResponse(Node):
    """Represents a simplified reporting unit response."""


class ListReportingUnitResponse(list[ReportingUnitResponse]):
    """Represents a list of ReportingUnitResponses"""

    def as_dict(self) -> dict[str, ReportingUnitResponse]:
        """Return the list as a dict of externalId: ReportingUnitResponse."""
        return {ru.external_id: ru for ru in self}
