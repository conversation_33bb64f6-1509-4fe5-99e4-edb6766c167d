import { Autocomplete, Box, TextField, styled } from '@mui/material'
import { z } from 'zod'
import { Controller, SubmitHandler, useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { ClnButton } from '@celanese/ui-lib'
import { useEffect } from 'react'
import { FilterOptionsCategoriesConfiguration } from '@/app/common/models/admin-settings/filter-categories'
import { translate } from '@/app/common/utils/generate-translate'

const categoryFilterSchema = z.object({
    category: z.array(z.string()),
    subcategory1: z.array(z.string()),
    subcategory2: z.array(z.string()),
})

type CatgeoryFilterSchema = z.infer<typeof categoryFilterSchema>

type CategoriesTabFilterProps = {
    onSubmit: (filters: any) => void
    data: CatgeoryFilterSchema
    defaultFilter?: FilterOptionsCategoriesConfiguration
}

const Form = styled('form')({
    display: 'flex',
    flexDirection: 'column',
    width: '18rem',
    padding: '1.5rem',
    gap: '1rem',
})

export function CategoriesTabFilter({ data, defaultFilter, onSubmit }: CategoriesTabFilterProps) {
    const cleanFilter = {
        category: [],
        subcategory1: [],
        subcategory2: [],
    }

    const { reset, setValue, handleSubmit, control } = useForm<CatgeoryFilterSchema>({
        defaultValues: cleanFilter,
        resolver: zodResolver(categoryFilterSchema),
    })

    function selectWithAllOption(op: string[]) {
        return [...op]
    }

    const clearFunction = () => {
        reset(cleanFilter)
    }

    const defaultFunction = () => {
        reset(cleanFilter)
    }

    const submitFn: SubmitHandler<CatgeoryFilterSchema> = (data) => {
        onSubmit(data)
    }

    useEffect(() => {
        reset({ ...cleanFilter, ...defaultFilter })
    }, [defaultFilter])

    return (
        <Box>
            <Form onSubmit={handleSubmit(submitFn)}>
                <Box sx={{ display: 'flex', gap: '8px', justifyContent: 'flex-end' }}>
                    <ClnButton
                        size="small"
                        variant="outlined"
                        label={translate('table.filter.clear')}
                        onClick={() => {
                            clearFunction()
                        }}
                    />
                    <ClnButton
                        size="small"
                        variant="outlined"
                        label={translate('table.filter.default')}
                        onClick={() => {
                            defaultFunction()
                        }}
                    />
                </Box>
                <Controller
                    control={control}
                    name="category"
                    render={({ field }) => (
                        <Autocomplete
                            {...field}
                            multiple
                            limitTags={2}
                            id="category"
                            options={selectWithAllOption(data.category)}
                            value={field.value || []}
                            noOptionsText={translate('adminSettings.table.filter.noOptions')}
                            onChange={(event, value) => setValue('category', value as string[])}
                            renderInput={(params) => (
                                <TextField
                                    {...params}
                                    label={translate('adminSettings.table.filter.category')}
                                    size="small"
                                />
                            )}
                        />
                    )}
                />

                <Controller
                    control={control}
                    name="subcategory1"
                    render={({ field }) => (
                        <Autocomplete
                            {...field}
                            multiple
                            limitTags={2}
                            id="subcategory1"
                            options={selectWithAllOption(data.subcategory1)}
                            value={field.value || []}
                            noOptionsText={translate('adminSettings.table.filter.noOptions')}
                            onChange={(event, value) => setValue('subcategory1', value as string[])}
                            renderInput={(params) => (
                                <TextField
                                    {...params}
                                    label={translate('adminSettings.table.filter.subcategoryOne')}
                                    size="small"
                                />
                            )}
                        />
                    )}
                />

                <Controller
                    control={control}
                    name="subcategory2"
                    render={({ field }) => (
                        <Autocomplete
                            {...field}
                            multiple
                            limitTags={2}
                            id="subcategory2"
                            options={selectWithAllOption(data.subcategory2)}
                            value={field.value || []}
                            noOptionsText={translate('adminSettings.table.filter.noOptions')}
                            onChange={(event, value) => setValue('subcategory2', value as string[])}
                            renderInput={(params) => (
                                <TextField
                                    {...params}
                                    label={translate('adminSettings.table.filter.subcategoryTwo')}
                                    size="small"
                                />
                            )}
                        />
                    )}
                />

                <Box
                    sx={{
                        display: 'flex',
                        width: '100%',
                        justifyContent: 'center',
                        margin: '20px 0 10px 0',
                        div: {
                            width: '100%',
                        },
                        button: {
                            width: '100%',
                        },
                    }}
                >
                    <ClnButton
                        type="submit"
                        size="small"
                        variant="contained"
                        label={translate('table.filter.applyFilter')}
                    />
                </Box>
            </Form>
        </Box>
    )
}
