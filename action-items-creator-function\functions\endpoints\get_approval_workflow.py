import azure.functions as func
from infra.action_item_client_factory import ActionItemClientFactory
from clients.approval_workflow.requests import GetApprovalWorkflowRequest
from clients.core.constants import APPLICATION_JSON
import logging
import json as json

bp = func.Blueprint()


@bp.function_name(name="GetApprovalWorkflow")
@bp.route("get-approval-workflow", methods=["get"], auth_level=func.AuthLevel.ANONYMOUS)
async def get_approval_workflow(req: func.HttpRequest) -> func.HttpResponse:
    try:
        auth_header = req.headers.get("Authorization")

        if auth_header and auth_header.startswith("Bearer "):
            token_request = auth_header[len("Bearer "):]
        else:
            token_request = auth_header

        approval_workflow_request_param = req.params.get(
            "approvalWorkflowRequest", "{}"
        )
        approval_workflow_request = json.loads(approval_workflow_request_param)

        logging.info("Function GetApprovalWorkflow started")

        items = ActionItemClientFactory.retriever(
            override_token=token_request
        ).approval_workflow.get_approval_workflows(
            GetApprovalWorkflowRequest.model_validate(approval_workflow_request)
        )

        serializable_items = [
            (
                item.model_dump(mode="json", by_alias=True)
                if hasattr(item, "model_dump")
                else item
            )
            for item in items
        ]

        logging.info(f"Finishing execution - GetApprovalWorkflow")
        response_body = json.dumps({"items": serializable_items})

        return func.HttpResponse(
            response_body,
            mimetype=APPLICATION_JSON,
            status_code=200,
        )
    except Exception as e:
        logging.error(f"Exception found: {e}", exc_info=True)
        return func.HttpResponse(f"Error: {e}", status_code=500)
