import { gql, useMutation } from '@apollo/client'
import { StatusHistoryInstance } from '../../models/status-history-instance'

const UPSERT_STATUS_HISTORY_INSTANCE_MUTATION = gql`
    mutation UpsertStatusHistoryInstance($statusHistoryInstances: [_UpsertStatusHistoryInstance!]!) {
        upsertStatusHistoryInstance(items: $statusHistoryInstances) {
            space
            externalId
        }
    }
`

export const useUpsertStatusHistoryInstance = () => {
    return useMutation<StatusHistoryInstance>(UPSERT_STATUS_HISTORY_INSTANCE_MUTATION)
}
