from http import HTT<PERSON>tatus
from io import BytesIO

import azure.functions as func
from openpyxl import Workbook
from openpyxl.utils import quote_sheetname
from openpyxl.worksheet.datavalidation import DataValidation

from clients.action_item_client import ActionItemClient
from clients.actions.requests import BulkUploadTemplateRequest
from clients.category_configuration.requests import (
    GetCategoriesRequest,
    GetSiteSpecificCategoriesRequest,
    GetSubCategoriesRequest,
)
from clients.reporting_location.requests import GetReportingLocationsRequest
from clients.reporting_unit.requests import GetReportingUnitsRequest


class BulkUploadService:
    def __init__(
        self,
        action_item_client: ActionItemClient,
    ):
        self._action_item_client = action_item_client
        self._site_id: None | str

    def get_bulk_upload_template(self, request: BulkUploadTemplateRequest):
        """Generates an Excel template for bulk upload based on the provided request."""
        self._site_id = request.reporting_site_external_id

        units, locations, categories, sub_categories, site_specific_categories = (
            self._get_dropdown_options()
        )

        self._validate_params(units, categories, sub_categories)

        workbook = self._create_excel_template(
            request.columns,
            units,
            locations,
            categories,
            sub_categories,
            site_specific_categories,
        )

        return self._generate_file_response(workbook)

    def _get_dropdown_options(self):
        """Fetches dropdown options (units, locations, categories, subcategories, site specific categories) for the template."""

        def _get_options(items, attr):
            """Extracts specified attribute values from a list of items."""
            return [getattr(item, attr) for item in items] if items else []

        client = self._action_item_client
        site_id: str = self._site_id

        units = client.reporting_unit.get_reporting_units(
            GetReportingUnitsRequest(reporting_site_external_id=site_id),
        ).data

        locations = client.reporting_location.get_reporting_locations(
            GetReportingLocationsRequest(reporting_site_external_id=site_id),
        ).data

        categories = client.category_configuration.get_categories(
            GetCategoriesRequest(),
        ).data

        sub_categories = client.category_configuration.get_sub_categories(
            GetSubCategoriesRequest(),
        ).data

        site_specific_categories = (
            client.category_configuration.get_site_specific_categories(
                GetSiteSpecificCategoriesRequest(reporting_site_external_id=site_id),
            ).data
        )

        return (
            _get_options(units, "description"),
            _get_options(locations, "description"),
            _get_options(categories, "name"),
            _get_options(sub_categories, "name"),
            _get_options(site_specific_categories, "name"),
        )

    def _validate_params(self, units, categories, sub_categories):
        """Validate that the required fields are not empty."""
        if not units or not categories or not sub_categories:
            raise ValueError(
                "Missing required fields: units, categories, or subCategories must not be empty.",
            )

    def _create_excel_template(
        self,
        columns,
        units,
        locations,
        categories,
        sub_categories,
        site_specific_categories,
    ):
        """Generate the Excel workbook template with the dropdown validations."""
        workbook = Workbook()
        ws = workbook.active
        ws.title = "Action Items Template"

        priorities = ["High", "Medium", "Low"]

        hidden_sheet = self._create_hidden_sheet(
            workbook,
            units,
            locations,
            categories,
            sub_categories,
            site_specific_categories,
            priorities,
        )

        self._setup_headers(ws, columns)
        self._setup_placeholders(ws, columns)
        self._adjust_column_width(ws)

        self._add_data_validations(
            ws,
            columns,
            hidden_sheet,
            units,
            locations,
            categories,
            sub_categories,
            site_specific_categories,
            priorities,
        )

        return workbook

    def _create_hidden_sheet(
        self,
        workbook: Workbook,
        units,
        locations,
        categories,
        sub_categories,
        site_specific_categories,
        priorities,
    ):
        """Create a hidden sheet in the workbook to store dropdown values."""
        hidden_sheet = workbook.create_sheet("DropdownData")
        hidden_sheet.sheet_state = "hidden"

        self._write_to_hidden_sheet(hidden_sheet, "A", units)
        self._write_to_hidden_sheet(hidden_sheet, "B", locations)
        self._write_to_hidden_sheet(hidden_sheet, "C", categories)
        self._write_to_hidden_sheet(hidden_sheet, "D", sub_categories)
        self._write_to_hidden_sheet(hidden_sheet, "E", site_specific_categories)
        self._write_to_hidden_sheet(hidden_sheet, "F", priorities)

        return hidden_sheet

    def _write_to_hidden_sheet(self, sheet, column, values):
        """Write a list of values to a specific column in the hidden sheet."""
        for i, value in enumerate(values, start=1):
            sheet[f"{column}{i + 1}"] = value

    def _setup_headers(self, ws, columns):
        """Set up the headers for the Excel sheet."""
        headers = [column_details.header for column_details in columns.values()]

        ws.append(headers)

    def _setup_placeholders(self, ws, columns):
        """Add a placeholder row with instructions for each field."""
        placeholders = [
            column_details.placeholder for column_details in columns.values()
        ]

        ws.append(placeholders)

    def _adjust_column_width(self, ws):
        """Adjust the column width based on the content."""
        for col in ws.columns:
            max_length = 0
            column = col[0].column_letter
            for cell in col:
                if cell.value is not None:
                    try:
                        max_length = max(max_length, len(str(cell.value)))
                    except TypeError:
                        continue
            adjusted_width = max_length + 2
            ws.column_dimensions[column].width = adjusted_width

    def _add_data_validations(
        self,
        ws,
        columns,
        hidden_sheet,
        units,
        locations,
        categories,
        sub_categories,
        site_specific_categories,
        priorities,
    ):
        """Add data validations (dropdowns) for the columns in the Excel template."""
        self._add_text_length_validation(ws, "A", 10, 200)
        self._add_text_length_validation(ws, "E", 10, 1000)

        self._add_list_validation(ws, "C", hidden_sheet, "A", len(units))
        self._add_list_validation(ws, "D", hidden_sheet, "B", len(locations))

        has_source_information = columns.get("sourceInformation", False)

        if has_source_information:
            self._add_text_length_validation(ws, "F", 10, 200)

        col_offset = 1 if has_source_information else 0

        self._add_list_validation(
            ws,
            chr(ord("F") + col_offset),
            hidden_sheet,
            "C",
            len(categories),
        )
        self._add_list_validation(
            ws,
            chr(ord("G") + col_offset),
            hidden_sheet,
            "D",
            len(sub_categories),
        )
        self._add_list_validation(
            ws,
            chr(ord("H") + col_offset),
            hidden_sheet,
            "E",
            len(site_specific_categories),
        )
        self._add_list_validation(
            ws,
            chr(ord("J") + col_offset),
            hidden_sheet,
            "F",
            len(priorities),
        )

    def _add_list_validation(
        self,
        ws,
        column,
        hidden_sheet,
        hidden_column,
        value_count,
    ):
        """Add data validation to an entire column for a dropdown list."""
        validation = DataValidation(
            type="list",
            formula1=f"{quote_sheetname(hidden_sheet.title)}!${hidden_column}$2:${hidden_column}${1 + value_count}",
            allow_blank=True,
        )

        ws.add_data_validation(validation)
        validation.ranges.add(f"{column}2:{column}1001")

    def _add_text_length_validation(self, ws, column, min_length, max_length):
        """Adds text length validation to filled rows in a given column."""
        validation = DataValidation(
            type="textLength",
            formula1=str(min_length),
            formula2=str(max_length),
            allow_blank=True,
        )

        ws.add_data_validation(validation)
        validation.ranges.add(f"{column}2:{column}1001")

    def _generate_file_response(self, workbook):
        """Generate an HTTP response to download the Excel workbook."""
        excel_file_buffer = BytesIO()
        workbook.save(excel_file_buffer)
        excel_file_buffer.seek(0)

        headers = {
            "Content-Disposition": "attachment; filename=action_items_template.xlsx",
            "Content-Type": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            "Access-Control-Allow-Origin": "*",
        }

        return func.HttpResponse(
            excel_file_buffer.read(),
            headers=headers,
            status_code=HTTPStatus.OK,
        )
