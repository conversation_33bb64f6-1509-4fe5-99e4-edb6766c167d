import os
import subprocess
import sys
import venv


def run_command(command: str, env: dict[str, str] | None = None):
    """Run a system command and handle errors."""
    try:
        subprocess.run(command, shell=True, check=True, env=env)
    except subprocess.CalledProcessError as e:
        print(f"Error: Command '{command}' failed with exit code {e.returncode}")
        sys.exit(1)


def create_virtualenv(venv_path: str):
    """Create a virtual environment."""
    if not os.path.exists(venv_path):
        print(f"Creating virtual environment at {venv_path}...")
        venv.create(venv_path, with_pip=True)
    else:
        print("Virtual environment already exists.")


def install_pre_commit(venv_bin_path: str):
    """Install pre-commit inside the virtual environment."""
    print("Installing pre-commit in the virtual environment...")
    run_command(f"{venv_bin_path}pip install pre-commit")


def install_hook(venv_bin_path: str):
    """Install the pre-commit hook."""
    print("Installing pre-commit hook...")
    run_command(f"{venv_bin_path}pre-commit install")


def main():
    # Define virtual environment path
    venv_path = os.path.join(os.getcwd(), ".venv")
    venv_bin = os.path.join(venv_path, "Scripts" if os.name == "nt" else "bin", "")

    create_virtualenv(venv_path)
    install_pre_commit(venv_bin)
    install_hook(venv_bin)
    print("Pre-commit setup complete!")


if __name__ == "__main__":
    main()
