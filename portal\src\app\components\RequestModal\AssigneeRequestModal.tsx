import { Cln<PERSON><PERSON>on, ClnCircularProgress, ClnTextField } from '@celanese/ui-lib'
import { zodResolver } from '@hookform/resolvers/zod'
import { Box, Grid, Typography, Divider, TextField } from '@mui/material'
import dayjs, { Dayjs } from 'dayjs'
import { Controller, SubmitHandler, useForm } from 'react-hook-form'
import { z } from 'zod'
import { useEffect, useMemo, useState } from 'react'
import { UploadFiles } from '../UploadFiles/uploadFile'
import { ActionDetailItem, CategoryConfigurationData } from '@/app/common/models/action-detail'
import { AzureFunctionClient } from '@/app/common/clients/azure-function-client'
import { ActionStatusExternalIdClearEnum } from '@/app/common/enums/ActionItemStatusEnum'
import GenericAutocomplete, { AutocompleteOption } from '../FieldsComponent/GenericAutocomplete'
import { transformOptionsForUser } from '@/app/common/utils/transform-options-for-filter'
import { useUsersSearch } from '@/app/common/hooks/user-management/useUsers'
import * as S from './styles'
import GenericDateRangePicker from '../FieldsComponent/GenericDateRangePicker'
import GenericFieldText from '../FieldsComponent/GenericFieldText'
import { useRouter } from 'next/navigation'
import { buildUploadFilesRequest } from '@/app/common/utils/files'
import { useCognite } from '@/app/common/hooks'
import RemoveItemIconButton from '../ButtonsComponents/RemoveItemIconButton'
import { translate } from '@/app/common/utils/generate-translate'
import { FileSourceStepEnum } from '@/app/common/enums/FileSourceStepEnum'
import { UserRolesPermission } from '@celanese/celanese-sdk'
import { CustomDrawer } from '../ModalComponent/Drawer/Drawer'
import { drawerStyles } from '../ModalComponent/Drawer/styles'
import { SITE_EXTERNAL_ID_REQUIRED_FIELD } from '@/app/common/utils/validate-codes'

interface RequestModalProps {
    action?: ActionDetailItem
    activeUser: UserRolesPermission
    assigneeRequestType?: string
    categoryConfig?: CategoryConfigurationData
    usersByAssigneeRequest?: string[]
    client: AzureFunctionClient | null
    openModal: boolean
    setOpenModal: (value: boolean) => void
    handleAlert: (message: string, error?: boolean) => void
    redirectToLastPage: () => void
    redirectToPage?: () => void
}

const zodDate = z.instanceof(dayjs as unknown as typeof Dayjs).nullable()

interface ActionItem {
    route: string
    hidden: boolean
    disabled?: boolean
    variant: 'text' | 'outlined' | 'contained' | undefined
    label: string
    color?: 'error' | 'primary'
    onClick: () => void
}

const createFormSchema = (
    dueDateExtension: boolean,
    reassignmentPeriod: boolean,
    assigneeRequest: boolean,
    dueDateExtensionAttachmentsRequired: boolean
) =>
    z
        .object({
            comments: z.string().min(10),
            assignee: z.string().optional(),
            newDueDate: zodDate.optional(),
            attachments: z.array(z.any()).optional(),
        })
        .superRefine((values, ctx) => {
            if (assigneeRequest) {
                if (dueDateExtension) {
                    if (
                        dueDateExtensionAttachmentsRequired &&
                        (!values.attachments || values.attachments.length === 0)
                    ) {
                        ctx.addIssue({
                            path: ['attachments'],
                            code: z.ZodIssueCode.custom,
                        })
                    }
                    if (!values.newDueDate) {
                        ctx.addIssue({
                            path: ['newDueDate'],
                            code: z.ZodIssueCode.custom,
                        })
                    }
                } else if (reassignmentPeriod) {
                    if (!values.assignee) {
                        ctx.addIssue({
                            path: ['assignee'],
                            code: z.ZodIssueCode.custom,
                        })
                    }
                }
            }
        })

type RequestSchema = z.infer<ReturnType<typeof createFormSchema>>

export function AssigneeRequestModal({
    action,
    activeUser,
    assigneeRequestType,
    client,
    openModal,
    categoryConfig,
    usersByAssigneeRequest,
    setOpenModal,
    handleAlert,
    redirectToPage,
    redirectToLastPage,
}: RequestModalProps) {
    const router = useRouter()
    const { cogniteClient } = useCognite()

    const formSchema = createFormSchema(
        assigneeRequestType === ActionStatusExternalIdClearEnum.DueDateExtension,
        assigneeRequestType === ActionStatusExternalIdClearEnum.ReassignmentPeriod,
        action?.currentStatus?.externalId === ActionStatusExternalIdClearEnum.Assigned,
        categoryConfig?.isExtensionAttachmentRequired ?? false
    )
    const [loading, setLoading] = useState<boolean>(true)
    const [filesUploaded, setFilesUploaded] = useState<File[]>([])

    const {
        control,
        handleSubmit,
        getValues,
        setValue,
        reset,
        formState: { errors },
    } = useForm<RequestSchema>({
        resolver: zodResolver(formSchema),
        defaultValues: {
            comments: '',
            assignee: '',
            newDueDate: null,
            attachments: [],
        },
    })

    const approvalRequest = action?.currentStatus?.externalId !== ActionStatusExternalIdClearEnum.Assigned

    const changeRequest = useMemo(() => {
        return action?.changeRequests?.find(
            (request) => request.approvalWorkflow && request.approvalWorkflow?.status?.externalId === 'APWST-InProgress'
        )
    }, [action?.changeRequests])

    const label = useMemo(() => {
        return translate(
            action?.currentStatus?.externalId === ActionStatusExternalIdClearEnum.ReassignmentPeriod
                ? 'requestModal.reassignComments'
                : action?.currentStatus?.externalId === ActionStatusExternalIdClearEnum.DueDateExtension
                  ? 'requestModal.extensionComments'
                  : 'requestModal.comments'
        )
    }, [action?.currentStatus?.externalId, translate])

    const getLabelKey = () => {
        if (assigneeRequestType === ActionStatusExternalIdClearEnum.ChallengePeriod) {
            return 'requestModal.challengeComments'
        } else if (action?.currentStatus?.externalId !== ActionStatusExternalIdClearEnum.Assigned) {
            return 'requestModal.comments'
        } else if (assigneeRequestType === ActionStatusExternalIdClearEnum.ReassignmentPeriod) {
            return 'requestModal.reassignComments'
        } else {
            return 'requestModal.extensionComments'
        }
    }

    const changeRequestComments = useMemo(() => {
        return changeRequest?.comments ?? ''
    }, [changeRequest])

    const [usersParam, setUsersParam] = useState<string>('NULL_PARAM')
    const [selectedUserOption, setSelectedUserOption] = useState<AutocompleteOption>()

    const { users: filtredUsers } = useUsersSearch(
        useMemo(() => {
            return usersParam
        }, [usersParam])
    )

    const sortedFiltredUsers = transformOptionsForUser([...filtredUsers], true)

    const sortedAssigneeOptions = Array.from(
        new Map([...sortedFiltredUsers].map((item) => [item.value, item])).values()
    ).sort((a, b) => a.label.localeCompare(b.label))

    const submitFn: (data: RequestSchema, isApproved: boolean, isRequest?: boolean) => Promise<void> = async (
        data,
        isApproved,
        isRequest = false
    ) => {
        try {
            setLoading(true)
            const approverEmail =
                usersByAssigneeRequest && usersByAssigneeRequest.length > 0
                    ? usersByAssigneeRequest
                    : [activeUser.email]
            if (approverEmail.length === 0) {
                handleAlert(translate('alerts.unexpectedErrorOcurred'), true)
                setLoading(false)
                return
            }

            const statusSubmit = isApproved ? 'Approved' : 'Rejected'

            const isDueDateExtension = assigneeRequestType === ActionStatusExternalIdClearEnum.DueDateExtension

            const isReassignmentPeriod = assigneeRequestType === ActionStatusExternalIdClearEnum.ReassignmentPeriod

            let uploadedFileIds = []
            if (data.attachments?.length) {
                const uploadFilesRequest = await buildUploadFilesRequest(
                    cogniteClient,
                    data.attachments,
                    activeUser.displayName,
                    action?.reportingSite?.externalId ?? '',
                    action?.isPrivate,
                    FileSourceStepEnum.ExtensionRequest
                )

                uploadedFileIds = (await client?.uploadFiles(uploadFilesRequest))?.externalIds
            }

            const requestData = {
                externalId: action?.externalId,
                space: action?.space,
                activeUserEmail: activeUser.email,
                reportingSiteExternalId: action?.reportingSite?.externalId ?? SITE_EXTERNAL_ID_REQUIRED_FIELD,
                ...(isRequest
                    ? {
                          ...(isDueDateExtension
                              ? {
                                    assigneeComment: data.comments,
                                    newDueDate: `{"newDueDate": "${dayjs(data.newDueDate).format('YYYY-MM-DD')}"}`,
                                    newFilesIds: uploadedFileIds,
                                }
                              : isReassignmentPeriod
                                ? {
                                      assigneeComment: data.comments,
                                      newAssignee: `{"newAssignee": "${selectedUserOption?.value}", "newAssigneeName": "${selectedUserOption?.label}"}`,
                                  }
                                : {
                                      assigneeComment: data.comments,
                                  }),
                          approverEmail: approverEmail,
                      }
                    : {
                          ...(isDueDateExtension
                              ? {
                                    extensionComment: data.comments,
                                    extensionStatus: statusSubmit,
                                    newDueDate: dayjs(changeRequest?.propertiesToChange?.newDueDate).format(
                                        'YYYY-MM-DD'
                                    ),
                                }
                              : {
                                    reassignComment: data.comments,
                                    reassignStatus: statusSubmit,
                                    newAssignee: changeRequest?.propertiesToChange?.newAssignee,
                                }),
                      }),
            }

            const result = await client?.upsertAssigneeRequest(action?.externalId ?? '-', requestData)
            handleAlert(`${translate('alerts.dataSavedWithSuccess')}: ${result.externalId}`)
            setLoading(false)
            setOpenModal(false)
            reset()
            if (redirectToPage) {
                redirectToPage()
            } else {
                redirectToLastPage()
            }
        } catch (error) {
            handleAlert(translate('alerts.unexpectedErrorOcurred'), true)
        } finally {
            setLoading(false)
        }
    }

    const onSubmitRequest: SubmitHandler<RequestSchema> = (data) => {
        submitFn(data, false, true)
    }

    const onSubmitApprove: SubmitHandler<RequestSchema> = (data) => {
        submitFn(data, true)
    }

    const onSubmitReject: SubmitHandler<RequestSchema> = (data) => {
        submitFn(data, false)
    }

    const handleEditActionClick = (): void => {
        setLoading(true)
        sessionStorage.setItem('challengeCompleted', `${action?.externalId}`)
        router.push(`/action-item/edit/${action?.reportingSite?.siteCode}/${action?.externalId}`)
    }

    const buttons: ActionItem[] = [
        {
            route: '/action-item-assignee-request-cancel',
            hidden: approvalRequest,
            variant: 'text',
            label: translate('requestModal.cancel'),
            onClick: () => {
                reset()
                setOpenModal(false)
            },
        },
        {
            route: '/action-item-assignee-request-save',
            hidden: approvalRequest,
            variant: 'contained',
            label: translate('requestModal.save'),
            onClick: handleSubmit(onSubmitRequest),
        },
        {
            route: '/action-item-assignee-request-reject',
            hidden: !approvalRequest || assigneeRequestType === ActionStatusExternalIdClearEnum.ChallengePeriod,
            variant: 'contained',
            color: 'error',
            label: translate('requestModal.reject'),
            onClick: handleSubmit(onSubmitReject),
        },
        {
            route: '/action-item-assignee-request-approve',
            hidden: !approvalRequest || assigneeRequestType === ActionStatusExternalIdClearEnum.ChallengePeriod,
            variant: 'contained',
            label: translate('requestModal.approve'),
            onClick: handleSubmit(onSubmitApprove),
        },
        {
            route: '/action-item-assignee-request-challenge-edit',
            hidden: !(
                action?.currentStatus?.externalId === ActionStatusExternalIdClearEnum.ChallengePeriod &&
                activeUser.email === action?.owner?.user.email
            ),
            variant: 'contained',
            label: translate('requestModal.editAction'),
            onClick: handleEditActionClick,
        },
    ]

    useEffect(() => {
        setLoading(false)
    }, [action])

    return (
        <CustomDrawer
            title={translate(
                assigneeRequestType === ActionStatusExternalIdClearEnum.ReassignmentPeriod
                    ? 'requestModal.requestReassign'
                    : assigneeRequestType === ActionStatusExternalIdClearEnum.DueDateExtension
                      ? 'requestModal.requestExtension'
                      : 'requestModal.requestChallenge'
            )}
            openDrawer={openModal}
            closeDrawer={() => {
                reset()
                setOpenModal(false)
            }}
            content={
                loading ? (
                    <Box sx={S.loaderContainerStyle}>
                        <ClnCircularProgress size={40} value={0} />
                    </Box>
                ) : (
                    <Box sx={drawerStyles.container}>
                        <Box sx={S.boxContainerStyle}>
                            <Grid container sx={S.gridContainerStyle}>
                                <Grid item sm={6} md={6}>
                                    <GenericFieldText
                                        fieldName={translate('requestModal.action')}
                                        value={action?.externalId ?? ''}
                                    />
                                </Grid>
                                <Grid item sm={6} md={6}>
                                    <GenericFieldText
                                        fieldName={translate('requestModal.assignee')}
                                        value={`${action?.assignedTo?.user?.lastName}, ${action?.assignedTo?.user?.firstName}`}
                                    />
                                </Grid>
                                <Grid item sm={6} md={6}>
                                    <GenericFieldText
                                        fieldName={translate('requestModal.title')}
                                        value={action?.title ?? ''}
                                    />
                                </Grid>
                                {assigneeRequestType !== ActionStatusExternalIdClearEnum.ReassignmentPeriod && (
                                    <Grid item sm={6} md={6}>
                                        <GenericFieldText
                                            fieldName={translate('requestModal.dueDate')}
                                            value={dayjs(action?.dueDate).format('MM/DD/YYYY')}
                                        />
                                    </Grid>
                                )}
                            </Grid>
                            {(action?.currentStatus?.externalId ===
                                ActionStatusExternalIdClearEnum.ReassignmentPeriod ||
                                action?.currentStatus?.externalId ===
                                    ActionStatusExternalIdClearEnum.DueDateExtension ||
                                action?.currentStatus?.externalId ===
                                    ActionStatusExternalIdClearEnum.ChallengePeriod) && (
                                <Grid container sx={S.gridContainerStyle}>
                                    <Grid item md={12}>
                                        <Box sx={S.boxWithFlexCenterStyle}>
                                            <TextField
                                                disabled
                                                label={label}
                                                multiline
                                                rows={3}
                                                defaultValue={changeRequestComments}
                                            />
                                        </Box>
                                    </Grid>
                                    {action?.currentStatus?.externalId !==
                                        ActionStatusExternalIdClearEnum.ChallengePeriod && (
                                        <Grid item md={12} mt={1}>
                                            <Box sx={S.boxWithFlexCenterStyle}>
                                                <ClnTextField
                                                    disabled
                                                    value={
                                                        action?.currentStatus?.externalId ===
                                                        ActionStatusExternalIdClearEnum.ReassignmentPeriod
                                                            ? changeRequest?.propertiesToChange?.newAssigneeName
                                                            : dayjs(
                                                                  changeRequest?.propertiesToChange?.newDueDate
                                                              ).format('MM/DD/YYYY')
                                                    }
                                                    label={translate(
                                                        action?.currentStatus?.externalId ===
                                                            ActionStatusExternalIdClearEnum.ReassignmentPeriod
                                                            ? 'requestModal.newAssignee'
                                                            : 'requestModal.newDueDate'
                                                    )}
                                                    variant="outlined"
                                                    size="small"
                                                    helperText=""
                                                />
                                            </Box>
                                        </Grid>
                                    )}
                                    {action?.currentStatus?.externalId ===
                                        ActionStatusExternalIdClearEnum.DueDateExtension &&
                                        changeRequest?.attatchments &&
                                        changeRequest?.attatchments?.length > 0 && (
                                            <Grid item md={12}>
                                                <UploadFiles
                                                    oldUploadedFiles={changeRequest?.attatchments}
                                                    sxProps={{ paddingTop: '1rem' }}
                                                />
                                            </Grid>
                                        )}
                                </Grid>
                            )}
                            {action?.currentStatus?.externalId !== ActionStatusExternalIdClearEnum.Assigned && (
                                <Divider />
                            )}
                            {action?.currentStatus?.externalId !== ActionStatusExternalIdClearEnum.ChallengePeriod && (
                                <form style={S.formContainerStyle}>
                                    <Box sx={S.boxWithFlexCenterStyle}>
                                        <Controller
                                            control={control}
                                            name="comments"
                                            render={({ field: { value, onChange } }) => (
                                                <ClnTextField
                                                    onChange={(e) => onChange(e.target.value)}
                                                    value={getValues('comments')}
                                                    label={translate(getLabelKey())}
                                                    variant="outlined"
                                                    required
                                                    multiline
                                                    rows={3}
                                                    helperText={translate('stepper.form.helperTextModal')}
                                                    error={Boolean(errors.comments)}
                                                />
                                            )}
                                        />
                                    </Box>
                                    {action?.currentStatus?.externalId === ActionStatusExternalIdClearEnum.Assigned && (
                                        <>
                                            {assigneeRequestType ===
                                                ActionStatusExternalIdClearEnum.DueDateExtension && (
                                                <>
                                                    <Box sx={S.boxWithFlexCenterStyle}>
                                                        <GenericDateRangePicker
                                                            name="newDueDate"
                                                            control={control}
                                                            size="small"
                                                            isRange={false}
                                                            label={translate('requestModal.newDueDate')}
                                                            minDate={dayjs()}
                                                            onError={Boolean(errors.newDueDate)}
                                                        />
                                                        <Typography
                                                            sx={{
                                                                paddingLeft: '1rem',
                                                                fontSize: '0.8rem',
                                                                color: errors.newDueDate
                                                                    ? 'error.dark'
                                                                    : 'text.primary',
                                                            }}
                                                        >
                                                            {translate('requestModal.noteDueDateExtension')}
                                                        </Typography>
                                                    </Box>
                                                    <UploadFiles
                                                        newUploadedFiles={filesUploaded}
                                                        isEditable={true}
                                                        setNewUploadedFiles={(newValue: any) => {
                                                            setFilesUploaded(newValue)
                                                            setValue('attachments', newValue)
                                                        }}
                                                        sxProps={{
                                                            maxHeight: '100px',
                                                            overflowY: 'auto',
                                                            border: '10px',
                                                        }}
                                                        title={
                                                            categoryConfig?.isExtensionAttachmentRequired
                                                                ? translate('requestModal.uploadFilesEvidenceRequired')
                                                                : translate('requestModal.uploadFiles')
                                                        }
                                                        error={Boolean(errors.attachments)}
                                                    />
                                                </>
                                            )}
                                            {assigneeRequestType ===
                                                ActionStatusExternalIdClearEnum.ReassignmentPeriod && (
                                                <GenericAutocomplete
                                                    name="assignee"
                                                    multiple={false}
                                                    valueController={selectedUserOption}
                                                    control={control}
                                                    options={sortedAssigneeOptions}
                                                    onChange={(newValue, fieldOption) => {
                                                        if (newValue) {
                                                            setValue('assignee', newValue)
                                                            setSelectedUserOption(fieldOption)
                                                        } else {
                                                            setValue('assignee', undefined)
                                                            setSelectedUserOption(undefined)
                                                            setUsersParam('NULL_PARAM')
                                                        }
                                                    }}
                                                    size="small"
                                                    placeholderTextField={translate('stepper.form.typeAtLeast3')}
                                                    onInputChange={(event, newInputValue) => {
                                                        if (newInputValue.length >= 3) {
                                                            setUsersParam(newInputValue)
                                                        }
                                                    }}
                                                    disableCloseOnSelect={false}
                                                    error={Boolean(errors.assignee)}
                                                />
                                            )}
                                        </>
                                    )}
                                </form>
                            )}
                        </Box>
                        <Box component={S.ButtonContainerStyle}>
                            {approvalRequest &&
                                assigneeRequestType === ActionStatusExternalIdClearEnum.ChallengePeriod &&
                                action?.currentStatus?.externalId === ActionStatusExternalIdClearEnum.ChallengePeriod &&
                                activeUser.email === action?.owner?.user.email && (
                                    <RemoveItemIconButton
                                        size="small"
                                        actionDetails={action}
                                        light
                                        activeUserEmail={activeUser.email}
                                        handleAlert={handleAlert}
                                        challengeRequest
                                        label={translate('requestModal.cancel')}
                                    />
                                )}
                            {buttons.map(
                                (button) =>
                                    !button.hidden && (
                                        <ClnButton
                                            key={button.route}
                                            size="small"
                                            variant={button.variant}
                                            label={button.label}
                                            onClick={button.onClick}
                                            color={button.color}
                                            sx={{ flex: 1 }}
                                        />
                                    )
                            )}
                        </Box>
                    </Box>
                )
            }
        />
    )
}

export default AssigneeRequestModal
