import { useCallback, useEffect, useState } from 'react'
import { useDebounceFunction } from './useDebounce'
import { AzureFunctionClient } from '../../clients/azure-function-client'

interface UseDebouncedFetchParams<TParam, TResult> {
    fetchFunction: (client: AzureFunctionClient, param: TParam) => Promise<TResult>
    param: TParam
    skipCondition?: (param: TParam) => boolean
    debounceDelay?: number
    onError?: (err: any) => void
}

export const useDebouncedFetch = <TParam, TResult>({
    fetchFunction,
    param,
    skipCondition = () => false,
    debounceDelay = 500,
    onError,
}: UseDebouncedFetchParams<TParam, TResult>) => {
    const [resultData, setResultData] = useState<{
        data: TResult | null
        loading: boolean
    }>({
        data: null,
        loading: false,
    })

    const fetchData = useCallback(async () => {
        if (skipCondition(param)) {
            setResultData({ data: null, loading: false })
            return
        }

        setResultData((prev) => ({ ...prev, loading: true }))
        try {
            const client = new AzureFunctionClient()
            const result = await fetchFunction(client, param)
            setResultData({ data: result, loading: false })
        } catch (err) {
            setResultData({ data: null, loading: false })
            onError?.(err)
        }
    }, [param])

    const debouncedFetch = useCallback(useDebounceFunction(fetchData, debounceDelay), [fetchData])

    useEffect(() => {
        debouncedFetch()
    }, [debouncedFetch])

    return {
        data: resultData.data ?? [],
        loading: resultData.loading,
    }
}
