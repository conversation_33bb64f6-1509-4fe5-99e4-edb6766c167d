import { RecurrenceInst<PERSON>Form } from './action-detail'
import { TemplateConfiguration } from './template-configuration'
import { UpsertApprovalWorkFlow, UpsertBaseWithName } from './upsert/upsertActionItemChangeRequest'
import { NodeOf } from './node-of'
import { UpsertBase } from './upsert/upsertBase'

export interface TemplateRequest {
    externalId: string
    space: string
    title?: string
    currentStatus?: NodeOf<UpsertBase> | null
    application: NodeOf<UpsertBase> | null
    assignedTo?: NodeOf<UpsertBaseWithName>
    reportingUnit?: NodeOf<UpsertBaseWithName | undefined> | null
    reportingLocation?: NodeOf<UpsertBaseWithName | undefined> | null
    reportingLine?: NodeOf<UpsertBaseWithName | undefined> | null
    assignees?: NodeOf<UpsertBaseWithName | undefined>[] | null
    owner?: NodeOf<UpsertBaseWithName | undefined> | null
    approver?: NodeOf<UpsertBaseWithName>
    verifier?: NodeOf<UpsertBaseWithName>
    priority?: string
    category?: NodeOf<UpsertBase>
    subCategory?: NodeOf<UpsertBase>
    siteSpecificCategory?: NodeOf<UpsertBase>
    voeActionItem?: string
    evidenceRequired?: boolean
    sourceInformation?: string
    description?: string
    reportingSite?: NodeOf<UpsertBase>
    createdBy?: NodeOf<UpsertBase>
    actionItemKind?: NodeOf<UpsertBase>
    attachments?: (string | undefined)[]
    approvalWorkflow?: NodeOf<UpsertApprovalWorkFlow>
    recurrenceInstance?: NodeOf<RecurrenceInstanceForm>
    templateConfiguration?: NodeOf<TemplateConfiguration> | null
    isTemplate?: boolean
}
