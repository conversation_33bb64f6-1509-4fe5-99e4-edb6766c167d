import asyncio
from pathlib import Path
from typing import Any, Literal
from typing_extensions import Annotated

from cognite.client import CogniteClient
from cognite.client.data_classes import (
    ClientCredentials,
    DynamicTaskParameters,
    FunctionTaskParameters,
    TransformationTaskParameters,
    WorkflowDefinitionUpsert,
    WorkflowTask,
    WorkflowTriggerList,
    WorkflowTriggerUpsert,
    WorkflowVersionId,
    WorkflowVersionList,
    WorkflowVersionUpsert,
)
from cognite.client.data_classes.workflows import (
    WorkflowScheduledTriggerRule,
    WorkflowUpsert,
)
from pydantic import BaseModel, ConfigDict, BeforeValidator
from pydantic.alias_generators import to_camel

from ..constants import COR_DATA_SET_EXTERNAL_ID

from ..configs import Settings
from ..utils import has_duplicates, is_valid_cron, load_json_async


DEFAULT_MODEL_CONFIG = ConfigDict(
    alias_generator=to_camel,
    populate_by_name=True,
    from_attributes=True,
    extra="ignore",
    arbitrary_types_allowed=True,
)


class WorkflowTaskParameterConfig(BaseModel):
    model_config = DEFAULT_MODEL_CONFIG
    external_id: str = ""
    concurrency_policy: (
        Literal["fail", "restartAfterCurrent", "waitForCurrent"] | None
    ) = None
    data: dict[str, Any] | str | None = None
    tasks: str = ""


def ensure_task_parse(value: Any) -> Any:
    if not isinstance(value, list):
        return value

    return [WorkflowTask.load(v) for v in value]


class WorkflowTaskConfig(BaseModel):
    model_config = DEFAULT_MODEL_CONFIG
    external_id: str
    name: str | None = None
    description: str | None = None
    type: Literal["function", "transformation", "dynamic"]
    on_failure: Literal["abortWorkflow", "skipTask"] = "abortWorkflow"
    retries: int = 3
    timeout: int = 3600
    depends_on: list[str] | None = None
    parameters: WorkflowTaskParameterConfig

    def to_task(self) -> WorkflowTask:
        return WorkflowTask(
            external_id=self.external_id,
            name=self.name,
            description=self.description,
            retries=self.retries,
            timeout=self.timeout,
            on_failure=self.on_failure,
            depends_on=self.depends_on,
            parameters=self.get_parameters(),
        )

    def get_parameters(
        self,
    ) -> FunctionTaskParameters | TransformationTaskParameters | DynamicTaskParameters:
        match self.type:
            case "function":
                return FunctionTaskParameters(
                    external_id=self.parameters.external_id,
                    data=self.parameters.data,
                )
            case "transformation":
                return TransformationTaskParameters(
                    external_id=self.parameters.external_id,
                    concurrency_policy=self.parameters.concurrency_policy,  # type: ignore
                )
            case "dynamic":
                return DynamicTaskParameters(tasks=self.parameters.tasks)


class WorkflowScheduleConfig(BaseModel):
    model_config = DEFAULT_MODEL_CONFIG
    external_id: str
    cron: str
    data: dict[str, Any] | None = None

    def is_valid(self):
        return is_valid_cron(self.cron)


class WorkflowConfig(BaseModel):
    model_config = DEFAULT_MODEL_CONFIG
    external_id: str
    version: str = "1_0_0"
    schedules: list[WorkflowScheduleConfig] | None = None
    description: str | None = None
    tasks: list[WorkflowTask]
    tasks: Annotated[list[WorkflowTask], BeforeValidator(ensure_task_parse)]
    # tasks: list[WorkflowTaskConfig]

    def validate_workflow(
        self, existing_versions: WorkflowVersionList
    ) -> tuple[bool, list[str]]:
        errors = []
        if not validate_workflow_external_id(self.external_id):
            errors.append(f"Workflow {self.external_id} has invalid external_id")

        if not self.tasks:
            errors.append(f"Workflow {self.external_id} must have a tasks")

        if not has_valid_version(self.version):
            errors.append(f"Workflow {self.external_id} has invalid version")

        all_tasks_ids = {t.external_id for t in self.tasks}
        is_depends_on_invalid = any(
            [
                t
                for t in self.tasks
                if not set(t.depends_on or []).issubset(all_tasks_ids)
            ]
        )
        if is_depends_on_invalid:
            errors.append(
                f"Workflow {self.external_id} has tasks which depends on missing tasks"
            )
        existing_version_ids = [
            int(e.version)
            for e in existing_versions
            if e.workflow_external_id == self.external_id
        ]
        if existing_version_ids and max(existing_version_ids) > int(self.version):
            errors.append(
                f"Workflow {self.external_id} already have a newer version deployed"
            )

        tasks_ids = [t.external_id for t in self.tasks]
        if has_duplicates(tasks_ids):
            errors.append(
                f"Duplicated task external ids found in workflow {self.external_id}"
            )

        if self.schedules:
            if any(not s.is_valid() for s in self.schedules):
                errors.append(
                    f"Invalid cron expression for workflow {self.external_id}"
                )
            if has_duplicates([s.external_id for s in self.schedules]):
                errors.append(f"Duplicated schedule id for workflow {self.external_id}")

        return len(errors) == 0, errors

    @property
    def version_id(self):
        return WorkflowVersionId(self.external_id, self.version)

    def to_version_upsert(self) -> WorkflowVersionUpsert:
        return WorkflowVersionUpsert(
            workflow_external_id=self.external_id,
            version=self.version,
            workflow_definition=WorkflowDefinitionUpsert(
                tasks=self.tasks,
                # tasks=[t.to_task() for t in self.tasks],
                description=self.description,
            ),
        )

    def to_triggers_upsert(self) -> list[WorkflowTriggerUpsert]:
        assert self.schedules
        return [
            WorkflowTriggerUpsert(
                external_id=s.external_id,
                workflow_external_id=self.external_id,
                workflow_version=self.version,
                trigger_rule=WorkflowScheduledTriggerRule(cron_expression=s.cron),
            )
            for s in self.schedules
        ]

    def get_all_transformations_ids(self):
        return [
            t.parameters.external_id for t in self.tasks if t.type == "transformation"
        ]

    def get_all_function_ids(self):
        return [t.parameters.external_id for t in self.tasks if t.type == "function"]


def has_valid_version(version: str):
    version_arr = version.split("_")
    return (
        version_arr
        or len(version_arr) != 3
        or any(not v.isdecimal() for v in version_arr)
    )


def validate_workflow_external_id(
    file_name: str, valid_prefixes: list[str] | None = None
) -> bool:
    if not file_name.startswith("wf-"):
        return False
    if not valid_prefixes:
        return True
    file_name_without_wf = file_name.replace("wf-", "")
    return any(
        True
        for prefix in valid_prefixes
        if file_name_without_wf.startswith(prefix + "-")
    )


class WorkflowPublisher:
    def __init__(
        self,
        env_settings: Settings,
        client: CogniteClient,
        directory: str,
        valid_prefixes: list[str],
    ) -> None:
        self._settings = env_settings
        self._client = client
        self._directory = directory
        self._valid_prefixes = valid_prefixes
        self._all_triggers: WorkflowTriggerList = []  # type: ignore

    async def publish_workflows(self, dry_run: bool, force_deploy_ids: list[str]):
        workflow_configs = await self._get_workflow_config_from_dir_async()
        existing_versions = self._get_existing_version()
        existing_workflows = {v.workflow_external_id for v in existing_versions}
        workflows_to_publish = self._get_workflows_to_publish(
            workflow_configs, existing_versions, force_deploy_ids
        )
        workflows_to_delete = self._get_deleted_workflows(
            workflow_configs, existing_workflows
        )
        if not workflows_to_publish and not workflows_to_delete:
            print("Nothing to publish")
            return True, None
        else:
            print(
                f"Worklows to be published: {[w.external_id for w in workflows_to_publish]}",
            )
            print(
                f"Worklows to be deleted: {workflows_to_delete}",
            )

        print("Validating workflows configurations")
        validation_results = [
            w.validate_workflow(existing_versions) for w in workflows_to_publish
        ]
        error_messages = [e for v in validation_results if not v[0] for e in v[1]]
        print("Validating workflow dependencies")
        error_messages.extend(
            self._validate_workflow_dependencies(workflows_to_publish)
        )
        if error_messages:
            return False, error_messages

        print("Workflows validated successfuly!")
        if dry_run:
            return True, None

        for w in workflows_to_publish:
            self._upsert_workflow(w)

        self._delete_missing_triggers(workflows_to_publish)

        if workflows_to_delete:
            self._delete_workflows(workflows_to_delete)
        return True, None

    async def _get_workflow_config_from_dir_async(
        self,
    ):
        print("Loading workflows configs from directory")
        files = [file for file in Path(self._directory).rglob("*.json")]
        assert not any(
            f
            for f in files
            if not validate_workflow_external_id(f.name, self._valid_prefixes)
        ), "Invalid prefix"
        json_files = await asyncio.gather(*[load_json_async(f) for f in files])
        # for j in json_files:
        #     j["tasks"] = (
        #         (WorkflowTask.load(t) for t in j["tasks"])
        #         if "tasks" in j and isinstance(j["tasks"], list)
        #         else j["tasks"]
        #     )
        return [WorkflowConfig(**j) for j in json_files]

    def _get_existing_version(self):
        print("Loading existing versions")
        return self._client.workflows.versions.list(limit=-1)

    def _get_workflows_to_publish(
        self,
        workflow_configs: list[WorkflowConfig],
        existing_versions: WorkflowVersionList,
        force_deploy_ids: list[str],
    ):
        existing_ids = existing_versions.as_ids()
        return [
            w
            for w in workflow_configs
            if w.version_id not in existing_ids or w.external_id in force_deploy_ids
        ]

    def _get_deleted_workflows(
        self,
        workflow_configs: list[WorkflowConfig],
        existing_workflows_ids: set[str],
    ):
        config_ids = [w.external_id for w in workflow_configs]
        return [
            id
            for id in existing_workflows_ids
            if validate_workflow_external_id(id, self._valid_prefixes)
            and id not in config_ids
        ]

    def _upsert_workflow(
        self,
        workflow: WorkflowConfig,
    ):
        assert workflow.tasks, "Workflow must have tasks"
        data_set_id = self._get_data_set_id()
        print(f"Upserting workflow {workflow.external_id}")
        self._client.workflows.upsert(
            WorkflowUpsert(
                external_id=workflow.external_id,
                description=workflow.description,
                data_set_id=data_set_id,
            )
        )
        print(
            f"\tUpserting workflow version {workflow.external_id}, version: {workflow.version}"
        )
        self._client.workflows.versions.upsert(workflow.to_version_upsert())
        if workflow.schedules and self._settings.secret:
            print(f"\tAdding workflow schedule {workflow.external_id}")
            triggers = workflow.to_triggers_upsert()
            for trigger in triggers:
                self._client.workflows.triggers.upsert(
                    workflow_trigger=trigger,
                    client_credentials=ClientCredentials(
                        client_id=self._settings.client_id,
                        client_secret=self._settings.secret,
                    ),
                )

    def _delete_workflows(self, workflow_ids: list[str]):
        print(f"Deleting workflows: {workflow_ids}")
        all_triggers = self._get_triggers()
        workflow_triggers = [
            t.external_id
            for t in all_triggers
            if t.workflow_external_id in workflow_ids
        ]
        self._client.workflows.triggers.delete(external_id=workflow_triggers)
        self._client.workflows.delete(
            external_id=workflow_ids,
            ignore_unknown_ids=True,
        )

    def _delete_missing_triggers(self, workflows_to_publish: list[WorkflowConfig]):
        workflow_ids = {w.external_id for w in workflows_to_publish}
        schedule_ids = {
            s.external_id for w in workflows_to_publish for s in w.schedules or []
        }
        all_triggers = self._get_triggers()
        all_wf_trigger_ids = {
            t.external_id
            for t in all_triggers
            if t.workflow_external_id in workflow_ids
        }
        triggers_to_delete = all_wf_trigger_ids - schedule_ids
        print(f"Deleting triggers: {triggers_to_delete}")
        self._client.workflows.triggers.delete(external_id=list(triggers_to_delete))

    def _get_triggers(self):
        if self._all_triggers:
            return self._all_triggers
        self._all_triggers = self._client.workflows.triggers.list(limit=-1)
        return self._all_triggers

    def _validate_workflow_dependencies(self, workflows: list[WorkflowConfig]):
        transf_map = {w.external_id: w.get_all_transformations_ids() for w in workflows}
        transformation_ids = {
            id for transformations in transf_map.values() for id in transformations
        }
        existing_transformations = self._client.transformations.retrieve_multiple(
            external_ids=list(transformation_ids), ignore_unknown_ids=True
        )

        func_map = {w.external_id: w.get_all_function_ids() for w in workflows}
        function_ids = {id for functions in func_map.values() for id in functions}
        existing_functions = self._client.functions.retrieve_multiple(
            external_ids=list(function_ids), ignore_unknown_ids=True
        )

        validation_errors = []

        existing_transformations_ids = {t.external_id for t in existing_transformations}
        missing_transf = transformation_ids - existing_transformations_ids
        if missing_transf:
            for missing_f in missing_transf:
                dependant_workflows = self._get_transformation_dependent_workflows(
                    transf_map, missing_f
                )
                validation_errors.append(
                    f"Transformation '{missing_f}' required by the workflows '{dependant_workflows}' was not found"
                )

        existing_functions_ids = {f.external_id for f in existing_functions}
        missing_function = function_ids - existing_functions_ids
        if missing_function:
            for missing_f in missing_function:
                dependant_workflows = self._get_function_dependent_workflows(
                    func_map, missing_f
                )
                validation_errors.append(
                    f"Function '{missing_f}' required by workflows {dependant_workflows} was not found"
                )

        return validation_errors

    def _get_transformation_dependent_workflows(
        self, workflows_to_transformations: dict[str, list[str]], transformation: str
    ):
        return [
            workflow_id
            for workflow_id, transf in workflows_to_transformations.items()
            if transformation in transf
        ]

    def _get_function_dependent_workflows(
        self, workflows_to_functions: dict[str, list[str]], function: str
    ):
        return [
            workflow_id
            for workflow_id, functions in workflows_to_functions.items()
            if function in functions
        ]

    def _get_data_set_id(self) -> int:
        return self._client.data_sets.retrieve(external_id=COR_DATA_SET_EXTERNAL_ID).id
