'use client'
import '../../../common/utils/polyfills'
import {
    TranslationContext,
    TranslationContextState,
    UserManagementContext,
    UserManagementContextState,
} from '@celanese/celanese-ui'
import { Box } from '@mui/material'
import { ClnButton, ClnPage, ClnPanel, MatIcon } from '@celanese/ui-lib'
import { useCallback, useContext, useEffect, useMemo, useState } from 'react'
import { APP_NAME } from '@/app/common/utils'
import AuthGuardWrapper from '@/app/common/wrapper/AuthGuardWrapper'
import { useRouter } from 'next/navigation'
import { AzureFunctionClient } from '@/app/common/clients/azure-function-client'
import { EventModalManager } from '@/app/components/CancelEventButton/EventModalManager'
import DetailsTab from '@/app/components/EventsComponent/Details/DetailsTab'
import ActionsTab from '@/app/components/EventsComponent/Details/ActionsTab'
import { useDebounceFunction } from '@/app/common/hooks/general-functions/useDebounce'
import MessageModal from '@/app/components/ModalComponent/Modal/MessageModal'
import { SourceEvent, SourceEventResponse } from '@/app/common/models/source-event'
import DetailsGeneralInfo from '@/app/components/EventsComponent/Details/DetailsGeneralInfo'
import { useRecurrenceTypes } from '@/app/common/hooks/action-item-management/useRecurrenceTypes'
import { CompleteEventModal } from '@/app/components/EventsComponent/Details/CompleteEventModal'
import { BackArrow } from '@/app/common/utils/backArrow'
import { AimTabs } from '@/app/components/Tabs'
import { BulkUploadDrawer } from '@/app/components/HomeComponent/UploadActionsDrawer'
import { useBulkUpload } from '@/app/common/contexts/BulkUploadContext'
import { useSnackbar } from '@/app/common/contexts/SnackbarContext'
import { SourceEventStatusClearEnum } from '@/app/common/enums/SourceEventStatusEnum'
import { AllActionStatusExternalIdEnum } from '@/app/common/enums/ActionItemStatusEnum'
import DetailEditIconButton from '@/app/components/ButtonsComponents/DetailEditIconButton'
import { translate } from '@/app/common/utils/generate-translate'
import { useLoading } from '@/app/common/contexts/LoadingContext'
import RecurrenceEventTab from '@/app/components/EventsComponent/Details/RecurrenceEventTab'
import { UserRolesPermission } from '@celanese/celanese-sdk'
import { UserExternalContext, UserExternalContextState } from '@/app/common/contexts/UserExternalContext'

type Props = {
    params: {
        slug: string[]
    }
}

export default function EventDetailsPage({ params }: Props) {
    const { id, siteId } = useMemo(() => {
        return params.slug?.length === 2
            ? { id: params.slug[1], siteId: `STS-${params.slug[0]}` }
            : { id: params.slug[0], siteId: undefined }
    }, [params])

    const client = new AzureFunctionClient()

    const router = useRouter()

    const { showSnackbar } = useSnackbar()
    const { showLoading } = useLoading()

    const { userInfo } = useContext<UserManagementContextState>(UserManagementContext)
    const { userExternalInfo } = useContext<UserExternalContextState>(UserExternalContext)

    const { locale } = useContext<TranslationContextState>(TranslationContext)
    const { state } = useBulkUpload()

    const [currentTab, setCurrentTab] = useState(0)
    const [sourceEvent, setSourceEvent] = useState<SourceEvent | undefined>(undefined)
    const [isAuthorized, setIsAuthorized] = useState<boolean>(true)

    const [buttonsActions] = useState(localStorage.getItem('isCreateNewActions'))

    const [activeUser, setActiveUser] = useState<UserRolesPermission>()

    const [showForm, setShowForm] = useState<boolean>(false)
    const [isActionDetailsOpen, setIsActionDetailsOpen] = useState<boolean>(false)

    const [openBulkUploadModal, setOpenBulkUploadModal] = useState(false)

    const [isEventModalManagerOpen, setIsEventModalManagerOpen] = useState<boolean>(false)
    const [completeEventModalOpen, setCompleteEventModalOpen] = useState(false)
    const [isCancelModalOpen, setIsCancelModalOpen] = useState<boolean>(false)

    const { allRecurrenceType } = useRecurrenceTypes()

    const tabs = useMemo(
        () => [
            {
                label: translate('source-event.details'),
                auth: 'DetailsEvent',
                content: '',
            },
            {
                label: translate('source-event.action-items'),
                auth: 'DetailsActionsEvent',
                content: '',
            },
            {
                label: translate('source-event.recurringTab.recurring'),
                auth: 'DetailsRecurring',
                content: '',
            },
        ],
        []
    )

    const handleCloseCancelModal = () => setIsCancelModalOpen(false)

    const handleLeaveCancelModal = () => {
        localStorage.removeItem('isEditForm')
        router.back()
        setIsCancelModalOpen(false)
    }

    const handleError = (err: any) => {
        const errorMessage = err instanceof Error ? err.message : `${translate('alerts.unexpectedErrorOcurred')}`
        showSnackbar(`${translate('alerts.unexpectedErrorOcurred')}: ${errorMessage}`, 'error', 'event-details')
    }

    const handleSuccess = (message?: string) =>
        showSnackbar(`${message ?? translate('alerts.dataSavedWithSuccess')}`, 'success', 'event-details')

    const fetchEvent = useCallback(async () => {
        try {
            if (sourceEvent) return

            showLoading(true)

            if (activeUser?.email) {
                const result: SourceEventResponse = await client.getSourceEventById({
                    externalId: id,
                    activeUserEmail: activeUser?.email,
                    reportingSiteExternalId: siteId
                        ? [siteId]
                        : activeUser.applications[0].userSites.map((item) => item.siteId),
                })

                const sourceEventDetail =
                    Array.isArray(result.data) && result.data.length > 0
                        ? result.data[0]
                        : (result.data as SourceEvent | undefined)

                setSourceEvent(sourceEventDetail)
                setIsAuthorized(result.access ?? false)
            } else {
                setIsAuthorized(false)
            }
        } catch (err) {
            handleError(err)
        } finally {
            showLoading(false)
        }
    }, [activeUser])

    const debouncedFetchEvent = useCallback(useDebounceFunction(fetchEvent, 300), [fetchEvent])

    const completeEvent = useCallback(async () => {
        showLoading(true)
        try {
            await client?.updateSourceEventStatusRequest({
                activeUserEmail: activeUser?.email ?? '-',
                reportingSiteExternalId: siteId ?? sourceEvent?.reportingSite?.externalId!,
                externalId: sourceEvent?.externalId,
                space: sourceEvent?.space,
                statusId: SourceEventStatusClearEnum.completed,
            })
            handleSuccess(`${translate('alerts.dataSavedWithSuccess')}`)
            setCompleteEventModalOpen(false)
            fetchEvent()
        } catch (err) {
            handleError(err)
        } finally {
            showLoading(false)
        }
    }, [sourceEvent])

    const renderTab = () => {
        switch (currentTab) {
            case 0:
                return <DetailsTab sourceEvent={sourceEvent} />
            case 1:
                return (
                    <ActionsTab
                        id={id}
                        sourceEvent={sourceEvent}
                        activeUser={activeUser}
                        showForm={showForm}
                        setShowForm={setShowForm}
                        isActionDetailsOpen={isActionDetailsOpen}
                        setIsActionDetailsOpen={setIsActionDetailsOpen}
                        handleError={handleError}
                    />
                )
            case 2:
                return (
                    <RecurrenceEventTab
                        id={id}
                        sourceEvent={sourceEvent}
                        showForm={showForm}
                        setShowForm={setShowForm}
                        recurrenceTypes={allRecurrenceType}
                    />
                )
            default:
                return null
        }
    }

    useEffect(() => {
        if (activeUser?.externalId !== undefined) debouncedFetchEvent()
    }, [fetchEvent])

    useEffect(() => {
        if (
            userExternalInfo.externalId &&
            userExternalInfo.externalId !== '' &&
            (activeUser?.externalId !== userExternalInfo.externalId ||
                activeUser?.applications !== userInfo.applications)
        ) {
            setActiveUser({
                ...userInfo,
                externalId: userExternalInfo.externalId,
                roles: userExternalInfo.roles,
                teams: userExternalInfo.teams,
            })
        }
    }, [userInfo, userExternalInfo])

    useEffect(() => {
        document.title = translate('pages.source-event.details') + ' ' + '|' + ' ' + APP_NAME
    }, [locale])

    return (
        <AuthGuardWrapper
            componentName={
                [1, 2].includes(params.slug.length) && isAuthorized ? EventDetailsPage.name : 'UnknownComponent'
            }
            siteId={siteId ?? sourceEvent?.reportingSite?.externalId}
        >
            <ClnPage>
                <ClnPanel
                    id="event-details-panel"
                    sx={{
                        padding: '1rem',
                        display: 'flex',
                        flexDirection: 'column',
                        marginBottom: '1rem',
                    }}
                >
                    {showForm || isActionDetailsOpen ? null : (
                        <>
                            <Box
                                sx={{
                                    display: 'flex',
                                    alignItems: 'top',
                                    flexDirection: 'row',
                                    justifyContent: 'space-between',
                                }}
                            >
                                <Box
                                    sx={{
                                        display: 'flex',
                                        flexGrow: 1,
                                        flexDirection: 'row',
                                        alignItems: 'center',
                                        gap: '10px',
                                    }}
                                >
                                    {BackArrow(
                                        buttonsActions
                                            ? translate('source-event.create')
                                            : translate('source-event.home'),
                                        () => {
                                            showLoading(true)
                                            router.push('/event-source')
                                        }
                                    )}
                                </Box>
                                {(sourceEvent?.secondaryOwnerUsers?.some(
                                    (item: any) => item?.user?.email === userInfo?.email
                                ) ||
                                    sourceEvent?.owner?.user?.email === userInfo?.email) &&
                                    sourceEvent?.status?.externalId !== AllActionStatusExternalIdEnum.Cancelled && (
                                        <Box
                                            sx={{
                                                display: 'flex',
                                                justifyContent: 'flex-end',
                                                gap: '1rem',
                                            }}
                                        >
                                            {!sourceEvent?.isPrivate &&
                                                (state.isLoading ? (
                                                    <MatIcon
                                                        icon="sync"
                                                        fontSize="30px"
                                                        sx={{
                                                            marginRight: '10px',
                                                            cursor: 'not-allowed',
                                                            opacity: 0.5,
                                                        }}
                                                    />
                                                ) : (
                                                    <MatIcon
                                                        icon="upload_2"
                                                        color="primary.main"
                                                        fontSize="30px"
                                                        sx={{ cursor: 'pointer' }}
                                                        onClick={() => setOpenBulkUploadModal(true)}
                                                    />
                                                ))}
                                            <DetailEditIconButton
                                                id={id}
                                                routerString={`/event-source/edit/${sourceEvent.reportingSite?.siteCode}/`}
                                                hidden={false}
                                            />
                                            {sourceEvent?.status?.externalId !== SourceEventStatusClearEnum.completed &&
                                                sourceEvent?.status?.externalId !==
                                                    SourceEventStatusClearEnum.cancelled && (
                                                    <>
                                                        <ClnButton
                                                            size="small"
                                                            variant="outlined"
                                                            label={translate('source-event.cancelEvent')}
                                                            onClick={() => setIsEventModalManagerOpen(true)}
                                                            color="error"
                                                        />
                                                        <ClnButton
                                                            size="small"
                                                            variant="contained"
                                                            label={translate('requestModal.completeEvent')}
                                                            onClick={() => setCompleteEventModalOpen(true)}
                                                        />
                                                    </>
                                                )}
                                        </Box>
                                    )}
                            </Box>
                            <DetailsGeneralInfo id={id} sourceEvent={sourceEvent} />
                            <AimTabs value={currentTab} tabs={tabs} onChange={(_e, value) => setCurrentTab(value)} />
                        </>
                    )}

                    {renderTab()}

                    <EventModalManager
                        eventId={id}
                        siteId={siteId ?? sourceEvent?.reportingSite?.externalId}
                        eventSpace={sourceEvent?.space}
                        isOpen={isEventModalManagerOpen}
                        onClose={() => setIsEventModalManagerOpen(false)}
                        activeUser={activeUser!}
                    />
                    <MessageModal
                        name=""
                        text={translate('requestModal.closeQuestion')}
                        open={isCancelModalOpen}
                        isCancelModal={true}
                        handleClose={handleCloseCancelModal}
                        handleLeave={handleLeaveCancelModal}
                    />
                    {completeEventModalOpen && (
                        <CompleteEventModal onClose={() => setCompleteEventModalOpen(false)} onSave={completeEvent} />
                    )}
                </ClnPanel>
            </ClnPage>
            {openBulkUploadModal && !state.modalType && (
                <BulkUploadDrawer
                    handleCloseDrawer={() => setOpenBulkUploadModal(false)}
                    eventId={id}
                    activeUser={activeUser!}
                    siteId={siteId ?? sourceEvent?.reportingSite?.externalId}
                />
            )}
        </AuthGuardWrapper>
    )
}
