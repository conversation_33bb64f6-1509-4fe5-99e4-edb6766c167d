import { gql } from '@apollo/client'
import { useEffect, useState } from 'react'
import { useGraphqlQuery } from '../'
import { BusinessLine } from '../../models/common/asset-hierarchy/business-line'

const buildBusinessLineQuery = (): string => {
    const filters: string[] = []

    filters.push(`{ isActive: { eq: true } }`)

    const queryFilter = `{ and: [ ${filters.join(', ')} ] }`

    return `
        query GetBusinessLine {
            listBusinessLine(
                filter: ${queryFilter}
                , first: 1000
                , sort: { description: ASC }
            ) {
                items {
                    externalId
                    name
                  	description
                    space
                }
            }
        }
    `
}

export const useBusinessLines = () => {
    const query = buildBusinessLineQuery()
    const { data: fdmData } = useGraphqlQuery<BusinessLine>(gql(query), 'listBusinessLine', {})

    const [resultData, setResultData] = useState<{ data: BusinessLine[]; loading: boolean }>({
        data: [],
        loading: true,
    })

    useEffect(() => {
        if (fdmData.length == 0) {
            setResultData({ data: [], loading: false })
        } else {
            setResultData({ data: fdmData, loading: false })
        }
    }, [fdmData])

    return {
        loading: resultData.loading,
        businessLine: resultData.data,
    }
}
