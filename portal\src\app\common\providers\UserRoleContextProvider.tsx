import { ReactNode, useCallback, useContext, useEffect, useState } from 'react'
import { AzureFunctionClient } from '../clients/azure-function-client'
import { UserComplementByEmail, UserRolesExternalIds } from '../models/user-role-site'
import { useDebounceFunction } from '../hooks/general-functions/useDebounce'
import { UserManagementContext, UserManagementContextState } from '@celanese/celanese-ui'
import { UserExternalContext } from '../contexts/UserExternalContext'
type UserRoleContextProps = {
    children: ReactNode
}

const MAX_RETRY_ATTEMPTS = 5
const RETRY_COOLDOWN_MS = 500

export const UserRoleContextProvider = ({ children }: UserRoleContextProps) => {
    const { userInfo } = useContext<UserManagementContextState>(UserManagementContext)

    const [retryCount, setRetryCount] = useState<number>(0)

    const [userExternalInfo, setUserExternalInfo] = useState<UserRolesExternalIds>({} as UserRolesExternalIds)

    const retryGetPermission = () => {
        if (retryCount < MAX_RETRY_ATTEMPTS) {
            setTimeout(
                () => {
                    setRetryCount(retryCount + 1)
                },
                Math.pow(2, retryCount) * RETRY_COOLDOWN_MS
            )
        }
    }

    const fetchPermissions = useCallback(async () => {
        try {
            const client = new AzureFunctionClient()

            const additionalInfo = await client.getUserRolesAndTeams({ email: userInfo.email })
            if (!additionalInfo) {
                return retryGetPermission()
            }

            const userComplement: UserComplementByEmail = additionalInfo.data[0]

            setUserExternalInfo({
                externalId: userComplement?.userAzureAttribute?.user?.externalId || '',
                roles: userComplement?.userRoleSite?.map((item: any) => item.role) || [],
                teams:
                    (Array.isArray(userComplement?.userAzureAttribute?.user?.teams)
                        ? userComplement?.userAzureAttribute?.user?.teams
                        : userComplement?.userAzureAttribute?.user?.teams?.items) || [],
            })
        } catch {
            retryGetPermission()
        }
    }, [userInfo?.email, retryCount])

    const debouncedFetchPermissions = useCallback(useDebounceFunction(fetchPermissions, 500), [fetchPermissions])

    useEffect(() => {
        if (userInfo?.email) {
            debouncedFetchPermissions()
        }
    }, [debouncedFetchPermissions])

    return <UserExternalContext.Provider value={{ userExternalInfo }}>{children}</UserExternalContext.Provider>
}
