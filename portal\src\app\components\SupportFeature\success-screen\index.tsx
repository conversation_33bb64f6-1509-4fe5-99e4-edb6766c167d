import CheckCircleIcon from '@mui/icons-material/CheckCircle'
import { Box, Typography } from '@mui/material'
import React, { useState } from 'react'
import { ClnDialog } from '@celanese/ui-lib'
import { translate } from '@celanese/celanese-ui'
import { useSnackbar } from '@/app/common/contexts/SnackbarContext'
import { styles } from '../styles'

interface SuccessScreenProps {
    onBack: () => void
    ticketNumber?: string
}

export const SuccessScreen = ({ onBack, ticketNumber }: SuccessScreenProps) => {
    const [existTicketNumber, setExistTicketNumber] = useState<boolean>(true)
    const { showSnackbar } = useSnackbar()

    const handleClose = () => {
        setExistTicketNumber(false)
    }

    const showAlert = ({ message, isError }: { message: string; isError: boolean }) => {
        showSnackbar(translate(message), isError ? 'error' : 'success', 'support-ticket')
    }

    return (
        <>
            <Box sx={styles.container}>
                <Box sx={styles.iconWrapper}>
                    <CheckCircleIcon sx={styles.checkIcon} />
                    <Box sx={styles.shadow} />
                </Box>
                <Typography sx={styles.message}>{translate('supportFeature.successMessage')}</Typography>
                <Box display="flex" gap="0.25rem" sx={styles.linkWrapper}>
                    <Typography sx={styles.newRequestLink} onClick={onBack}>
                        {translate('supportFeature.newRequestLink')}
                    </Typography>
                    <Typography sx={styles.messageEnd}>{translate('supportFeature.successMessageEnd')}</Typography>
                </Box>
            </Box>
            <ClnDialog
                title={
                    <Typography fontWeight={600} fontSize={'20px'}>
                        {translate('supportFeature.ticketNumber')}
                    </Typography>
                }
                contentText={translate('supportFeature.checkTicketInfo') + ' ' + ticketNumber}
                open={existTicketNumber}
                handleClose={handleClose}
                variantRightButton="contained"
                leftButtonOnClick={handleClose}
                labelRightButton={translate('common.copy')}
                variantLeftButton="text"
                labelLeftButton={translate('common.cancel')}
                rightButtonOnClick={() => {
                    ticketNumber && navigator.clipboard.writeText(ticketNumber)
                    handleClose()
                    showAlert({ message: 'supportFeature.ticketNumberCopied', isError: false })
                }}
            />
        </>
    )
}
