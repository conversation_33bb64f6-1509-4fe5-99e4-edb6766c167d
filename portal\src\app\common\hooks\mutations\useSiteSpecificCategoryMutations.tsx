import { useCallback } from 'react'
import { useFdmDeleter, useFdmMutation } from '..'
import { SiteSpecificCategory } from '../../models/site-specific-category'

export const useSiteSpecificCategoryMutations = () => {
    const [siteSpecificCategoriesMutation, siteSpecificCategoriesMutationStatus] = useFdmMutation<SiteSpecificCategory>(
        'SiteSpecificCategory',
        false
    )
    const [siteSpecificCategoriesDeleter] = useFdmDeleter()

    const createOrUpdateSiteSpecificCategory = useCallback(
        async (siteSpecificCategories: SiteSpecificCategory) => {
            const siteSpecificCategoriesMutationResult = await siteSpecificCategoriesMutation(
                [siteSpecificCategories],
                []
            )

            return {
                ok: siteSpecificCategoriesMutationResult.ok,
                data: siteSpecificCategoriesMutationResult.data,
                error: siteSpecificCategoriesMutationResult.error,
            }
        },
        [siteSpecificCategoriesMutation]
    )

    const deleteSiteSpecificCategories = useCallback(
        async (data: SiteSpecificCategory[]) => {
            const dataToRequest = data.map((x) => x.externalId)

            return await siteSpecificCategoriesDeleter({ externalIds: dataToRequest })
        },
        [siteSpecificCategoriesDeleter]
    )

    return [
        createOrUpdateSiteSpecificCategory,
        deleteSiteSpecificCategories,
        siteSpecificCategoriesMutationStatus,
    ] as const
}
