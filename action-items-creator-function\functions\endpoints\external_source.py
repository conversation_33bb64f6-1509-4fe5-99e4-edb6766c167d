import json
import logging
from http import HTT<PERSON>tatus

import azure.functions as func
from pydantic import ValidationError

from clients.external_source.errors import DataModelQueryError
from clients.external_source.requests import ExternalSourceRequest
from infra.external_source_client_factory import ExternalSourceClientFactory
from services.external_source_service import ExternalSourceService

bp = func.Blueprint()
logging.basicConfig(format="%(message)s", level=logging.INFO)
logger = logging.getLogger(__name__)


@bp.function_name(name="GetExternalSourceDetails")
@bp.route(
    "get-external-source-details",
    methods=["get"],
    auth_level=func.AuthLevel.ANONYMOUS,
)
def main(req: func.HttpRequest) -> func.HttpResponse:
    """Handle GET requests to retrieve external source details based on the provided request parameters."""
    try:
        external_source_request_param = req.params.get("externalSourceRequest", "{}")
        external_source_request = json.loads(external_source_request_param)

        request = ExternalSourceRequest.model_validate(external_source_request)
        service = ExternalSourceService(ExternalSourceClientFactory.retriever())

        details = service.get_external_source_details(request)

        if details is None:
            return func.HttpResponse(
                json.dumps({"error": "Invalid request data"}),
                status_code=HTTPStatus.NOT_FOUND,
                mimetype="application/json",
            )

        return func.HttpResponse(
            details.model_dump_json(by_alias=True),
            status_code=HTTPStatus.OK,
            mimetype="application/json",
        )

    except ValidationError as ve:
        logger.exception("Validation error")
        return func.HttpResponse(
            json.dumps({"error": "Invalid request data", "details": ve.errors()}),
            status_code=HTTPStatus.BAD_REQUEST,
            mimetype="application/json",
        )

    except ValueError as ve:
        logger.exception("Invalid source type or source id")
        return func.HttpResponse(
            json.dumps({"error": "Invalid  or source id", "details": str(ve)}),
            status_code=HTTPStatus.BAD_REQUEST,
            mimetype="application/json",
        )

    except DataModelQueryError as de:
        logger.exception("Data model query error")
        return func.HttpResponse(
            json.dumps({"error": "Failed to retrieve data", "details": str(de)}),
            status_code=HTTPStatus.BAD_GATEWAY,
            mimetype="application/json",
        )

    except Exception as e:
        logger.exception("Unhandled exception occurred")
        return func.HttpResponse(
            json.dumps({"error": "Internal server error", "details": str(e)}),
            status_code=HTTPStatus.INTERNAL_SERVER_ERROR,
            mimetype="application/json",
        )
