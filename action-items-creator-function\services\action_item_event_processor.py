import asyncio
import copy
import os
import sys
from datetime import datetime, timedelta, timezone
from typing import Annotated, Any, Literal
from uuid import uuid4

from annotated_types import Len
from pydantic import Field

script_dir = os.path.dirname(os.path.abspath(__file__))
project_dir = os.path.dirname(script_dir)
sys.path.append(project_dir)
from models.entities_enum import EntitiesEnum
from models.node_reference import NodeReference
from models.settings import Settings
from services.action_item_creator import ActionItemCreator
from services.cognite_service import CogniteService
from services.graphql_service import GraphqlService
from services.logging_service import LoggingService
from utils.general_utils import generate_query
from utils.id_generation_utils import IdGenerator
from utils.list_utils import divide_in_chunks
from utils.space_utils import get_transactional_space_from_site_id


class ReportingUnit(NodeReference):
    reportingSites: list[NodeReference] | None = None


class ActionItemReference(NodeReference):
    name: str
    description: str


class ActionItemEvent(NodeReference):
    eventMetadata: dict[str, Any]
    actionItemReference: ActionItemReference | None = None
    application: NodeReference | None = None
    createdTime: datetime
    reportingSite: NodeReference | None = None
    _is_bypassed: bool = False
    _internal_status: (
        Literal[
            "missingScope",
            "unsupportedSite",
            "bypassedWithErrors",
            "bypassedSuccess",
            "success",
            "withErrors",
            "unmatched",
            "matchedWithNoAssignees",
            "processing",
        ]
        | None
    ) = None
    _target_dataset_id: int | None = None
    _target_space: str | None = None


class ActionItemTriggerField(NodeReference):
    metadataFieldName: str = Field(default_factory=lambda: uuid4().hex)


class ActionItemTriggerCondition(NodeReference):
    metadataField: ActionItemTriggerField
    metadataValue: str = Field(default="---")


class ActionItemTrigger(NodeReference):
    role: NodeReference
    reportingSite: NodeReference
    reportingUnit: ReportingUnit | None = None
    daysUntilDueDate: int
    actionItemReference: ActionItemReference
    application: NodeReference
    conditions: Annotated[list[ActionItemTriggerCondition], Len(min_length=1)]


class ActionItemEventProcessor:
    def __init__(
        self,
        cognite_service: CogniteService,
        graphql_service: GraphqlService,
        action_item_creator_service: ActionItemCreator,
        settings: Settings,
        logging_service: LoggingService,
    ):
        self._graphql_service = graphql_service
        self._cognite_service = cognite_service
        self._action_item_creator_service = action_item_creator_service
        self._log = logging_service
        self._settings = settings
        self._now = datetime.utcnow().replace(tzinfo=timezone.utc)
        self._today = self._now.date()

    async def execute(self):
        self._log.info("Starting action item event processing.")
        events_list: list[ActionItemEvent]
        triggers_list: list[ActionItemTrigger]
        events_list, triggers_list = await asyncio.gather(
            self._get_unprocessed_events(), self._get_triggers()
        )

        if len(events_list) == 0:
            self._log.info("No events to process.")
            await self._graphql_service.cleanup()
            return [], {}

        events: dict[str, ActionItemEvent] = {
            event.externalId: event for event in events_list
        }

        processed_events_to_create = []
        id_generator = IdGenerator[EntitiesEnum](self._now)
        for event_id, event in events.items():
            status = "processing"

            self._log.info(
                f"Event with Id {event_id} was processed. Processing status: {status}."
            )
            processed_event_to_be_created = {
                "externalId": id_generator.next_id(
                    EntitiesEnum.ProcessedActionItemEvent
                ),
                "space": event._target_space
                or self._settings.aim_default_instances_space,
                "actionItemEvent": {
                    "externalId": event_id,
                    "space": event.space,
                },
                "executionId": self._log.execution_id,
                "status": {
                    "externalId": f"{EntitiesEnum.ProcessedActionItemEventStatus.value}-{status}",
                    "space": self._settings.aim_ref_instances_space,
                },
            }

            self._log.info(
                f"Processed event to be created: {processed_event_to_be_created}"
            )

            processed_events_to_create.append(processed_event_to_be_created)

        try:
            await self._upsert_processed_events(
                copy.deepcopy(processed_events_to_create), events, True
            )
        except Exception:
            self._log.error("Internal errors when creating processed events.")

        if len(triggers_list) == 0:
            self._log.info("No triggers to process.")

        triggers_by_external_id = {
            trigger.externalId: trigger for trigger in triggers_list
        }
        triggers_by_air_app_and_site: dict[str, list[ActionItemTrigger]] = {}
        triggers_by_app_and_site: dict[str, list[ActionItemTrigger]] = {}
        for trigger in triggers_list:
            key_air_app_and_site = f"{trigger.actionItemReference.externalId}_{trigger.application.externalId}_{trigger.reportingSite.externalId}"
            if key_air_app_and_site not in triggers_by_air_app_and_site:
                triggers_by_air_app_and_site[key_air_app_and_site] = []

            triggers_by_air_app_and_site[key_air_app_and_site].append(trigger)

            key_app_and_site = (
                f"{trigger.application.externalId}_{trigger.reportingSite.externalId}"
            )
            if key_app_and_site not in triggers_by_app_and_site:
                triggers_by_app_and_site[key_app_and_site] = []

            triggers_by_app_and_site[key_app_and_site].append(trigger)

        action_items_to_create: list[dict[str, Any]] = []
        trigger_to_matched_events: dict[str, list[ActionItemEvent]] = {}

        for _, event in events.items():
            if event.application is None or event.reportingSite is None:
                event._internal_status = "missingScope"
                self._log.info(
                    f"Event does not have an application or reporting site and will be skipped. Event: {event.externalId}."
                )
                continue

            target_space = get_transactional_space_from_site_id(
                event.reportingSite.externalId
            )

            target_dataset_id = self._cognite_service.get_dataset_id(target_space)
            if (
                not self._cognite_service.is_space_valid(target_space)
                or target_dataset_id is None
            ):
                event._internal_status = "unsupportedSite"
                self._log.info(
                    f"Event's reporting site is not supported and event will be skipped. Event: {event.externalId}."
                )
                continue

            event._target_dataset_id = target_dataset_id
            event._target_space = target_space

            event._is_bypassed = (
                event.application.externalId
                not in self._settings.applications_using_triggers
            )

            if (
                event.externalId == "internal"
            ):  # this is a safeguard, as it is used as a key in some places
                event._internal_status = (
                    "bypassedWithErrors" if event._is_bypassed else "withErrors"
                )
                self._log.info(
                    f"Event externalId can't be 'internal'. Event: {event.externalId}."
                )
                continue

            if event._is_bypassed:
                action_items_to_create.append(
                    {
                        "eventId": event.externalId,
                        "applicationId": event.application.externalId,
                        "reportingSiteId": event.reportingSite.externalId,
                        **event.eventMetadata,
                        **(
                            {
                                "title": event.actionItemReference.name,
                                "description": event.actionItemReference.description,
                            }
                            if event.actionItemReference is not None
                            else {}
                        ),
                        "assignmentDate": event.eventMetadata.get(
                            "assignmentDate", self._today.isoformat()
                        ),
                        "target_dataset_id": event._target_dataset_id,
                    }
                )
                continue

            key = (
                f"{event.actionItemReference.externalId}_"
                if event.actionItemReference is not None
                else ""
            ) + (f"{event.application.externalId}_{event.reportingSite.externalId}")

            triggers = (
                triggers_by_air_app_and_site.get(key)
                if event.actionItemReference is not None
                else triggers_by_app_and_site.get(key)
            )
            if triggers is None:
                event._internal_status = "unmatched"
                self._log.info(
                    f"Event does not match any trigger for this action item reference, application and reporting site. Event: {event.externalId}."
                )
                continue

            was_matched = False
            for trigger in triggers:
                event_should_be_processed_based_on_metadata_fields = all(
                    condition.metadataField.metadataFieldName in event.eventMetadata
                    and event.eventMetadata[condition.metadataField.metadataFieldName]
                    == condition.metadataValue
                    for condition in trigger.conditions
                )

                if not event_should_be_processed_based_on_metadata_fields:
                    self._log.info(
                        f"Metadata fields do not match. Trigger: {trigger.externalId}. Event: {event.externalId}."
                    )
                    continue

                trigger_site_matches_event_site = (
                    trigger.reportingSite.externalId == event.reportingSite.externalId
                )

                if not trigger_site_matches_event_site:
                    self._log.info(
                        f"Trigger's reporting site does not match event's reporting site. Trigger: {trigger.externalId}. Event: {event.externalId}."
                    )
                    continue

                trigger_unit_is_filled = trigger.reportingUnit is not None

                trigger_unit_reporting_sites = (
                    {
                        site.externalId: site.space
                        for site in trigger.reportingUnit.reportingSites
                    }
                    if trigger.reportingUnit is not None
                    and trigger.reportingUnit.reportingSites is not None
                    else {}
                )

                trigger_unit_matches_event_reporting_site = (
                    event.reportingSite.externalId in trigger_unit_reporting_sites
                )

                event_should_be_processed_based_on_unit_and_site = (
                    not trigger_unit_is_filled
                    or trigger_unit_matches_event_reporting_site
                )

                if not event_should_be_processed_based_on_unit_and_site:
                    self._log.info(
                        f"Trigger's unit does not match event's site. Trigger: {trigger.externalId}. Event: {event.externalId}."
                    )
                    continue

                if trigger.externalId not in trigger_to_matched_events:
                    trigger_to_matched_events[trigger.externalId] = []

                self._log.info(
                    f"Event matches trigger. Trigger: {trigger.externalId}. Event: {event.externalId}."
                )
                was_matched = True
                trigger_to_matched_events[trigger.externalId].append(event)

            if not was_matched:
                event._internal_status = "unmatched"
                self._log.info(
                    f"Event does not match any trigger. Event: {event.externalId}."
                )

        events_matched_with_no_assignees = {}
        events_matched_with_assignees = {}
        for trigger_id, matched_events in trigger_to_matched_events.items():
            trigger = triggers_by_external_id[trigger_id]
            assignees_ids = await self._get_assignees_ids(
                trigger.role.externalId,
                trigger.reportingSite.externalId,
                (
                    trigger.reportingUnit.externalId
                    if trigger.reportingUnit is not None
                    else None
                ),
            )
            if len(assignees_ids) == 0:
                for matched_event in matched_events:
                    events_matched_with_no_assignees[matched_event.externalId] = (
                        matched_event.space
                    )
                self._log.info(
                    f"No assignees found for this trigger (role + site + unit). Trigger: {trigger_id}. Matched events: {', '.join([event.externalId for event in matched_events])}"
                )
                continue
            for matched_event in matched_events:
                events_matched_with_assignees[matched_event.externalId] = (
                    matched_event.space
                )
                action_item_kind_id = matched_event.eventMetadata.get(
                    "actionItemKindId"
                )
                dueDate = (
                    (self._today + timedelta(days=trigger.daysUntilDueDate)).isoformat()
                    if action_item_kind_id is None
                    or action_item_kind_id == "ACTK-oneTime"
                    else None
                )
                action_item_initial_fields = {
                    "eventId": matched_event.externalId,
                    "dueDate": dueDate,
                    "assignmentDate": self._today.isoformat(),
                    "title": trigger.actionItemReference.name,
                    "description": trigger.actionItemReference.description,
                    "assignedToIds": assignees_ids,
                    "applicationId": trigger.application.externalId,
                    "reportingSiteId": trigger.reportingSite.externalId,
                    "reportingUnitId": (
                        trigger.reportingUnit.externalId
                        if trigger.reportingUnit is not None
                        else matched_event.eventMetadata.get("reportingUnitId")
                    ),
                    "target_dataset_id": matched_event._target_dataset_id,
                }
                action_items_to_create.append(
                    {**matched_event.eventMetadata, **action_item_initial_fields}
                )

        for event_id in events_matched_with_no_assignees.keys():
            if event_id in events_matched_with_assignees:
                # one event can be matched with multiple triggers, one having assignees and others not
                continue

            events[event_id]._internal_status = "matchedWithNoAssignees"

        error_global = False
        try:
            _, errors_by_event_id, _ = await self._action_item_creator_service.execute(
                action_items_to_create
            )
        except Exception:
            error_global = True
            self._log.error(
                "Validation errors when creating action items, some events will not generate action items."
            )

        if "internal" in errors_by_event_id:
            self._log.error(
                f"Internal errors when creating action items. Events {', '.join([event_id for event_id in events.keys()])} will not be marked as processed. Errors: {errors_by_event_id}"
            )
            return [], errors_by_event_id

        if any([len(errors) > 0 for errors in errors_by_event_id]):
            errors_to_print = {
                event_id: errors
                for event_id, errors in errors_by_event_id.items()
                if len(errors) > 0
            }
            self._log.error(
                f"Validation errors when creating action items, some events will not generate action items. Errors by event Id: {errors_to_print}"
            )

        id_generator = IdGenerator[EntitiesEnum](self._now)
        for event_id, event in events.items():
            processed_event = next(
                (
                    pe
                    for pe in processed_events_to_create
                    if pe["actionItemEvent"]["externalId"] == event_id
                ),
                None,
            )

            status = (
                "bypassedWithErrors"
                if error_global
                else (
                    (
                        ("bypassedSuccess" if event._is_bypassed else "success")
                        if len(errors_by_event_id[event_id]) == 0
                        else (
                            "bypassedWithErrors" if event._is_bypassed else "withErrors"
                        )
                    )
                    if event._internal_status is None
                    else event._internal_status
                )
            )

            self._log.info(
                f"Event with Id {event_id} was processed. Processing status: {status}."
            )
            if processed_event:
                processed_event["processedAt"] = self._now.replace(
                    microsecond=0
                ).isoformat()
                processed_event["status"][
                    "externalId"
                ] = f"{EntitiesEnum.ProcessedActionItemEventStatus.value}-{status}"
                self._log.info(f"Updated processed event: {processed_event}")
            else:
                processed_event_to_be_created = {
                    "externalId": id_generator.next_id(
                        EntitiesEnum.ProcessedActionItemEvent
                    ),
                    "space": event._target_space
                    or self._settings.aim_default_instances_space,
                    "actionItemEvent": {
                        "externalId": event_id,
                        "space": event.space,
                    },
                    "executionId": self._log.execution_id,
                    "processedAt": self._now.replace(microsecond=0).isoformat(),
                    "status": {
                        "externalId": f"{EntitiesEnum.ProcessedActionItemEventStatus.value}-{status}",
                        "space": self._settings.aim_ref_instances_space,
                    },
                }

                self._log.info(
                    f"Created new processed event: {processed_event_to_be_created}"
                )
                processed_events_to_create.append(processed_event_to_be_created)

        try:
            await self._upsert_processed_events(
                processed_events_to_create, events, False
            )
        except Exception:
            self._log.error("Internal errors when creating processed events.")

        self._log.info("Action item event processing finished successfully.")

        return processed_events_to_create, {}

    async def _upsert_processed_events(
        self,
        processed_events_to_create: list,
        events: dict[str, ActionItemEvent],
        new_processed: bool,
    ):
        retry_count = 0
        MAX_RETRY_COUNT = 5
        while retry_count < MAX_RETRY_COUNT:  # this is a safeguard
            (
                _,
                upsert_processed_events_errors,
            ) = self._cognite_service.upsert_processed_events(
                processed_events_to_create, new_processed
            )
            there_are_no_errors = len(upsert_processed_events_errors) == 0
            if there_are_no_errors:
                break

            retry_count += 1
            if retry_count == MAX_RETRY_COUNT:  # worst case scenario
                self._log.error(
                    f"Internal errors when creating processed events. Max retry count reached ({retry_count}). Events {', '.join([event_id for event_id in events.keys()])} may not be marked as processed even though they were. Errors: {upsert_processed_events_errors}"
                )
                return [], {"internal": upsert_processed_events_errors}

            self._log.error(
                f"Internal errors when creating processed events. Retry count: {retry_count}. Errors: {upsert_processed_events_errors}"
            )
            await asyncio.sleep(retry_count)

    async def _get_assignees_ids(
        self,
        role_external_id: str,
        site_external_id: str,
        unit_external_id: str | None = None,
    ) -> list[str]:
        if role_external_id is None or site_external_id is None:
            return []

        query_filter = {
            "and": [
                {"role": {"externalId": {"eq": role_external_id}}},
                {"reportingSite": {"externalId": {"eq": site_external_id}}},
            ],
        }

        user_role_sites = await self._graphql_service.get_all_results_list(
            generate_query(get_assinees_list_name, get_assinees_item_selection),
            get_assinees_list_name,
            query_filter,
        )

        no_user_role_site_matches_role_and_site = len(user_role_sites) == 0
        if no_user_role_site_matches_role_and_site:
            return []

        users_with_role_in_site = user_role_sites[0]["usersComplements"]

        no_users_with_role_in_site = len(users_with_role_in_site) == 0
        if no_users_with_role_in_site:
            return []

        if unit_external_id is None:
            return [
                user["userAzureAttribute"]["externalId"]
                for user in users_with_role_in_site
            ]

        users_with_role_in_unit = list(
            filter(
                lambda user: any(
                    [
                        user_unit["externalId"] == unit_external_id
                        for user_unit in user["reportingUnits"]
                    ]
                ),
                users_with_role_in_site,
            )
        )

        return [
            user["userAzureAttribute"]["externalId"] for user in users_with_role_in_unit
        ]

    async def _get_unprocessed_events(self) -> list[ActionItemEvent]:
        self._log.info(
            f"Getting unprocessed events from {self._now - timedelta(hours=12)} to {self._now}."
        )
        last_events_filter_list = [
            {
                "createdTime": {
                    "gte": (self._now - timedelta(hours=12))
                    .replace(microsecond=0)
                    .isoformat()
                }
            }
        ]

        if (
            self._settings.applications_to_ignore_events
            and len(self._settings.applications_to_ignore_events) > 0
        ):
            last_events_filter_list.append(
                {
                    "application": {
                        "not": {
                            "externalId": {
                                "in": self._settings.applications_to_ignore_events
                            }
                        }
                    }
                }
            )

        # NOTE: comment filter above and uncomment filter below to process only events from specific applications (usually local)
        # last_events_filter_list.append(
        #     {"application": {"externalId": {"in": ["APP-ICAP"]}}}
        # )

        last_events_filter = {"and": last_events_filter_list}

        last_events = await self._graphql_service.get_all_results_list(
            generate_query(get_events_list_name, get_events_item_selection),
            get_events_list_name,
            last_events_filter,
        )

        if len(last_events) == 0:
            self._log.info("No events from last day.")
            return []

        unprocessed_events = []
        for last_events_chunk in divide_in_chunks(last_events, 5000):
            processed_events_filter = {
                "actionItemEvent": {
                    "externalId": {
                        "in": list(event["externalId"] for event in last_events_chunk),
                    },
                },
            }
            processed_events = await self._graphql_service.get_results_list(
                generate_query(
                    get_processed_events_list_name, get_processed_events_item_selection
                ),
                get_processed_events_list_name,
                processed_events_filter,
            )

            processed_events_map = {
                processed_event["actionItemEvent"]["externalId"]: processed_event[
                    "actionItemEvent"
                ]
                for processed_event in processed_events
                if processed_event["actionItemEvent"] is not None
            }

            unprocessed_events.extend(
                event
                for event in last_events_chunk
                if event["externalId"] not in processed_events_map
                and event.get("eventMetadata") is not None
            )

        return [
            ActionItemEvent(**unprocessed_event_dict)
            for unprocessed_event_dict in unprocessed_events
        ]

    async def _get_triggers(self) -> list[ActionItemTrigger]:
        valid_triggers = []
        for trigger_dict in await self._graphql_service.get_all_results_list(
            generate_query(get_triggers_list_name, get_triggers_item_selection),
            get_triggers_list_name,
        ):
            try:
                parsed_trigger = ActionItemTrigger(**trigger_dict)
                valid_triggers.append(parsed_trigger)
            except Exception as e:
                self._log.error(
                    f"Error parsing trigger {trigger_dict.get('externalId')}: {e}"
                )
                continue

        return valid_triggers


get_events_list_name = "listActionItemEvent"

get_events_item_selection = """
    externalId
    space
    eventMetadata
    reportingSite {
        externalId
        space
    }
    actionItemReference {
        externalId
        space
        name
        description
    }
    application {
        externalId
        space
    }
    createdTime
"""

get_processed_events_list_name = "listProcessedActionItemEvent"

get_processed_events_item_selection = """
    actionItemEvent {
        externalId
        space
    }
"""


get_triggers_list_name = "listActionItemTrigger"

get_triggers_item_selection = """
    externalId
    space
    role {
        externalId
        space
    }
    reportingUnit {
        externalId
        space
        reportingSites {
            items {
                externalId
                space
            }
        }
    }
    reportingSite {
        externalId
        space
    }
    daysUntilDueDate
    actionItemReference {
        externalId
        space
        name
        description
    }
    application {
        externalId
        space
    }
    conditions {
        items {
            externalId
            space
            metadataField {
                externalId
                space
                metadataFieldName
            }
            metadataValue
        }
    }
"""


get_assinees_list_name = "listUserRoleSite"

get_assinees_item_selection = """
    externalId
    space
    usersComplements {
        items {
            externalId
            space
            userAzureAttribute {
                externalId
                space
            }
            reportingUnits {
                items {
                    externalId
                    space
                }
            }
        }
    }
"""
