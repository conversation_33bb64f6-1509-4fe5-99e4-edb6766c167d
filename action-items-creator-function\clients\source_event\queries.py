GET_SOURCE_EVENT_QUERY = """
query GetSourceEvent($cursor: String, $pageSize: Int, $sourceEventFilter: _ListSourceEventFilter, $sorting: _SourceEventSort!, $actionsFilter: _ListActionFilter) {
  listSourceEvent(after: $cursor, first: $pageSize, filter: $sourceEventFilter, sort: [$sorting]) {
    items {
      externalId
      space
      title
      description
      status {
        externalId
        space
        name
      }
      reportingSite {
        externalId
        space
        siteCode
        name
        description
      }
      reportingUnit {
        externalId
        space
        name
        description
      }
      reportingUnits {
        items {
          externalId
          space
          name
          description
        }
      }
      reportingLine{
        externalId
        space
        name
        description
      }
      category {
        externalId
        space
        name
      }
      subCategory {
        externalId
        space
        name
      }
      siteSpecificCategory {
        externalId
        space
        name
      }
      owner {
        externalId
        space
        user {
          externalId
          space
          lastName
          firstName
          email
        }
      }
      assignmentDate
      dueDate
      displayDueDate
      isPrivate
      actions(filter: $actionsFilter, first: 1000) {
        items {
          externalId
          space
          isPrivate
          viewUsers {
            items {
              externalId
              space
            }
          }
          viewRoles {
            items {
              externalId
              space
            }
          }
          viewTeams {
            items {
              externalId
              space
            }
          }
          assignedTo {
            externalId
            space
          }
          owner {
            externalId
            space
          }
          approvalWorkflow {
            externalId
            space
            steps(first: 1000) {
              items {
                externalId
                space
                users(first: 1) {
                  items {
                    externalId
                    space
                  }
                }
              }
            }
          }
        }
      }
      views
      equipments {
        items {
          externalId
          space
          name
          description
          number
        }
      }
      functionalLocations {
        items {
          externalId
          space
          name
          description
        }
      }
      attachments {
        externalId
        name
        mimeType
        uploadedTime
        metadata
        source
      }
    }
    pageInfo {
      endCursor
      hasNextPage
    }
  }
}
"""


GET_SOURCE_EVENT_BY_ID_QUERY = """
query GetSourceEvent($cursor: String, $pageSize: Int, $sourceEventFilter: _ListSourceEventFilter, $sorting: _SourceEventSort!) {
  listSourceEvent(after: $cursor, first: $pageSize, filter: $sourceEventFilter, sort: [$sorting]) {
    items {
      externalId
      space
      title
      description
      status {
        externalId
        space
        name
      }
      reportingSite {
        externalId
        space
        siteCode
        name
        description
      }
      reportingUnit {
        externalId
        space
        name
        description
      }
      reportingUnits {
        items {
          externalId
          space
          name
          description
        }
      }
      reportingLine{
        externalId
        space
        name
        description
      }
      category {
        externalId
        space
        name
      }
      subCategory {
        externalId
        space
        name
      }
      siteSpecificCategory {
        externalId
        space
        name
      }
      assignmentDate
      dueDate
      displayDueDate
      isPrivate
      reportingLocation {
        externalId
        space
        name
        description
      }
      impactedReportingLocations {
        items {
          externalId
          space
          name
          description
        }
      }
      businessLine {
        externalId
        space
        name
        description
      }
      attachments {
        externalId
        name
        mimeType
        uploadedTime
        metadata
        source
      }
      owner {
        externalId
        space
        user {
          externalId
          space
          lastName
          firstName
          email
        }
      }
      secondaryOwnerUsers {
        items {
          externalId
          space
          user {
            externalId
            space
            lastName
            firstName
            email
          }
        }
      }
      secondaryOwnerRoles {
        items {
          externalId
          space
          name
        }
      }
      secondaryOwnerTeams {
        items {
          externalId
          space
          name
        }
      }
      actionsCount
      viewUsers {
        items {
          externalId
          space
          user {
            externalId
            space
            lastName
            firstName
            email
          }
        }
      }
      viewRoles {
        items {
          externalId
          space
          name
        }
      }
      viewTeams {
        items {
          externalId
          space
          name
        }
      }
      views
      equipments {
        items {
          externalId
          space
          name
          description
          number
        }
      }
      functionalLocations {
        items {
          externalId
          space
          name
          description
        }
      }
    }
    pageInfo {
      endCursor
      hasNextPage
    }
  }
}
"""
