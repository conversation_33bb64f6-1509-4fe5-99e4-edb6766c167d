import { Dayjs } from 'dayjs'

export const determineActiveFilterCount = (filterActions: any, isDueDateArray: boolean = false) => {
    if (!filterActions) {
        return 0
    }

    let count = 0
    let dueDateFilterCounted = false

    Object.entries(filterActions)?.forEach(([key, value]) => {
        if ((Array.isArray(value) && value.length === 0) || value === undefined) {
            return
        }

        if (key === 'onlyPrivate') {
            if (filterActions.onlyPrivate === undefined) return
            else {
                count++
                return
            }
        }

        if (key === 'dueDateGte' || key === 'dueDateLt') {
            if (dueDateFilterCounted) return
            if (filterActions.dueDateGte || filterActions.dueDateLt) {
                count++
                dueDateFilterCounted = true
            }
            return
        }

        if (isDueDateArray && key === 'dueDate') {
            const dueDateArray = value as (string | Dayjs | null | undefined)[]
            if (
                dueDateArray &&
                ((dueDateArray[0] !== null && dueDateArray[0] !== undefined) ||
                    (dueDateArray[1] !== null && dueDateArray[1] !== undefined))
            ) {
                count++
            }
            return
        }

        if (value) {
            count++
        }
    })

    return count
}
