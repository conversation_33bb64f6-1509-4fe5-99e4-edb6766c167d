import { Box, FormHelperText, Grid, Step, <PERSON><PERSON><PERSON><PERSON>, Stepper, styled, Typography, useTheme } from '@mui/material'
import { z } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'
import { ActionDetailItem, Attachment, CategoryConfigurationData } from '@/app/common/models/action-detail'
import { useForm } from 'react-hook-form'
import { PREFIX_USER_AZURE_ATTRIBUTE } from '@/app/common/utils'
import { useCallback, useEffect, useMemo, useState } from 'react'
import { translate } from '@/app/common/utils/generate-translate'
import LinksComponent from '../LinksComponent'
import GenericTextField from '../FieldsComponent/GenericTextField'
import GenericAutocomplete, { AutocompleteOption } from '../FieldsComponent/GenericAutocomplete'
import { useUsersSearch } from '@/app/common/hooks/user-management/useUsers'
import {
    transformOptions,
    transformOptionsForUser,
    transformStringOptions,
} from '@/app/common/utils/transform-options-for-filter'
import { useSnackbar } from '@/app/common/contexts/SnackbarContext'
import { useLoading } from '@/app/common/contexts/LoadingContext'
import { useReportingUnits } from '@/app/common/hooks/asset-hierarchy/useReportingUnits'
import { LocationQueryRequest, useReportingLocations } from '@/app/common/hooks/asset-hierarchy/useReportingLocations'
import { LineQueryRequest, useReportingLines } from '@/app/common/hooks/asset-hierarchy/useReportingLines'
import GenericFieldTitle from '../FieldsComponent/GenericFieldTitle'
import { ClnButton, ClnRadio, ClnToggle } from '@celanese/ui-lib'
import { useActionItemCategories } from '@/app/common/hooks/action-item-management/useActionItemCategories'
import { useActionItemSubCategories } from '@/app/common/hooks/action-item-management/useActionItemSubCategories'
import { UploadFiles } from '../UploadFiles/uploadFile'
import dayjs, { Dayjs } from 'dayjs'
import isSameOrAfter from 'dayjs/plugin/isSameOrAfter'

import { GenericDatePicker } from '../FieldsComponent/GenericDatePicker'
import { RecurrenceDrawer } from './RecurrenceDrawer'
import { RecurrenceForm } from '@/app/common/models/forms/recurrence-form'
import PrivateComponent from '../PrivateComponent/PrivateComponent'
import { User } from '@/app/common/models/common/user-management/user'
import { Role } from '@/app/common/models/common/user-management/role'
import { Team } from '@/app/common/models/common/user-management/team'
import { Viewers } from '../UserComponent/privateSettingsModal'
import { ActionItemKindExternalIdEnum } from '@/app/common/enums/ActionItemKindEnum'
import { NotificationSubscribers } from '@/app/common/models/notification-subscribers'
import SendToComponent from '../UserComponent/SendTo/sendTo'
import { UserComplement } from '@/app/common/models/common/user-management/user-complement'
import { PriorityEnum } from '@/app/common/enums/PriorityEnum'
import { ActionItemCategoryExternalIdEnum } from '@/app/common/enums/ActionItemCategoryEnum'
import { AzureFunctionClient } from '@/app/common/clients/azure-function-client'
import { formatDate } from '@/app/common/utils/date'
import { formatRecurrence } from '@/app/common/utils/format-Recurrence'
import { mapExternalId } from '@/app/common/utils/map-externalId'
import { buildUploadFilesRequest } from '@/app/common/utils/files'
import { useCognite } from '@/app/common/hooks'
import { environment } from '@/app/common/configurations/environment'
import { useRouter } from 'next/navigation'
import { getConditionalValue, getUpdatedValue, removeUndefinedValues } from '@/app/common/utils/transform-input'
import { ActionStatusExternalIdClearEnum, AllActionStatusExternalIdEnum } from '@/app/common/enums/ActionItemStatusEnum'
import { SITE_EXTERNAL_ID_REQUIRED_FIELD, UNIT_MANAGER_ROLE_WAS } from '@/app/common/utils/validate-codes'
import { FormFieldNameEnum } from '@/app/common/enums/FormFieldNameEnum'

import * as styles from './styles'
import { NewTemplateModal } from './NewTemplateModal'
import { useDebounceFunction } from '@/app/common/hooks/general-functions/useDebounce'
import { ActionSourceTypeExternalIdEnum } from '@/app/common/enums/ActionSourceTypeEnum'
import timezone from 'dayjs/plugin/timezone'
import utc from 'dayjs/plugin/utc'
import { createFormSchema, formSchema } from './formSchema'
import { RecurrenceTypeEnum } from '@/app/common/enums/RecurrenceTypeEnum'
import { RecurrenceSubTypeEnum } from '@/app/common/enums/RecurrenceSubTypeEnum'
import { formatLinksToRequest, getLinks } from '@/app/common/utils/format-links-to-request'
import { formatAttachmentExternalIdToRequest } from '@/app/common/utils/format-attachmants-to-request'
import { ActionItemLink } from '@/app/common/models/link'
import { NewActionItemRequest, TaskTypeRelatedProps } from '@/app/common/models/new-action-item-request'
import { FileSourceStepEnum } from '@/app/common/enums/FileSourceStepEnum'
import { UserRolesPermission } from '@celanese/celanese-sdk'
import MessageModal from '../ModalComponent/Modal/MessageModal'
import { useSiteSpecificCategories } from '@/app/common/hooks/action-item-management/useSiteSpecificCategories'

dayjs.extend(isSameOrAfter)
dayjs.extend(utc)
dayjs.extend(timezone)

const Form = styled('form')({ paddingTop: '1rem', width: '100%' })
export type ActionFormSchema = z.infer<typeof formSchema>

type ActionItemFormsProps = {
    siteId: string
    pageTitle?: string
    actionItem?: ActionDetailItem
    activeUser: UserRolesPermission
    isMasterAdmin?: boolean
    eventId?: string
    defaultValues?: Partial<ActionFormSchema>
    fromEdit?: boolean
    clickLeaveCancelModal?: () => void
    finishSteppedFunction?: (value: any, success: boolean) => void
}
export function ActionItemForms({
    siteId,
    pageTitle,
    actionItem,
    activeUser,
    isMasterAdmin,
    eventId,
    defaultValues,
    fromEdit = false,
    clickLeaveCancelModal,
    finishSteppedFunction,
}: ActionItemFormsProps) {
    const theme = useTheme()
    const router = useRouter()

    const { cogniteClient } = useCognite()
    const azureFunctionClient = new AzureFunctionClient()

    const { showSnackbar } = useSnackbar()
    const { showLoading } = useLoading()

    const [isCancelModalOpen, setIsCancelModalOpen] = useState(false)

    const [activeStep, setActiveStep] = useState<number>(0)
    const steps = [
        translate('stepper.form.general'),
        translate('stepper.form.source.name'),
        translate('stepper.form.assignment.name'),
    ]

    const newSchema = createFormSchema(
        activeStep,
        siteId,
        fromEdit ?? false,
        !actionItem?.isTemplate && !actionItem?.recurrenceInstance
    )
    const {
        control,
        setValue,
        getValues,
        trigger,
        reset,
        watch,
        formState: { errors },
    } = useForm<ActionFormSchema>({
        resolver: zodResolver(newSchema),
        defaultValues: {
            reportingSite: siteId,
            sourceInformation: defaultValues?.sourceInformation ?? undefined,
            category:
                defaultValues?.category ?? actionItem?.category?.externalId ?? ActionItemCategoryExternalIdEnum.General,
            subCategory1: defaultValues?.subCategory1 ?? undefined,
            subCategory2: defaultValues?.subCategory2 ?? undefined,
            approvalRequired: false,
            verificationRequired: false,
            evidenceRequired: false,
            isPrivate: defaultValues?.isPrivate ?? false,
            taskType: defaultValues?.taskType ?? ActionItemKindExternalIdEnum.OneTime,
            assignmentDate: actionItem?.assignmentDate
                ? dayjs(actionItem?.assignmentDate).startOf('day')
                : dayjs().startOf('day'),
            dueDate: actionItem?.dueDate ? dayjs(actionItem?.dueDate).startOf('day') : dayjs().startOf('day'),
        },
    })
    const { reportingUnit, approvalRequired, verificationRequired, taskType, isPrivate } = watch()

    const reportingSiteOptions = activeUser.applications[0].userSites.map((site) => ({
        value: site.siteId,
        label: site.siteName,
    }))

    const { units } = useReportingUnits({ siteId })
    const { locations } = useReportingLocations(
        useMemo(() => {
            const request: LocationQueryRequest = { siteId, unitIds: [reportingUnit || '-'], onlyActive: true }
            return request
        }, [reportingUnit])
    )
    const { lines } = useReportingLines(
        useMemo(() => {
            const request: LineQueryRequest = { siteIds: [siteId], unitId: reportingUnit || '-' }
            return request
        }, [reportingUnit])
    )
    const { categories } = useActionItemCategories()
    const { subCategories } = useActionItemSubCategories()
    const { siteSpecificCategories } = useSiteSpecificCategories({ siteIds: [siteId] })

    const [linkList, setlinkList] = useState<ActionItemLink[]>([])

    const [filesUploaded, setFilesUploaded] = useState<File[]>([])
    const [oldUploadedFiles, setOldUploadedFiles] = useState<Attachment[]>([])

    const [categoryConfigurations, setCategoryConfigurations] = useState<CategoryConfigurationData[] | undefined>(
        undefined
    )
    const [selectCategoryConfiguration, setSelectCategoryConfiguration] = useState<
        CategoryConfigurationData | undefined
    >(undefined)

    const [selectedOwnerOption, setSelectedOwnerOption] = useState<AutocompleteOption>()
    const [selectedAssigneeOption, setSelectedAssigneeOption] = useState<AutocompleteOption>()
    const [selectedApproverOption, setSelectedApproverOption] = useState<AutocompleteOption>()
    const [selectedVerificationOption, setSelectedVerificationOption] = useState<AutocompleteOption>()
    const [usersParam, setUsersParam] = useState<string>('NULL_PARAM')
    const { users: filtredUsers } = useUsersSearch(
        useMemo(() => {
            return usersParam
        }, [usersParam])
    )

    const { users: filtredActiveUser } = useUsersSearch(
        useMemo(() => {
            return activeUser.email.split('@')[0]
        }, [activeUser.email])
    )

    const sortedFiltredUsers = transformOptionsForUser([...filtredUsers], true)
    const sortedUsersOptions = Array.from(
        new Map([...sortedFiltredUsers].map((item) => [item.value, item])).values()
    ).sort((a, b) => a.label.localeCompare(b.label))

    const selectedPriorityOption = transformStringOptions(
        Object.values(PriorityEnum),
        'stepper.form.assignment.priorityOptions'
    )

    const [defaultCheckedApprover, setDefaultCheckedApprover] = useState<boolean>(false)
    const [defaultCheckedVerification, setDefaultCheckedVerification] = useState<boolean>(false)
    const [defaultCheckedEvidenceRequired, setDefaultCheckedEvidenceRequired] = useState<boolean>(false)

    const [recurrenceSavedValue, setRecurrenceSavedValue] = useState<RecurrenceForm>()

    const [selectedUsersViewers, setSelectedUsersViewers] = useState<User[]>(
        actionItem?.viewUsers?.map((x) => x.user).filter((user): user is User => !!user) ?? []
    )
    const [selectedRolesViewers, setSelectedRolesViewers] = useState<Role[]>(actionItem?.viewRoles ?? [])
    const [selectedTeamsViewers, setSelectedTeamsViewers] = useState<Team[]>(actionItem?.viewTeams ?? [])

    const [selectedAssignees, setSelectedAssignees] = useState<NotificationSubscribers>(new NotificationSubscribers())

    const displayDueDateRestrictionReason =
        fromEdit && !actionItem?.isTemplate && isMasterAdmin && !!selectCategoryConfiguration?.daysFromAssignedDate

    const selectedOptionsUser = (user?: AutocompleteOption) => {
        let selectedOptionsUser: AutocompleteOption[] = [...sortedUsersOptions]

        if (user) {
            selectedOptionsUser = Array.from(new Set([...sortedUsersOptions, user]))
        }

        return selectedOptionsUser
    }

    const disableField = (fieldName: FormFieldNameEnum) => {
        const defaultDisable = fromEdit && !(actionItem?.recurrenceInstance || actionItem?.isTemplate)
        switch (fieldName) {
            case FormFieldNameEnum.SourceInformation:
                return !!eventId
            case FormFieldNameEnum.Owner:
                return defaultDisable && !isMasterAdmin
            case FormFieldNameEnum.Unit:
                return (
                    defaultDisable &&
                    !(
                        isMasterAdmin &&
                        actionItem?.currentStatus?.externalId === ActionStatusExternalIdClearEnum.Assigned
                    )
                )
            case FormFieldNameEnum.Location:
                return (
                    defaultDisable &&
                    !(
                        (isMasterAdmin &&
                            actionItem?.currentStatus?.externalId === ActionStatusExternalIdClearEnum.Assigned) ||
                        (siteId !== SITE_EXTERNAL_ID_REQUIRED_FIELD && !isMasterAdmin)
                    )
                )
            case FormFieldNameEnum.Category:
            case FormFieldNameEnum.Subcategory:
            case FormFieldNameEnum.SiteSpecificCategory:
                return [
                    ActionStatusExternalIdClearEnum.PendingApproval,
                    ActionStatusExternalIdClearEnum.PendingVerification,
                    ActionStatusExternalIdClearEnum.ApprovalRejected,
                    ActionStatusExternalIdClearEnum.VerificationRejected,
                ]
                    .map(String)
                    .includes(actionItem?.currentStatus?.externalId ?? '')
            case FormFieldNameEnum.TaskType:
                return !!eventId || fromEdit || isPrivate
            case FormFieldNameEnum.DueDate:
                if (!fromEdit) {
                    return !!selectCategoryConfiguration?.daysFromAssignedDate
                }

                if (actionItem?.isTemplate) {
                    return false
                }

                return (
                    !isMasterAdmin ||
                    actionItem?.currentStatus?.externalId !== ActionStatusExternalIdClearEnum.Assigned ||
                    !!selectCategoryConfiguration?.daysFromAssignedDate
                )
            case FormFieldNameEnum.ApproverToggle:
                return (
                    selectCategoryConfiguration?.isApprovalRequired ||
                    [
                        ActionStatusExternalIdClearEnum.PendingApproval,
                        ActionStatusExternalIdClearEnum.PendingVerification,
                        ActionStatusExternalIdClearEnum.ApprovalRejected,
                        ActionStatusExternalIdClearEnum.VerificationRejected,
                    ]
                        .map(String)
                        .includes(actionItem?.currentStatus?.externalId ?? '')
                )
            case FormFieldNameEnum.Approver:
            case FormFieldNameEnum.VerifierToggle:
                const categoryConfigurationValidate =
                    fieldName === FormFieldNameEnum.Approver
                        ? selectCategoryConfiguration?.isApprovalRequired
                        : selectCategoryConfiguration?.isVerificationRequired
                return (
                    categoryConfigurationValidate ||
                    [
                        ActionStatusExternalIdClearEnum.PendingVerification,
                        ActionStatusExternalIdClearEnum.VerificationRejected,
                    ]
                        .map(String)
                        .includes(actionItem?.currentStatus?.externalId ?? '')
                )
            case FormFieldNameEnum.Verifier:
                return selectCategoryConfiguration?.isVerificationRequired
            case FormFieldNameEnum.EvidenceRequired:
                return selectCategoryConfiguration?.attachmentRequired
            default:
                return defaultDisable
        }
    }

    function getTaskTypeRelatedProps(
        data: ActionFormSchema,
        recurringForm?: RecurrenceForm,
        assignedToIds?: string[],
        actionItem?: ActionDetailItem
    ): TaskTypeRelatedProps {
        let assigneeIdsResult: string[] | undefined = assignedToIds?.map((a) =>
            a.startsWith(PREFIX_USER_AZURE_ATTRIBUTE) ? a : `${PREFIX_USER_AZURE_ATTRIBUTE}${a}`
        )

        const shouldEvaluateAssignedTo = taskType === ActionItemKindExternalIdEnum.OneTime && !actionItem?.isTemplate

        const shouldEvaluateAssignees = actionItem?.isTemplate || taskType === ActionItemKindExternalIdEnum.Recurring

        if (shouldEvaluateAssignees && actionItem?.assignees?.length && assignedToIds) {
            const externalIds = actionItem.assignees.map((a) => `${PREFIX_USER_AZURE_ATTRIBUTE}${a.externalId}`)
            if (
                assigneeIdsResult?.length === actionItem.assignees?.length &&
                assigneeIdsResult?.every((id) => externalIds.includes(id))
            ) {
                assigneeIdsResult = undefined
            }
        }

        if (shouldEvaluateAssignedTo && actionItem?.assignedTo?.externalId) {
            if (assigneeIdsResult?.length === 1 && assigneeIdsResult[0] === actionItem.assignedTo.externalId) {
                assigneeIdsResult = undefined
            }
        }

        if (taskType === ActionItemKindExternalIdEnum.OneTime) {
            return {
                assignedToId: actionItem?.isTemplate ? undefined : assigneeIdsResult?.[0],
                assigneeIds: actionItem?.isTemplate ? assigneeIdsResult : undefined,
                assignmentDate: getUpdatedValue(actionItem?.assignmentDate, formatDate(data.assignmentDate!)),
                dueDate: getUpdatedValue(actionItem?.dueDate, formatDate(data.dueDate!)),
            }
        }

        return {
            recurrenceInstance: formatRecurrence(
                siteId,
                recurringForm!,
                actionItem?.recurrenceInstance?.externalId,
                actionItem?.recurrenceInstance
            ),
            assigneeIds: assigneeIdsResult,
        }
    }

    function getViewers(
        isPrivate: boolean,
        selectedUsers: User[] = [],
        selectedRoles: Role[] = [],
        selectedTeams: Team[] = []
    ) {
        if (!isPrivate) return { users: undefined, roles: undefined, teams: undefined }

        return {
            users: mapExternalId(selectedUsers, PREFIX_USER_AZURE_ATTRIBUTE),
            roles: mapExternalId(selectedRoles),
            teams: mapExternalId(selectedTeams),
        }
    }

    async function uploadFiles(newUploadedFiles: any, isPrivate: boolean) {
        if (!newUploadedFiles || !newUploadedFiles.length) return []

        const uploadFilesRequest = await buildUploadFilesRequest(
            cogniteClient,
            newUploadedFiles,
            activeUser.displayName,
            siteId,
            isPrivate,
            FileSourceStepEnum.ActionInProgress
        )

        return (await azureFunctionClient.uploadFiles(uploadFilesRequest)).externalIds
    }

    async function getAttachmentIds(data: any) {
        let newUploadedFilesIds = []
        if (data.newUploadedFiles?.length) {
            const uploadFilesRequest = await buildUploadFilesRequest(
                cogniteClient,
                data.newUploadedFiles,
                activeUser.displayName,
                siteId,
                data.isPrivate,
                FileSourceStepEnum.ActionCreation
            )
            newUploadedFilesIds = (await azureFunctionClient.uploadFiles(uploadFilesRequest)).externalIds
        }

        const oldUploadedFilesIds = data.oldUploadedFiles?.map((attachment: Attachment) => attachment.externalId) ?? []
        return [...newUploadedFilesIds, ...oldUploadedFilesIds]
    }

    async function buildActionItemRequest(): Promise<NewActionItemRequest> {
        const data = getValues()

        const taskTypeRelatedProps = getTaskTypeRelatedProps(data, recurrenceSavedValue)

        const mappedViewers = getViewers(
            data.isPrivate,
            selectedUsersViewers,
            selectedRolesViewers,
            selectedTeamsViewers
        )

        const attachmentIds = await getAttachmentIds(data)
        const formattedLinkList = formatLinksToRequest(linkList)

        return {
            eventId: '',
            title: data.title,
            timeZone: dayjs.tz.guess(),
            categoryId: data.category,
            subCategoryId: data.subCategory1,
            siteSpecificCategoryId: data.subCategory2 || undefined,
            ownerId: `${PREFIX_USER_AZURE_ATTRIBUTE}${data.owner}`,
            createdById: `${PREFIX_USER_AZURE_ATTRIBUTE}${activeUser.email}`,
            reportingUnitId: data.reportingUnit,
            actionItemKindId: data.taskType,
            reportingLocationId: data.reportingLocation || undefined,
            links: formattedLinkList.length > 0 ? formattedLinkList : undefined,
            description: data.description,
            attachmentIds: attachmentIds.length ? attachmentIds : undefined,
            sourceInformation: data.sourceInformation || undefined,
            approverId: data.approvalRequired ? `${PREFIX_USER_AZURE_ATTRIBUTE}${data.approver}` : undefined,
            verifierId: data.verificationRequired ? `${PREFIX_USER_AZURE_ATTRIBUTE}${data.verification}` : undefined,
            evidenceRequired: data.evidenceRequired,
            priority: data.priority || undefined,
            reportingLineId: data.reportingLine || undefined,
            voeActionItem: data.verificationRequired ? data.verificationOfEffectiveness || undefined : undefined,
            assignedToIds:
                data.assignees?.map((a) =>
                    a.startsWith(PREFIX_USER_AZURE_ATTRIBUTE) ? a : `${PREFIX_USER_AZURE_ATTRIBUTE}${a}`
                ) ?? [],
            applicationId: environment.userManagementAppCode!,
            reportingSiteId: siteId,
            sourceEventId: eventId,
            sourceTypeId: eventId ? ActionSourceTypeExternalIdEnum.AIMEvent : ActionSourceTypeExternalIdEnum.AIMScratch,
            sourceId: eventId,
            ...taskTypeRelatedProps,
            private: data.isPrivate || false,
            viewUsers: data.isPrivate ? mappedViewers.users : undefined,
            viewRoles: data.isPrivate ? mappedViewers.roles : undefined,
            viewTeams: data.isPrivate ? mappedViewers.teams : undefined,
        }
    }

    function getAssignedToIds(data: any, actionItem?: ActionDetailItem): string[] {
        if (data.taskType === ActionItemKindExternalIdEnum.Recurring || actionItem?.isTemplate) {
            return data.assignees ?? []
        }
        return data.assignedTo ? [data.assignedTo] : []
    }

    async function buildEditActionRequest(isChallengeEdit?: boolean) {
        const data = getValues()

        const assignedToIds = getAssignedToIds(data, actionItem)
        const taskTypeRelatedProps = getTaskTypeRelatedProps(data, recurrenceSavedValue, assignedToIds, actionItem)

        const mappedViewers = getViewers(
            data.isPrivate,
            selectedUsersViewers,
            selectedRolesViewers,
            selectedTeamsViewers
        )

        const newUploadedFilesIds = await uploadFiles(data.newUploadedFiles, data.isPrivate)
        const oldUploadedFilesIds = data.oldUploadedFiles?.map((attachment: Attachment) => attachment.externalId) ?? []

        const formattedLinkList = formatLinksToRequest(linkList)

        return {
            externalId: actionItem?.externalId ?? '',
            activeUserEmail: activeUser.email,
            reportingSiteId: siteId,

            challengeEdit: isChallengeEdit,

            title: getUpdatedValue(actionItem?.title, data.title),
            ownerId: getUpdatedValue(
                actionItem?.owner?.user?.externalId,
                data.owner,
                false,
                PREFIX_USER_AZURE_ATTRIBUTE
            ),
            reportingUnitId: getUpdatedValue(actionItem?.reportingUnit?.externalId, data.reportingUnit),
            reportingLocationId: getUpdatedValue(
                actionItem?.reportingLocation?.externalId,
                data.reportingLocation,
                true
            ),
            reportingLineId: getUpdatedValue(actionItem?.reportingLine?.externalId, data.reportingLine, true),
            description: getUpdatedValue(actionItem?.description, data.description),

            sourceInformation: getUpdatedValue(actionItem?.sourceInformation, data.sourceInformation, true),
            categoryId: getUpdatedValue(actionItem?.category?.externalId, data.category),
            subCategoryId: getUpdatedValue(actionItem?.subCategory?.externalId, data.subCategory1),
            siteSpecificCategoryId: getUpdatedValue(
                actionItem?.siteSpecificCategory?.externalId,
                data.subCategory2,
                true
            ),

            priority: getUpdatedValue(actionItem?.priority, data.priority, true),
            actionItemKindId: getUpdatedValue(actionItem?.actionItemKind?.externalId, data.taskType),
            approverId: getConditionalValue(data.approvalRequired, actionItem?.approver?.externalId, data.approver),
            verifierId: getConditionalValue(
                data.verificationRequired,
                actionItem?.verifier?.externalId,
                data.verification
            ),
            voeActionItem: getConditionalValue(
                data.verificationRequired,
                actionItem?.voeActionItem,
                data.verificationOfEffectiveness,
                true
            ),
            evidenceRequired: getUpdatedValue(actionItem?.evidenceRequired ?? false, data.evidenceRequired, true),
            ...taskTypeRelatedProps,

            links: getLinks(formattedLinkList, actionItem?.actionItemLink),

            attachmentIds: formatAttachmentExternalIdToRequest(
                newUploadedFilesIds,
                oldUploadedFilesIds,
                actionItem?.attachments?.map((x) => x.externalId)
            ),

            viewUsers: data.isPrivate ? mappedViewers.users : undefined,
            viewRoles: data.isPrivate ? mappedViewers.roles : undefined,
            viewTeams: data.isPrivate ? mappedViewers.teams : undefined,
        }
    }

    const submitNewAction = async () => {
        showLoading(true)
        try {
            const actionItemRequest = removeUndefinedValues(await buildActionItemRequest())
            const result = await azureFunctionClient.createActionItem([actionItemRequest])

            if (result.actionsIds && result.actionsIds.length > 0) {
                azureFunctionClient.createNotification(result.actionsIds).catch((err) => {
                    handleAlert(translate('alerts.errorCreatingNotification'), true)
                })
            }

            finishSteppedFunction && finishSteppedFunction(false, true)
            localStorage.removeItem('isEditForm')
            handleAlert(translate('alerts.actionsSavedWithSuccess'))
            !eventId && router.push('/')
        } catch (error) {
            handleAlert(translate('alerts.unexpectedErrorOcurred'), true)
        } finally {
            showLoading(false)
        }
    }

    const submitEdit = async () => {
        const isChallengeEdit =
            sessionStorage.getItem('challengeCompleted') === actionItem?.externalId ||
            actionItem?.currentStatus?.externalId === AllActionStatusExternalIdEnum.ChallengePeriod
        if (!isChallengeEdit && sessionStorage.getItem('challengeCompleted') !== null) {
            sessionStorage.removeItem('challengeCompleted')
        }

        try {
            showLoading(true)
            const data = getValues()
            const buildEdit = removeUndefinedValues(await buildEditActionRequest(isChallengeEdit))

            await azureFunctionClient.updateActionItem(actionItem?.externalId ?? '', buildEdit)

            localStorage.removeItem('isEditForm')

            handleAlert(translate('alerts.actionsSavedWithSuccess'))
            if (isChallengeEdit) {
                sessionStorage.removeItem('challengeCompleted')
            }

            if (data.recurringSaved || actionItem?.isTemplate) {
                location.replace('/admin-settings')
            } else {
                const isPendingApproval =
                    actionItem?.currentStatus?.externalId === ActionStatusExternalIdClearEnum.PendingApproval
                const isPendingVerification =
                    actionItem?.currentStatus?.externalId === ActionStatusExternalIdClearEnum.PendingVerification

                const isApprovalByDifferentUser = isPendingApproval && actionItem?.approver !== data.approver
                const isVerifyByDifferentUser = isPendingVerification && actionItem?.verifier !== data.verification

                const shouldSendNotification =
                    actionItem?.externalId && (isApprovalByDifferentUser || isVerifyByDifferentUser)

                if (shouldSendNotification) {
                    azureFunctionClient
                        .createActionEditNotification([actionItem?.externalId], siteId, activeUser.email)
                        .catch((err) => {})
                }

                router.push(`/action-item/details/${actionItem?.reportingSite?.siteCode}/${actionItem?.externalId}`)
            }
        } catch (err) {
            handleAlert(translate('alerts.unexpectedErrorOcurred'), true)
        } finally {
            showLoading(false)
        }
    }

    const handleBack = () => {
        setActiveStep((prevActiveStep) => prevActiveStep - 1)
    }

    const handleNext = async () => {
        const isValid = await trigger()
        if (isValid) {
            if (activeStep !== steps.length - 1) {
                setActiveStep((prevActiveStep) => prevActiveStep + 1)
            }
        } else {
            handleAlert(translate('stepper.form.requiredFieldsErro'), true)
            return
        }

        if (activeStep == 0 && fromEdit) {
            const newCategoryConfiguration = getCategoryConfiguration()
            setSelectCategoryConfiguration(newCategoryConfiguration)
        }

        if (activeStep == 1) {
            const newCategoryConfiguration = getCategoryConfiguration()
            setSelectCategoryConfiguration(newCategoryConfiguration)
            setRolesByCategoryConfig(newCategoryConfiguration)
        }
        if (activeStep === steps.length - 1) {
            return fromEdit ? submitEdit() : submitNewAction()
        }
    }

    const handleClick = () => {
        return handleNext()
    }

    const handleAlert = (message: string, error: boolean = false) => {
        showSnackbar(message, error ? 'error' : 'success', fromEdit ? 'edit-action' : 'new-action')
    }

    const handleSavePrivateSettings = (viewers: Viewers) => {
        const { users, roles, teams } = viewers
        setSelectedUsersViewers(users)
        setSelectedRolesViewers(roles)
        setSelectedTeamsViewers(teams)

        setValue('isPrivate', true)
    }

    const handleLeaveCancelModal = () => {
        localStorage.removeItem('isEditForm')
        setIsCancelModalOpen(false)
        reset({})
        clickLeaveCancelModal && clickLeaveCancelModal()
    }

    const renderGeneral = () => {
        return (
            <Grid id={'forms-general'} container spacing={2.5} width={'100%'}>
                <Grid item xs={12} md={6} id={'fields-forms-action'}>
                    <Grid item md={12} id={'geneal-forms-action'} sx={styles.itemsForm}>
                        <GenericFieldTitle fieldName={translate('stepper.form.general')} isSubHeader />
                    </Grid>
                    <Grid item md={12} id={'title-forms-action'} sx={styles.itemsForm}>
                        <GenericTextField
                            name={'title'}
                            control={control}
                            error={Boolean(errors.title)}
                            valueController={getValues('title')}
                            label={translate('stepper.form.title')}
                            helperText={translate('stepper.form.helperTextTitle')}
                            onChange={(newValue) => setValue('title', newValue)}
                            required
                            placeholder=""
                            shrink={false}
                            data-test="new_action_item_flow_1-general_title_field"
                            data-origin="aim"
                        />
                    </Grid>
                    <Grid item md={12} id={'owner-forms-action'} sx={styles.itemsForm}>
                        <GenericAutocomplete
                            name="owner"
                            multiple={false}
                            disabled={disableField(FormFieldNameEnum.Owner)}
                            valueController={selectedOwnerOption}
                            control={control}
                            options={selectedOptionsUser(selectedOwnerOption)}
                            onChange={(newValue, fieldOption) => {
                                setValue('owner', newValue)
                                setSelectedOwnerOption(fieldOption)
                            }}
                            onInputChange={(event, newInputValue) => {
                                if (newInputValue.length >= 3) {
                                    setUsersParam(newInputValue)
                                }
                            }}
                            size="small"
                            disableCloseOnSelect={false}
                            error={Boolean(errors.owner)}
                            label={`${translate('stepper.form.actionItemOwner')} *`}
                            noOptionsText={translate('stepper.form.typeAtLeast3')}
                            data-test="new_action_item_flow_1-action_item_owner_field"
                            data-origin="aim"
                        />
                    </Grid>
                    {reportingSiteOptions?.length > 1 && (
                        <Grid item md={12} id={'reporting-site-forms-action'} sx={styles.itemsForm}>
                            <GenericAutocomplete
                                name="reportingSite"
                                control={control}
                                options={reportingSiteOptions}
                                onChange={(value) => {
                                    setValue('reportingSite', value)
                                }}
                                multiple={false}
                                label={`${translate('stepper.form.reportingSite')} *`}
                                size="small"
                                disabled
                                required
                                data-test="new_action_item_flow_1-general_reporting-site_field"
                                data-origin="aim"
                            />
                        </Grid>
                    )}
                    <Grid item md={12} id={'reporting-unit-forms-action'} sx={styles.itemsForm}>
                        <GenericAutocomplete
                            name="reportingUnit"
                            control={control}
                            disabled={disableField(FormFieldNameEnum.Unit)}
                            options={transformOptions(units) ?? []}
                            onChange={(newValue) => {
                                setValue('reportingUnit', newValue)
                                setValue('reportingLocation', '')
                                setValue('reportingLine', '')
                            }}
                            label={`${translate('stepper.form.unit')} *`}
                            size="small"
                            error={Boolean(errors.reportingUnit)}
                            multiple={false}
                            disableCloseOnSelect={false}
                            data-test="new_action_item_flow_1-unit_field"
                            data-origin="aim"
                        />
                    </Grid>
                    <Grid item md={12} id={'reporting-location-forms-action'} sx={styles.itemsForm}>
                        <GenericAutocomplete
                            name="reportingLocation"
                            control={control}
                            disabled={disableField(FormFieldNameEnum.Location)}
                            options={transformOptions(locations) ?? []}
                            onChange={(newValue) => {
                                setValue('reportingLocation', newValue)
                            }}
                            label={`${translate('stepper.form.reportingLocation')}${
                                siteId === SITE_EXTERNAL_ID_REQUIRED_FIELD ? ' *' : ''
                            }`}
                            size="small"
                            error={Boolean(errors.reportingLocation)}
                            multiple={false}
                            disableCloseOnSelect={false}
                            data-test="new_action_item_flow_1-reporting_location_field"
                            data-origin="aim"
                        />
                    </Grid>
                    <Grid item md={12} id={'reporting-line-forms-action'} sx={styles.itemsForm}>
                        <GenericAutocomplete
                            name="reportingLine"
                            control={control}
                            options={transformOptions(lines) ?? []}
                            onChange={(newValue) => {
                                setValue('reportingLine', newValue)
                            }}
                            label={translate('stepper.form.reportingLine')}
                            noOptionsText={translate('stepper.form.noOptionsReportingLine')}
                            size="small"
                            error={Boolean(errors.reportingLine)}
                            multiple={false}
                            data-test="new_action_item_flow_1-reporting_line_field"
                            data-origin="aim"
                        />
                    </Grid>
                    <Grid item md={12} id={'description-forms-action'} sx={styles.itemsForm}>
                        <GenericTextField
                            name={'description'}
                            control={control}
                            error={Boolean(errors.description)}
                            valueController={getValues('description')}
                            label={translate('stepper.form.description')}
                            helperText={translate('stepper.form.helperTextDescription')}
                            onChange={(newValue) => {
                                setValue('description', newValue)
                            }}
                            required
                            rows={3}
                            placeholder=""
                            shrink={false}
                            data-test="new_action_item_flow_1-description_field"
                            data-origin="ui-lib"
                        />
                    </Grid>
                </Grid>
                <Grid item xs={12} md={6} id={'files-and-configuration-options-forms-action'}>
                    <LinksComponent
                        links={linkList ?? []}
                        setLinks={setlinkList}
                        sxProps={{
                            paddingTop: '1rem',
                            display: 'flex',
                            overflow: 'auto',
                        }}
                        handleAlert={handleAlert}
                    />
                </Grid>
            </Grid>
        )
    }

    const renderSource = () => {
        return (
            <Grid id={'source-form'} container spacing={2.5}>
                <Grid item xs={12} md={6} id={'fields-source-form'}>
                    <Grid item md={12} id={'sorce-information-source-form'} sx={styles.itemsForm}>
                        <GenericTextField
                            name={'sourceInformation'}
                            control={control}
                            error={Boolean(errors.sourceInformation)}
                            valueController={getValues('sourceInformation')}
                            label={translate('stepper.form.source.sourceInformation')}
                            helperText={translate('stepper.form.helperTextSourceInfo')}
                            onChange={(newValue) => setValue('sourceInformation', newValue)}
                            required={siteId === SITE_EXTERNAL_ID_REQUIRED_FIELD}
                            placeholder=""
                            shrink={false}
                            disabled={disableField(FormFieldNameEnum.SourceInformation)}
                        />
                    </Grid>
                    <Grid item md={12} id={'category-source-form'} sx={styles.itemsForm}>
                        <GenericAutocomplete
                            name="category"
                            control={control}
                            disabled={disableField(FormFieldNameEnum.Category)}
                            options={transformOptions(categories, 'name') ?? []}
                            onChange={(newValue) => setValue('category', newValue)}
                            size="small"
                            error={Boolean(errors.category)}
                            label={`${translate('stepper.form.source.category')} *`}
                            multiple={false}
                            disableCloseOnSelect={false}
                        />
                    </Grid>
                    <Grid item md={12} id={'subcategory-source-form'} sx={styles.itemsForm}>
                        <GenericAutocomplete
                            name="subCategory1"
                            control={control}
                            disabled={disableField(FormFieldNameEnum.Subcategory)}
                            options={transformOptions(subCategories, 'name') ?? []}
                            onChange={(newValue) => setValue('subCategory1', newValue)}
                            size="small"
                            error={Boolean(errors.subCategory1)}
                            label={`${translate('stepper.form.source.subcategory01')} *`}
                            multiple={false}
                            disableCloseOnSelect={false}
                        />
                    </Grid>
                    <Grid item md={12} id={'site-specific-category-source-form'} sx={styles.itemsForm}>
                        <GenericAutocomplete
                            name="subCategory2"
                            control={control}
                            disabled={disableField(FormFieldNameEnum.SiteSpecificCategory)}
                            options={transformOptions(siteSpecificCategories, 'name') ?? []}
                            onChange={(newValue) => setValue('subCategory2', newValue)}
                            size="small"
                            error={Boolean(errors.subCategory2)}
                            label={`${translate('stepper.form.source.subcategory02')}${
                                siteId === SITE_EXTERNAL_ID_REQUIRED_FIELD ? ' *' : ''
                            }`}
                            multiple={false}
                            disableCloseOnSelect={false}
                        />
                    </Grid>
                </Grid>
            </Grid>
        )
    }

    const renderAssigment = () => {
        return (
            <Grid id={'assignment-form'} container spacing={2.5}>
                <Grid item xs={12} md={6} id={'fields-assignment-form'}>
                    <Grid item md={12} id={'priority-assignment-form'} sx={styles.itemsForm}>
                        <GenericAutocomplete
                            name="priority"
                            control={control}
                            options={selectedPriorityOption ?? []}
                            valueController={selectedPriorityOption.find((x) => x.value === getValues('priority'))}
                            onChange={(newValue) => {
                                setValue('priority', newValue)
                            }}
                            label={translate('stepper.form.assignment.priority')}
                            size="small"
                            error={Boolean(errors.priority)}
                            multiple={false}
                            disableCloseOnSelect={false}
                            data-test="new_action_item_flow_3-priority_field"
                            data-origin="aim"
                        />
                    </Grid>
                    <Grid item md={12} id={'assignee-assignment-form'} sx={styles.itemsForm}>
                        {fromEdit && !actionItem?.isTemplate && !actionItem?.recurrenceInstance ? (
                            <GenericAutocomplete
                                name="assignedTo"
                                multiple={false}
                                valueController={selectedAssigneeOption}
                                control={control}
                                options={selectedOptionsUser(selectedAssigneeOption)}
                                onChange={(newValue, fieldOption) => {
                                    setValue('assignedTo', newValue)
                                    setSelectedAssigneeOption(fieldOption)
                                }}
                                size="small"
                                onInputChange={(event, newInputValue) => {
                                    if (newInputValue.length >= 3) {
                                        setUsersParam(newInputValue)
                                    }
                                }}
                                disableCloseOnSelect={false}
                                error={Boolean(errors.assignedTo)}
                                label={`${translate('edit.fields.assignee')} *`}
                                noOptionsText={translate('stepper.form.typeAtLeast3')}
                                data-test="new_action_item_flow_3-assignee_to_field"
                                data-origin="aim"
                            />
                        ) : (
                            <SendToComponent
                                stepWithError={Boolean(errors.assignees)}
                                selectedSubscribers={selectedAssignees}
                                setSelectedSubscribers={setSelectedAssignees}
                                siteId={siteId}
                            />
                        )}
                    </Grid>
                    <Grid item md={12} id={'taskType-assignment-form'} sx={styles.itemsForm}>
                        <Box sx={{ display: 'flex', flexDirection: 'column' }}>
                            <GenericFieldTitle fieldName={translate('stepper.form.assignment.taskType')} isSubHeader />
                            {isPrivate && (
                                <Typography color="error.main" variant="overline">
                                    {translate('alerts.alertPrivateForm')}
                                </Typography>
                            )}
                            <ClnRadio
                                aria-labelledby="action-form-taskType-radio-group"
                                items={[
                                    {
                                        value: ActionItemKindExternalIdEnum.OneTime,
                                        label: translate('stepper.form.assignment.taskTypeOneTime'),
                                        disabled: disableField(FormFieldNameEnum.TaskType),
                                    },
                                    {
                                        value: ActionItemKindExternalIdEnum.Recurring,
                                        label: translate('stepper.form.assignment.taskTypeRecurring'),
                                        disabled: disableField(FormFieldNameEnum.TaskType),
                                    },
                                ]}
                                value={getValues('taskType')}
                                onChange={(item) => {
                                    setValue('taskType', item.value as string)
                                }}
                                radioDirection="row"
                                sxProps={{
                                    marginTop: '10px',
                                    '& .MuiRadioGroup-root': {
                                        display: 'flex',
                                        gap: 1,
                                    },
                                    '& .MuiFormControlLabel-label': {
                                        fontSize: '1rem',
                                        fontWeight: 400,
                                        lineHeight: 1.5,
                                    },
                                    '& .MuiRadio-root': {
                                        transform: 'scale(1.5)',
                                        paddingRight: 2,
                                    },
                                }}
                            />
                        </Box>
                    </Grid>
                    {taskType === ActionItemKindExternalIdEnum.OneTime ? (
                        <Grid item xs={12} md={12} id={'date-fields-assignment-form'}>
                            <Grid item md={12} id={'assignement-date-assignment-form'} sx={styles.itemsForm}>
                                <GenericDatePicker
                                    control={control}
                                    name="assignmentDate"
                                    subHeader={translate('stepper.form.assignment.assignementDate')}
                                    disabled={disableField(FormFieldNameEnum.AssigmentDate)}
                                    minDate={dayjs().startOf('day')}
                                    onChange={(date: Dayjs | null) => {
                                        if (selectCategoryConfiguration?.daysFromAssignedDate) {
                                            const calculatedDueDate = dayjs(date).add(
                                                selectCategoryConfiguration.daysFromAssignedDate,
                                                'day'
                                            )
                                            setValue('dueDate', calculatedDueDate)
                                        }
                                    }}
                                    error={Boolean(errors.assignmentDate)}
                                    data-test="new_action_item_flow_3-assignment_date_button"
                                    data-origin="aim"
                                />
                            </Grid>
                            <Grid item md={12} id={'due-date-assignment-form'} sx={styles.itemsForm}>
                                <GenericDatePicker
                                    control={control}
                                    name="dueDate"
                                    subHeader={translate('stepper.form.assignment.dueDate')}
                                    disabled={disableField(FormFieldNameEnum.DueDate)}
                                    minDate={dayjs(getValues('assignmentDate') || dayjs()).startOf('day')}
                                    error={Boolean(errors.dueDate)}
                                    data-test="new_action_item_flow_3-assignment_date_button"
                                    data-origin="aim"
                                />
                                {displayDueDateRestrictionReason && (
                                    <Typography
                                        variant="subtitle1"
                                        sx={{ fontSize: '12px', color: theme.palette.grey[600] }}
                                    >
                                        {`* ${translate('stepper.form.assignment.dueDateRestrictionReason')}`}
                                    </Typography>
                                )}
                            </Grid>
                        </Grid>
                    ) : (
                        <Grid
                            item
                            xs={12}
                            md={12}
                            id={'recurring-date-fields-assignment-form'}
                            sx={{ ...styles.itemsDateColumnForm, marginBottom: '1.5rem' }}
                        >
                            {Boolean(errors.taskType) && (
                                <Typography color="error.main" variant="overline">
                                    {translate('stepper.form.assignment.recurrenceRequired')}
                                </Typography>
                            )}
                            <RecurrenceDrawer
                                recurrenceSavedValue={recurrenceSavedValue}
                                setRecurrenceForm={(value: RecurrenceForm) => {
                                    setRecurrenceSavedValue(value)
                                    setValue('recurringSaved', true)
                                }}
                            />
                        </Grid>
                    )}
                    <Grid item md={12} id={'upload-files-assignment-form'} sx={styles.itemsForm}>
                        <Box
                            sx={{
                                display: 'flex',
                                flexDirection: 'column',
                                width: '100%',
                                gap: 1,
                            }}
                        >
                            <UploadFiles
                                oldUploadedFiles={oldUploadedFiles ?? []}
                                newUploadedFiles={filesUploaded ?? []}
                                isEditable
                                setOldUploadedFiles={(newValue: any) => {
                                    setOldUploadedFiles(newValue)
                                    setValue('oldUploadedFiles', newValue)
                                }}
                                setNewUploadedFiles={(newValue: any) => {
                                    setFilesUploaded(newValue)
                                    setValue('newUploadedFiles', newValue)
                                }}
                                sxProps={{ maxHeight: '60px', overflowY: 'auto' }}
                                showClearAllButton={false}
                            />
                        </Box>
                    </Grid>
                </Grid>
                <Grid item xs={12} md={6} id={'workflow-and-private-fields-assignment-form'}>
                    <GenericFieldTitle
                        fieldName={translate('stepper.form.assignment.approvalAndVerification')}
                        isSubHeader
                    />
                    <Grid item md={12} id={'approver-action'} mt={2} sx={styles.itemsForm}>
                        <Box
                            sx={{
                                display: 'flex',
                                flexDirection: 'column',
                                width: '100%',
                                gap: 1,
                            }}
                        >
                            <ClnToggle
                                defaultChecked={defaultCheckedApprover}
                                label={translate('stepper.form.assignment.approvalRequired')}
                                disabled={disableField(FormFieldNameEnum.ApproverToggle)}
                                labelPlacement="end"
                                onChange={() => {
                                    const currentValue = getValues('approvalRequired')
                                    setValue('approvalRequired', !currentValue)
                                    setDefaultCheckedApprover(!currentValue)
                                }}
                                size="medium"
                                sxProps={styles.toggleStyles}
                            />
                            {approvalRequired && (
                                <GenericAutocomplete
                                    name="approver"
                                    multiple={false}
                                    valueController={selectedApproverOption}
                                    disabled={disableField(FormFieldNameEnum.Approver)}
                                    control={control}
                                    options={selectedOptionsUser(selectedApproverOption)}
                                    onChange={(newValue, fieldOption) => {
                                        setValue('approver', newValue)
                                        setSelectedApproverOption(fieldOption)
                                    }}
                                    size="small"
                                    disableCloseOnSelect={false}
                                    error={Boolean(errors.approver)}
                                    onInputChange={(event, newInputValue) => {
                                        if (newInputValue.length >= 3) {
                                            setUsersParam(newInputValue)
                                        }
                                    }}
                                    label={translate('stepper.form.assignment.approvalBy')}
                                    helperText={translate('stepper.form.assignment.approvalDueDate').replace(
                                        '{}',
                                        `${selectCategoryConfiguration?.daysToApproval ?? '10'}`
                                    )}
                                />
                            )}
                        </Box>
                    </Grid>
                    <Grid item md={12} id={'verification-action'} mt={2} sx={styles.itemsForm}>
                        <Box
                            sx={{
                                display: 'flex',
                                flexDirection: 'column',
                                width: '100%',
                                gap: 1,
                            }}
                        >
                            <ClnToggle
                                defaultChecked={defaultCheckedVerification}
                                disabled={disableField(FormFieldNameEnum.VerifierToggle)}
                                label={translate('stepper.form.assignment.verificationRequired')}
                                labelPlacement="end"
                                onChange={() => {
                                    const currentValue = getValues('verificationRequired')
                                    setValue('verificationRequired', !currentValue)
                                    setDefaultCheckedVerification(!currentValue)
                                }}
                                size="medium"
                                sxProps={styles.toggleStyles}
                            />
                            {verificationRequired && (
                                <Box
                                    sx={{
                                        display: 'flex',
                                        flexDirection: 'column',
                                        width: '100%',
                                        gap: 2,
                                    }}
                                >
                                    <GenericAutocomplete
                                        name="verification"
                                        multiple={false}
                                        disabled={disableField(FormFieldNameEnum.Verifier)}
                                        valueController={selectedVerificationOption}
                                        control={control}
                                        options={selectedOptionsUser(selectedVerificationOption)}
                                        onChange={(newValue, fieldOption) => {
                                            setValue('verification', newValue)
                                            setSelectedVerificationOption(fieldOption)
                                        }}
                                        onInputChange={(event, newInputValue) => {
                                            if (newInputValue.length >= 3) {
                                                setUsersParam(newInputValue)
                                            }
                                        }}
                                        size="small"
                                        disableCloseOnSelect={false}
                                        error={Boolean(errors.verification)}
                                        label={translate('stepper.form.assignment.verifyBy')}
                                    />
                                    <Box>
                                        <GenericTextField
                                            name={'verificationOfEffectiveness'}
                                            control={control}
                                            error={Boolean(errors.verificationOfEffectiveness)}
                                            valueController={getValues('verificationOfEffectiveness')}
                                            label={translate('stepper.form.assignment.verificationOfEffectiveness')}
                                            helperText={translate(
                                                'stepper.form.assignment.helperTextVerificationOfEddectiveness'
                                            )}
                                            rows={5}
                                            shrink={false}
                                        />
                                        <FormHelperText>
                                            {translate('stepper.form.assignment.verificationDueDate').replace(
                                                '{}',
                                                `${selectCategoryConfiguration?.daysToVerification ?? '45'}`
                                            )}
                                        </FormHelperText>
                                    </Box>
                                </Box>
                            )}
                        </Box>
                    </Grid>
                    <Grid item md={12} id={'evidence-required-action'} mt={2} sx={styles.itemsForm}>
                        <ClnToggle
                            defaultChecked={defaultCheckedEvidenceRequired}
                            label={translate('stepper.form.assignment.evidenceRequired')}
                            disabled={disableField(FormFieldNameEnum.EvidenceRequired)}
                            labelPlacement="end"
                            onChange={() => {
                                const currentValue = getValues('evidenceRequired')
                                setValue('evidenceRequired', !currentValue)
                                setDefaultCheckedEvidenceRequired(!currentValue)
                            }}
                            size="medium"
                            sxProps={styles.toggleStyles}
                        />
                    </Grid>
                    <Grid item xs={12} id={'private-field-forms-action'} sx={styles.itemsForm}>
                        <PrivateComponent
                            siteId={siteId}
                            disabled={fromEdit || taskType == ActionItemKindExternalIdEnum.Recurring || !!eventId}
                            isChecked={isPrivate}
                            setIsChecked={(value: boolean) => setValue('isPrivate', value)}
                            selectedUsersViewers={selectedUsersViewers}
                            selectedRolesViewers={selectedRolesViewers}
                            selectedTeamsViewers={selectedTeamsViewers}
                            onSave={handleSavePrivateSettings}
                            isFromActionForm
                        />
                    </Grid>
                </Grid>
            </Grid>
        )
    }

    const renderForm = (activeStep: number) => (
        <Form id={'forms-action'}>
            {activeStep === 0 && renderGeneral()}
            {activeStep === 1 && renderSource()}
            {activeStep === 2 && renderAssigment()}
        </Form>
    )

    const getLabelText = () => {
        if (activeStep === steps.length - 1) {
            return fromEdit ? translate('stepper.save') : translate('stepper.submit')
        }
        return translate('stepper.next')
    }

    const getCategoryConfiguration: () => CategoryConfigurationData | undefined = () => {
        const data = getValues()
        const { category, subCategory1, subCategory2 } = data
        const identifier = `CCON-${category}_${subCategory1}_${subCategory2 ?? ''}`

        const newCategoryConfiguration =
            categoryConfigurations?.find((config) => config.externalId === identifier) ?? undefined

        return newCategoryConfiguration
    }

    const setRolesByCategoryConfig = (categoryConfiguration?: CategoryConfigurationData) => {
        const {
            daysFromAssignedDate,
            attachmentRequired,
            isApprovalRequired,
            isVerificationRequired,
            defaultApprovalRole,
            defaultVerificationRole,
            defaultApprovalUser,
            defaultVerificationUser,
        } = categoryConfiguration ?? {}

        const userComplementApproval = defaultApprovalRole?.userRoleSites?.[0].usersComplements ?? []
        const userComplementVerification = defaultVerificationRole?.userRoleSites?.[0].usersComplements ?? []

        if (attachmentRequired) {
            setValue('evidenceRequired', true)
        }

        if (daysFromAssignedDate) {
            const calculatedDueDate = dayjs(getValues('assignmentDate')).add(daysFromAssignedDate, 'day')
            setValue('dueDate', calculatedDueDate)
        }

        if (isApprovalRequired) {
            setDefaultCheckedApprover(true)
            setValue('approvalRequired', true)
            const filteredUser =
                siteId === SITE_EXTERNAL_ID_REQUIRED_FIELD && defaultApprovalRole?.externalId === UNIT_MANAGER_ROLE_WAS
                    ? userComplementApproval?.find(
                          (user) => user.reportingUnits?.some((unit) => unit.externalId.startsWith(reportingUnit ?? ''))
                      )
                    : userComplementApproval[0]

            const approver = defaultApprovalUser ?? filteredUser?.userAzureAttribute?.user

            if (approver) {
                setValue('approver', approver?.externalId)
                setSelectedApproverOption({
                    value: `${approver?.externalId}`,
                    label: `${approver?.lastName}, ${approver?.firstName} (${approver?.email})`,
                })
            }
        }

        if (isVerificationRequired) {
            setDefaultCheckedVerification(true)
            setValue('verificationRequired', true)
            const filteredUser =
                siteId === SITE_EXTERNAL_ID_REQUIRED_FIELD &&
                defaultVerificationRole?.externalId === UNIT_MANAGER_ROLE_WAS
                    ? userComplementVerification.find(
                          (user) => user.reportingUnits?.some((unit) => unit.externalId.startsWith(reportingUnit ?? ''))
                      )
                    : userComplementVerification[0]
            const verification = defaultVerificationUser ?? filteredUser?.userAzureAttribute?.user

            if (verification) {
                setValue('verification', verification?.externalId)
                setSelectedVerificationOption({
                    value: `${verification?.externalId}`,
                    label: `${verification?.lastName}, ${verification?.firstName} (${verification?.email})`,
                })
            }
        }
    }

    const fetchCategoryConfiguration = useCallback(async () => {
        try {
            showLoading(true)

            const resultCategory: CategoryConfigurationData[] =
                await azureFunctionClient.getCategoryConfigurationBySite({
                    reportingSiteExternalId: siteId,
                })
            if (resultCategory) setCategoryConfigurations(resultCategory)
        } catch (err) {
            const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred'
            handleAlert(errorMessage, true)
        } finally {
            showLoading(false)
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [siteId])

    const debouncedFetchCategoryConfiguration = useCallback(useDebounceFunction(fetchCategoryConfiguration, 800), [
        fetchCategoryConfiguration,
    ])

    useEffect(() => {
        if (siteId !== undefined) debouncedFetchCategoryConfiguration()
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [debouncedFetchCategoryConfiguration])

    useEffect(() => {
        if (!actionItem) return

        const {
            title,
            description,
            actionItemLink,
            sourceInformation,
            category,
            subCategory,
            siteSpecificCategory,
            reportingUnit,
            reportingLocation,
            reportingLine,
            owner,
            priority,
            actionItemKind,
            recurrenceInstance,
            assignees,
            assignedTo,
            assignmentDate,
            dueDate,
            attachments,
            approver,
            verifier,
            voeActionItem,
            evidenceRequired,
            isPrivate,
            viewUsers,
            viewRoles,
            viewTeams,
        } = actionItem

        setlinkList(actionItemLink ?? [])
        if (recurrenceInstance) {
            let recurrenceTypeValue =
                recurrenceInstance.recurrenceType?.externalId?.replace('RCT-', '') ?? RecurrenceTypeEnum.Daily
            let recurrenceSubType = undefined

            if (Object.values(RecurrenceSubTypeEnum).includes(recurrenceTypeValue as RecurrenceSubTypeEnum)) {
                recurrenceSubType = recurrenceTypeValue as RecurrenceSubTypeEnum
                recurrenceTypeValue = RecurrenceTypeEnum.Yearly
            }

            setRecurrenceSavedValue({
                ...recurrenceInstance,
                startDate: recurrenceInstance.startDate ? dayjs(recurrenceInstance.startDate) : dayjs(),
                noEndDate: recurrenceInstance.endDate ? false : true,
                endDate: recurrenceInstance.endDate ? dayjs(recurrenceInstance.endDate) : undefined,
                recurrenceType: recurrenceTypeValue as RecurrenceTypeEnum,
                recurrenceSubType: recurrenceSubType,
            })
        }

        const newValues = {
            title: title ?? '',
            description: description ?? '',
            owner: owner?.user.externalId ?? '',
            reportingUnit: reportingUnit?.externalId ?? '',
            reportingLocation: reportingLocation?.externalId ?? '',
            reportingLine: reportingLine?.externalId ?? '',
            reportingSite: siteId ?? '',

            sourceInformation: sourceInformation ?? '',
            category: category?.externalId ?? '',
            subCategory1: subCategory?.externalId ?? '',
            subCategory2: siteSpecificCategory?.externalId ?? '',

            priority: priority ?? '',

            taskType: actionItemKind?.externalId,
            recurringSaved: recurrenceInstance ? true : false,

            assignees: assignees?.map((user) => user.externalId) ?? [],
            assignedTo: assignedTo?.user.externalId ?? '',
            assignmentDate: assignmentDate ? dayjs(assignmentDate) : null,
            dueDate: dueDate ? dayjs(dueDate) : null,
            oldUploadedFiles: attachments ?? [],

            approvalRequired: !!approver,
            approver: approver?.externalId,
            verificationRequired: !!verifier,
            verification: verifier?.externalId,
            verificationOfEffectiveness: voeActionItem,
            evidenceRequired: evidenceRequired ?? false,
            isPrivate: isPrivate ?? false,
        }

        reset(newValues)

        assignees &&
            setSelectedAssignees((prev: NotificationSubscribers) => ({
                ...prev,
                users: [...(assignees as UserComplement[])],
            }))

        setSelectedOwnerOption({
            value: `${owner?.user.externalId}`,
            label: `${owner?.user.lastName}, ${owner?.user.firstName} (${owner?.user.email})`,
        })

        setSelectedAssigneeOption({
            value: `${assignedTo?.user.externalId}`,
            label: `${assignedTo?.user.lastName}, ${assignedTo?.user.firstName} (${assignedTo?.user.email})`,
        })

        setSelectedApproverOption(
            approver?.externalId
                ? {
                      value: `${approver?.externalId}`,
                      label: `${approver?.lastName}, ${approver?.firstName} (${approver?.email})`,
                  }
                : undefined
        )

        setSelectedVerificationOption(
            verifier?.externalId
                ? {
                      value: `${verifier?.externalId}`,
                      label: `${verifier?.lastName}, ${verifier?.firstName} (${verifier?.email})`,
                  }
                : undefined
        )

        setOldUploadedFiles(attachments ?? [])
        setDefaultCheckedApprover(!!approver)
        setDefaultCheckedVerification(!!verifier)
        setDefaultCheckedEvidenceRequired(evidenceRequired ?? false)
        if (isPrivate) {
            setSelectedUsersViewers(viewUsers?.map((x) => x.user) ?? [])
            setSelectedRolesViewers(viewRoles ?? [])
            setSelectedTeamsViewers(viewTeams ?? [])
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [actionItem])

    useEffect(() => {
        const extractUserExternalIds = (subscribers: NotificationSubscribers) => {
            const allUsers: UserComplement[] = [
                ...subscribers.users,
                ...subscribers.roles.flatMap((role) => role.users ?? []),
                ...subscribers.teams.flatMap((team) => team.users ?? []),
                ...subscribers.units.flatMap((unit) => unit.users ?? []),
            ]

            return allUsers.map((user) => user.externalId)
        }
        const externalIds = extractUserExternalIds(selectedAssignees)
        setValue('assignees', externalIds)
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [selectedAssignees])

    useEffect(() => {
        if (!filtredActiveUser || filtredActiveUser.length === 0 || fromEdit) return

        const user = transformOptionsForUser([filtredActiveUser.find((user) => user.email === activeUser.email)], true)
        setSelectedOwnerOption(user[0])
        setValue('owner', user[0].value)
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [filtredActiveUser])

    return (
        <Box id={'new-action'} width={'100%'}>
            <Box
                sx={{
                    display: 'flex',
                    justifyContent: pageTitle ? 'space-between' : 'flex-end',
                    alignItems: 'center',
                    paddingBottom: '20px',
                }}
            >
                {pageTitle && <GenericFieldTitle fieldName={pageTitle} isSectionTitle />}
                <ClnButton
                    size="small"
                    variant="outlined"
                    label={translate('stepper.form.cancel')}
                    onClick={() => setIsCancelModalOpen(true)}
                    data-test="new_action_item_flow_1-cancel_button"
                    data-origin="ui-lib"
                />
            </Box>
            <Stepper activeStep={activeStep}>
                {steps.map((label) => {
                    const stepProps: { completed?: boolean } = {}
                    const labelProps: {
                        optional?: React.ReactNode
                    } = {}
                    return (
                        <Step key={label} {...stepProps}>
                            <StepLabel {...labelProps}>{label}</StepLabel>
                        </Step>
                    )
                })}
            </Stepper>
            {renderForm(activeStep)}
            <Box id={'forms-new-action-buttons'} sx={{ display: 'flex', gap: '8px', justifyContent: 'flex-end' }}>
                {!fromEdit && activeStep === steps.length - 1 && (
                    <NewTemplateModal
                        siteId={siteId!}
                        activeUser={activeUser}
                        linkList={linkList}
                        disabled={isPrivate}
                        formValues={getValues()}
                        recurrenceSavedValue={recurrenceSavedValue}
                    />
                )}
                <ClnButton
                    variant="outlined"
                    disabled={activeStep === 0}
                    onClick={handleBack}
                    label={translate('stepper.back')}
                />
                <ClnButton
                    onClick={handleClick}
                    label={getLabelText()}
                    disabled={fromEdit && activeStep === steps.length - 1 && !actionItem}
                />
            </Box>
            <MessageModal
                name=""
                text={translate('requestModal.closeQuestion')}
                open={isCancelModalOpen}
                isCancelModal
                handleClose={() => setIsCancelModalOpen(false)}
                handleLeave={handleLeaveCancelModal}
            />
        </Box>
    )
}
