import { environment } from '../configurations/environment'
import { createPublicClientApplication } from '../factories/msal-factory'
import { CategoryConfigurationData, CommentRequest, ResponseData } from '../models/action-detail'
import { getIdTokenFromMsal, safeStringify } from '../utils'
import { SourceEventTitlesRequest, SourceEventTitlesResponse } from '../models/source-event'
import { ExternalSourceDetails, ExternalSourceRequest } from '../models/external-source-details'
import { EquipmentRequest, SimpleEquipment } from '../models/common/asset-hierarchy/equipment'
import { DescribableEntity } from '@celanese/celanese-sdk'
import { FunctionalLocationRequest } from '../models/common/asset-hierarchy/functional-location'
import { ReportingLocation, ReportingLocationRequest } from '../models/common/asset-hierarchy/reporting-location'
import { ReportingUnit, ReportingUnitRequest } from '../models/common/asset-hierarchy/reporting-unit'
import { ActionItemsRequest } from '../models/action/action-item-request'
import { FilterInfoEventProps } from '@/app/components/EventsComponent/EventFilter'
import { SiteSpecificCategory, SiteSpecificCategoryRequest } from '../models/site-specific-category'

const base_url = `${environment.azureFunctionUrl}/api`

const msalInstance = createPublicClientApplication()

const accounts = msalInstance.getAllAccounts()
if (accounts.length > 0) {
    msalInstance.setActiveAccount(accounts[0])
}

export class AzureFunctionClient {
    constructor() {}

    private buildHeaders(): HeadersInit {
        return {
            'Content-Type': 'application/json',
        }
    }

    async getUser(userRequest: any): Promise<any> {
        const serializedUserRequest = encodeURIComponent(JSON.stringify(userRequest))
        const accessToken = await getIdTokenFromMsal(msalInstance)

        const response = await fetch(`${base_url}/get-user?userRequest=${serializedUserRequest}`, {
            method: 'GET',
            headers: {
                ...this.buildHeaders(),
                Authorization: `${accessToken}`,
            },
        })

        if (!response.ok) {
            const errorText = response.statusText
            throw new Error(`Error get user, status: ${response.status}, details: ${errorText}`)
        }

        return response.json()
    }

    async getUsersByFeature(userRequest: any): Promise<any> {
        const serializedUserRequest = encodeURIComponent(JSON.stringify(userRequest))
        const accessToken = await getIdTokenFromMsal(msalInstance)

        const response = await fetch(`${base_url}/get-users-by-feature?userRequest=${serializedUserRequest}`, {
            method: 'GET',
            headers: {
                ...this.buildHeaders(),
                Authorization: `${accessToken}`,
            },
        })

        if (!response.ok) {
            const errorText = response.statusText
            throw new Error(`Error get user, status: ${response.status}, details: ${errorText}`)
        }

        return response.json()
    }

    async getUserRolesAndTeams<T>(userRequest: any): Promise<any> {
        const serializedUserRequest = encodeURIComponent(JSON.stringify(userRequest))

        const accessToken = await getIdTokenFromMsal(msalInstance, true)

        const response = await fetch(`${base_url}/get-user-roles-and-teams?userRequest=${serializedUserRequest}`, {
            method: 'GET',
            headers: {
                ...this.buildHeaders(),
                Authorization: `${accessToken}`,
            },
        })

        if (!response.ok) {
            const errorText = response.statusText
            throw new Error(`Error getting roles and teams by user, status: ${response.status}, details: ${errorText}`)
        }

        return response.json()
    }

    async createActionItem<T>(actionItemRequest: any): Promise<any> {
        const response = await fetch(`${base_url}/create-action-item-trigger`, {
            method: 'POST',
            headers: this.buildHeaders(),
            body: safeStringify({ actionItemRequest }),
        })

        if (!response.ok) {
            const errorText = response.statusText
            throw new Error(`Error creating action item, status: ${response.status}, details: ${errorText}`)
        }

        return response.json()
    }

    async createSourceEvent<T>(sourceEventRequest: any): Promise<any> {
        const response = await fetch(`${base_url}/create-source-event`, {
            method: 'POST',
            headers: this.buildHeaders(),
            body: safeStringify({ sourceEventRequest }),
        })

        if (!response.ok) {
            const errorText = response.statusText
            throw new Error(`Error creating source event, status: ${response.status}, details: ${errorText}`)
        }

        return response.json()
    }

    async upsertActionApprovalWorkflow(id: string, actionItemRequest: any): Promise<any> {
        const response = await fetch(`${base_url}/update-action-approval-workflow/${id}`, {
            method: 'PATCH',
            headers: this.buildHeaders(),
            body: safeStringify(actionItemRequest),
        })

        if (!response.ok) {
            const errorText = response.statusText
            throw new Error(`Error updating source event, status: ${response.status}, details: ${errorText}`)
        }

        return response.json()
    }

    async updateActionItem<T>(id: string, actionItemRequest: any): Promise<any> {
        const response = await fetch(`${base_url}/update-action/${id}`, {
            method: 'PATCH',
            headers: this.buildHeaders(),
            body: safeStringify(actionItemRequest),
        })

        if (!response.ok) {
            const errorText = response.statusText
            throw new Error(`Error updating action item, status: ${response.status}, details: ${errorText}`)
        }

        return response.json()
    }

    async updateSourceEvent<T>(id: string, sourceEventRequest: any): Promise<any> {
        const response = await fetch(`${base_url}/update-source-event/${id}`, {
            method: 'PATCH',
            headers: this.buildHeaders(),
            body: safeStringify({ sourceEventRequest }),
        })

        if (!response.ok) {
            const errorText = response.statusText
            throw new Error(`Error updating source event, status: ${response.status}, details: ${errorText}`)
        }

        return response.json()
    }

    async updateMultipleActions(actionItemRequests: any[]): Promise<any> {
        const accessToken = await getIdTokenFromMsal(msalInstance)

        const response = await fetch(`${base_url}/update-multiple-actions`, {
            method: 'PATCH',
            headers: this.buildHeaders(),
            body: safeStringify(actionItemRequests),
        })

        if (!response.ok) {
            const errorText = await response.text()
            throw new Error(`Error updating multiple actions, status: ${response.status}, details: ${errorText}`)
        }

        return response.json()
    }

    async upsertAssigneeRequest<T>(id: string, actionItemRequest: any): Promise<any> {
        const response = await fetch(`${base_url}/update-action-assignee-request/${id}`, {
            method: 'PATCH',
            headers: this.buildHeaders(),
            body: safeStringify(actionItemRequest),
        })

        if (!response.ok) {
            const errorText = response.statusText
            throw new Error(`Error updating assignee request, status: ${response.status}, details: ${errorText}`)
        }

        return response.json()
    }

    async createNotification<T>(actionsIds: any): Promise<any> {
        const response = await fetch(`${base_url}/create-notifications-by-actions-ids`, {
            method: 'POST',
            headers: this.buildHeaders(),
            body: safeStringify({ actionsIds }),
        })

        if (!response.ok) {
            const errorText = response.statusText
            throw new Error(`Error creating action item, status: ${response.status}, details: ${errorText}`)
        }

        return response.json()
    }

    async createActionEditNotification<T>(
        actionIds: string[],
        reportingSiteId: string,
        activeUserEmail: string
    ): Promise<any> {
        const response = await fetch(`${base_url}/create-action-edit-notification`, {
            method: 'POST',
            headers: this.buildHeaders(),
            body: safeStringify({ actionIds, reportingSiteId, activeUserEmail }),
        })

        if (!response.ok) {
            const errorText = response.statusText
            throw new Error(`Error sending notification, status: ${response.status}, details: ${errorText}`)
        }
    }

    async getSourceEvent<T>(sourceEventRequest: any): Promise<any> {
        const serializedSourceEventRequest = encodeURIComponent(JSON.stringify(sourceEventRequest))

        const accessToken = await getIdTokenFromMsal(msalInstance, true)

        const response = await fetch(
            `${base_url}/get-home-source-event?sourceEventRequest=${serializedSourceEventRequest}`,
            {
                method: 'GET',
                headers: {
                    ...this.buildHeaders(),
                    Authorization: `${accessToken}`,
                },
            }
        )

        if (!response.ok) {
            const errorText = response.statusText
            throw new Error(`Error getting source events, status: ${response.status}, details: ${errorText}`)
        }

        return response.json()
    }

    async getActionsHome<T>(actionItemRequest: any): Promise<ResponseData> {
        const serializedActionsRequest = encodeURIComponent(JSON.stringify(actionItemRequest))

        const accessToken = await getIdTokenFromMsal(msalInstance, true)

        const response = await fetch(`${base_url}/get-actions-table?actionItemRequest=${serializedActionsRequest}`, {
            method: 'GET',
            headers: {
                ...this.buildHeaders(),
                Authorization: `${accessToken}`,
            },
        })

        if (!response.ok) {
            const errorText = response.statusText
            throw new Error(`Error getting actions, status: ${response.status}, details: ${errorText}`)
        }

        return response.json()
    }

    async bulkUploadActions<T>(
        file: File,
        reportingSiteExternalId: string,
        columns: Record<string, any>,
        userId: string,
        eventId?: string
    ): Promise<any> {
        const accessToken = await getIdTokenFromMsal(msalInstance, true)

        const formData = new FormData()
        formData.append('file', file)
        formData.append('reportingSiteExternalId', reportingSiteExternalId)
        formData.append('columns', JSON.stringify(columns))
        formData.append('userId', userId)
        if (eventId) {
            formData.append('eventId', eventId)
        }

        const response = await fetch(`${base_url}/bulk-upload-actions`, {
            method: 'POST',
            headers: {
                Authorization: `${accessToken}`,
            },
            body: formData,
        })

        if (!response.ok) {
            throw new Error('Failed to upload Excel file')
        }

        return response.json()
    }

    async getBulkUploadTemplate<T>(
        reportingSiteExternalId: string,
        columns: Record<string, Record<string, string>>
    ): Promise<void> {
        const accessToken = await getIdTokenFromMsal(msalInstance, true)

        const response = await fetch(`${base_url}/get-bulk-upload-template`, {
            method: 'POST',
            headers: {
                ...this.buildHeaders(),
                Authorization: `${accessToken}`,
            },
            body: JSON.stringify({ reportingSiteExternalId, columns }),
        })

        if (!response.ok) {
            throw new Error('Failed to fetch Excel template')
        }

        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = 'action_items_template.xlsx'
        document.body.appendChild(a)
        a.click()
        document.body.removeChild(a)
        window.URL.revokeObjectURL(url)
    }

    async exportActionsToExcel(
        actionItemRequest: ActionItemsRequest,
        columns: Record<string, string>,
        fileName: string,
        translations?: Record<string, string>
    ): Promise<void> {
        const accessToken = await getIdTokenFromMsal(msalInstance, true)

        const response = await fetch(`${base_url}/get-export-actions`, {
            method: 'POST',
            headers: {
                ...this.buildHeaders(),
                Authorization: `${accessToken}`,
            },
            body: JSON.stringify({ ...actionItemRequest, columns, translations }),
        })

        if (!response.ok) {
            throw new Error('Failed to fetch Excel template')
        }

        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `${fileName}.xlsx`
        document.body.appendChild(a)
        a.click()
        document.body.removeChild(a)
        window.URL.revokeObjectURL(url)
    }

    async getHomeKpis<T>(aggregateRequest: any): Promise<any> {
        const serializedAggregatesRequest = encodeURIComponent(JSON.stringify(aggregateRequest))

        const accessToken = await getIdTokenFromMsal(msalInstance, true)

        const response = await fetch(`${base_url}/get-home-kpis?aggregateRequest=${serializedAggregatesRequest}`, {
            method: 'GET',
            headers: {
                ...this.buildHeaders(),
                Authorization: `${accessToken}`,
            },
        })

        if (!response.ok) {
            const errorText = response.statusText
            throw new Error(`Error getting home kpis, status: ${response.status}, details: ${errorText}`)
        }

        return response.json()
    }

    async getSiteTabKpis<T>(aggregateRequest: any): Promise<any> {
        const serializedAggregatesRequest = encodeURIComponent(JSON.stringify(aggregateRequest))

        const accessToken = await getIdTokenFromMsal(msalInstance, true)

        const response = await fetch(`${base_url}/get-site-tab-kpis?aggregateRequest=${serializedAggregatesRequest}`, {
            method: 'GET',
            headers: {
                ...this.buildHeaders(),
                Authorization: `${accessToken}`,
            },
        })

        if (!response.ok) {
            const errorText = response.statusText
            throw new Error(`Error getting supervisor tab kpis, status: ${response.status}, details: ${errorText}`)
        }

        return response.json()
    }

    async getSupervisorTabKpis<T>(aggregateRequest: any): Promise<any> {
        const serializedAggregatesRequest = encodeURIComponent(JSON.stringify(aggregateRequest))

        const accessToken = await getIdTokenFromMsal(msalInstance, true)

        const response = await fetch(
            `${base_url}/get-supervisor-tab-kpis?aggregateRequest=${serializedAggregatesRequest}`,
            {
                method: 'GET',
                headers: {
                    ...this.buildHeaders(),
                    Authorization: `${accessToken}`,
                },
            }
        )

        if (!response.ok) {
            const errorText = response.statusText
            throw new Error(`Error getting supervisor tab kpis, status: ${response.status}, details: ${errorText}`)
        }

        return response.json()
    }

    async getActionsGroupedBy<T>(aggregateRequest: any): Promise<any> {
        const serializedAggregatesRequest = encodeURIComponent(JSON.stringify(aggregateRequest))

        const accessToken = await getIdTokenFromMsal(msalInstance, true)

        const response = await fetch(
            `${base_url}/get-actions-grouped-by?aggregateRequest=${serializedAggregatesRequest}`,
            {
                method: 'GET',
                headers: {
                    ...this.buildHeaders(),
                    Authorization: `${accessToken}`,
                },
            }
        )

        if (!response.ok) {
            const errorText = response.statusText
            throw new Error(`Error getting actions grouped by, status: ${response.status}, details: ${errorText}`)
        }

        return response.json()
    }

    async getSourceEventById<T>(sourceEventRequest: any): Promise<any> {
        const serializedSourceEventRequest = encodeURIComponent(JSON.stringify(sourceEventRequest))

        const accessToken = await getIdTokenFromMsal(msalInstance, true)

        const response = await fetch(
            `${base_url}/get-source-event-by-id?sourceEventRequest=${serializedSourceEventRequest}`,
            {
                method: 'GET',
                headers: {
                    ...this.buildHeaders(),
                    Authorization: `${accessToken}`,
                },
            }
        )

        if (!response.ok) {
            const errorText = response.statusText
            throw new Error(`Error getting source events, status: ${response.status}, details: ${errorText}`)
        }

        return response.json()
    }

    async getActionItemById<T>(actionItemRequest: any): Promise<any> {
        const serializedActionItemRequest = encodeURIComponent(JSON.stringify(actionItemRequest))

        const accessToken = await getIdTokenFromMsal(msalInstance, true)

        const response = await fetch(
            `${base_url}/get-action-item-by-id?actionItemRequest=${serializedActionItemRequest}`,
            {
                method: 'GET',
                headers: {
                    ...this.buildHeaders(),
                    Authorization: `${accessToken}`,
                },
            }
        )

        if (!response.ok) {
            const errorText = response.statusText
            throw new Error(`Error getting action item by id, status: ${response.status}, details: ${errorText}`)
        }

        return response.json()
    }

    async getTemplatesByUserId<T>(templateRequest: any): Promise<any> {
        const serializedTemplateRequest = encodeURIComponent(JSON.stringify(templateRequest))

        const accessToken = await getIdTokenFromMsal(msalInstance, true)

        const response = await fetch(
            `${base_url}/get-templates-by-user-id?templateRequest=${serializedTemplateRequest}`,
            {
                method: 'GET',
                headers: {
                    ...this.buildHeaders(),
                    Authorization: `${accessToken}`,
                },
            }
        )

        if (!response.ok) {
            const errorText = response.statusText
            throw new Error(`Error getting templates configurations, status: ${response.status}, details: ${errorText}`)
        }

        return response.json()
    }

    async getActionByTemplateConfigId<T>(actionItemRequest: any): Promise<any> {
        const serializedActionItemRequest = encodeURIComponent(JSON.stringify(actionItemRequest))

        const accessToken = await getIdTokenFromMsal(msalInstance, true)

        const response = await fetch(
            `${base_url}/get-action-item-by-template-id?actionItemRequest=${serializedActionItemRequest}`,
            {
                method: 'GET',
                headers: {
                    ...this.buildHeaders(),
                    Authorization: `${accessToken}`,
                },
            }
        )

        if (!response.ok) {
            const errorText = response.statusText
            throw new Error(
                `Error getting action item by template config id, status: ${response.status}, details: ${errorText}`
            )
        }

        return response.json()
    }

    async getCategoryConfigurationBySite<T>(categoryConfigurationRequest: any): Promise<CategoryConfigurationData[]> {
        const serializedActionItemRequest = encodeURIComponent(JSON.stringify(categoryConfigurationRequest))

        const accessToken = await getIdTokenFromMsal(msalInstance, true)

        const response = await fetch(
            `${base_url}/get-all-category-configurations?categoryConfigurationRequest=${serializedActionItemRequest}`,
            {
                method: 'GET',
                headers: {
                    ...this.buildHeaders(),
                    Authorization: `${accessToken}`,
                },
            }
        )

        if (!response.ok) {
            const errorText = response.statusText
            throw new Error(`Error getting action item by id, status: ${response.status}, details: ${errorText}`)
        }

        return response.json()
    }

    async getCategoryConfigurationByConfig<T>(categoryConfigurationRequest: any): Promise<CategoryConfigurationData> {
        const serializedActionItemRequest = encodeURIComponent(JSON.stringify(categoryConfigurationRequest))

        const accessToken = await getIdTokenFromMsal(msalInstance, true)

        const response = await fetch(
            `${base_url}/get-category-configuration-by-config?categoryConfigurationRequest=${serializedActionItemRequest}`,
            {
                method: 'GET',
                headers: {
                    ...this.buildHeaders(),
                    Authorization: `${accessToken}`,
                },
            }
        )

        if (!response.ok) {
            const errorText = response.statusText
            throw new Error(`Error getting action item by id, status: ${response.status}, details: ${errorText}`)
        }

        return response.json()
    }

    async getUserComplementsByUnits<T>(userComplementRequest: any): Promise<any> {
        const serializedUserComplementRequest = encodeURIComponent(JSON.stringify(userComplementRequest))

        const accessToken = await getIdTokenFromMsal(msalInstance, true)

        const response = await fetch(
            `${base_url}/get-user-complements_by-units?userComplementRequest=${serializedUserComplementRequest}`,
            {
                method: 'GET',
                headers: {
                    ...this.buildHeaders(),
                    Authorization: `${accessToken}`,
                },
            }
        )

        if (!response.ok) {
            const errorText = response.statusText
            throw new Error(
                `Error getting user complements by units, status: ${response.status}, details: ${errorText}`
            )
        }

        return response.json()
    }

    async getUserComplementsByLocations<T>(userComplementRequest: any): Promise<any> {
        const serializedUserComplementRequest = encodeURIComponent(JSON.stringify(userComplementRequest))

        const accessToken = await getIdTokenFromMsal(msalInstance, true)

        const response = await fetch(
            `${base_url}/get-user-complements_by-locations?userComplementRequest=${serializedUserComplementRequest}`,
            {
                method: 'GET',
                headers: {
                    ...this.buildHeaders(),
                    Authorization: `${accessToken}`,
                },
            }
        )

        if (!response.ok) {
            const errorText = response.statusText
            throw new Error(
                `Error getting user complements by locations, status: ${response.status}, details: ${errorText}`
            )
        }

        return response.json()
    }

    async uploadFiles<T>(fileRequest: any): Promise<any> {
        const accessToken = await getIdTokenFromMsal(msalInstance, true)

        const response = await fetch(`${base_url}/upload-files`, {
            method: 'POST',
            headers: {
                Authorization: `${accessToken}`,
            },
            body: fileRequest,
        })

        if (!response.ok) {
            const errorText = response.statusText
            throw new Error(`Error uploading files, status: ${response.status}, details: ${errorText}`)
        }

        return response.json()
    }

    async getDownloadUrls<T>(fileRequest: any): Promise<any> {
        const serializedFileRequest = encodeURIComponent(JSON.stringify(fileRequest))

        const accessToken = await getIdTokenFromMsal(msalInstance, true)

        const response = await fetch(`${base_url}/get-files-download-urls?fileRequest=${serializedFileRequest}`, {
            method: 'GET',
            headers: {
                Authorization: `${accessToken}`,
            },
        })

        if (!response.ok) {
            const errorText = response.statusText
            throw new Error(`Error getting download urls, status: ${response.status}, details: ${errorText}`)
        }

        return response.json()
    }

    async deleteActionRequest(actionItemRequest: any): Promise<any> {
        const response = await fetch(`${base_url}/delete-actions`, {
            method: 'put',
            headers: this.buildHeaders(),
            body: safeStringify(actionItemRequest),
        })

        if (!response.ok) {
            const errorText = response.statusText
            throw new Error(`Error delete action item, status: ${response.status}, details: ${errorText}`)
        }

        return response.json()
    }

    async cancelActionRequest(actionItemRequest: any): Promise<any> {
        const response = await fetch(`${base_url}/cancel-actions`, {
            method: 'put',
            headers: this.buildHeaders(),
            body: safeStringify(actionItemRequest),
        })

        if (!response.ok) {
            const errorText = response.statusText
            throw new Error(`Error cancel action item, status: ${response.status}, details: ${errorText}`)
        }

        return response.json()
    }

    async updateSourceEventStatusRequest(sourceEventRequest: any): Promise<any> {
        const response = await fetch(`${base_url}/update-source-event-status`, {
            method: 'patch',
            headers: this.buildHeaders(),
            body: safeStringify(sourceEventRequest),
        })

        if (!response.ok) {
            const errorText = response.statusText
            throw new Error(`Error delete action item, status: ${response.status}, details: ${errorText}`)
        }

        return response.json()
    }

    async createActionComment<T>(actionCommentRequest: CommentRequest): Promise<any> {
        const response = await fetch(`${base_url}/create-action-comment`, {
            method: 'POST',
            headers: this.buildHeaders(),
            body: safeStringify(actionCommentRequest),
        })

        if (!response.ok) {
            const errorText = response.statusText
            throw new Error(`Error creating action comment, status: ${response.status}, details: ${errorText}`)
        }

        return response.json()
    }

    async getSourceEventTitle<T>(
        sourceEventTitleRequest: SourceEventTitlesRequest
    ): Promise<SourceEventTitlesResponse> {
        const serializedSourceEventTitleRequest = encodeURIComponent(JSON.stringify(sourceEventTitleRequest))
        const accessToken = await getIdTokenFromMsal(msalInstance, true)

        const response = await fetch(
            `${base_url}/get-source-events-title?sourceEventTitleRequest=${serializedSourceEventTitleRequest}`,
            {
                method: 'GET',
                headers: {
                    ...this.buildHeaders(),
                    Authorization: `${accessToken}`,
                },
            }
        )

        if (!response.ok) {
            const errorText = response.statusText
            throw new Error(`Error getting source events title, status: ${response.status}, details: ${errorText}`)
        }

        return response.json()
    }

    async getExternalSourceDetails(externalSourceRequest: ExternalSourceRequest): Promise<ExternalSourceDetails> {
        const serializedActionsRequest = encodeURIComponent(JSON.stringify(externalSourceRequest))

        const accessToken = await getIdTokenFromMsal(msalInstance, true)

        const response = await fetch(
            `${base_url}/get-external-source-details?externalSourceRequest=${serializedActionsRequest}`,
            {
                method: 'GET',
                headers: {
                    ...this.buildHeaders(),
                    Authorization: `${accessToken}`,
                },
            }
        )

        if (!response.ok) {
            const errorText = response.statusText
            throw new Error(`Error getting external source details, status: ${response.status}, details: ${errorText}`)
        }

        return response.json()
    }

    async getFunctionalLocations<T>(
        functionalLocationRequest: FunctionalLocationRequest
    ): Promise<DescribableEntity[]> {
        const serializedRequest = encodeURIComponent(JSON.stringify(functionalLocationRequest))

        const accessToken = await getIdTokenFromMsal(msalInstance, true)

        const response = await fetch(
            `${base_url}/get-functional-locations?functionalLocationRequest=${serializedRequest}`,
            {
                method: 'GET',
                headers: {
                    ...this.buildHeaders(),
                    Authorization: `${accessToken}`,
                },
            }
        )

        if (!response.ok) throw new Error()

        return response.json()
    }

    async getEquipments<T>(equipmentRequest: EquipmentRequest): Promise<SimpleEquipment[]> {
        const serializedRequest = encodeURIComponent(JSON.stringify(equipmentRequest))

        const accessToken = await getIdTokenFromMsal(msalInstance, true)

        const response = await fetch(`${base_url}/get-equipments?equipmentRequest=${serializedRequest}`, {
            method: 'GET',
            headers: {
                ...this.buildHeaders(),
                Authorization: `${accessToken}`,
            },
        })

        if (!response.ok) throw new Error()

        return response.json()
    }

    async getSiteSpecificCategories(
        siteSpecificCategories: SiteSpecificCategoryRequest
    ): Promise<SiteSpecificCategory[]> {
        const serializedRequest = encodeURIComponent(JSON.stringify(siteSpecificCategories))

        const accessToken = await getIdTokenFromMsal(msalInstance, true)

        const response = await fetch(
            `${base_url}/get-site-specific-categories-by-sites?siteSpecificCategoriesRequest=${serializedRequest}`,
            {
                method: 'GET',
                headers: {
                    ...this.buildHeaders(),
                    Authorization: `${accessToken}`,
                },
            }
        )

        if (!response.ok) throw new Error()

        return response.json()
    }

    async getReportingUnits(reportingUnitsRequest: ReportingUnitRequest): Promise<ReportingUnit[]> {
        const serializedRequest = encodeURIComponent(JSON.stringify(reportingUnitsRequest))

        const accessToken = await getIdTokenFromMsal(msalInstance, true)

        const response = await fetch(`${base_url}/get-units-by-sites?reportingUnitsRequest=${serializedRequest}`, {
            method: 'GET',
            headers: {
                ...this.buildHeaders(),
                Authorization: `${accessToken}`,
            },
        })

        if (!response.ok) throw new Error()

        return response.json()
    }

    async getReportingLocations(reportingLocationsRequest: ReportingLocationRequest): Promise<ReportingLocation[]> {
        const serializedRequest = encodeURIComponent(JSON.stringify(reportingLocationsRequest))

        const accessToken = await getIdTokenFromMsal(msalInstance, true)

        const response = await fetch(
            `${base_url}/get-locations-by-sites?reportingLocationsRequest=${serializedRequest}`,
            {
                method: 'GET',
                headers: {
                    ...this.buildHeaders(),
                    Authorization: `${accessToken}`,
                },
            }
        )

        if (!response.ok) throw new Error()

        return response.json()
    }

    async exportSourceEventsToExcel(
        request: FilterInfoEventProps,
        columns: Record<string, string>,
        fileName: string,
        translations?: Record<string, string>
    ): Promise<void> {
        const accessToken = await getIdTokenFromMsal(msalInstance, true)

        const response = await fetch(`${base_url}/source-event/export`, {
            method: 'POST',
            headers: {
                ...this.buildHeaders(),
                Authorization: accessToken,
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ filters: request, columns, translations }),
        })

        if (!response.ok) {
            const errorBody = await response.text()
            throw new Error(
                `Failed to export source events. Status: ${response.status} ${response.statusText}. Details: ${errorBody}`
            )
        }

        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `${fileName}.xlsx`
        document.body.appendChild(a)
        a.click()
        document.body.removeChild(a)
        window.URL.revokeObjectURL(url)
    }
}
