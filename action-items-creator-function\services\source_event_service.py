import asyncio
from datetime import UTC, datetime
from http import HTT<PERSON><PERSON>us
from http.client import <PERSON>TT<PERSON><PERSON>x<PERSON>
from io import By<PERSON><PERSON>
from typing import Optional

import azure.functions as func
from openpyxl import Workbook

from clients.action_item_client import ActionItemClient
from clients.actions.models import ActionItemUpdate, StatusHistoryInstance
from clients.actions.requests import GetActionRequest
from clients.approval_workflow.constants import HistoryActionUser
from clients.core.constants import AGGREGATE_LIMIT, ActionStatusEnum, DataSpaceEnum
from clients.core.models import Node, PaginatedData
from clients.source_event.constants import EVENT_EXPORT_FIELD_MAP, SourceEventStatus
from clients.source_event.models import (
    SourceEventByIdResult,
    SourceEventExportResponse,
    SourceEventExportView,
    SourceEventHistoryUpdate,
    SourceEventResult,
    SourceEventStatusUpdate,
    SourceEventWithActionsResult,
)
from clients.source_event.requests import (
    ExportSourceEventsRequest,
    GetSourceEventByIdRequest,
    GetSourceEventRequest,
    GetSourceEventRequestForIndustrialModel,
    UpdateSourceEventStatusRequest,
)
from clients.user_complement.models import UserByEmailResult
from clients.user_complement.requests import GetUserRolesAndTeamsRequest
from models.entities_enum import EntitiesEnum
from services.action_service import ActionService
from services.user_complements_service import UserComplementsService
from utils.id_generation_utils import IdGenerator


class SourceEventService:
    """A service class responsible for managing source events."""

    def __init__(
        self,
        action_item_client: ActionItemClient,
    ) -> None:
        """Initialize the SourceEventService."""
        self._action_item_client = action_item_client
        self._notification_service = action_item_client.actions._notification_service
        self._log = action_item_client.actions._logging
        self._now_datetime = datetime.now(tz=UTC)
        self._now_datetime_str = self._now_datetime.date().strftime("%Y-%m-%d")
        self._id_generator = IdGenerator[EntitiesEnum](self._now_datetime)
        self._settings = action_item_client.actions._settings

    async def get_source_event_by_site(
        self,
        request: GetSourceEventRequest,
    ) -> tuple[Optional[PaginatedData[SourceEventResult]], int]:
        """
        Retrieve source events for a given reporting site.

        This method fetches source events based on the provided request parameters,
        including filtering by active user and aggregating related action data.

        Args:
            request (GetSourceEventRequest):
                The request object containing filters such as reporting site ID
                and active user details.

        Returns:
            tuple[Optional[PaginatedData[SourceEventResult]], int]:
                A tuple where:
                - The first element is a `PaginatedData` object containing the list of
                source events if found, or `None` if no events are retrieved.
                - The second element is the total count of source events.

        """
        try:
            user_auth_result = await self._authenticate_user(request)
            if isinstance(user_auth_result, func.HttpResponse):
                return None, 0

            events: PaginatedData[SourceEventResult] = (
                self._action_item_client.source_event.get_source_events(request)
            )
            total: int = self._action_item_client.source_event.count_source_events(
                request,
            )

            list_source_event_ids = [event.external_id for event in events.data]

            total_complete_actions_by_source_id = (
                await self._get_aggregate_actions_by_event_id(
                    request,
                    list_source_event_ids,
                    action_completed=True,
                )
            )

            source_id_to_completed_actions = {
                item["group"]["sourceId"]: (
                    next(
                        (aggregate["value"] for aggregate in item["aggregates"]),
                        None,
                    )
                    if item["aggregates"]
                    else None
                )
                for item in total_complete_actions_by_source_id
            }

            total_actions_by_source_id = await self._get_aggregate_actions_by_event_id(
                request,
                list_source_event_ids,
            )

            source_id_to_actions = {
                item["group"]["sourceId"]: (
                    next(
                        (aggregate["value"] for aggregate in item["aggregates"]),
                        None,
                    )
                    if item["aggregates"]
                    else None
                )
                for item in total_actions_by_source_id
            }

            for event in events.data:
                aggregate_total_actions_value = source_id_to_actions.get(
                    event.external_id,
                )
                aggregate_completed_actions_value = source_id_to_completed_actions.get(
                    event.external_id,
                )
                event.set_aggregate_value(
                    aggregate_total_actions_value,
                    aggregate_completed_actions_value,
                )

        except Exception as err:
            self._log.error(
                f"Error getting source event items by site: {request.reporting_site_external_id}. Error: {err}",
            )
            return None, 0
        else:
            return events, total

    async def get_source_event_by_id(
        self,
        request: GetSourceEventByIdRequest,
    ) -> Optional[SourceEventByIdResult]:
        """
        Retrieve a source event by its ID.

        This method fetches details of a source event based on the provided request,
        ensuring the active user is identified before querying the data.

        Args:
            request (GetSourceEventByIdRequest):
                The request object containing the external ID of the source event
                and user-related information.

        Returns:
            Optional[SourceEventByIdResult]:
                The source event details if found, otherwise None.

        """
        try:
            user_auth_result = await self._authenticate_user(request)
            if isinstance(user_auth_result, func.HttpResponse):
                return None

            events: PaginatedData[SourceEventByIdResult] = (
                self._action_item_client.source_event.get_source_event_by_id(request)
            )

            if len(events.data) < 1:
                return None

            return events.data[0]
        except Exception as err:
            self._log.error(
                f"Error get action item by ID: {request.external_id}. Error: {err}",
            )
            return None

    async def get_source_events_with_actions(
        self,
        request: GetSourceEventRequest,
    ) -> list[SourceEventWithActionsResult]:
        """
        Retrieve a list of source events along with their associated actions.

        This method fetches source events based on the provided request.

        Args:
            request (GetSourceEventRequest):
                The request object containing details about the source event and user.

        Returns:
            list[SourceEventWithActionsResult]:
                A list of source events with their associated actions.
                Returns an empty list if no events are found or if an error occurs.

        """
        try:
            events: PaginatedData[SourceEventWithActionsResult] = (
                self._action_item_client.source_event.get_source_events_with_actions(
                    request,
                )
            )

        except Exception as err:
            self._log.error(
                f"Unexpected error while retrieving events with actions. Error: {err}",
            )
            return []
        else:
            return events.data

    async def update_source_event_status(
        self,
        request: UpdateSourceEventStatusRequest,
    ) -> tuple[str, str | None]:
        """
        Update a source event's status and optionally its related actions and histories.

        Args:
            request (UpdateSourceEventStatusRequest): Contains the event data and optional filters for actions.

        Returns:
            tuple[str, str | None]: The source event external ID and an optional error message if the update failed.

        """
        try:
            user = await self._get_user_by_email(
                request.active_user_email,
                request.reporting_site_external_id,
            )
            if user is None:
                msg = "User not found"
                raise ValueError(msg)

            actions_to_update: list[ActionItemUpdate] = []
            actions_histories: list[StatusHistoryInstance] = []

            if request.actions_filters:
                filters = request.actions_filters
                filters.active_user_email = None
                filters.active_user = user.external_id
                filters.active_user_roles_ids = user.active_user_roles_ids
                filters.active_user_teams_ids = user.active_user_teams_ids
                filters.source_ids = [request.external_id]

                action_service = ActionService(self._action_item_client)
                actions = await action_service.get_all_actions_by_site(filters)
                actions_refs = [a.to_external_ref() for a in actions]

                actions_histories = self._build_actions_histories(
                    actions_refs,
                    user.external_id,
                )
                actions_to_update = self._build_actions_to_update(
                    actions_refs,
                    actions_histories,
                )

            source_event_to_update = self._build_source_event_to_update(request)
            source_event_history = self._build_source_event_history(
                request,
                user.external_id,
            )

            self._action_item_client.source_event.update_source_event_and_actions_with_history(
                source_event_to_update,
                source_event_history,
                actions_to_update,
                actions_histories,
            )

        except Exception as err:
            self._log.error(
                f"Error updating source event {request.external_id}: {err}",
            )
            return (
                request.external_id,
                f"Failed to update source event {request.external_id}.",
            )

        return request.external_id, None

    async def _get_user_by_email(
        self,
        email: str,
        site_external_id: str | list[str],
    ) -> Optional[UserByEmailResult]:
        """
        Retrieve a user by email and site, returning None if no match is found.

        This method searches for a user based on the provided email and site external ID.
        If the user exists, their details are returned.

        Args:
            email (str):
                The email of the user.
            site_external_id (str | list[str]):
                The external ID(s) of the reporting site(s).

        Returns:
            Optional[UserByEmailResult]:
                The user details if found, otherwise None.

        """
        user_by_email_request = GetUserRolesAndTeamsRequest(
            email=email,
            reporting_site_external_id=site_external_id,
        )

        try:
            user_service = UserComplementsService(self._action_item_client)
            user: Optional[UserByEmailResult] = await user_service.get_user_by_email(
                user_by_email_request,
            )

            if user is None:
                self._raise_unauthorized(email)
        except Exception:
            self._raise_unauthorized(email)
        else:
            return user

    async def _get_aggregate_actions_by_event_id(
        self,
        request: GetSourceEventRequest | GetSourceEventRequestForIndustrialModel,
        list_source_event_ids: list[str],
        action_completed: bool = False,
    ) -> list[dict]:
        """
        Retrieve aggregated actions by a list of event IDs.

        This method queries the action service to fetch aggregated action counts
        associated with the provided source event IDs. Optionally, the results can be
        filtered by the "completed" status if specified.

        Args:
            request (GetSourceEventRequest):
                The request object containing parameters such as reporting site external ID,
                active user, roles, and teams.
            list_source_event_ids (list[str]):
                A list of source event IDs for which aggregated actions should be retrieved.
            action_completed (bool, optional):
                A flag indicating whether the query should filter for completed actions.
                Defaults to False.

        Returns:
            list[dict]:
                A list of dictionaries representing the aggregated actions by source event ID.
                Returns an empty list if an error occurs.

        Raises:
            Exception:
                Logs an error and returns an empty list if any exception is raised during the operation.

        """
        action_request = GetActionRequest(
            reporting_site_external_id=request.reporting_site_external_id,
            active_user=request.active_user,
            active_user_roles_ids=request.active_user_roles_ids,
            active_user_teams_ids=request.active_user_teams_ids,
            source_ids=list_source_event_ids,
            status_external_ids=(
                [ActionStatusEnum.COMPLETED]
                if action_completed
                else [
                    status
                    for status in ActionStatusEnum
                    if status
                    not in {
                        ActionStatusEnum.DELETED,
                        ActionStatusEnum.ACTIVE,
                        ActionStatusEnum.INACTIVE,
                    }
                ]
            ),
        )

        try:
            action_service = ActionService(self._action_item_client)
            total_actions_complete = (
                await action_service.get_aggregate_actions_by_source_id_by_site(
                    action_request,
                )
            )

        except Exception as err:
            self._log.error(
                f"Error getting aggregate actions by event id. Error: {err}",
            )
            return []
        else:
            return total_actions_complete

    def _build_source_event_to_update(
        self,
        request: UpdateSourceEventStatusRequest,
    ) -> SourceEventStatusUpdate:
        """
        Build a SourceEventStatusUpdate object based on the provided request data.

        This method constructs a SourceEventStatusUpdate object using the provided
        UpdateSourceEventStatusRequest. It also retrieves a sort value from the
        action item client to set the `sort_status` field of the update.

        Args:
            request (UpdateSourceEventStatusRequest):
                The request data containing details about the source event and its status.

        Returns:
            SourceEventStatusUpdate:
                The constructed SourceEventStatusUpdate object containing the external ID, space,
                status, and the corresponding sort value for the source event.

        """
        sort_mapper = self._action_item_client.sort_mapper.get_sort_mapper()

        return SourceEventStatusUpdate(
            external_id=request.external_id,
            space=request.space,
            status=Node(
                external_id=request.status_id,
                space=DataSpaceEnum.AIM_REF_DATA_SPACE,
            ),
            sort_status=sort_mapper.get_source_event_status_sort_value(
                request.status_id,
            ),
        )

    def _build_source_event_history(
        self,
        request: UpdateSourceEventStatusRequest,
        active_user: str,
    ) -> SourceEventHistoryUpdate:
        """
        Build a SourceEventHistoryUpdate object based on the provided request data.

        This method creates a SourceEventHistoryUpdate object, including the current timestamp,
        status (either 'completed' or 'cancelled'), and other necessary fields for the history update.

        Args:
            request (UpdateSourceEventStatusRequest):
                The request data containing details about the source event, status, and user.
            active_user (str):
                The ID of the active user performing the update.

        Returns:
            SourceEventHistoryUpdate:
                The constructed SourceEventHistoryUpdate object, including external ID, status, user information,
                and timestamp of the change.

        """
        current_time = self._now_datetime.replace(microsecond=0).isoformat()

        status = (
            "completed"
            if request.status_id == SourceEventStatus.COMPLETED
            else "cancelled"
        )

        return SourceEventHistoryUpdate(
            external_id=self._id_generator.next_id(EntitiesEnum.SourceEventHistory),
            space=request.space,
            source_event=Node(external_id=request.external_id, space=request.space),
            status=status,
            comments="",
            update_by=Node(
                external_id=active_user,
                space=self._settings.user_management_instances_space,
            ),
            changed_at=current_time,
        )

    def _build_actions_to_update(
        self,
        actions_refs: list[dict[str, str]],
        actions_histories: list[StatusHistoryInstance],
    ) -> list[ActionItemUpdate]:
        """
        Build a list of actions to update with their associated status histories.

        Args:
            actions_refs (list[dict[str, str]]): List of action references with 'external_id' and 'space'.
            actions_histories (list[StatusHistoryInstance]): History instances related to actions.

        Returns:
            list[ActionItemUpdate]: List of actions to be updated.

        """
        sort_mapper = self._action_item_client.sort_mapper.get_sort_mapper()
        action_service = ActionService(self._action_item_client)

        def find_history(external_id: str, space: str) -> StatusHistoryInstance | None:
            return next(
                (
                    h
                    for h in actions_histories
                    if h.action.external_id == external_id and h.action.space == space
                ),
                None,
            )

        actions_to_update = []
        for ref in actions_refs:
            external_id = ref.get("external_id", "")
            space = ref.get("space", "")
            history = find_history(external_id, space)

            action = action_service.generate_upsert_action(
                external_id=external_id,
                space=space,
                history=[history] if history else [],
                status_id=ActionStatusEnum.CANCELLED,
                sort_mapper=sort_mapper,
            )

            actions_to_update.append(action)

        return actions_to_update

    def _build_actions_histories(
        self,
        actions_refs: list[dict[str, str]],
        active_user_id: str,
    ) -> list[StatusHistoryInstance]:
        """
        Build a list of status history instances for a list of actions.

        Args:
            actions_refs (list[dict[str, str]]): List of action references with 'external_id' and 'space'.
            active_user_id (str): ID of the user performing the action.

        Returns:
            list[StatusHistoryInstance]: List of history instances to be used in the update.

        """
        action_service = ActionService(self._action_item_client)

        return [
            action_service.generate_upsert_status_history_instance(
                external_id=ref.get("external_id", ""),
                space=ref.get("space", ""),
                status_id=ActionStatusEnum.CANCELLED,
                action_user=HistoryActionUser.CANCELLED,
                comment="",
                created_by_id=active_user_id,
            )
            for ref in actions_refs
        ]

    async def get_source_events_title(
        self,
        request: GetSourceEventRequest,
    ) -> list[str]:
        """
        Retrieve a list of source event titles based on the provided request.

        This method attempts to retrieve the source event titles for the given request. It first checks
        if the active user exists based on the provided email and site ID, and if not, returns an empty list.
        If successful, it fetches and returns the list of titles from the action item client.

        Args:
            request (GetSourceEventRequest):
                The request data containing details about the active user, site, and event criteria.

        Returns:
            list[str]:
                A list of source event titles if successful, otherwise an empty list in case of errors or no events found.

        """
        try:
            if request.active_user_email:
                user = await self._get_user_by_email(
                    request.active_user_email,
                    request.reporting_site_external_id or "",
                )

                if user is None:
                    return []

                request.active_user = user.external_id
                request.active_user_roles_ids = user.active_user_roles_ids
                request.active_user_teams_ids = user.active_user_teams_ids
            else:
                return []

            return self._action_item_client.source_event.get_source_events_title(
                request,
            )
        except Exception as err:
            self._log.error(f"Error retrieving source event titles. Error: {err}")
            return []

    def _raise_unauthorized(self, email: str) -> None:
        self._log.error(f"No user found with email: {email}")
        raise HTTPException(status_code=HTTPStatus.UNAUTHORIZED)

    async def export_source_events_to_excel(
        self,
        request: ExportSourceEventsRequest,
    ) -> func.HttpResponse:
        """Generate an Excel file with source events based on the provided request."""
        try:
            user_auth_result = await self._authenticate_user(request.filters)
            if isinstance(user_auth_result, func.HttpResponse):
                return user_auth_result

            source_event_request = (
                GetSourceEventRequestForIndustrialModel.model_validate(request.filters)
            )

            all_events = await self._action_item_client.source_event.get_source_events_from_industrial_model(
                source_event_request,
            )

            events = await self._collect_aggregated_events_data(request, all_events)

            workbook = self._create_excel_file(events, request.columns)
            return self._generate_file_response(workbook)

        except Exception as err:
            self._log.error(
                f"Unexpected error generating Excel file for source events: {err}",
            )
            return func.HttpResponse(
                "Failed to generate Excel file.",
                status_code=HTTPStatus.INTERNAL_SERVER_ERROR,
            )

    async def _authenticate_user(
        self,
        request: GetSourceEventRequest | GetSourceEventRequestForIndustrialModel,
    ) -> func.HttpResponse | UserByEmailResult:
        if not request.active_user_email:
            return func.HttpResponse(
                "Unauthorized: active_user_email is required.",
                status_code=HTTPStatus.UNAUTHORIZED,
            )

        user = await self._get_user_by_email(
            request.active_user_email,
            request.reporting_site_external_ids,
        )

        if user is None:
            self._log.warning(
                f"Unauthorized access attempt: user '{request.active_user_email}' not found.",
            )
            return func.HttpResponse(
                "Unauthorized: user not found.",
                status_code=HTTPStatus.UNAUTHORIZED,
            )

        request.active_user = user.external_id
        request.active_user_roles_ids = user.active_user_roles_ids
        request.active_user_teams_ids = user.active_user_teams_ids

        return user

    async def _collect_aggregated_events_data(
        self,
        request: ExportSourceEventsRequest,
        all_events: list[SourceEventExportView],
    ) -> list[SourceEventExportResponse]:
        if not all_events:
            return []

        source_event_ids = [event.external_id for event in all_events]

        (
            completed_by_source_id,
            total_by_source_id,
        ) = await self._get_aggregates_in_batches(
            request.filters,
            source_event_ids,
        )

        return [
            event.set_aggregate_value(
                total_by_source_id.get(event.external_id),
                completed_by_source_id.get(event.external_id),
            )
            or event.to_response(request.translations)
            for event in all_events
        ]

    async def _get_aggregates_in_batches(
        self,
        filters: GetSourceEventRequestForIndustrialModel,
        source_event_ids: list[str],
        batch_size: int = AGGREGATE_LIMIT,
    ) -> tuple[dict, dict]:
        def map_source_id_to_aggregate(
            aggregates_data: list[dict],
        ) -> dict:
            return {
                item["group"]["sourceId"]: (
                    next((agg["value"] for agg in item["aggregates"]), None)
                    if item.get("aggregates")
                    else None
                )
                for item in aggregates_data
            }

        async def process_batch(batch_ids: list[str]) -> tuple[list, list]:
            return await asyncio.gather(
                self._get_aggregate_actions_by_event_id(
                    filters,
                    batch_ids,
                    action_completed=True,
                ),
                self._get_aggregate_actions_by_event_id(filters, batch_ids),
            )

        completed_aggregates = []
        total_aggregates = []

        for i in range(0, len(source_event_ids), batch_size):
            batch_ids = source_event_ids[i : i + batch_size]
            completed_batch, total_batch = await process_batch(batch_ids)
            completed_aggregates.extend(completed_batch)
            total_aggregates.extend(total_batch)

        return map_source_id_to_aggregate(
            completed_aggregates,
        ), map_source_id_to_aggregate(total_aggregates)

    def _create_excel_file(
        self,
        all_data: list[SourceEventExportResponse],
        columns: dict[str, str],
    ) -> Workbook:
        workbook = Workbook()
        ws = workbook.active
        if ws is None:
            msg = "Failed to create a new worksheet in the workbook."
            raise RuntimeError(msg)

        ws.title = "AIM Source Events"

        headers = list(columns.values())
        keys = list(columns.keys())
        ws.append(headers)

        for item in all_data:
            row = [
                str(getattr(item, EVENT_EXPORT_FIELD_MAP.get(key) or "", "") or "")
                for key in keys
            ]
            ws.append(row)

        return workbook

    def _generate_file_response(self, workbook: Workbook) -> func.HttpResponse:
        """Generate an HTTP response to download the Excel workbook."""
        excel_file_buffer = BytesIO()
        workbook.save(excel_file_buffer)
        excel_file_buffer.seek(0)

        headers = {
            "Content-Disposition": "attachment; filename=source_events.xlsx",
            "Content-Type": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            "Access-Control-Allow-Origin": "*",
        }

        return func.HttpResponse(
            excel_file_buffer.read(),
            headers=headers,
            status_code=HTTPStatus.OK,
        )
