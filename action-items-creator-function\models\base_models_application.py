from typing import Optional
from pydantic import BaseModel
import os
import sys
from models.node_reference import PossiblyNewNode
import os
import sys

script_dir = os.path.dirname(os.path.abspath(__file__))
project_dir = os.path.dirname(script_dir)
sys.path.append(project_dir)


class User(PossiblyNewNode):
    first_name: str
    last_name: str
    email: Optional[str] = None


class UserAzureAtribute(PossiblyNewNode):
    user: User


class Attachments(PossiblyNewNode):
    name: Optional[str]
    description: Optional[str]


class GenericalModal(PossiblyNewNode):
    name: Optional[str]
    description: Optional[str]
