import { useEffect, useState } from 'react'
import { User } from '../../models/common/user-management/user'
import { useGetAllResultsFunction } from '../general-functions/useGetAllResultFunction'
import { useGraphqlQuery } from '..'
import { gql } from '@apollo/client'
import { EntityType, GetSpace } from '../../utils/space-util'
import { UserAzureAttribute } from '../../models/common/user-management/user-azure-attribute'
import { useSearchResultsFunction } from '../general-functions/useSearchResultsFunction'
import { useRetrieveResultsFunction } from '../general-functions/useRetrieveResultsFunction'

const PROPERTIES = ['firstName', 'lastName', 'email']

export interface UserSearchRequest {
    search: string
}

export interface UserQueryRequest {
    externalId?: string
}

export const compareUsers = (a: User, b: User): number => {
    const firstNameComparison = (a.firstName ?? '').localeCompare(b.firstName ?? '')
    if (firstNameComparison === 0) {
        return (a.lastName ?? '').localeCompare(b.lastName ?? '')
    }
    return firstNameComparison
}

const buildUserQuery = (request: string): string => {
    const filters: string[] = []

    filters.push(
        `{ space: { eq: "${GetSpace(EntityType.UMG)}" } }`,
        `{ active: { eq: true } }`,
        `{ firstName: { isNull: false } }`,
        `{ lastName: { isNull: false } }`,
        `{ email: { isNull: false } }`
    )

    const queryFilter = `{ and: [ ${filters.join(', ')} ] }`

    return `
        query GetUser {
            searchUser(
                query: "${request}"
                , filter: ${queryFilter}
                , first: 1000
            ) {
                items {
                    externalId
                  	firstName
                    lastName
                  	email
                    space
                    active
                }
            }
        }
    `
}

export const useUsersSearch = (search: string) => {
    const query = buildUserQuery(search)
    const { data: fdmData } = useGraphqlQuery<User>(gql(query), 'searchUser')

    const [resultData, setResultData] = useState<{ data: User[]; loading: boolean }>({
        data: [],
        loading: true,
    })

    useEffect(() => {
        if (fdmData.length == 0) {
            setResultData({ data: [], loading: false })
        } else {
            setResultData({ data: fdmData, loading: false })
        }
    }, [fdmData])

    return {
        loading: resultData.loading,
        users: resultData.data,
    }
}

export interface UserByIdsQueryRequest {
    externalIds?: string[]
}

const queryUserByIds = `
    user {
        email
    }
`

export const useUsersByIds = () => {
    const { getAllResults: getAllData } = useGetAllResultsFunction<UserAzureAttribute>(
        queryUserByIds,
        'listUserAzureAttribute'
    )

    type UserByIdsQueryRequest = {
        externalIds: string[]
    }

    const getEmailsByIds = (request: UserByIdsQueryRequest): Promise<{ usersEmail: string[] }> => {
        return new Promise((resolve, reject) => {
            getAllData({
                and: [request.externalIds ? { externalId: { in: request.externalIds } } : {}],
            })
                .then((res) => {
                    if (res.length === 0) {
                        resolve({ usersEmail: [] })
                    } else {
                        const sortedUsers = res.map((x) => x.user.email ?? '').sort()
                        resolve({ usersEmail: sortedUsers })
                    }
                })
                .catch((error) => {
                    reject(error)
                })
        })
    }

    return {
        getEmailsByIds: getEmailsByIds,
    }
}

export const useUserSearch = (request: UserSearchRequest) => {
    const [userViewVersion, setUserViewVersion] = useState<string>('')
    const [view, setView] = useState<any>()
    const [filter, setFilter] = useState<any>()
    const [resultData, setResultData] = useState<{ data: string[]; loading: boolean }>({
        data: [],
        loading: true,
    })

    const { getAllResults: getAllData } = useSearchResultsFunction()
    const { getAllResults: getAllViews } = useRetrieveResultsFunction()

    useEffect(() => {
        const fetchViews = async () => {
            try {
                const res = await getAllViews()
                const userView = res.find((v) => v.externalId === 'User')
                const userViewVersion = userView?.version

                setView(userView)
                setUserViewVersion(userViewVersion ?? '0')
            } catch (error) {}
        }

        fetchViews()
    }, [])

    useEffect(() => {
        setFilter(buildFilterQuery(userViewVersion))
    }, [request])

    useEffect(() => {
        const performSearch = async () => {
            if (!filter) return

            if (request.search !== '' && view) {
                setResultData({ data: [], loading: true })
                try {
                    const res = await getAllData(view, request.search, 'node', PROPERTIES, filter)
                    const lowerSearchText = request.search.toLowerCase()
                    const externalIds =
                        res?.items
                            .filter((item: any) => {
                                const userProperties = item.properties['UMG-COR-ALL-DMD'][`User/${userViewVersion}`]
                                return (
                                    userProperties.firstName?.toLowerCase().includes(lowerSearchText) ||
                                    userProperties.lastName?.toLowerCase().includes(lowerSearchText) ||
                                    userProperties.email?.toLowerCase().includes(lowerSearchText)
                                )
                            })
                            .map((item: any) => item.externalId) || []
                    setResultData({ data: externalIds, loading: false })
                } catch (error) {
                    setResultData({ data: [], loading: false })
                }
            } else {
                setResultData({ data: [], loading: false })
            }
        }

        performSearch()
    }, [request.search, filter])

    return {
        loading: resultData.loading,
        searchResult: resultData.data,
    }
}

const buildFilterQuery = (userViewVersion: string) => {
    const spaceUMG = 'UMG-COR-ALL-DMD'

    return {
        and: [
            {
                equals: {
                    property: [spaceUMG, `User/${userViewVersion}`, 'active'],
                    value: true,
                },
            },
            {
                exists: {
                    property: [spaceUMG, `User/${userViewVersion}`, 'firstName'],
                },
            },
            {
                exists: {
                    property: [spaceUMG, `User/${userViewVersion}`, 'lastName'],
                },
            },
            {
                exists: {
                    property: [spaceUMG, `User/${userViewVersion}`, 'email'],
                },
            },
        ],
    }
}
