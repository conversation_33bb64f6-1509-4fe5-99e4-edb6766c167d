from datetime import date

from pydantic import Field

from clients.core.models import Node


class EventAnalysisResponse(Node):
    """Represents an event analysis response."""

    external_event_id: str | None = Field(default=None)
    event_title: str | None = Field(default=None)
    event_type: str | None = Field(default=None)
    investigation_start_date: date | None = Field(default=None)
    due_date: date | None = Field(default=None)
    lead_investigator: str | None = Field(default=None)
    investigation_team: list[str] | None = Field(default_factory=list)
    rca_type: str | None = Field(default=None)
    root_causes: list[str] | None = Field(default_factory=list)
    approvers: list[str] | None = Field(default_factory=list)
    reporting_location: str | None = Field(default=None)


class EventInvestigationResponse(Node):
    """Represents an event investigation response."""

    reporting_unit: str | None = Field(default=None)
    business_line: str | None = Field(default=None)
    functional_location: str | None = Field(default=None)
    equipment: str | None = Field(default=None)


class WorkProcessInvestigationResponse(Node):
    """Represents a work process investigation response."""

    reporting_unit: str | None = Field(default=None)
    functional_location: str | None = Field(default=None)
    equipment: str | None = Field(default=None)
    problem_description: str | None = Field(default=None)
