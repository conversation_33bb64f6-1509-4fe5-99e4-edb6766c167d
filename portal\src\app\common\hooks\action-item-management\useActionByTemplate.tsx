import { useEffect, useState } from 'react'
import { Action, Owner } from '../../models/action'
import { EntityType, GetSpace } from '../../utils/space-util'
import { useGetAllResultsFunction } from '..//general-functions/useGetAllResultFunction'
import { Filter } from '../../models/base-hook-request'
import { AllActionStatusExternalIdEnum } from '../../enums/ActionItemStatusEnum'
import { getLocalUserSite } from '@celanese/celanese-ui'

export interface UseActionByTemplateQueryRequest {
    unit?: any[]
    category?: string[]
    owner?: any[]
}

const queryListActionTemplate = `
    title
    space
    externalId
    owner {
        user {
            externalId
            firstName
            lastName
            email
        }
    }
    reportingUnit {
        externalId
        name
        description
    }
    category {
        externalId
        name
        description
        space
    }
    templateConfiguration {
        name
        description
        space
        externalId
        users {
            items {
                externalId
                email
                firstName
                lastName
            }
        }
        roles {
            items {
                externalId
                name
                description
            }
        }
    }
`

export const useActionByTemplate = (request: UseActionByTemplateQueryRequest) => {
    const [resultData, setResultData] = useState<{ data: Action[]; loading: boolean }>({
        data: [],
        loading: true,
    })

    const { getAllResults: getAllData } = useGetAllResultsFunction<Action>(queryListActionTemplate, 'listAction')

    const filter: Filter<Action> = {
        and: [
            {
                space: {
                    eq: GetSpace(EntityType.Instance, getLocalUserSite()?.siteCode),
                },
            },
            {
                not: { templateConfiguration: { externalId: { isNull: true } } },
            },
            {
                currentStatus: {
                    externalId: { in: [AllActionStatusExternalIdEnum.Active, AllActionStatusExternalIdEnum.Inactive] },
                },
            },
            { currentStatus: { externalId: { eq: AllActionStatusExternalIdEnum.Active } } },
            ...(request.unit && request.unit.length > 0
                ? [{ reportingUnit: { externalId: { in: request.unit.map((unit) => unit.externalId) } } }]
                : []),
            ...(request.category && request.category.length > 0
                ? [{ category: { description: { in: request.category } } }]
                : []),
            ...(request.owner && request.owner.length > 0
                ? [{ owner: { user: { externalId: { in: request.owner.map((user) => user.externalId) } } } }]
                : []),
        ],
    }

    const fetchData = async () => {
        const res = await getAllData(filter)
        if (res.length === 0) {
            setResultData({ data: [], loading: false })
        } else {
            setResultData({ data: sortActionByCreatedTimeDesc(res), loading: false })
        }
    }

    useEffect(() => {
        fetchData()
    }, [request])

    const refetch = () => {
        fetchData()
    }

    return {
        loadingTemplate: resultData.loading,
        actions: resultData.data,
        refetchTemplate: refetch,
    }
}

export const useActionByTemplateUser = () => {
    const [resultData, setResultData] = useState<{ data: Owner[]; loading: boolean }>({
        data: [],
        loading: true,
    })

    const { getAllResults: getAllData } = useGetAllResultsFunction<Action>(queryListActionTemplate, 'listAction')

    const filter: Filter<Action> = {
        and: [
            {
                space: {
                    in: [GetSpace(EntityType.Instance, getLocalUserSite()?.siteCode), GetSpace(EntityType.Instance)],
                },
            },
            {
                not: {
                    or: [
                        { templateConfiguration: { externalId: { isNull: true } } },
                        { currentStatus: { externalId: { in: [AllActionStatusExternalIdEnum.Cancelled] } } },
                    ],
                },
            },
            { currentStatus: { externalId: { eq: AllActionStatusExternalIdEnum.Active } } },
        ],
    }

    useEffect(() => {
        getAllData(filter).then((res) => {
            if (res.length == 0) {
                setResultData({ data: [], loading: false })
            } else {
                const actionItems = res
                const owners: Owner[] = []
                actionItems.forEach((action) => {
                    if (action.owner && !owners.includes(action.owner)) {
                        owners.push(action.owner)
                    }
                })
                setResultData({ data: owners, loading: true })
            }
        })
    }, [])

    return {
        loading: resultData.loading,
        owners: resultData.data,
    }
}

const sortActionByCreatedTimeDesc = (actionItems: Action[]): Action[] => {
    const sortedActions = [...actionItems]
    return sortedActions?.sort((a, b) => new Date(b.createdTime).getTime() - new Date(a.createdTime).getTime())
}
