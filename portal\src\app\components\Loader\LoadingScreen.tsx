﻿import React from 'react'
import { Box, Typography } from '@mui/material'
import { LoaderCircularIcon } from '.'
import { translate } from '@/app/common/utils/generate-translate'

export default function LoadingScreen() {
    return (
        <Box
            sx={{
                display: 'flex',
                height: '84vh',
                justifyContent: 'center',
                alignItems: 'center',
                flexDirection: 'column',
                gap: '1.25rem',
            }}
        >
            {LoaderCircularIcon()}
            <Typography
                sx={{
                    fontSize: '1.25rem',
                    textAlign: 'center',
                    color: 'text.primary',
                    fontWeight: '400',
                }}
            >
                {translate('alerts.loading')}
            </Typography>
        </Box>
    )
}
