from functools import lru_cache
from typing import Optional

from cognite.client.data_classes import data_modeling
from industrial_model import AsyncEngine, DataModelId

from clients.core.constants import DataModelIdEnum, DataModelSpaceEnum
from infra.cognite_client_factory import CogniteClientFactory
from infra.graphql_client_factory import GraphqlClientFactory
from models.settings import Settings
from services.notification_service import NotificationService


@lru_cache
def get_settings():
    return Settings.from_env()


@lru_cache
def get_cognite_client_as_service():
    return CogniteClientFactory.create(get_settings())


@lru_cache
def get_graphql_client_as_service():
    return GraphqlClientFactory.create(get_cognite_client_as_service(), get_settings())


@lru_cache(maxsize=128)
def get_cognite_client_as_user(token: str):
    return CogniteClientFactory.create(get_settings(), token)


@lru_cache(maxsize=128)
def check_spaces_user_has_access(view: str, token: Optional[str] = None) -> set[str]:
    if token is None:
        return set[str]()

    view_entity = get_views().get(view)
    if not view_entity:
        return set[str]()

    client = get_cognite_client_as_user(token)

    data = client.data_modeling.instances.aggregate(
        view=view_entity,
        aggregates=data_modeling.aggregations.Count("space"),
        group_by="space",
    )

    return {
        item.group["space"] for item in data if isinstance(item.group["space"], str)
    }


@lru_cache
def get_data_model(
    space: DataModelSpaceEnum = DataModelSpaceEnum.ACTION_DATA_MODEL_SPACE,
    external_id: DataModelIdEnum = DataModelIdEnum.AIM_DATA_MODEL_ID,
):
    client = get_cognite_client_as_service()
    return client.data_modeling.data_models.retrieve(
        ids=(space, external_id),
    ).latest_version()


@lru_cache
def get_views():
    return {view.external_id: view for view in get_data_model().views}


@lru_cache
def get_notification_service():
    return NotificationService(get_cognite_client_as_service(), get_settings())


@lru_cache(maxsize=64)
def get_engine_async(
    user_access_token: str,
) -> AsyncEngine:
    client = get_cognite_client_as_user(user_access_token)
    data_model_view = get_data_model()
    data_model_id = DataModelId(
        external_id=data_model_view.external_id,
        space=data_model_view.space,
        version=data_model_view.version,
    )
    return AsyncEngine(client, data_model_id)
