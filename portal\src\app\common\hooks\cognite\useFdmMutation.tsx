import { useCognite } from './useCognite'
import { useCallback, useState } from 'react'
import { Entity, MutationFunction, MutationFunctionResult, MutationHookResult } from '../../models/common'
import { EdgeInstance } from '../../models/common/cognite/edge-instance'

export function useFdmMutation<T extends Entity>(entityName: string, autoCreateDirectRelations?: boolean): MutationHookResult<T> {
    const { fdmClient: client } = useCognite()
    const [data, setData] = useState<any | undefined>()
    const [error, setError] = useState<any | undefined>()
    const [loading, setLoading] = useState<boolean>(false)
    const [called, setCalled] = useState<boolean>(false)

    const reset = useCallback(() => {
        setData(undefined)
        setError(undefined)
        setLoading(false)
        setCalled(false)
    }, [])

    const mutationFunction: MutationFunction<T> = useCallback(
        (nodes: T[], edges: EdgeInstance[]): Promise<MutationFunctionResult<T>> => {
            setLoading(true)
            setCalled(true)
            if (!nodes?.length) {
                return Promise.resolve({ ok: true })
            }

            if (edges?.length) {
                return client
                    .upsertNodesAndEdges({ entityName, nodes, edges, autoCreateDirectRelations })
                    .then((response) => {
                        setData(response)
                        return {
                            ok: true,
                            data: response,
                        }
                    })
                    .catch((error) => {
                        setError(error)
                        throw error
                    })
                    .finally(() => setLoading(false))
            }

            return client
                .upsertNodes({ entityName, nodes, autoCreateDirectRelations })
                .then((response) => {
                    setData(response)
                    return {
                        ok: true,
                        data: response,
                    }
                })
                .catch((error) => {
                    setError(error)
                    throw error
                })
                .finally(() => setLoading(false))
        },
        [client, entityName, autoCreateDirectRelations]
    )

    return [mutationFunction, { data, error, loading, called, client, reset }]
}
