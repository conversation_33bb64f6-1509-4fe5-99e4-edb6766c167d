from typing import Any, Literal
from cognite.client.data_classes.data_modeling import (
    DirectRelationReference,
    Edge,
    EdgeList,
    Node,
    NodeList,
    ViewId,
)

from clients.core.models import BaseEntity

EdgeDirection = Literal["outwards", "inwards"]


class CogniteDependencyMapper:
    @staticmethod
    def map(
        items: NodeList,
        dependencies: dict[str, NodeList] | None = None,
        edges: dict[str, EdgeList | tuple[EdgeList, EdgeDirection]] | None = None,
    ) -> list[dict[str, Any]]:
        """
        Maps a list of nodes to a structured representation, including dependencies and edges.

        Args:
            items (NodeList): The list of nodes to map.
            dependencies (dict[str, NodeList] | None): Internal dependencies of the nodes, grouped by key.
            edges (dict[str, EdgeList | tuple[EdgeList, EdgeDirection]] | None): Internal edges associated with the nodes.

        Returns:
            list[dict[str, Any]]: A list of mapped nodes with dependencies and edges included.
        """
        internal_dependency = CogniteDependencyMapper._map_internal_dependency(
            dependencies
        )
        internal_edges = CogniteDependencyMapper._map_internal_edges(edges)
        items_nodes = CogniteNodesMapper.from_node_list(items)

        data: list[dict[str, Any]] = []
        for element in items_nodes.values():
            CogniteDependencyMapper._add_element_dependencies(
                element,
                internal_dependency,
                internal_edges,
            )
            data.append(element)

        return data

    @staticmethod
    def _add_element_dependencies(
        element: dict[str, Any],
        internal_dependency: dict[str, dict[str, dict[str, Any]]],
        internal_edges: dict[str, dict[str, set[str]]],
    ):
        """
        Adds dependencies to a node element based on internal mappings.

        Args:
            element (dict[str, Any]): The node element to enrich with dependencies.
            internal_dependency (dict[str, dict[str, dict[str, Any]]]): Mapped internal dependencies.
            internal_edges (dict[str, dict[str, set[str]]]): Mapped internal edges.
        """
        for dependency_key, dependency_values in internal_dependency.items():
            dependency_edge = internal_edges.get(dependency_key)
            is_edge = dependency_edge is not None

            values_to_search: set[str] | None = None
            if is_edge:
                values_to_search = dependency_edge.get(
                    NodeKeyGenerator.generate(element)
                )
            else:
                entry = element.get(dependency_key)
                values_to_search = (
                    set[str]([NodeKeyGenerator.generate(entry)]) if entry else None
                )

            if not values_to_search:
                continue
            entry_data = (dependency_values.get(value) for value in values_to_search)
            element[dependency_key] = (
                [item for item in entry_data if item]
                if is_edge
                else next(entry_data, None)
            )

    @staticmethod
    def _map_internal_dependency(
        dependencies: dict[str, NodeList] | None
    ) -> dict[str, dict[str, dict[str, Any]]]:
        """
        Maps internal dependencies into a structured format.

        Args:
            dependencies (dict[str, NodeList] | None): The internal dependencies to map.

        Returns:
            dict[str, dict[str, dict[str, Any]]]: Mapped dependencies.
        """
        if not dependencies:
            return {}
        return {
            k: CogniteNodesMapper.from_node_list(v) for k, v in dependencies.items()
        }

    @staticmethod
    def _map_internal_edges(
        edges: dict[str, EdgeList | tuple[EdgeList, EdgeDirection]] | None
    ) -> dict[str, dict[str, set[str]]]:
        """
        Maps internal edges into a structured format.

        Args:
            edges (dict[str, EdgeList | tuple[EdgeList, EdgeDirection]] | None): The internal edges to map.

        Returns:
            dict[str, dict[str, set[str]]]: Mapped edges.
        """
        if not edges:
            return {}

        return {key: _make_edge_connectors(v) for key, v in edges.items()}

    @staticmethod
    def include_dependencies(
        items: list[dict[str, Any]], dependencies: dict[str, list[dict[str, Any]]]
    ) -> list[dict[str, Any]]:
        """
        Includes additional dependencies into a list of items.

        Args:
            items (list[dict[str, Any]]): The list of items to enrich.
            dependencies (dict[str, list[dict[str, Any]]]): Dependencies to include.

        Returns:
            list[dict[str, Any]]: Enriched items with added dependencies.
        """
        dependencies_hash = {
            k: {NodeKeyGenerator.generate(i): i for i in v}
            for k, v in dependencies.items()
        }
        for item in items:
            for key, values_hash in dependencies_hash.items():
                entry = item.get(key)
                if entry is None:
                    continue
                if isinstance(entry, dict):
                    entry_key = NodeKeyGenerator.generate(entry)
                    item[key] = values_hash.get(entry_key, entry)
                elif isinstance(entry, list):
                    item[key] = [
                        values_hash.get(
                            NodeKeyGenerator.generate(entry_value), entry_value
                        )
                        for entry_value in entry
                    ]
        return items


class NodeKeyGenerator:
    @staticmethod
    def generate(node: Node | DirectRelationReference | dict[str, Any]) -> str:
        """
        Generates a unique key for a given node.

        Args:
            node (Node | DirectRelationReference | dict[str, Any]): The node to generate a key for.

        Returns:
            str: A unique key for the node.
        """
        return (
            BaseEntity.generate_node_hash(node.external_id, node.space)
            if isinstance(node, Node) or isinstance(node, DirectRelationReference)
            else BaseEntity.generate_node_hash(
                node.get("externalId", ""), node.get("space", "")
            )
        )


class CogniteNodesMapper:
    @staticmethod
    def from_node_list(items: NodeList):
        """
        Maps a list of nodes into a dictionary representation.

        Args:
            items (NodeList): The list of nodes to map.

        Returns:
            dict[str, dict[str, Any]]: Mapped nodes.
        """
        data: dict[str, dict[str, Any]] = {}
        key: ViewId | None = None
        for item in items:
            if not isinstance(item, Node):
                continue
            if not key:
                key = next((key for key in item.properties.keys()), None)
                if not key:
                    continue
            entry = {str(k): v for k, v in item.properties.get(key, {}).items()}
            if not entry:
                continue
            entry["externalId"] = item.external_id
            entry["space"] = item.space

            data[NodeKeyGenerator.generate(item)] = entry
        return data


def _make_edge_connectors(
    edges: EdgeList | tuple[EdgeList, EdgeDirection]
) -> dict[str, set[str]]:
    """
    Creates edge connectors from a list of edges.

    Args:
        edges (EdgeList | tuple[EdgeList, EdgeDirection]): The edges to process.

    Returns:
        dict[str, set[str]]: A mapping of edge connections.
    """
    if isinstance(edges, EdgeList):
        return _make_edge_connectors_internal(edges, "outwards")
    else:
        return _make_edge_connectors_internal(edges[0], edges[1])


def _make_edge_connectors_internal(
    edges: EdgeList, direction: EdgeDirection
) -> dict[str, set[str]]:
    """
    Internal helper to create edge connectors based on a direction.

    Args:
        edges (EdgeList): The edges to process.
        direction (EdgeDirection): The direction of the edges ("outwards" or "inwards").

    Returns:
        dict[str, set[str]]: A mapping of edge connections.
    """
    result: dict[str, set[str]] = {}
    regular_direction = direction == "outwards"
    for edge in edges:
        if not isinstance(edge, Edge):
            continue
        key = NodeKeyGenerator.generate(
            edge.start_node if regular_direction else edge.end_node
        )
        entry = result.get(key, set[str]())
        entry.add(
            NodeKeyGenerator.generate(
                edge.end_node if regular_direction else edge.start_node
            )
        )
        result[key] = entry
    return result
