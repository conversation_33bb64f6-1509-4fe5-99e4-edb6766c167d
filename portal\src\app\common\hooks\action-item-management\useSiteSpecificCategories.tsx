import { gql } from '@apollo/client'
import { useEffect, useState } from 'react'
import { useGraphqlQuery } from '../'
import { SiteSpecificCategory } from '../../models/site-specific-category'
import { EntityType, getCodeFromReportingSiteExternalId, GetSpace } from '../../utils/space-util'

export interface SiteSpecificCategoryQueryRequest {
    siteIds: string[]
    excludeDeleted?: boolean
}

const buildSiteSpecificCategoryQuery = ({ siteIds, excludeDeleted }: SiteSpecificCategoryQueryRequest): string => {
    const siteCodes = siteIds.map(getCodeFromReportingSiteExternalId)
    const spaces = siteCodes.map((code) => `"${GetSpace(EntityType.Instance, code)}"`).join(', ')
    const reportingSites = siteIds.map((id) => `"${id}"`).join(', ')

    const baseFilters = [
        `{ space: { in: [ ${spaces}, "${GetSpace(EntityType.Instance)}" ] } }`,
        `{ reportingSite: { externalId: { in: [ ${reportingSites} ] } } }`,
    ]

    if (excludeDeleted) {
        baseFilters.push(`{ isDeleted: { isNull: true } }`)
    }

    const queryFilter = `{ and: [ ${baseFilters.join(', ')} ] }`

    return `
        query GetSiteSpecificCategory {
            listSiteSpecificCategory(
                filter: ${queryFilter}
                , first: 1000
                , sort: { name: ASC }
            ) {
                items {
                    externalId
                    name
                    description
                    isDeleted
                    reportingSite {
                        externalId
                        siteCode
                        space
                    }
                }
            }
        }
    `
}

export const useSiteSpecificCategories = (request: SiteSpecificCategoryQueryRequest) => {
    const query = buildSiteSpecificCategoryQuery(request)
    const { data: fdmData, refetch } = useGraphqlQuery<SiteSpecificCategory>(gql(query), 'listSiteSpecificCategory', {})

    const [resultData, setResultData] = useState<{ data: SiteSpecificCategory[]; loading: boolean }>({
        data: [],
        loading: true,
    })

    useEffect(() => {
        if (fdmData.length == 0) {
            setResultData({ data: [], loading: false })
        } else {
            setResultData({ data: fdmData, loading: false })
        }
    }, [fdmData])

    return {
        loading: resultData.loading,
        siteSpecificCategories: resultData.data,
        refetchSiteSpecificCategory: refetch,
    }
}
