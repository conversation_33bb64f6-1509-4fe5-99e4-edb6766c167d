import { Cln<PERSON>utton, SelectItem } from '@celanese/ui-lib'
import { Box } from '@mui/material'
import { z } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'
import { useEffect, useMemo } from 'react'
import { Reference } from '@/app/common/models/reference'
import { SubmitHandler, useForm } from 'react-hook-form'
import { Application } from '@/app/common/models/application'
import GenericTextField from '@/app/components/FieldsComponent/GenericTextField'
import GenericAutocomplete from '@/app/components/FieldsComponent/GenericAutocomplete'
import { transformOptions } from '@/app/common/utils/transform-options-for-filter'
import { translate } from '@/app/common/utils/generate-translate'
import { CustomDrawer } from '@/app/components/ModalComponent/Drawer/Drawer'
import { drawerStyles } from '@/app/components/ModalComponent/Drawer/styles'

interface ReferenceFormProps {
    onClose: () => void
    openDrawer: boolean
    onSubmitCallback: (data: any, onSuccess?: () => void) => void
    onCloseCallback: () => void
    referenceToEdit?: Reference
    applications: Application[]
    isEdit: boolean
}

type ReferenceConfigurationFormSchema = z.infer<typeof formSchema>

const formSchema = z.object({
    name: z.string().min(1),
    description: z.string().min(1),
    application: z.string().min(1),
    externalId: z.string().nullable(),
})

export const ReferenceForm: React.FC<ReferenceFormProps> = ({
    openDrawer,
    onSubmitCallback,
    onCloseCallback,
    referenceToEdit,
    applications,
    isEdit,
}) => {
    const {
        control,
        handleSubmit,
        reset,
        setValue,
        getValues,
        formState: { errors },
    } = useForm<ReferenceConfigurationFormSchema>({
        resolver: zodResolver(formSchema),
    })

    const applicationsOptions: SelectItem[] = useMemo(() => {
        return transformOptions(applications, 'alias', 'name')
    }, [applications])

    useEffect(() => {
        reset({
            name: referenceToEdit?.name ?? '',
            description: referenceToEdit?.description ?? '',
            application: referenceToEdit?.application?.externalId ?? '',
            externalId: referenceToEdit?.externalId ?? null,
        })
    }, [referenceToEdit, reset])

    const onSubmit: SubmitHandler<ReferenceConfigurationFormSchema> = (data) => {
        onSubmitCallback(data, () => reset())
    }

    const onCancel = () => {
        reset()
        onCloseCallback()
    }

    return (
        <CustomDrawer
            overlineMeta={
                isEdit
                    ? translate('adminSettings.reference.drawerEditTitle')
                    : translate('adminSettings.reference.drawerCreateTitle')
            }
            title={
                isEdit
                    ? translate('adminSettings.reference.drawerEditTitle')
                    : translate('adminSettings.reference.drawerCreateTitle')
            }
            openDrawer={openDrawer}
            closeDrawer={onCancel}
            content={
                <Box sx={drawerStyles.container}>
                    <form onSubmit={handleSubmit(onSubmit)} style={drawerStyles.formContainer}>
                        {getValues('externalId') !== null && (
                            <GenericTextField
                                name="externalId"
                                control={control}
                                label={translate('adminSettings.reference.form.id')}
                                helperText=""
                                disabled
                            />
                        )}
                        <GenericTextField
                            required
                            name="name"
                            control={control}
                            valueController={getValues('name')}
                            label={translate('adminSettings.reference.form.title')}
                            helperText=""
                            error={Boolean(errors.name)}
                        />
                        <GenericTextField
                            required
                            name="description"
                            control={control}
                            valueController={getValues('description')}
                            rows={6}
                            label={translate('adminSettings.reference.form.description')}
                            helperText=""
                            error={Boolean(errors.description)}
                        />
                        <GenericAutocomplete
                            name="application"
                            multiple={false}
                            control={control}
                            options={applicationsOptions}
                            onChange={(newValue) => setValue('application', newValue)}
                            label={`${translate('adminSettings.reference.form.application')} *`}
                            error={Boolean(errors.application)}
                            disableCloseOnSelect={false}
                            size="small"
                        />
                    </form>
                    <Box sx={drawerStyles.buttonsContainer}>
                        <ClnButton
                            label={translate('adminSettings.reference.form.buttons.cancel')}
                            variant="text"
                            onClick={onCancel}
                            sx={{ flex: 1 }}
                        />
                        <ClnButton
                            label={
                                isEdit
                                    ? translate('adminSettings.reference.form.buttons.save')
                                    : translate('adminSettings.reference.form.buttons.create')
                            }
                            onClick={handleSubmit(onSubmit)}
                            sx={{ flex: 1 }}
                        />
                    </Box>
                </Box>
            }
        />
    )
}
