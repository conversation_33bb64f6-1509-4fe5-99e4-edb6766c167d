import { Autocomplete, Box, TextField, styled } from '@mui/material'
import { z } from 'zod'
import { Controller, SubmitHandler, useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { ClnButton } from '@celanese/ui-lib'
import { FilterOptionsActionItemTemplate } from '@/app/common/models/admin-settings/filter-action-item-template'
import { useEffect } from 'react'
import { translate } from '@/app/common/utils/generate-translate'

const actionItemTemplateFilterSchema = z.object({
    unit: z.array(
        z.object({
            externalId: z.string(),
            description: z.string(),
        })
    ),
    category: z.array(z.string()),
    owner: z.array(
        z.object({
            externalId: z.string(),
            label: z.string(),
        })
    ),
})

type ActionItemTemplateFilterSchema = z.infer<typeof actionItemTemplateFilterSchema>

type ActionItemTemplateTabFilterProps = {
    onSubmit: (filters: any) => void
    data: FilterOptionsActionItemTemplate
    defaultFilter?: FilterOptionsActionItemTemplate
}

const Form = styled('form')({
    display: 'flex',
    flexDirection: 'column',
    width: '18rem',
    padding: '1.5rem',
    gap: '1rem',
})

export function ActionItemTemplateTabFilter({ data, defaultFilter, onSubmit }: ActionItemTemplateTabFilterProps) {
    const cleanFilter = {
        unit: [],
        category: [],
        owner: [],
    }

    const { reset, setValue, handleSubmit, control } = useForm<ActionItemTemplateFilterSchema>({
        defaultValues: cleanFilter,
        resolver: zodResolver(actionItemTemplateFilterSchema),
    })

    function selectWithAllOption<T>(op: T[]): T[] {
        return [...op]
    }

    const clearFunction = () => {
        reset(cleanFilter)
    }

    const defaultFunction = () => {
        reset(cleanFilter)
    }

    const submitFn: SubmitHandler<ActionItemTemplateFilterSchema> = (data) => {
        onSubmit(data)
    }

    useEffect(() => {
        reset({ ...cleanFilter, ...defaultFilter })
    }, [defaultFilter])

    return (
        <Box>
            <Form onSubmit={handleSubmit(submitFn)}>
                <Box sx={{ display: 'flex', gap: '8px', justifyContent: 'flex-end' }}>
                    <ClnButton
                        size="small"
                        variant="outlined"
                        label={translate('table.filter.clear')}
                        onClick={() => {
                            clearFunction()
                        }}
                    />
                    <ClnButton
                        size="small"
                        variant="outlined"
                        label={translate('table.filter.default')}
                        onClick={() => {
                            defaultFunction()
                        }}
                    />
                </Box>
                <Controller
                    control={control}
                    name="unit"
                    render={({ field }) => (
                        <Autocomplete
                            {...field}
                            multiple
                            limitTags={2}
                            id="unit"
                            options={selectWithAllOption(data.unit)}
                            getOptionLabel={(option) => option.description || ''}
                            value={field.value || []}
                            noOptionsText={translate('adminSettings.table.filter.noOptions')}
                            onChange={(event, value) => setValue('unit', value)}
                            renderInput={(params) => (
                                <TextField
                                    {...params}
                                    label={translate('adminSettings.table.filter.unit')}
                                    size="small"
                                />
                            )}
                            renderOption={(props, option) => (
                                <li {...props} key={option.externalId} data-value={option}>
                                    <span>{option.description}</span>
                                </li>
                            )}
                        />
                    )}
                />
                <Controller
                    control={control}
                    name="category"
                    render={({ field }) => (
                        <Autocomplete
                            {...field}
                            multiple
                            limitTags={2}
                            id="category"
                            options={selectWithAllOption(data.category)}
                            value={field.value || []}
                            noOptionsText={translate('adminSettings.table.filter.noOptions')}
                            onChange={(event, value) => setValue('category', value as string[])}
                            renderInput={(params) => (
                                <TextField
                                    {...params}
                                    label={translate('adminSettings.table.filter.category')}
                                    size="small"
                                />
                            )}
                        />
                    )}
                />
                <Controller
                    control={control}
                    name="owner"
                    render={({ field }) => (
                        <Autocomplete
                            {...field}
                            multiple
                            limitTags={2}
                            id="status"
                            options={selectWithAllOption(data.owner)}
                            getOptionLabel={(option) => option.label}
                            value={field.value || []}
                            noOptionsText={translate('adminSettings.table.filter.noOptions')}
                            onChange={(event, value) => setValue('owner', value)}
                            renderInput={(params) => (
                                <TextField
                                    {...params}
                                    label={translate('adminSettings.table.filter.owner')}
                                    size="small"
                                />
                            )}
                        />
                    )}
                />
                <Box
                    sx={{
                        display: 'flex',
                        width: '100%',
                        justifyContent: 'center',
                        margin: '20px 0 10px 0',
                        div: {
                            width: '100%',
                        },
                        button: {
                            width: '100%',
                        },
                    }}
                >
                    <ClnButton
                        type="submit"
                        size="small"
                        variant="contained"
                        label={translate('table.filter.applyFilter')}
                    />
                </Box>
            </Form>
        </Box>
    )
}
