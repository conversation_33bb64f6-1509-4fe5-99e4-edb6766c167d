export interface ModalData {
    title?: string
    subtitle?: string
    externalId?: string
    sections: ModalDataSection[]
}

export interface ModalDataSection {
    title?: string
    subsections?: ModalDataSubsection[]
}

export interface ModalDataSubsection {
    title?: string
    subtitle?: string
    externalId?: string
    columns: ModalDataColumn[]
    rows?: ModalDataField[]
}

export interface ModalDataColumn {
    title?: string
    fields: ModalDataField[]
}

export interface ModalDataField {
    label: string
    value?: string
}
