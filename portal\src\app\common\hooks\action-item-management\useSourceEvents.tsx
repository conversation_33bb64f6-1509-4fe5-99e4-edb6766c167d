import { useCallback, useEffect, useState } from 'react'
import { SourceEventResponse } from '../../models/source-event'
import { useDebounceFunction } from '../general-functions/useDebounce'
import { AzureFunctionClient } from '../../clients/azure-function-client'
import { UserRolesPermission } from '@celanese/celanese-sdk'

export const useSourceEvents = (activeUser: UserRolesPermission, id?: string, siteId?: string) => {
    const [sourceEvents, setSourceEvents] = useState<SourceEventResponse | undefined>(undefined)
    const [loading, setLoading] = useState<boolean>(true)

    const fetchEvent = useCallback(async () => {
        try {
            setLoading(true)

            const client = new AzureFunctionClient()

            if (activeUser?.email) {
                const result: SourceEventResponse = await client.getSourceEventById({
                    externalId: id,
                    activeUserEmail: activeUser.email,
                    reportingSiteExternalId: siteId ?? '-',
                })

                setSourceEvents(result)
            }
        } catch (err) {
            console.log(err)
        } finally {
            setLoading(false)
        }
    }, [activeUser, id])

    const debouncedFetchEvent = useCallback(useDebounceFunction(fetchEvent, 300), [fetchEvent])

    useEffect(() => {
        if (id) debouncedFetchEvent()
    }, [debouncedFetchEvent])

    return { sourceEvents: sourceEvents, loading: loading }
}
