from clients.core.models import BaseCamelCaseModel


class _FeaturePermission(BaseCamelCaseModel):
    feature_code: str
    feature_name: str
    feature_access_level: str
    feature_access_level_code: str


class _UserRole(BaseCamelCaseModel):
    role_name: str
    role_code: str
    site_codes: list[str]
    features: list[_FeaturePermission]


class UserSite(BaseCamelCaseModel):
    site_id: str
    site_name: str
    site_code: str


class UserUnit(BaseCamelCaseModel):
    unit_name: str
    unit_code: str


class UserApplication(BaseCamelCaseModel):
    application_code: str
    roles: list[_UserRole]
    user_sites: list[UserSite]