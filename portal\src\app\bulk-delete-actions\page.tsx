'use client'
import '../common/utils/polyfills'
import { Box, Typography, useMediaQuery, useTheme } from '@mui/material'
import { useRouter } from 'next/navigation'
import { useState, useMemo, useEffect, useContext } from 'react'

import AuthGuardWrapper from '../common/wrapper/AuthGuardWrapper'
import { ClnButton, ClnPanel } from '@celanese/ui-lib'
import { GridRowSelectionModel } from '@mui/x-data-grid-pro'

import { AzureFunctionClient } from '../common/clients/azure-function-client'
import { BackArrow } from '../common/utils/backArrow'
import { getLocalUserSite, translate, UserManagementContext, UserManagementContextState } from '@celanese/celanese-ui'
import { useAuthGuard } from '../common/hooks/useAuthGuard'
import { FilterInfoProps } from '../components/ActionTable/HomeFilter'
import { BulkActionRemovalModal } from '../components/BulkActionRemoval/BulkActionRemovalModal'
import { ActionTable } from '../components/ActionTable'
import { AutocompleteOption } from '../components/FieldsComponent/GenericAutocomplete'

import { ActionStatusExternalIdHomeDefaultEnum } from '../common/enums/ActionItemStatusEnum'
import { ActionDetailsModal } from '../components/CancelEventButton/ActionDetailsModal'
import { useSnackbar } from '../common/contexts/SnackbarContext'
import { actionStateTranslationMap } from '../common/models/action-state'
import ConfirmationModal from '../components/BulkActionRemoval/ConfirmationModal'
import { ActionItemStateStatus, ActionItemStateStatusEnum } from '../common/enums/KpiStatusEnum'
import { UserRolesPermission } from '@celanese/celanese-sdk'
import { UserExternalContext, UserExternalContextState } from '../common/contexts/UserExternalContext'
import { PageType } from '../common/enums/PageTypeEnum'

type ButtonVariant = 'text' | 'outlined' | 'contained'

type ButtonConfig = {
    label: string
    variant: ButtonVariant
    color: 'error' | 'inherit' | 'info' | 'warning' | 'primary' | 'secondary' | 'success'
    action: () => void
}

enum BulkActionType {
    CANCEL = 'cancel',
    DELETE = 'delete',
}

export default function BulkDeleteActionsPage() {
    const client = new AzureFunctionClient()
    const { siteId } = getLocalUserSite() || {}

    const router = useRouter()
    const theme = useTheme()
    const isMobile = useMediaQuery(theme.breakpoints.down('md'))

    const { showSnackbar } = useSnackbar()

    const { checkPermissionsFromComponents } = useAuthGuard()
    const { userInfo } = useContext<UserManagementContextState>(UserManagementContext)
    const { userExternalInfo } = useContext<UserExternalContextState>(UserExternalContext)

    const [activeUser, setActiveUser] = useState<UserRolesPermission>()

    const isAuthorizedToDelete = useMemo(
        () => checkPermissionsFromComponents('BulkDeleteActionsPage').isAuthorized,
        [activeUser]
    )

    const storedFilterInfo = sessionStorage.getItem('home-filterInfo-toDo-tab')
    const parsedFilterInfo = storedFilterInfo ? JSON.parse(storedFilterInfo) : {}

    const [filterInfo, setFilterInfo] = useState<FilterInfoProps>({
        reportingSiteExternalId: siteId ?? '-',
        statusExternalIds: Object.values(ActionStatusExternalIdHomeDefaultEnum),
        ...parsedFilterInfo?.filter,
    })

    const [defaultFilterInfo] = useState<FilterInfoProps>({
        reportingSiteExternalId: siteId ?? '-',
        statusExternalIds: Object.values(ActionStatusExternalIdHomeDefaultEnum),
        ...parsedFilterInfo?.filter,
    })

    const [ownerName, setOwnerName] = useState<AutocompleteOption[]>(parsedFilterInfo?.ownerName || [])
    const [assigneeName, setAssigneeName] = useState<AutocompleteOption[]>(parsedFilterInfo?.assignedName || [])

    const [selectedRows, setSelectedRows] = useState<GridRowSelectionModel>([])
    const [isBulkActionRemovalModalOpen, setIsBulkActionRemovalModalOpen] = useState(false)
    const [modalState, setModalState] = useState({ cancel: false, delete: false })

    const [selectedActionId, setSelectedActionId] = useState('')
    const [isActionDetailsModalOpen, setIsActionDetailsModalOpen] = useState(false)

    const [currentPage, setCurrentPage] = useState(0)
    const [memoryPage, setMemoryPage] = useState(0)

    const [isConfirmationModalOpen, setIsConfirmationModalOpen] = useState<boolean>(false)

    const actionButtons: ButtonConfig[] = [
        {
            label: translate('requestModal.cancelSelectedActions'),
            variant: 'outlined',
            color: 'error',
            action: () => {
                setModalState({ delete: false, cancel: true })
                setIsBulkActionRemovalModalOpen(true)
            },
        },
        {
            label: translate('requestModal.deleteSelectedActions'),
            variant: 'contained',
            color: 'error',
            action: () => {
                setModalState({ delete: true, cancel: false })
                setIsBulkActionRemovalModalOpen(true)
            },
        },
    ]

    const handleActionDetails = (actionId: string) => {
        setSelectedActionId(actionId)
        setIsActionDetailsModalOpen(true)
    }

    useEffect(() => {
        if (userExternalInfo.externalId && activeUser?.externalId !== userExternalInfo.externalId) {
            setActiveUser({
                ...userInfo,
                externalId: userExternalInfo.externalId,
                roles: userExternalInfo.roles,
                teams: userExternalInfo.teams,
            })

            const kpiName: ActionItemStateStatus =
                parsedFilterInfo?.filter?.kpiFilter ?? ActionItemStateStatusEnum.TotalActions
            if (kpiName === ActionItemStateStatusEnum.TotalActions) return

            const kpiNameTranslation = `"${translate(actionStateTranslationMap[kpiName] ?? '')}"`
            const message = translate('alerts.actionsOriginatedFromKPI')

            showSnackbar(`${message.replace('{}', kpiNameTranslation)}`, 'info', 'kpi-bulk-delete-action', 6000, {
                vertical: 'top',
                horizontal: 'right',
            })
        }
    }, [userInfo, userExternalInfo])

    return (
        <AuthGuardWrapper componentName={isAuthorizedToDelete ? BulkDeleteActionsPage.name : 'UnknownComponent'}>
            <Box
                sx={{
                    display: 'flex',
                    flexDirection: 'column',
                    flex: 1,
                    padding: '1.5rem',
                    backgroundColor: 'primary.white',
                    minHeight: 'calc(100vh - 3rem)',
                    overflow: 'auto',
                }}
            >
                <ClnPanel
                    sx={{
                        display: 'flex',
                        flexDirection: 'column',
                        justifyContent: 'space-between',
                        padding: '1rem',
                        gap: 1,
                        flexGrow: 1,
                    }}
                >
                    {BackArrow(translate('new-action-item.back'), () => router.push('/'))}

                    <Typography
                        variant="h6"
                        sx={{
                            wordBreak: 'break-word',
                            whiteSpace: 'normal',
                            fontWeight: 400,
                            fontSize: '20px',
                            color: 'text.primary',
                        }}
                    >
                        {`${translate('bulkDeleteActionItems.cancelOrDelete')} (${selectedRows.length} ${translate(
                            'bulkDeleteActionItems.itemsSelected'
                        )}):`}
                    </Typography>

                    <Typography
                        component="div"
                        sx={{
                            wordBreak: 'break-word',
                            whiteSpace: 'normal',
                            fontWeight: 400,
                            fontSize: '14px',
                            color: 'text.primary',
                        }}
                    >
                        <ul style={{ listStylePosition: 'inside', padding: 0, margin: 0 }}>
                            <li>{translate('bulkDeleteActionItems.deleteActionsAdvice')}</li>
                            <li>{translate('bulkDeleteActionItems.cancelActionsAdvice')}</li>
                        </ul>
                    </Typography>

                    <Box sx={{ flex: 1, height: '100%', overflow: 'auto', maxHeight: '500px', mb: 2 }}>
                        {activeUser && (
                            <ActionTable
                                id="bulk-delete-actions"
                                filterInfo={filterInfo}
                                setFilterInfo={setFilterInfo}
                                currentPage={currentPage}
                                setCurrentPage={setCurrentPage}
                                memoryPage={memoryPage}
                                setMemoryPage={setMemoryPage}
                                isToDoTab
                                activeUser={activeUser}
                                client={client}
                                siteIds={[siteId!]}
                                initialChipValue={filterInfo.search ?? ''}
                                showLink={false}
                                showBulkUpload={false}
                                ownerName={ownerName}
                                setOwnerName={setOwnerName}
                                assigneeName={assigneeName}
                                setAssigneeName={setAssigneeName}
                                infoMessage={`* ${translate('table.filter.infoMessage')}`}
                                checkboxSelection
                                rowSelectionModel={selectedRows}
                                setRowSelectionModel={setSelectedRows}
                                onActionDetailsClick={handleActionDetails}
                                disableDefaultFilterFields
                                defaultFilterInfo={defaultFilterInfo}
                                disableSearch={!!defaultFilterInfo.search}
                                pageType={PageType.BulkDeleteActions}
                            />
                        )}
                    </Box>

                    <Box
                        sx={{
                            display: 'flex',
                            gap: 1,
                            justifyContent: isMobile ? 'stretch' : 'flex-end',
                            alignItems: 'center',
                            mt: 'auto',
                        }}
                    >
                        {!isMobile && (
                            <ClnButton
                                variant="text"
                                label={translate('stepper.form.cancel')}
                                color="primary"
                                onClick={() => router.push('/')}
                            />
                        )}
                        {actionButtons.map(({ label, variant, color, action }, index) => (
                            <ClnButton
                                key={index}
                                variant={variant}
                                color={color}
                                onClick={action}
                                disabled={!selectedRows.length}
                                sx={{
                                    width: isMobile ? '100%' : 'auto',
                                    whiteSpace: 'normal',
                                    wordBreak: 'break-word',
                                    textAlign: 'center',
                                }}
                                label={label}
                            />
                        ))}
                    </Box>
                    {activeUser &&
                        isBulkActionRemovalModalOpen &&
                        Object.values(BulkActionType).map(
                            (type) =>
                                modalState[type] && (
                                    <BulkActionRemovalModal
                                        key={type}
                                        onClose={(showConfirmationModal) => {
                                            setIsBulkActionRemovalModalOpen(false)
                                            if (showConfirmationModal) setIsConfirmationModalOpen(true)
                                        }}
                                        isCancel={type === BulkActionType.CANCEL}
                                        defaultFilterInfo={{
                                            ...filterInfo,
                                            externalIds: selectedRows as string[],
                                            cursor: undefined,
                                        }}
                                        activeUser={activeUser}
                                    />
                                )
                        )}
                    {isActionDetailsModalOpen && (
                        <ActionDetailsModal
                            onClose={() => setIsActionDetailsModalOpen(false)}
                            actionId={selectedActionId}
                            activeUser={activeUser!}
                        />
                    )}
                    {isConfirmationModalOpen && (
                        <ConfirmationModal onClose={() => router.push('/')} isCancel={modalState.cancel} />
                    )}
                </ClnPanel>
            </Box>
        </AuthGuardWrapper>
    )
}
