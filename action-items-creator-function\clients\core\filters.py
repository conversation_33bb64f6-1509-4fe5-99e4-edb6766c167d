from datetime import date
from typing import Any

from cognite.client.data_classes import data_modeling


def eq_filter(key: str, value: Any | None) -> dict[str, dict[str, Any]]:
    """
    Create a filter that checks if a field is equal to a given value.

    Args:
        key (str): The field name to filter.
        value (Any | None): The value to compare.

    Returns:
        dict[str, dict[str, Any]]: The equality filter.

    """
    return {key: {"eq": value}}


def in_filter(key: str, value: list[Any]) -> dict[str, dict[str, list[Any]]]:
    """
    Create a filter that checks if a field's value is within a list.

    Args:
        key (str): The field name to filter.
        value (list[Any]): The list of allowed values.

    Returns:
        dict[str, dict[str, list[Any]]]: The 'in' filter.

    """
    return {key: {"in": value}}


def not_in_filter(
    key: str,
    value: list[Any],
) -> dict[str, dict[str, dict[str, list[Any]]]]:
    """
    Create a filter that excludes items where the given field's value is in the provided list.

    Args:
        key (str): The field name to filter.
        value (list[Any]): The list of values to exclude.

    Returns:
        dict[str, dict[str, dict[str, list[Any]]]]: The 'not in' filter.

    """
    return {"not": {key: {"in": value}}}


def prefix_filter(key: str, value: str) -> dict[str, dict[str, str]]:
    """
    Create a filter that checks if a field starts with a specific prefix.

    Args:
        key (str): The field name to filter.
        value (str): The prefix to match.

    Returns:
        dict[str, dict[str, str]]: The prefix filter.

    """
    return {key: {"prefix": value}}


def contains_any_filter(key: str, value: list[Any]) -> dict[str, dict[str, list[Any]]]:
    """
    Create a filter that checks if any item in a field matches values from a list.

    Args:
        key (str): The field name to filter.
        value (list[Any]): List of values to check.

    Returns:
        dict[str, dict[str, list[Any]]]: The containsAny filter.

    """
    return {key: {"containsAny": value}}


def is_null_filter(key: str, is_null: bool) -> dict[str, dict[str, bool]]:
    """
    Create a filter that checks if a field is null or not.

    Args:
        key (str): The field name to filter.
        is_null (bool): True to check for null values, False otherwise.

    Returns:
        dict[str, dict[str, bool]]: The isNull filter.

    """
    return {key: {"isNull": is_null}}


def contains_all_filter(key: str, value: list[Any]) -> dict[str, dict[str, list[Any]]]:
    """
    Create a filter that checks if a field contains all values in a list.

    Args:
        key (str): The field name to filter.
        value (list[Any]): List of values that must all be contained.

    Returns:
        dict[str, dict[str, list[Any]]]: The containsAll filter.

    """
    return {key: {"containsAll": value}}


def gte_filter(key: str, value: date) -> dict[str, dict[str, str]]:
    """
    Create a filter that checks if a field is greater than or equal to a date.

    Args:
        key (str): The field name to filter.
        value (date): The minimum date.

    Returns:
        dict[str, dict[str, str]]: The gte filter.

    """
    return {key: {"gte": value.isoformat()}}


def lt_filter(key: str, value: date) -> dict[str, dict[str, str]]:
    """
    Create a filter that checks if a field is less than a date.

    Args:
        key (str): The field name to filter.
        value (date): The maximum date.

    Returns:
        dict[str, dict[str, str]]: The lt filter.

    """
    return {key: {"lt": value.isoformat()}}


def node_id_filter(
    external_id: str | list[str],
    space: str | list[str],
) -> dict[str, list[dict]]:
    """
    Create a filter based on node external ID and space.

    Args:
        external_id (str | list[str]): The external ID(s) of the node.
        space (str | list[str]): The space(s) of the node.

    Returns:
        dict[str, list[dict]]: Combined 'and' filter for externalId and space.

    """
    external_id_filter = (
        eq_filter("externalId", external_id)
        if isinstance(external_id, str)
        else in_filter("externalId", external_id)
    )

    space_filter = (
        eq_filter("space", space)
        if isinstance(space, str)
        else in_filter("space", space)
    )
    return {"and": [external_id_filter, space_filter]}


def equals_sdk_filter(
    property_name: str,
    value: dict[str, str] | str | bool,
    view: data_modeling.ViewId | None = None,
) -> data_modeling.filters.Equals:
    """
    Create a Cognite SDK Equals filter for a field.

    Args:
        property_name (str): Field name to filter.
        value (dict[str, str] | str | bool): Value to compare.
        view (ViewId | None): View context for the property.

    Returns:
        data_modeling.filters.Equals: The Equals filter.

    """
    return data_modeling.filters.Equals(
        (
            view.as_property_ref(property_name)
            if property_name not in {"externalId", "space"}
            else ["node", property_name]
        ),
        value,
    )


def in_sdk_filter(
    property_name: str,
    external_ids: list[str] | list[dict[str, str]],
    view: data_modeling.ViewId,
) -> data_modeling.filters.In:
    """
    Create a Cognite SDK In filter for a field.

    Args:
        property_name (str): Field name to filter.
        external_ids (list): List of values to match.
        view (ViewId): View context for the property.

    Returns:
        data_modeling.filters.In: The In filter.

    """
    return data_modeling.filters.In(
        (
            view.as_property_ref(property_name)
            if property_name != "externalId"
            else ["node", property_name]
        ),
        external_ids,
    )


def not_in_sdk_filter(
    property_name: str,
    external_ids: list[str] | list[dict[str, str]],
    view: data_modeling.ViewId,
) -> data_modeling.filters.Not:
    """
    Create a Cognite SDK Not In filter for a field.

    Args:
        property_name (str): The name of the property to apply the filter on.
        external_ids (list[str] | list[dict[str, str]]): The values to be excluded.
        view (ViewId): The view context in which the property is defined.

    Returns:
        data_modeling.filters.Not: A filter object representing the 'Not In' condition.

    """
    return data_modeling.filters.Not(
        data_modeling.filters.In(
            (
                view.as_property_ref(property_name)
                if property_name != "externalId"
                else ["node", property_name]
            ),
            external_ids,
        ),
    )


def contains_sdk_filter(
    property_name: str,
    external_ids: list[str],
    view: data_modeling.ViewId,
) -> data_modeling.filters.ContainsAny:
    """
    Create a Cognite SDK ContainsAny filter for a field.

    Args:
        property_name (str): Field name to filter.
        external_ids (list[str]): Values to check presence.
        view (ViewId): View context for the property.

    Returns:
        data_modeling.filters.ContainsAny: The ContainsAny filter.

    """
    return data_modeling.filters.ContainsAny(
        (
            view.as_property_ref(property_name)
            if property_name != "externalId"
            else ["node", property_name]
        ),
        external_ids,
    )


def prefix_sdk_filter(
    property_name: str,
    external_id: str,
    view: data_modeling.ViewId,
) -> data_modeling.filters.Prefix:
    """
    Create a Cognite SDK Prefix filter for a field.

    Args:
        property_name (str): Field name to filter.
        external_id (str): Prefix string.
        view (ViewId): View context for the property.

    Returns:
        data_modeling.filters.Prefix: The Prefix filter.

    """
    return data_modeling.filters.Prefix(
        (
            view.as_property_ref(property_name)
            if property_name != "externalId"
            else ["node", property_name]
        ),
        external_id,
    )


def range_sdk_filter(
    property_name: str,
    view: data_modeling.ViewId,
    gt: str | None = None,
    gte: str | None = None,
    lt: str | None = None,
    lte: str | None = None,
) -> data_modeling.filters.Range:
    """
    Create a Cognite SDK Range filter for a field.

    Args:
        property_name (str): Field name to filter.
        view (ViewId): View context for the property.
        gt (str | None): Greater than value.
        gte (str | None): Greater than or equal to value.
        lt (str | None): Less than value.
        lte (str | None): Less than or equal to value.

    Returns:
        data_modeling.filters.Range: The Range filter.

    """
    return data_modeling.filters.Range(
        view.as_property_ref(property_name),
        gt,
        gte,
        lt,
        lte,
    )


def not_exists_sdk_filter(
    property_name: str,
    view: data_modeling.ViewId,
) -> data_modeling.filters.Not:
    """
    Create a Cognite SDK Not Exists filter for a field.

    Args:
        property_name (str): Field name to check.
        view (ViewId): View context for the property.

    Returns:
        data_modeling.filters.Not: The Not Exists filter.

    """
    return data_modeling.filters.Not(
        data_modeling.filters.Exists(view.as_property_ref(property_name)),
    )


def exists_sdk_filter(
    property_name: str,
    view: data_modeling.ViewId,
) -> data_modeling.filters.Exists:
    """
    Create a Cognite SDK Exists filter for a field.

    Args:
        property_name (str): Field name to check existence.
        view (ViewId): View context for the property.

    Returns:
        data_modeling.filters.Exists: The Exists filter.

    """
    return data_modeling.filters.Exists(view.as_property_ref(property_name))


def nested_sdk_filter(
    property_name: str,
    sdk_filter: data_modeling.filters.Filter,
    view: data_modeling.ViewId,
) -> data_modeling.filters.Nested:
    """
    Create a Cognite SDK Nested filter for a field.

    Args:
        property_name (str): Field name representing the nested scope.
        sdk_filter (Filter): The filter to apply inside the nested property.
        view (ViewId): View context for the property.

    Returns:
        data_modeling.filters.Nested: The Nested filter.

    """
    return data_modeling.filters.Nested(
        scope=[view.space, f"{view.external_id}/{view.version}", property_name],
        filter=sdk_filter,
    )
