from typing import Optional

from clients.core.constants import AGGR<PERSON>ATE_LIMIT, DataSpaceEnum
from clients.core.models import PaginatedData, ServiceParams
from clients.reporting_location.models import ReportingLocationResult
from clients.reporting_location.requests import (
    GetReportingLocationsBySitesRequest,
    GetReportingLocationsRequest,
)

from .queries import (
    GET_REPORTING_LOCATION_BY_FILTER,
    GET_SEARCH_REPORTING_LOCATION_BY_FILTER,
)


class ReportingLocationClient:
    """Client responsible for handling locations and related views in the data model."""

    def __init__(
        self,
        params: ServiceParams,
    ) -> None:
        """Initialize the ReportingLocationClient with the required services and configuration."""
        self._cognite_client = params.cognite_client
        self._data_model_id = params.data_model.as_id()

    def get_reporting_locations(
        self,
        request: GetReportingLocationsRequest,
    ) -> Optional[PaginatedData[ReportingLocationResult]]:
        """
        Fetch a list of reporting locations filtered by site code and optionally by description.

        Args:
            request (GetReportingLocationsRequest): Request containing the site code and optional description.

        Returns:
            Optional[PaginatedData[ReportingLocationResult]]: A paginated list of reporting locations,
            or None if an error occurs.

        """
        filters = [
            {"name": {"prefix": request.site_code}},
            {"isActive": {"eq": True}},
        ]

        if request.description:
            filters.append({"description": {"eq": request.description}})

        variables = {"filter": {"and": filters}, "sort": {"description": "ASC"}}

        try:
            result = self._cognite_client.data_modeling.graphql.query(
                id=self._data_model_id,
                query=GET_REPORTING_LOCATION_BY_FILTER,
                variables=variables,
            )

            return PaginatedData[ReportingLocationResult].from_graphql_response(
                result,
                AGGREGATE_LIMIT,
            )
        except Exception:
            return None

    def get_reporting_locations_by_sites(
        self,
        request: GetReportingLocationsBySitesRequest,
    ) -> Optional[PaginatedData[ReportingLocationResult]]:
        """
        Fetch reporting locations for multiple sites, optionally filtering by reporting unit external IDs.

        Args:
            request (GetReportingLocationsBySitesRequest): Request containing site codes, search term,
                search fields, and optionally a list of reporting unit external IDs.

        Returns:
            Optional[PaginatedData[ReportingLocationResult]]: A paginated list of filtered reporting locations,
            or None if an error occurs.

        """
        filters = [
            {"space": {"eq": DataSpaceEnum.REF_DATA_SPACE}},
            {"or": [{"name": {"prefix": code}} for code in request.site_codes]},
            {"isActive": {"eq": True}},
        ]

        variables = {
            "query": request.search,
            "fields": request.search_properties,
            "filter": {"and": filters},
            "sort": {"description": "ASC"},
        }

        try:

            result = self._cognite_client.data_modeling.graphql.query(
                id=self._data_model_id,
                query=GET_SEARCH_REPORTING_LOCATION_BY_FILTER,
                variables=variables,
            )

            paginated_locations = PaginatedData[
                ReportingLocationResult
            ].from_graphql_response(result, AGGREGATE_LIMIT)

            if request.reporting_unit_external_ids:
                filtered_data = [
                    location
                    for location in paginated_locations.data
                    if location.reporting_unit
                    and location.reporting_unit.external_id
                    in request.reporting_unit_external_ids
                ]
            else:
                filtered_data = paginated_locations.data

            filtered_paginated = PaginatedData[ReportingLocationResult](
                data=filtered_data,
                has_next_page=paginated_locations.has_next_page,
                cursor=paginated_locations.cursor,
            )

            return filtered_paginated

        except Exception:
            return None
