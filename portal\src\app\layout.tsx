'use client'

import './common/styles/globals.css'
import '@celanese/ui-lib/src/common/styles/icons.css'
import '@fontsource/roboto/300.css'
import '@fontsource/roboto/400.css'
import '@fontsource/roboto/500.css'
import '@fontsource/roboto/700.css'
import '@material-symbols/font-400/outlined.css'
import './common/utils/polyfills'

import { useEffect, useState } from 'react'
import { createPublicClientApplication } from './common/factories/msal-factory'
import { AccountInfo, EventType, InteractionType } from '@azure/msal-browser'
import { MsalAuthenticationTemplate, MsalProvider } from '@azure/msal-react'
import { ApolloClientProvider } from './common/providers/ApolloClientProvider'
import { CogniteClientProvider } from './common/providers/CogniteClientProvider'
import { environment } from './common/configurations/environment'
import { LicenseInfo } from '@mui/x-license-pro'
import { getIdTokenFromMsal } from './common/utils'
import { useLocale } from './common/hooks/useLocale'
import { IntlProvider } from 'react-intl'
import {
    DynamicTranslationArea,
    getMessages,
    TranslationContextProvider,
    UserManagementContextProvider,
} from '@celanese/celanese-ui'
import { CelaneseSDKProvider } from './common/providers/CelaneseSDKProvider'

import { SnackbarProvider } from './common/providers/SnackbarProvider'
import { BulkUploadProvider } from './common/providers/BulkUploadProvider'
import { BulkUploadModal } from './components/ModalComponent/BulkUploadModal'
import BaseLayout from './common/layouts/BaseLayout'
import { LoadingProvider } from './common/providers/LoadingProvider'
import { ThemeProvider } from './common/providers/ThemeProvider'
import { UserRoleContextProvider } from './common/providers/UserRoleContextProvider'

LicenseInfo.setLicenseKey(environment.muiLicenseKey ?? '')

const msalInstance = createPublicClientApplication()

const accounts = msalInstance.getAllAccounts()
if (accounts.length > 0) {
    msalInstance.setActiveAccount(accounts[0])
}

const getIdToken = (): Promise<string> => getIdTokenFromMsal(msalInstance)
const getAuthToken = (): Promise<string> => getIdTokenFromMsal(msalInstance, true)

msalInstance.addEventCallback((event) => {
    if (!event) {
        return
    }
    if (event.eventType === EventType.LOGIN_SUCCESS && event.payload) {
        const account: AccountInfo = event.payload as AccountInfo
        msalInstance.setActiveAccount(account)
    }
})

export default function RootLayout({ children }: { children: React.ReactNode }) {
    const { locale, switchLocale } = useLocale()
    const [localeCode, setLocaleCode] = useState<string>(locale)
    const [shouldTranslateDynamic, setShouldTranslateDynamic] = useState<boolean>()
    const [dynamicTranslationLoading, setDynamicTranslationLoading] = useState(false)
    const [messages, setMessages] = useState<any>()

    const cacheNameShouldTranslate = 'shouldTranslateDynamic'

    useEffect(() => {
        getMessages(localeCode, getAuthToken, getIdToken, environment.userManagementAppCode).then((m) => {
            if (m) {
                setMessages(m)
            }
        })
    }, [localeCode])

    const handleLangChanged = (code: string) => {
        switchLocale(code)
        setLocaleCode(code)
    }

    useEffect(() => {
        const cacheValue = window.localStorage.getItem(cacheNameShouldTranslate)
        if (cacheValue && cacheValue === 'true') {
            setShouldTranslateDynamic(true)
        } else {
            setShouldTranslateDynamic(false)
        }
    }, [])

    useEffect(() => {
        if (shouldTranslateDynamic !== undefined) {
            window.localStorage.setItem(cacheNameShouldTranslate, JSON.stringify(shouldTranslateDynamic))
        }
    }, [shouldTranslateDynamic])

    return (
        <html lang="en">
            <body>
                <IntlProvider locale={locale} messages={messages}>
                    <MsalProvider instance={msalInstance}>
                        <MsalAuthenticationTemplate interactionType={InteractionType.Redirect}>
                            <ThemeProvider>
                                <DynamicTranslationArea
                                    getAuthToken={getAuthToken}
                                    getIdToken={getIdToken}
                                    dynamicTranslationLoadingState={{
                                        dynamicTranslationLoading,
                                        setDynamicTranslationLoading,
                                    }}
                                    shouldTranslateDynamicState={{ shouldTranslateDynamic, setShouldTranslateDynamic }}
                                    translatedClasses={
                                        ".desktop-drawer, .mobile-drawer, #menu-triggerField ul[role='listbox']"
                                    }
                                >
                                    <UserManagementContextProvider
                                        getAuthToken={getAuthToken}
                                        getIdToken={getIdToken}
                                        applicationCode={environment.userManagementAppCode!}
                                    >
                                        <UserRoleContextProvider>
                                            <TranslationContextProvider
                                                getAuthToken={getAuthToken}
                                                getIdToken={getIdToken}
                                            >
                                                <ApolloClientProvider>
                                                    <CogniteClientProvider>
                                                        <CelaneseSDKProvider>
                                                            <SnackbarProvider>
                                                                <LoadingProvider>
                                                                    <BulkUploadProvider>
                                                                        <BaseLayout
                                                                            setLocaleCode={setLocaleCode}
                                                                            handleLangChanged={handleLangChanged}
                                                                            shouldTranslateDynamic={
                                                                                shouldTranslateDynamic
                                                                            }
                                                                            setShouldTranslateDynamic={
                                                                                setShouldTranslateDynamic
                                                                            }
                                                                            dynamicTranslationLoading={
                                                                                dynamicTranslationLoading
                                                                            }
                                                                            setDynamicTranslationLoading={
                                                                                setDynamicTranslationLoading
                                                                            }
                                                                        >
                                                                            {children}
                                                                        </BaseLayout>
                                                                        <BulkUploadModal />
                                                                    </BulkUploadProvider>
                                                                </LoadingProvider>
                                                            </SnackbarProvider>
                                                        </CelaneseSDKProvider>
                                                    </CogniteClientProvider>
                                                </ApolloClientProvider>
                                            </TranslationContextProvider>
                                        </UserRoleContextProvider>
                                    </UserManagementContextProvider>
                                </DynamicTranslationArea>
                            </ThemeProvider>
                        </MsalAuthenticationTemplate>
                    </MsalProvider>
                </IntlProvider>
            </body>
        </html>
    )
}
