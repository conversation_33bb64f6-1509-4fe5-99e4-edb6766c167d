import { Box, Tooltip, Typography } from '@mui/material'
import React, { useEffect, useMemo, useState } from 'react'
import { useForm } from 'react-hook-form'
import { ClnButton, ClnRadio, PlantItem, RadioItem } from '@celanese/ui-lib'
import { supportFeatureValidationSchema as schema, SupportFeatureValidationSchema } from './schema'
import { zodResolver } from '@hookform/resolvers/zod'
import { ValidationScreen } from './validation-screen'
import { impactOptions, AppInformationProps } from './consts'

import { filterAvailableAzureSites } from './utils/mapSitesToAzure'

import InfoOutlined from '@mui/icons-material/InfoOutlined'
import { translate } from '@celanese/celanese-ui'
import { useAzureService } from '@/app/common/clients/azure-support-feature-publish'
import GenericTextField from '../FieldsComponent/GenericTextField'
import GenericAutocomplete from '../FieldsComponent/GenericAutocomplete'
import { UploadFiles } from '../UploadFiles/uploadFile'
import { transformOptions } from '@/app/common/utils/transform-options-for-filter'
import { useSnackbar } from '@/app/common/contexts/SnackbarContext'
import { applicationArea } from '@/app/common/utils/constants'
import { WorkItemTypeEnum } from '@/app/common/enums/WorkItemTypeEnum'
import { RequestTypeEnum } from '@/app/common/enums/RequestTypeEnum'
import { WorkItemBodyInformation } from '@/app/common/models/work-item-body-information'
import { UserRolesPermission } from '@celanese/celanese-sdk'

interface SupportFeatureProps {
    onSuccess: (ticketNumber: string) => void
    sites: PlantItem[]
    loadingSites: boolean
    areaPath: string
    useCase: string
    userInfo: UserRolesPermission
    appCode: string
}

export const SupportFeaturePanel = ({
    onSuccess,
    sites,
    loadingSites,
    userInfo,
    appCode,
    areaPath,
    useCase,
}: SupportFeatureProps) => {
    const {
        handleSubmit,
        control,
        setValue,
        reset,
        watch,
        getValues,
        formState: { errors, isSubmitting },
    } = useForm<SupportFeatureValidationSchema>({
        defaultValues: {
            impactedSites: userInfo.selectedSite ? [userInfo.selectedSite.siteId] : undefined,
        },
        resolver: zodResolver(schema),
    })
    const [loading, setLoading] = useState<boolean>(false)
    const [requestType, setRequestType] = useState<string | number>('')
    const [reviewMode, setReviewMode] = useState<boolean>(false)
    const requestTypeItems: RadioItem[] = [
        { value: 'incident', label: translate('supportFeature.incident'), disabled: loading },
        { value: 'improvement', label: translate('supportFeature.improvement'), disabled: loading },
    ]
    const [fileSelected, setFileSelected] = useState<File[]>([])

    const { showSnackbar } = useSnackbar()

    const userCurrentSite = userInfo?.selectedSite?.siteName ? userInfo.selectedSite?.siteName : 'Global'
    const impactedSites = watch('impactedSites')
    const typeOfRequest = watch('typeOfRequest')
    const subjectTitle = watch('subjectTitle')
    const natureOfIncident = watch('natureOfIncident')
    const previouslyFunctioning = watch('previouslyFunctioning')
    const impactOnWork = watch('impactOnWork')
    const incidentStepByStep = watch('incidentStepByStep')
    const expectedFunctionality = watch('expectedFunctionality')
    const action = watch('action')
    const objective = watch('objective')

    const appInfo = useMemo((): AppInformationProps => {
        return {
            titlePrefix:
                typeOfRequest &&
                `[${appCode}] - ${
                    typeOfRequest.charAt(0).toUpperCase() + typeOfRequest.slice(1)
                } / ${userCurrentSite} - `,
            useCase,
            areaPath,
        }
    }, [typeOfRequest, userCurrentSite, areaPath, useCase, appCode])

    const { handlePostWorkItem, getDescriptionTemplate, fetchAvailableDeploymentAndScaling, fetchProjects } =
        useAzureService({
            appInformation: appInfo,
            typeOfRequest: watch('typeOfRequest') as RequestTypeEnum,
        })

    const onSubmit = async () => {
        await fetchProjects()
        if (Object.keys(errors).length === 0) {
            setReviewMode(true)
        }
    }
    const sitesConverted = transformOptions([...sites, { externalId: 'Global', value: 'Global Site' }], 'value')

    const workItemType =
        watch('typeOfRequest') === RequestTypeEnum.INCIDENT ? WorkItemTypeEnum.BugV2 : WorkItemTypeEnum.UserStoryV2

    const onFinalSubmit = async () => {
        const values = getValues()
        const descriptionTemplate = getDescriptionTemplate({
            incident: values.natureOfIncident,
            description: values.description,
            previouslyFunctioning: values.previouslyFunctioning,
            expectedFunctionality: values.expectedFunctionality,
            action: values.action,
            userEmail: userInfo?.email,
            siteAccessed: userCurrentSite,
            jobTitle: userInfo?.jobTitle,
            objective: values.objective,
            stepByStep: values.incidentStepByStep,
            typeOfRequest: values.typeOfRequest as RequestTypeEnum,
            impactedSites: impactedSites,
        })

        const subjectTitle = values.subjectTitle?.charAt(0).toUpperCase() + values.subjectTitle?.slice(1) || ''
        const dataSitesAzure = await fetchAvailableDeploymentAndScaling(workItemType)
        const availableDeploymentScallingSite = filterAvailableAzureSites(
            dataSitesAzure,
            impactedSites,
            userCurrentSite
        )
        const obj: Partial<WorkItemBodyInformation> = {
            title: subjectTitle,
            description: descriptionTemplate,
            application: applicationArea,
            itemType:
                values.typeOfRequest === RequestTypeEnum.INCIDENT
                    ? WorkItemTypeEnum.BugV2
                    : WorkItemTypeEnum.UserStoryV2,
            useCase: appInfo.useCase,
            siteAccessed: availableDeploymentScallingSite,
            priority: values.impactOnWork ?? '3',
            attachments: fileSelected,
        }
        setLoading(true)

        try {
            const { success, data } = await handlePostWorkItem(obj)
            if (success) {
                onSuccess(data.id)
            }
        } catch (error) {
            showSnackbar(translate('supportFeature.errorSubmit'))
        } finally {
            setLoading(false)
        }
    }

    const handleBackButton = () => {
        setReviewMode(false)
    }

    const isButtonDisabled = React.useMemo(() => {
        const requiredIncidentFields = [
            subjectTitle,
            typeOfRequest,
            natureOfIncident,
            previouslyFunctioning,
            incidentStepByStep,
            expectedFunctionality,
            impactedSites,
            impactOnWork,
        ]

        const requiredImprovmentFields = [action, objective, subjectTitle, typeOfRequest]

        const requiredField =
            typeOfRequest === RequestTypeEnum.IMPROVEMENT ? requiredImprovmentFields : requiredIncidentFields

        const allFieldsValid = requiredField.every((field) => {
            return field && field.toString().trim().length > 0
        })

        const hasValidationErrors = Object.keys(errors).length > 0

        return !allFieldsValid || hasValidationErrors || loading || isSubmitting
    }, [
        typeOfRequest,
        subjectTitle,
        natureOfIncident,
        previouslyFunctioning,
        incidentStepByStep,
        expectedFunctionality,
        impactedSites,
        impactOnWork,
        loading,
        isSubmitting,
        errors,
        action,
        objective,
    ])

    useEffect(() => {
        if (userInfo.selectedSite) setValue('impactedSites', [userInfo.selectedSite.siteId])
    }, [userInfo.selectedSite])

    return (
        <form onSubmit={handleSubmit(onSubmit)}>
            {reviewMode ? (
                <ValidationScreen
                    subjectTitle={watch('subjectTitle')}
                    typeOfRequest={typeOfRequest}
                    isButtonDisabled={isButtonDisabled}
                    natureOfIncident={watch('natureOfIncident')}
                    description={watch('description')}
                    action={watch('action')}
                    userInfo={userInfo}
                    objective={watch('objective')}
                    impactOnWork={watch('impactOnWork')}
                    previouslyFunctioning={watch('previouslyFunctioning')}
                    incidentStepDecision={watch('incidentStepByStep')}
                    expectedFunctionality={watch('expectedFunctionality')}
                    sitesSelected={impactedSites}
                    onFinalSubmit={onFinalSubmit}
                    onBack={handleBackButton}
                    uploadedFiles={fileSelected ? fileSelected : []}
                />
            ) : (
                <Box
                    sx={{
                        display: 'grid',
                        gridTemplateColumns: 'repeat(2, 1fr)',
                        padding: '1rem',
                        gridRowGap: '2rem',
                        gridColumnGap: '1.5rem',
                    }}
                >
                    <Box
                        sx={{
                            backgroundColor: 'background.default',
                            padding: '1rem',
                            borderRadius: '0.5rem',
                            gridColumn: '1 / span 2',
                        }}
                    >
                        <Typography
                            sx={{
                                color: (theme) => theme.palette.text.primary,
                            }}
                        >
                            {translate('supportFeature.welcomeMessage')}
                        </Typography>
                    </Box>
                    <GenericTextField
                        name={'subjectTitle'}
                        control={control}
                        valueController={getValues('subjectTitle')}
                        required
                        label={translate('supportFeature.subject')}
                        placeholder={translate('supportFeature.subjectPlaceholder')}
                    />
                    <Box sx={{ gridColumn: '1 / span 2' }}>
                        <Box
                            sx={{
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'left',
                                marginBottom: '1rem',
                                width: '100%',
                            }}
                        >
                            <Typography sx={{ fontWeight: 500 }}>
                                {translate('supportFeature.typeOfRequest')}
                            </Typography>
                            <Tooltip placement="right" title={translate('supportFeature.mandatoryFieldsWarning')}>
                                <InfoOutlined sx={{ marginLeft: '10px', color: 'error.main', fontSize: '18px' }} />
                            </Tooltip>
                        </Box>

                        <ClnRadio
                            items={requestTypeItems}
                            value={requestType}
                            radioDirection="row"
                            onChange={(ri) => {
                                const newType = ri.value
                                setRequestType(newType)
                                setValue('typeOfRequest', String(newType))

                                if (newType === 'incident') {
                                    setValue('action', '')
                                    setValue('objective', '')
                                } else if (newType === 'improvement') {
                                    setValue('impactOnWork', undefined)
                                    setValue('impactedSites', undefined)
                                    setValue('natureOfIncident', '')
                                    setValue('previouslyFunctioning', '')
                                    setValue('expectedFunctionality', '')
                                    setValue('incidentStepByStep', '')
                                }
                            }}
                        />
                    </Box>

                    {requestType === RequestTypeEnum.INCIDENT && (
                        <>
                            <GenericTextField
                                name={'natureOfIncident'}
                                control={control}
                                valueController={getValues('natureOfIncident')}
                                required
                                label={translate('supportFeature.natureOfIncident')}
                                placeholder={translate('supportFeature.natureOfIncidentPlaceHolder')}
                                helperText={`"${translate('supportFeature.natureOfIncidentHelper')}"`}
                            />
                            <GenericTextField
                                name={'previouslyFunctioning'}
                                control={control}
                                valueController={getValues('previouslyFunctioning')}
                                required
                                label={translate('supportFeature.previouslyFunctioning')}
                                placeholder={translate('supportFeature.previouslyFunctioningPlaceHolder')}
                                helperText={`"${translate('supportFeature.previouslyFunctioningHelper')}"`}
                            />
                            <GenericTextField
                                name={'incidentStepByStep'}
                                control={control}
                                valueController={getValues('incidentStepByStep')}
                                required
                                label={translate('supportFeature.incidentStepByStepDescription')}
                                placeholder={translate('supportFeature.incidentStepDecisionPlaceHolder')}
                                helperText={`"${translate('supportFeature.incidentStepDecisionHelper')}"`}
                            />
                            <GenericTextField
                                name={'expectedFunctionality'}
                                control={control}
                                valueController={getValues('expectedFunctionality')}
                                required
                                label={translate('supportFeature.expectedFunctionality')}
                                placeholder={translate('supportFeature.expectedFunctionalityPlaceHolder')}
                                helperText={`"${translate('supportFeature.expectedFunctionalityHelper')}"`}
                            />
                            <GenericAutocomplete
                                name="impactOnWork"
                                multiple={false}
                                control={control}
                                className="no-translate"
                                options={impactOptions}
                                onChange={(newValue) => {
                                    setValue('impactOnWork', newValue || undefined)
                                }}
                                required
                                size="small"
                                label={`${translate('supportFeature.impactOnWork')} *`}
                                placeholderTextField={translate('supportFeature.impactOnWorkPlaceHolder')}
                                sx={{
                                    width: '100%',
                                }}
                            />
                            <GenericAutocomplete
                                name="impactedSites"
                                control={control}
                                options={sitesConverted}
                                onChange={(newValue) => {
                                    setValue('impactedSites', newValue || undefined)
                                }}
                                required
                                size="small"
                                label={`${translate('supportFeature.impactedSites')} *`}
                                placeholderTextField={
                                    impactedSites && impactedSites.length > 0
                                        ? ''
                                        : translate('supportFeature.impactedSitesPlaceHolder')
                                }
                            />
                        </>
                    )}
                    {requestType === RequestTypeEnum.IMPROVEMENT && (
                        <>
                            <GenericTextField
                                name="action"
                                control={control}
                                label={translate('supportFeature.actionIWant')}
                                valueController={getValues('action')}
                                required
                                placeholder={translate('supportFeature.actionPlaceHolder')}
                                helperText={`"${translate('supportFeature.actionHelper')}"`}
                            />
                            <GenericTextField
                                name="objective"
                                control={control}
                                label={translate('supportFeature.objectiveSoThat')}
                                valueController={getValues('objective')}
                                required
                                placeholder={translate('supportFeature.objectivePlaceHolder')}
                                helperText={`"${translate('supportFeature.objectiveHelper')}"`}
                            />
                        </>
                    )}
                    {typeOfRequest && (
                        <Box sx={{ gridColumn: '1 / span 2', gap: '1rem' }}>
                            <GenericTextField
                                name="description"
                                control={control}
                                label={translate('supportFeature.descriptionOptional')}
                                valueController={getValues('description')}
                                placeholder={translate('supportFeature.descriptionPlaceHolder')}
                                helperText=""
                            />
                            <UploadFiles
                                oldUploadedFiles={[]}
                                newUploadedFiles={fileSelected}
                                setOldUploadedFiles={() => {}}
                                setNewUploadedFiles={setFileSelected}
                                isEditable={!isSubmitting}
                                title={''}
                                showClearAllButton={false}
                                hideFiles={false}
                            />
                        </Box>
                    )}
                    <Box sx={{ display: 'flex', gap: '1rem', gridColumn: '2 /  2', justifyContent: 'end' }}>
                        <ClnButton
                            disabled={loadingSites || loading}
                            label={translate('common.cancel')}
                            variant="text"
                            onClick={() => {
                                reset({
                                    typeOfRequest: '',
                                    impactedSites: undefined,
                                    natureOfIncident: '',
                                    previouslyFunctioning: '',
                                    expectedFunctionality: '',
                                    incidentStepByStep: '',
                                    action: '',
                                    objective: '',
                                    description: '',
                                    subjectTitle: '',
                                    impactOnWork: undefined,
                                    uploadedFiles: undefined,
                                })
                            }}
                        />

                        <ClnButton
                            disabled={isButtonDisabled}
                            label={translate('supportFeature.reviewButton')}
                            onClick={handleSubmit(onSubmit)}
                            variant="contained"
                        />
                    </Box>
                </Box>
            )}
        </form>
    )
}
