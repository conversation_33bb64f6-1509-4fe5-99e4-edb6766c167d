import { CSSObject } from '@mui/material'

export const BoxTextField: CSSObject = {
    backgroundColor: 'primary.white',
    border: '1px solid',
    borderColor: 'otherColor.outlineBorder',
    borderRadius: '8px',
    flexGrow: '1',
    display: 'flex',
    flexDirection: 'column',
    padding: '20px',
    marginBottom: '1rem',
    gap: 2,
}

export const BoxInformationField: CSSObject = {
    wordWrap: 'break-word',
    whiteSpace: 'normal !important',
    maxHeight: '230px',
    overflowY: 'auto',
}

export const TopografyInformation: CSSObject = {
    wordBreak: 'break-word',
    whiteSpace: 'normal',
    overflowWrap: 'break-word',
    flexShrink: 1,
}

export const BoxFilesField: CSSObject = {
    backgroundColor: 'primary.white',
    border: '1px solid',
    borderColor: 'otherColor.outlineBorder',
    borderRadius: '8px',
    flexGrow: '1',
    display: 'flex',
    flexDirection: 'column',
    padding: '20px',
    marginBottom: '1rem',
    gap: 1,
    height: '200px',
    overflowY: 'auto',
}
