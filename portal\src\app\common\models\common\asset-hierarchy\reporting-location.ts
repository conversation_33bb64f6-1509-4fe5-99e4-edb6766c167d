import { ExternalEntity } from '..'
import { ReportingSite, ReportingUnitHomeTable } from '../../action-detail'

export interface ReportingLocation extends ExternalEntity {
    name: string
    description: string
    aliases: string[]
    reportingUnit: ReportingUnitHomeTable
    reportingSites: ReportingSite[]
    isActive?: boolean
}

export interface ReportingLocationRequest {
    search?: string
    reportingSiteExternalIds: string[]
    reportingUnitExternalIds?: string[]
}
