import { cognite } from '../configurations/cognite'
import { CogniteViewsResponseItem, Entity, FdmInstanceRequest } from '../models/common'
import { EdgeInstance } from '../models/common/cognite/edge-instance'
import { CogniteSdkEntity, removeTypename, safeStringify } from '../utils'

interface FdmClientConstructor {
    cogniteProject: string
    cogniteBaseUrl: string
    cogniteFdmModelSpace: string
    cogniteFdmModel: string
    cogniteFdmInstancesSpace: string
    cogniteApiVersion?: string
    getToken: () => Promise<string>
    isRelationshipProperty?: (entityName: string, entity: Entity, prop: string) => boolean
}

interface UpsertOptionsBase<T extends Entity> {
    entityName: string
    abortSignal?: AbortSignal
    autoCreateDirectRelations?: boolean
}

interface UpsertEdgeOptions<T extends Entity> extends UpsertOptionsBase<T> {
    edges: T[]
}

interface UpsertNodesOptions<T extends Entity> extends UpsertOptionsBase<T> {
    nodes: T[]
}

interface UpsertNodesAndEdgesOptions<T extends Entity> extends UpsertOptionsBase<T> {
    nodes: T[]
    edges: EdgeInstance[]
}

interface DeleteNodeOptions {
    externalIds: string[]
    abortSignal?: AbortSignal
    instanceType: InstanceTypes
    space?: string
}

interface AggregateNodeOptions {
    externalId: string
    propetyCount: string
    filter?: any
    groupBy?: string[]
    space?: string
    query?: string
}

interface QueryNodeOptions {
    withQuery: any
    select: any
    cursors?: any
}

interface SearchNodeOptions {
    view: ViewOptions
    query?: any
    instanceType?: InstanceTypes
    properties?: any[]
    filter?: any
    limit?: number
}

interface ViewOptions {
    type: string
    space?: string
    externalId: string
    version: string
}

interface BodyAggregateNodeOptions {
    aggregates: any[]
    filter?: any
    groupBy?: string[]
    limit?: number
    view: ViewOptions
    query?: string
}

interface BodyQueryNodeOptions {
    with: any
    select: any
    cursors?: any
}

interface BodySearchNodeOptions {
    view: any
    query?: string
    instanceType?: InstanceTypes
    properties?: string[]
    filter?: any
    sort?: any[]
    limit?: number
}

export type InstanceTypes = 'node' | 'edge'

const ENTITY_NAME_ENTITY_HISTORY = 'EntityHistory'
const ENTITY_HISTORY_NESTED_KEYS = ['value', 'oldValue']
const MAX_RETRY_ATTEMPTS = 5
const RETRY_COOLDOWN_MS = 2000

export class FdmClient {
    private baseUrl: string
    private fdmModelSpace: string
    private fdmModel: string
    private fdmInstancesSpace: string
    private views?: CogniteViewsResponseItem[]
    private getToken: () => Promise<string>
    private isRelationshipProperty: <T extends CogniteSdkEntity>(entityName: string, entity: T, prop: string) => boolean

    constructor({
        cogniteProject,
        cogniteBaseUrl,
        cogniteFdmModelSpace,
        cogniteFdmModel,
        cogniteFdmInstancesSpace,
        cogniteApiVersion = 'v1',
        getToken,
        isRelationshipProperty,
    }: FdmClientConstructor) {
        this.baseUrl = `${cogniteBaseUrl}/api/${cogniteApiVersion}/projects/${cogniteProject}/models`
        this.getToken = getToken
        this.fdmModelSpace = cogniteFdmModelSpace
        this.fdmModel = cogniteFdmModel
        this.fdmInstancesSpace = cogniteFdmInstancesSpace
        this.isRelationshipProperty = isRelationshipProperty || this.defaultIsRelationshipProperty
    }

    public async upsertNodes<T extends CogniteSdkEntity>({
        entityName,
        nodes,
        abortSignal,
        autoCreateDirectRelations,
    }: UpsertNodesOptions<T>) {
        const version = await this.getEntityVersion(entityName)
        const requestItems = this.mapToRequestItems('node', entityName, version, nodes)
        return await this.sendNodeOrEdgeRequest(requestItems, abortSignal, autoCreateDirectRelations)
    }

    public async upsertNodesAndEdges<T extends CogniteSdkEntity>({
        entityName,
        nodes,
        edges,
        abortSignal,
        autoCreateDirectRelations,
    }: UpsertNodesAndEdgesOptions<T>) {
        let version = '0'

        try {
            version = await this.getEntityVersion(entityName)
        } catch (e) {
            console.error(e)
        }

        const requestItems = this.mapToRequestItems('node', entityName, version, nodes)
        requestItems.push(...this.mapToEdgeItems(edges))

        return await this.sendNodeOrEdgeRequest(requestItems, abortSignal, autoCreateDirectRelations)
    }

    public async getEntityVersion(entityName: string) {
        await this.loadDatamodelVersion()
        if (!this.views) {
            throw new Error('Failed to load model version')
        }
        const version = this.views?.find((v) => v.externalId === entityName)?.version
        if (!version) {
            throw new Error('Failed to load model version')
        }
        return version
    }

    public async getAllVersions() {
        await this.loadDatamodelVersion()
        if (!this.views) {
            throw new Error('Failed to load model version')
        }
        const version = this.views
        if (!version) {
            throw new Error('Failed to load model version')
        }
        return version
    }

    public async queryNodesOrEdges({ withQuery, select, cursors }: QueryNodeOptions): Promise<any> {
        const url = `${this.baseUrl}/instances/query`

        const body: BodyQueryNodeOptions = {
            with: withQuery,
            select: select,
        }
        if (cursors) body.cursors = cursors

        let retryCount = 0
        while (retryCount < MAX_RETRY_ATTEMPTS) {
            try {
                const response = await fetch(url, {
                    method: 'POST',
                    headers: await this.buildHeaders(),
                    body: safeStringify(body),
                    signal: undefined,
                })

                if (response.ok) {
                    return await response.json()
                }

                if (response.status === 429) {
                    retryCount++
                    if (retryCount >= MAX_RETRY_ATTEMPTS) {
                        throw new Error('Failed to query nodes or edges due to rate limiting')
                    }

                    await new Promise((resolve) => setTimeout(resolve, Math.pow(2, retryCount) * RETRY_COOLDOWN_MS))
                    continue
                }

                throw new Error('Failed to query nodes or edges')
            } catch (error) {
                retryCount++
                if (retryCount >= MAX_RETRY_ATTEMPTS) {
                    const errorMessage = error instanceof Error ? error.message : 'Unknown error'
                    console.error('Failed to query nodes or edges.', errorMessage)
                    throw error
                }

                await new Promise((resolve) => setTimeout(resolve, Math.pow(2, retryCount) * RETRY_COOLDOWN_MS))
            }
        }
    }

    public async searchNodesOrEdges({ view, query, instanceType, properties, filter, limit }: SearchNodeOptions) {
        const url = `${this.baseUrl}/instances/search`

        const body: BodySearchNodeOptions = {
            view,
            sort: [
                {
                    property: ['node', 'externalId'],
                    direction: 'descending',
                },
            ],
            limit: 1000,
        }

        if (query) body.query = query
        if (instanceType) body.instanceType = instanceType
        if (properties) body.properties = properties
        if (filter) body.filter = filter
        if (limit) body.limit = limit

        const response = await fetch(url, {
            method: 'POST',
            headers: await this.buildHeaders(),
            body: safeStringify(body),
            signal: undefined,
        })
        if (!response.ok) {
            throw new Error('Failed to send node request')
        }
        return await response.json()
    }

    public async deleteNodesOrEdges({ externalIds, abortSignal, instanceType, space }: DeleteNodeOptions) {
        const url = `${this.baseUrl}/instances/delete`
        const body = {
            items: externalIds.map((externalId) => ({
                externalId,
                space: space ?? this.fdmInstancesSpace,
                instanceType: instanceType,
            })),
        }
        const response = await fetch(url, {
            method: 'POST',
            headers: await this.buildHeaders(),
            body: safeStringify(body),
            signal: abortSignal,
        })
        if (!response.ok) {
            throw new Error('Failed to send node request')
        }
        return await response.json()
    }

    private async loadDatamodelVersion() {
        if (this.views) {
            return
        }

        const url = `${this.baseUrl}/datamodels?space=${this.fdmModelSpace}`

        const response = await fetch(url, {
            method: 'GET',
            headers: await this.buildHeaders(),
        })

        if (!response.ok) {
            throw new Error('Failed to load model version')
        }

        this.views = (await response.json())?.items?.find((x: any) => x.externalId === this.fdmModel)?.views
    }

    private async buildHeaders(): Promise<HeadersInit> {
        const token = await this.getToken()
        const cogniteAppHeader = cognite.cogniteXCdpApp
        const cogniteAppId = cognite.appId
        return {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${token}`,
            [cogniteAppHeader]: cogniteAppId,
        }
    }

    private async buildNodeOrEdgeRequestCall(
        nodes: FdmInstanceRequest[],
        abortSignal?: AbortSignal,
        autoCreateDirectRelations?: boolean
    ) {
        const url = `${this.baseUrl}/instances`
        const body = {
            items: nodes,
            skipOnVersionConflict: false,
            autoCreateDirectRelations: autoCreateDirectRelations ?? true,
            replace: true,
        }
        return async () => {
            return await fetch(url, {
                method: 'POST',
                headers: await this.buildHeaders(),
                body: JSON.stringify(body),
                signal: abortSignal,
            })
        }
    }

    private async sendNodeOrEdgeRequest(
        nodes: FdmInstanceRequest[],
        abortSignal?: AbortSignal,
        autoCreateDirectRelations?: boolean
    ) {
        const postFunction = await this.buildNodeOrEdgeRequestCall(nodes, abortSignal, autoCreateDirectRelations)
        let retryCount = 0
        while (retryCount < MAX_RETRY_ATTEMPTS) {
            const response = await postFunction()
            if (response.ok) {
                return await response.json()
            }

            if (response.status !== 429) {
                throw new Error('Failed to send node request')
            }

            retryCount++
            if (retryCount >= MAX_RETRY_ATTEMPTS) {
                throw new Error('Failed to send node request due to rate limiting')
            }

            await new Promise((resolve) => setTimeout(resolve, Math.pow(2, retryCount) * RETRY_COOLDOWN_MS))
            continue
        }
    }

    private mapToRequestItems<T extends CogniteSdkEntity>(
        instanceType: InstanceTypes,
        entity: string,
        version: string,
        items: T[]
    ): FdmInstanceRequest[] {
        const isEdge = instanceType === 'edge'
        const result: FdmInstanceRequest[] = items.map((item) => {
            const { externalId, space, type, startNode, endNode, ...properties } = removeTypename(item)

            return {
                instanceType: instanceType,
                space: space ?? this.fdmInstancesSpace,
                externalId: externalId,
                type: isEdge && type ? { externalId: type, space: this.fdmModelSpace } : undefined,
                startNode:
                    isEdge && startNode ? { externalId: startNode, space: space ?? this.fdmInstancesSpace } : undefined,
                endNode:
                    isEdge && endNode ? { externalId: endNode, space: space ?? this.fdmInstancesSpace } : undefined,
                sources: [
                    {
                        source: {
                            type: 'view',
                            space: this.fdmModelSpace,
                            externalId: entity,
                            version: version,
                        },
                        properties: this.toRequestProperties(properties, space ?? this.fdmInstancesSpace, entity),
                    },
                ],
            }
        })
        return result
    }

    private mapToEdgeItems(items: EdgeInstance[]): FdmInstanceRequest[] {
        const result: FdmInstanceRequest[] = items.map((item) => {
            const { externalId, type, startNode, endNode, ...properties } = removeTypename(item)

            return {
                instanceType: 'edge',
                space: item.space,
                externalId: item.externalId,
                type: item.type,
                startNode: item.startNode,
                endNode: item.endNode,
            }
        })
        return result
    }

    private toRequestProperties<T extends CogniteSdkEntity>(codeList: T, space: string, entityName: string) {
        const result: any = {}
        for (const key in codeList) {
            const isRelationshipProperty = this.isRelationshipProperty(entityName, codeList, key)
            if (isRelationshipProperty) {
                const isTimeSeries = key.toLowerCase().endsWith('timeseries')
                const externalId = codeList[key].externalId
                result[key] = isTimeSeries ? externalId : { externalId, space: codeList[key].space ?? space }
                continue
            }
            result[key] = codeList[key]
        }
        return result
    }

    private defaultIsRelationshipProperty<T extends CogniteSdkEntity>(entityName: string, entity: T, prop: keyof T) {
        if (entityName === ENTITY_NAME_ENTITY_HISTORY && ENTITY_HISTORY_NESTED_KEYS.includes(prop.toString())) {
            return false
        }
        return Boolean(entity[prop]?.externalId)
    }
}
