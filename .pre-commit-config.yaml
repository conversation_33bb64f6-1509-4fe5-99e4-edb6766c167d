# All Files
repos:
-   repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v3.2.0
    hooks:
    -   id: trailing-whitespace
    -   id: end-of-file-fixer
    -   id: check-yaml

# Python Fast API / Function

## Linting
- repo: https://github.com/astral-sh/ruff-pre-commit
  rev: v0.11.2
  hooks:
    - id: ruff
      files: ^api/.*|^action-items-creator-function/.*
      types_or: [ python ]
      args: [--fix] # Comment to stop autofixing


## Formatting
- repo: https://github.com/psf/black-pre-commit-mirror
  rev: 25.1.0
  hooks:
    - id: black
      files: ^api/.*|^action-items-creator-function/.*
      language_version: python3.11

## Static type checking
- repo: https://github.com/RobertCraigie/pyright-python
  rev: v1.1.396
  hooks:
  - id: pyright
    args: [-p, api/pyproject.toml]
    files: ^api/.*
    types_or: [ python ]


# Front end

## Linting
- repo: local
  hooks:
    - id: eslint
      name: eslint
      language: system  # Uses system-installed ESLint
      entry: node portal/lint-staged.js --fix # Comment --fix to stop autofixing
      types: [file]
      files: ^portal/.*
      pass_filenames: false
      stages: [pre-commit]

## Formatting
- repo: https://github.com/pre-commit/mirrors-prettier
  rev: v3.1.0
  hooks:
  - id: prettier
    files: ^portal/.*
