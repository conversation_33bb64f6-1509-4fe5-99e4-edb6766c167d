import json as json

from clients.core.models import ServiceParams
from clients.notification.requests import NotificationRequest
from clients.notification.constants import NOTIFICATIONS_FUNCTION_EXTERNAL_ID


class NotificationClient:
    def __init__(self, params: ServiceParams):
        self._cognite_client = params.cognite_client
        self._settings = params.settings

    def send_notifications(self, notifications: list[NotificationRequest]):
        return self._cognite_client.functions.call(
            external_id=NOTIFICATIONS_FUNCTION_EXTERNAL_ID,
            data={
                "appId": self._settings.notifications_client_id,
                "items": [
                    notification.model_dump(by_alias=True)
                    for notification in notifications
                ],
            },
            wait=False,
        )
