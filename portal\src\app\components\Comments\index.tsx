import { Box, Typography, useMediaQuery } from '@mui/material'
import GenericFieldTitle from '../FieldsComponent/GenericFieldTitle'
import { Dispatch, SetStateAction, useState } from 'react'
import { CommentsModal } from './CommentsModal'
import { ClnButton, ClnPanel } from '@celanese/ui-lib'
import { Comment, Application, ActionDetailItem, CommentRequest } from '@/app/common/models/action-detail'

import * as S from './styles'
import { AzureFunctionClient } from '@/app/common/clients/azure-function-client'
import dayjs from 'dayjs'
import { useSnackbar } from '@/app/common/contexts/SnackbarContext'
import { translate } from '@/app/common/utils/generate-translate'
import { UserRolesPermission } from '@celanese/celanese-sdk'

const CommentSource = ({
    isMobile,
    comment,
    application,
}: {
    isMobile: boolean
    comment: Comment
    application: Application | undefined
}) => (
    <Typography variant="caption" sx={isMobile ? {} : S.CommentSourceDesktop}>
        {comment.isLegacy === true ? (
            <>
                {translate('details.components.comments.historicalDataFrom')} <strong>{application?.name}</strong>
            </>
        ) : (
            <>
                {translate('details.components.comments.createdVia')} <strong>{translate('app.boldTitle')}</strong>
            </>
        )}
    </Typography>
)

export function Comments({
    actionItem,
    activeUser,
    currentComments,
    setCurrentComments,
}: {
    actionItem: ActionDetailItem
    activeUser?: UserRolesPermission
    currentComments: Comment[]
    setCurrentComments: Dispatch<SetStateAction<Comment[]>>
}) {
    const { showSnackbar } = useSnackbar()

    const [isModalOpen, setIsModalOpen] = useState(false)

    const client = new AzureFunctionClient()
    const isMobile = useMediaQuery('(max-width:600px)')

    const handleOpenModal = () => setIsModalOpen(true)
    const handleCloseModal = () => setIsModalOpen(false)

    const handleAddComment = async (newComment: string) => {
        const timestamp = dayjs().utc().format('YYYY-MM-DDTHH:mm:ss[Z]')

        const newCommentObj: CommentRequest = {
            comment: newComment,
            actionExternalId: actionItem.externalId,
            userEmail: activeUser?.email ?? '',
            isPrivate: !!actionItem.isPrivate,
            reportingSiteExternalId: actionItem.reportingSite?.externalId ?? '',
            timestamp,
        }
        try {
            setCurrentComments((prev) => [
                ...prev,
                {
                    externalId: '',
                    space: actionItem.space,
                    isLegacy: false,
                    comment: newComment,
                    timestamp,
                    user: {
                        externalId: activeUser?.externalId ?? '',
                        space: 'UMG-COR-ALL-DAT',
                        firstName: activeUser?.firstName ?? '',
                        lastName: activeUser?.lastName ?? '',
                    },
                },
            ])
            await client.createActionComment(newCommentObj)
        } catch (error) {
            setCurrentComments((prev) => prev.slice(0, -1))
            showSnackbar(translate('alerts.errorAddComment'), 'error', 'action-comments')
        }
    }

    return (
        <Box sx={S.Container}>
            <Box id="comments-field" sx={S.Header}>
                <GenericFieldTitle fieldName={translate('requestModal.comments')} isSubHeader />
                <GenericFieldTitle
                    fieldName={translate('details.components.comments.fullComments')}
                    isDetailsExternalId
                />
                <Box sx={S.CommentsContainer}>
                    {currentComments.map((comment, index) => {
                        const fullName = comment.user ? `${comment.user.firstName} ${comment.user.lastName}` : ''

                        return (
                            <ClnPanel key={index} sx={S.CommentBox}>
                                {isMobile && (
                                    <CommentSource
                                        isMobile={true}
                                        comment={comment}
                                        application={actionItem.application}
                                    />
                                )}
                                <Typography variant="body2" sx={S.CommentInDetails}>
                                    {comment.comment}
                                </Typography>
                                <Box sx={S.CommentFooter}>
                                    {(comment.timestamp || comment.user) && (
                                        <Typography variant="caption">
                                            {translate('details.components.comments.commentSent')}
                                            {comment.timestamp && dayjs(comment.timestamp).format(' MM/DD/YYYY HH:mm')}
                                            {comment.user && (
                                                <>
                                                    {' '}
                                                    {translate('details.components.comments.by')}{' '}
                                                    <strong>{fullName}</strong>
                                                </>
                                            )}
                                        </Typography>
                                    )}
                                    {!isMobile && (
                                        <CommentSource
                                            isMobile={false}
                                            comment={comment}
                                            application={actionItem.application}
                                        />
                                    )}
                                </Box>
                            </ClnPanel>
                        )
                    })}
                </Box>
            </Box>
            {!currentComments.length && (
                <Box
                    sx={{
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                        height: '100%',
                        width: '100%',
                    }}
                >
                    <Typography
                        textAlign="center"
                        color="text.secondary"
                        sx={{
                            paddingTop: isMobile ? '40px' : '0',
                        }}
                    >
                        {translate('details.components.comments.noCommentsAvailable')}
                    </Typography>
                </Box>
            )}
            <ClnButton
                size="small"
                variant="outlined"
                onClick={handleOpenModal}
                label={translate('details.components.comments.expandEAddComments')}
            />
            {isModalOpen && (
                <CommentsModal
                    onClose={handleCloseModal}
                    comments={currentComments}
                    application={actionItem.application}
                    onAddComment={handleAddComment}
                />
            )}
        </Box>
    )
}
