import { Box, useMediaQuery, useTheme } from '@mui/material'
import { ClnButtonProps, ActionIcon, MatIcon } from '@celanese/ui-lib'
import { useEffect, useMemo, useState } from 'react'
import { useAuthGuard } from '../../common/hooks/useAuthGuard'
import { EntityType, GetSpace } from '../../common/utils/space-util'
import { ReferenceForm } from './ReferenceForm'
import { useActionItemReferenceMutations } from '@/app/common/hooks/mutations/useActionItemReferenceMutations'
import { ApolloQueryResult } from '@apollo/client'
import { Reference } from '@/app/common/models/reference'
import { FilterOptionsReferencesConfiguration } from '@/app/common/models/admin-settings/filter-reference'
import { ReferenceTabFilter } from './ReferenceTabFilter'
import { useApplications } from '@/app/common/hooks/asset-hierarchy/useApplications'
import { GridColDef, GridColumnVisibilityModel, GridRowsProp } from '@mui/x-data-grid-pro'
import { DataGridTable } from '@/app/components/PaginatedTable/DataGridTable'
import { useSnackbar } from '@/app/common/contexts/SnackbarContext'
import { translate } from '@/app/common/utils/generate-translate'
import { determineActiveFilterCount } from '@/app/common/utils/active-filter-count'
import { getLocalUserSite } from '@celanese/celanese-ui'
import { ROWS_PER_PAGE_OPTIONS_ADMIN_SCREEN } from '@/app/common/utils'

type ReferenceTabProps = {
    loading: boolean
    references: Reference[]
    refetchReference: () => Promise<ApolloQueryResult<any>>
}

export const ReferenceTab = (props: ReferenceTabProps) => {
    const { references, refetchReference } = props

    const { showSnackbar } = useSnackbar()

    const theme = useTheme()
    const isMobile = useMediaQuery(theme.breakpoints.down('md'))

    const { checkPermissionsFromComponents } = useAuthGuard()
    const [saveReference] = useActionItemReferenceMutations()

    const { applications } = useApplications()

    //Drawer
    const [referenceToEdit, setReferenceToEdit] = useState<Reference | undefined>()
    const [isEdit, setIsEdit] = useState<boolean>(false)
    const [openReferenceDrawer, setOpenReferenceDrawer] = useState<boolean>(false)

    //Filter
    const [search, setSearch] = useState<string>('')

    const storedFilterInfo = sessionStorage.getItem(`admin-reference-filterInfo`)
    const parsedFilterInfo = storedFilterInfo ? JSON.parse(storedFilterInfo) : {}

    const [filterOptions, setFilterOptions] = useState<FilterOptionsReferencesConfiguration>({
        application: [],
    })
    const [filterActions, setFilterActions] = useState<FilterOptionsReferencesConfiguration>({ ...parsedFilterInfo })

    const applyFilters = async (filters: any) => {
        setFilterActions(filters)
        resetPageProps()
    }

    useEffect(() => {
        buildFilterOptions()
    }, [references])

    const buildFilterOptions = (): void => {
        const options: FilterOptionsReferencesConfiguration = {
            application: [],
        }
        references.forEach((rf) => {
            if (
                rf.application &&
                (rf.application.alias || rf.application.name) &&
                !options.application.includes(rf.application.alias || rf.application.name || '')
            ) {
                options.application.push(rf.application.alias || rf.application.name || '')
            }
        })
        setFilterOptions(options)
    }

    //Filter data in frontend according to filter
    const filteredData: Reference[] = useMemo(() => {
        const filteredData = references.filter((rf: Reference) => {
            //Filter
            if (filterActions) {
                if (filterActions.application?.length > 0) {
                    if (!filterActions.application.includes(rf.application?.alias ?? rf.application?.name ?? '')) {
                        return false
                    }
                }
            }

            //Search
            if (search && search?.length > 0) {
                const searchData = search.toLowerCase()
                const id = rf.externalId?.toLowerCase()
                const name = rf.name?.toLowerCase()
                const description = rf.description.toLowerCase()

                if (!id?.includes(searchData) && !name?.includes(searchData) && !description?.includes(searchData)) {
                    return false
                }
            }
            return true
        })
        return filteredData
    }, [references, filterActions, search])

    //Table
    const headCells: GridColDef[] = useMemo(
        () => [
            {
                field: 'id',
                headerName: translate('adminSettings.table.headers.id'),
                filterable: false,
                flex: 1,
                renderHeader: () => (
                    <span data-test="admin-reference-tab-id_table_sort" data-origin="aim">
                        {translate('adminSettings.table.headers.id')}
                    </span>
                ),
            },
            {
                field: 'title',
                headerName: translate('adminSettings.table.headers.title'),
                filterable: false,
                flex: 1,
                renderHeader: () => (
                    <span data-test="admin-reference-tab-title_table_sort" data-origin="aim">
                        {translate('adminSettings.table.headers.title')}
                    </span>
                ),
            },
            {
                field: 'description',
                headerName: translate('adminSettings.table.headers.description'),
                filterable: false,
                flex: 1,
                renderHeader: () => (
                    <span data-test="admin-reference-tab-description_table_sort" data-origin="aim">
                        {translate('adminSettings.table.headers.description')}
                    </span>
                ),
            },
            {
                field: 'application',
                headerName: translate('adminSettings.table.headers.application'),
                filterable: false,
                flex: 1,
                renderHeader: () => (
                    <span data-test="admin-reference-tab-application_table_sort" data-origin="aim">
                        {translate('adminSettings.table.headers.application')}
                    </span>
                ),
            },
        ],
        []
    )

    const [currentPage, setCurrentPage] = useState(0)
    const [rowsPerPage, setRowsPerPage] = useState(ROWS_PER_PAGE_OPTIONS_ADMIN_SCREEN[0])

    const resetPageProps = () => {
        setCurrentPage(0)
    }

    const tableRows: GridRowsProp = useMemo(() => {
        function convertActionItemDataToRows(items: any[]) {
            return items.map((item) => ({
                id: `${item.externalId}`,
                title: `${item.name}`,
                description: `${item.description}`,
                application: `${item.application?.alias ?? item.application?.name ?? ''}`,
            }))
        }

        const actionsRows =
            filteredData != null && filteredData.length > 0 ? convertActionItemDataToRows(filteredData) : []

        return actionsRows
    }, [filteredData])

    const actions: ActionIcon[] = [
        {
            icon: <MatIcon icon="edit" />,
            onClick: (id) => {
                const rfItem = references.find((rf) => rf.externalId == id)
                handleEditClick(rfItem)
            },
        },
    ]

    const buttons: ClnButtonProps[] = [
        {
            label: isMobile ? '' : translate('adminSettings.table.newReference'),
            startIcon: isMobile ? <MatIcon icon="add" /> : undefined,
            variant: 'contained',
            sxProps: isMobile
                ? {
                      '& .MuiButton-startIcon': {
                          marginRight: 0,
                      },
                  }
                : undefined,
            onClick: () => setOpenReferenceDrawer(true),
        },
    ]

    const handleEditClick = (reference: Reference | undefined) => {
        setReferenceToEdit(reference)
        setIsEdit(true)
        setOpenReferenceDrawer(true)
    }

    //Form
    const generateReferenceExternalId = () => {
        return `ACTREF-${getLocalUserSite()?.siteCode}-Reference-${references.length}`
    }

    const handleCloseForm = () => {
        setIsEdit(false)
        setOpenReferenceDrawer(false)
        setReferenceToEdit(undefined)
    }

    const handleSubmitForm = async (reference: any, onSuccess?: () => void) => {
        const { externalId, name, description, application } = reference

        let allReferences = references

        if (externalId) {
            allReferences = references.filter((reference: any) => reference.externalId !== externalId)
        }

        const referenceNames = allReferences.map((reference: any) => reference.name)
        const isDuplicateName = referenceNames.includes(name)

        if (isDuplicateName) {
            showAlert('alerts.errorOcurred', true)
            return
        }

        const dataToRequest: Reference = {
            externalId: externalId || generateReferenceExternalId(),
            name,
            description,
            application: { externalId: application, space: GetSpace(EntityType.UMG) },
        }

        try {
            await saveReference(dataToRequest)
            showAlert('alerts.dataSavedWithSuccess', false)
            refetchReference()
            handleCloseForm()
            onSuccess?.()
        } catch (error) {
            showAlert('alerts.unexpectedErrorOcurred', true)
        }
    }

    const showAlert = (messageKey: string, isError: boolean) => {
        showSnackbar(translate(messageKey), isError ? 'error' : 'success', 'admin-reference')
    }

    enum TableTranslateKey {
        Search = 'Search',
        RowsPerPage = 'Rows per page',
        Of = 'of',
        Filters = 'Filters',
        Actions = 'Action',
    }

    const translatedLabels = new Map<TableTranslateKey, string>()
    translatedLabels.set(TableTranslateKey.Search, translate('common.search'))
    translatedLabels.set(TableTranslateKey.RowsPerPage, translate('common.rowsPerPage'))
    translatedLabels.set(TableTranslateKey.Of, translate('common.of'))
    translatedLabels.set(TableTranslateKey.Filters, translate('common.filters'))
    translatedLabels.set(TableTranslateKey.Actions, translate('common.action'))

    const customPopoverContent = useMemo(() => {
        return (
            <ReferenceTabFilter
                data={filterOptions}
                defaultFilter={filterActions}
                onSubmit={(filters) => {
                    applyFilters(filters)
                    sessionStorage.setItem(`admin-reference-filterInfo`, JSON.stringify(filters))
                }}
            />
        )
    }, [translate, filterOptions, filterActions])

    const initialStateColumnVisibilityModel: GridColumnVisibilityModel = useMemo(() => {
        return {
            id: false,
        }
    }, [])

    return (
        <Box
            sx={{
                margin: '1rem 0px',
                display: 'flex',
                flexGrow: 1,
                flexDirection: 'column',
            }}
        >
            <DataGridTable
                id="admin-reference"
                initialStateColumnVisibilityModel={initialStateColumnVisibilityModel}
                isLoading={props.loading}
                initialColumnDefs={headCells}
                onSearchSubmit={(value: string) => setSearch(value)}
                customButtons={buttons}
                rows={tableRows.slice(currentPage * rowsPerPage, currentPage * rowsPerPage + rowsPerPage)}
                rowsPerPageOptions={ROWS_PER_PAGE_OPTIONS_ADMIN_SCREEN}
                currentPage={currentPage}
                rowsPerPage={rowsPerPage}
                setRowsPerPage={setRowsPerPage}
                customPopoverContent={customPopoverContent}
                totalPages={Math.ceil(tableRows.length / rowsPerPage)}
                setCurrentPage={setCurrentPage}
                actions={checkPermissionsFromComponents(ReferenceTab.name) ? actions : undefined}
                activeFiltersCount={determineActiveFilterCount(filterActions)}
            />
            <ReferenceForm
                onClose={() => setOpenReferenceDrawer(false)}
                applications={applications}
                openDrawer={openReferenceDrawer}
                onSubmitCallback={handleSubmitForm}
                onCloseCallback={handleCloseForm}
                referenceToEdit={referenceToEdit}
                isEdit={isEdit}
            />
        </Box>
    )
}
