import azure.functions as func

from functions.endpoints.action import bp as action
from functions.endpoints.action_comment import bp as action_comments
from functions.endpoints.aggregate import bp as aggregate
from functions.endpoints.category_configuration import bp as category_configuration
from functions.endpoints.create_action_item_trigger import (
    bp as create_action_item_trigger,
)
from functions.endpoints.create_notifications_by_actions_ids import (
    bp as create_notifications_by_actions_ids,
)
from functions.endpoints.delete_action_item_trigger import (
    bp as delete_action_item_trigger,
)
from functions.endpoints.equipment import bp as equipment
from functions.endpoints.external_source import bp as external_sources
from functions.endpoints.file import bp as files
from functions.endpoints.functional_location import bp as functional_location
from functions.endpoints.get_action_item_by_template_id import (
    bp as get_action_item_by_template_id,
)
from functions.endpoints.get_action_item_trigger_conditions import (
    bp as get_action_item_trigger_conditions,
)
from functions.endpoints.get_action_item_triggers import bp as get_action_item_triggers
from functions.endpoints.get_action_items_reference import (
    bp as get_action_items_reference,
)
from functions.endpoints.get_approval_workflow import bp as get_approval_workflow
from functions.endpoints.get_bulk_upload_template import (
    bp as get_bulk_upload_template,
)
from functions.endpoints.integration import bp as integration
from functions.endpoints.reporting_location import bp as reporting_location
from functions.endpoints.reporting_unit import bp as reporting_unit
from functions.endpoints.source_events import bp as source_events
from functions.endpoints.template import bp as templates
from functions.endpoints.upsert_source_event import bp as upsert_source_event
from functions.endpoints.user_complements import bp as user_complements
from functions.event_processor import bp as event_processor_bp
from functions.overdue_notifier import bp as overdue_notifier_bp
from functions.recurrence_processor import bp as recurrence_processor_bp

app = func.FunctionApp()

app.register_functions(event_processor_bp)
app.register_functions(recurrence_processor_bp)
app.register_functions(overdue_notifier_bp)

app.register_functions(action)
app.register_functions(aggregate)
app.register_functions(category_configuration)
app.register_functions(integration)
app.register_functions(user_complements)
app.register_functions(files)
app.register_functions(source_events)
app.register_functions(action_comments)
app.register_functions(external_sources)
app.register_functions(functional_location)
app.register_functions(equipment)
app.register_functions(reporting_unit)
app.register_functions(reporting_location)
app.register_functions(templates)

app.register_functions(get_action_item_triggers)
app.register_functions(delete_action_item_trigger)
app.register_functions(get_action_item_trigger_conditions)
app.register_functions(get_action_items_reference)
app.register_functions(create_action_item_trigger)
app.register_functions(upsert_source_event)
app.register_functions(create_notifications_by_actions_ids)
app.register_functions(get_bulk_upload_template)
app.register_functions(get_action_item_by_template_id)
app.register_functions(get_approval_workflow)
