import { useCallback, useState } from 'react'
import { useCognite } from './useCognite'

export type AggregateHookResult = [AggregateFunction, AggregateState]

export type AggregateFunctionParams = {
    withQuery: any
    select: any
    cursors?: any
}

export type ItemsFunctionPromise = {
    items: any
    nextCursor?: any
}

export type AggregateParams = {
    aggregate: string
    property: string
    value: string
}

export type AggregateFunction = (params: AggregateFunctionParams) => Promise<ItemsFunctionPromise>
export type AggregateState = {
    loading: boolean
    called: boolean
    error: any
}

export function useFdmQuery(): AggregateHookResult {
    const { fdmClient: client } = useCognite()
    const [error, setError] = useState<any | undefined>()
    const [loading, setLoading] = useState<boolean>(false)
    const [called, setCalled] = useState<boolean>(false)

    const queryFunction = useCallback(
        ({ withQuery, select, cursors }: AggregateFunctionParams) => {
            setLoading(true)
            setCalled(true)
            return client
                .queryNodesOrEdges({
                    withQuery,
                    select,
                    cursors,
                })
                .catch((error) => setError(error))
                .finally(() => setLoading(false))
        },
        [client]
    )

    return [queryFunction, { error, loading, called }]
}
