import { FdmClient } from '../../../clients'
import { EdgeInstance } from './edge-instance'

export type MutationHookResult<T> = [MutationFunction<T>, MutationState]

export type MutationFunction<T> = (items: T[], edges: EdgeInstance[]) => Promise<MutationFunctionResult<T>>

export type MutationFunctionResult<T> = {
    ok: boolean
    data?: T[]
    error?: any
}

export type MutationState = {
    readonly loading: boolean
    readonly called: boolean
    readonly error: any
    readonly data?: any
    readonly client: FdmClient
    readonly reset: () => void
}
