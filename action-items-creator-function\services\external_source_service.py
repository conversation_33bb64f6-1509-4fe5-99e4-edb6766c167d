from typing import NoReturn

from clients.core.constants import ActionSourceTypeEnum
from clients.external_source.errors import DataModelQueryError
from clients.external_source.external_source_client import ExternalSourceClient
from clients.external_source.requests import ExternalSourceRequest
from clients.external_source.responses import ExternalSourceDetailsResponse


class ExternalSourceService:
    """Service for retrieving external source details from RCA and Quality clients."""

    def __init__(self, external_source_client: ExternalSourceClient) -> None:
        """Initialize ExternalSourceService with RCA and Quality clients."""
        self._client = external_source_client

    def get_external_source_details(
        self,
        request: ExternalSourceRequest,
    ) -> ExternalSourceDetailsResponse | None:
        """
        Retrieve external source details from the appropriate data model based on the action's source type.

        Args:
            request (ExternalSourceRequest): Request containing the external source ID and source type.

        Returns:
            ExternalSourceDetailsResponse | None: Object containing the results from the queried models.

        Raises:
            ValueError: If the source type is not supported.
            DataModelQueryError: If an error occurs while querying the data models.

        """
        if not request.source_type.startswith("AST-RCA"):
            self._handle_unsuported_type(request.source_type)

        external_event_id = request.source_id.replace("EVEN-", "")
        event_analysis = self._client.root_cause_analysis.get_event_analysis(
            external_event_id,
        )

        if event_analysis is None:
            return None

        try:
            source_type = request.source_type
            event_details = None

            match source_type:
                case (
                    ActionSourceTypeEnum.RCA_QR_EVTY_QAR
                    | ActionSourceTypeEnum.RCA_QR_EVTY_RAR
                    | ActionSourceTypeEnum.RCA_QR_EVTY_CCI
                ):  # Quality Awareness | Reliability Awareness | Customer Complaint
                    event_details = self._client.quality_reliability.get_event(
                        external_id=external_event_id,
                    )

                case (
                    ActionSourceTypeEnum.RCA_EVN_INV_RCAABEVENT
                    | ActionSourceTypeEnum.RCA_EVN_INV_RCACEVENT
                ):  # RCAAB/ Event
                    event_details = (
                        self._client.root_cause_analysis.get_event_investigation(
                            external_id=external_event_id,
                        )
                    )

                case (
                    ActionSourceTypeEnum.RCA_WI_EVTY_ADM
                    | ActionSourceTypeEnum.RCA_WI_EVTY_ENG
                    | ActionSourceTypeEnum.RCA_WI_EVTY_MNTC
                    | ActionSourceTypeEnum.RCA_WI_EVTY_QA
                ):  # RCAC Work Process
                    event_details = (
                        self._client.root_cause_analysis.get_work_process_investigation(
                            external_id=external_event_id,
                        )
                    )

                case (
                    ActionSourceTypeEnum.RCA_WI_SAP_Q1
                    | ActionSourceTypeEnum.RCA_WI_SAP_Q3
                ):  # SAP Event
                    event_details = self._client.quality.get_notification(
                        external_id=external_event_id,
                    )

                case _:
                    self._handle_unsuported_type(source_type)

        except ValueError:
            raise

        except Exception as e:
            error_msg = f"Failed to retrieve data from data model for source_id={request.source_id}"
            self._client.root_cause_analysis.log.exception(error_msg)
            raise DataModelQueryError(request.source_id, error_msg) from e

        return ExternalSourceDetailsResponse(
            general=event_analysis,
            specific=event_details,
        )

    def _handle_unsuported_type(self, source_type: str) -> NoReturn:
        error_msg = f"Unsupported source type: {source_type}"
        self._client.root_cause_analysis.log.exception(error_msg)
        raise ValueError(error_msg)
