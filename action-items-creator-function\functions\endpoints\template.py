import json
import logging

import azure.functions as func

from clients.core.constants import APPLICATION_JSON
from clients.templates.requests import (
    GetTemplatesByUserIdRequest,
)
from infra.action_item_client_factory import ActionItemClientFactory
from services.template_service import TemplateService

bp = func.Blueprint()
logging.basicConfig(format="%(message)s", level=logging.INFO)


@bp.function_name(name="GetTemplatesByUserId")
@bp.route(
    "get-templates-by-user-id",
    methods=["get"],
    auth_level=func.AuthLevel.ANONYMOUS,
)
async def get_templates_by_user_id_request(req: func.HttpRequest) -> func.HttpResponse:
    """
    Retrieve templates for a specific user by their user ID.

    Args:
        req (func.HttpRequest): The HTTP request object containing parameters.

    Returns:
        func.HttpResponse: The HTTP response with the retrieved templates or error message.

    """
    try:
        auth_header = req.headers.get("Authorization")
        if auth_header and auth_header.startswith("Bearer "):
            token_request = auth_header[len("Bearer ") :]
        else:
            token_request = auth_header

        template_request_params = req.params.get("templateRequest", "{}")
        template_request = json.loads(template_request_params)

        logging.info("Function GetTemplatesByUserId started")

        request = GetTemplatesByUserIdRequest.model_validate(template_request)

        service = TemplateService(
            ActionItemClientFactory.retriever(override_token=token_request),
        )
        items = await service.get_templates_by_user_id(request=request)

        logging.info(f"Finishing execution - Items Retrieved : {len(items)}")

        return func.HttpResponse(
            json.dumps({"items": items}),
            mimetype=APPLICATION_JSON,
            status_code=200,
        )
    except Exception as e:
        logging.error(f"Exception found: {e}", exc_info=True)
        return func.HttpResponse(json.dumps({"error": str(e)}), status_code=500)
