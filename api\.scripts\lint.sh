#!/bin/bash

# Run Ruff lint check
printf "\n\n ====================== Running RUFF ====================== \n\n"
ruff check
CHECK_RESULT=$?
if [ $CHECK_RESULT -eq 0 ]; then
    printf "✅"
fi

# Run black formatter check
printf "\n\n ====================== Running BLACK ====================== \n\n"
black app tests --check
FORMAT_RESULT=$?
if [ $FORMAT_RESULT -eq 0 ]; then
    printf "✅"
fi

# Run Pyright
printf "\n\n ====================== Running PYRIGHT ====================== \n\n"
pyright
STATIC_RESULT=$?
if [ $STATIC_RESULT -eq 0 ]; then
    printf "✅"
fi

printf "\n\n ====================== Running PYTEST ====================== \n\n"
python -m pytest
TEST_RESULT=$?
if [ $TEST_RESULT -eq 0 ]; then
    printf "✅"
fi

# Combine the results and exit
if [ $CHECK_RESULT -ne 0 ] || [ $FORMAT_RESULT -ne 0 ] || [ $STATIC_RESULT -ne 0 ] || [ $TEST_RESULT -ne 0 ]; then
    printf "\n\n💥 Errors found please check all the log messages above"
    exit 1
else
    printf "\n\n✅ No errors found"
    exit 0
fi
