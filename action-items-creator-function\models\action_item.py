import os
import sys
from datetime import date
from typing import Annotated, Optional

from annotated_types import Len
from pydantic import BaseModel, Field, model_validator

from clients.actions.constants import RecurrenceTypeExternalId
from clients.core.utils import to_user_azure_attribute
from models.base_recurrence_instance import BaseRecurrenceInstance
from models.node_reference import NodeReference, PossiblyNewNode
from models.source_event import SourceEvent, SourceEventHistory

script_dir = os.path.dirname(os.path.abspath(__file__))
project_dir = os.path.dirname(script_dir)
sys.path.append(project_dir)
from clients.actions.validators import validate_recurrence


class ApprovalWorkflowStep(PossiblyNewNode):
    approvalWorkflow: NodeReference
    step: int
    startDate: date = Field(default=date.today())
    description: str = Field(min_length=1)
    status: NodeReference
    approvalCondition: NodeReference
    users: list[NodeReference] | None = None
    roles: list[NodeReference] | None = None
    approvalWorkflowConsentType: NodeReference


class ApprovalWorkflowStepUpdate(PossiblyNewNode):
    status: NodeReference | None = None


class ApprovalWorkflowUpdate(PossiblyNewNode):
    status: NodeReference | None
    steps: Annotated[list[ApprovalWorkflowStepUpdate], Len(min_length=1)] | None


class ApprovalWorkflow(PossiblyNewNode):
    currentStep: int = Field(default=1)
    startDate: date = Field(default=date.today())
    createdBy: NodeReference
    status: NodeReference
    steps: Annotated[list[ApprovalWorkflowStep], Len(min_length=1)]


class ActionItemLink(PossiblyNewNode):
    action: NodeReference | None = None
    link: str = Field(min_length=1)
    description: str | None = Field(min_length=1, default=None)


class StatusHistoryInstance(NodeReference):
    action: NodeReference
    status: NodeReference
    friendlyName: str = Field(default="")
    changedAt: str
    statusSubject: NodeReference | None = None


class MetadataField(NodeReference):
    action: NodeReference
    metadataType: NodeReference
    value: str


class RecurrenceInstanceType(BaseModel):
    externalId: RecurrenceTypeExternalId
    space: str | None = Field(min_length=1, default=None)


class RecurrenceInstance(BaseRecurrenceInstance):
    recurrenceType: RecurrenceInstanceType
    sourceEvent: SourceEvent | None = None
    description: str | None = Field(default=None)

    @model_validator(mode="after")
    def check_recurrence_consistency(self) -> "RecurrenceInstance":
        """
        Validate the consistency of recurrence data for a RecurrenceInstance.

        This method calls `validate_recurrence` to check if the recurrence attributes
        are correctly defined based on the recurrence type.

        Args:
            self (RecurrenceInstance): The instance of RecurrenceInstance being validated.

        Returns:
            RecurrenceInstance: The validated instance if all checks pass.

        Raises:
            ValueError: If any recurrence attribute is inconsistent with the expected
                        recurrence type.

        """
        validate_recurrence(
            recurrence_type_external_id=self.recurrenceType.externalId,
            week_days=self.weekDays,
            months=self.months,
            day_of_the_month=self.dayOfTheMonth,
            quarters=self.quarters,
            month_of_the_year=self.monthOfTheYear,
            next_dates=self.nextDates,
        )
        return self


class ActionItem(PossiblyNewNode):
    externalId: str | None = None
    parent: NodeReference | None = None
    title: str
    description: str
    statusHistory: list[StatusHistoryInstance]
    sourceEventHistory: list[SourceEventHistory] | None = None
    sourceEvent: list[SourceEvent] | None = None
    currentStatus: NodeReference
    approvalWorkflow: ApprovalWorkflow | None = None
    recurrenceInstance: RecurrenceInstance | None = None
    owner: NodeReference
    reportingUnit: NodeReference | None = None
    reportingLocation: NodeReference | None = None
    links: Annotated[list[ActionItemLink], Len(min_length=1)] | None = None
    attachments: list[str] | None = None
    sourceInformation: str | None = None
    application: NodeReference
    category: NodeReference
    subCategory: NodeReference | None = None
    siteSpecificCategory: NodeReference | None = None
    assignees: list[NodeReference] | None = None
    actionItemKind: NodeReference
    assignedTo: NodeReference | None = None
    assignmentDate: str | None = None
    displayDueDate: str | None = None
    dueDate: str | None = None
    displayDueDate: str | None = None
    approvalDate: str | None = None
    verificationDate: str | None = None
    conclusionDate: str | None = None
    voeActionItem: str | None = None
    estimatedCost: float | None = None
    price: str | None = None
    priceCurrencyKey: str | None = None
    priority: str | None = None
    evidenceRequired: bool | None = None
    estimatedGrade: str | None = None
    voeActionItemStatus: str | None = None
    isPlantShutdownRequired: str | None = None
    actionTaken: str | None = None
    objectType: str | None = None
    objectExternalId: str | None = None
    reportingSite: NodeReference
    reportingLine: NodeReference | None = None
    businessLine: NodeReference | None = None
    createdBy: NodeReference
    modifiedBy: NodeReference | None = None
    sourceId: str | None = None
    sourceType: NodeReference | None = None
    sourceEventId: str | None = None
    createdAt: str | None = None
    isPrivate: bool | None = None
    viewUsers: list[NodeReference] | None = None
    viewRoles: list[NodeReference] | None = None
    viewTeams: list[NodeReference] | None = None
    views: list[str] | None = None
    metadatas: list[MetadataField] | None = None
    _is_new: bool = True
    _event_id: str | None = None
    _approver_id: str | None = None
    _verifier_id: str | None = None
    _target_dataset_id: int | None = None

    sortCategory: str | None = None
    sortSubCategory: str | None = None
    sortSiteSpecificCategory: str | None = None
    sortOwner: str | None = None
    sortReportingUnit: str | None = None
    sortReportingLocation: str | None = None
    sortAssignee: str | None = None
    sortApplication: str | None = None
    sortCurrentStatus: str | None = None
    sortApprover: str | None = None
    sortVerifier: str | None = None
    sortSourceEventTitle: str | None = None
    sortTitle: str | None = None
    sortSourceInformation: str | None = None

    @model_validator(mode="after")
    def check_recurrence_consistency(self) -> "ActionItem":
        """
        Validate the consistency of recurrence data for an ActionItem.

        Args:
            self (ActionItem): The instance of the ActionItem being validated.

        Returns:
            ActionItem: The validated instance if all checks pass.

        Raises:
            ValueError: If:
                - The action item is active and has a custom recurrence type (CUSTOM),
                  but `nextDates` is missing or empty.
                - The action item is active and has a non-CUSTOM recurrence type,
                  but `nextDates` contains more than one date.

        """
        if self.recurrenceInstance is None:
            return self

        recurrence_type = self.recurrenceInstance.recurrenceType.externalId
        status = self.currentStatus.externalId

        if (
            status == "active"
            and recurrence_type == RecurrenceTypeExternalId.CUSTOM
            and (
                self.recurrenceInstance.nextDates is None
                or len(self.recurrenceInstance.nextDates) == 0
            )
        ):
            msg = "nextDates must be provided for custom action items"
            raise ValueError(msg)
        if (
            status == "active"
            and recurrence_type != RecurrenceTypeExternalId.CUSTOM
            and (
                self.recurrenceInstance.nextDates is not None
                and len(self.recurrenceInstance.nextDates) > 1
            )
        ):
            msg = "only one nextDate must be provided for recurring action items"
            raise ValueError(msg)

        return self

    def generate_views(self, source_event: Optional[SourceEvent]) -> None:
        """Generate the `views` field by aggregating unique user, role, and team externalIds from the action and the source event."""

        def extract_ids(
            nodes: Optional[list[NodeReference]],
            is_user: bool = False,
        ) -> list[str]:
            """Extract IDs from nodes, applying `to_user_azure_attribute` if `is_user` is True."""
            if is_user:
                return [to_user_azure_attribute(ref.externalId) for ref in nodes or []]
            return [ref.externalId for ref in nodes or []]

        def extract_approval_user_ids() -> list[str]:
            if not self.approvalWorkflow or not self.approvalWorkflow.steps:
                return []
            return [
                to_user_azure_attribute(user.externalId)
                for step in self.approvalWorkflow.steps or []
                for user in step.users or []
            ]

        user_ids = (
            extract_ids(self.viewUsers, is_user=True)
            + extract_approval_user_ids()
            + [
                ref.externalId
                for ref in [self.owner, self.assignedTo]
                if ref and ref.externalId
            ]
        )

        role_ids = extract_ids(self.viewRoles)
        team_ids = extract_ids(self.viewTeams)

        source_event_views = (
            source_event.views
            if source_event and source_event.views is not None
            else []
        )

        combined_views = list(set(user_ids + role_ids + team_ids + source_event_views))

        self.views = combined_views if combined_views else None
