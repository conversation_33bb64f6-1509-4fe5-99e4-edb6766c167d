'use client'
import '../common/utils/polyfills'

import { useContext, useEffect, useState } from 'react'

import { ClnPage, ClnPanel } from '@celanese/ui-lib'
import {
    TranslationContext,
    TranslationContextState,
    UserManagementContext,
    UserManagementContextState,
} from '@celanese/celanese-ui'
import { UserRolesPermission } from '@celanese/celanese-sdk'

import AuthGuardWrapper from '../common/wrapper/AuthGuardWrapper'
import { UserExternalContext, UserExternalContextState } from '../common/contexts/UserExternalContext'
import { translate } from '../common/utils/generate-translate'
import { APP_NAME } from '../common/utils'
import { BackArrow } from '../common/utils/backArrow'

import EventForms from '../components/EventsComponent/EventForm/eventForms'
import EventTable from '../components/EventsComponent/EventTable/eventTable'
import MessageModal from '../components/ModalComponent/Modal/MessageModal'
import PageHeader from '../components/PageHeader'

export default function EventPage() {
    const { userInfo } = useContext<UserManagementContextState>(UserManagementContext)
    const { userExternalInfo } = useContext<UserExternalContextState>(UserExternalContext)
    const { locale } = useContext<TranslationContextState>(TranslationContext)

    const [activeUser, setActiveUser] = useState<UserRolesPermission>()
    const [showEventForm, setShowEventForm] = useState(false)
    const [isCancelModalOpen, setIsCancelModalOpen] = useState(false)

    useEffect(() => {
        sessionStorage.removeItem('event-details-filterInfo-action-tab')
        sessionStorage.removeItem('event-details-filterInfo-recurring-tab')
    }, [])

    useEffect(() => {
        document.title = `${translate('pages.source-event.title')} | ${APP_NAME}`
    }, [locale])

    useEffect(() => {
        if (
            userExternalInfo.externalId &&
            userExternalInfo.externalId !== '' &&
            (activeUser?.externalId !== userExternalInfo.externalId ||
                activeUser?.applications !== userInfo.applications)
        ) {
            setActiveUser({
                ...userInfo,
                externalId: userExternalInfo.externalId,
                roles: userExternalInfo.roles,
                teams: userExternalInfo.teams,
            })
        }
    }, [userInfo, userExternalInfo])

    const handleOpenEventForm = () => setShowEventForm(true)
    const handleClickBack = () => setIsCancelModalOpen(true)
    const handleCloseCancelModal = () => setIsCancelModalOpen(false)
    const handleLeaveCancelModal = () => {
        localStorage.removeItem('isEditForm')
        setShowEventForm(false)
        setIsCancelModalOpen(false)
    }

    return (
        <AuthGuardWrapper componentName={EventPage.name}>
            <ClnPage id="cln-page-tile" sx={{ gap: '0.5px' }}>
                {activeUser && (
                    <>
                        {showEventForm ? (
                            <ClnPanel
                                sx={{
                                    display: 'flex',
                                    flexDirection: 'column',
                                    padding: '20px',
                                    gap: '0.5rem',
                                }}
                            >
                                {BackArrow(translate('source-event.create'), handleClickBack)}
                                <EventForms
                                    onClose={setIsCancelModalOpen}
                                    siteId={userInfo.selectedSites?.length == 1 ? userInfo.selectedSites[0].siteId : ''}
                                />
                            </ClnPanel>
                        ) : (
                            <>
                                <PageHeader title={translate('source-event.title')} activeUser={activeUser} />
                                <ClnPanel
                                    sx={{
                                        padding: '1.5rem',
                                        display: 'flex',
                                        flexDirection: 'column',
                                    }}
                                >
                                    <EventTable
                                        buttomNewEvent
                                        showSourceEventForm={handleOpenEventForm}
                                        activeUser={activeUser}
                                    />
                                </ClnPanel>
                            </>
                        )}

                        <MessageModal
                            name=""
                            text={translate('requestModal.closeQuestion')}
                            open={isCancelModalOpen}
                            isCancelModal
                            handleClose={handleCloseCancelModal}
                            handleLeave={handleLeaveCancelModal}
                        />
                    </>
                )}
            </ClnPage>
        </AuthGuardWrapper>
    )
}
