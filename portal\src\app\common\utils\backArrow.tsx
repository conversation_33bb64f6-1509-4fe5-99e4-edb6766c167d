import GenericFieldTitle from '@/app/components/FieldsComponent/GenericFieldTitle'
import { MatIcon } from '@celanese/ui-lib'
import { Box } from '@mui/material'

export const BackArrow = (title: string, handleClickBack: () => void) => {
    return (
        <Box
            sx={{ display: 'flex', flexDirection: 'row', alignItems: 'center', cursor: 'pointer', gap: '6px' }}
            onClick={handleClickBack}
            data-test='new_action_item_menu-back_button'
            data-origin='aim'
        >
            <Box sx={{ width: '1.5rem', height: '1.5rem' }}>
                <MatIcon icon='arrow_back' color='primary.main'/>
            </Box>
            <GenericFieldTitle fieldName={title} isSubHeader />
        </Box>
    )
}
