from pydantic import BaseModel, ConfigDict
from pydantic.alias_generators import to_camel


class BaseIntegrationRequest(BaseModel):
    model_config = ConfigDict(
        alias_generator=to_camel,
        populate_by_name=True,
        from_attributes=True,
    )

    site_code: str | None = None

    application_code: str| None = "APP-AIM"

class UserByNodeAccessRequest(BaseIntegrationRequest):
    feature_code: list[str]
    type: str| None = "EditAccess"