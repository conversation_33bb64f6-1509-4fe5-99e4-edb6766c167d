from typing import Optional
import os
import sys
from uuid import uuid4


script_dir = os.path.dirname(os.path.abspath(__file__))
project_dir = os.path.dirname(script_dir)
sys.path.append(project_dir)
from infra.cognite_client_factory import CogniteClientFactory
from infra.graphql_client_factory import GraphqlClientFactory
from models.settings import Settings
from services.action_item_by_id_retriever import ActionItemByIdRetriever
from services.logging_service import LoggingService
from services.cognite_service import CogniteService
from services.graphql_service import GraphqlService


class ActionItemByIdRetrieverFactory:
    @staticmethod
    def retriever(
        settings: Settings = Settings.from_env(),
        call_id: str = str(uuid4()),
        override_token: Optional[str] = None,
    ) -> ActionItemByIdRetriever:
        log = LoggingService(call_id)
        cognite_service_function = CogniteClientFactory.create(settings)
        graphql_service_function = GraphqlClientFactory.create(
            cognite_service_function, settings
        )

        cognite_client = CogniteClientFactory.create(settings, override_token)
        graphl_client = GraphqlClientFactory.create(cognite_client, settings)

        return ActionItemByIdRetriever(
            CogniteService(cognite_client, settings, log),
            CogniteService(cognite_service_function, settings, log),
            GraphqlService(graphl_client, log),
            GraphqlService(graphql_service_function, log),
            settings,
            log,
        )
