import { Box, Pagination, PaginationItem, useMediaQuery, useTheme } from '@mui/material'
import { useState } from 'react'
import * as S from './styles'
import { translate } from '@/app/common/utils/generate-translate'

type TablePaginationProps = {
    rowsLen: number
    rowsPerPageOptions: number[]
    currentPage: number
    initialRowsPerPage?: number
    totalPages: number
    setCurrentPage?: (page: number) => void
    setInitialRowsPerPage?: (value: number) => void
}

export function TablePagination({
    rowsLen,
    rowsPerPageOptions,
    currentPage,
    initialRowsPerPage,
    totalPages,
    setCurrentPage,
    setInitialRowsPerPage,
}: TablePaginationProps) {
    const theme = useTheme()
    const isNotMobile = useMediaQuery(theme.breakpoints.up('sm'))

    const [rowsPerPage, setRowsPerPage] = useState(initialRowsPerPage ?? rowsPerPageOptions[0])

    const handleRowsPerPageChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        const newValue = parseInt(event.target.value, 10)
        setRowsPerPage(newValue)
        setInitialRowsPerPage?.(newValue)
        setCurrentPage?.(0)
    }

    return (
        <S.Container>
            <Box
                sx={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    padding: '0.5rem 0',
                    position: 'relative',
                }}
            >
                <Box
                    sx={{
                        display: 'flex',
                        justifyContent: 'center',
                        overflowX: 'auto',
                        flexWrap: 'wrap',
                        width: '100%',
                    }}
                >
                    <Pagination
                        count={totalPages}
                        color="primary"
                        page={currentPage + 1}
                        onChange={(_event: React.ChangeEvent<unknown>, newPage: number) =>
                            setCurrentPage?.(newPage - 1)
                        }
                        renderItem={(item) => {
                            const isNearCurrent =
                                item.page &&
                                (item.page <= 2 ||
                                    Math.abs(item.page - (currentPage + 1)) <= 1 ||
                                    currentPage + 1 >= totalPages)

                            if (isNearCurrent || item.type === 'start-ellipsis' || item.type === 'end-ellipsis') {
                                return <PaginationItem {...item} />
                            }

                            return null
                        }}
                    />
                </Box>

                {isNotMobile && (
                    <Box
                        sx={{
                            position: 'absolute',
                            right: 0,
                            top: '50%',
                            transform: 'translateY(-50%)',
                        }}
                    >
                        <S.TablePagination
                            page={currentPage}
                            rowsPerPageOptions={rowsPerPageOptions}
                            count={rowsLen}
                            rowsPerPage={rowsPerPage}
                            onPageChange={() => {}}
                            slotProps={{
                                actions: {
                                    nextButton: { style: { display: 'none' } },
                                    previousButton: { style: { display: 'none' } },
                                },
                            }}
                            onRowsPerPageChange={handleRowsPerPageChange}
                            labelRowsPerPage={`${translate('common.rowsPerPage')}:`}
                            data-test="home-rows_per_page_button"
                            data-origin="aim"
                        />
                    </Box>
                )}
            </Box>
        </S.Container>
    )
}
