import { MonthEnum } from '../enums/MonthEnum'

export const transformDataToFormInput = (input: any) => {
    if (Array.isArray(input) && input.length === 0) return undefined
    if (typeof input === 'string' && input.trim() === '') return undefined
    return input
}

export const monthStringToNumber = (month?: string): number | undefined => {
    const monthKeys = Object.keys(MonthEnum)
    const monthIndex = monthKeys.findIndex((key) => MonthEnum[key as keyof typeof MonthEnum] === month)
    return monthIndex !== -1 ? monthIndex + 1 : undefined
}

export const removeUndefinedValues = (obj: Record<string, any>) => {
    return Object.fromEntries(Object.entries(obj).filter(([_, value]) => value !== undefined))
}

export const getUpdatedValue = <T extends string | number | boolean | (string | number)[]>(
    currentValue: T | undefined,
    newValue: T | undefined | null,
    emptyAsNull = false,
    prefix?: string
): T | undefined | null | string => {
    if ((currentValue === undefined || currentValue === null) && newValue === '') return undefined
    if (Array.isArray(currentValue) && Array.isArray(newValue) && currentValue.join(',') === newValue.join(',')) {
        return undefined
    }
    if (currentValue === newValue) return undefined
    if (emptyAsNull && newValue === '') return null
    return prefix && newValue !== undefined && newValue !== null ? `${prefix}${newValue}` : newValue ?? null
}

export const getConditionalValue = (
    isRequired: boolean,
    currentValue: string | undefined,
    newValue: string | undefined | null,
    emptyAsNull?: boolean,
    prefix?: string
): string | undefined | null => {
    if (!isRequired) return currentValue ? null : undefined
    return getUpdatedValue(currentValue, newValue, emptyAsNull, prefix)
}
