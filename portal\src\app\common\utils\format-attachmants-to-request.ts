export function formatAttachmentExternalIdToRequest(
    newUploadedFilesIds: string[],
    oldUploadedFilesIds: string[],
    actionItemAttachments?: string[]
): string[] | undefined | null {
    if (newUploadedFilesIds.length === 0 && oldUploadedFilesIds.length === actionItemAttachments?.length) {
        return undefined
    }

    if (newUploadedFilesIds.length === 0 && oldUploadedFilesIds.length === 0) {
        return null
    }

    return [...newUploadedFilesIds, ...oldUploadedFilesIds]
}
