import { useEffect, useState } from 'react'
import { UserComplement } from '../../models/common/user-management/user-complement'
import { useGraphqlQuery } from '../cognite/useGraphqlQuery'
import { gql } from '@apollo/client'

export interface UserComplementQueryRequest {
    userIds?: string[]
}

export interface UserComplementByUnitsQueryRequest {
    unitIds: string[]
}

export interface UserComplementByLocationsQueryRequest {
    locationIds: string[]
}

const buildUserComplementQuery = (request: UserComplementQueryRequest): string => {
    const filters: string[] = []

    filters.push(
        `{
            userAzureAttribute: {
                user: {
                   and: [
                        { space: { eq: "UMG-COR-ALL-DAT" } },
                        { active: { eq: true } },
                    ]
                }
            }
        }`,
        `{ employeeStatus: { or: [ { not: { externalId: { eq: "EMST_INACTIVE" } } }, { externalId: { isNull: true } } ] } }`
    )

    if (request.userIds && request.userIds.length > 0) {
        filters.push(
            `{ userAzureAttribute: { user: { externalId: { in: [${request.userIds
                .map((e) => `"${e}"`)
                .join(', ')}] } } } }`
        )
    }

    const queryFilter = `{ and: [ ${filters.join(', ')} ] }`

    return `
        query GetUserComplement {
            listUserComplement(
                filter: ${queryFilter}
                , first: 1000
            ) {
                items {
                    externalId
                    space
                    userAzureAttribute {
                        externalId
                        space
                        user {
                            active
                            email
                            externalId
                            firstName
                            lastName
                            space
                        }
                    }
                    employeeStatus {
                        externalId
                    }
                }
            }
        }
    `
}

export const useUserComplement = (request: UserComplementQueryRequest) => {
    const query = buildUserComplementQuery(request)
    const { data: fdmData } = useGraphqlQuery<UserComplement>(gql(query), 'listUserComplement', {})

    const [resultData, setResultData] = useState<{ data: UserComplement[]; loading: boolean }>({
        data: [],
        loading: true,
    })

    useEffect(() => {
        if (fdmData.length == 0 || (request?.userIds && request.userIds.length === 0)) {
            setResultData({ data: [], loading: false })
        } else {
            const userComplements = fdmData?.map((item) => {
                const { userAzureAttribute } = item || {}
                const { user } = userAzureAttribute || {}
                const userComplement = {
                    externalId: userAzureAttribute?.externalId ?? '',
                    space: userAzureAttribute?.space ?? '',
                    email: user?.email ?? '',
                    firstName: user?.firstName ?? '',
                    lastName: user?.lastName ?? '',
                    name: `${user?.lastName ?? ''}, ${user?.firstName}`,
                    label: `${user?.lastName ?? ''}, ${user?.firstName} (${user?.email ?? ''})`,
                    userAzureAttribute: {
                        externalId: userAzureAttribute?.externalId ?? '',
                        space: userAzureAttribute?.space ?? '',
                        user: {
                            externalId: user?.externalId ?? '',
                            space: user?.space ?? '',
                            email: user?.email ?? '',
                            firstName: user?.firstName ?? '',
                            lastName: user?.lastName ?? '',
                        },
                    },
                }

                return userComplement
            })

            setResultData({ data: userComplements, loading: false })
        }
    }, [fdmData])

    return {
        loading: resultData.loading,
        users: resultData.data,
    }
}
