from pydantic import BaseModel, Field


class GetSortMapperRequest(BaseModel):
    user_azure_attribute_external_ids: list[str] = Field(default_factory=list)
    user_external_ids: list[str] = Field(default_factory=list)
    application_external_ids: list[str] = Field(default_factory=list)
    reporting_unit_external_ids: list[str] = Field(default_factory=list)
    reporting_location_external_ids: list[str] = Field(default_factory=list)
    category_external_ids: list[str] = Field(default_factory=list)
    sub_category_external_ids: list[str] = Field(default_factory=list)
    site_specific_category_instances: list[tuple[str, str]] = Field(
        default_factory=list
    )
