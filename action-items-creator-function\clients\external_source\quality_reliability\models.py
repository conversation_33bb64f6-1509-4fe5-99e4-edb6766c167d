from datetime import date

from pydantic import Field

from clients.core.models import Node

from .responses import QualityReliabilityEventResponse


class DescriptiveNode(Node):
    """Base class for entities that only have a externalId, space and description."""

    description: str | None = Field(default=None)


class Initiator(Node):
    """Represents the person or entity who initiated the event report."""

    display_name: str | None = Field(default=None)


class QualityDetail(Node):
    """Contains additional details for quality-related events."""

    immediate_action_taken: str | None = Field(default=None)
    batch_number: str | None = Field(default=None)
    equipment: DescriptiveNode | None = Field(default=None)
    reporting_location: DescriptiveNode | None = Field(default=None)
    procedures_involved: str | None = Field(default=None)


class ReliabilityDetail(Node):
    """Contains additional details for reliability-related events."""

    immediate_action_taken: str | None = Field(default=None)
    equipment: DescriptiveNode | None = Field(default=None)
    reporting_location: DescriptiveNode | None = Field(default=None)


class CustomerComplaintDetail(Node):
    """Contains additional details for events categorized as customer complaints."""

    problem_description: str | None = Field(default=None)
    batch_number: str | None = Field(default=None)


class QualityReliabilityEvent(Node):
    """Represents a quality or reliability event with detailed context and metadata."""

    reporting_unit: DescriptiveNode | None = Field(default=None)
    event_date: date | None = Field(default=None)
    report_title: str | None = Field(default=None)
    report_date: date | None = Field(default=None)
    problem_category: DescriptiveNode | None = Field(default=None)
    initiator: Initiator | None = Field(default=None)
    quality_detail: QualityDetail | None = Field(default=None)
    reliability_detail: ReliabilityDetail | None = Field(default=None)
    customer_complaint_detail: CustomerComplaintDetail | None = Field(default=None)

    def to_response(self) -> QualityReliabilityEventResponse:
        """Cast the entity to its response type."""
        reporting_unit = (
            self.reporting_unit.description if self.reporting_unit else None
        )
        problem_category = (
            self.problem_category.description if self.problem_category else None
        )
        initiator = self.initiator.display_name if self.initiator else None

        procedures_involved = (
            self.quality_detail.procedures_involved if self.quality_detail else None
        )

        immediate_action_taken = (
            self.quality_detail.immediate_action_taken
            if self.quality_detail and self.quality_detail.immediate_action_taken
            else (
                self.reliability_detail.immediate_action_taken
                if self.reliability_detail
                else None
            )
        )

        batch_number = (
            self.quality_detail.batch_number
            if self.quality_detail and self.quality_detail.batch_number
            else (
                self.customer_complaint_detail.batch_number
                if self.customer_complaint_detail
                else None
            )
        )

        equipment = (
            self.quality_detail.equipment.description
            if self.quality_detail and self.quality_detail.equipment
            else (
                self.reliability_detail.equipment.description
                if self.reliability_detail and self.reliability_detail.equipment
                else None
            )
        )

        problem_description = (
            self.customer_complaint_detail.problem_description
            if self.customer_complaint_detail
            else None
        )

        return QualityReliabilityEventResponse(
            external_id=self.external_id,
            space=self.space,
            event_date=self.event_date,
            reporting_unit=reporting_unit,
            report_title=self.report_title,
            report_date=self.report_date,
            problem_category=problem_category,
            initiator=initiator,
            procedures_involved=procedures_involved,
            immediate_action_taken=immediate_action_taken,
            batch_number=batch_number,
            equipment=equipment,
            event_id=self.external_id,
            problem_description=problem_description,
        )
