import { AutocompleteOption } from '@/app/components/FieldsComponent/GenericAutocomplete'
import { Dayjs } from 'dayjs'

export interface FilterSelectedRecurring {
    unit: string[]
    owner: string[]
    updateBy: string[]
    category: string[]
    startDate: Dayjs | null
    frequency: string[]
    status: string[]
    noEndDate: string | null
}

export interface Unit {
    externalId: string
    description: string
}

export interface FilterOptionsRecurring {
    unit: AutocompleteOption[]
    owner: AutocompleteOption[]
    updateBy: AutocompleteOption[]
    category: AutocompleteOption[]
    startDate: Dayjs | null
    frequency: AutocompleteOption[]
    status: AutocompleteOption[]
    noEndDate: AutocompleteOption[]
}
