import { ActionItemKindExternalIdEnum } from '@/app/common/enums/ActionItemKindEnum'
import { SITE_EXTERNAL_ID_REQUIRED_FIELD } from '@/app/common/utils/validate-codes'
import dayjs, { Dayjs } from 'dayjs'
import { z } from 'zod'

const zodDate = z.instanceof(dayjs as unknown as typeof Dayjs).nullable()

export const formSchema = z.object({
    title: z.string().min(10).max(200),
    description: z.string().min(10).max(1000),
    owner: z.string().min(1),
    reportingSite: z.string().min(1),
    reportingUnit: z.string().min(1),
    reportingLocation: z.string().optional(),
    reportingLine: z.string().optional(),

    sourceInformation: z.string().optional(),
    category: z.string().min(1),
    subCategory1: z.string().min(1),
    subCategory2: z.string().optional(),

    taskType: z.string().min(1),
    recurringSaved: z.boolean().optional(),
    assignmentDate: zodDate.optional(),
    dueDate: zodDate.optional(),

    priority: z.string().optional(),
    assignedTo: z.string().optional(),
    assignees: z.array(z.string()).optional(),
    oldUploadedFiles: z.array(z.any()).optional(),
    newUploadedFiles: z.array(z.any()).optional(),
    approvalRequired: z.boolean(),
    verificationRequired: z.boolean(),
    approver: z.string().optional(),
    verification: z.string().optional(),
    verificationOfEffectiveness: z.string().optional(),
    evidenceRequired: z.boolean(),
    isPrivate: z.boolean(),
})

export const createFormSchema = (activeStep: number, siteId?: string, edit?: boolean, assignedToValidate?: boolean) => {
    let newSchema: any = formSchema

    if (activeStep === 0) {
        newSchema = newSchema.pick({
            title: true,
            description: true,
            owner: true,
            reportingUnit: true,
            reportingLocation: true,
        })
        if (siteId === SITE_EXTERNAL_ID_REQUIRED_FIELD) {
            newSchema = newSchema.refine((data: any) => !!data.reportingLocation, {
                path: ['reportingLocation'],
            })
        }
    } else if (activeStep === 1) {
        newSchema = newSchema.pick({
            sourceInformation: true,
            category: true,
            subCategory1: true,
            subCategory2: true,
        })
        if (siteId === SITE_EXTERNAL_ID_REQUIRED_FIELD) {
            newSchema = newSchema.refine((data: any) => !!data.sourceInformation, {
                path: ['sourceInformation'],
            })
            newSchema = newSchema.refine((data: any) => !!data.subCategory2, {
                path: ['subCategory2'],
            })
        }
    } else if (activeStep === 2) {
        newSchema = newSchema.pick({
            priority: true,
            assignedTo: true,
            assignees: true,
            taskType: true,
            recurringSaved: true,
            assignmentDate: true,
            dueDate: true,
            approvalRequired: true,
            verificationRequired: true,
            approver: true,
            verification: true,
        })

        if (!edit) {
            newSchema = newSchema.refine((data: any) => Array.isArray(data.assignees) && data.assignees.length > 0, {
                path: ['assignees'],
            })

            newSchema = newSchema.refine(
                (data: any) => (data.taskType === ActionItemKindExternalIdEnum.OneTime ? !!data.assignmentDate : true),
                { path: ['assignmentDate'] }
            )

            newSchema = newSchema.refine(
                (data: any) => (data.taskType === ActionItemKindExternalIdEnum.OneTime ? !!data.dueDate : true),
                {
                    path: ['dueDate'],
                }
            )

            newSchema = newSchema.refine(
                (data: any) =>
                    data.taskType === ActionItemKindExternalIdEnum.OneTime && data.assignmentDate
                        ? dayjs(data.assignmentDate).startOf('day').isSameOrAfter(dayjs().startOf('day'))
                        : true,
                {
                    path: ['assignmentDate'],
                }
            )

            newSchema = newSchema.refine(
                (data: any) =>
                    data.taskType === ActionItemKindExternalIdEnum.OneTime && data.dueDate && data.assignmentDate
                        ? dayjs(data.dueDate).isSameOrAfter(dayjs(data.assignmentDate))
                        : true,
                { path: ['dueDate'] }
            )

            newSchema = newSchema.refine(
                (data: any) =>
                    data.taskType === ActionItemKindExternalIdEnum.Recurring ? data.recurringSaved === true : true,
                { path: ['taskType', 'recurringSaved'] }
            )
        } else {
            if (assignedToValidate)
                newSchema = newSchema.refine(
                    (data: any) => {
                        return data.assignedTo && data.assignedTo !== ''
                    },
                    { path: ['assignedTo'] }
                )
        }

        newSchema = newSchema.refine((data: any) => (data.approvalRequired ? !!data.approver : true), {
            path: ['approver'],
        })

        newSchema = newSchema.refine((data: any) => (data.verificationRequired ? !!data.verification : true), {
            path: ['verification'],
        })
    }

    return newSchema
}
