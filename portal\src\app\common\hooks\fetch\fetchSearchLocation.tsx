import { useCallback, useEffect, useState } from 'react'
import { AzureFunctionClient } from '../../clients/azure-function-client'
import { useDebounceFunction } from '../general-functions/useDebounce'
import { ReportingLocation, ReportingLocationRequest } from '../../models/common/asset-hierarchy/reporting-location'

export const useFetchSearchLocaton = (request: ReportingLocationRequest) => {
    const client = new AzureFunctionClient()
    const [resultData, setResultData] = useState<{
        data: ReportingLocation[]
        loading: boolean
    }>({
        data: [],
        loading: true,
    })

    const fetchLocatons = useCallback(async () => {
        if (request.search === 'NULL_PARAM') {
            setResultData({ data: [], loading: false })
            return
        }
        setResultData((prev) => ({ ...prev, loading: true }))
        try {
            const result: ReportingLocation[] = await client.getReportingLocations(request)
            setResultData({ data: result, loading: false })
        } catch (err) {
            setResultData({ data: [], loading: false })
        }
    }, [request])

    const debouncedFetchLocatons = useCallback(useDebounceFunction(fetchLocatons, 800), [fetchLocatons])

    useEffect(() => {
        debouncedFetchLocatons()
    }, [debouncedFetchLocatons])

    return {
        loading: resultData.loading,
        locations: resultData.data,
    }
}
