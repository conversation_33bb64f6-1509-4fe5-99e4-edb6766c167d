'use client'
import '../common/utils/polyfills'
import { Badge, Box, useMediaQuery, useTheme } from '@mui/material'
import { useContext, useEffect, useMemo, useState } from 'react'
import {
    getLocalUserSite,
    TranslationContext,
    TranslationContextState,
    UserManagementContext,
    UserManagementContextState,
} from '@celanese/celanese-ui'
import { useReportingUnits } from '../common/hooks/asset-hierarchy/useReportingUnits'
import { useActionItemCategories } from '../common/hooks/action-item-management/useActionItemCategories'
import { useActionItemSubCategories } from '../common/hooks/action-item-management/useActionItemSubCategories'
import { useActionItemStatuses } from '../common/hooks/action-item-management/useActionItemStatuses'
import { ReportingUnitHomeTable, Status } from '../common/models/action'
import { ReportingSite } from '../common/models/common/asset-hierarchy/reporting-site'
import AuthGuardWrapper from '../common/wrapper/AuthGuardWrapper'
import { useAuthGuard } from '../common/hooks/useAuthGuard'
import { APP_NAME } from '../common/utils'
import { useReportingLocations } from '../common/hooks/asset-hierarchy/useReportingLocations'
import { AimTabs } from '../components/Tabs'
import { LoaderCircularIcon } from '../components/Loader'
import { useUsersByIds } from '../common/hooks/user-management/useUsers'
import { useUserSiteConfigurations } from '../common/hooks/user-management/useUserSiteConfigurations'
import SiteTab from './SiteTab'
import SupervisorTab from './SupervisorTab'
import { ReportingUnit } from '../common/models/common/asset-hierarchy/reporting-unit'
import { ClnButton, ClnButtonProps, ClnPage, ClnPanel, MatIcon } from '@celanese/ui-lib'
import { translate } from '../common/utils/generate-translate'
import { DashboardFilterOptions } from '../common/models/dashboard/filter-dashboard'
import { FilterInfoProps } from '../components/ActionTable/HomeFilter'
import { DashboardFilter } from '../components/Dashboard/Filter/DashboardFilter'
import { PopoverFilterBase } from '../components/PopoverFilterBase'
import { determineActiveFilterCount } from '../common/utils/active-filter-count'
import { ActionStatusExternalIdHomeDefaultEnum } from '../common/enums/ActionItemStatusEnum'
import { UserRolesPermission } from '@celanese/celanese-sdk'
import { UserExternalContext, UserExternalContextState } from '../common/contexts/UserExternalContext'
import { useSiteSpecificCategories } from '../common/hooks/action-item-management/useSiteSpecificCategories'
import PageHeader from '../components/PageHeader'

const filterReportingUnitsBySiteCode = (units: ReportingUnit[], siteCode: string): ReportingUnitHomeTable[] => {
    const filter = (item: ReportingSite[]) => {
        return Array.isArray(item) && item.some((site) => site.siteCode === siteCode)
    }

    return units.filter((unit) => unit.reportingSites && filter(unit.reportingSites))
}

const filterStatusList = (statuses: Status[]): Status[] => {
    const excludedStatuses = ['Inactive', 'Active', 'Deleted']
    return statuses.filter((status) => !excludedStatuses.includes(status.name ?? ''))
}

export default function DashboardPage() {
    const { userInfo } = useContext<UserManagementContextState>(UserManagementContext)
    const { userExternalInfo } = useContext<UserExternalContextState>(UserExternalContext)
    const [activeUser, setActiveUser] = useState<UserRolesPermission>()
    const { locale } = useContext<TranslationContextState>(TranslationContext)

    const { siteId, siteCode } = getLocalUserSite() || {}

    const { checkPermissionsFromRoutes } = useAuthGuard()
    const [currentTab, setCurrentTab] = useState(localStorage.getItem('dashboardTab') ?? '0')

    const storedFilterInfo = sessionStorage.getItem(
        `dashboard-filterInfo-${currentTab === '0' ? 'site' : 'supervisor'}-tab`
    )
    const parsedFilterInfo = storedFilterInfo ? JSON.parse(storedFilterInfo) : {}

    const [isLoadingEmails, setIsLoadingEmails] = useState(false)
    const [employeesListById, setEmployeesListById] = useState<string[]>([])

    const { units } = useReportingUnits({ siteId: siteId! })
    const { locations } = useReportingLocations({ siteId: siteId! })
    const { categories } = useActionItemCategories()
    const { subCategories } = useActionItemSubCategories()
    const { siteSpecificCategories } = useSiteSpecificCategories({ siteIds: [siteId!] })
    const { allStatus } = useActionItemStatuses({})
    const { supervisorManager } = useUserSiteConfigurations({})

    const { getEmailsByIds } = useUsersByIds()

    const supervisorManagerList = useMemo(() => supervisorManager.filter((x) => x.externalId), [supervisorManager])
    const reportingUnits = useMemo(() => filterReportingUnitsBySiteCode(units, siteCode ?? ''), [units, siteCode])
    const statusList = useMemo(() => filterStatusList(allStatus), [allStatus])

    const theme = useTheme()
    const isMobile = useMediaQuery(theme.breakpoints.down('md'))

    const [currentPage, setCurrentPage] = useState(0)
    const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null)

    const filterOptions: DashboardFilterOptions = useMemo(
        () => ({
            unit: reportingUnits ?? [],
            location: locations ?? [],
            category: categories ?? [],
            subcategories1: subCategories ?? [],
            subcategories2: siteSpecificCategories ?? [],
            status: statusList ?? [],
            dueDate: [null, null],
            assignee: [],
            managerName: supervisorManagerList ?? [],
            updateStatus: [],
        }),
        [
            reportingUnits,
            locations,
            categories,
            subCategories,
            siteSpecificCategories,
            statusList,
            supervisorManagerList,
        ]
    )

    const [filterInfo, setFilterInfo] = useState<FilterInfoProps>({
        reportingSiteExternalId: siteId ?? '-',
        statusExternalIds: Object.values(ActionStatusExternalIdHomeDefaultEnum),
        onSiteManagerToExternalId:
            currentTab === '1' && supervisorManagerList.some((manager) => manager.email === activeUser?.email)
                ? [`${activeUser?.externalId}`]
                : [],
        pageSize: 10,
        activeUserEmail: activeUser?.email ?? '-',
        ...parsedFilterInfo?.filter,
        search: '',
    })

    const handleEmailClick = async () => {
        setIsLoadingEmails(true)
        try {
            const ids = employeesListById.length ? employeesListById : ['-']
            const res = await getEmailsByIds({ externalIds: ids })
            const emails = res.usersEmail ?? []
            const subject = `${translate('dashboards.emailTeam.subject')}`
            const body = `${translate('dashboards.emailTeam.body')} \n${window.location.origin}\n\n`
            const emailList = emails.join(';')
            const mailtoLink = `mailto:?bcc=${encodeURIComponent(emailList ?? '')}&subject=${encodeURIComponent(
                subject
            )}&body=${encodeURIComponent(body)}`
            window.location.href = mailtoLink
        } finally {
            setIsLoadingEmails(false)
        }
    }

    const tabButtons: ClnButtonProps[] = [
        {
            startIcon: isLoadingEmails ? LoaderCircularIcon(10) : <MatIcon icon="mail" />,
            label: translate('dashboards.emailTeam.emailTeam'),
            variant: 'contained',
            onClick: handleEmailClick,
            sxProps: {
                boxShadow: 'none !important',
                '&:hover': {
                    boxShadow: 'none',
                },
            },
        },
    ]

    const tabs = useMemo(
        () =>
            [
                {
                    route: '/dashboard-tab-site',
                    label: translate('dashboards.tabs.site'),
                    content: (
                        <SiteTab
                            siteId={siteId ?? '-'}
                            categories={categories}
                            allStatus={statusList}
                            activeUser={activeUser}
                            isSiteTab={currentTab === '0'}
                            filterInfo={filterInfo}
                            currentPage={currentPage}
                            setCurrentPage={setCurrentPage}
                        />
                    ),
                },
                {
                    route: '/dashboard-tab-supervisor',
                    label: translate('dashboards.tabs.supervisor'),
                    content: (
                        <SupervisorTab
                            siteId={siteId ?? '-'}
                            categories={categories}
                            allStatus={statusList}
                            activeUser={activeUser}
                            setEmployeesListById={setEmployeesListById}
                            isSupervisorTab={currentTab === '1'}
                            filterInfo={filterInfo}
                            currentPage={currentPage}
                            setCurrentPage={setCurrentPage}
                        />
                    ),
                },
            ].filter((item) => checkPermissionsFromRoutes(item.route)),
        [locale, categories, statusList, activeUser, currentTab, filterInfo, currentPage, checkPermissionsFromRoutes]
    )

    useEffect(() => {
        if (
            userExternalInfo.externalId &&
            userExternalInfo.externalId !== '' &&
            activeUser?.externalId !== userExternalInfo.externalId
        ) {
            setActiveUser({
                ...userInfo,
                externalId: userExternalInfo.externalId,
                roles: userExternalInfo.roles,
                teams: userExternalInfo.teams,
            })
        }
    }, [userInfo, userExternalInfo])

    useEffect(() => {
        document.title = `${translate('pages.dashboards.title')} | ${APP_NAME}`
    }, [locale])

    const activeFiltersCount = useMemo(() => {
        const filters = {
            statusExternalIds: filterInfo.statusExternalIds,
            dueDateGte: filterInfo.dueDateGte,
            dueDateLt: filterInfo.dueDateLt,
            updateStatusDate: filterInfo.updateStatusDate,
            onlyPrivate: filterInfo.onlyPrivate,
            reportingUnitExternalIds: filterInfo.reportingUnitExternalIds,
            reportingLocationExternalIds: filterInfo.reportingLocationExternalIds,
            ownerExternalId: filterInfo.ownerExternalId,
            assignedToExternalId: filterInfo.assignedToExternalId,
            onSiteManagerToExternalId: filterInfo.onSiteManagerToExternalId,
            categoryExternalId: filterInfo.categoryExternalId,
            subcategoryExternalId: filterInfo.subcategoryExternalId,
            siteSpecificCategoryExternalId: filterInfo.siteSpecificCategoryExternalId,
            applicationExternalId: filterInfo.applicationExternalId,
            externalIdPrefix: filterInfo.externalIdPrefix,
            titlePrefix: filterInfo.titlePrefix,
            sourceInfoPrefix: filterInfo.sourceInfoPrefix,
            icapActionIdPrefix: filterInfo.icapActionIdPrefix,
            sourceEventTitlePrefix: filterInfo.sourceEventTitlePrefix,
            sourceEventTitleEq: filterInfo.sourceEventTitleEq,
        }

        const count = determineActiveFilterCount(filters)
        return count
    }, [filterInfo])

    const handleTabChange = (_e: any, value: number) => {
        const storedFilterInfo = sessionStorage.getItem(
            `dashboard-filterInfo-${value === 0 ? 'site' : 'supervisor'}-tab`
        )
        const parsedFilterInfo = storedFilterInfo ? JSON.parse(storedFilterInfo) : {}

        setFilterInfo({
            reportingSiteExternalId: siteId ?? '-',
            statusExternalIds: Object.values(ActionStatusExternalIdHomeDefaultEnum),
            onSiteManagerToExternalId:
                value === 1 && supervisorManagerList.some((manager) => manager.email === activeUser?.email)
                    ? [`${activeUser?.externalId}`]
                    : [],
            pageSize: 10,
            activeUserEmail: activeUser?.email ?? '-',
            ...parsedFilterInfo?.filter,
            search: '',
        })

        setCurrentTab(value.toString())
        setCurrentPage(0)
        localStorage.setItem('dashboardTab', value.toString())
    }

    const handleFilterSubmit = (filter: any) => {
        setAnchorEl(null)
        setFilterInfo(filter)
    }

    const filterComponent = (
        <Box
            sx={{
                width: 'auto',
                p: '10px 0px',
                mr: 2,
            }}
        >
            <Badge color="primary" badgeContent={activeFiltersCount > 99 ? '99+' : activeFiltersCount || undefined}>
                <ClnButton
                    variant={isMobile ? 'text' : 'outlined'}
                    label={translate('common.filters')}
                    startIcon={isMobile ? undefined : <MatIcon icon="filter_list" />}
                    endIcon={isMobile ? <MatIcon icon="filter_list" /> : undefined}
                    onClick={(event: React.MouseEvent<HTMLButtonElement>) => setAnchorEl(event.currentTarget)}
                    sx={{ width: isMobile ? '100%' : 'auto' }}
                    size="small"
                    data-test="home-filter_button"
                    data-origin="ui-lib"
                />
            </Badge>
        </Box>
    )

    return (
        <AuthGuardWrapper componentName={DashboardPage.name}>
            <ClnPage>
                {activeUser && (
                    <Box sx={{ display: 'flex', flexDirection: 'column' }}>
                        <PageHeader title={translate('dashboards.dashboard')} />
                        <ClnPanel
                            sx={{
                                padding: '1.5rem',
                                display: 'flex',
                                flexDirection: 'column',
                            }}
                            id="dashboard-panel"
                        >
                            <AimTabs
                                value={parseInt(currentTab)}
                                tabs={tabs}
                                onChange={handleTabChange}
                                buttons={currentTab === '1' ? tabButtons : []}
                                filterComponent={filterComponent}
                            />

                            <PopoverFilterBase
                                anchorEl={anchorEl}
                                onClose={() => setAnchorEl(null)}
                                sxProps={{ paddingBottom: '10px', height: '90vh' }}
                            >
                                <DashboardFilter
                                    activeUser={activeUser}
                                    filterInfo={filterInfo}
                                    data={filterOptions}
                                    onSubmit={handleFilterSubmit}
                                    setPage={setCurrentPage}
                                    isSiteTab={currentTab === '0'}
                                    employeesName={parsedFilterInfo?.employeesName}
                                    supervisorManagers={supervisorManagerList}
                                    updateStatus={parsedFilterInfo?.updateStatus}
                                />
                            </PopoverFilterBase>
                        </ClnPanel>
                    </Box>
                )}
            </ClnPage>
        </AuthGuardWrapper>
    )
}
