# Manifest file downloaded from fusion
externalId: tr-AIM-COR-ALL-ICAP-ACT-FILE
name: AIM-COR-ALL-ICAP-ACT-FILE
query: >-
  SELECT
    aim_action.externalId,
    aim_action.space,
    array_distinct(array_agg(cdf_aim_file.externalId)) AS attachments
  FROM `ICAP-COR`.`EVNT-tblEventEvidence` icap_event_evidence

  INNER JOIN cdf_data_models('AIM-COR-ALL-DMD', 'ActionItemManagementDOM', '6_0_0', 'Action') aim_action
    	ON split_part(aim_action.objectType, '-', 2) = cast(icap_event_evidence.ActionItemID AS STRING)
  INNER JOIN _cdf.files cdf_icap_file
  	ON icap_event_evidence.fileName = cdf_icap_file.name
    	AND icap_event_evidence.EventID = split_part(cdf_icap_file.directory, '/', -1)
  INNER JOIN _cdf.files cdf_aim_file
    	ON cdf_aim_file.externalId = concat('AIM-', cdf_icap_file.externalId)
    	AND cdf_aim_file.dataSetId = dataset_id(aim_action.space)
  WHERE icap_event_evidence.active
  	AND icap_event_evidence.ActionItemID IS NOT NULL
      AND cdf_icap_file.dataSetId = dataset_id('ICAP-COR-ALL-DAT')
  	AND startswith(cdf_aim_file.externalId, 'AIM-ICAP-COR')
    	-- AND ifnull(aim_action.isPrivate, false)
    	AND aim_action.viewOnly
  GROUP BY aim_action.externalId, aim_action.space
destination:
  dataModel:
    space: AIM-COR-ALL-DMD
    externalId: ActionItemManagementDOM
    version: "6_0_0"
    destinationType: Action
  type: instances
ignoreNullFields: true
shared: true
action: upsert
dataSetExternalId: AIM-COR-ALL-DAT
# Specify credentials separately like this:
# authentication:
#   read:
#     clientId: ${READ_CLIENT_ID}
#     clientSecret: ${READ_CLIENT_SECRET}
#     tokenUrl: ${READ_TOKEN_URL}
#     cdfProjectName: ${READ_CDF_PROJECT_NAME}
#     # Optional: If idP requires providing the scopes
#     scopes:
#       - ${READ_SCOPES}
#     # Optional: If idP requires providing the audience
#     audience: ${READ_CDF_AUDIENCE}
#   write:
#     clientId: ${WRITE_CLIENT_ID}
#     clientSecret: ${WRITE_CLIENT_SECRET}
#     tokenUrl: ${WRITE_TOKEN_URL}
#     cdfProjectName: ${WRITE_CDF_PROJECT_NAME}
#     # Optional: If idP requires providing the scopes
#     scopes:
#       - ${WRITE_SCOPES}
#     # Optional: If idP requires providing the audience
#     audience: ${WRITE_CDF_AUDIENCE}
# Or together like this:
# authentication:
#   clientId: ${CLIENT_ID}
#   clientSecret: ${CLIENT_SECRET}
#   tokenUrl: ${TOKEN_URL}
#   # Optional: If idP requires providing the scopes
#   cdfProjectName: ${CDF_PROJECT_NAME}
#   scopes:
#    - ${SCOPES}
#   # Optional: If idP requires providing the audience
#   audience: ${AUDIENCE}