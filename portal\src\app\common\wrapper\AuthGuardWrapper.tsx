import React, { ReactNode, useContext } from 'react'
import { useAuthGuard } from '../hooks/useAuthGuard'
import ErrorScreen from '@/app/components/ErrorScreen'
import LoadingScreen from '@/app/components/Loader/LoadingScreen'
import { translate } from '../utils/generate-translate'
import { UserManagementContext, UserManagementContextState } from '@celanese/celanese-ui'

interface AuthGuardWrapperProps {
    children: ReactNode
    componentName: string
    siteId?: string
}

const AuthGuardWrapper: React.FC<AuthGuardWrapperProps> = ({ children, componentName, siteId }) => {
    const { userInfo, loading } = useContext<UserManagementContextState>(UserManagementContext)
    const { checkPermissionsFromComponents } = useAuthGuard()

    if (Object.keys(userInfo).length === 0 || loading) {
        return <LoadingScreen />
    }

    const nameToCheck = componentName || 'UnknownComponent'
    const userPermission = checkPermissionsFromComponents(nameToCheck, siteId)

    const permissionResult = {
        isAuthorized: userPermission.isAuthorized,
        message: userPermission.message ? translate(userPermission.message) : '',
    }

    if (permissionResult.isAuthorized) {
        return <>{children}</>
    }

    return <ErrorScreen />
}

export default AuthGuardWrapper
