from datetime import datetime, timezone
from enum import Enum
from typing import Generic, TypeVar

_E = TypeVar("_E", bound=Enum)


class IdGenerator(Generic[_E]):
    """
    Generate unique, timestamped IDs based on an Enum and optional prefix.

    This class can be used to generate unique identifiers with consistent formatting
    that includes a timestamp and an incremented sequence number. The generated IDs
    are scoped by a combination of the enum value and an optional prefix.

    Attributes:
        datetime (str): Formatted reference datetime in "YYYY-MM-DD HH:MM:SS".

    """

    def __init__(
        self,
        ref_time: datetime = datetime.now(tz=timezone.utc),
    ) -> None:
        """
        Initialize the ID generator with a reference time.

        Args:
            ref_time (datetime): Reference time to use for timestamping.
                Defaults to the current UTC time.

        """
        self._sequence_map: dict[str, int] = {}
        self.datetime = ref_time.strftime("%Y-%m-%d %H:%M:%S")
        self._ref_time = ref_time.strftime("%Y%m%d%H%M%S")

    def get_timestamp(self) -> str:
        """
        Return the formatted reference timestamp.

        Returns:
            str: The reference datetime as "YYYY-MM-DD HH:MM:SS".

        """
        return f"{self.datetime}"

    def next_id(
        self,
        enum: _E,
        prefix_id: str | None = None,
        prefix_id_existing_count: int | None = None,
    ) -> str:
        """
        Generate the next unique ID based on the enum and optional prefix.

        Args:
            enum (_E): Enum instance to include in the ID.
            prefix_id (str, optional): Custom prefix to use instead of the default.
            prefix_id_existing_count (int, optional): Starting sequence number if
                prefix is reused from an existing context.

        Returns:
            str: A new unique ID in the format "<prefix>-<sequence>".

        """
        self._validate_enum(enum)

        prefix_id = prefix_id or f"{enum.value}-{self._ref_time}"

        self._create_sequence_if_not_exists(prefix_id, prefix_id_existing_count)
        self._sequence_map[prefix_id] += 1

        padded_sequence = str(self._sequence_map[prefix_id]).zfill(4)

        return f"{prefix_id}-{padded_sequence}"

    def _create_sequence_if_not_exists(
        self,
        prefix_id: str,
        prefix_id_existing_count: int | None,
    ) -> None:
        """
        Initialize the sequence counter for a given prefix if not already present.

        Args:
            prefix_id (str): The prefix to initialize.
            prefix_id_existing_count (int, optional): The starting count.

        """
        if prefix_id not in self._sequence_map:
            self._sequence_map[prefix_id] = (
                prefix_id_existing_count if prefix_id_existing_count is not None else 0
            )

    def _validate_enum(self, enum: _E) -> None:
        """
        Validate that the input is an instance of Enum.

        Args:
            enum (_E): The enum to validate.

        Raises:
            ValueError: If the input is not an instance of Enum.

        """
        if not isinstance(enum, Enum):
            msg = "enum must be an Enum"
            raise ValueError(msg)
