import { CSSObject } from 'styled-components'

export const drawerContainer: CSSObject = {
    width: '25vw !important',
    padding: '42px !important',
    '& > div:nth-of-type(3)': {
        height: '100%',
    },
}

export const subHeader: CSSObject = {
    fontSize: '16px',
    fontWeight: 'bold',
    color: 'grey[400]',
    textTransform: 'uppercase',
    justifyContent: 'flex-start',
    justifyItems: 'center',
    alignItems: 'center',
    display: 'flex',
    gap: '1rem',
}

export const boxCenterItems: CSSObject = {
    justifyContent: 'flex-start',
    justifyItems: 'center',
    alignItems: 'center',
    display: 'flex',
}

export const tabItensWrapper: CSSObject = {
    backgroundColor: 'primary.white',
    border: '1px solid',
    borderColor: 'otherColor.outlineBorder',
    borderRadius: '8px',
    flexGrow: '1',
    display: 'flex',
    flexDirection: 'column',
    padding: '16px 24px 16px 24px',
    marginBottom: '1rem',
    gap: '10px',
}

export const itensForm: CSSObject = {
    display: 'flex',
    flexDirection: 'inherit',
    margin: '5px 0 5px 0',
    '& .MuiTextField-root': { m: 1, width: '100%' },
    '& .MuiFormControl-root': { minWidth: '100%' },
}

export const itensDateForm: CSSObject = {
    display: 'flex',
    flexDirection: 'row',
    width: '100%',
}

export const suggestionsContainter: CSSObject = {
    width: 'calc(43vw - 84px)',
    position: 'absolute',
    backgroundColor: 'white',
    border: '1px solid',
    borderColor: 'otherColor.outlineBorder',
    borderRadius: '4px',
    zIndex: '2',
    maxHeight: 'calc(35vh - 84px)',
    overflowY: 'auto',
}

export const searchSugestion: CSSObject = {
    alignSelf: 'start !important',
    overflowWrap: 'anywhere',
    textAlign: 'left',
    width: '100%',
    justifyContent: 'start',
    textTransform: 'uppercase !important' as 'uppercase',
    font: '16px !important',
    fontFamily: 'Roboto',
}
export const clearAll: CSSObject = {
    minWidth: '120px',
    margin: 'auto',
    alignSelf: 'start !important',
    overflowWrap: 'anywhere',
    textAlign: 'left',
    width: '100%',
    justifyContent: 'center',
    textTransform: 'uppercase !important' as 'uppercase',
}

export const selectedUsersContainer: CSSObject = {
    display: 'flex',
    flexWrap: 'wrap',
    gap: '5px',
    overflowX: 'hidden',
    maxHeight: '200px',
    overflowY: 'auto',
}

export const selectedUsersAdvancedSetings: CSSObject = {
    display: 'flex',
    flexWrap: 'wrap',
    gap: '5px',
    overflowX: 'hidden',
}

export const selectedUsersAdvancedContainer: CSSObject = {
    overflowY: 'auto',
    maxHeight: 'calc(40vh - 100px)',
    border: '1px solid #cccccc',
    borderRadius: '5px',
    div: {
        maxWidth: '98%',
    },
    '@media (max-height: 910px)': {
        marginBottom: '60px',
        maxHeight: 'calc(30vh - 50px)',
    },
    margin: '0px 0px 10px 0px',
}

export const circularButton: CSSObject = {
    backgroundColor: 'action.hover',
    borderRadius: '50px',
    textTransform: 'none !important' as 'none',
}

export const changeThisName: CSSObject = {
    display: 'flex',
    alignItems: 'center',
    backgroundColor: 'action.hover',
    padding: '5px 8px',
    gap: '1px',
    borderRadius: '50px',
    maxWidth: 'calc(25vw - 84px)',
    '& .MuiButtonBase-root': {
        minWidth: 0,
        padding: 0,
    },
    ':hover': {
        backgroundColor: 'action.selected',
        cursor: 'pointer',
    },
}

export const buttonLabel: CSSObject = {
    textOverflow: 'ellipsis',
    whiteSpace: 'nowrap',
    overflow: 'hidden',
}

export const icon: CSSObject = {
    color: 'black',
}

export const expandIcon: CSSObject = {
    ...icon,
    fontSize: '20px',
}

export const saveButton: CSSObject = {
    width: '100%',
}
