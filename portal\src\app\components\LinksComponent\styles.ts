import { styled } from '@mui/system'

export const Container = styled('div')({
    flexGrow: 1,
    display: 'flex',
    textAlign: 'center',
    flexDirection: 'row',
    gap: '1',
    justifyContent: 'flex-start',
    paddingBottom: '5px',
    width: '100%',
})

export const Pointer = styled('div')({
    display: 'flex',
    flexDirection: 'row',
    cursor: 'pointer',
    width: '100%',
})

export const Icon = styled('div')`
    ${({ theme }) => `
        backgroundColor: ${theme.palette.background.grey[50]},
        alignItems: 'center',
        padding: '1px',
        color: 'grey[700]',
        display: 'flex',
        border: '1px solid',
        borderColor: ${theme.palette.background.grey[400]},
        justifyContent: 'center',
        borderRadius: '5px',
    `}
`

export const NoData = styled('div')({
    height: '100%',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
})
