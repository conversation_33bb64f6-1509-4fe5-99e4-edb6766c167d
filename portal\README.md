# Action Item Management

 The AIM (Action Item Management) app intends to create, manage, review and oversee action items. Its central function is to consolidate and centralize the process of handling these items, ensuring accountability and efficiency.

## Installation

Into the portal folder do you need install dependencies from portal.

```bash
npm install
```

## Usage

```bash
npm run build

npm run dev
```

## Environment

[Prototype](https://xd.adobe.com/view/c80f50be-0473-4434-aee7-0ef5a0ea5dc2-9d5c/?fullscreen)
[Dev environment](https://app-dplantactionitemmgmt-d-ussc-01.azurewebsites.net)
[QA environment](https://app-dplantactionitemmgmt-qa-ussc-01.azurewebsites.net)
[Prod environment](https://app-dplantactionitemmgmt-p-ussc-01.azurewebsites.net)
[Prod Friendly URL](https://actionitemmgmt.celanese.com)

## Contributing

Pull requests are welcome.

For creating branchs to features use the pattern bellow:

features/[id from card]-[a short title of the feature card]

For creating branchs to hotfixes use the pattern bellow:
hotfixes/[id from card]-[a short title of the fault card]

To creating Pull request the title need be in the pattern bellow:
[Feat|Fix|Merge]: [short title describing the release] [[DEV]|[QA]|[PROD]]