from functools import lru_cache

from cognite.client import ClientConfig, CogniteClient
from cognite.client.credentials import OAuthClientCredentials, Token
from dotenv import load_dotenv
from fastapi import Depends

from app.infra.settings import Settings


@lru_cache(maxsize=1)
def get_settings() -> Settings:
    """Return a Settings object after loading environment variables."""
    load_dotenv()  # only for local development
    return Settings()


def get_cognite_client(settings: Settings = Depends(get_settings)) -> CogniteClient:
    """Return a CogniteClient based on settings."""
    credentials = (
        Token(settings.override_token)
        if settings.override_token is not None
        else OAuthClientCredentials(
            token_url=settings.auth_token_uri,
            client_id=settings.auth_client_id,
            client_secret=settings.auth_secret,
            scopes=settings.auth_scopes,
        )
    )

    config = ClientConfig(
        client_name=settings.cognite_client_name,
        project=settings.cognite_project,
        credentials=credentials,
        base_url=settings.cognite_base_uri,
    )
    return CogniteClient(config=config)
