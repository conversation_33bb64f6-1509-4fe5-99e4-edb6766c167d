import { AuthenticationResult, IPublicClientApplication } from '@azure/msal-browser'
import dayjs from 'dayjs'
import { msalScopes } from '../configurations/auth'

export const unique = <T>(array: T[]) => Array.from(new Set(array))

export function removeTypename<T>(obj: T) {
    if (!obj || typeof obj !== 'object') {
        return obj
    }
    if (dayjs.isDayjs(obj)) {
        return dayjs(obj)
    }
    if (obj instanceof Date) {
        return new Date(obj)
    }
    const result: any = Array.isArray(obj) ? [] : {}
    for (const key in obj) {
        if (key !== '__typename') {
            result[key] = removeTypename(obj[key])
        }
    }
    return result
}

export function safeStringify(value: any) {
    try {
        return jsonStringifyRecursive(value)
    } catch (e) {
        return 'err'
    }
}

function jsonStringifyRecursive(obj: any) {
    const cache = new Set()
    return JSON.stringify(
        obj ?? '',
        (_key, value) => {
            if (typeof value === 'object' && value !== null && _key !== 'property') {
                if (cache.has(value)) {
                    // Circular reference found, discard key
                    return
                }
                // Store value in our collection
                cache.add(value)
            }
            return value
        },
        4
    )
}

export interface LanguageItem {
    externailId: string
    language: string
    code: string
}

export const availableLanguages: LanguageItem[] = [
    {
        externailId: 'English',
        language: 'English',
        code: 'EN',
    },
    {
        externailId: 'French',
        language: 'French',
        code: 'FR',
    },
    {
        externailId: 'Spanish',
        language: 'Spanish',
        code: 'ES',
    },
    {
        externailId: 'Portuguese',
        language: 'Portuguese',
        code: 'PT',
    },
    {
        externailId: 'Italian',
        language: 'Italian',
        code: 'IT',
    },
    {
        externailId: 'Mandarin',
        language: 'Mandarin',
        code: 'ZH',
    },
    {
        externailId: 'Japanese',
        language: 'Japanese',
        code: 'JA',
    },
    {
        externailId: 'Korean',
        language: 'Korean',
        code: 'KO',
    },
    {
        externailId: 'Dutch',
        language: 'Dutch',
        code: 'NL',
    },
    {
        externailId: 'German',
        language: 'German',
        code: 'DE',
    },
]

export const getIdTokenFromMsal = async (msal: IPublicClientApplication, getAccess?: boolean) => {
    const account = msal?.getActiveAccount()
    if (!account) {
        return 'NOTOKENFOUND'
    }

    const accessTokenRequest = {
        account,
        scopes: msalScopes,
    }

    let result: AuthenticationResult | undefined

    try {
        result = await msal.acquireTokenSilent(accessTokenRequest).then((accessTokenResponse) => {
            return accessTokenResponse
        })
    } catch {
        try {
            await msal.acquireTokenRedirect(accessTokenRequest)
        } catch {
            result = undefined
        }
    }

    return result ? (getAccess ? result.accessToken : result.idToken) : 'NOTOKENFOUND'
}
