import { usePathname } from 'next/navigation'

const multiSiteEnabledRoutes: string[] = [
    '/',
    '/new-action-item',
    '/action-item/details',
    '/event-source',
    '/event-source/details',
]

export function useMultiSiteEnabled() {
    const pathname = usePathname()

    const isEnabled = multiSiteEnabledRoutes.some((route) => pathname === route || pathname.startsWith(`${route}/`))

    return isEnabled
}
