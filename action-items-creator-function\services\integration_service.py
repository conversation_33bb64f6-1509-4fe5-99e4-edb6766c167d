import os
import sys
from typing import Optional

from clients.integration.models import UserByNodeAccessResponse, UserIntegrationResponse
from clients.integration.requests import BaseIntegrationRequest, UserByNodeAccessRequest
from clients.action_item_client import ActionItemClient

script_dir = os.path.dirname(os.path.abspath(__file__))
project_dir = os.path.dirname(script_dir)
sys.path.append(project_dir)


class IntegrationService:
    def __init__(
        self,
        action_item_client: ActionItemClient,
    ):
        self._action_item_client = action_item_client
        self._log = action_item_client.actions._logging

    async def get_user(
        self, request: BaseIntegrationRequest
    ) -> Optional[UserIntegrationResponse]:
        """
        Retrieves user integration details, setting the appropriate `site_code` in the request if not provided.

        Workflow:
        - If `site_code` is not set in the request, the method fetches user information.
        - It then checks for a `favorite_reporting_site` in the user data:
            - If the favorite site is found and matches one of the user's sites, the request's `site_code` is updated.
            - If no favorite site is set but the user has sites, the request's `site_code` is set to the first site's ID.
            - If no sites exist, the method returns `None`.

        Parameters:
        - request (BaseIntegrationRequest): The request object containing integration details.

        Returns:
        - Optional[UserIntegrationResponse]: The user integration response object if successful.
        - None: If an error occurs or required data is missing.

        Exceptions:
        - Logs any exceptions encountered during execution and returns `None`.
        """
        try:
            user = self._action_item_client.integration.get_user_by_request(request)
            if user.applications is None:
                raise ValueError("User has no applications.")
            if (
                request.site_code is None
                or not any(site.site_id == request.site_code for site in user.sites)
                or len(user.applications[0].roles) == 0
            ):
                if user.favorite_reporting_site is not None and any(
                    site.site_id == user.favorite_reporting_site.site_id
                    for site in user.sites
                ):
                    request.site_code = user.favorite_reporting_site.site_id
                elif user.sites:
                    request.site_code = user.sites[0].site_id
                else:
                    raise ValueError("No valid site code found for the user.")
                user = self._action_item_client.integration.get_user_by_request(request)

            return user
        except Exception as err:
            self._log.error(f"Error getting integration. Error: {err}")
            return None

    async def get_users_by_feature_code(
        self, request: UserByNodeAccessRequest
    ) -> dict[str, list[str]]:
        """
        Retrieves user external_ids for each feature code specified in the request.

        This method processes a list of feature codes, fetches associated users for each
        feature code via the integration client, and returns a dictionary where each
        feature code maps to a list of user external_ids. If no users are found for a
        feature code, an empty list is returned for that feature code.

        Args:
            request (UserByNodeAccessRequest):
                The request object containing the list of feature codes to be processed
                and the optional type of access (default is "EditAccess").

        Returns:
            dict[str, list[str]]:
                A dictionary where:
                - The keys are feature codes (`str`).
                - The values are lists of external_ids (`list[str]`) of the users associated
                  with each feature code.

        Example:
            request = UserByNodeAccessRequest(
                feature_code=["Feature1", "Feature2"],
                type="EditAccess"
            )
            result = await get_users_by_feature_code(request)
            # Example result:
            # {
            #     "Feature1": ["user1_external_id", "user2_external_id"],
            #     "Feature2": []
            # }

        Raises:
            Exception: Logs an error and returns an empty dictionary if an exception occurs
            during the process.

        Notes:
            - If no users are found for a given feature code, the value will be an empty list.
            - Logs errors encountered during the execution to aid debugging.
        """
        users_by_feature = {}
        try:
            for feature_code in request.feature_code:
                users = (
                    self._action_item_client.integration.get_user_by_feature_request(
                        request, feature_code
                    )
                )
                users_by_feature[feature_code] = (
                    [user.external_id for user in users] if users else []
                )
        except Exception as err:
            self._log.error(f"Error getting integration. Error: {err}")
            return {}
        return users_by_feature
