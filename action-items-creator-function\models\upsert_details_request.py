from pydantic import BaseModel, Field, validator
from typing import List, Optional

import os
import sys

script_dir = os.path.dirname(os.path.abspath(__file__))
project_dir = os.path.dirname(script_dir)
sys.path.append(project_dir)


class UpsertDetailsRequest(BaseModel):
    id: Optional[str] = Field(default=None, max_length=200)
    reporting_site_id: Optional[str] = Field(default=None, alias="reportingSiteId")
    active_user: Optional[str] = Field(default=None, alias="activeUser")
    roles_id: Optional[List[str]] = Field(default=None, alias="rolesIds")
    teams_id: Optional[List[str]] = Field(default=None, alias="teamsIds")
    template_config_id: Optional[str] = Field(default=None, alias="templateConfigId")

    @validator("roles_id", pre=True, always=True)
    def validate_roles_id(cls, v):
        return v if v != [] else None

    @validator("teams_id", pre=True, always=True)
    def validate_teams_id(cls, v):
        return v if v != [] else None
