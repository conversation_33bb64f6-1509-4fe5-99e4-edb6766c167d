from datetime import date, datetime
from typing import Any
from pydantic import BaseModel, Field

import os
import sys

script_dir = os.path.dirname(os.path.abspath(__file__))
project_dir = os.path.dirname(script_dir)
sys.path.append(project_dir)


ACTION_ITEM_CREATION = "Action Item Creation"
OVERDUE_TYPE = "Overdue"
OVERDUE_N_DAYS_TYPE = "Action Due in [n] Days"

PRIMITIVE_TYPES = (str, int, float, bool)
NUMERIC_TYPES = (int, float)


class NotificationProperty(BaseModel):
    name: str
    value: str
    type: str


class Notification(BaseModel):
    notification_type: str = Field(serialization_alias="notificationType")
    description: str
    users: list[str] = Field(default_factory=list)
    properties: list[NotificationProperty] = Field(default_factory=list)

    @classmethod
    def from_action_item_creation(
        cls, action_item_dict: dict[str, Any], assigned_to_email: str, owner_email: str
    ) -> "Notification":
        properties = create_notification_properties(action_item_dict)
        user_ids_for_notification = [assigned_to_email, owner_email]

        return cls(
            notification_type=ACTION_ITEM_CREATION,
            description=ACTION_ITEM_CREATION,
            properties=properties,
            users=list(set(user_ids_for_notification)),
        )

    @classmethod
    def create_notification_request(
        cls,
        action_item_dict: dict[str, Any],
        users: list[str],
        notification_type: str,
        description: str | None = None,
    ) -> "Notification":
        properties = create_notification_properties(action_item_dict)

        return cls(
            notification_type=notification_type,
            description=description if description is not None else notification_type,
            properties=properties,
            users=list(set(users)),
        )


def create_properties(key: str, value: Any) -> list[NotificationProperty]:
    if not value:
        return []

    if type(value) in PRIMITIVE_TYPES:
        prop_type = "text" if type(value) is str else "number"
        return [NotificationProperty(name=key, value=str(value), type=prop_type)]

    properties: list[NotificationProperty] = []

    keys_to_search = {
        "externalId": "ExternalId",
        "name": "Name",
        "description": "Description",
        "space": "Space",
        "firstName": "FirstName",
        "lastName": "LastName",
        "userMail": "UserMail",
    }

    for key_to_search, suffix in keys_to_search.items():
        if key_to_search in value:
            properties.append(
                NotificationProperty(
                    name=f"{key}{suffix}",
                    value=str(value[key_to_search]),
                    type="text",
                )
            )
    return properties


def create_notification_properties(
    object: dict[str, Any]
) -> list[NotificationProperty]:
    properties: list[NotificationProperty] = []
    convert_object = convert_dict_keys_to_camel_case(object)
    for key, value in convert_object.items():
        if value is None:
            continue
        if isinstance(value, (datetime, date)):
            value = value.strftime("%Y-%m-%d")

        if type(value) is list:
            index = -1

            for value_item in value:
                index += 1
                result_properties = create_properties(key, value_item)
                for item in result_properties:
                    item.name = f"{item.name}_{index}"
                properties.extend(result_properties)
        else:
            properties.extend(create_properties(key, value))
    return properties


def to_camel_case(snake_str: str) -> str:
    components = snake_str.split("_")
    return components[0] + "".join(x.title() for x in components[1:])


def convert_dict_keys_to_camel_case(d: dict[str, Any]) -> dict[str, Any]:
    new_dict = {}
    for key, value in d.items():
        new_key = to_camel_case(key)
        if isinstance(value, dict):
            new_dict[new_key] = convert_dict_keys_to_camel_case(value)
        elif isinstance(value, list):
            new_dict[new_key] = [
                (
                    convert_dict_keys_to_camel_case(item)
                    if isinstance(item, dict)
                    else item
                )
                for item in value
            ]
        else:
            new_dict[new_key] = value
    return new_dict
