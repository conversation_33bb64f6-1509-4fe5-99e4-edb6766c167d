from datetime import date
from typing import Annotated

from pydantic import BaseModel, ConfigDict, Field
from pydantic.alias_generators import to_camel

from clients.core.constants import ApprovalWorkflowStepDescriptionEnum
from clients.core.models import AttachmentsEntity, DescribableEntity, Node
from clients.core.validators import (
    edge_unwraper_validator,
    edge_unwraper_validator_with_edge_id,
    str_or_default_validator,
)


class _User(Node):
    display_name: Annotated[str, str_or_default_validator] | None = None
    first_name: Annotated[str, str_or_default_validator] | None = None
    last_name: Annotated[str, str_or_default_validator] | None = None
    email: Annotated[str, str_or_default_validator] | None = None
    edge_external_id: str | None = None

    def _format_user(self) -> str:
        return f"{self.last_name}, {self.first_name}"


class _UserSiteConfiguration(Node):
    reporting_site: Node | None = None
    on_site_manager: _User | None = None


class _UserComplement(Node):
    user_site_configurations: Annotated[
        list[_UserSiteConfiguration],
        edge_unwraper_validator,
    ] = Field(default_factory=list)


class _UserAzureAttribute(Node):
    user: _User | None
    user_complement: _UserComplement | None = None


class _SourceEvent(Node):
    title: str | None = Field(default=None)
    owner: Node | None = Field(default=None)
    secondary_owner_users: Annotated[list[Node], edge_unwraper_validator] = Field(
        default_factory=list,
    )
    secondary_owner_roles: Annotated[list[Node], edge_unwraper_validator] = Field(
        default_factory=list,
    )
    secondary_owner_teams: Annotated[list[Node], edge_unwraper_validator] = Field(
        default_factory=list,
    )
    views: list[str] | None = Field(default=None)


class _ActionItemLink(Node):
    description: str | None
    link: str


class _StatusHistoryInstance(Node):
    status: DescribableEntity
    friendly_name: str | None
    status_subject: _UserAzureAttribute | None
    changed_at: str | None


class _StatusApprovalWorkflowStep(Node):
    description: Annotated[str, str_or_default_validator]


class _ApprovalWorkflowStep(Node):
    description: str | None = None
    status: _StatusApprovalWorkflowStep | None = None
    users: Annotated[list[_User], edge_unwraper_validator_with_edge_id] | None = None


class ApprovalWorkflow(Node):
    status: DescribableEntity | None = None
    steps: Annotated[list[_ApprovalWorkflowStep], edge_unwraper_validator] | None = (
        Field(default=None)
    )

    def get_approver(self) -> _User | None:
        return self._get_user_by_step_description(
            ApprovalWorkflowStepDescriptionEnum.APPROVAL.value,
        )

    def get_verifier(self) -> _User | None:
        return self._get_user_by_step_description(
            ApprovalWorkflowStepDescriptionEnum.VERIFICATION.value,
        )

    def _get_user_by_step_description(self, description: str) -> _User | None:
        return next(
            (
                step.users[0]
                for step in self.steps or []
                if step.users and step.description == description
            ),
            None,
        )


class _PropertiesToChange(BaseModel):
    model_config = ConfigDict(
        alias_generator=to_camel,
        populate_by_name=True,
        from_attributes=True,
    )
    new_due_date: str | None = None
    new_assignee: str | None = None
    new_assignee_name: str | None = None


class ChangeRequest(Node):
    comments: list[str] | None = None
    properties_to_change: _PropertiesToChange | None = None
    change_request_type: Node | None = None
    approval_workflow: ApprovalWorkflow | None = None
    attatchments: list[AttachmentsEntity | str] | None = None


class MetadataField(Node):
    value: str | None = None
    metadata_type: DescribableEntity | None = None


class _RecurrenceType(Node):
    name: str | None
    description: str | None


class Recurrence(Node):
    description: str | None = None
    week_days: list[int] | None
    months: list[int] | None
    day_of_the_month: int | None
    quarters: list[int] | None
    month_of_the_year: int | None
    next_dates: list[date] | None
    start_date: date | None
    end_date: date | None
    recurrence_type: _RecurrenceType | None


class ViewEntityForUpdateComparisonResult(Node):
    edge_external_id: str


class SourceEventForUpdateComparisonResult(Node):
    owner: Node | None = Field(default=None)
    secondary_owner_users: Annotated[
        list[ViewEntityForUpdateComparisonResult],
        edge_unwraper_validator_with_edge_id,
    ]
    secondary_owner_roles: Annotated[
        list[ViewEntityForUpdateComparisonResult],
        edge_unwraper_validator_with_edge_id,
    ]
    secondary_owner_teams: Annotated[
        list[ViewEntityForUpdateComparisonResult],
        edge_unwraper_validator_with_edge_id,
    ]
    view_users: Annotated[
        list[ViewEntityForUpdateComparisonResult],
        edge_unwraper_validator_with_edge_id,
    ]
    view_roles: Annotated[
        list[ViewEntityForUpdateComparisonResult],
        edge_unwraper_validator_with_edge_id,
    ]
    view_teams: Annotated[
        list[ViewEntityForUpdateComparisonResult],
        edge_unwraper_validator_with_edge_id,
    ]
