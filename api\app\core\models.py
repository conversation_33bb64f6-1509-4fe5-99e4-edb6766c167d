from typing import Any, <PERSON><PERSON>, TypeVar

from pydantic import BaseModel, ConfigDict, Field, computed_field
from pydantic.alias_generators import to_camel

DEFAULT_MODEL_CONFIG = ConfigDict(
    alias_generator=to_camel,
    populate_by_name=True,
    from_attributes=True,
)


class BaseCamelCaseModel(BaseModel):
    """Base model that uses camel cased property names as aliases."""

    model_config = DEFAULT_MODEL_CONFIG


class Node(BaseCamelCaseModel):
    """Represents the base cognite node structure."""

    external_id: str = Field(min_length=1)
    space: str = Field(min_length=1)


class Edge(Node):
    """Represents the base cognite edge structure."""

    start_node: Node
    end_node: Node
    type: Node


T = TypeVar("T", bound=Node)


class _PageInfo(BaseCamelCaseModel):
    has_next_page: bool = False
    end_cursor: str | None = None


class PaginatedInstanceResponse(BaseCamelCaseModel, Generic[T]):
    """Represents a paginated response from a cognite graphql request."""

    data: list[T] = Field(default_factory=list[T])
    has_next_page: bool = False
    cursor: str | None = None

    @computed_field
    @property
    def total_items(self) -> int:
        """Return count of total items in response."""
        return len(self.data)

    def first_or_default(self, default: T | None = None) -> T | None:
        """Get the first item on response or default."""
        return self.data[0] if len(self.data) > 0 else default

    @classmethod
    def create_default(cls) -> "PaginatedInstanceResponse[T]":
        """Create an empty response."""
        return cls(data=[], has_next_page=False, cursor=None)

    @classmethod
    def from_graphql_response(
        cls,
        response: dict[str, Any],
        request_page_size: int,
    ) -> "PaginatedInstanceResponse[T]":
        """Create an instance from a cognite graphql reponse."""
        keys = list(response.keys())
        if not keys:
            return cls.create_default()

        entry = response[keys[0]]
        page_info = _PageInfo.model_validate(entry.get("pageInfo", {}))

        raw_items: list[T] = entry.get("items", [])

        result = cls(
            has_next_page=page_info.has_next_page,
            cursor=page_info.end_cursor,
            data=raw_items,
        )

        # This fixes the Cognite pagination issue of N + 1 requests
        if result.has_next_page and len(raw_items) < request_page_size:
            result.has_next_page = False
            result.cursor = None

        return result
