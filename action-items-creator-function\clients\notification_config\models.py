from typing import Annotated
from clients.core.models import Node
from clients.core.validators import edge_unwraper_validator


class NotificationConfigResult(Node):
    notification_type: Node | None
    reporting_site: Node | None
    number_of_days: int
    assigned_send_to_types: Annotated[list[Node], edge_unwraper_validator]
    assigned_roles: Annotated[list[Node], edge_unwraper_validator] | None
