import { CSSObject } from '@mui/material'
import { styled } from '@mui/system'

export const gridContainerStyle: CSSObject = {
    width: '100%',
    rowGap: '1rem',
}

export const boxWithFlexCenterStyle: CSSObject = {
    width: '100%',
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    '& .MuiFormControl-root': {
        maxWidth: '100% !important',
        width: '100% !important',
    },
    '& > *': { width: '100% !important' },
}

export const ButtonContainerStyle = styled('div')`
    ${({ theme }) => `
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        margin: 1.5rem 0 0 0;
        gap: 1rem;
        background: ${theme.palette.background.paper};
    `}
`

export const loaderContainerStyle: CSSObject = {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    height: '200px',
}

export const formContainerStyle = {
    display: 'flex',
    flexDirection: 'column' as const,
    gap: '1rem',
}

export const boxContainerStyle: CSSObject = {
    display: 'flex',
    flexDirection: 'column',
    overflowY: 'auto',
    overflowX: 'hidden',
    gap: '1rem',
}
