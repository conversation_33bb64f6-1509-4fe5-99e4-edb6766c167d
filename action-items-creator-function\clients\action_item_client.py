from clients.action_comment import ActionCommentClient
from clients.actions import ActionClient
from clients.approval_workflow import ApprovalWorkflowClient
from clients.category_configuration import CategoryConfigurationClient
from clients.core import ServiceParams
from clients.dashboard_kpis import DashboardKpisClient
from clients.equipment import EquipmentClient
from clients.files import FileClient
from clients.functional_location import Functional<PERSON>ocation<PERSON>lient
from clients.integration import IntegrationClient
from clients.kpis import ActionItemKpiClient
from clients.notification import NotificationClient
from clients.notification_config import NotificationConfigClient
from clients.reporting_location import ReportingLocationClient
from clients.reporting_site import ReportingSiteClient
from clients.reporting_unit import ReportingUnitClient
from clients.sort_mapper import SortMapperClient
from clients.source_event import SourceEventClient
from clients.templates import TemplateClient
from clients.user.user_client import UserClient
from clients.user_complement import UserComplementClient
from clients.user_role_site import UserRoleSiteClient


class ActionItemClient:
    """Client responsible for managing action item-related operations."""

    def __init__(
        self,
        params: ServiceParams,
    ) -> None:
        """Initialize all action item-related clients with the given service parameters."""
        self.actions = ActionClient(params)
        self.approval_workflow = ApprovalWorkflowClient(params)
        self.category_configuration = CategoryConfigurationClient(params)
        self.kpis = ActionItemKpiClient(params)
        self.source_event = SourceEventClient(params)
        self.user_complement = UserComplementClient(params)
        self.dashboard_kpis = DashboardKpisClient(params)
        self.notification = NotificationClient(params)
        self.integration = IntegrationClient(params)
        self.files = FileClient(params)
        self.reporting_unit = ReportingUnitClient(params)
        self.reporting_location = ReportingLocationClient(params)
        self.action_comment = ActionCommentClient(params)
        self.notification_config = NotificationConfigClient(params)
        self.reporting_site = ReportingSiteClient(params)
        self.user_role_site = UserRoleSiteClient(params)
        self.user = UserClient(params)
        self.functional_location = FunctionalLocationClient(params)
        self.equipment = EquipmentClient(params)
        self.sort_mapper = SortMapperClient(params)
        self.templates = TemplateClient(params)
