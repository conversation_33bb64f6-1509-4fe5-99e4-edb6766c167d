import { gql, useMutation } from '@apollo/client'
import { ActionItemLink } from '../../models/link'

const UPSERT_ACTION_ITEM_LINK_MUTATION = gql`
    mutation UpsertActionItemLink($actionItemLinks: [_UpsertActionItemLink!]!) {
        upsertActionItemLink(items: $actionItemLinks) {
            space
            externalId
        }
    }
`

export const useUpsertActionItemLink = () => {
    return useMutation<ActionItemLink>(UPSERT_ACTION_ITEM_LINK_MUTATION)
}
