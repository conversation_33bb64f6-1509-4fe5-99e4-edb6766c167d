import { gql } from '@apollo/client'
import { useEffect, useState } from 'react'
import { useGraphqlQuery } from '../'
import { ActionSourceDetailConfig } from '../../models/action-source-detail-config'

export interface ActionSourceDetailConfigQueryRequest {
    sourceTypeId?: string
}

const buildActionSourceDetailConfigQuery = (request: ActionSourceDetailConfigQueryRequest): string => {
    const filters: string[] = []

    let queryFilter = '{ }'

    if (request.sourceTypeId) {
        filters.push(`{ sourceType: { externalId: { eq: "${request.sourceTypeId}" } } }`)
    }

    if (filters.length > 0) {
        queryFilter = `{ and: [ ${filters.join(', ')} ] }`
    }

    return `
        query GetActionSourceDetailConfig {
            listActionSourceDetailConfig(
                filter: ${queryFilter}
                , first: 1
            ) {
                items {
                    externalId
                    space
                    properties
                }
            }
        }
    `
}

export const useActionSourceDetailConfigs = (request: ActionSourceDetailConfigQueryRequest) => {
    const query = buildActionSourceDetailConfigQuery(request)
    const listName = 'listActionSourceDetailConfig'
    const { data: fdmData, refetch } = useGraphqlQuery<ActionSourceDetailConfig>(gql(query), listName, {})

    const [resultData, setResultData] = useState<{ data: ActionSourceDetailConfig[]; loading: boolean }>({
        data: [],
        loading: true,
    })

    useEffect(() => {
        if (fdmData.length == 0) {
            setResultData({ data: [], loading: false })
        } else {
            setResultData({ data: fdmData, loading: false })
        }
    }, [fdmData])

    return {
        loading: resultData.loading,
        actionSourceDetailConfigs: resultData.data,
        refetchEvents: refetch,
    }
}
