import {
    ActionItemStateStatus,
    ActionItemStateStatusEnum,
    ActionItemStateStatusEnumType,
} from '@/app/common/enums/KpiStatusEnum'
import { getLocalUserSite, TranslationContext, TranslationContextState } from '@celanese/celanese-ui'
import { Box, Typography, useMediaQuery, useTheme } from '@mui/material'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
import React, { useContext, useEffect, useMemo, useState } from 'react'
import {
    ActionStatusExternalIdHomeClosedDefaultEnum,
    ActionStatusExternalIdHomeDefaultEnum,
    ActionStatusQuery,
    AllActionStatusExternalIdEnum,
    RestrictedStatus,
} from '@/app/common/enums/ActionItemStatusEnum'
import { AzureFunctionClient } from '@/app/common/clients/azure-function-client'
import { AimTabs } from '../Tabs'
import { HomeKPIs } from './HomeKPIs'
import { ActionTable } from '../ActionTable'
import { AutocompleteOption } from '../FieldsComponent/GenericAutocomplete'
import { ClnButtonProps, ClnPanel, MatIcon } from '@celanese/ui-lib'
import { useSnackbar } from '@/app/common/contexts/SnackbarContext'
import { useRouter } from 'next/navigation'
import { translate } from '@/app/common/utils/generate-translate'
import { useLoading } from '@/app/common/contexts/LoadingContext'
import { UserRolesPermission } from '@celanese/celanese-sdk'
import { PageType } from '@/app/common/enums/PageTypeEnum'
import { SITE_EXTERNAL_ID_REQUIRED_FIELD } from '@/app/common/utils/validate-codes'
import { FilterInfoProps } from '../ActionTable/HomeFilter'

dayjs.extend(utc)

interface HomeComponentProps {
    activeUser: UserRolesPermission
}

export const HomeComponent = ({ activeUser }: HomeComponentProps) => {
    const router = useRouter()
    const { locale } = useContext<TranslationContextState>(TranslationContext)

    const client = new AzureFunctionClient()
    const { showSnackbar } = useSnackbar()
    const { showLoading } = useLoading()

    const theme = useTheme()
    const isMobile = useMediaQuery(theme.breakpoints.down('md'))

    const siteIds = getLocalUserSite(true)?.map((site) => site.siteId) || []

    const [currentTab, setCurrentTab] = useState<number>(parseInt(localStorage.getItem('currentTabHome') ?? '0', 10))

    const storedFilterInfo = sessionStorage.getItem(`home-filterInfo-${currentTab === 0 ? 'toDo' : 'closed'}-tab`)
    const parsedFilterInfo = storedFilterInfo ? JSON.parse(storedFilterInfo) : {}

    const initialState =
        parsedFilterInfo?.filter?.kpiFilter ??
        (currentTab ? ActionItemStateStatusEnum.TotalActions : ActionItemStateStatusEnum.AssignedToMe)

    const [state, setState] = useState<ActionItemStateStatus>(initialState)

    const [siteSpecificCategories, setSiteSpecificCategories] = useState<AutocompleteOption[]>(
        parsedFilterInfo?.siteSpecificCategories || []
    )
    const [reportingUnits, setReportingUnits] = useState<AutocompleteOption[]>(parsedFilterInfo?.reportingUnits || [])
    const [reportingLocations, setReportingLocations] = useState<AutocompleteOption[]>(
        parsedFilterInfo?.reportingLocations || []
    )

    const [ownerName, setOwnerName] = useState<AutocompleteOption[]>(
        parsedFilterInfo?.ownerName ? parsedFilterInfo?.ownerName : []
    )

    const activeUserOption: AutocompleteOption = {
        value: activeUser.externalId ?? '',
        label: `${activeUser.lastName}, ${activeUser.firstName}`,
    }

    const [assigneeName, setAssigneeName] = useState<AutocompleteOption[]>(
        parsedFilterInfo?.assignedName ? parsedFilterInfo?.assignedName : currentTab === 0 ? [activeUserOption] : []
    )

    const [filterInfo, setFilterInfo] = useState<FilterInfoProps>({
        reportingSiteExternalId: siteIds[0] ?? '-',
        statusExternalIds:
            currentTab === 0
                ? Object.values(ActionStatusExternalIdHomeDefaultEnum)
                : Object.values(ActionStatusExternalIdHomeClosedDefaultEnum),
        assignedToExternalId: parsedFilterInfo?.filter
            ? parsedFilterInfo?.filter?.assignedToExternalId
            : currentTab === 0
              ? activeUser.externalId
                  ? [activeUser.externalId]
                  : []
              : undefined,
        pageSize: 10,
        ...parsedFilterInfo?.filter,
        kpiFilter: initialState,
    })

    const [currentPage, setCurrentPage] = useState<number>(0)
    const [memoryPage, setMemoryPage] = useState<number>(0)

    const tabs = useMemo(() => {
        return [
            {
                label: translate('table.tabs.todo'),
                content: '',
                dataTest: 'home-to_do_tab',
            },
            {
                label: translate('table.tabs.closed'),
                content: '',
                dataTest: 'home-closed_tab',
            },
        ]
    }, [locale, translate])

    const handleError = (err: any) => {
        const errorMessage = err instanceof Error ? err.message : `${translate('alerts.unexpectedErrorOcurred')}`
        showSnackbar(`${translate('alerts.unexpectedErrorOcurred')}: ${errorMessage}`, 'error', 'home-screen')
    }

    const additionalButtons: ClnButtonProps[] = [
        {
            label: isMobile ? '' : translate('table.newActionItem'),
            startIcon: isMobile ? <MatIcon icon="add" /> : undefined,
            variant: isMobile ? 'text' : 'contained',
            sxProps: isMobile
                ? {
                      color: 'primary.main !important',
                      padding: '8px 12px',
                      minWidth: '12px !important',
                      '& .MuiButton-startIcon': {
                          marginRight: 0,
                      },
                  }
                : undefined,
            onClick: () => {
                showLoading(true)
                localStorage.setItem('isEditForm', 'true')
                router.push('/new-action-item')
            },
        },
    ]

    const getDefaultFilterInfo = (kpi: ActionItemStateStatusEnumType): FilterInfoProps => {
        const common = {
            reportingSiteExternalId: siteIds[0] ?? '-',
            pageSize: filterInfo.pageSize,
            kpiFilter: filterInfo.kpiFilter,
        }

        const userId = activeUser.externalId ? [activeUser.externalId] : []
        const unitIds = activeUser.units?.map((u) => u.unitCode) ?? []

        const withStatus = (status: string) => ({ ...common, statusExternalIds: [status] })
        const withStatusAndAssigned = (status: string) => ({ ...withStatus(status), assignedToExternalId: userId })
        const withStatusAndOwner = (status: string) => ({ ...withStatus(status), ownerExternalId: userId })
        const withStatusAndUnits = (status: string) => ({ ...withStatus(status), reportingUnitExternalIds: unitIds })

        switch (kpi) {
            case ActionItemStateStatusEnum.TotalActions:
            case ActionItemStateStatusEnum.RelatedToMe:
            case ActionItemStateStatusEnum.Overdue:
                return common

            case ActionItemStateStatusEnum.AssignedToMe:
                return { ...common, assignedToExternalId: userId }

            case ActionItemStateStatusEnum.PendingApprovals:
                return withStatusAndAssigned(AllActionStatusExternalIdEnum.PendingApproval)

            case ActionItemStateStatusEnum.PendingVerifications:
                return withStatusAndAssigned(AllActionStatusExternalIdEnum.PendingVerification)

            case ActionItemStateStatusEnum.MyApprovals:
                return withStatus(AllActionStatusExternalIdEnum.PendingApproval)

            case ActionItemStateStatusEnum.MyVerifications:
                return withStatus(AllActionStatusExternalIdEnum.PendingVerification)

            case ActionItemStateStatusEnum.MyChallenges:
                return withStatusAndOwner(AllActionStatusExternalIdEnum.ChallengePeriod)

            case ActionItemStateStatusEnum.MyExtends:
                return withStatus(AllActionStatusExternalIdEnum.DueDateExtension)

            case ActionItemStateStatusEnum.MyReassignment:
                return siteIds.includes(SITE_EXTERNAL_ID_REQUIRED_FIELD)
                    ? withStatusAndUnits(AllActionStatusExternalIdEnum.ReassignmentPeriod)
                    : withStatus(AllActionStatusExternalIdEnum.ReassignmentPeriod)

            default:
                return common
        }
    }

    useEffect(() => {
        const storedFilterInfo = sessionStorage.getItem(`home-filterInfo-${currentTab === 0 ? 'toDo' : 'closed'}-tab`)
        const parsedFilterInfo = storedFilterInfo ? JSON.parse(storedFilterInfo) : {}
        sessionStorage.setItem(
            `home-filterInfo-${currentTab === 0 ? 'toDo' : 'closed'}-tab`,
            JSON.stringify({
                filter: parsedFilterInfo?.filter ?? filterInfo,
                ownerName: ownerName,
                assignedName: assigneeName,
                siteSpecificCategories: siteSpecificCategories,
                reportingUnits: reportingUnits,
                reportingLocations: reportingLocations,
            })
        )
    }, [assigneeName, ownerName])

    return (
        <Box
            sx={{
                display: 'flex',
                flexDirection: 'column',
                width: '100%',
            }}
        >
            <ClnPanel
                sx={{
                    padding: '1.5rem',
                    display: 'flex',
                    flexDirection: 'column',
                }}
            >
                <HomeKPIs
                    client={client}
                    siteIds={siteIds}
                    activeUser={activeUser}
                    currentTab={currentTab}
                    setFilterInfo={setFilterInfo}
                    filterInfo={filterInfo}
                    activeUserOption={activeUserOption}
                    setAssigneeName={setAssigneeName}
                    setOwnerName={setOwnerName}
                    state={state}
                    setState={setState}
                    setPage={(value: number) => {
                        setCurrentPage(value)
                        setMemoryPage(value)
                    }}
                    handleError={handleError}
                />

                <Typography
                    sx={{
                        wordBreak: 'break-word',
                        fontSize: '0.75rem',
                        color: 'text.secondary',
                    }}
                >
                    {translate(
                        `kpis.infoMessage.${currentTab === 0 ? 'toDo' : 'closed'}.${
                            state.charAt(0).toLowerCase() + state.slice(1)
                        }`
                    )}
                </Typography>

                <AimTabs
                    value={currentTab}
                    tabs={tabs}
                    onChange={(e, value) => {
                        localStorage.setItem('currentTabHome', value.toString())
                        setCurrentTab(value)
                        setState(ActionItemStateStatusEnum.AssignedToMe)
                        setCurrentPage(0)
                        setMemoryPage(0)

                        const newFilterInfo: FilterInfoProps = {
                            reportingSiteExternalId: siteIds ?? '-',
                            statusExternalIds:
                                value === 0
                                    ? Object.values(ActionStatusExternalIdHomeDefaultEnum)
                                    : Object.values(ActionStatusExternalIdHomeClosedDefaultEnum),
                            assignedToExternalId: activeUser.externalId ? [activeUser.externalId] : [],
                            pageSize: filterInfo.pageSize,
                            kpiFilter: ActionItemStateStatusEnum.AssignedToMe,
                            sortBy: filterInfo.sortBy,
                            direction: filterInfo.direction,
                        }

                        sessionStorage.setItem(
                            `home-filterInfo-${value === 0 ? 'toDo' : 'closed'}-tab`,
                            JSON.stringify({
                                filter: newFilterInfo,
                                ownerName: [],
                                assignedName: [activeUserOption],
                                siteSpecificCategories: siteSpecificCategories,
                                reportingUnits: reportingUnits,
                                reportingLocations: reportingLocations,
                            })
                        )

                        setFilterInfo(newFilterInfo)
                        setAssigneeName([activeUserOption])
                        setOwnerName([])
                    }}
                    sxProps={{
                        marginBottom: '0.5rem',
                        variant: 'fullWidth',
                    }}
                />
                <ActionTable
                    id="home"
                    setFilterInfo={setFilterInfo}
                    filterInfo={filterInfo}
                    statusNameListForIncludeOrExclude={
                        currentTab === 0 ? undefined : [RestrictedStatus.COMPLETED, RestrictedStatus.CANCELLED]
                    }
                    actionForStatusFilter={currentTab === 0 ? undefined : ActionStatusQuery.INCLUDE}
                    setCurrentPage={setCurrentPage}
                    currentPage={currentPage}
                    isToDoTab={currentTab === 0}
                    activeUser={activeUser}
                    client={client}
                    siteIds={siteIds}
                    ownerName={ownerName}
                    setOwnerName={setOwnerName}
                    assigneeName={assigneeName}
                    setAssigneeName={setAssigneeName}
                    handleFetchFailure={handleError}
                    memoryPage={memoryPage}
                    setMemoryPage={setMemoryPage}
                    additionalButtons={additionalButtons}
                    disableDefaultFilterFields
                    defaultFilterInfo={getDefaultFilterInfo(state)}
                    pageType={PageType.Home}
                    initialChipValue={filterInfo?.search ?? ''}
                    siteSpecificCategories={siteSpecificCategories}
                    reportingUnits={reportingUnits}
                    reportingLocations={reportingLocations}
                    setSiteSpecificCategories={setSiteSpecificCategories}
                    setReportingLocations={setReportingUnits}
                    setReportingUnits={setReportingLocations}
                />
            </ClnPanel>
        </Box>
    )
}
