from cognite.client.data_classes import data_modeling

from clients.core.constants import DataSpaceEnum
from clients.core.filters import (
    equals_sdk_filter,
)

from .requests import GetTemplatesByUserIdRequest


def get_user_id_filters_for_templates_query(
    request: GetTemplatesByUserIdRequest,
) -> list[data_modeling.Filter]:
    """
    Generate filters for querying templates based on the user's ID.

    Args:
        request (GetTemplatesByUserIdRequest): The request containing the active user's external ID.

    Returns:
        list[data_modeling.Filter]: A list of filters to be applied in the template query.
        The filter includes a match for the "UMG_DATA_SPACE" and the active user's external ID.

    """
    return [
        equals_sdk_filter("space", DataSpaceEnum.UMG_DATA_SPACE),
        equals_sdk_filter(
            "externalId",
            request.active_user_external_id or "",
        ),
    ]


def get_space_filters_for_templates_query(
    space: str,
) -> data_modeling.filters.Equals:
    """
    Generate a filter for querying templates based on the provided space.

    Args:
        space (str): The space for which the templates are being filtered.

    Returns:
        data_modeling.filters.Equals: An equality filter for the "space" field.

    """
    return equals_sdk_filter("space", space)
