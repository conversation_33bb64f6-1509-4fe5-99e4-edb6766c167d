import React, { SetStateAction, useCallback, useContext, useEffect, useMemo, useState } from 'react'
import { Box, Typography, IconButton, Checkbox, useTheme, useMediaQuery } from '@mui/material'
import { ClnButton, ClnCircularProgress, MatIcon } from '@celanese/ui-lib'
import { NoTranslate, UserManagementContext, UserManagementContextState } from '@celanese/celanese-ui'
import { DataGridTable } from '../PaginatedTable/DataGridTable'
import { ROWS_PER_PAGE_OPTIONS } from '@/app/common/utils'
import { GridColDef, GridRowsProp } from '@mui/x-data-grid-pro'
import { ModalWrapper } from '../ModalComponent/Modal/ModalWrapper'
import { SelectionState } from './EventModalManager'
import { useActionItemStatuses } from '@/app/common/hooks/action-item-management/useActionItemStatuses'
import { Status } from '@/app/common/models/action'
import { AzureFunctionClient } from '@/app/common/clients/azure-function-client'
import { ActionStatusExternalIdClearEnum, RestrictedStatus } from '@/app/common/enums/ActionItemStatusEnum'
import { ActionsByEventModalFilter } from './ActionsByEventModalFilter'
import { GenerateStatusChip } from '../StatusComponet'
import { translate } from '@/app/common/utils/generate-translate'
import { ActionDetailItem, ResponseData } from '@/app/common/models/action-detail'
import { FilterInfoProps } from '../ActionTable/HomeFilter'
import { useDebounceFunction } from '@/app/common/hooks/general-functions/useDebounce'
import { useSnackbar } from '@/app/common/contexts/SnackbarContext'
import { determineActiveFilterCount } from '@/app/common/utils/active-filter-count'

interface ActionsByEventModalProps {
    eventId: string
    onClose: () => void
    onActionsCancel: () => void
    onActionView: (value: string) => void
    onEventCancel: () => void
    selectionState: SelectionState
    setSelectionState: (value: SetStateAction<SelectionState>) => void
    isSelectionModal: boolean
    filterInfo: FilterInfoProps
    setFilterInfo: (value: FilterInfoProps) => void
    totalActionItemsTable: number
    setTotalActionItemsTable: (value: number) => void
    isCancellationInProgress?: boolean
}

export function ActionsByEventModal({
    eventId,
    onClose,
    onActionsCancel,
    onActionView,
    onEventCancel,
    selectionState,
    setSelectionState,
    isSelectionModal,
    filterInfo,
    setFilterInfo,
    totalActionItemsTable,
    setTotalActionItemsTable,
    isCancellationInProgress,
}: ActionsByEventModalProps) {
    const theme = useTheme()
    const isMobile = useMediaQuery(theme.breakpoints.down('sm'))

    const client = new AzureFunctionClient()

    const { userInfo: activeUser } = useContext<UserManagementContextState>(UserManagementContext)

    const { allStatus } = useActionItemStatuses({})
    const { showSnackbar } = useSnackbar()

    const statusList: Status[] = useMemo(() => {
        const restrictedStatus = Object.values(RestrictedStatus) as string[]
        return allStatus.filter((status) => status.name && !restrictedStatus.includes(status.name))
    }, [allStatus])

    const [currentPage, setCurrentPage] = useState<number>(0)
    const [memoryPage, setMemoryPage] = useState(0)
    const [totalPages, setTotalPages] = useState<number>(1)

    const [actionResponse, setActionResponse] = useState<ResponseData>()
    const [actionItems, setActionItems] = useState<ActionDetailItem[]>([])
    const [visibleActionItems, setVisibleActionItems] = useState<ActionDetailItem[]>([])

    const [loadingTable, setLoadingTable] = useState<boolean>(true)

    const headCells: GridColDef[] = useMemo(() => {
        const { isCheckboxChecked, checkedActionIds, uncheckedActionIds } = selectionState ?? {}

        const columns: GridColDef[] = [
            {
                field: 'select',
                headerName: translate('table.headers.select'),
                sortable: false,
                filterable: false,
                width: 100,
                renderCell: (params) => (
                    <Checkbox
                        checked={
                            (isCheckboxChecked && !uncheckedActionIds?.includes(params.row.id)) ||
                            (!isCheckboxChecked && checkedActionIds?.includes(params.row.id))
                        }
                        onChange={() => handleSelectRow(params.row.id)}
                    />
                ),
                renderHeader: () => (
                    <Checkbox
                        checked={
                            (!isCheckboxChecked && checkedActionIds?.length > 0) ||
                            (isCheckboxChecked && uncheckedActionIds?.length !== totalActionItemsTable)
                        }
                        onChange={handleSelectAll}
                        indeterminate={
                            (isCheckboxChecked &&
                                uncheckedActionIds?.length > 0 &&
                                uncheckedActionIds?.length !== totalActionItemsTable) ||
                            (!isCheckboxChecked &&
                                checkedActionIds?.length > 0 &&
                                checkedActionIds?.length !== totalActionItemsTable)
                        }
                    />
                ),
                hideable: false,
            },
            {
                field: 'actionsNumber',
                headerName: translate('table.headers.id'),
                sortable: false,
                filterable: false,
                width: 250,
                flex: 1,
                renderCell: (params) => <NoTranslate>{params.value}</NoTranslate>,
                renderHeader: () => (
                    <span data-test="cancel-event-action-modal-id_table_sort" data-origin="aim">
                        {translate('table.headers.id')}
                    </span>
                ),
            },
            {
                field: 'title',
                headerName: translate('table.headers.title'),
                sortable: false,
                filterable: false,
                width: 250,
                flex: 1,
                renderCell: (params) => <NoTranslate>{params.row.title}</NoTranslate>,
                renderHeader: () => (
                    <span data-test="cancel-event-action-modal-title_table_sort" data-origin="aim">
                        {translate('table.headers.title')}
                    </span>
                ),
            },
            {
                field: 'status',
                headerName: translate('table.headers.status'),
                sortable: false,
                filterable: false,
                width: 150,
                flex: 1,
                renderCell: (params) => (
                    <Box
                        sx={{
                            display: 'flex',
                            width: '100%',
                            height: '100%',
                            alignItems: 'center',
                        }}
                        data-test="new_action_item_create_new_event_no_table-status_content"
                        data-origin="aim"
                    >
                        <NoTranslate>
                            <GenerateStatusChip statusId={params.value ?? ActionStatusExternalIdClearEnum.Assigned} />
                        </NoTranslate>
                    </Box>
                ),
                renderHeader: () => (
                    <span data-test="cancel-event-action-modal-status_table_sort" data-origin="aim">
                        {translate('table.headers.status')}
                    </span>
                ),
            },
            {
                field: 'act',
                headerName: translate('table.headers.actions'),
                sortable: false,
                filterable: false,
                width: 100,
                renderCell: (params) => (
                    <IconButton color="primary">
                        <MatIcon icon="visibility" onClick={() => onActionView(params.row.id)} />
                    </IconButton>
                ),
                renderHeader: () => (
                    <span data-test="cancel-event-action-modal-actions_table_sort" data-origin="aim">
                        {translate('table.headers.actions')}
                    </span>
                ),
            },
        ]

        return isSelectionModal ? columns : columns.slice(1, -1)
    }, [translate, selectionState, totalActionItemsTable])

    const tableRows: GridRowsProp = useMemo(() => {
        function convertActionItemDataToRows(items: any[]) {
            return items.map((item) => ({
                id: `${item.externalId}`,
                actionsNumber: `${item.externalId}`,
                title: `${item.title}`,
                status: item?.status ?? ActionStatusExternalIdClearEnum.Assigned,
            }))
        }

        const actionsRows =
            visibleActionItems != null && visibleActionItems.length > 0
                ? convertActionItemDataToRows(visibleActionItems)
                : []
        setLoadingTable(false)
        return actionsRows
    }, [visibleActionItems])

    const activeFiltersCount = useMemo(() => {
        const filters = {
            externalIdPrefix: filterInfo.externalIdPrefix,
            statusExternalIds: filterInfo.statusExternalIds,
            titlePrefix: filterInfo.titlePrefix,
        }

        const count = determineActiveFilterCount(filters)
        return count
    }, [filterInfo])

    const fetchActions = useCallback(async () => {
        try {
            const { isCheckboxChecked, uncheckedActionIds, checkedActionIds } = selectionState

            const result: ResponseData = await client.getActionsHome({
                ...filterInfo,
                activeUserEmail: activeUser.email,
                sourceIds: eventId ? [eventId] : undefined,
                externalIds: isSelectionModal ? [] : isCheckboxChecked ? uncheckedActionIds : checkedActionIds,
                notInExternalIds: isSelectionModal ? false : isCheckboxChecked,
            })

            const resultData = result.data as ActionDetailItem[]
            const filteredResultData = filterInfo.cursor
                ? resultData.filter(
                      (item) => !actionItems.some((existingItem) => existingItem.externalId === item.externalId)
                  )
                : resultData

            setActionResponse(result)

            const allItems = filterInfo.cursor ? [...actionItems, ...filteredResultData] : filteredResultData
            setActionItems(allItems)
            setVisibleActionItems(filteredResultData)

            if (isSelectionModal) {
                setTotalActionItemsTable(result.totalActions)
            }
        } catch (err) {
            showSnackbar(`${translate('alerts.unexpectedErrorOcurred')}`, 'error', 'cancel-event-action-modal-table')
        } finally {
            setLoadingTable(false)
        }
    }, [filterInfo])

    const debouncedFetchActions = useCallback(useDebounceFunction(fetchActions, 800), [fetchActions])

    useEffect(() => {
        if (filterInfo !== undefined) {
            if (loadingTable) return

            setLoadingTable(true)
            debouncedFetchActions()
        }
    }, [debouncedFetchActions])

    const handleSelectRow = useCallback((actionId: string) => {
        setSelectionState((prevValue: SelectionState) => {
            if (!prevValue) return prevValue

            const { isCheckboxChecked, checkedActionIds, uncheckedActionIds } = prevValue

            if (isCheckboxChecked) {
                const updatedUnchecked = uncheckedActionIds.includes(actionId)
                    ? uncheckedActionIds.filter((id) => id !== actionId)
                    : [...uncheckedActionIds, actionId]

                return {
                    ...prevValue,
                    uncheckedActionIds: updatedUnchecked,
                }
            } else {
                const updatedChecked = checkedActionIds.includes(actionId)
                    ? checkedActionIds.filter((id) => id !== actionId)
                    : [...checkedActionIds, actionId]

                return {
                    ...prevValue,
                    checkedActionIds: updatedChecked,
                }
            }
        })
    }, [])

    const handleSelectAll = () => {
        setSelectionState((prevValue: SelectionState) => ({
            isCheckboxChecked: !prevValue?.isCheckboxChecked,
            checkedActionIds: [],
            uncheckedActionIds: [],
        }))
    }

    const resetAllPages = () => {
        setMemoryPage(0)
        setCurrentPage(0)
        setTotalPages(1)
    }

    const resetSelectionState = () => {
        setSelectionState({
            isCheckboxChecked: true,
            checkedActionIds: [],
            uncheckedActionIds: [],
        })
    }

    const customPopoverContent = useMemo(() => {
        return (
            <ActionsByEventModalFilter
                onFilter={(value: FilterInfoProps) => {
                    resetSelectionState()
                    resetAllPages()
                    setFilterInfo(value)
                }}
                statusForFilter={statusList}
                defaultFilter={filterInfo}
            />
        )
    }, [filterInfo, statusList])

    useEffect(() => {
        const pages = Math.ceil(actionItems.length / filterInfo.pageSize)
        setTotalPages(actionResponse?.hasNextPage ? pages + 1 : pages)
    }, [actionResponse])

    return (
        <ModalWrapper
            title={
                isSelectionModal
                    ? translate('requestModal.selectActionsToCancel')
                    : translate('requestModal.cancelEventAndActionsQuestion')
            }
            openModal={true}
            closeModal={() => {
                resetAllPages()
                onClose()
            }}
            content={
                <Box
                    sx={{
                        width: isMobile ? 'auto' : '80vw',
                        minWidth: isMobile ? 'auto' : '80vw',
                        height: isMobile ? 'auto' : '75vh',
                        maxHeight: '600px',
                        display: 'flex',
                        flexDirection: 'column',
                        position: 'relative',
                        overflow: 'hidden',
                        padding: '0 25px 60px 25px',
                    }}
                >
                    {isCancellationInProgress ? (
                        <Box
                            sx={{
                                display: 'flex',
                                justifyContent: 'center',
                                alignItems: 'center',
                                height: '100%',
                            }}
                        >
                            <ClnCircularProgress size={40} value={0} />
                        </Box>
                    ) : (
                        <>
                            <Typography sx={{ fontWeight: '700', fontSize: '16px', color: 'text.secondary' }}>
                                {isSelectionModal
                                    ? translate('table.headers.actions').toUpperCase()
                                    : `${translate('requestModal.selectedActions')}:`}
                            </Typography>
                            <Box
                                sx={{
                                    flex: '1',
                                    overflowY: 'auto',
                                    overflowX: 'auto',
                                    mb: 2,
                                }}
                            >
                                <DataGridTable
                                    id="cancel-event-action-modal-table"
                                    isLoading={loadingTable}
                                    rows={tableRows}
                                    initialColumnDefs={headCells}
                                    rowsPerPageOptions={ROWS_PER_PAGE_OPTIONS}
                                    currentPage={currentPage}
                                    setCurrentPage={(value: number) => {
                                        setCurrentPage(value)
                                        if (value > memoryPage) {
                                            setMemoryPage(value)
                                            if (actionResponse?.cursor) {
                                                const newFilterInfo = { ...filterInfo, cursor: actionResponse.cursor }
                                                setFilterInfo(newFilterInfo)
                                            }
                                        } else {
                                            setVisibleActionItems(
                                                actionItems.slice(
                                                    value * filterInfo.pageSize,
                                                    value * filterInfo.pageSize + filterInfo.pageSize
                                                )
                                            )
                                        }
                                    }}
                                    totalPages={totalPages}
                                    rowsPerPage={filterInfo.pageSize}
                                    setRowsPerPage={(value: number) => {
                                        resetAllPages()
                                        const newFilterInfo = {
                                            ...filterInfo,
                                            pageSize: value,
                                            cursor: undefined,
                                        }
                                        setFilterInfo(newFilterInfo)
                                    }}
                                    paginationMode={'server'}
                                    showOptionsBar={isSelectionModal}
                                    showColumnSelector={false}
                                    customPopoverContent={customPopoverContent}
                                    searchHelpMessage={translate('table.search.searchForTitle')}
                                    searchInfoMessage={translate('requestModal.applyFilterOrSearchSubtitle')}
                                    onSearchSubmit={(value) => {
                                        resetSelectionState()
                                        resetAllPages()
                                        const newFilterInfo = {
                                            ...filterInfo,
                                            search: value,
                                            cursor: undefined,
                                        }
                                        setFilterInfo(newFilterInfo)
                                    }}
                                    activeFiltersCount={activeFiltersCount}
                                />
                            </Box>
                            <Box
                                sx={{
                                    display: 'flex',
                                    justifyContent: 'flex-end',
                                    gap: '10px',
                                    position: 'absolute',
                                    bottom: '0',
                                    left: '0',
                                    right: '0',
                                    padding: '16px 25px',
                                }}
                            >
                                <ClnButton
                                    variant={'text'}
                                    label={translate('requestModal.close')}
                                    onClick={() => {
                                        resetAllPages()
                                        onClose()
                                    }}
                                />
                                <ClnButton
                                    variant={'outlined'}
                                    label={translate('requestModal.onlyCancelEvent')}
                                    onClick={onEventCancel}
                                    color="error"
                                />
                                <ClnButton
                                    variant={'contained'}
                                    label={translate('requestModal.cancelSelectedActions')}
                                    disabled={
                                        !(
                                            (!selectionState.isCheckboxChecked &&
                                                selectionState.checkedActionIds?.length > 0) ||
                                            (selectionState.isCheckboxChecked &&
                                                selectionState.uncheckedActionIds?.length !== totalActionItemsTable)
                                        )
                                    }
                                    onClick={onActionsCancel}
                                    color={
                                        (!selectionState.isCheckboxChecked &&
                                            selectionState.checkedActionIds?.length > 0) ||
                                        (selectionState.isCheckboxChecked &&
                                            selectionState.uncheckedActionIds?.length !== totalActionItemsTable)
                                            ? 'error'
                                            : undefined
                                    }
                                />
                            </Box>
                        </>
                    )}
                </Box>
            }
        />
    )
}
