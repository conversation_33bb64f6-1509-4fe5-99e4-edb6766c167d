import { gql, useMutation } from '@apollo/client'
import { TemplateConfiguration } from '../../models/action'

const UPSERT_TEMPLATE_CONFIGURATION_MUTATION = gql`
    mutation UpsertTemplateConfiguration($templateConfigurations: [_UpsertTemplateConfiguration!]!) {
        upsertTemplateConfiguration(items: $templateConfigurations) {
            space
            externalId
        }
    }
`

export const useUpsertTemplateConfiguration = () => {
    return useMutation<TemplateConfiguration>(UPSERT_TEMPLATE_CONFIGURATION_MUTATION)
}
