import { ExternalEntity } from '..'
import { ReportingSite } from '../asset-hierarchy/reporting-site'
import { ReportingUnit } from '../asset-hierarchy/reporting-unit'
import { User } from './user'

export interface Team extends ExternalEntity {
    name: string
    description?: string
    reportingSite?: ReportingSite
    reportingUnit?: ReportingUnit
    members?: User[]
    teamLeader?: User
    isActive?: boolean
}
