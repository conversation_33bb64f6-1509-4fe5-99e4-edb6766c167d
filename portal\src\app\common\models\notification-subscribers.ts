import { ApplicationGroup } from './application-group'
import { NotificationRole } from './notification-role'
import { UserComplement } from './common/user-management/user-complement'
import { NotificationTeam } from './notification-team'
import { NotificationUnit } from './notification-unit'

export class NotificationSubscribers {
    users!: UserComplement[]
    roles!: NotificationRole[]
    teams!: NotificationTeam[]
    units!: NotificationUnit[]
    applicationGroups!: ApplicationGroup[]

    constructor() {
        this.users = []
        this.roles = []
        this.teams = []
        this.units = []
        this.applicationGroups = []
    }
}
