import { Grid } from '@mui/material'
import { FieldItem } from './FieldItem'
import { ModalDataColumn } from './modalData'
import { ColumnTitle, FieldsWrapper } from './styles'

interface ColumnProps extends ModalDataColumn {
    columnCount?: number
}

export function Column({ title, fields, columnCount = 3 }: ColumnProps) {
    const mdSize = 12 / columnCount

    return (
        <Grid item xs={12} sm={6} md={mdSize}>
            {title && <ColumnTitle>{title}</ColumnTitle>}
            <FieldsWrapper>
                {fields.map((field, index) => (
                    <FieldItem key={`action-modal-field-${index.toString()}`} label={field.label} value={field.value} />
                ))}
            </FieldsWrapper>
        </Grid>
    )
}
