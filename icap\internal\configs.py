from typing import Literal

from pydantic import BaseModel, Field
from pydantic_settings import BaseSettings, SettingsConfigDict


class Settings(BaseSettings):
    model_config = SettingsConfigDict(env_file=(".env", ".env.local"), extra="ignore")

    client_name: str = ""
    project: str = ""
    base_uri: str = ""

    client_id: str = ""
    tenant_id: str = ""
    secret: str = ""
    scopes: str = ""

    @property
    def token_uri(self):
        result = "https://login.microsoftonline.com/"
        result += f"{self.tenant_id}/"
        result += "oauth2/v2.0/token"

        return result

    def get_scopes(self) -> list[str]:
        return self.scopes.split(",")


class WorkbookConfig(BaseModel):
    name: str
    destination_table: str | None = None
    key: str = "key"
    mode: Literal["upsert", "override"] = "upsert"
    struct_columns: list[str] = Field(default_factory=list)


class SheetConfig(BaseModel):
    file: str
    workbooks: list[WorkbookConfig]


class ReferenceDataConfig(BaseModel):
    database: str
    sheet: SheetConfig
    ensure_exists: list[str]


class FunctionConfig(BaseModel):
    name: str
    function_path: str
    cron: str | None = Field(default=None)
    ignore_folders: list[str] = Field(default_factory=list)
    ignore_files: list[str] = Field(default_factory=list)


class Config(BaseModel):
    version: float = 1.0

    env_settings: Settings = Field(default_factory=lambda: Settings())
    reference_data: list[ReferenceDataConfig] = Field(default_factory=list)
    functions: list[FunctionConfig] = Field(default_factory=list)
    webhook_uri: str
