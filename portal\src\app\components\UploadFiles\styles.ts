import { CSSObject } from '@mui/material'
import { Box, styled } from '@mui/system'

export const clearAll: CSSObject = {
    minWidth: '120px',
    alignSelf: 'start !important',
    textTransform: 'uppercase !important' as 'uppercase',
}

export const titleUpload: CSSObject = {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
}

export const SaveIcon = styled('div')(({ theme }) => ({
    backgroundColor: theme.palette.mode === 'dark' ? theme.palette.grey[700] : theme.palette.grey[100],
    color: theme.palette.mode === 'dark' ? theme.palette.grey[100] : theme.palette.grey[700],
    alignItems: 'center',
    padding: '1px',
    display: 'flex',
    border: '1px solid',
    borderColor: theme.palette.grey[400],
    justifyContent: 'center',
    borderRadius: '5px',
}))

export const TextUploadDark: CSSObject = {
    fontSize: '14px',
    paddingLeft: '8px',
    textAlign: 'left',
    fontWeight: '500',
    color: 'primary.dark',
}

export const TextUpload: CSSObject = {
    fontSize: '14px',
    paddingLeft: '5px',
    textAlign: 'left',
    fontWeight: '500',
    color: 'gray.400',
}

export const BoxPointer = styled(Box)({
    display: 'flex',
    alignItems: 'center',
    color: 'gray.400',
    cursor: 'pointer',
})

export const NoData = styled('div')({
    height: '100%',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
})
