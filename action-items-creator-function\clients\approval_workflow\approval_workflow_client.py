from cognite.client.data_classes.data_modeling.query import (
    EdgeResultSetExpression,
    NodeResultSetExpression,
    Query,
    Select,
)
from cognite.client.data_classes.filters import And, Equals, HasD<PERSON>, In, SpaceFilter

from clients.approval_workflow.requests import GetApprovalWorkflowRequest
from clients.core.constants import (
    APPROVAL_WORKFLOW_STEP_TO_USERS,
    LIMIT,
    ViewEnum,
)
from clients.core.models import Node, ServiceParams


class ApprovalWorkflowClient:
    """Client responsible for handling approval workflow and related views in the data model."""

    def __init__(
        self,
        params: ServiceParams,
    ) -> None:
        """Initialize the ApprovalWorkflowClient with the required services and configuration."""
        self._cognite_client = params.cognite_client
        self._data_model_id = params.data_model.as_id()

        views = {view.external_id: view for view in params.data_model.views}
        self._approval_workflow_view = views[ViewEnum.APPROVAL_WORKFLOW]
        self._approval_workflow_step_view = views[ViewEnum.APPROVAL_WORKFLOW_STEP]

    def get_approval_workflows(self, request: GetApprovalWorkflowRequest) -> list[Node]:
        """
        Retrieve a list of approval workflow nodes based on the provided request filters.

        Args:
            request (GetApprovalWorkflowRequest): Request object containing filters for querying approval workflows.

        Returns:
            list[Node]: A list of approval workflow nodes matching the request filters.

        """
        with_ = self._make_with_clause(request)

        node_ids = (
            self._cognite_client.data_modeling.instances.query(
                Query(
                    with_=with_,
                    select={"approval_workflow": Select()},
                ),
            )
            .get_nodes("approval_workflow")
            .as_ids()
        )

        return [Node.model_validate(node_id) for node_id in node_ids]

    def _make_with_clause(self, request: GetApprovalWorkflowRequest) -> dict:
        description_property = self._approval_workflow_step_view.as_property_ref(
            "description",
        )
        status_property = self._approval_workflow_step_view.as_property_ref("status")
        approval_workflow_property = self._approval_workflow_step_view.as_property_ref(
            "approvalWorkflow",
        )

        return {
            "approval_workflow_step_by_user": EdgeResultSetExpression(
                filter=And(
                    Equals(
                        ["edge", "type"],
                        APPROVAL_WORKFLOW_STEP_TO_USERS,
                    ),
                    SpaceFilter(
                        request.get_filter_spaces(),
                        instance_type="edge",
                    ),
                    In(["edge", "endNode"], request.get_users()),
                ),
                direction="inwards",
                node_filter=And(
                    In(
                        status_property,
                        request.get_status(),
                    ),
                    In(
                        description_property,
                        request.approval_workflow_ste_descriptions,
                    ),
                    SpaceFilter(
                        request.get_filter_spaces(),
                    ),
                ),
                max_distance=1,
                limit=LIMIT,
            ),
            "approval_workflow_step": NodeResultSetExpression(
                from_="approval_workflow_step_by_user",
                filter=And(
                    HasData(
                        views=[self._approval_workflow_step_view],
                    ),
                    SpaceFilter(
                        request.get_filter_spaces(),
                    ),
                ),
                limit=LIMIT,
            ),
            "approval_workflow": NodeResultSetExpression(
                from_="approval_workflow_step",
                filter=And(
                    HasData(views=[self._approval_workflow_view]),
                    SpaceFilter(
                        request.get_filter_spaces(),
                    ),
                ),
                through=approval_workflow_property,
                limit=LIMIT,
            ),
        }
