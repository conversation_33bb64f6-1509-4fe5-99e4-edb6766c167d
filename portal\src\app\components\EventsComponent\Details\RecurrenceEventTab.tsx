import { Box } from '@mui/material'
import { useContext, useEffect, useMemo, useState } from 'react'
import {
    NoTranslate,
    TranslationContext,
    TranslationContextState,
    UserManagementContext,
    UserManagementContextState,
} from '@celanese/celanese-ui'
import { DataGridTable } from '@/app/components/PaginatedTable/DataGridTable'
import { GridColDef, GridColumnVisibilityModel, GridRowsProp } from '@mui/x-data-grid-pro'
import { useAuthGuard } from '@/app/common/hooks/useAuthGuard'
import { FilterOptionsRecurring, FilterSelectedRecurring } from '@/app/common/models/admin-settings/filter-recurring'
import {
    ActionByRecurrenceQueryRequest,
    useActionByRecurrence,
} from '@/app/common/hooks/action-item-management/useActionByRecurrence'
import { SourceEvent } from '@/app/common/models/source-event'
import { ROWS_PER_PAGE_OPTIONS } from '@/app/common/utils'
import { RecurringTabFilter } from '@/app/admin-settings/Recurring/RecurringTabFilter'
import { useReportingUnits } from '@/app/common/hooks/asset-hierarchy/useReportingUnits'
import { useActionItemCategories } from '@/app/common/hooks/action-item-management/useActionItemCategories'
import { useActionItemStatuses } from '@/app/common/hooks/action-item-management/useActionItemStatuses'
import GenericFieldTitle from '@/app/components/FieldsComponent/GenericFieldTitle'
import dayjs from 'dayjs'
import ActionFormDetailsEvent from './ActionFormDetailsEvent'
import { ExternalEntityWithName } from '@/app/common/models/common'
import { ActionButton } from '@celanese/ui-lib'
import { GenerateStatusChip } from '../../StatusComponet'
import { AllActionStatusExternalIdEnum } from '@/app/common/enums/ActionItemStatusEnum'
import { translate } from '@/app/common/utils/generate-translate'
import { determineActiveFilterCount } from '@/app/common/utils/active-filter-count'
import { transformOptions, transformStringOptions } from '@/app/common/utils/transform-options-for-filter'
import MessageModal from '../../ModalComponent/Modal/MessageModal'

type RecurrenceTabProps = {
    id: string
    sourceEvent?: SourceEvent
    showForm: boolean
    recurrenceTypes: ExternalEntityWithName[]
    setShowForm: (value: boolean) => void
}

export default function RecurrenceEventTab({
    id,
    sourceEvent,
    recurrenceTypes,
    showForm,
    setShowForm,
}: RecurrenceTabProps) {
    const { locale } = useContext<TranslationContextState>(TranslationContext)

    const { userInfo: activeUser } = useContext<UserManagementContextState>(UserManagementContext)
    const { checkPermissionsFromComponents } = useAuthGuard()

    const [loadingTable, setLoadingTable] = useState(false)
    const [openRecurrenceModal, setOpenRecurrenceModal] = useState<boolean>(false)

    const [currentPage, setCurrentPage] = useState(0)
    const [rowsPerPage, setRowsPerPage] = useState(10)

    const { units } = useReportingUnits({ siteId: sourceEvent?.reportingSite?.externalId! })
    const { categories } = useActionItemCategories()
    const { allStatus } = useActionItemStatuses({})

    const storedFilterInfo = sessionStorage.getItem(`event-details-filterInfo-recurrence-tab`)
    const parsedFilterInfo = storedFilterInfo ? JSON.parse(storedFilterInfo) : {}

    const [filterActions, setFilterActions] = useState<FilterSelectedRecurring | undefined>({ ...parsedFilterInfo })

    const {
        actions: actionsItens,
        refetchActions,
        loading,
    } = useActionByRecurrence(
        useMemo<ActionByRecurrenceQueryRequest>(() => {
            const queryParams: ActionByRecurrenceQueryRequest = {
                unit: filterActions?.unit,
                category: filterActions?.category,
                status: filterActions?.status,
                recurrenceType: filterActions?.frequency,
                start: filterActions?.startDate
                    ? dayjs(filterActions?.startDate)
                          .startOf('day')
                          .utc()
                          .format('YYYY-MM-DD')
                    : null,
                noEndDate:
                    filterActions?.noEndDate === 'Yes' ? false : filterActions?.noEndDate === 'No' ? true : undefined,
                sourceEventId: id,
            }
            setCurrentPage(0)
            return queryParams
        }, [filterActions])
    )

    const actionsRecurring = useMemo(() => {
        setLoadingTable(true)
        const recurring = actionsItens.map((action) => {
            return {
                externalId: `${action.externalId}`,
                recurringExternalId: `${action.recurrenceInstance?.externalId}`,
                title: `${action.title}`,
                unit: `${action.reportingUnit?.description}`,
                category: `${action.category?.name}`,
                startDate: `${dayjs(action.recurrenceInstance?.startDate).format('MM/DD/YYYY')}`,
                frequency: `${action.recurrenceInstance?.recurrenceType?.name}`,
                owner: `${action.owner?.user.lastName}, ${action.owner?.user.firstName}`,
                noEndDate: action.recurrenceInstance?.endDate
                    ? `${dayjs(action.recurrenceInstance?.endDate).format('MM/DD/YYYY')}`
                    : '-',
                status: `${action.currentStatus?.externalId ?? ''}`,
                action: action,
            }
        })
        setLoadingTable(false)
        return recurring
    }, [actionsItens])

    const [search, setSearch] = useState<string>('')
    const filteredData = useMemo(() => {
        const filteredData = actionsRecurring.filter((sc) => {
            if (filterActions) {
                if (filterActions.owner?.length > 0) {
                    if (!filterActions.owner.includes(sc.owner)) {
                        return false
                    }
                }
            }
            if (search && search.length > 0) {
                const searchData = search.toLowerCase()
                const recurringId = sc.externalId?.toLowerCase()
                const recurringTitle = sc.title?.toLowerCase()
                const recurringUnit = sc.unit?.toLowerCase()
                const recurringCategory = sc.category?.toLowerCase()
                const recurringStartDate = sc.startDate
                const recurringFrequency = sc.frequency.toLowerCase()
                const recurringStatus = sc.status.toLowerCase()
                const recurringOwner = sc.owner.toLowerCase()
                const recurringNoEndDate = sc.noEndDate.toLowerCase()

                if (
                    !recurringId?.includes(searchData) &&
                    !recurringTitle?.includes(searchData) &&
                    !recurringUnit?.includes(searchData) &&
                    !recurringCategory?.includes(searchData) &&
                    !recurringStartDate?.includes(searchData) &&
                    !recurringFrequency?.includes(searchData) &&
                    !recurringOwner?.includes(searchData) &&
                    !recurringNoEndDate?.includes(searchData) &&
                    !recurringStatus?.includes(searchData)
                ) {
                    return false
                }
            }
            return true
        })
        return filteredData
    }, [actionsRecurring, filterActions, search])

    const [filterOptions, setFilterOptions] = useState<FilterOptionsRecurring>({
        unit: [],
        owner: [],
        updateBy: [],
        category: [],
        startDate: null,
        frequency: [],
        status: [],
        noEndDate: [],
    })

    const applyFilters = async (filters: any) => {
        setFilterActions(filters)
        resetPageProps()
    }

    const buildFilterOptions = (): void => {
        const options: FilterOptionsRecurring = {
            unit: [],
            owner: [],
            updateBy: [],
            category: [],
            startDate: null,
            frequency: [],
            status: [],
            noEndDate: [],
        }
        options.category = transformOptions(categories, 'name', 'name', 'category')
        options.unit = transformOptions(units)

        const statusList = ['Active', 'Inactive']
        options.status = transformOptions(
            allStatus.filter((x) => statusList.includes(x?.name ?? '')),
            'name',
            'name',
            'status'
        )
        options.frequency = transformOptions(recurrenceTypes, 'name', 'name', 'frequency')

        const owner = Array.from(new Set(actionsRecurring.map((x) => x.owner).filter(Boolean)))
        options.owner = transformStringOptions(owner)

        options.noEndDate = transformStringOptions(['Yes', 'No'])

        setFilterOptions(options)
    }

    const headCells: GridColDef[] = useMemo(
        () => [
            {
                field: 'externalId',
                headerName: translate('source-event.recurringTab.id'),
                filterable: false,
                flex: 1,
                renderHeader: () => (
                    <span data-test="recurring-id_table_sort" data-origin="aim">
                        {translate('source-event.recurringTab.id')}
                    </span>
                ),
            },
            {
                field: 'title',
                headerName: translate('source-event.recurringTab.title'),
                filterable: false,
                flex: 1,
                renderHeader: () => (
                    <span data-test="recurring-title_table_sort" data-origin="aim">
                        {translate('source-event.recurringTab.title')}
                    </span>
                ),
            },
            {
                field: 'status',
                headerName: translate('source-event.recurringTab.status'),
                filterable: false,
                flex: 1,
                renderCell: (params) => (
                    <Box
                        sx={{
                            display: 'flex',
                            width: '100%',
                            height: '100%',
                            alignItems: 'center',
                        }}
                        data-test="evet-details-recurring-tab-status_content"
                        data-origin="aim"
                    >
                        <NoTranslate>
                            <GenerateStatusChip
                                statusId={params.value != '' ? params.value : AllActionStatusExternalIdEnum.Inactive}
                            />
                        </NoTranslate>
                    </Box>
                ),
                renderHeader: () => (
                    <span data-test="recurring-status_table_sort" data-origin="aim">
                        {translate('source-event.recurringTab.status')}
                    </span>
                ),
            },
            {
                field: 'unit',
                headerName: translate('source-event.recurringTab.unit'),
                filterable: false,
                flex: 1,
                renderHeader: () => (
                    <span data-test="recurring-unit_table_sort" data-origin="aim">
                        {translate('source-event.recurringTab.unit')}
                    </span>
                ),
            },
            {
                field: 'category',
                headerName: translate('source-event.recurringTab.category'),
                filterable: false,
                flex: 1,
                renderHeader: () => (
                    <span data-test="recurring-category_table_sort" data-origin="aim">
                        {translate('source-event.recurringTab.category')}
                    </span>
                ),
            },
            {
                field: 'startDate',
                headerName: translate('source-event.recurringTab.startDate'),
                filterable: false,
                flex: 1,
                renderHeader: () => (
                    <span data-test="recurring-startDate_table_sort" data-origin="aim">
                        {translate('source-event.recurringTab.startDate')}
                    </span>
                ),
            },
            {
                field: 'frequency',
                headerName: translate('source-event.recurringTab.frequency'),
                filterable: false,
                flex: 1,
                renderHeader: () => (
                    <span data-test="recurring-frequency_table_sort" data-origin="aim">
                        {translate('source-event.recurringTab.frequency')}
                    </span>
                ),
            },
            {
                field: 'owner',
                headerName: translate('source-event.recurringTab.owner'),
                filterable: false,
                flex: 1,
                renderHeader: () => (
                    <span data-test="recurring-owner_table_sort" data-origin="aim">
                        {translate('source-event.recurringTab.owner')}
                    </span>
                ),
            },
            {
                field: 'noEndDate',
                headerName: translate('source-event.recurringTab.endDate'),
                filterable: false,
                flex: 1,
                renderHeader: () => (
                    <span data-test="recurring-endDate_table_sort" data-origin="aim">
                        {translate('source-event.recurringTab.endDate')}
                    </span>
                ),
            },
        ],
        [translate, filteredData]
    )

    const tableRows: GridRowsProp = useMemo(() => {
        function convertActionItemDataToRows(items: any[]) {
            return items.map((item) => ({
                id: `${item.externalId}`,
                externalId: `${item.recurringExternalId}`,
                title: `${item.title}`,
                unit: `${item.unit}`,
                category: `${item.category}`,
                startDate: `${item.startDate}`,
                frequency: `${item.frequency}`,
                owner: `${item.owner}`,
                updateBy: `${item.updateBy}`,
                noEndDate: `${item.noEndDate}`,
                status: `${item.status}`,
            }))
        }

        const actionsRows =
            filteredData != null && filteredData.length > 0 ? convertActionItemDataToRows(filteredData) : []

        return actionsRows
    }, [filteredData])

    const buttons: ActionButton[] = [
        {
            route: '/new-action-item',
            label: translate('table.newRecurringItem'),
            variant: 'contained',
            onClick: () => {
                setShowForm(true)
                localStorage.setItem('isEditForm', 'true')
            },
            disabled: sourceEvent?.isPrivate,
            tooltipTitle: sourceEvent?.isPrivate
                ? 'Currently, it’s not possible to create a recurrence for private'
                : '',
        },
    ].filter((item) => checkPermissionsFromComponents(item.route)) as ActionButton[]

    const customPopoverContent = useMemo(() => {
        return (
            <RecurringTabFilter
                data={filterOptions}
                onSubmit={(filters) => {
                    applyFilters(filters)
                    sessionStorage.setItem(`event-details-filterInfo-recurring-tab`, JSON.stringify(filters))
                }}
                defaultFilter={filterActions}
            />
        )
    }, [translate, filterOptions])

    const initialStateColumnVisibilityModel: GridColumnVisibilityModel = useMemo(() => {
        return {
            externalId: false,
        }
    }, [])

    const resetPageProps = () => {
        setCurrentPage(0)
    }

    useEffect(() => {
        buildFilterOptions()
    }, [locale, categories, actionsRecurring, units, allStatus, recurrenceTypes])

    return (
        <>
            {showForm && activeUser ? (
                <ActionFormDetailsEvent
                    id={id}
                    sourceEvent={sourceEvent}
                    activeUser={activeUser}
                    sourceInformation={undefined}
                    closeForm={setShowForm}
                    setRefatchActions={() => {
                        refetchActions()
                        setShowForm(false)
                        setOpenRecurrenceModal(true)
                    }}
                    isRecurring
                />
            ) : (
                <Box
                    id={'action-item-tab-event-details'}
                    sx={{
                        width: '100%',
                        height: '100%',
                        display: 'grid',
                        alignItems: 'start',
                        gridTemplateColumns: '1fr',
                        marginRight: '10px',
                        marginTop: '1rem',
                    }}
                >
                    <Box sx={{ overflow: 'auto' }}>
                        <Box paddingLeft={1}>
                            <GenericFieldTitle
                                fieldName={translate('source-event.recurringTab.recurring')}
                                isSubHeader
                            />
                        </Box>

                        <DataGridTable
                            id={`event-details-recurrence-tab`}
                            isLoading={loading || loadingTable}
                            initialStateColumnVisibilityModel={initialStateColumnVisibilityModel}
                            initialColumnDefs={headCells}
                            onSearchSubmit={(value: string) => setSearch(value)}
                            customButtons={buttons}
                            rows={tableRows.slice(currentPage * rowsPerPage, currentPage * rowsPerPage + rowsPerPage)}
                            rowsPerPageOptions={ROWS_PER_PAGE_OPTIONS}
                            currentPage={currentPage}
                            rowsPerPage={rowsPerPage}
                            setRowsPerPage={setRowsPerPage}
                            customPopoverContent={customPopoverContent}
                            totalPages={Math.ceil(tableRows.length / rowsPerPage)}
                            setCurrentPage={setCurrentPage}
                            paginationMode="server"
                            activeFiltersCount={determineActiveFilterCount(filterActions)}
                        />
                        <MessageModal
                            open={openRecurrenceModal}
                            handleClose={() => {
                                setOpenRecurrenceModal(false)
                            }}
                            text={translate('requestModal.recurringActionSavedText')}
                            title={translate('requestModal.recurringActionSavedTitle')}
                            isCloseModal
                        />
                    </Box>
                </Box>
            )}
        </>
    )
}
