# Manifest file downloaded from fusion
externalId: tr-AIM-COR-ALL-ICAP-ACT-VIEWER
name: AIM-COR-ALL-ICAP-ACT-VIEWER
query: >-
  SELECT
    concat(
    	'Action.viewUsers-', 
    	aim_action.externalId, '-', 
    	utl_user.user_azure_attribute.externalId
    ) AS externalId,
    aim_action.space,
    node_reference(aim_action.space, aim_action.externalId) AS startNode,
    utl_user.user_azure_attribute AS endNode
  FROM (
    SELECT 
    	key AS action_id, 
    	PPRID AS user_id
    FROM `ICAP-COR`.`AXN-tblActionItem`

    UNION 

    SELECT 
    	key AS action_id, 
    	SecondaryPPRID AS user_id
    FROM `ICAP-COR`.`AXN-tblActionItem`

    UNION

    SELECT 
    	icap_action.key AS action_id, 
    	icap_event.EventAddedByOwner AS user_id
    FROM `ICAP-COR`.`AXN-tblActionItem` icap_action
    INNER JOIN `ICAP-COR`.`EVNT-tblEvent` icap_event 
    	ON icap_event.key = cast(icap_action.EventID AS STRING)

    UNION 

    SELECT
    	icap_action.key AS action_id,
    	icap_event_secondary_owner.OwnerID AS user_id
    FROM `ICAP-COR`.`AXN-tblActionItem` icap_action
    INNER JOIN `ICAP-COR`.`EVNT-tblEventSecondaryOwners` icap_event_secondary_owner
    	ON icap_event_secondary_owner.EventID = icap_action.EventID

    UNION 

    SELECT
    	icap_action.key AS action_id,
    	icap_event_viewer.ViewerID AS user_id
    FROM `ICAP-COR`.`AXN-tblActionItem` icap_action
    INNER JOIN `ICAP-COR`.`EVNT-tblEventViewer` icap_event_viewer
    	ON icap_event_viewer.EventID = icap_action.EventID
  ) icap_viewer

  INNER JOIN cdf_data_models("AIM-COR-ALL-DMD", "ActionItemManagementDOM", "6_0_0", "Action") AS aim_action
    	ON split_part(aim_action.objectType, '-', 2) = icap_viewer.action_id
    	AND startswith(aim_action.objectType, 'ICAP')
  INNER JOIN `AIM-COR`.`ICAP-MAP-User` utl_user
  	ON utl_user.key = cast(icap_viewer.user_id AS STRING)
  WHERE aim_action.isPrivate
  	AND aim_action.viewOnly
destination:
  dataModel:
    space: AIM-COR-ALL-DMD
    externalId: ActionItemManagementDOM
    version: "6_0_0"
    destinationType: Action
    destinationRelationshipFromType: viewUsers
  type: instances
ignoreNullFields: true
shared: true
action: upsert
dataSetExternalId: AIM-COR-ALL-DAT
# Specify credentials separately like this:
# authentication:
#   read:
#     clientId: ${READ_CLIENT_ID}
#     clientSecret: ${READ_CLIENT_SECRET}
#     tokenUrl: ${READ_TOKEN_URL}
#     cdfProjectName: ${READ_CDF_PROJECT_NAME}
#     # Optional: If idP requires providing the scopes
#     scopes:
#       - ${READ_SCOPES}
#     # Optional: If idP requires providing the audience
#     audience: ${READ_CDF_AUDIENCE}
#   write:
#     clientId: ${WRITE_CLIENT_ID}
#     clientSecret: ${WRITE_CLIENT_SECRET}
#     tokenUrl: ${WRITE_TOKEN_URL}
#     cdfProjectName: ${WRITE_CDF_PROJECT_NAME}
#     # Optional: If idP requires providing the scopes
#     scopes:
#       - ${WRITE_SCOPES}
#     # Optional: If idP requires providing the audience
#     audience: ${WRITE_CDF_AUDIENCE}
# Or together like this:
# authentication:
#   clientId: ${CLIENT_ID}
#   clientSecret: ${CLIENT_SECRET}
#   tokenUrl: ${TOKEN_URL}
#   # Optional: If idP requires providing the scopes
#   cdfProjectName: ${CDF_PROJECT_NAME}
#   scopes:
#    - ${SCOPES}
#   # Optional: If idP requires providing the audience
#   audience: ${AUDIENCE}