import { useCallback, useState } from 'react'
import { useCognite } from './useCognite'
import { InstanceTypes } from '../../clients'

export type DeleterHookResult = [DeleterFunction, DeleterState]

export type DeleterFunctionParams = {
    externalIds: string[]
    instanceType?: InstanceTypes
    space?: string
}

export type DeleterFunction = (params: DeleterFunctionParams) => Promise<void>
export type DeleterState = {
    loading: boolean
    called: boolean
    error: any
}

export function useFdmDeleter(): DeleterHookResult {
    const { fdmClient: client } = useCognite()
    const [error, setError] = useState<any | undefined>()
    const [loading, setLoading] = useState<boolean>(false)
    const [called, setCalled] = useState<boolean>(false)

    const deleteFunction = useCallback(
        ({ externalIds, instanceType, space }: DeleterFunctionParams) => {
            setLoading(true)
            setCalled(true)
            return client
                .deleteNodesOrEdges({
                    externalIds,
                    instanceType: instanceType ?? 'node',
                    space,
                })
                .catch((error) => setError(error))
                .finally(() => setLoading(false))
        },
        [client]
    )

    return [deleteFunction, { error, loading, called }]
}
