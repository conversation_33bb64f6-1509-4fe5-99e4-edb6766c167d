import azure.functions as func
from infra.action_item_by_id_retriever_factory import ActionItemByIdRetrieverFactory
import logging
import json as json
from clients.core.constants import APPLICATION_JSON

bp = func.Blueprint()


@bp.function_name(name="GetActionItemByTemplateId")
@bp.route(
    "get-action-item-by-template-id",
    methods=["get"],
    auth_level=func.AuthLevel.ANONYMOUS,
)
async def main(req: func.HttpRequest) -> func.HttpResponse:
    try:
        auth_header = req.headers.get("Authorization")
        if auth_header and auth_header.startswith("Bearer "):
            token_request = auth_header[len("Bearer "):]
        else:
            token_request = auth_header

        action_item_request_param = req.params.get("actionItemRequest", "{}")
        try:
            action_item_request = json.loads(action_item_request_param)
        except json.JSONDecodeError:
            return func.HttpResponse(
                json.dumps({"error": "Invalid JSON format"}),
                status_code=400,
            )

        logging.info("Function GetActionItemByTemplateId started")

        items = await ActionItemByIdRetrieverFactory.retriever(
            override_token=token_request
        ).get_action_item_by_template_id(action_item_request)

        logging.info(f"Finishing execution - Items Retrieved : {len(items)}")

        return func.HttpResponse(
            json.dumps({"items": items}),
            mimetype=APPLICATION_JSON,
            status_code=200,
        )
    except Exception as e:
        logging.error(f"Exception found: {e}", exc_info=True)
        return func.HttpResponse(json.dumps({"error": str(e)}), status_code=500)
