from datetime import date
from typing import (
    Annotated,
    Any,
    Generic,
    Literal,
    Optional,
    TypeVar,
    get_type_hints,
    overload,
)

from industrial_model.queries import (
    NestedQueryParam,
    QueryParam,
)
from pydantic import (
    BaseModel,
    Field,
    ValidationInfo,
    computed_field,
    field_validator,
    model_validator,
)

from clients.category_configuration.models import CategoryConfigurationByFilterResult
from clients.core.constants import (
    ActionStatusEnum,
    DataSpaceEnum,
    KpiNameEnum,
)
from clients.core.models import (
    BaseCamelCaseModel,
    BaseEntity,
    GlobalModel,
    GlobalModelV2,
    Node,
)

from .validators import validate_recurrence


class BaseActionRequestForIndustrialModel(GlobalModelV2):
    external_ids: list[str] | None = None
    not_in_external_ids: bool = False

    due_date_gte: date | None = None
    due_date_lt: date | None = None
    due_date_eq: date | None = None

    update_status_date: list[tuple[Optional[date], Optional[date]]] = []

    only_private: bool | None = None
    permissions_extend: bool = False
    permissions_reassing: bool = False

    extension_approval_site_external_ids: list[str] | None = None
    reassignment_approval_site_external_ids: list[str] | None = None

    status_external_ids: Annotated[
        list[str] | None,
        NestedQueryParam(
            "currentStatus",
            QueryParam(property="externalId", operator="in"),
        ),
    ] = None
    reporting_unit_external_ids: Annotated[
        list[str] | None,
        NestedQueryParam(
            "reportingUnit",
            QueryParam(property="externalId", operator="in"),
        ),
    ] = None

    permissions_reporting_unit_external_ids: list[str] | None = None
    reporting_location_external_ids: Annotated[
        list[str] | None,
        NestedQueryParam(
            "reportingLocation",
            QueryParam(property="externalId", operator="in"),
        ),
    ] = None

    owner_external_id: Annotated[
        list[str] | None,
        NestedQueryParam("owner", QueryParam(property="externalId", operator="in")),
    ] = None
    assigned_to_external_id: Annotated[
        list[str] | None,
        NestedQueryParam(
            "assignedTo",
            QueryParam(property="externalId", operator="in"),
        ),
    ] = None
    category_external_id: Annotated[
        list[str] | None,
        NestedQueryParam("category", QueryParam(property="externalId", operator="in")),
    ] = None
    subcategory_external_id: Annotated[
        list[str] | None,
        NestedQueryParam(
            "subCategory",
            QueryParam(property="externalId", operator="in"),
        ),
    ] = None
    site_specific_category_external_id: list[str] | None = None
    application_external_id: Annotated[
        list[str] | None,
        NestedQueryParam(
            "application",
            QueryParam(property="externalId", operator="in"),
        ),
    ] = None
    approval_workflow_nodes: list[Node] | None = None
    source_ids: Annotated[
        list[str] | None,
        QueryParam(property="sourceId", operator="in"),
    ] = None
    source_type_external_ids: Annotated[
        list[str] | None,
        NestedQueryParam(
            "sourceType",
            QueryParam(property="externalId", operator="in"),
        ),
    ] = None

    external_id_prefix: str | None = None
    title_prefix: str | None = None
    source_info_prefix: str | None = None
    kpi_filter: Optional[KpiNameEnum] = None

    icap_action_id_prefix: Annotated[
        str | None,
        QueryParam(property="objectType", operator="prefix"),
    ] = None

    source_event_title_eq: str | None = None
    source_event_title_prefix: str | None = None

    active_user_email: str | None = None
    active_user: str | None = None
    active_user_roles_ids: list[str] | None = None
    active_user_teams_ids: list[str] | None = None

    category_configurations: list[CategoryConfigurationByFilterResult] | None = None

    def get_filter_spaces(
        self,
        all_sites: bool = False,
        private_space: bool | None = None,
    ) -> list[str]:
        """Return the applicable data spaces based on site and privacy settings."""
        if private_space:
            return [DataSpaceEnum.PRIVATE_SPACE]
        if private_space is not None:
            return self.spaces
        if all_sites:
            return [
                DataSpaceEnum.PRIVATE_SPACE,
                DataSpaceEnum.COR_SPACE,
                *self.spaces,
            ]
        return [DataSpaceEnum.PRIVATE_SPACE, *self.spaces]

    @computed_field
    @property
    def views_private(self) -> list[str]:
        """Return a list of private view identifiers for the active user."""
        if not (self.active_user):
            return ["-"]

        return (
            ([self.active_user] if self.active_user else [])
            + (self.active_user_roles_ids or [])
            + (self.active_user_teams_ids or [])
        )

    @computed_field
    @property
    def approval_workflow_ids(self) -> list[str] | None:
        """Return a list of approval workflows ids"""
        if not self.approval_workflow_nodes:
            return None

        return [node.external_id for node in self.approval_workflow_nodes]


class GetActionRequestForIndustrialModel(BaseActionRequestForIndustrialModel):
    """Request model for retrieving actions with search and sorting options."""

    search: str | None = None
    search_properties: list[str] = ["title", "sourceInformation"]

    sort_by: str = "createdAt"
    direction: Literal["ASC", "DESC"] = "DESC"

    page_size: int = 10
    cursor: str | None = None

    @computed_field
    @property
    def direction_industrial_model(self) -> Literal["ascending", "descending"]:
        """Return a converted direction for industrial model."""
        return "ascending" if self.direction == "ASC" else "descending"


class BaseActionRequest(GlobalModel):
    """Base action request model with filters and permissions."""

    external_ids: list[str] | None = None
    not_in_external_ids: bool = False

    due_date_gte: date | None = None
    due_date_lt: date | None = None
    due_date_eq: date | None = None

    update_status_date: list[tuple[Optional[date], Optional[date]]] = []

    only_private: bool | None = None
    permissions_extend: bool = False
    permissions_reassing: bool = False

    status_external_ids: list[str] | None = None
    reporting_unit_external_ids: list[str] | None = None
    permissions_reporting_unit_external_ids: list[str] | None = None
    reporting_location_external_ids: list[str] | None = None
    extension_approval_site_external_ids: list[str] | None = None
    reassignment_approval_site_external_ids: list[str] | None = None

    owner_external_id: list[str] | None = None
    assigned_to_external_id: list[str] | None = None
    category_external_id: list[str] | None = None
    subcategory_external_id: list[str] | None = None
    site_specific_category_external_id: list[str] | None = None
    application_external_id: list[str] | None = None
    approval_workflow_nodes: list[Node] | None = None
    source_ids: list[str] | None = None
    source_type_external_ids: list[str] | None = None

    external_id_prefix: str | None = None
    title_prefix: str | None = None
    source_info_prefix: str | None = None
    kpi_filter: Optional[KpiNameEnum] = None

    icap_action_id_prefix: str | None = None

    source_event_title_eq: str | None = None
    source_event_title_prefix: str | None = None

    active_user_email: str | None = None
    active_user: str | None = None
    active_user_roles_ids: list[str] | None = None
    active_user_teams_ids: list[str] | None = None

    category_configurations: list[CategoryConfigurationByFilterResult] | None = None

    def get_filter_spaces(
        self,
        all_sites: bool = False,
        private_space: bool | None = None,
    ) -> list[str]:
        """Return the applicable data spaces based on site and privacy settings."""
        if private_space:
            return [DataSpaceEnum.PRIVATE_SPACE]
        if private_space is not None:
            return self.spaces
        if all_sites:
            return [
                DataSpaceEnum.PRIVATE_SPACE,
                DataSpaceEnum.COR_SPACE,
                *self.spaces,
            ]
        return [DataSpaceEnum.PRIVATE_SPACE, *self.spaces]

    @computed_field
    @property
    def views_private(self) -> list[str]:
        """Return a list of private view identifiers for the active user."""
        if not (self.active_user):
            return ["-"]

        return (
            ([self.active_user] if self.active_user else [])
            + (self.active_user_roles_ids or [])
            + (self.active_user_teams_ids or [])
        )

    @computed_field
    @property
    def approval_workflow_ids(self) -> list[str] | None:
        """Return a list of approval workflows ids"""
        if not self.approval_workflow_nodes:
            return None

        return [node.external_id for node in self.approval_workflow_nodes]


class GetActionRequest(BaseActionRequest):
    """Request model for retrieving actions with search and sorting options."""

    search: str | None = None
    search_properties: list[str] = ["title", "sourceInformation"]

    sort_by: str = "createdAt"
    direction: Literal["ASC", "DESC"] = "DESC"

    page_size: int = 10
    cursor: str | None = None


class GetActionByIdRequest(GetActionRequest):
    """Request model for retrieving an action by its external ID."""

    external_id: str
    is_edition: bool = False
    is_assignee_request_process: bool = False

    def __init__(self, **data) -> None:
        """Initialize the request and sets default status filters."""
        super().__init__(**data)
        if self.external_id and (
            self.external_ids is None or self.external_id not in self.external_ids
        ):
            self.external_ids = (self.external_ids or []) + [self.external_id]

        if not self.is_edition:
            self.status_external_ids = [
                status.value
                for status in ActionStatusEnum
                if status != ActionStatusEnum.DELETED
            ]


class UpdateActionApprovalWorkflowRequest(BaseEntity):
    """Request model for updating the approval workflow of an action."""

    active_user_email: str
    reporting_site_external_id: str

    approval_status: Literal["Approved", "Rejected"] | None = None
    verification_status: Literal["Approved", "Rejected"] | None = None

    approver_comment: Optional[str] = Field(None, min_length=1)
    assignee_comment: Optional[str] = Field(None, min_length=1)
    verifier_comment: Optional[str] = Field(None, min_length=1)

    new_files_ids: Optional[list[str]] = Field(default=None)


class UpdateActionAssigneeRequest(BaseEntity):
    """Request model for updating the assignee of an action."""

    active_user_email: str
    reporting_site_external_id: str

    challenge_status: Literal["Approved", "Rejected"] | None = Field(default=None)
    reassign_status: Literal["Approved", "Rejected"] | None = Field(default=None)
    extension_status: Literal["Approved", "Rejected"] | None = Field(default=None)

    approver_email: Optional[list[str]] = Field(default=None, min_length=1)
    reassign_comment: Optional[str] = Field(default=None, min_length=1)
    assignee_comment: Optional[str] = Field(default=None, min_length=1)
    extension_comment: Optional[str] = Field(default=None, min_length=1)
    new_assignee: Optional[str] = Field(default=None, min_length=1)
    new_due_date: Optional[str] = Field(default=None, min_length=1)

    new_files_ids: Optional[list[str]] = Field(default=None)


class DeleteActionRequest(BaseCamelCaseModel):
    """Request model for deleting one or more actions."""

    external_ids: list[str]
    active_user_email: str
    reporting_site_external_id: str


class CancelActionRequest(BaseCamelCaseModel):
    """Request model for canceling one or more actions."""

    external_ids: list[str]

    active_user_email: str
    reporting_site_external_id: str
    comments: str = Field(default="")

    challenge_request: Optional[bool] = Field(default=None)


class FormLink(BaseCamelCaseModel):
    """Model for storing form links with descriptions and external IDs."""

    link: str
    description: str
    external_id: Optional[str] = Field(default=None)


class RecurrenceInstanceRequest(BaseCamelCaseModel):
    """Request model for defining recurrence instances."""

    external_id: Optional[str] = Field(default=None)
    space: Optional[str] = Field(default=None)
    description: Optional[str] = Field(default=None)
    recurrence_type: str
    week_days: Optional[list[int]] = Field(default_factory=list)
    months: Optional[list[int]] = Field(default_factory=list)
    day_of_the_month: Optional[int] = Field(default=None)
    quarters: Optional[list[int]] = Field(default_factory=list)
    month_of_the_year: Optional[int] = Field(default=None)
    next_dates: Optional[list[str]] = Field(default=None)
    start_date: Optional[str] = Field(default=None)
    end_date: Optional[str] = Field(default=None)

    @model_validator(mode="after")
    def validate_recurrence(self) -> "RecurrenceInstanceRequest":
        """Validate the recurrence parameters."""
        validate_recurrence(
            recurrence_type_external_id=self.recurrence_type,
            week_days=self.week_days,
            months=self.months,
            day_of_the_month=self.day_of_the_month,
            quarters=self.quarters,
            month_of_the_year=self.month_of_the_year,
            next_dates=self.next_dates,
        )
        return self


T = TypeVar("T")


class UpdatableField(BaseCamelCaseModel, Generic[T]):
    """Represent a field that tracks updates and holds an optional value."""

    # NOTE: DO NOT change this fields' names without changing UpdateActionRequest field_validator return
    was_updated: bool = Field(default=False)
    value: Optional[T] = Field(default=None)

    @computed_field
    def was_updated_and_is_not_none(self) -> bool:
        """Check if the field was updated and has a non-null value."""
        return self.was_updated and self.value is not None

    @overload
    def to_node(self, space: str, allow_none: Literal[True] = True) -> Node | None: ...

    @overload
    def to_node(self, space: str, allow_none: Literal[False]) -> Node: ...

    def to_node(self, space: str, allow_none: bool = True) -> Node | None:
        """Convert the value to a Node object if it is a valid string."""
        if self.value is None:
            if not allow_none:
                msg = "Value can't be None."
                raise ValueError(msg)
            return None

        if not isinstance(self.value, str):
            msg = "Value must be a string."
            raise ValueError(msg)

        return Node(external_id=self.value, space=space)

    @overload
    def to_nodes(
        self,
        space: str,
        allow_none: Literal[True] = True,
        allow_empty: bool = False,
    ) -> list[Node] | None: ...

    @overload
    def to_nodes(
        self,
        space: str,
        allow_none: Literal[False],
        allow_empty: bool = False,
    ) -> list[Node]: ...

    def to_nodes(
        self,
        space: str,
        allow_none: bool = True,
        allow_empty: bool = True,
    ) -> list[Node] | None:
        """Convert a list of values to a list of Node objects."""
        if self.value is None:
            if not allow_none:
                msg = "Value can't be None."
                raise ValueError(msg)
            return None

        if not isinstance(self.value, list):
            msg = "Value must be a list."
            raise ValueError(msg)

        if len(self.value) == 0 and not allow_empty:
            msg = "Value list cannot be empty."
            raise ValueError(msg)

        node_list: list[Node] = []
        for v in self.value:
            if not isinstance(v, str):
                msg = "All values must be strings."
                raise ValueError(msg)
            node_list.append(Node(external_id=v, space=space))

        return node_list


class UpdateActionRequest(BaseCamelCaseModel):
    """Update request for an action item."""

    external_id: str
    active_user_email: str
    reporting_site_id: str
    challenge_edit: Optional[bool] = Field(default=None)
    attachment_ids: UpdatableField[list[str]] = Field(
        default_factory=UpdatableField[list[str]],
    )
    title: UpdatableField[str] = Field(default_factory=UpdatableField[str])
    description: UpdatableField[str] = Field(default_factory=UpdatableField[str])
    owner_id: UpdatableField[str] = Field(default_factory=UpdatableField[str])
    reporting_unit_id: UpdatableField[str] = Field(default_factory=UpdatableField[str])
    reporting_location_id: UpdatableField[str] = Field(
        default_factory=UpdatableField[str],
    )
    reporting_line_id: UpdatableField[str] = Field(default_factory=UpdatableField[str])
    links: UpdatableField[list[FormLink]] = Field(
        default_factory=UpdatableField[list[FormLink]],
    )
    source_information: UpdatableField[str] = Field(default_factory=UpdatableField[str])
    category_id: UpdatableField[str] = Field(default_factory=UpdatableField[str])
    sub_category_id: UpdatableField[str] = Field(default_factory=UpdatableField[str])
    site_specific_category_id: UpdatableField[str] = Field(
        default_factory=UpdatableField[str],
    )
    priority: UpdatableField[str] = Field(default_factory=UpdatableField[str])
    assigned_to_id: UpdatableField[str] = Field(default_factory=UpdatableField[str])
    assignee_ids: UpdatableField[list[str]] = Field(
        default_factory=UpdatableField[list[str]],
    )
    due_date: UpdatableField[str] = Field(default_factory=UpdatableField[str])
    assignment_date: UpdatableField[str] = Field(default_factory=UpdatableField[str])
    action_item_kind: UpdatableField[str] = Field(default_factory=UpdatableField[str])
    recurrence_instance: UpdatableField[RecurrenceInstanceRequest] = Field(
        default_factory=UpdatableField[RecurrenceInstanceRequest],
    )
    approver_id: UpdatableField[str] = Field(default_factory=UpdatableField[str])
    verifier_id: UpdatableField[str] = Field(default_factory=UpdatableField[str])
    voe_action_item: UpdatableField[str] = Field(default_factory=UpdatableField[str])
    evidence_required: UpdatableField[bool] = Field(
        default_factory=UpdatableField[bool],
    )
    views: UpdatableField[list[str]] = Field(default_factory=UpdatableField[list[str]])
    view_users: UpdatableField[list[str]] = Field(
        default_factory=UpdatableField[list[str]],
    )
    view_roles: UpdatableField[list[str]] = Field(
        default_factory=UpdatableField[list[str]],
    )
    view_teams: UpdatableField[list[str]] = Field(
        default_factory=UpdatableField[list[str]],
    )

    @field_validator("*", mode="before")
    @classmethod
    def parse_updatable_fields(cls, value: Any, info: ValidationInfo) -> Any:
        """Parse fields to ensure they conform to the UpdatableField format."""
        if info.field_name is None:
            return value

        field_to_type = get_type_hints(UpdateActionRequest)
        field_type = field_to_type.get(info.field_name)
        if field_type is None or not hasattr(field_type, "__mro__"):
            return value

        type_hierarchy = field_type.__mro__
        # Since we use UpdatableField[Type] as annotation
        # type_hierarchy[0] will be UpdatableField[Type]
        # and type_hierarchy[1] will be UpdatableField
        is_not_updatable_field = (
            len(type_hierarchy) < 2 or type_hierarchy[1] != UpdatableField
        )
        if is_not_updatable_field:
            return value

        return {"wasUpdated": True, "value": value}


class ColumnDetails(BaseModel):
    """Details for a column in bulk upload templates."""

    header: str
    placeholder: str


class BulkUploadTemplateRequest(BaseCamelCaseModel):
    """Request for generating a bulk upload template."""

    reporting_site_external_id: str
    columns: dict[
        Literal[
            "title",
            "owner",
            "unit",
            "location",
            "description",
            "sourceInformation",
            "category",
            "subCategory",
            "siteSpecificCategory",
            "assignee",
            "priority",
            "assignmentDate",
            "dueDate",
        ],
        ColumnDetails,
    ]


class ActionEditNotificationRequest(BaseCamelCaseModel):
    """Notification request for editing action items."""

    action_ids: list[str]
    reporting_site_id: str
    active_user_email: str


class ExportActionsRequest(GetActionRequestForIndustrialModel):
    """Request for generating a bulk upload template."""

    columns: dict[str, str]
    translations: dict[str, str]
