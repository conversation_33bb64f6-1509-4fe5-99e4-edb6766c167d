trigger:
  branches:
    include:
      - dev
      - qa
      - prod
  paths:
    include:
      - icap/*

variables:
  - group: action-item-management # TODO: create this
  - name: celaneseProject
    ${{ if eq(variables['Build.SourceBranchName'], 'dev') }}:
      value: celanese-dev
    ${{ elseif eq(variables['Build.SourceBranchName'], 'qa') }}:
      value: celanese-stg
    ${{ elseif eq(variables['Build.SourceBranchName'], 'prod') }}:
      value: celanese
    ${{ else }}:
      value: celanese-dev
  - name: authSecret
    ${{ if eq(variables['Build.SourceBranchName'], 'dev') }}:
      value: $(AUTH_CLIENT_SECRET_DEV)
    ${{ elseif eq(variables['Build.SourceBranchName'], 'qa') }}:
      value: $(AUTH_CLIENT_SECRET_QA)
    ${{ elseif eq(variables['Build.SourceBranchName'], 'prod')}}:
      value: $(AUTH_CLIENT_SECRET_PROD)
    ${{ else }}:
      value: $(AUTH_CLIENT_SECRET_DEV)
  - name: authClientId
    ${{ if eq(variables['Build.SourceBranchName'], 'dev') }}:
      value: $(AUTH_CLIENT_ID_DEV)
    ${{ elseif eq(variables['Build.SourceBranchName'], 'qa') }}:
      value: $(AUTH_CLIENT_ID_QA)
    ${{ elseif eq(variables['Build.SourceBranchName'], 'prod') }}:
      value: $(AUTH_CLIENT_ID_PROD)
    ${{ else }}:
      value: $(AUTH_CLIENT_ID_DEV)
      
stages:
  - stage: PublishCogniteFunction
    displayName: "Publish Function to Cognite for ${{ variables.celaneseProject }} Project"
    jobs:
      - job: PublishFunction
        steps:
          - task: UsePythonVersion@0
            inputs:
              versionSpec: "3.11.5"
            displayName: "Use python 3.11"
            
            
          - bash: |
              sed -i "s/^CLIENT_ID=.*/CLIENT_ID=${{ variables.authClientId }}/" .env
              sed -i "s/^SECRET=.*/SECRET=${{ variables.authSecret }}/" .env
              sed -i "s/^PROJECT=.*/PROJECT=${{ variables.celaneseProject }}/" .env
              sed -i "s/^TRANSFORMATIONS_CLIENT_ID=.*/TRANSFORMATIONS_CLIENT_ID=${{ variables.authClientId }}/" .env
              sed -i "s/^TRANSFORMATIONS_SECRET=.*/TRANSFORMATIONS_SECRET=${{ variables.authSecret }}/" .env
              sed -i "s/^TRANSFORMATIONS_PROJECT=.*/TRANSFORMATIONS_PROJECT=${{ variables.celaneseProject }}/" .env

              cat .env
            workingDirectory: $(System.DefaultWorkingDirectory)/icap
            displayName: "Fill environment variables"

          - script: |
              python -m pip install --upgrade pip
            displayName: "Install pip"
            
          - script: |
              pip install cognite-transformations-cli
              python append_authentication_to_yamls.py
              transformations-cli deploy
              pip uninstall cognite-transformations-cli
            displayName: "Deploy transformations"
            workingDirectory: $(System.DefaultWorkingDirectory)/icap/transformations
            
          - script: |
              python install -r requirements.txt 
            displayName: "Install dependencies"
            workingDirectory: $(System.DefaultWorkingDirectory)/icap

          - script: |
              python -m main reference-data
            displayName: "Upsert Reference Data"
            workingDirectory: $(System.DefaultWorkingDirectory)/icap

          - script: |
              python -m main deploy-functions
            displayName: "Deploy Cognite Functions"
            workingDirectory: $(System.DefaultWorkingDirectory)/icap

          - script: |
              python -m main publish-workflows
            displayName: "Publish Workflows"
            workingDirectory: $(System.DefaultWorkingDirectory)/icap
