import { Grid } from '@mui/material'
import { useCallback, useEffect, useMemo, useState } from 'react'
import { useDebounceFunction } from '@/app/common/hooks/general-functions/useDebounce'
import { useAuthGuard } from '@/app/common/hooks/useAuthGuard'
import { ActionItemStateStatus, ActionItemStateStatusEnum } from '@/app/common/enums/KpiStatusEnum'
import { AzureFunctionClient } from '@/app/common/clients/azure-function-client'
import {
    EXTEND_APPROVER_TAG,
    REASSIGNMENT_APPROVER_TAG,
    SITE_EXTERNAL_ID_REQUIRED_FIELD,
} from '@/app/common/utils/validate-codes'
import {
    ActionStatusExternalIdHomeClosedDefaultEnum,
    ActionStatusExternalIdHomeDefaultEnum,
    AllActionStatusExternalIdEnum,
} from '@/app/common/enums/ActionItemStatusEnum'
import { FilterInfoProps } from '../../ActionTable/HomeFilter'
import { K<PERSON><PERSON>ompo<PERSON>, KpisParams } from './KpisComponent'
import { <PERSON><PERSON><PERSON>ilter, KpisResponseData } from '@/app/common/models/kpis_response'
import { translate } from '@/app/common/utils/generate-translate'
import { AutocompleteOption } from '../../FieldsComponent/GenericAutocomplete'
import { UserRolesPermission } from '@celanese/celanese-sdk'

interface HomeKPIsProps {
    client: AzureFunctionClient
    siteIds: string[]
    activeUser: UserRolesPermission
    currentTab: number
    setFilterInfo: (value: FilterInfoProps) => void
    filterInfo: FilterInfoProps
    activeUserOption: AutocompleteOption
    state: ActionItemStateStatus
    setState: (value: ActionItemStateStatus) => void
    setAssigneeName: (value: AutocompleteOption[]) => void
    setOwnerName: (value: AutocompleteOption[]) => void
    setPage: (value: number) => void
    handleError: (value: any) => void
}

export const HomeKPIs = ({
    client,
    siteIds,
    activeUser,
    currentTab,
    filterInfo,
    setFilterInfo,
    activeUserOption,
    state,
    setState,
    setAssigneeName,
    setOwnerName,
    setPage,
    handleError,
}: HomeKPIsProps) => {
    const activeUserUnitCodes =
        activeUser.units?.map((x) => x.unitCode).filter((code) => code.startsWith('UNT-WAS')) ?? []

    const { checkPermissionsFromComponents } = useAuthGuard()

    const permissions = useMemo(() => {
        if (!activeUser.externalId) return
        return {
            extend: checkPermissionsFromComponents(EXTEND_APPROVER_TAG, undefined, true),
            reassign: checkPermissionsFromComponents(REASSIGNMENT_APPROVER_TAG, undefined, true),
        }
    }, [activeUser])

    const [kpisValues, setKpisValues] = useState<KpisResponseData>()

    const defaultStatus = useMemo(() => {
        return currentTab === 0
            ? Object.values(ActionStatusExternalIdHomeDefaultEnum)
            : Object.values(ActionStatusExternalIdHomeClosedDefaultEnum)
    }, [currentTab])

    const createFilterInfo = (overrides: Partial<FilterInfoProps>): FilterInfoProps => ({
        reportingSiteExternalId: filterInfo.reportingSiteExternalId,
        pageSize: filterInfo.pageSize,
        sortBy: filterInfo.sortBy,
        direction: filterInfo.direction,
        ...overrides,
    })

    const myActionsKpiParams: KpisParams[] = useMemo(() => {
        const totalActions =
            currentTab === 0 ? kpisValues?.totalActionItems ?? '0' : kpisValues?.totalActionClosed ?? '0'
        const assignedToMe = currentTab === 0 ? kpisValues?.assignedToMe ?? '0' : kpisValues?.assignedToMeClosed ?? '0'
        const relatedToMe = currentTab === 0 ? kpisValues?.relatedToMe ?? '0' : '0'
        const overdue = currentTab === 0 ? kpisValues?.overdue ?? '0' : '0'
        const pendingApprovals = currentTab === 0 ? kpisValues?.pendingApprovals ?? '0' : '0'
        const pendingVerifications = currentTab === 0 ? kpisValues?.pendingVerifications ?? '0' : '0'

        let reportingUnits = undefined
        if (permissions?.extend.isAuthorized && siteIds.includes(SITE_EXTERNAL_ID_REQUIRED_FIELD)) {
            reportingUnits = activeUserUnitCodes.length ? activeUserUnitCodes : ['-']
        }
        return [
            {
                key: 'kpi-total-home',
                label: translate('kpis.allActionItems'),
                value: totalActions,
                statusName: ActionItemStateStatusEnum.TotalActions,
                onClick: () => {
                    setState(ActionItemStateStatusEnum.TotalActions)
                    setAssigneeName([])
                    setOwnerName([])
                    setPage(0)
                    setFilterInfo(
                        createFilterInfo({
                            statusExternalIds: defaultStatus,
                            kpiFilter: ActionItemStateStatusEnum.TotalActions,
                        })
                    )
                },
                dataTest: 'home-all_actions_items_card',
            },
            {
                key: 'kpi-assigned-to-me-home',
                label: translate('kpis.assignedToMe'),
                value: assignedToMe,
                statusName: ActionItemStateStatusEnum.AssignedToMe,
                onClick: () => {
                    setState(ActionItemStateStatusEnum.AssignedToMe)
                    setAssigneeName([activeUserOption])
                    setOwnerName([])
                    setPage(0)
                    setFilterInfo(
                        createFilterInfo({
                            statusExternalIds: defaultStatus,
                            assignedToExternalId: activeUser.externalId ? [activeUser.externalId] : [],
                            kpiFilter: ActionItemStateStatusEnum.AssignedToMe,
                        })
                    )
                },
                dataTest: 'home-assigned_to_me_card',
            },
            {
                key: 'kpi-related-to-me-home',
                label: translate('kpis.assigned'),
                value: relatedToMe,
                statusName: ActionItemStateStatusEnum.RelatedToMe,
                disable: currentTab !== 0,
                onClick: () => {
                    setState(ActionItemStateStatusEnum.RelatedToMe)
                    setAssigneeName([])
                    setOwnerName([])
                    setPage(0)
                    setFilterInfo(
                        createFilterInfo({
                            statusExternalIds: defaultStatus,
                            permissionsExtend: permissions?.extend.isAuthorized ?? false,
                            permissionsReassing: permissions?.reassign.isAuthorized ?? false,
                            extensionApprovalSiteExternalIds: permissions?.extend.sitesIds ?? [],
                            reassignmentApprovalSiteExternalIds: permissions?.reassign.sitesIds ?? [],
                            permissionsReportingUnitExternalIds: reportingUnits,
                            kpiFilter: ActionItemStateStatusEnum.RelatedToMe,
                        })
                    )
                },
                dataTest: 'home-related_to_me_card',
            },
            {
                key: 'kpi-pending-approvals-home',
                label: translate('kpis.pendingApprovals'),
                value: pendingApprovals,
                statusName: ActionItemStateStatusEnum.PendingApprovals,
                disable: currentTab !== 0,
                onClick: () => {
                    setState(ActionItemStateStatusEnum.PendingApprovals)
                    setAssigneeName([activeUserOption])
                    setOwnerName([])
                    setPage(0)
                    setFilterInfo(
                        createFilterInfo({
                            statusExternalIds: [AllActionStatusExternalIdEnum.PendingApproval],
                            assignedToExternalId: activeUser.externalId ? [activeUser.externalId] : [],
                            kpiFilter: ActionItemStateStatusEnum.PendingApprovals,
                        })
                    )
                },
                dataTest: 'home-pending_approvals_card',
            },
            {
                key: 'kpi-pending-verification-home',
                label: translate('kpis.pendingVerifications'),
                value: pendingVerifications,
                statusName: ActionItemStateStatusEnum.PendingVerifications,
                disable: currentTab !== 0,
                onClick: () => {
                    setState(ActionItemStateStatusEnum.PendingVerifications)
                    setAssigneeName([activeUserOption])
                    setOwnerName([])
                    setPage(0)
                    setFilterInfo(
                        createFilterInfo({
                            statusExternalIds: [AllActionStatusExternalIdEnum.PendingVerification],
                            assignedToExternalId: activeUser.externalId ? [activeUser.externalId] : [],
                            kpiFilter: ActionItemStateStatusEnum.PendingVerifications,
                        })
                    )
                },
                dataTest: 'home-pending_verifications_card',
            },
            {
                key: 'kpi-overdue-home',
                label: translate('kpis.overdue'),
                value: overdue,
                statusName: ActionItemStateStatusEnum.Overdue,
                disable: currentTab !== 0,
                onClick: () => {
                    setState(ActionItemStateStatusEnum.Overdue)
                    setAssigneeName([])
                    setOwnerName([])
                    setPage(0)
                    setFilterInfo(
                        createFilterInfo({
                            statusExternalIds: defaultStatus,
                            permissionsExtend: permissions?.extend.isAuthorized ?? false,
                            permissionsReassing: permissions?.reassign.isAuthorized ?? false,
                            extensionApprovalSiteExternalIds: permissions?.extend.sitesIds ?? [],
                            reassignmentApprovalSiteExternalIds: permissions?.reassign.sitesIds ?? [],
                            permissionsReportingUnitExternalIds: reportingUnits,
                            kpiFilter: ActionItemStateStatusEnum.Overdue,
                        })
                    )
                },
                dataTest: 'home-overdue_card',
            },
        ]
    }, [translate, kpisValues, permissions, filterInfo, currentTab, activeUserOption])

    const myApprovalsKpiParams: KpisParams[] = useMemo(() => {
        const myApprovals = currentTab === 0 ? kpisValues?.myApprovals ?? '0' : '0'
        const myVerifications = currentTab === 0 ? kpisValues?.myVerifications ?? '0' : '0'
        const myChallenges = currentTab === 0 ? kpisValues?.myChallenges ?? '0' : '0'
        const myExtends = currentTab === 0 ? kpisValues?.myExtends ?? '0' : '0'
        const myReassignments = currentTab === 0 ? kpisValues?.myReassignment ?? '0' : '0'

        let reportingUnits = undefined
        if (permissions?.extend.isAuthorized && siteIds.includes(SITE_EXTERNAL_ID_REQUIRED_FIELD)) {
            reportingUnits = activeUserUnitCodes
        }

        return [
            {
                key: 'kpi-my-approvals-home',
                label: translate('kpis.myApprovals'),
                value: myApprovals,
                statusName: ActionItemStateStatusEnum.MyApprovals,
                disable: currentTab !== 0,
                onClick: () => {
                    setState(ActionItemStateStatusEnum.MyApprovals)
                    setAssigneeName([])
                    setOwnerName([])
                    setPage(0)
                    setFilterInfo(
                        createFilterInfo({
                            statusExternalIds: [AllActionStatusExternalIdEnum.PendingApproval],
                            kpiFilter: ActionItemStateStatusEnum.MyApprovals,
                        })
                    )
                },
                dataTest: 'home-my_approvals_card',
            },
            {
                key: 'kpi-my-verifications-home',
                label: translate('kpis.myVerifications'),
                value: myVerifications,
                statusName: ActionItemStateStatusEnum.MyVerifications,
                disable: currentTab !== 0,
                onClick: () => {
                    setState(ActionItemStateStatusEnum.MyVerifications)
                    setAssigneeName([])
                    setOwnerName([])
                    setPage(0)
                    setFilterInfo(
                        createFilterInfo({
                            statusExternalIds: [AllActionStatusExternalIdEnum.PendingVerification],
                            kpiFilter: ActionItemStateStatusEnum.MyVerifications,
                        })
                    )
                },
                dataTest: 'home-my_verifications_card',
            },
            {
                key: 'kpi-my-challanges-home',
                label: translate('kpis.myChallenges'),
                value: myChallenges,
                statusName: ActionItemStateStatusEnum.MyChallenges,
                disable: currentTab !== 0,
                onClick: () => {
                    setState(ActionItemStateStatusEnum.MyChallenges)
                    setAssigneeName([])
                    setOwnerName([activeUserOption])
                    setPage(0)
                    setFilterInfo(
                        createFilterInfo({
                            statusExternalIds: [AllActionStatusExternalIdEnum.ChallengePeriod],
                            ownerExternalId: activeUser.externalId ? [activeUser.externalId] : [],
                            kpiFilter: ActionItemStateStatusEnum.MyChallenges,
                        })
                    )
                },
                dataTest: 'home-my_challenges_card',
            },
            {
                key: 'kpi-my-extends-home',
                label: translate('kpis.myExtends'),
                value: myExtends,
                statusName: ActionItemStateStatusEnum.MyExtends,
                disable: currentTab !== 0,
                onClick: () => {
                    setState(ActionItemStateStatusEnum.MyExtends)
                    setAssigneeName([])
                    setOwnerName([])
                    setPage(0)
                    setFilterInfo(
                        createFilterInfo({
                            statusExternalIds: [AllActionStatusExternalIdEnum.DueDateExtension],
                            permissionsReportingUnitExternalIds:
                                Array.isArray(reportingUnits) && reportingUnits.length === 0 ? ['-'] : reportingUnits,
                            permissionsExtend: permissions?.extend.isAuthorized ?? false,
                            extensionApprovalSiteExternalIds: permissions?.extend.sitesIds ?? [],
                            kpiFilter: ActionItemStateStatusEnum.MyExtends,
                        })
                    )
                },
                dataTest: 'home-my_extends_card',
            },
            {
                key: 'kpi-my-reassignment-home',
                label: translate('kpis.myReassignment'),
                value: myReassignments,
                statusName: ActionItemStateStatusEnum.MyReassignment,
                disable: currentTab !== 0 || !permissions?.reassign.isAuthorized,
                onClick: () => {
                    setState(ActionItemStateStatusEnum.MyReassignment)
                    setAssigneeName([])
                    setOwnerName([])
                    setPage(0)
                    setFilterInfo(
                        createFilterInfo({
                            statusExternalIds: [AllActionStatusExternalIdEnum.ReassignmentPeriod],
                            permissionsReportingUnitExternalIds:
                                Array.isArray(reportingUnits) && reportingUnits.length === 0 ? ['-'] : reportingUnits,
                            permissionsReassing: permissions?.reassign.isAuthorized ?? false,
                            reassignmentApprovalSiteExternalIds: permissions?.reassign.sitesIds ?? [],
                            kpiFilter: ActionItemStateStatusEnum.MyReassignment,
                        })
                    )
                },
                dataTest: 'home-my_reassignment_card',
            },
        ]
    }, [translate, kpisValues, permissions, filterInfo, currentTab, activeUserOption])

    const fetchKpis = useCallback(async () => {
        try {
            const request: KpisFilter = {
                reportingSiteExternalId: siteIds,
                activeUserEmail: activeUser?.email,
                reportingUnitExternalIds: siteIds.includes(SITE_EXTERNAL_ID_REQUIRED_FIELD)
                    ? activeUserUnitCodes.length > 0
                        ? activeUserUnitCodes
                        : ['-']
                    : undefined,
                permissionsExtend: permissions?.extend.isAuthorized ?? false,
                permissionsReassing: permissions?.reassign.isAuthorized ?? false,
                extensionApprovalSiteExternalIds: permissions?.extend.sitesIds ?? [],
                reassignmentApprovalSiteExternalIds: permissions?.reassign.sitesIds ?? [],
            }

            const result: KpisResponseData = await client.getHomeKpis(request)
            setKpisValues(result)
        } catch (err) {
            handleError(err)
        }
    }, [permissions])

    const debouncedFetchKpi = useCallback(useDebounceFunction(fetchKpis, 600), [fetchKpis])

    useEffect(() => {
        debouncedFetchKpi()
    }, [debouncedFetchKpi])

    return (
        <Grid
            container
            spacing={2}
            id="panel-my-kpis"
            sx={{
                width: '100%',
                height: 'auto',
                minHeight: '100%',
                margin: '0px',
                marginBottom: '0.5rem !important',
                gap: {
                    xs: '1rem',
                    sm: '1rem',
                    md: '1rem',
                    lg: '0',
                },
            }}
        >
            <Grid
                item
                xs={12}
                sm={12}
                md={12}
                lg={6}
                xl={6}
                sx={{ paddingLeft: '0 !important', paddingTop: '0 !important' }}
                id="panel-my-actions"
            >
                <KpisComponent
                    title={translate('kpis.headers.myActions')}
                    kpisParams={myActionsKpiParams}
                    currentState={state}
                />
            </Grid>
            <Grid
                item
                xs={12}
                sm={12}
                md={12}
                lg={6}
                xl={6}
                pt={0}
                sx={{
                    height: 'auto',
                    paddingLeft: {
                        xs: '0 !important',
                        sm: '0 !important',
                        md: '0 !important',
                        lg: '1rem !important',
                    },
                    paddingTop: '0 !important',
                }}
                id="panel-my-approvals-and-verifications"
            >
                <KpisComponent
                    title={translate('kpis.headers.myApprovalsAndVerifications')}
                    kpisParams={myApprovalsKpiParams}
                    currentState={state}
                />
            </Grid>
        </Grid>
    )
}
