from pydantic import computed_field

from clients.core.models import BaseCamelCaseModel
from utils.space_utils import get_code_from_reporting_site_external_id


class GetReportingUnitsRequest(BaseCamelCaseModel):
    """
    Request model for retrieving reporting units based on a specific site and optional description.

    Attributes:
        reporting_site_external_id (str): The external ID of the reporting site.
        description (Optional[str]): An optional description to filter the reporting units.
        site_code (str): Computed property that extracts the site code from the `reporting_site_external_id`.

    Methods:
        site_code: Returns the site code extracted from the `reporting_site_external_id`.

    """

    reporting_site_external_id: str
    description: str | None = None

    @computed_field
    @property
    def site_code(self) -> str:
        """
        Extracts the site code from the reporting site's external ID.

        The external ID is expected to be in the format "prefix-siteCode", and the site code is
        extracted by splitting the ID on the "-" character and returning the second part.

        Returns:
            str: The extracted site code.

        """
        return get_code_from_reporting_site_external_id(self.reporting_site_external_id)


class GetReportingUnitsBySitesRequest(BaseCamelCaseModel):
    """
    Request model for retrieving reporting units based on multiple site IDs and optional search filters.

    Attributes:
        reporting_site_external_id (list[str]): List of external IDs for the reporting sites.
        search (Optional[str]): An optional search term to filter reporting units.
        search_properties (list[str]): List of properties to search for (defaults to ["name", "description"]).
        site_code (list[str]): Computed property that extracts the site code from each reporting site's external ID.

    Methods:
        site_code: Returns a list of site codes extracted from the `reporting_site_external_ids`.

    """

    reporting_site_external_ids: list[str]

    search: str | None = None
    search_properties: list[str] = ["name", "description"]

    @computed_field
    @property
    def site_codes(self) -> list[str]:
        """
        Extracts the site codes from a list of reporting site external IDs.

        Each external ID is expected to be in the format "prefix-siteCode", and the site code is
        extracted by splitting the ID on the "-" character and returning the second part for each ID.

        Returns:
            list[str]: A list of extracted site codes.

        """
        return [
            get_code_from_reporting_site_external_id(site_id)
            for site_id in self.reporting_site_external_ids
        ]
