{"name": "action-item-management-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "build:dev": "env-cmd -f .env.dev next build", "build:qa": "env-cmd -f .env.qa next build", "build:prod": "env-cmd -f .env.prod next build", "start": "node_modules/next/dist/bin/next start", "lint": "next lint", "test": "jest --watch --passWithNoTests --coverage", "test:ci": "jest --ci --passWithNoTests --coverage", "dev:um": "start cmd /K startUmAPI.cmd && start cmd /K startAimAPI.cmd && next dev", "dev:um:nt": "start cmd /K startUmAPI.cmd && start cmd /K startAimAPI.cmd && start cmd /K startNotification.cmd && next dev", "dev:um:tl": "start cmd /K startUmAPI.cmd && start cmd /K startAimAPI.cmd && start cmd /K startTranslation.cmd && next dev", "update-um": "cmd /c updateUm.cmd"}, "dependencies": {"@apollo/client": "^3.7.14", "@azure/msal-browser": "^2.37.0", "@azure/msal-react": "^1.5.7", "@celanese/celanese-sdk": "6.13.0", "@celanese/celanese-ui": "5.13.2", "@celanese/contextualization-lib": "1.22.11", "@celanese/ui-lib": "3.2.1", "@cognite/reveal": "^4.4.0", "@cognite/sdk": "^9.17.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@fontsource/roboto": "^5.1.1", "@hookform/resolvers": "^3.3.2", "@loadable/component": "^5.16.4", "@material-symbols/font-400": "^0.23.0", "@mui/icons-material": "^5.16.14", "@mui/material": "^5.16.14", "@mui/system": "^6.3.1", "@mui/x-data-grid-pro": "^7.22.1", "@mui/x-date-pickers-pro": "^7.22.1", "@mui/x-license-pro": "^6.10.2", "@mui/x-tree-view": "^7.13.0", "@reduxjs/toolkit": "^2.2.5", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@xyflow/react": "^12.3.5", "apexcharts": "3.54.1", "dagre": "^0.8.5", "dayjs": "^1.11.13", "env-cmd": "^10.1.0", "graphql": "^16.6.0", "material-ui-popup-state": "^5.1.2", "mui-markdown": "^1.2.5", "next": "14.1.3", "notistack": "^3.0.1", "react": "^18", "react-apexcharts": "1.4.1", "react-dom": "^18", "react-dropzone": "^14.2.3", "react-hook-form": "^7.49.2", "react-icons": "^4.11.0", "react-intl": "^6.6.8", "react-json-view-lite": "^2.0.1", "react-loadable-visibility": "^3.0.2", "react-redux": "^9.1.2", "react-simple-keyboard": "^3.8.7", "three": "^0.172.0", "xlsx": "^0.18.5", "zod": "^3.22.4", "zustand": "^5.0.1", "terser-webpack-plugin": "^5.3.14"}, "devDependencies": {"@testing-library/jest-dom": "6.1.2", "@testing-library/react": "14.0.0", "@testing-library/user-event": "14.4.3", "@types/jest": "^29.5.5", "@types/node": "^20", "autoprefixer": "^10.4.16", "encoding": "^0.1.13", "eslint": "^8", "eslint-config-next": "13.5.4", "eslint-config-prettier": "^9.0.0", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-unused-imports": "^2.0.0", "jest": "29.6.4", "jest-environment-jsdom": "29.6.4", "postcss": "^8.4.31", "prettier": "2.8.8", "typescript": "^5"}, "overrides": {"glob": "^10", "@celanese/ui-lib": "3.2.1", "@celanese/celanese-sdk": "6.13.0", "@celanese/celanese-file-preview": "1.2.9", "@celanese/celanese-ui": {"three": "^0.172.0", "apexcharts": "3.54.1", "@mui/icons-material": "^5.16.14", "@mui/material": "^5.16.14", "@azure/msal-browser": "^2.37.0", "@azure/msal-react": "^1.5.7"}, "@celanese/contextualization-lib": {"react-apexcharts": "1.4.1", "apexcharts": "3.54.1"}, "@cognite/reveal": {"@cognite/sdk": "^9.17.0", "three": "^0.172.0"}}}