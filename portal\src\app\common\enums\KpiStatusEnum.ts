export type ActionItemStateStatus =
    | 'AssignedToMe'
    | 'RelatedToMe'
    | 'PendingApprovals'
    | 'PendingVerifications'
    | 'Overdue'
    | 'MyApprovals'
    | 'MyVerifications'
    | 'MyChallenges'
    | 'MyExtends'
    | 'MyReassignment'
    | 'TotalActions'
    | 'TotalActionsByFilters'
    | 'DashboardSite'
    | 'DashboardSiteOpen'
    | 'DashboardSitePending'
    | 'DashboardSiteUnitChart'
    | 'DashboardSiteCategoryChart'
    | 'DashboardSupervisor'
    | 'DashboardSupervisorApproval'
    | 'DashboardSupervisorVerification'

export const ActionItemStateStatusEnum = {
    AssignedToMe: 'AssignedToMe',
    RelatedToMe: 'RelatedToMe',
    PendingApprovals: 'PendingApprovals',
    PendingVerifications: 'PendingVerifications',
    Overdue: 'Overdue',
    MyApprovals: 'MyApprovals',
    MyVerifications: 'MyVerifications',
    MyChallenges: 'MyChallenges',
    MyExtends: 'MyExtends',
    MyReassignment: 'MyReassignment',
    TotalActions: 'TotalActions',
    TotalActionsByFilters: 'TotalActionsByFilters',
    DashboardSite: 'DashboardSite',
    DashboardSiteOverdue: 'DashboardSiteOverdue',
    DashboardSiteOpen: 'DashboardSiteOpen',
    DashboardSitePending: 'DashboardSitePending',
    DashboardSiteUnitChart: 'DashboardSiteUnitChart',
    DashboardSiteCategoryChart: 'DashboardSiteCategoryChart',
    DashboardSupervisor: 'DashboardSupervisor',
    DashboardSupervisorKpisEmployee: 'DashboardSupervisorKpisEmployee',
    DashboardSupervisorApproval: 'DashboardSupervisorApproval',
    DashboardSupervisorVerification: 'DashboardSupervisorVerification',
    DashboardSupervisorEmployeeChart: 'DashboardSupervisorEmployeeChart',
    DashboardSupervisorStatusChart: 'DashboardSupervisorStatusChart',
    DashboardSupervisorCategoryChart: 'DashboardSupervisorCategoryChart',
} as const

export type ActionItemStateStatusEnumType = (typeof ActionItemStateStatusEnum)[keyof typeof ActionItemStateStatusEnum]
