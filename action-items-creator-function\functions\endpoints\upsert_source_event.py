import json
import logging
import os
import sys
from http import HTTPStatus

import azure.functions as func
from clients.core.constants import APPLICATION_JSON
from clients.source_event import CreateSourceEventRequest, UpdateSourceEventRequest
from infra.source_event_upserter_factory import SourceEventUpserterFactory

bp = func.Blueprint()

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "..", "..")))


@bp.function_name(name="CreateSourceEvent")
@bp.route("create-source-event", methods=["post"], auth_level=func.AuthLevel.ANONYMOUS)
async def create_source_event(req: func.HttpRequest) -> func.HttpResponse:
    logging.info("Function CreateSourceEvent started")
    try:
        body = req.get_json()
        source_event_request = body.get("sourceEventRequest")
        if not source_event_request:
            return func.HttpResponse(
                "Missing 'sourceEventRequest' in request body",
                status_code=HTTPStatus.BAD_REQUEST,
                mimetype=APPLICATION_JSON,
            )

        if not isinstance(source_event_request, list):
            return func.HttpResponse(
                "'sourceEventRequest' should be a list",
                status_code=HTTPStatus.BAD_REQUEST,
                mimetype=APPLICATION_JSON,
            )

        logging.info("Creating source event in Cognite")
        external_ids, errors = await SourceEventUpserterFactory.create().execute(
            source_event_request, is_update=False
        )

        logging.info("CreateSourceEvent executed successfully")

        return func.HttpResponse(
            json.dumps({"externalIds": external_ids, "errors": errors}),
            status_code=(
                HTTPStatus.OK if external_ids else HTTPStatus.INTERNAL_SERVER_ERROR
            ),
            mimetype=APPLICATION_JSON,
        )
    except Exception as e:
        logging.error(f"Error in CreateSourceEvent: {e}", exc_info=True)
        return func.HttpResponse(
            f"Error: {e}",
            status_code=HTTPStatus.INTERNAL_SERVER_ERROR,
            mimetype=APPLICATION_JSON,
        )


@bp.function_name(name="UpdateSourceEvent")
@bp.route(
    "update-source-event/{id}", methods=["patch"], auth_level=func.AuthLevel.ANONYMOUS
)
async def update_source_event(req: func.HttpRequest) -> func.HttpResponse:
    logging.info("Function UpdateSourceEvent started")
    try:
        body = req.get_json()
        source_event_request = body.get("sourceEventRequest")
        if not source_event_request:
            return func.HttpResponse(
                "Missing 'sourceEventRequest' in request body",
                status_code=HTTPStatus.BAD_REQUEST,
                mimetype=APPLICATION_JSON,
            )

        source_event_request["externalId"] = req.route_params["id"]

        logging.info("Updating source event in Cognite")
        external_ids, errors = await SourceEventUpserterFactory.create().execute(
            [source_event_request], is_update=True
        )

        logging.info("UpdateSourceEvent executed successfully")

        return func.HttpResponse(
            json.dumps({"externalIds": external_ids, "errors": errors}),
            status_code=(
                HTTPStatus.OK if external_ids else HTTPStatus.INTERNAL_SERVER_ERROR
            ),
            mimetype=APPLICATION_JSON,
        )
    except Exception as e:
        logging.error(f"Error in UpdateSourceEvent: {e}", exc_info=True)
        return func.HttpResponse(
            f"Error: {e}",
            status_code=HTTPStatus.INTERNAL_SERVER_ERROR,
            mimetype=APPLICATION_JSON,
        )
