GET_REPORTING_UNIT_BY_FILTER = """
query GetReportingUnit ($filter: _ListReportingUnitFilter, $sort: _ReportingUnitSort!) {
  listReportingUnit (filter: $filter, first: 1000, sort: [$sort]) {
    items {
      externalId
      space
      description
      reportingSites {
        items {
          externalId
          space
        }
      }
    }
  }
}
"""

GET_SEARCH_REPORTING_UNIT_BY_FILTER = """
query SearchReportingUnit($query: String!, $fields: [_SearchReportingUnitFields!], $filter: _SearchReportingUnitFilter, $sort: _ReportingUnitSort!) {
  searchReportingUnit(
    query: $query
    fields: $fields
    filter: $filter
    first: 1000
    sort: [$sort]
  ) {
    items {
      externalId
      space
      description
      reportingSites {
        items {
          externalId
          space
          siteCode
        }
      }
    }
  }
}
"""
