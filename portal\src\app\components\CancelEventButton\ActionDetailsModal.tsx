import React from 'react'
import { ModalWrapper } from '../ModalComponent/Modal/ModalWrapper'
import { Box, useMediaQuery, useTheme } from '@mui/material'
import ActionDetails from '../ActionDetails'
import { UserRolesPermission } from '@celanese/celanese-sdk'

interface ActionDetailsModalProps {
    onClose: () => void
    actionId: string
    activeUser: UserRolesPermission
    hideBackdrop?: boolean
}

export function ActionDetailsModal({ onClose, actionId, activeUser, hideBackdrop = false }: ActionDetailsModalProps) {
    const theme = useTheme()
    const isMobile = useMediaQuery(theme.breakpoints.down('sm'))

    return (
        <ModalWrapper
            openModal={true}
            closeModal={onClose}
            hideBackdrop={hideBackdrop}
            content={
                <Box
                    sx={{
                        width: isMobile ? 'auto' : '80vw',
                        minWidth: isMobile ? 'auto' : '80vw',
                        height: isMobile ? 'auto' : '75vh',
                        maxHeight: '600px',
                        display: 'flex',
                        flexDirection: 'column',
                        position: 'relative',
                        overflowY: 'auto',
                        borderRadius: '50px',
                        padding: '0 25px 25px 25px',
                    }}
                >
                    <ActionDetails
                        id={actionId}
                        siteId={activeUser.selectedSites?.[0].siteId}
                        activeUser={activeUser}
                        isModal
                        onClose={onClose}
                    />
                </Box>
            }
        />
    )
}
