import React, { useMemo, useState } from 'react'
import dayjs from 'dayjs'
import { ActionDetailItem, StatusHistoryInstance } from '@/app/common/models/action-detail'
import { GridColDef, GridRenderCellParams, GridRowsProp } from '@mui/x-data-grid-pro'
import { Box, Link } from '@mui/material'
import { DataGridTable } from '../../PaginatedTable/DataGridTable'
import AuthGuardWrapper from '@/app/common/wrapper/AuthGuardWrapper'
import { translate } from '@/app/common/utils/generate-translate'
import { CustomDrawer } from '../Drawer/Drawer'
import { drawerStyles } from '@/app/components/ModalComponent/Drawer/styles'
import { ClnButton } from '@celanese/ui-lib'
import { historyDrawerStyles } from './styles'

interface HistoryModalProps {
    onClose: () => void
    openDrawer: boolean
    action: ActionDetailItem
}

const ROWS_PER_PAGE_OPTIONS = [10, 15, 25]

function ExpandableCell({ value }: GridRenderCellParams) {
    const [expanded, setExpanded] = useState(false)

    return (
        <div
            style={{
                whiteSpace: 'pre-wrap',
                overflowWrap: 'break-word',
                wordBreak: 'break-word',
                minHeight: '50px',
                margin: '0.5rem 0',
            }}
        >
            {expanded ? value : value.slice(0, 80)}&nbsp;
            {value.length > 80 && (
                <Link
                    type="button"
                    component="button"
                    sx={{ fontSize: 'inherit', letterSpacing: 'inherit' }}
                    onClick={() => setExpanded(!expanded)}
                >
                    {expanded ? 'view less' : 'view more'}
                </Link>
            )}
        </div>
    )
}

export function HistoryModal({ action, openDrawer, onClose }: HistoryModalProps) {
    const [currentPage, setCurrentPage] = useState(0)
    const [rowsPerPage, setRowsPerPage] = useState(10)

    const headCells: GridColDef[] = useMemo(() => {
        return [
            {
                field: 'when',
                label: translate('requestModal.history.when'),
                sortable: false,
                filterable: false,
                minWidth: 100,
                flex: 1,
                renderCell: (params: GridRenderCellParams) => (
                    <span style={historyDrawerStyles.verticalCenterCell}>{params.value}</span>
                ),
                renderHeader: () => (
                    <span data-test="history-when_table_sort" data-origin="aim">
                        {translate('requestModal.history.when')}
                    </span>
                ),
            },
            {
                field: 'actions',
                label: translate('requestModal.history.actions'),
                sortable: false,
                filterable: false,
                minWidth: 150,
                flex: 1,
                renderCell: (params: GridRenderCellParams) => (
                    <span style={historyDrawerStyles.verticalCenterColumnWrap}>
                        <strong>{translate(`requestModal.history.${params.row.actionUser}`)}</strong>
                        {params.row.actions}
                    </span>
                ),
                renderHeader: () => (
                    <span data-test="history-actions_table_sort" data-origin="aim">
                        {translate('requestModal.history.actions')}
                    </span>
                ),
            },
            {
                field: 'description',
                label: translate('requestModal.history.description'),
                sortable: false,
                filterable: false,
                minWidth: 300,
                flex: 1,
                renderCell: (params: GridRenderCellParams) => (
                    <span style={historyDrawerStyles.verticalCenterColumnWrap}>
                        <ExpandableCell {...params} />
                    </span>
                ),
                renderHeader: () => (
                    <span data-test="history-description_table_sort" data-origin="aim">
                        {translate('requestModal.history.description')}
                    </span>
                ),
            },
        ]
    }, [translate])

    const tableRows: GridRowsProp = useMemo(() => {
        function convertActionItemDataToRows(items: StatusHistoryInstance[]) {
            const sortedHistoryInstance = [...items].sort((a, b) => {
                const changedAtA = dayjs(a.changedAt)
                const changedAtB = dayjs(b.changedAt)
                return changedAtB.diff(changedAtA)
            })

            return sortedHistoryInstance.map((item) => {
                const formattedJsonString = item.friendlyName ? item.friendlyName.replace(/\n/g, '\\n') : ''
                const friendlyNameData = JSON.parse(formattedJsonString)
                const comments = friendlyNameData.comments || ''
                const actionUser = friendlyNameData.actionUser || ''
                const change = friendlyNameData.change || []

                return {
                    id: `${item.externalId}`,
                    when: dayjs(item.changedAt).format('MM/DD/YYYY'),
                    actions: `${item.statusSubject?.user?.lastName ?? ''}, ${
                        item.statusSubject?.user?.firstName ?? ''
                    }`,
                    description: comments,
                    actionUser: `${actionUser}`,
                    change: change,
                }
            })
        }

        const actionsRows =
            action?.historyInstance && action.historyInstance.length > 0
                ? convertActionItemDataToRows(action.historyInstance)
                : []

        return actionsRows
    }, [action])

    return (
        <AuthGuardWrapper componentName={HistoryModal.name}>
            <CustomDrawer
                overlineMeta={translate('requestModal.actionItemManagement')}
                title={translate('requestModal.history.history')}
                openDrawer={openDrawer}
                closeDrawer={onClose}
                customWidth
                content={
                    <Box sx={drawerStyles.container}>
                        <Box sx={historyDrawerStyles.datagridTableContainer}>
                            <DataGridTable
                                initialColumnDefs={headCells}
                                rows={tableRows.slice(
                                    currentPage * rowsPerPage,
                                    currentPage * rowsPerPage + rowsPerPage
                                )}
                                getEstimatedRowHeight={() => 100}
                                getRowHeight={() => 'auto'}
                                rowsPerPageOptions={ROWS_PER_PAGE_OPTIONS}
                                currentPage={currentPage}
                                rowsPerPage={rowsPerPage}
                                setRowsPerPage={setRowsPerPage}
                                totalPages={Math.ceil(tableRows.length / rowsPerPage)}
                                setCurrentPage={setCurrentPage}
                                paginationMode="server"
                                showOptionsBar={false}
                            />
                        </Box>
                        <Box sx={historyDrawerStyles.cancelButtonContainer}>
                            <ClnButton variant="text" label={translate('requestModal.cancel')} onClick={onClose} />
                        </Box>
                    </Box>
                }
            />
        </AuthGuardWrapper>
    )
}

export default HistoryModal
