from pydantic import computed_field

from clients.core.constants import (
    DataSpaceEnum,
)
from clients.core.models import GlobalModel

from .constants import (
    USER_AZURE_ATTRIBUTE_PREFIX,
)


def _create_node_as_dict(external_id: str, space: str) -> dict[str, str]:
    return {"externalId": external_id, "space": space}


class CountActionsRequest(GlobalModel):
    """
    Request class for counting actions based on reporting site external ID.

    Attributes:
        reporting_site_external_id (str): The external ID for the reporting site.

    """

    reporting_site_external_id: str | list[str]

    def get_filter_spaces(
        self,
        private_space: bool | None = None,
        *,
        all_sites: bool = False,
    ) -> list[str]:
        """
        Get a list of filter spaces based on the provided parameters.

        Args:
            all_sites (bool): If True, include all sites in the filter.
            private_space (Optional[bool]): If True, include only private space.

        Returns:
            list[str]: A list of filter spaces.

        """
        if private_space:
            return [DataSpaceEnum.PRIVATE_SPACE]
        if private_space is not None:
            return self.spaces
        if all_sites:
            return [
                DataSpaceEnum.PRIVATE_SPACE,
                DataSpaceEnum.COR_SPACE,
                *self.spaces,
            ]
        return [DataSpaceEnum.PRIVATE_SPACE, *self.spaces]

    @computed_field
    @property
    def reporting_site(self) -> list[dict[str, str]]:
        """
        Returns the reporting site as a dictionary.

        Returns:
            list[dict[str, str]]: A dictionary containing the reporting site external ID and its associated space.

        """
        return [
            _create_node_as_dict(
                external_id,
                DataSpaceEnum.REF_DATA_SPACE,
            )
            for external_id in self.reporting_site_external_ids
        ]

    @computed_field
    @property
    def views_private(self) -> list[dict[str, str]]:
        """
        Returns a list of views with private space information.

        Returns:
            list[dict[str, str]]: A list of dictionaries containing private views information.

        """
        if not (self.user_external_id):
            return []

        return (
            [USER_AZURE_ATTRIBUTE_PREFIX + self.user_external_id]
            + (self.active_user_roles_ids or [])
            + (self.active_user_teams_ids or [])
        )


class GetKpisRequest(CountActionsRequest):
    """
    Request class for retrieving KPIs based on the CountActionsRequest.

    Attributes:
        active_user_email (str): The active user's email.
        permissions_extend (bool): Whether to extend permissions.
        permissions_reassing (bool): Whether to reassign permissions.
        user_external_id (Optional[str]): The external ID of the user.
        active_user_roles_ids (Optional[list[str]]): List of active user roles.
        active_user_teams_ids (Optional[list[str]]): List of active user teams.
        reporting_unit_external_ids (Optional[list[str]]): List of reporting unit external IDs.

    """

    active_user_email: str

    permissions_extend: bool = False
    permissions_reassing: bool = False
    extension_approval_site_external_ids: list[str] | None = None
    reassignment_approval_site_external_ids: list[str] | None = None

    user_external_id: str | None = None
    active_user_roles_ids: list[str] | None = None
    active_user_teams_ids: list[str] | None = None
    reporting_unit_external_ids: list[str] | None = None

    @computed_field
    @property
    def user(self) -> dict[str, str]:
        """
        Returns the user as a dictionary.

        Returns:
            dict[str, str]: A dictionary containing the user external ID and its associated space.

        """
        return _create_node_as_dict(self.user_external_id, DataSpaceEnum.UMG_DATA_SPACE)

    @computed_field
    @property
    def user_azure_attribute(self) -> dict[str, str]:
        """
        Returns the user Azure attribute as a dictionary.

        Returns:
            dict[str, str]: A dictionary containing the user Azure attribute and its associated space.

        """
        return _create_node_as_dict(
            USER_AZURE_ATTRIBUTE_PREFIX + self.user_external_id,
            DataSpaceEnum.UMG_DATA_SPACE,
        )
