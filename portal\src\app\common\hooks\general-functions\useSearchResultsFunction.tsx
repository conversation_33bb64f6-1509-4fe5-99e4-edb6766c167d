import { useCallback } from 'react'
import { ViewReference, useFdmSearch } from '../cognite/useFdmSearch'

export function useSearchResultsFunction() {
    const [searchFunctionFdm] = useFdmSearch()
    return {
        getAllResults: useCallback(
            async (
                view: ViewReference,
                query?: string,
                instanceType?: string,
                properties?: string[],
                filter?: any,
                limit?: number
            ): Promise<any> => {
                const res = await searchFunctionFdm({
                    view,
                    query,
                    instanceType,
                    properties,
                    filter,
                    limit,
                })
                return res
            },
            [searchFunctionFdm]
        ),
    }
}
