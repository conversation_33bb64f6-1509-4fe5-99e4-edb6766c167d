import azure.functions as func
from clients.core.constants import APPLICATION_JSON

bp = func.Blueprint()


@bp.function_name(name="NotificationsByActionsIds")
@bp.route(
    "create-notifications-by-actions-ids",
    methods=["post"],
    auth_level=func.AuthLevel.ANONYMOUS,
)
async def main(req: func.HttpRequest) -> func.HttpResponse:
    import logging
    import os
    import sys
    import json as json

    sys.path.append(
        os.path.abspath(os.path.join(os.path.dirname(__file__), "..", ".."))
    )
    from infra.notifications_by_action_ids_factory import (
        NotificationByActionsIdsFactory,
    )

    logging.info("Function NotificationsByActionsIds started")

    try:
        body = req.get_json()
        actions_ids_request = body.get("actionsIds")

        if not actions_ids_request:
            raise ValueError("Missing 'actionsIds' in request body")

        logging.info(f"Getting started send notifications by Notifications app")
        notifications_send = await NotificationByActionsIdsFactory.create().execute(
            actions_ids_request
        )

        logging.info("Finishing execution")

        return func.HttpResponse(
            json.dumps(
                {
                    "notifications_send": notifications_send,
                }
            ),
            status_code=200,
            mimetype=APPLICATION_JSON,
        )
    except Exception as e:
        logging.info("Exception found!")
        return func.HttpResponse(f"Error: {e}", status_code=500)
