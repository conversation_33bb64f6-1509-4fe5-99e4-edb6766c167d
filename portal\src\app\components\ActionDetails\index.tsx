import { AzureFunctionClient } from '@/app/common/clients/azure-function-client'
import { useLoading } from '@/app/common/contexts/LoadingContext'
import { useSnackbar } from '@/app/common/contexts/SnackbarContext'
import { ActionStatusExternalIdClearEnum } from '@/app/common/enums/ActionItemStatusEnum'
import { FileSourceStepEnum } from '@/app/common/enums/FileSourceStepEnum'
import { useCognite } from '@/app/common/hooks'
import { useDebounceFunction } from '@/app/common/hooks/general-functions/useDebounce'
import { useAuthGuard } from '@/app/common/hooks/useAuthGuard'
import {
    ActionDetailItem,
    ActionDetailsPermissions,
    Attachment,
    CategoryConfigurationData,
    ResponseData,
    Comment,
} from '@/app/common/models/action-detail'
import { UserComplement } from '@/app/common/models/common/user-management/user-complement'
import { APP_NAME } from '@/app/common/utils'
import { buildUploadFilesRequest } from '@/app/common/utils/files'
import { translate } from '@/app/common/utils/generate-translate'
import {
    FEATURE_REASSIGNMENT_APPROVER,
    FEATURE_EXTEND_APPROVER,
    SITE_EXTERNAL_ID_REQUIRED_FIELD,
} from '@/app/common/utils/validate-codes'
import { ResponseUsersByFeatureData, UserRolesPermission } from '@celanese/celanese-sdk'
import { TranslationContext, TranslationContextState } from '@celanese/celanese-ui'
import { ClnPage, ClnPanel, MatIcon } from '@celanese/ui-lib'
import { Box, Typography, useMediaQuery, useTheme } from '@mui/material'
import { useRouter } from 'next/navigation'
import { useCallback, useContext, useEffect, useMemo, useState } from 'react'
import DetailEditIconButton from '../ButtonsComponents/DetailEditIconButton'
import RemoveItemIconButton from '../ButtonsComponents/RemoveItemIconButton'
import AuthGuardWrapper from '@/app/common/wrapper/AuthGuardWrapper'
import LoaderCircular from '../Loader'
import { BackArrow } from '@/app/common/utils/backArrow'
import GeneralInfo from '../HomeComponent/Details/GeneralInfo'
import { AimTabs } from '../Tabs'
import DetailsTab from '../HomeComponent/Details/DetailsTab'
import FilesAndLinksTab from '../HomeComponent/Details/FilesAndLinksTab'
import MessageModal from '../ModalComponent/Modal/MessageModal'
import DetailsMenu from '../HomeComponent/Details/DetailsMenu'
import HistoryModal from '../ModalComponent/HistoryModal/HistoryModal'
import RequestApprovalModal from '../RequestModal/RequestApprovalModal'
import AssigneeRequestModal from '../RequestModal/AssigneeRequestModal'
import RequestModalComplete from '../RequestModal/RequestModalComplete'

import './styles.css'

interface ActionDetailsProps {
    id: string
    siteId?: string
    activeUser: UserRolesPermission
    isModal?: boolean
    onClose?: () => void
    onRefetch?: () => void
}

export default function ActionDetails({ id, siteId, activeUser, isModal, onClose, onRefetch }: ActionDetailsProps) {
    const { cogniteClient } = useCognite()
    const client = new AzureFunctionClient()

    const theme = useTheme()
    const isMobile = useMediaQuery(theme.breakpoints.down('md'))

    const { showLoading } = useLoading()

    const { checkPermissionsFromComponents } = useAuthGuard()
    const { locale } = useContext<TranslationContextState>(TranslationContext)

    const { showSnackbar } = useSnackbar()

    const router = useRouter()

    const [categoryConfiguration, setCategoryConfiguration] = useState<CategoryConfigurationData | undefined>(undefined)
    const [usersByAssigneeRequest, setUsersByAssigneeRequest] = useState<ResponseUsersByFeatureData[] | undefined>()

    const [actionDetails, setActionDetails] = useState<ActionDetailItem | undefined>(undefined)

    const [loadingActivateDetails, setLoadingActivateDetails] = useState<boolean>(true)
    const [initialAttachmentsLength, setInitialAttachmentsLength] = useState<number | null>(null)

    const [permissions, setPermissions] = useState<ActionDetailsPermissions>({
        edit: false,
        delete: false,
        view: true,
    })

    const [currentTab, setCurrentTab] = useState(0)

    const hasBackPage = !!onClose
    const Container = hasBackPage ? Box : ClnPanel
    const ContainerPage = hasBackPage ? Box : ClnPage

    const [modalExtensionOpen, setModalExtensionOpen] = useState(false)
    const [modalReassignOpen, setModalReassignOpen] = useState(false)
    const [modalChallengeOpen, setModalChallengeOpen] = useState(false)
    const [modalCompleteOpen, setModalCompleteOpen] = useState(false)
    const [modalApprovalOpen, setModalApprovalOpen] = useState(false)
    const [modalHistoryOpen, setModalHistoryOpen] = useState(false)

    const [currentComments, setCurrentComments] = useState<Comment[]>([])

    const [uploadState, setUploadState] = useState({
        isModalOpen: false,
        files: [] as File[],
        isUploading: false,
    })

    const handleAlert = (message: string, error: boolean = false) => {
        showSnackbar(message, error ? 'error' : 'success', 'action-details')
    }

    const [assigneeRequestType, setAssigneeRequestType] = useState<string>(
        actionDetails?.currentStatus?.externalId === ActionStatusExternalIdClearEnum.ReassignmentPeriod
            ? ActionStatusExternalIdClearEnum.ReassignmentPeriod
            : ActionStatusExternalIdClearEnum.DueDateExtension
    )

    const isMasterAdminEdit: boolean = useMemo(() => {
        const permissions = checkPermissionsFromComponents(
            'DetailEditIconButton',
            siteId ?? actionDetails?.reportingSite?.externalId
        )
        return permissions.isAuthorized
    }, [activeUser, actionDetails])

    const isMasterAdminDelete: boolean = useMemo(() => {
        const permissions = checkPermissionsFromComponents(
            'RemoveItemIconButton',
            siteId ?? actionDetails?.reportingSite?.externalId
        )
        return permissions.isAuthorized
    }, [activeUser, actionDetails])

    const canUploadFiles: boolean = useMemo(() => {
        const activeUserName = activeUser?.externalId
        const assigneeName = actionDetails?.assignedTo?.user.externalId
        const ownerName = actionDetails?.owner?.user.externalId
        const actionStatusCantUpload =
            actionDetails?.currentStatus?.externalId === ActionStatusExternalIdClearEnum.Cancelled ||
            actionDetails?.currentStatus?.externalId === ActionStatusExternalIdClearEnum.Completed
        return (
            (activeUserName === ownerName || activeUserName === assigneeName || isMasterAdminEdit) &&
            !actionStatusCantUpload
        )
    }, [activeUser, actionDetails, isMasterAdminEdit])

    const viewOnly: boolean = useMemo(() => {
        return actionDetails?.viewOnly ?? false
    }, [actionDetails])

    const usersByAssigneeRequestByRequestType = useMemo(() => {
        const filteredFeatureCode =
            assigneeRequestType === ActionStatusExternalIdClearEnum.DueDateExtension
                ? FEATURE_EXTEND_APPROVER
                : FEATURE_REASSIGNMENT_APPROVER

        return (
            usersByAssigneeRequest
                ?.filter((item) => item.featureCode === filteredFeatureCode)
                ?.flatMap((item) => item.users) ?? []
        )
    }, [assigneeRequestType, usersByAssigneeRequest])

    const handleOpenCloseHistoryModal = (open: boolean) => {
        setModalHistoryOpen(open)
    }

    const redirectToLastPage = () => {
        const lastPage = sessionStorage.getItem('lastPage') ?? '/'

        const isCompleteOrCancelled =
            actionDetails?.currentStatus?.externalId === ActionStatusExternalIdClearEnum.Completed ||
            actionDetails?.currentStatus?.externalId === ActionStatusExternalIdClearEnum.Cancelled

        sessionStorage.removeItem('lastPage')

        if (isCompleteOrCancelled && lastPage === '/') {
            localStorage.setItem('currentTabHome', '1')
        }

        showLoading(true)
        router.push(lastPage)
    }

    const handleBackOrCancel = () => {
        if (onClose) {
            onClose()
        } else {
            redirectToLastPage()
        }
    }

    const handleOpenCloseModal = (open: boolean, modal: string) => {
        switch (modal) {
            case ActionStatusExternalIdClearEnum.Completed:
                setModalCompleteOpen(open)
                break
            case ActionStatusExternalIdClearEnum.ChallengePeriod:
                setAssigneeRequestType(ActionStatusExternalIdClearEnum.ChallengePeriod)
                setModalChallengeOpen(open)
                break
            case ActionStatusExternalIdClearEnum.DueDateExtension:
                setAssigneeRequestType(ActionStatusExternalIdClearEnum.DueDateExtension)
                setModalExtensionOpen(open)
                break
            case ActionStatusExternalIdClearEnum.ReassignmentPeriod:
                setAssigneeRequestType(ActionStatusExternalIdClearEnum.ReassignmentPeriod)
                setModalReassignOpen(open)
                break
            case ActionStatusExternalIdClearEnum.PendingApproval:
                setModalApprovalOpen(open)
                break
            default:
                break
        }
    }

    const masterAdminCanDeleteAction =
        actionDetails?.currentStatus?.externalId !== ActionStatusExternalIdClearEnum.Cancelled &&
        actionDetails?.currentStatus?.externalId !== ActionStatusExternalIdClearEnum.Completed

    const masterAdminCanEditAction = [
        ActionStatusExternalIdClearEnum.Assigned,
        ActionStatusExternalIdClearEnum.PendingApproval,
        ActionStatusExternalIdClearEnum.PendingVerification,
        ActionStatusExternalIdClearEnum.ApprovalRejected,
        ActionStatusExternalIdClearEnum.VerificationRejected,
    ]
        .map(String)
        .includes(actionDetails?.currentStatus?.externalId ?? '')

    const tabs = useMemo(
        () => [
            {
                label: translate('details.tab.details'),
                auth: 'DetailsAction',
                content: '',
            },
            {
                label: translate('details.tab.filesAndLinks'),
                auth: 'DetailsActionLink',
                content: '',
            },
        ],
        [translate, locale]
    )
    const authorizedTabs = tabs.filter((item) => checkPermissionsFromComponents(item.auth).isAuthorized)

    const fetchAction = useCallback(
        async (skipLoadingState?: boolean) => {
            try {
                if (actionDetails === undefined) {
                    if (!skipLoadingState) {
                        setLoadingActivateDetails(true)
                    }

                    if (activeUser?.email) {
                        const result: ResponseData = await client.getActionItemById({
                            externalId: id,
                            activeUserEmail: activeUser.email,
                            reportingSiteExternalId: siteId,
                        })

                        const actionDetail =
                            Array.isArray(result.data) && result.data.length > 0
                                ? result.data[0]
                                : (result.data as ActionDetailItem | undefined)
                        setActionDetails(actionDetail)
                        setCurrentComments(actionDetail?.comments ?? [])
                        setPermissions(
                            result.permissions ?? {
                                edit: false,
                                delete: false,
                                view: false,
                            }
                        )
                    } else {
                        setPermissions({
                            edit: false,
                            delete: false,
                            view: false,
                        })
                    }
                }
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred'
                handleAlert(errorMessage, true)
            }
        },
        [activeUser]
    )

    const debouncedFetchActions = useCallback(useDebounceFunction(fetchAction, 800), [fetchAction])

    const fetchCategoryConfiguration = useCallback(async () => {
        try {
            if (client == null) {
                return
            }

            const resultCategory: CategoryConfigurationData = await client.getCategoryConfigurationByConfig({
                reportingSiteExternalId: siteId ?? actionDetails?.reportingSite?.externalId,
                categoryId: actionDetails?.category?.externalId,
                subCategoryId: actionDetails?.subCategory?.externalId,
                siteSpecificCategoryId: actionDetails?.siteSpecificCategory?.externalId,
            })
            if (resultCategory) setCategoryConfiguration(resultCategory)
        } catch (err) {
            const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred'
            handleAlert(errorMessage, true)
        } finally {
            setLoadingActivateDetails(false)
        }
    }, [actionDetails])

    const debouncedFetchCategoryConfiguration = useCallback(useDebounceFunction(fetchCategoryConfiguration, 800), [
        fetchCategoryConfiguration,
    ])

    useEffect(() => {
        if (activeUser?.email && actionDetails) {
            debouncedFetchCategoryConfiguration()
        }
    }, [debouncedFetchCategoryConfiguration])

    const matchUsersByExternalId = (
        userExternalIds: Set<string>,
        userComplementObjects: UserComplement[]
    ): Set<string> => {
        return new Set(
            userComplementObjects
                .filter((userComplement) =>
                    userExternalIds.has(userComplement.userAzureAttribute?.user.externalId ?? '')
                )
                .map((userComplement) => userComplement.userAzureAttribute?.user.externalId ?? '')
        )
    }

    const uploadFiles = async (newFiles: File[]): Promise<void> => {
        if (!newFiles.length) return

        setUploadState({ isModalOpen: true, files: newFiles, isUploading: false })
    }

    const handleConfirmUploadFiles = async () => {
        const { files } = uploadState
        if (!files.length) return

        setUploadState((prev) => ({ ...prev, isUploading: true }))
        setInitialAttachmentsLength(actionDetails?.attachments?.length ?? 0)

        try {
            const uploadFilesRequest = await buildUploadFilesRequest(
                cogniteClient,
                files,
                activeUser!.displayName,
                actionDetails?.reportingSite?.externalId ?? '',
                actionDetails?.isPrivate,
                FileSourceStepEnum.ActionInProgress
            )

            const newUploadedFilesIds = (await client.uploadFiles(uploadFilesRequest)).externalIds
            const oldUploadedFilesIds =
                actionDetails?.attachments?.map((attachment: Attachment) => attachment.externalId) ?? []

            await client.updateActionItem(actionDetails?.externalId ?? '', {
                externalId: actionDetails?.externalId,
                reportingSiteId: actionDetails?.reportingSite?.externalId,
                activeUserEmail: activeUser?.email,
                attachmentIds: [...newUploadedFilesIds, ...oldUploadedFilesIds],
            })

            fetchAction(true)
        } catch (error) {
            setUploadState({ isModalOpen: false, files: [], isUploading: false })
            throw error
        }
    }

    const handleCancelUploadFiles = () => {
        setUploadState({ isModalOpen: false, files: [], isUploading: false })
    }

    useEffect(() => {
        if (
            uploadState.isUploading &&
            initialAttachmentsLength !== null &&
            actionDetails?.attachments?.length !== initialAttachmentsLength
        ) {
            setUploadState({ isModalOpen: false, files: [], isUploading: false })
            setInitialAttachmentsLength(null)
        }
    }, [actionDetails?.attachments?.length, uploadState.isUploading, initialAttachmentsLength])

    const fetchUserPermission = useCallback(async () => {
        try {
            if (!client) return
            const featureCode = [FEATURE_REASSIGNMENT_APPROVER, FEATURE_EXTEND_APPROVER]

            const usersByFeature: ResponseUsersByFeatureData[] = await client.getUsersByFeature({
                siteCode: siteId ?? actionDetails?.reportingSite?.externalId,
                featureCode,
            })

            let externalIds = new Set(usersByFeature.flatMap((item) => item.users))

            if (
                (siteId ?? actionDetails?.reportingSite?.externalId) === SITE_EXTERNAL_ID_REQUIRED_FIELD &&
                externalIds.size > 0
            ) {
                const userComplementsByUnit: UserComplement[] = await client.getUserComplementsByUnits({
                    reportingUnitsIds: [actionDetails?.reportingUnit?.externalId],
                })

                externalIds = matchUsersByExternalId(externalIds, userComplementsByUnit)
            }

            const filteredResult = usersByFeature.map((item) => ({
                featureCode: item.featureCode,
                users: item.users.filter((user) => externalIds.has(user)),
            }))

            setUsersByAssigneeRequest(filteredResult)
        } catch (err) {
            const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred'
            handleAlert(errorMessage, true)
        }
    }, [actionDetails])

    const debouncedFetchUserPermission = useCallback(useDebounceFunction(fetchUserPermission, 800), [
        fetchUserPermission,
    ])

    useEffect(() => {
        if (activeUser?.email && actionDetails) {
            debouncedFetchUserPermission()
        }
    }, [debouncedFetchUserPermission])

    useEffect(() => {
        if (activeUser?.email !== undefined) debouncedFetchActions()
    }, [debouncedFetchActions])

    useEffect(() => {
        document.title = translate('pages.home.details') + ' ' + '|' + ' ' + APP_NAME
    }, [locale])

    const actionButtons = () => {
        return (
            <>
                <MatIcon
                    icon="history"
                    color="primary.main"
                    fontSize={isMobile ? '1.5rem' : '30px'}
                    sx={{
                        cursor: 'pointer',
                    }}
                    onClick={() => {
                        handleOpenCloseHistoryModal(true)
                    }}
                />

                <DetailEditIconButton
                    id={id}
                    hidden={!(permissions.edit || (isMasterAdminEdit && masterAdminCanEditAction)) || viewOnly}
                    iconSize={isMobile ? '1.5rem' : undefined}
                    siteId={siteId ?? actionDetails?.reportingSite?.externalId!}
                />

                <RemoveItemIconButton
                    hidden={!(permissions.delete || (isMasterAdminDelete && masterAdminCanDeleteAction)) || viewOnly}
                    actionDetails={actionDetails}
                    activeUserEmail={activeUser?.email ?? '-'}
                    handleAlert={handleAlert}
                    isDelete
                    iconSize={isMobile ? '1.5rem' : undefined}
                    redirectToPage={onRefetch}
                />

                <RemoveItemIconButton
                    hidden={
                        !(permissions.delete || (isMasterAdminDelete && masterAdminCanDeleteAction)) ||
                        viewOnly ||
                        isMobile
                    }
                    label={translate('requestModal.cancelAction')}
                    isOutline
                    light
                    actionDetails={actionDetails}
                    activeUserEmail={activeUser?.email ?? '-'}
                    handleAlert={handleAlert}
                    redirectToPage={onRefetch}
                />
            </>
        )
    }

    const messageModalContent = () => {
        return (
            <>
                <Typography>
                    <Box
                        sx={{
                            display: 'flex',
                            flexDirection: 'column',
                            gap: '1rem',
                            flexWrap: 'wrap',
                        }}
                    >
                        <Typography>{`${translate('requestModal.uploadFileText').replace(
                            '{}',
                            `${uploadState.files.length}`
                        )}`}</Typography>
                        <Box>
                            {uploadState.files.map((file, index) => (
                                <Typography
                                    key={file.name}
                                    sx={{
                                        lineBreak: 'anywhere',
                                    }}
                                >
                                    <strong>{file.name}</strong>
                                    {index < uploadState.files.length - 1 ? ', ' : ''}
                                </Typography>
                            ))}
                        </Box>
                        <Typography>{translate('requestModal.confirmUpload')}</Typography>
                    </Box>
                </Typography>
            </>
        )
    }

    return (
        <AuthGuardWrapper componentName={permissions.view ? ActionDetails.name : 'UnknownComponent'}>
            <ContainerPage sx={isMobile ? { flexDirection: 'column' } : {}}>
                {loadingActivateDetails ? (
                    LoaderCircular()
                ) : (
                    <>
                        <Container
                            id="action-id-panel"
                            sx={{
                                ...(!hasBackPage && {
                                    padding: '1rem',
                                    display: 'flex',
                                    flexDirection: 'column',
                                    marginBottom: '1rem',
                                }),
                            }}
                        >
                            <Box>
                                {!isModal && (
                                    <Box
                                        sx={{
                                            display: 'flex',
                                            alignItems: 'top',
                                            flexDirection: 'column',
                                        }}
                                    >
                                        <Box
                                            sx={{
                                                display: 'flex',
                                                flexDirection: isMobile ? 'column' : 'row',
                                                gap: isMobile ? '1.5rem' : '0',
                                                justifyContent: 'space-between',
                                            }}
                                        >
                                            <Box onClick={handleBackOrCancel}>
                                                {BackArrow(translate('stepper.back'), () => handleBackOrCancel)}
                                            </Box>
                                            <Box
                                                sx={{
                                                    display: 'flex',
                                                    gap: '1rem',
                                                    flexDirection: 'row',
                                                    alignContent: 'center',
                                                    justifyContent: isMobile ? 'space-between' : 'center',
                                                    alignItems: 'center',
                                                    flexWrap: 'nowrap',
                                                }}
                                            >
                                                {isMobile ? (
                                                    <>
                                                        <Typography
                                                            variant="h3"
                                                            sx={{
                                                                fontWeight: 'bold',
                                                                wordBreak: 'break-word',
                                                                whiteSpace: 'normal',
                                                                overflowWrap: 'break-word',
                                                                flexShrink: 1,
                                                            }}
                                                        >
                                                            {actionDetails?.title ?? ''}
                                                        </Typography>

                                                        <Box sx={{ display: 'flex', gap: '1rem' }}>
                                                            {actionButtons()}
                                                        </Box>
                                                    </>
                                                ) : (
                                                    actionButtons()
                                                )}
                                            </Box>
                                        </Box>
                                    </Box>
                                )}

                                <GeneralInfo actionItem={actionDetails} isMobile={isMobile} />

                                {isMobile && (
                                    <Box
                                        sx={{
                                            py: '1.2rem',
                                        }}
                                    >
                                        <RemoveItemIconButton
                                            hidden={
                                                !(
                                                    permissions.delete ||
                                                    (isMasterAdminDelete && masterAdminCanDeleteAction)
                                                ) || viewOnly
                                            }
                                            label={translate('requestModal.cancelAction')}
                                            isOutline
                                            light
                                            actionDetails={actionDetails}
                                            activeUserEmail={activeUser?.email ?? '-'}
                                            handleAlert={handleAlert}
                                            redirectToPage={onRefetch}
                                            fullWidth
                                        />
                                    </Box>
                                )}

                                <AimTabs
                                    value={currentTab}
                                    tabs={authorizedTabs}
                                    onChange={(_e, value) => setCurrentTab(value)}
                                />
                                {currentTab === 0 ? (
                                    <DetailsTab
                                        actionItem={actionDetails}
                                        activeUser={activeUser}
                                        currentComments={currentComments}
                                        setCurrentComments={setCurrentComments}
                                    />
                                ) : (
                                    <>
                                        <FilesAndLinksTab
                                            canUploadFiles={canUploadFiles}
                                            attachments={[
                                                ...(actionDetails?.attachments || []),
                                                ...(actionDetails?.changeRequests
                                                    ?.filter(
                                                        (item) =>
                                                            item.approvalWorkflow?.status?.externalId ===
                                                            'APWST-Approved'
                                                    )
                                                    ?.flatMap((change) => change?.attatchments || []) || []),
                                            ]}
                                            onUploadFiles={uploadFiles}
                                            links={actionDetails?.actionItemLink}
                                            isLoading={uploadState.isUploading}
                                        />
                                        <MessageModal
                                            name={translate('requestModal.uploadFile')}
                                            open={uploadState.isModalOpen && !uploadState.isUploading}
                                            isCancelModal={false}
                                            handleClose={handleCancelUploadFiles}
                                            handleConfirm={handleConfirmUploadFiles}
                                            isCloseModal
                                        >
                                            {messageModalContent()}
                                        </MessageModal>
                                    </>
                                )}
                            </Box>
                            <DetailsMenu
                                actionItem={actionDetails}
                                activeUser={activeUser}
                                categoryConfig={categoryConfiguration}
                                isModal={Boolean(isModal)}
                                handleOpenCloseModal={handleOpenCloseModal}
                                handleCancel={handleBackOrCancel}
                                buttonsBoxStyle={{
                                    flexDirection: isMobile ? 'column' : undefined,
                                }}
                                fullWidthButtons={isMobile}
                            />
                        </Container>
                        {modalHistoryOpen && actionDetails && (
                            <HistoryModal
                                onClose={() => handleOpenCloseHistoryModal(false)}
                                openDrawer={modalHistoryOpen}
                                action={actionDetails}
                            />
                        )}
                        {modalApprovalOpen && actionDetails && (
                            <RequestApprovalModal
                                client={client}
                                openDrawer={modalApprovalOpen}
                                action={actionDetails}
                                activeUserEmail={activeUser?.email ?? '-'}
                                onClose={() =>
                                    handleOpenCloseModal(false, ActionStatusExternalIdClearEnum.PendingApproval)
                                }
                                handleAlert={handleAlert}
                                redirectToPage={onRefetch}
                                redirectToLastPage={redirectToLastPage}
                            />
                        )}
                        {(modalReassignOpen || modalExtensionOpen || modalChallengeOpen) && actionDetails && (
                            <AssigneeRequestModal
                                client={client}
                                action={actionDetails}
                                activeUser={activeUser!}
                                assigneeRequestType={assigneeRequestType}
                                handleAlert={handleAlert}
                                openModal={modalReassignOpen || modalExtensionOpen || modalChallengeOpen}
                                setOpenModal={(value) => handleOpenCloseModal(value, assigneeRequestType)}
                                categoryConfig={categoryConfiguration}
                                usersByAssigneeRequest={usersByAssigneeRequestByRequestType}
                                redirectToPage={onRefetch}
                                redirectToLastPage={redirectToLastPage}
                            />
                        )}
                        {modalCompleteOpen && actionDetails && (
                            <RequestModalComplete
                                client={client}
                                activeUser={activeUser!}
                                evidenceRequired={actionDetails?.evidenceRequired ?? false}
                                actionDetails={actionDetails}
                                handleAlert={handleAlert}
                                onClose={() => handleOpenCloseModal(false, ActionStatusExternalIdClearEnum.Completed)}
                                redirectToPage={onRefetch}
                                redirectToLastPage={redirectToLastPage}
                            />
                        )}
                    </>
                )}
            </ContainerPage>
        </AuthGuardWrapper>
    )
}
