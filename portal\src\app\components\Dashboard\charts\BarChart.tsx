'use client'
import React, { useRef } from 'react'
import { ApexOptions } from 'apexcharts'
import { Box, useMediaQuery, useTheme } from '@mui/material'
import { useChartDefaultColors } from './styles'

import dynamic from 'next/dynamic'
import { translate } from '@/app/common/utils/generate-translate'
const ReactApexChart = dynamic(() => import('react-apexcharts'), { ssr: false })

export interface QuantityBarItemChart {
    label: string
    value: number | number[]
}

export interface ClnBarChartConfig {
    id: string
    data: QuantityBarItemChart[]
    groups?: Array<string | number>
    title?: string
    legendPosition?: 'top' | 'right' | 'bottom' | 'left'
    isHorizontal?: boolean
    isStacked?: boolean
    width?: number
    height?: number
    labelSuffix?: string
    labelPrefix?: string
    colors?: string[]
    onReachEnd?: () => void
}

export function BarChart(config: ClnBarChartConfig) {
    const chartContainerRef = useRef<HTMLDivElement>(null)
    const chartColors = useChartDefaultColors()
    const theme = useTheme()

    const isMobile = useMediaQuery(theme.breakpoints.down('sm'))

    const width = config.isHorizontal ? `95%` : config.width || (isMobile ? '100%' : 500)
    const height = config.height || 350
    
    const prefix = config.labelPrefix || ''
    const suffix = config.labelSuffix || ''
    const colors = config.colors ?? chartColors

    const handleScroll = () => {
        const container = chartContainerRef.current
        if (container) {
            const isAtBottom = container.scrollHeight - container.scrollTop <= container.clientHeight + 1
            if (isAtBottom) {
                if (config.onReachEnd) {
                    config.onReachEnd()
                }
            }
        }
    }

    const title = {
        text: config.title,
        style: {
            fontSize: '20px',
            color: theme.palette.text.secondary,
        },
    }

    const generateSeries = () => {
        if (config.isStacked) {
            return config.data.map((item) => ({
                name: item.label,
                data: Array.isArray(item.value) ? (item.value as number[]) : [item.value],
            }))
        } else {
            return [{ data: config.data.map((item) => ({ x: item.label, y: item.value })) }]
        }
    }

    const series: ApexAxisChartSeries = generateSeries()

    const xaxis = {
        categories: config.isStacked ? config.groups : config.data.map((point: any) => point.label),
        labels: {
            style: {
                colors: theme.palette.text.secondary,
            },
            formatter: function (val: any) {
                return config.isHorizontal ? `${prefix} ${val} ${suffix}` : val
            },
        },
    }

    const yaxis = {
        labels: {
            style: {
                colors: theme.palette.text.secondary,
            },
            formatter: function (val: any) {
                return config.isHorizontal ? val : `${prefix} ${val} ${suffix}`
            },
        },
    }

    const chart = {
        stacked: config.isStacked,
        toolbar: {
            show: false,
        },
        offsetX: config.isHorizontal ? -10 : -15,
    }

    const noData: ApexNoData = {
        text: `${translate('common.noResultsForFilter')}`,
        align: 'center',
        verticalAlign: 'middle',
        offsetX: 0,
        offsetY: -25,
        style: {
            color: theme.palette.text.secondary,
            fontSize: '25px',
            fontFamily: 'Helvetica, Arial, sans-serif',
        },
    }

    const plotOptions = {
        bar: {
            borderRadius: 0,
            horizontal: config.isHorizontal,
            barHeight: config.isHorizontal ? '20px' : 0,
            dataLabels: {
                total: {
                    enabled: true,
                    offsetX: 0,
                    offsetY: config.isHorizontal ? 2 : 0,
                    style: {
                        fontSize: '14px',
                        fontFamily: 'Roboto',
                        color: theme.palette.text.secondary,
                    },
                },
                style: {
                    colors: [theme.palette.text.secondary + ' !important'],
                },
            },
        },
        responsive: [
            {
              breakpoint: 600,
              options: {
                plotOptions: {
                  bar: {
                    horizontal: true
                  }
                },
                legend: {
                  position: "bottom"
                }
              }
            }
          ]
    }

    const dataLabels = {
        enabled: !config.isStacked,
        style: {
            colors: [theme.palette.text.secondary],
        },
        formatter: function (val: any) {
            return `${prefix} ${val} ${suffix}`
        },
    }

    const legend: ApexLegend = {
        show: !!config.isStacked,
        offsetY: 0,
        offsetX: config.isHorizontal ? 0 : 10,
        position: config.legendPosition ?? 'bottom',
        horizontalAlign: 'left',
        labels: {
            colors: theme.palette.text.secondary,
        },
        markers: {
            offsetY: 1.5,
        },
    }

    const apexTheme: ApexTheme = {
        mode: theme.palette.mode,
    }

    const options: ApexOptions = {
        title,
        plotOptions,
        legend,
        noData,
        dataLabels,
        colors,
        chart,
        xaxis,
        yaxis,
        theme: apexTheme,
    }

    return (
        <Box
            ref={chartContainerRef}
            onScroll={handleScroll}
            sx={{
                maxHeight: isMobile ? '100%' : '300px',
                overflowX: 'hidden',
                overflowY: config.isHorizontal ? (height < 300 ? 'hidden' : 'auto') : 'hidden',
            }}
            id={`stacked-chart-${config.id}`}
        >
            <ReactApexChart id={config.id} options={options} series={series} type="bar" width={width} height={height} />
        </Box>
    )
}
