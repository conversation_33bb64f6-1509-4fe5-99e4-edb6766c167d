import { ColorModeProvider, useColorMode, darkTheme, lightTheme } from '@celanese/ui-lib'
import { ThemeProvider as PrimitiveThemeProvider } from '@mui/material'
import React, { ReactNode } from 'react'

const ThemedComponent: React.FC<{ children: ReactNode }> = ({ children }) => {
    const { colorMode } = useColorMode()
    const theme = colorMode === 'dark' ? darkTheme : lightTheme

    return <PrimitiveThemeProvider theme={theme}>{children}</PrimitiveThemeProvider>
}

export const ThemeProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
    return (
        <ColorModeProvider>
            <ThemedComponent>{children}</ThemedComponent>
        </ColorModeProvider>
    )
}
