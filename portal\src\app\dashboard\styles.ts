export const styles = {
    container: { marginTop: '1rem', gap: '1rem', display: 'flex', flexGrow: 1, flexDirection: 'column' },
    filterWrapper: {
        display: 'flex',
        flexDirection: 'row !important',
        gap: '12px',
        width: '100% !important',
    },
    headerButtons: {
        display: 'flex',
        flexDirection: 'row !important',
        justifyContent: 'space-between',
        gap: '12px',
        width: '100% !important',
    },
    containerLine: {
        display: 'flex',
        border: '1px solid',
        borderRadius: '8px',
        borderColor: 'otherColor.outlineBorder',
        padding: '10px 24px 24px 24px',
        width: '100%',
        minWidth: '420px',
        flexDirection: 'column',
        justifyContent: 'space-between',
        gap: '1rem',
        // mobile media query
        '@media (max-width:600px)': {
            maxWidth: '300px',
            overflowX: 'scroll',
        },
    },
    label: { color: 'grey[500]', fontSize: '20px', lineHeight: '24px', marginTop: '5px' },
}

export const stylesSite = {
    kpiWrapper: {
        gap: '1rem',
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(180px, 1fr))',
        '@media (max-width:800px)': {
            gridTemplateColumns: 'repeat(1, 1fr)',
        },
    },
}

export const stylesSupervisor = {
    chartsWrapper: {
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(420px, 1fr))',
        gap: '1rem',
        '@media (max-width:1623px)': {
            '& > :nth-child(3n)': {
                gridColumn: 'span 2',
            },
        },
        '@media (max-width:1130px)': {
            gridTemplateColumns: 'repeat(1, 1fr)',
            '& > :nth-child(3n)': {
                gridColumn: 'span 1',
            },
        },
    },
    kpiWrapper: {
        gap: '1rem',
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(170px, 1fr))',
        '@media (max-width:800px)': {
            gridTemplateColumns: 'repeat(1, 1fr)',
        },
    },
}
