import { MatIcon } from '@celanese/ui-lib'
import { useRouter } from 'next/navigation'
import { useLoading } from '@/app/common/contexts/LoadingContext'
import { getCodeFromReportingSiteExternalId } from '@/app/common/utils/space-util'

type DetailEditIconButtonProps = {
    id: string
    siteId?: string
    hidden?: boolean
    routerString?: string
    iconSize?: string
}

export default function DetailEditIconButton({
    id,
    siteId,
    hidden,
    routerString,
    iconSize,
}: DetailEditIconButtonProps) {
    const router = useRouter()

    const { showLoading } = useLoading()

    return (
        <>
            {!hidden && (
                <MatIcon
                    icon="edit"
                    color="primary.main"
                    fontSize={iconSize ?? '30px'}
                    sx={{
                        cursor: 'pointer',
                    }}
                    onClick={() => {
                        showLoading(true)
                        router.push(
                            (routerString ?? `/action-item/edit/${getCodeFromReportingSiteExternalId(siteId ?? '')}/`) +
                                id
                        )
                    }}
                />
            )}
        </>
    )
}
