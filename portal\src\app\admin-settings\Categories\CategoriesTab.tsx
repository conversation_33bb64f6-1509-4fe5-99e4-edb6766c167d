import { Box, useMediaQuery, useTheme } from '@mui/material'
import { ClnButtonProps, ActionIcon, MatIcon } from '@celanese/ui-lib'
import { useEffect, useMemo, useState } from 'react'
import { CategoryConfiguration } from '../../common/models/category-configuration'
import { useCategoryConfigurations } from '../../common/hooks/action-item-management/useCategoryConfigurations'
import { ActionItemCategory } from '../../common/models/category'
import { ActionItemSubCategory } from '../../common/models/sub-category'
import { SiteSpecificCategory } from '../../common/models/site-specific-category'
import { useCategoryConfigurationMutations } from '../../common/hooks/mutations/useCategoryConfigurationMutations'
import { FilterOptionsCategoriesConfiguration } from '../../common/models/admin-settings/filter-categories'
import { CategoriesTabFilter } from './CategoriesTabFilter'
import { CategoriesConfigurationFormSchema, CategoriesForm } from './CategoriesForm'
import { useAuthGuard } from '../../common/hooks/useAuthGuard'
import DetailDeleteModal from '@/app/components/ModalComponent/DetailDeleteModal'
import {
    CategoryConfigRequest,
    useCategoryConfigCheck,
} from '@/app/common/hooks/action-item-management/useCategoryConfigCheck'
import { DataGridTable } from '@/app/components/PaginatedTable/DataGridTable'
import { GridColDef, GridRowsProp } from '@mui/x-data-grid-pro'
import { useSnackbar } from '@/app/common/contexts/SnackbarContext'
import { translate } from '@/app/common/utils/generate-translate'
import { determineActiveFilterCount } from '@/app/common/utils/active-filter-count'
import { ROWS_PER_PAGE_OPTIONS_ADMIN_SCREEN } from '@/app/common/utils'

type CategoriesTabProps = {
    loading: boolean
    categories: ActionItemCategory[]
    subCategories: ActionItemSubCategory[]
    siteSpecificCategories: SiteSpecificCategory[]
}

export const CategoriesTab = (props: CategoriesTabProps) => {
    const { categories, subCategories, siteSpecificCategories } = props

    const { showSnackbar } = useSnackbar()

    const theme = useTheme()
    const isMobile = useMediaQuery(theme.breakpoints.down('md'))

    const { checkPermissionsFromComponents } = useAuthGuard()
    const [saveCategoriesConfiguration, deleteCategoriesConfiguration] = useCategoryConfigurationMutations()
    const { categoryConfig: categoriesConfigurationsData, refetchConfig } = useCategoryConfigurations()

    //Drawer
    const [categoriesConfigurationToEdit, setCategoriesConfigurationToEdit] = useState<
        CategoryConfiguration | undefined
    >()
    const [isEdit, setIsEdit] = useState<boolean>(false)
    const [openCategoriesConfigurationDrawer, setOpenCategoriesConfigurationDrawer] = useState(false)

    //Delete
    const [openModal, setOpenModal] = useState<boolean>(false)
    const [categoryConfigToDelete, setCategoryConfigToDelete] = useState<CategoryConfiguration>()

    const { assConfig } = useCategoryConfigCheck(
        useMemo<CategoryConfigRequest>(() => {
            const queryParams: CategoryConfigRequest = {
                categoryId: categoryConfigToDelete?.category.externalId,
                subCategoryId: categoryConfigToDelete?.actionItemSubCategory.externalId,
                siteSpecificCategoryId: categoryConfigToDelete?.siteSpecificCategory?.externalId,
            }
            return queryParams
        }, [categoryConfigToDelete])
    )

    //Filter
    const [search, setSearch] = useState<string>('')

    const storedFilterInfo = sessionStorage.getItem(`admin-category-configuration-filterInfo`)
    const parsedFilterInfo = storedFilterInfo ? JSON.parse(storedFilterInfo) : {}

    const [filterOptions, setFilterOptions] = useState<FilterOptionsCategoriesConfiguration>({
        category: [],
        subcategory1: [],
        subcategory2: [],
    })
    const [filterActions, setFilterActions] = useState<FilterOptionsCategoriesConfiguration>({ ...parsedFilterInfo })

    const applyFilters = async (filters: any) => {
        setFilterActions(filters)
        resetPageProps()
    }

    useEffect(() => {
        buildFilterOptions()
    }, [categories, subCategories, siteSpecificCategories])

    const buildFilterOptions = (): void => {
        const options: FilterOptionsCategoriesConfiguration = {
            category: [],
            subcategory1: [],
            subcategory2: [],
        }
        categories.forEach((cat) => {
            if (cat.name && !options.category.includes(cat.name)) {
                options.category.push(cat.name)
            }
        })
        subCategories.forEach((subCat) => {
            if (subCat.name && !options.subcategory1.includes(subCat.name)) {
                options.subcategory1.push(subCat.name)
            }
        })
        siteSpecificCategories.forEach((siteSpecificCat) => {
            if (siteSpecificCat.name && !options.subcategory2.includes(siteSpecificCat.name)) {
                options.subcategory2.push(siteSpecificCat.name)
            }
        })
        setFilterOptions(options)
    }

    const handleDeleteClick = (config: CategoryConfiguration | undefined) => {
        setCategoryConfigToDelete(config)
        setOpenModal(true)
    }

    const deleteSpecificCategoryFunction = async (onSuccess?: () => void) => {
        if (!categoryConfigToDelete) return

        try {
            await deleteCategoriesConfiguration([categoryConfigToDelete])
            showAlert('alerts.dataSavedWithSuccess', false)
            refetchConfig()
            onSuccess?.()
        } catch (error) {
            showAlert('alerts.unexpectedErrorOcurred', true)
        }
    }

    const showAlert = (messageKey: string, isError: boolean) => {
        showSnackbar(translate(messageKey), isError ? 'error' : 'success', 'admin-category-configuration')
    }

    const handleCloseModal = () => {
        setCategoryConfigToDelete(undefined)
        setOpenModal(false)
    }

    //Filter data in frontend according to filter
    const filteredData: CategoryConfiguration[] = useMemo(() => {
        const filteredData = categoriesConfigurationsData.filter((cc: CategoryConfiguration) => {
            //Filter
            if (filterActions) {
                if (filterActions.category?.length > 0) {
                    if (!filterActions.category.includes(cc.category?.name)) {
                        return false
                    }
                }
                if (filterActions.subcategory1?.length > 0) {
                    if (!filterActions.subcategory1.includes(cc.actionItemSubCategory?.name)) {
                        return false
                    }
                }
                if (filterActions.subcategory2?.length > 0) {
                    if (!filterActions.subcategory2.includes(cc.siteSpecificCategory?.name)) {
                        return false
                    }
                }
            }

            //Search
            if (search && search?.length > 0) {
                const searchData = search.toLowerCase()
                const category = cc.category?.name?.toLowerCase()
                const subcategory1 = cc.actionItemSubCategory?.name?.toLowerCase()
                const subcategory2 = cc.siteSpecificCategory?.name?.toLowerCase()

                if (
                    !category?.includes(searchData) &&
                    !subcategory1?.includes(searchData) &&
                    !subcategory2?.includes(searchData)
                ) {
                    return false
                }
            }
            return true
        })
        return filteredData
    }, [categoriesConfigurationsData, filterActions, search])

    //Table
    const headCells: GridColDef[] = useMemo(
        () => [
            {
                field: 'category',
                headerName: translate('adminSettings.table.headers.category'),
                filterable: false,
                flex: 1,
                renderHeader: () => (
                    <span data-test="admin-category-tab-category_table_sort" data-origin="aim">
                        {translate('adminSettings.table.headers.category')}
                    </span>
                ),
            },
            {
                field: 'subcategory1',
                headerName: translate('adminSettings.table.headers.subcategoryOne'),
                filterable: false,
                flex: 1,
                renderHeader: () => (
                    <span data-test="admin-category-tab-subcategoryOne_table_sort" data-origin="aim">
                        {translate('adminSettings.table.headers.subcategoryOne')}
                    </span>
                ),
            },
            {
                field: 'subcategory2',
                headerName: translate('adminSettings.table.headers.subcategoryTwo'),
                filterable: false,
                flex: 1,
                renderHeader: () => (
                    <span data-test="admin-category-tab-subcategoryTwo_table_sort" data-origin="aim">
                        {translate('adminSettings.table.headers.subcategoryTwo')}
                    </span>
                ),
            },
        ],
        []
    )

    const [currentPage, setPage] = useState(0)
    const [rowsPerPage, setRowsPerPage] = useState(ROWS_PER_PAGE_OPTIONS_ADMIN_SCREEN[0])

    const resetPageProps = () => {
        setPage(0)
    }

    const tableRows: GridRowsProp = useMemo(() => {
        function convertActionItemDataToRows(items: any[]) {
            return items.map((item) => ({
                id: `${item.externalId}`,
                category: `${item.category?.name}`,
                subcategory1: `${item.actionItemSubCategory?.name}`,
                subcategory2: `${item.siteSpecificCategory?.name ?? ' '}`,
            }))
        }

        const actionsRows =
            filteredData != null && filteredData.length > 0 ? convertActionItemDataToRows(filteredData) : []

        return actionsRows
    }, [filteredData])

    const actions: ActionIcon[] = [
        {
            icon: <MatIcon icon="edit" />,
            onClick: (id) => {
                const ccItem = categoriesConfigurationsData.find((cc) => cc.externalId == id)
                handleEditClick(ccItem)
            },
        },
        {
            icon: <MatIcon icon="delete" color="error.main" />,
            onClick: (id) => {
                const ccItem = categoriesConfigurationsData.find((cc) => cc.externalId == id)
                handleDeleteClick(ccItem)
            },
        },
    ]

    const buttons: ClnButtonProps[] = [
        {
            label: isMobile ? '' : translate('adminSettings.table.newCategory'),
            startIcon: isMobile ? <MatIcon icon="add" /> : undefined,
            variant: 'contained',
            sxProps: isMobile
                ? {
                      '& .MuiButton-startIcon': {
                          marginRight: 0,
                      },
                  }
                : undefined,
            onClick: () => setOpenCategoriesConfigurationDrawer(true),
        },
    ]

    const handleEditClick = (categoriesConfiguration: CategoryConfiguration | undefined) => {
        setCategoriesConfigurationToEdit(categoriesConfiguration)
        setOpenCategoriesConfigurationDrawer(true)
        setIsEdit(true)
    }

    //Form
    const handleSubmitForm = async (
        categoriesConfigurationFromForm: CategoriesConfigurationFormSchema,
        onSuccess?: () => void
    ) => {
        const { externalId } = categoriesConfigurationToEdit || {}
        const {
            category,
            subCategory1,
            subCategory2,
            isApprovalRequired,
            isVerificationRequired,
            evidenceRequired,
            isExtensionsAllowed,
            isExtensionAttachmentRequired,
            daysToApproval,
            daysToVerification,
            daysFromAssignedDate,
            defaultApprovalRole,
            defaultVerificationRole,
            defaultExtensionApproverRole,
            hasEmailNotification,
            defaultApprovalUser,
            defaultVerificationUser,
        } = categoriesConfigurationFromForm

        let categoriesConfig = categoriesConfigurationsData

        if (externalId) {
            categoriesConfig = categoriesConfigurationsData.filter((x) => x.externalId !== externalId)
        }

        const existingExternalIds = categoriesConfig.map((x) => x.externalId)
        const newExternalId = externalId ?? generateCategoriesConfigurationExternalId(categoriesConfigurationFromForm)

        const isDuplicateExternalId = existingExternalIds.includes(newExternalId)
        if (isDuplicateExternalId) {
            showAlert('alerts.errorOcurred', true)
            return
        }

        const dataToRequest: CategoryConfiguration = {
            externalId: newExternalId,
            category: categories.find((cat) => cat.externalId === category) as ActionItemCategory,
            actionItemSubCategory: subCategories.find(
                (subCat) => subCat.externalId === subCategory1
            ) as ActionItemSubCategory,
            siteSpecificCategory: siteSpecificCategories.find(
                (siteSpecificCat) => siteSpecificCat.externalId === subCategory2
            ) as SiteSpecificCategory,
            isApprovalRequired: isApprovalRequired === 'true',
            isVerificationRequired: isVerificationRequired === 'true',
            attachmentRequired: evidenceRequired === 'true',
            isExtensionsAllowed: isExtensionsAllowed === 'true',
            isExtensionAttachmentRequired: isExtensionAttachmentRequired === 'true',
            daysToApproval: daysToApproval && parseInt(daysToApproval) !== 0 ? parseInt(daysToApproval) : undefined,
            daysToVerification:
                daysToVerification && parseInt(daysToVerification) !== 0 ? parseInt(daysToVerification) : undefined,
            daysFromAssignedDate:
                daysFromAssignedDate && parseInt(daysFromAssignedDate) !== 0
                    ? parseInt(daysFromAssignedDate)
                    : undefined,
            defaultApprovalRole:
                defaultApprovalRole && defaultApprovalRole !== ''
                    ? {
                          externalId: defaultApprovalRole,
                          space: 'UMG-COR-ALL-DAT',
                      }
                    : undefined,
            defaultVerificationRole:
                defaultVerificationRole && defaultVerificationRole !== ''
                    ? {
                          externalId: defaultVerificationRole,
                          space: 'UMG-COR-ALL-DAT',
                      }
                    : undefined,
            defaultExtensionApproverRole:
                defaultExtensionApproverRole && defaultExtensionApproverRole !== ''
                    ? {
                          externalId: defaultExtensionApproverRole,
                          space: 'UMG-COR-ALL-DAT',
                      }
                    : undefined,
            hasEmailNotification: hasEmailNotification === 'true',
            defaultApprovalUser:
                defaultApprovalUser && defaultApprovalUser !== ''
                    ? {
                          externalId: defaultApprovalUser,
                          space: 'UMG-COR-ALL-DAT',
                      }
                    : undefined,
            defaultVerificationUser:
                defaultVerificationUser && defaultVerificationUser !== ''
                    ? {
                          externalId: defaultVerificationUser,
                          space: 'UMG-COR-ALL-DAT',
                      }
                    : undefined,
        }

        try {
            await saveCategoriesConfiguration(dataToRequest)
            showAlert('alerts.dataSavedWithSuccess', false)
            refetchConfig()
            handleCloseForm()
            onSuccess?.()
        } catch (error) {
            showAlert('alerts.unexpectedErrorOcurred', true)
        }
    }

    const generateCategoriesConfigurationExternalId = (categoriesConfigurationFromForm: any) => {
        return (
            'CCON-' +
            categoriesConfigurationFromForm.category +
            '_' +
            categoriesConfigurationFromForm.subCategory1 +
            '_' +
            categoriesConfigurationFromForm.subCategory2
        )
    }

    const handleCloseForm = () => {
        setIsEdit(false)
        setOpenCategoriesConfigurationDrawer(false)
        setCategoriesConfigurationToEdit(undefined)
    }

    enum TableTranslateKey {
        Search = 'Search',
        RowsPerPage = 'Rows per page',
        Of = 'of',
        Filters = 'Filters',
        Actions = 'Actions',
    }

    const translatedLabels = new Map<TableTranslateKey, string>()
    translatedLabels.set(TableTranslateKey.Search, translate('common.search'))
    translatedLabels.set(TableTranslateKey.RowsPerPage, translate('common.rowsPerPage'))
    translatedLabels.set(TableTranslateKey.Of, translate('common.of'))
    translatedLabels.set(TableTranslateKey.Filters, translate('common.filters'))
    translatedLabels.set(TableTranslateKey.Actions, translate('common.actions'))

    const customPopoverContent = useMemo(() => {
        return (
            <CategoriesTabFilter
                data={filterOptions}
                defaultFilter={filterActions}
                onSubmit={(filters) => {
                    applyFilters(filters)
                    sessionStorage.setItem(`admin-category-configuration-filterInfo`, JSON.stringify(filters))
                }}
            />
        )
    }, [translate, filterOptions, filterActions])

    return (
        <Box
            sx={{
                margin: '1rem 0px',
                display: 'flex',
                flexGrow: 1,
                flexDirection: 'column',
            }}
        >
            <DataGridTable
                id="admin-category-configuration"
                isLoading={props.loading}
                initialColumnDefs={headCells}
                onSearchSubmit={(value: string) => setSearch(value)}
                customButtons={buttons}
                rows={tableRows.slice(currentPage * rowsPerPage, currentPage * rowsPerPage + rowsPerPage)}
                rowsPerPageOptions={ROWS_PER_PAGE_OPTIONS_ADMIN_SCREEN}
                currentPage={currentPage}
                rowsPerPage={rowsPerPage}
                setRowsPerPage={setRowsPerPage}
                customPopoverContent={customPopoverContent}
                totalPages={Math.ceil(tableRows.length / rowsPerPage)}
                setCurrentPage={setPage}
                actions={checkPermissionsFromComponents(CategoriesTab.name) ? actions : undefined}
                activeFiltersCount={determineActiveFilterCount(filterActions)}
            />
            <DetailDeleteModal
                name={categoryConfigToDelete?.externalId ?? ''}
                activeAlertText={assConfig}
                alertText={translate('adminSettings.categories.alertDelete')}
                open={openModal}
                handleClose={handleCloseModal}
                deleteFunction={deleteSpecificCategoryFunction}
            />
            <CategoriesForm
                isEdit={isEdit}
                onClose={() => setOpenCategoriesConfigurationDrawer(false)}
                openDrawer={openCategoriesConfigurationDrawer}
                onSubmitCallback={handleSubmitForm}
                onCloseCallback={handleCloseForm}
                categoriesConfigurationToEdit={categoriesConfigurationToEdit}
                categories={categories}
                subCategories={subCategories}
                siteSpecificCategories={siteSpecificCategories}
            />
        </Box>
    )
}
