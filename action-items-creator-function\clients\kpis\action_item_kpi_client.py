from datetime import datetime, timedelta, timezone

from cognite.client import CogniteClient
from cognite.client.data_classes import data_modeling

from clients.category_configuration.models import (
    CategoryConfigurationByFilterResult,
)
from clients.core.constants import (
    APPROVAL_WORKFLOW_STEP_TO_USERS,
    LIMIT,
    ActionStatusEnum,
    ApprovalWorkflowStepDescriptionEnum,
    DataSpaceEnum,
    ViewEnum,
)
from clients.core.filters import (
    contains_sdk_filter,
)
from clients.core.models import ServiceParams

from .constants import (
    ACTION_ACTIVE_STATUS,
    ACTION_COLSED_STATUS,
    APPROVAL_WORKFLOW_STEP_ACTIVE_STATUS,
)
from .models import ActionResult, GetActionResponse, KpiResponse, RelatedToMeConfig
from .requests import CountActionsRequest, GetKpisRequest


class ActionItemKpiClient:
    """Client for handling KPI-related operations for action items."""

    def __init__(
        self,
        params: ServiceParams,
    ) -> None:
        """Initialize the KPI client with service parameters."""
        self._cognite_client = params.cognite_client
        view_as_dict = {view.external_id: view for view in params.data_model.views}
        self._action_view = view_as_dict[ViewEnum.ACTION]
        self._approval_workflow_step_view = view_as_dict[
            ViewEnum.APPROVAL_WORKFLOW_STEP
        ]

    @classmethod
    def create(
        cls,
        cognite_client: CogniteClient,
        data_model_id: data_modeling.DataModelIdentifier,
    ) -> "ActionItemKpiClient":
        """Create a KPI client instance with the provided Cognite client."""
        data_model = cognite_client.data_modeling.data_models.retrieve(
            ids=data_model_id,
        ).latest_version()

        return cls(cognite_client, data_model.views)

    def get_kpis(
        self,
        request: GetKpisRequest,
        category_configurations: list[CategoryConfigurationByFilterResult] | None,
    ) -> KpiResponse:
        """
        Retrieve all KPIs for the action home page.

        Args:
            request (GetKpisRequest): The request object containing the parameters for fetching KPIs.
            category_configurations (list[CategoryConfigurationByFilterResult] | None): Optional list of category configurations to filter the KPIs.

        Returns:
            KpiResponse: The response containing the fetched KPIs for the action home page.

        """
        total_action_items = self._count_actions(request=request)

        total_action_closed = self._count_actions(request=request, is_closed=True)

        response = self._get_actions(request)
        response_closed = self._get_actions(request, is_closed=True)

        assigned_to_me = response.count_assigned_to(request.user_external_id)

        assigned_to_me_closed = response_closed.count_assigned_to(
            request.user_external_id,
        )

        related_to_me = response.count_related_to_me(
            RelatedToMeConfig(
                user_external_id=request.user_external_id,
                user_reporting_units_ids=request.reporting_unit_external_ids,
                permissions_extend=request.permissions_extend,
                permissions_reassing=request.permissions_reassing,
                user_roles_ids=request.active_user_roles_ids,
                categories_configuration=category_configurations,
                extension_approval_site_external_ids=request.extension_approval_site_external_ids,
                reassignment_approval_site_external_ids=request.reassignment_approval_site_external_ids,
            ),
        )

        pending_approvals = response.count_assigned_to(
            request.user_external_id,
            [ActionStatusEnum.PENDING_APPROVAL],
        )

        pending_verifications = response.count_assigned_to(
            request.user_external_id,
            [ActionStatusEnum.PENDING_VERIFICATION],
        )

        overdue = response.count_related_to_me(
            RelatedToMeConfig(
                user_external_id=request.user_external_id,
                user_reporting_units_ids=request.reporting_unit_external_ids,
                permissions_extend=request.permissions_extend,
                permissions_reassing=request.permissions_reassing,
                extension_approval_site_external_ids=request.extension_approval_site_external_ids,
                reassignment_approval_site_external_ids=request.reassignment_approval_site_external_ids,
                due_date=datetime.now(tz=timezone.utc).date() - timedelta(days=1),
                action_by_pending_verification_status=[
                    ActionStatusEnum.PENDING_VERIFICATION,
                ],
                action_by_pending_approval_status=[ActionStatusEnum.PENDING_APPROVAL],
                action_by_owner_status=[ActionStatusEnum.CHALLENGE_PERIOD],
                action_by_assigned_to_status=[
                    ActionStatusEnum.ASSIGNED,
                    ActionStatusEnum.VERIFICATION_REJECTED,
                    ActionStatusEnum.APPROVAL_REJECTED,
                ],
                user_roles_ids=request.active_user_roles_ids,
                categories_configuration=category_configurations,
            ),
        )

        my_approvals = response.count_pending_approval(
            [ActionStatusEnum.PENDING_APPROVAL],
        )

        my_verifications = response.count_pending_verification(
            [ActionStatusEnum.PENDING_VERIFICATION],
        )

        my_challenges = response.count_owner(
            request.user_external_id,
            [ActionStatusEnum.CHALLENGE_PERIOD],
        )

        my_extends = (
            response.count_my_extends(
                [ActionStatusEnum.DUE_DATE_EXTENSION_PERIOD],
                request.reporting_unit_external_ids,
                request.permissions_extend,
                request.active_user_roles_ids,
                category_configurations,
                request.extension_approval_site_external_ids,
            )
            if request.permissions_extend or request.active_user_roles_ids
            else 0
        )

        my_reassignment = (
            response.count_general_actions(
                [ActionStatusEnum.REASSIGNMENT_PERIOD],
                request.reporting_unit_external_ids,
                request.reassignment_approval_site_external_ids,
            )
            if request.permissions_reassing
            else 0
        )

        return KpiResponse(
            total_action_items=total_action_items,
            total_action_closed=total_action_closed,
            assigned_to_me=assigned_to_me,
            assigned_to_me_closed=assigned_to_me_closed,
            related_to_me=related_to_me,
            pending_approvals=pending_approvals,
            pending_verifications=pending_verifications,
            overdue=overdue,
            my_approvals=my_approvals,
            my_verifications=my_verifications,
            my_challenges=my_challenges,
            my_extends=my_extends,
            my_reassignment=my_reassignment,
        )

    def _count_actions(
        self,
        request: CountActionsRequest,
        *,
        is_closed: bool = False,
    ) -> int:
        return int(
            self._cognite_client.data_modeling.instances.aggregate(
                view=self._action_view,
                aggregates=data_modeling.aggregations.Count("externalId"),
                filter=data_modeling.filters.And(
                    *self._get_action_default_filters(
                        request,
                        request.reporting_site,
                        is_closed=is_closed,
                    ),
                ),
            ).value,
        )

    def _get_actions(
        self,
        params: GetKpisRequest,
        *,
        is_closed: bool = False,
    ) -> GetActionResponse:
        approval_workflow_property = self._action_view.as_property_ref(
            "approvalWorkflow",
        )
        select_clause = self._get_select_kpi_clause()

        with_clause = {
            "generalActions": data_modeling.query.NodeResultSetExpression(
                filter=data_modeling.filters.Or(
                    data_modeling.filters.And(
                        *self._get_action_default_filters(
                            params,
                            params.reporting_site,
                            is_closed=is_closed,
                        ),
                        data_modeling.filters.Or(
                            data_modeling.filters.Equals(
                                self._action_view.as_property_ref("owner"),
                                params.user_azure_attribute,
                            ),
                            data_modeling.filters.Equals(
                                self._action_view.as_property_ref("assignedTo"),
                                params.user_azure_attribute,
                            ),
                        ),
                    ),
                    data_modeling.filters.And(
                        *self._get_action_default_filters(
                            params,
                            params.reporting_site,
                            (
                                [
                                    ActionStatusEnum.DUE_DATE_EXTENSION_PERIOD,
                                    ActionStatusEnum.REASSIGNMENT_PERIOD,
                                ]
                                if not is_closed
                                else ["-"]
                            ),
                        ),
                    ),
                ),
                limit=LIMIT,
            ),
        }

        select_clause_dict = {
            "generalActions": select_clause,
        }

        if not is_closed:
            with_clause.update(self._get_approval_workflow_result_sets(params))
            with_clause["actionsByPendingVerification"] = (
                data_modeling.query.NodeResultSetExpression(
                    from_="approvalWorkflowByVerification",
                    filter=data_modeling.filters.And(
                        *self._get_action_default_filters(
                            params,
                            params.reporting_site,
                        ),
                    ),
                    through=approval_workflow_property,
                    direction="inwards",
                    limit=LIMIT,
                )
            )
            with_clause["actionsByPedingApproval"] = (
                data_modeling.query.NodeResultSetExpression(
                    from_="approvalWorkflowByApproval",
                    filter=data_modeling.filters.And(
                        *self._get_action_default_filters(
                            params,
                            params.reporting_site,
                        ),
                    ),
                    through=approval_workflow_property,
                    direction="inwards",
                    limit=LIMIT,
                )
            )

            select_clause_dict["actionsByPendingVerification"] = select_clause
            select_clause_dict["actionsByPedingApproval"] = select_clause

        data = self._cognite_client.data_modeling.instances.query(
            data_modeling.query.Query(
                with_=with_clause,
                select=select_clause_dict,
            ),
        )

        return self.map_kpi_result(data, is_closed=is_closed)

    def map_kpi_result(
        self,
        data: data_modeling.query.QueryResult,
        *,
        is_closed: bool = False,
    ) -> GetActionResponse:
        """
        Map the query result into categorized action responses.

        This method processes the query result and separates the action items
        into three categories: pending approval, pending verification, and general actions.
        If `is_closed` is True, the pending approval and verification lists are returned empty.

        Args:
            data (data_modeling.query.QueryResult): The query result containing nodes from the KPI query.
            is_closed (bool, optional): Whether the KPI refers to closed actions. Defaults to False.

        Returns:
            GetActionResponse: The structured response containing categorized lists of action items.

        """
        actions_by_pending_verification = ActionResult.from_node_list(
            data.get_nodes("actionsByPendingVerification") if not is_closed else [],
        )
        actions_by_pending_approval = ActionResult.from_node_list(
            data.get_nodes("actionsByPedingApproval") if not is_closed else [],
        )
        general_actions = ActionResult.from_node_list(data.get_nodes("generalActions"))

        return GetActionResponse(
            actions_by_pending_approval=list(actions_by_pending_approval),
            actions_by_pending_verification=list(actions_by_pending_verification),
            general_actions=list(general_actions),
        )

    def _get_select_kpi_clause(self) -> data_modeling.query.Select:
        return data_modeling.query.Select(
            sources=[
                data_modeling.query.SourceSelector(
                    source=self._action_view,
                    properties=[
                        "owner",
                        "assignedTo",
                        "currentStatus",
                        "displayDueDate",
                        "reportingSite",
                        "reportingUnit",
                        "category",
                        "subCategory",
                        "siteSpecificCategory",
                    ],
                ),
            ],
        )

    def _contains_filter(
        self,
        property_name: str,
        external_ids: list[str],
    ) -> data_modeling.filters.ContainsAny:
        return contains_sdk_filter(
            property_name,
            external_ids,
            self._action_view,
        )

    def _get_kpi_space_filter(
        self,
        request: GetKpisRequest,
    ) -> data_modeling.filters.Or:
        filter_private = data_modeling.filters.And(
            data_modeling.filters.SpaceFilter(
                request.get_filter_spaces(private_space=True),
            ),
            self._contains_filter("views", request.views_private),
        )

        filter_site = data_modeling.filters.SpaceFilter(
            request.get_filter_spaces(private_space=False),
        )

        return data_modeling.filters.Or(filter_site, filter_private)

    def _get_action_default_filters(
        self,
        request: GetKpisRequest,
        reporting_site: list[dict[str, str]],
        status: list[str] | None = None,
        *,
        is_closed: bool = False,
    ) -> list[data_modeling.Filter]:
        return [
            self._get_kpi_space_filter(request),
            data_modeling.filters.In(
                self._action_view.as_property_ref("reportingSite"),
                reporting_site,
            ),
            data_modeling.filters.In(
                self._action_view.as_property_ref("currentStatus"),
                (
                    ACTION_COLSED_STATUS
                    if is_closed
                    else (
                        ACTION_ACTIVE_STATUS
                        if not status
                        else self.create_status_nodes(status)
                    )
                ),
            ),
        ]

    def _get_approval_workflow_result_sets(
        self,
        request: GetKpisRequest,
    ) -> dict[str, data_modeling.query.ResultSetExpression]:
        """
        Retrieve the result sets for the approval workflow based on the provided request.

        Args:
            request (GetKpisRequest): The request containing parameters to filter and retrieve
                                    the approval workflow data.

        Returns:
            dict[str, data_modeling.query.ResultSetExpression]: A dictionary where the keys are
                                                            string identifiers and the values are
                                                            corresponding result set expressions
                                                            representing the approval workflow data.

        """
        description_property = self._approval_workflow_step_view.as_property_ref(
            "description",
        )
        status_property = self._approval_workflow_step_view.as_property_ref("status")
        approval_workflow_property = self._approval_workflow_step_view.as_property_ref(
            "approvalWorkflow",
        )

        descriptions = [
            ApprovalWorkflowStepDescriptionEnum.VERIFICATION,
            ApprovalWorkflowStepDescriptionEnum.APPROVAL,
        ]
        return {
            "approvalWorkflowStepByUser": data_modeling.query.EdgeResultSetExpression(
                filter=data_modeling.filters.And(
                    data_modeling.filters.Equals(
                        ["edge", "type"],
                        APPROVAL_WORKFLOW_STEP_TO_USERS,
                    ),
                    data_modeling.filters.Equals(["edge", "endNode"], request.user),
                ),
                direction="inwards",
                node_filter=data_modeling.filters.And(
                    data_modeling.filters.In(
                        status_property,
                        APPROVAL_WORKFLOW_STEP_ACTIVE_STATUS,
                    ),
                    data_modeling.filters.In(
                        description_property,
                        descriptions,
                    ),
                ),
                max_distance=1,
                limit=LIMIT,
            ),
            "approvalWorkflowStepByVerification": data_modeling.query.NodeResultSetExpression(
                from_="approvalWorkflowStepByUser",
                filter=data_modeling.filters.Equals(
                    description_property,
                    ApprovalWorkflowStepDescriptionEnum.VERIFICATION,
                ),
                limit=LIMIT,
            ),
            "approvalWorkflowStepByApproval": data_modeling.query.NodeResultSetExpression(
                from_="approvalWorkflowStepByUser",
                filter=data_modeling.filters.Equals(
                    description_property,
                    ApprovalWorkflowStepDescriptionEnum.APPROVAL,
                ),
                limit=LIMIT,
            ),
            "approvalWorkflowByVerification": data_modeling.query.NodeResultSetExpression(
                from_="approvalWorkflowStepByVerification",
                through=approval_workflow_property,
                limit=LIMIT,
            ),
            "approvalWorkflowByApproval": data_modeling.query.NodeResultSetExpression(
                from_="approvalWorkflowStepByApproval",
                through=approval_workflow_property,
                limit=LIMIT,
            ),
        }

    def create_status_nodes(self, external_ids: list[str]) -> list[dict[str, str]]:
        """
        Generate nodes for the given external IDs.

        Args:
            external_ids (list[str]): A list of external IDs to create nodes for.

        Returns:
            list[dict]: A list of dictionaries representing the generated nodes,
                        each containing the external ID and associated space information.

        """
        return [
            {"externalId": external_id, "space": DataSpaceEnum.AIM_REF_DATA_SPACE}
            for external_id in external_ids
        ]
