# Action Items Creator Function

This project is an Azure Function developed in Python. To set up the local environment and run the function, follow these steps.

## Requirements

- Python 3.11
- Azure Functions Core Tools v4

## Environment Setup

1. Open a terminal and navigate to the project directory;

   ```shell
   cd action-items-creator-function
   ```

2. Run the following command to create a virtual environment named `.venv`:

   ```shell
   python -m venv .venv
   or
   python3.11 -m venv .venv
   ```

3. Run the following command to change the directory to the Scripts folder inside the virtual environment:

   ```shell
   cd .\.venv\Scripts\
   ```

4. Run the following command to activate the virtual environment:

   ```shell
   .\activate
   ```

5. Go back to the project directory:

   ```shell
   cd ..\..
   ```

6. Run the following command to install the packages listed in the requirements file:

   ```shell
   pip install --no-cache-dir -r requirements.txt
   ```

7. Create the `local.settings.json` file in the `action-items-creator-function` directory with the following content:

   ```
   {
      "IsEncrypted": false,
      "Values": {
         "AzureWebJobsStorage": "UseDevelopmentStorage=true",
         "FUNCTIONS_WORKER_RUNTIME": "python",
         "FUNCTIONS_WORKER_PROCESS_COUNT": "8"
      },
      "Host": {
         "CORS": "*"
      }
   }
   ```

8. In the `action-items-creator-function/function_app.py` file, comment out the following lines:
   app.register_functions(event_processor_bp)
   app.register_functions(recurrence_processor_bp)
   app.register_functions(overdue_notifier_bp)

9. Update the `.env` file in the `action-items-creator-function` directory to ensure it includes the following settings:

   ```
   AUTH_CLIENT_ID, AUTH_SECRET, NOTIFICATIONS_CLIENT_ID, NOTIFICATIONS_SECRET
   ```

10. Update the `.env` file in the `portal` directory to ensure it includes:

    ```
    NEXT_PUBLIC_AZURE_FUNCTION_URL='http://localhost:7071'
    ```

11. Start the Azure Functions runtime locally:
    ```shell
    func start
    ```

## Debug Setup

1. To debug the function, add the `debugger_function.py` file in the `action-items-creator-function`. Ask someone from the team to provide the necessary file.
