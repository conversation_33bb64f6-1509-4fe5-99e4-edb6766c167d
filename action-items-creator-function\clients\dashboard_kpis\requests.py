from datetime import date
from typing import Optional

from pydantic import BaseModel, ConfigDict, computed_field
from pydantic.alias_generators import to_camel

from clients.core.constants import (
    USER_AZURE_ATTRIBUTE_PREFIX,
    ActionStatusClearEnum,
    ActionStatusClosedEnum,
    ActionStatusEnum,
    DataSpaceEnum,
)
from utils.space_utils import get_transactional_space_from_site_id

from .constants import DashboardTabsEnum


def create_node_dict(external_id: str, space: str) -> dict[str, str]:
    """
    Construct a dictionary representing a node with the specified external ID and space.

    Args:
        external_id (str): The unique identifier of the node.
        space (str): The data space or domain to which the node belongs, indicating its categorization
                     or grouping within the system.

    Returns:
        dict[str, str]: A dictionary with keys 'externalId' and 'space', representing the node. The
                        dictionary allows for easy reference of the node's unique ID and its associated
                        data space.

    """
    return {"externalId": external_id, "space": space}


class KpisFilter(BaseModel):
    """Represents filters for querying KPIs with various criteria such as reporting site, due date, categories,assignees, statuses, and more."""

    model_config = ConfigDict(
        alias_generator=to_camel,
        populate_by_name=True,
        from_attributes=True,
    )

    reporting_site_external_id: str

    due_date_gte: date | None = None
    due_date_lt: date | None = None

    reporting_unit_external_ids: list[str] | None = None
    reporting_location_external_ids: list[str] | None = None

    category_external_ids: list[str] | None = None
    subcategory_external_ids: list[str] | None = None
    site_specific_category_external_ids: list[str] | None = None

    status_external_ids: list[ActionStatusEnum] | None = [
        status
        for status in ActionStatusEnum
        if status not in [ActionStatusEnum.DELETED, ActionStatusEnum.ACTIVE]
    ]

    update_status: list[tuple[Optional[date], Optional[date]]] = []
    assignee_external_ids: list[str] | None = None

    only_private: bool | None = None

    not_in_assignee_external_ids: list[str] | None = None
    source_event_title_eq: str | None = None

    @computed_field
    @property
    def reporting_site(self) -> dict[str, str]:
        """Returns the reporting site as a dictionary with externalId and space."""
        return create_node_dict(
            self.reporting_site_external_id,
            DataSpaceEnum.REF_DATA_SPACE,
        )

    @computed_field
    @property
    def space(self) -> str:
        """Returns the space associated with the reporting site based on its externalId."""
        return get_transactional_space_from_site_id(self.reporting_site_external_id)

    @computed_field
    @property
    def reporting_units(self) -> list[dict[str, str]]:
        """Returns a list of reporting units as dictionaries with externalId and space."""
        return [
            create_node_dict(unit_id, DataSpaceEnum.REF_DATA_SPACE)
            for unit_id in self.reporting_unit_external_ids or []
        ]

    @computed_field
    @property
    def reporting_locations(self) -> list[dict[str, str]]:
        """Returns a list of reporting locations as dictionaries with externalId and space."""
        return [
            create_node_dict(location_id, DataSpaceEnum.REF_DATA_SPACE)
            for location_id in self.reporting_location_external_ids or []
        ]

    @computed_field
    @property
    def categories(self) -> list[dict[str, str]]:
        """Returns a list of categories as dictionaries with externalId and space."""
        return [
            create_node_dict(category_id, DataSpaceEnum.AIM_REF_DATA_SPACE)
            for category_id in self.category_external_ids or []
        ]

    @computed_field
    @property
    def subcategories(self) -> list[dict[str, str]]:
        """Returns a list of subcategories as dictionaries with externalId and space."""
        return [
            create_node_dict(subcategory_id, DataSpaceEnum.AIM_REF_DATA_SPACE)
            for subcategory_id in self.subcategory_external_ids or []
        ]

    @computed_field
    @property
    def site_specific_categories(self) -> list[dict[str, str]]:
        """Returns a list of site-specific categories as dictionaries with externalId and space."""
        return [
            create_node_dict(category_id, self.space)
            for category_id in self.site_specific_category_external_ids or []
        ]

    @computed_field
    @property
    def status(self) -> list[dict[str, str]]:
        """Returns a list of statuses as dictionaries with externalId and space."""
        return [
            create_node_dict(status_id, DataSpaceEnum.AIM_REF_DATA_SPACE)
            for status_id in self.status_external_ids or []
        ]

    @computed_field
    @property
    def assignees(self) -> list[dict[str, str]]:
        """Returns a list of assignees as dictionaries with externalId and space."""
        return [
            create_node_dict(
                USER_AZURE_ATTRIBUTE_PREFIX + external_id,
                DataSpaceEnum.UMG_DATA_SPACE,
            )
            for external_id in self.assignee_external_ids or []
        ]

    @computed_field
    @property
    def default_overdue_statuses(self) -> list[dict[str, str]]:
        """Returns a list of default overdue statuses as dictionaries with externalId and space."""
        return [
            create_node_dict(status_id, DataSpaceEnum.AIM_REF_DATA_SPACE)
            for status_id in list(ActionStatusClearEnum.__members__.values())
        ]

    @computed_field
    @property
    def closed_statuses(self) -> list[dict[str, str]]:
        """Returns a list of closed statuses as dictionaries with externalId and space."""
        return [
            create_node_dict(status_id, DataSpaceEnum.AIM_REF_DATA_SPACE)
            for status_id in list(ActionStatusClosedEnum.__members__.values())
        ]


class GetDashboardKpisRequest(BaseModel):
    """Represents a request to fetch dashboard KPIs, including user information and optional filters for the KPIs."""

    model_config = ConfigDict(
        alias_generator=to_camel,
        populate_by_name=True,
        from_attributes=True,
    )

    active_user_email: str
    user_external_id: str | None = None
    active_user_roles_ids: list[str] | None = None
    active_user_teams_ids: list[str] | None = None

    tab: DashboardTabsEnum | None = None

    filters: KpisFilter

    def get_filter_spaces(
        self,
        *,
        all_sites: bool = False,
        private_space: bool | None = None,
    ) -> list[str]:
        """
        Construct a list of data spaces to filter KPI-related queries based on the given criteria.

        Args:
            all_sites (bool, optional): Whether to include all available data spaces. Defaults to False.
            private_space (bool | None, optional):
                - True: Returns only the private space.
                - False: Returns the main filter's space.
                - None: Combines spaces based on the `all_sites` flag.

        Returns:
            list[str]: A list of data spaces based on the specified criteria, enabling filtering of KPI-related queries
                    based on the desired space configuration.

        """
        if private_space:
            return [DataSpaceEnum.PRIVATE_SPACE]
        if private_space is not None:
            return [self.filters.space]
        if all_sites:
            return [
                DataSpaceEnum.PRIVATE_SPACE,
                DataSpaceEnum.COR_SPACE,
                self.filters.space,
            ]
        return [DataSpaceEnum.PRIVATE_SPACE, self.filters.space]

    @computed_field
    @property
    def views_private(self) -> list[str]:
        """Generate a list of users, roles, and teams for private view access."""
        if not self.user_external_id:
            return []

        return (
            [USER_AZURE_ATTRIBUTE_PREFIX + self.user_external_id]
            + (self.active_user_roles_ids or [])
            + (self.active_user_teams_ids or [])
        )


class GetChartsRequest(GetDashboardKpisRequest):
    """Represents a request to fetch chart data, extending GetDashboardKpisRequest with grouping and pagination parameters."""

    group_by: list[str]
    items_per_page: int = 10
