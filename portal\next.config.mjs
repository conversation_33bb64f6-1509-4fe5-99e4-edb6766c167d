/** @type {import('next').NextConfig} */

import { fileURLToPath } from 'url'
import { dirname, resolve } from 'path'
import './src/app/common/utils/polyfills.js'
import TerserPlugin from 'terser-webpack-plugin'

const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

const nextConfig = {
    webpack: (config, { dev, isServer, webpack, nextRuntime }) => {
        config.resolve.alias = {
            ...config.resolve.alias,
            '@celanese/celanese-ui': resolve(__dirname, 'node_modules/@celanese/celanese-ui/build/esm/index.ts'),
        }

        config.optimization = {
            ...config.optimization,
            chunkIds: 'named',
            moduleIds: 'named',
            minimize: true,
            minimizer: [
                new TerserPlugin({
                    terserOptions: {
                        keep_classnames: true,
                        keep_fnames: true,
                    },
                }),
            ],
        }

        return config
    },
}

export default nextConfig
