# Manifest file downloaded from fusion
externalId: tr-AIM-COR-ALL-RAW-ICAP-VAL-ACTION-MIGRATION-PROGRESS
name: AIM-COR-ALL-RAW-ICAP-VAL-ACTION-MIGRATION-PROGRESS
query: >
  SELECT
    	icap_action.key,
    	icap_action.key AS `1_ActionItemID`,
    	icap_action.ActionDescription AS `2_ActionTitle`,
    	icap_action.Cause AS `3_ActionDescription`,
    	icap_action_category.ActionCategoryName AS `4_ActionCategory`,
    	icap_user_assignee.Email AS `5_PPREmail`,
    	icap_user_secondary_assignee.Email AS `6_SecondaryPPREmail`,
    	icap_action_status.ActionStatusName AS `7_ActionStatus`,
    	DATE(icap_action.ActionStartDate) AS `8_ActionStartDate`,
    	DATE(icap_action.ActionDueDate) AS `9_ActionDueDate`,
    	DATE(icap_action.ActionActualCompletionDate) AS `10_ActionCompletedDate`,
    	icap_action_priority.ActionPriority AS `11_Priority`,
    	icap_action.EvidenceRequired AS `12_EvidenceRequired`,
    	icap_unit.UnitName AS `13_AreaName`,
    	icap_event.key AS `14_EventID`,
    	icap_moc.key AS `15_MOCID`,
    	icap_mooc.key AS `16_MOOCID`,
    	icap_event.EventTitle AS `17_EventTitle`,
    	icap_user_owner.Email AS `18_EventPrimaryOwnerEmail`,
    	icap_event_category.EventCategoryName AS `19_EventCategory`,
    	isnotnull(aim_action.externalId) AS `20_isMigrated`,
    	coalesce(icap_event.Private, icap_action.Private) AS `21_isPrivate`,
    	NOT icap_event.EventCategoryID IN (5, 9, 15, 21, 27) AS `22_isEditable`,
      concat(
        if(isnull(stg_action.category), 'Missing Category; ', ''),
        if(isnull(stg_action.subCategory), 'Missing Sub Category 1; ', ''),
        if(isnull(stg_action.dueDate), 'Missing Due Date; ', ''),
        if(isnull(stg_action.assignmentDate), 'Missing Assignment Date; ', ''),
        if(isnull(stg_action.assignedTo), 'Missing Assignee; ', ''),
        if(isnull(stg_action.owner), 'Missing Owner; ', ''),
        if(isnull(stg_action.createdBy), 'Missing Created By; ', ''),
        if(isnull(stg_action.currentStatus), 'Missing Status; ', ''),
        if(isnull(stg_action.reportingUnit), 'Missing Reporting Unit - Invalid Area Mapping; ', ''),
        if(
            isnotnull(stg_action.reportingSite) 
            AND isnotnull(stg_action.reportingUnit)
            AND NOT startswith(
              stg_action.reportingUnit.externalId,
              replace(stg_action.reportingSite.externalId, 'STS-', 'UNT-')
            ), 
            'Reporting Unit from another Site; ', 
            ''
        ),
        if(
            isnotnull(stg_action.reportingSite) 
            AND isnotnull(stg_action.reportingLocation)
            AND NOT startswith(
              stg_action.reportingLocation.externalId,
              replace(stg_action.reportingSite.externalId, 'STS-', 'LOC-')
            ), 
            'Reporting Location from another Site; ', 
            ''
        )
  	) AS `23_errorDetails`,
    	aim_action.externalId AS `24_[AIM] Action ExternalId`,
    	aim_action.space AS `25_[AIM] Action Space`,
    	aim_action.title AS `26_[AIM] Action Title`,
    	aim_action.description AS `27_[AIM] Action Description`,
    	aim_action_category.description AS `28_[AIM] Action Category`,
    	aim_action_sub_category.description AS `29_[AIM] Action Sub Category 1`,
    	aim_action_site_category.description AS `30_[AIM] Action Sub Category 2`,
    	replace(aim_action.assignedTo.externalId, 'UserAzureAttribute_', '') AS `31_[AIM] Assignee`,
    	replace(aim_action.owner.externalId, 'UserAzureAttribute_', '') AS `32_[AIM] Owner`,
    	replace(aim_action.createdBy.externalId, 'UserAzureAttribute_', '') AS `33_[AIM] Created By`,
    	aim_action_status.name AS `34_[AIM] Status`,
    	aim_action.assignmentDate AS `35_[AIM] Assignment Date`,
    	aim_action.dueDate AS `36_[AIM] Action Due Date`,
    	aim_action.conclusionDate AS `37_[AIM] Conclusion Date`,
    	aim_action.priority AS `38_[AIM] Priority`,
    	aim_action.evidenceRequired AS `39_[AIM] Evidence Required`,
    	if(
    		isnotnull(ah_reporting_unit.externalId),
    		concat(ah_reporting_unit.description, ' (', ah_reporting_unit.name, ')'),
    		''
    	) AS `40_[AIM] Reporting Unit`,
      if(
    		isnotnull(ah_reporting_location.externalId),
    		concat(ah_reporting_location.description, ' (', ah_reporting_location.name, ')'),
    		''
    	) AS `41_[AIM] Reporting Location`,
    	aim_event.externalId AS `42_[AIM] Event ExternalId`,
    	aim_event.space AS `43_[AIM] Event Space`,
    	aim_event.title AS `44_[AIM] Event Title`,
    	replace(aim_event.owner.externalId, 'UserAzureAttribute_', '') AS `45_[AIM] Event Primary Owner`,
      aim_event_category.description AS `46_[AIM] Event Category`,
    	aim_event_sub_category.description AS `47_[AIM] Event Sub Category 1`,
    	aim_event_site_category.description AS `48_[AIM] Event Sub Category 2`,
    	stg_action.reportingSite.externalId AS reporting_site
  FROM `ICAP-COR`.`AXN-tblActionItem` icap_action

  LEFT JOIN `ICAP-COR`.`AXN-tblActionCategory` icap_action_category
  	ON icap_action_category.key = cast(icap_action.ActionCategoryID AS STRING)
  LEFT JOIN `ICAP-COR`.`USR-tblUser` icap_user_assignee
  	ON icap_user_assignee.key = cast(icap_action.PPRID AS STRING)
  LEFT JOIN `ICAP-COR`.`USR-tblUser` icap_user_secondary_assignee
  	ON icap_user_secondary_assignee.key = cast(icap_action.SecondaryPPRID AS STRING)
  LEFT JOIN `ICAP-COR`.`AXN-tblActionStatus` icap_action_status
  	ON icap_action_status.key = cast(icap_action.ActionStatusID AS STRING)
  LEFT JOIN `ICAP-COR`.`AXN-tblActionPriority` icap_action_priority
  	ON icap_action_priority.key = cast(icap_action.ActionPriorityID AS STRING)
  LEFT JOIN `ICAP-COR`.`EVNT-tblEvent` icap_event
  	ON icap_event.key = cast(icap_action.EventID AS STRING)
  LEFT JOIN `ICAP-COR`.`UNT-tblUnit` icap_unit
    	ON icap_unit.key = cast(icap_event.UnitID AS STRING)
  LEFT JOIN `ICAP-COR`.`MOC-tblMOC` icap_moc
  	ON cast(icap_moc.EventID AS STRING) = icap_event.key
    	AND icap_event.EventCategoryID = 15
  LEFT JOIN `ICAP-COR`.`MOOC-tblMOOC` icap_mooc
  	ON cast(icap_mooc.EventID AS STRING) = icap_event.key
  	AND icap_event.EventCategoryID = 21
  LEFT JOIN `ICAP-COR`.`USR-tblUser` icap_user_owner
  	ON icap_user_owner.key = cast(icap_event.EventAddedByOwner AS STRING)
  LEFT JOIN `ICAP-COR`.`EVNT-tblEventCategory` icap_event_category
  	ON icap_event_category.key = cast(icap_event.EventCategoryID AS STRING)
  LEFT JOIN `AIM-COR`.`ICAP-STG-Action` stg_action
  	ON stg_action.key = icap_action.key
  LEFT JOIN cdf_data_models("AIM-COR-ALL-DMD", "ActionItemManagementDOM", "6_0_0", "Action") aim_action
  	ON aim_action.externalId = stg_action.externalId
  	AND aim_action.space = stg_action.space
  LEFT JOIN cdf_data_models("AIM-COR-ALL-DMD", "ActionItemManagementDOM", "6_0_0", "ActionItemCategory") aim_action_category
  	ON aim_action.category.externalId = aim_action_category.externalId
  	AND aim_action.category.space = aim_action_category.space
  LEFT JOIN cdf_data_models("AIM-COR-ALL-DMD", "ActionItemManagementDOM", "6_0_0", "ActionItemSubCategory") aim_action_sub_category
  	ON aim_action.subCategory.externalId = aim_action_sub_category.externalId
  	AND aim_action.subCategory.space = aim_action_sub_category.space
  LEFT JOIN cdf_data_models("AIM-COR-ALL-DMD", "ActionItemManagementDOM", "6_0_0", "SiteSpecificCategory") aim_action_site_category
  	ON aim_action.siteSpecificCategory.externalId = aim_action_site_category.externalId
  	AND aim_action.siteSpecificCategory.space = aim_action_site_category.space
  LEFT JOIN cdf_data_models("AIM-COR-ALL-DMD", "ActionItemManagementDOM", "6_0_0", "ActionItemStatus") aim_action_status
  	ON aim_action.currentStatus.externalId = aim_action_status.externalId
  	AND aim_action.currentStatus.space = aim_action_status.space
  LEFT JOIN cdf_data_models("AIM-COR-ALL-DMD", "ActionItemManagementDOM", "6_0_0", "ReportingUnit") ah_reporting_unit
  	ON aim_action.reportingUnit.externalId = ah_reporting_unit.externalId
  	AND aim_action.reportingUnit.space = ah_reporting_unit.space
  LEFT JOIN cdf_data_models("AIM-COR-ALL-DMD", "ActionItemManagementDOM", "6_0_0", "ReportingLocation") ah_reporting_location
  	ON aim_action.reportingLocation.externalId = ah_reporting_location.externalId
  	AND aim_action.reportingLocation.space = ah_reporting_location.space
  LEFT JOIN cdf_data_models("AIM-COR-ALL-DMD", "ActionItemManagementDOM", "6_0_0", "SourceEvent", "actions") aim_event_to_actions
  	ON aim_action.externalId = aim_event_to_actions.endNode.externalId
  	AND aim_action.space = aim_event_to_actions.endNode.space
  	AND aim_action.space = aim_event_to_actions.space
  	AND aim_action.space = aim_event_to_actions.startNode.space
  LEFT JOIN cdf_data_models("AIM-COR-ALL-DMD", "ActionItemManagementDOM", "6_0_0", "SourceEvent") aim_event
  	ON aim_event_to_actions.startNode.externalId = aim_event.externalId
  	AND aim_event_to_actions.startNode.space = aim_event.space
  LEFT JOIN cdf_data_models("AIM-COR-ALL-DMD", "ActionItemManagementDOM", "6_0_0", "ActionItemCategory") aim_event_category
  	ON aim_event.category.externalId = aim_event_category.externalId
  	AND aim_event.category.space = aim_event_category.space
  LEFT JOIN cdf_data_models("AIM-COR-ALL-DMD", "ActionItemManagementDOM", "6_0_0", "ActionItemSubCategory") aim_event_sub_category
  	ON aim_event.subCategory.externalId = aim_event_sub_category.externalId
  	AND aim_event.subCategory.space = aim_event_sub_category.space
  LEFT JOIN cdf_data_models("AIM-COR-ALL-DMD", "ActionItemManagementDOM", "6_0_0", "SiteSpecificCategory") aim_event_site_category
  	ON aim_event.siteSpecificCategory.externalId = aim_event_site_category.externalId
  	AND aim_event.siteSpecificCategory.space = aim_event_site_category.space
destination:
  database: AIM-COR
  table: ICAP-VAL-ActionMigrationProgress
  type: raw
ignoreNullFields: true
shared: true
action: upsert
dataSetExternalId: AIM-COR-ALL-DAT
# Specify credentials separately like this:
# authentication:
#   read:
#     clientId: ${READ_CLIENT_ID}
#     clientSecret: ${READ_CLIENT_SECRET}
#     tokenUrl: ${READ_TOKEN_URL}
#     cdfProjectName: ${READ_CDF_PROJECT_NAME}
#     # Optional: If idP requires providing the scopes
#     scopes:
#       - ${READ_SCOPES}
#     # Optional: If idP requires providing the audience
#     audience: ${READ_CDF_AUDIENCE}
#   write:
#     clientId: ${WRITE_CLIENT_ID}
#     clientSecret: ${WRITE_CLIENT_SECRET}
#     tokenUrl: ${WRITE_TOKEN_URL}
#     cdfProjectName: ${WRITE_CDF_PROJECT_NAME}
#     # Optional: If idP requires providing the scopes
#     scopes:
#       - ${WRITE_SCOPES}
#     # Optional: If idP requires providing the audience
#     audience: ${WRITE_CDF_AUDIENCE}
# Or together like this:
# authentication:
#   clientId: ${CLIENT_ID}
#   clientSecret: ${CLIENT_SECRET}
#   tokenUrl: ${TOKEN_URL}
#   # Optional: If idP requires providing the scopes
#   cdfProjectName: ${CDF_PROJECT_NAME}
#   scopes:
#    - ${SCOPES}
#   # Optional: If idP requires providing the audience
#   audience: ${AUDIENCE}