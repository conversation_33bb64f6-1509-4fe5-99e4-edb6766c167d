from typing import Optional

from pydantic import Field, computed_field

from clients.core.constants import (
    AGGREGATE_LIMIT,
    DataSpaceEnum,
)
from clients.core.models import BaseCamelCaseModel, GlobalModel
from utils.space_utils import (
    get_transactional_space_from_site_id,
    is_valid_reporting_site_id,
)


class GetCategoryConfigurationByFilterRequest(GlobalModel):
    """Request model for retrieving category configuration based on filters."""

    reporting_site_external_id: str | list[str]

    category_id: Optional[str] = Field(None, min_length=1)
    sub_category_id: Optional[str] = Field(None, min_length=1)
    site_specific_category_id: Optional[str] = Field(None, min_length=1)

    non_null_extension_approver_role: Optional[bool] = False

    page_size: int = AGGREGATE_LIMIT
    cursor: str | None = None

    def get_filter_spaces(self, all_sites: bool = False) -> list[str]:
        """
        Return a list of data spaces to use for filtering based on reporting site(s).

        Args:
            all_sites (bool): Whether to include COR space along with private spaces.

        Returns:
            list[str]: A list of data spaces.

        """
        if all_sites:
            return [DataSpaceEnum.COR_SPACE, *self.spaces]
        return [DataSpaceEnum.PRIVATE_SPACE, *self.spaces]


class GetCategoriesRequest(BaseCamelCaseModel):
    """Request model for retrieving categories."""

    name: str | None = None


class GetSubCategoriesRequest(BaseCamelCaseModel):
    """Request model for retrieving subcategories."""

    name: str | None = None


class GetSiteSpecificCategoriesRequest(BaseCamelCaseModel):
    """Request model for retrieving site-specific categories."""

    reporting_site_external_id: str
    name: str | None = None

    @computed_field
    @property
    def space(self) -> str:
        """
        Determine the data space string based on the reporting site external ID.

        Returns:
            str: Data space string.

        """
        return get_transactional_space_from_site_id(self.reporting_site_external_id)


class GetSiteSpecificCategoriesBySitesRequest(BaseCamelCaseModel):
    """
    Request model for retrieving reporting units based on multiple site IDs and optional search filters.

    Attributes:
        reporting_site_external_id (list[str]): List of external IDs for the reporting sites.
        search (Optional[str]): An optional search term to filter reporting units.
        search_properties (list[str]): List of properties to search for (defaults to ["name", "description"]).
        spaces (list[str]): Computed property that extracts the spaces from each reporting site's external ID.

    Methods:
        spaces: Returns a list of spaces extracted from the `reporting_site_external_ids`.

    """

    reporting_site_external_ids: list[str]

    search: str | None = None
    search_properties: list[str] = ["name", "description"]

    @computed_field
    @property
    def spaces(self) -> list[str]:
        """Compute the data space identifier(s) based on the reporting site(s)."""
        return [
            get_transactional_space_from_site_id(site_id)
            for site_id in self.reporting_site_external_ids
            if is_valid_reporting_site_id(site_id)
        ]
