from typing import Annotated

from clients.core.models import Node
from clients.core.validators import edge_unwraper_validator
from clients.reporting_site.models import ReportingSiteResult


class ReportingLocationResult(Node):
    """Represent the result of a reporting location."""

    description: str | None = None
    reporting_unit: Node | None = None
    reporting_sites: (
        Annotated[list[ReportingSiteResult], edge_unwraper_validator] | None
    ) = None
