from pydantic import computed_field

from clients.core.models import BaseCamelCaseModel
from utils.space_utils import get_transactional_space_from_site_id


class BaseTemplateRequest(BaseCamelCaseModel):
    """Base template request model with filters and permissions."""

    reporting_site_external_id: str

    @computed_field
    @property
    def space(self) -> str:
        """Compute the data space identifier based on the reporting site."""
        return get_transactional_space_from_site_id(self.reporting_site_external_id)


class GetTemplatesByUserIdRequest(BaseTemplateRequest):
    """Request model for retrieving an template by its user external ID."""

    active_user_email: str
    active_user_external_id: str | None = None
