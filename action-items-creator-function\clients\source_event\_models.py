from typing import Annotated, Optional

from clients.core.models import BaseEntity, DescribableEntity, Node
from clients.core.validators import edge_unwraper_validator, str_or_default_validator


class _User(Node):
    first_name: Annotated[str, str_or_default_validator]
    last_name: Annotated[str, str_or_default_validator]
    email: Annotated[str, str_or_default_validator]


class _UserAzureAttribute(Node):
    user: _User | None


class Equipment(DescribableEntity):
    number: str | None


class ApprovalWorkflowStep(Node):
    users: Annotated[list[Node], edge_unwraper_validator]


class ApprovalWorkflow(BaseEntity):
    steps: Annotated[list[ApprovalWorkflowStep], edge_unwraper_validator]


class Action(BaseEntity):
    view_users: Annotated[list[Node], edge_unwraper_validator]
    view_roles: Annotated[list[Node], edge_unwraper_validator]
    view_teams: Annotated[list[Node], edge_unwraper_validator]
    assigned_to: Optional[Node] = None
    owner: Optional[Node] = None
    approval_workflow: Optional[ApprovalWorkflow] = None
    is_private: Optional[bool] = False
