from datetime import date
from typing import Any
from pydantic import BaseModel, ConfigDict, computed_field
from pydantic.alias_generators import to_camel

from clients.core.models import Node
from clients.core.constants import ActionStatusEnum


class ActionResult(Node):
    current_status: Node
    display_due_date: date | None = None

    def evaluate(
        self,
        due_date_lt: date | None,
        due_date_gte: date | None,
        status: list[str] | None,
    ) -> bool:
        """
        Evaluates whether an action satisfies the specified criteria.

        Parameters:
        - due_date_lt (date | None): Upper bound for the due date (exclusive).
        - due_date_gte (date | None): Lower bound for the due date (inclusive).
        - status (list[str] | None): List of statuses to match.

        Returns:
        - bool: True if the action satisfies all criteria, False otherwise.
        """
        return (
            (
                due_date_lt is None
                or (
                    self.display_due_date is not None
                    and self.display_due_date < due_date_lt
                )
            )
            and (
                due_date_gte is None
                or (
                    self.display_due_date is not None
                    and self.display_due_date >= due_date_gte
                )
            )
            and (not status or self.current_status.external_id in status)
        )

    @staticmethod
    def apply_filter(
        actions: list["ActionResult"],
        due_date_lt: date | None,
        due_date_gte: date | None,
        status: list[str] | None,
    ) -> list["ActionResult"]:
        """
        Filters a list of actions based on the specified criteria.

        Parameters:
        - actions (list[ActionResult]): List of actions to filter.
        - due_date_lt (date | None): Upper bound for the due date.
        - due_date_gte (date | None): Lower bound for the due date.
        - status (list[str] | None): List of statuses to match.

        Returns:
        - list[ActionResult]: Filtered list of actions.
        """
        return [
            action
            for action in actions
            if action.evaluate(due_date_lt, due_date_gte, status)
        ]


class GetActionResponse(BaseModel):
    general_actions: list[ActionResult]

    @computed_field
    @property
    def _pending_approval(self) -> int:
        return len(
            ActionResult.apply_filter(
                self.general_actions,
                None,
                None,
                [ActionStatusEnum.PENDING_APPROVAL],
            )
        )

    @computed_field
    @property
    def _pending_verification(self) -> int:
        return len(
            ActionResult.apply_filter(
                self.general_actions,
                None,
                None,
                [ActionStatusEnum.PENDING_VERIFICATION],
            )
        )

    @computed_field
    @property
    def _pending(self) -> int:
        return self._pending_approval + self._pending_verification

    def count_overdue(
        self, due_date_lt: date | None = None, status: list[str] | None = None
    ) -> int:
        """
        Counts the number of overdue actions based on due date and status.

        Parameters:
        - due_date_lt (date | None): Upper bound for the due date.
        - status (list[str] | None): List of statuses to match.

        Returns:
        - int: Number of overdue actions.
        """
        return len(
            ActionResult.apply_filter(
                self.general_actions,
                due_date_lt,
                None,
                status,
            )
        )

    def count_open(self, status: list[str] | None = None) -> int:
        """
        Counts the number of open actions based on status.

        Parameters:
        - status (list[str] | None): List of statuses to match.

        Returns:
        - int: Number of open actions.
        """
        return len(
            ActionResult.apply_filter(
                self.general_actions,
                None,
                None,
                status,
            )
        )

    def count_tasks_within_7_days(
        self, due_date_lt: date | None, due_date_gte: date | None
    ) -> int:
        """
        Counts tasks due within 7 days.

        Parameters:
        - due_date_lt (date | None): Upper bound for the due date.
        - due_date_gte (date | None): Lower bound for the due date.

        Returns:
        - int: Number of tasks due within 7 days.
        """
        return len(
            ActionResult.apply_filter(
                self.general_actions,
                due_date_lt,
                due_date_gte,
                None,
            )
        )

    def count_tasks_within_this_month(
        self, due_date_lt: date | None, due_date_gte: date | None
    ) -> int:
        """
        Counts tasks due within the current month.

        Parameters:
        - due_date_lt (date | None): Upper bound for the due date.
        - due_date_gte (date | None): Lower bound for the due date.

        Returns:
        - int: Number of tasks due within the current month.
        """
        return len(
            ActionResult.apply_filter(
                self.general_actions,
                due_date_lt,
                due_date_gte,
                None,
            )
        )

    def count_due_date_over_30_days(self, due_date_gte: date | None) -> int:
        """
        Counts tasks due in more than 30 days.

        Parameters:
        - due_date_gte (date | None): Lower bound for the due date.

        Returns:
        - int: Number of tasks due in more than 30 days.
        """
        return len(
            ActionResult.apply_filter(
                self.general_actions,
                None,
                due_date_gte,
                None,
            )
        )


class SiteTabKpiResponse(BaseModel):
    model_config = ConfigDict(
        alias_generator=to_camel,
        populate_by_name=True,
        from_attributes=True,
    )

    total_entered: int
    overdue: int
    open: int
    pending: int
    overdue_percentage: float


class SupervisorTabKpiResponse(BaseModel):
    model_config = ConfigDict(
        alias_generator=to_camel,
        populate_by_name=True,
        from_attributes=True,
    )

    employees: int
    overdue: int
    tasks_within_7_days: int
    tasks_within_this_month: int
    due_date_over_30_days: int
    pending_approval: int
    pending_verification: int
