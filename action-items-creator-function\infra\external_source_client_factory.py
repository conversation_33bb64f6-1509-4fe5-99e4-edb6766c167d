from typing import Optional
from uuid import uuid4

from cognite.client.data_classes.data_modeling import DataModel, ViewId

from clients.core.constants import DataModelIdEnum, DataModelSpaceEnum
from clients.core.models import ExternalSourceServiceParams
from clients.external_source.external_source_client import ExternalSourceClient
from infra.core_factory import (
    get_cognite_client_as_service,
    get_data_model,
)
from models.settings import Settings
from services.logging_service import LoggingService


def build_service_params(
    data_model: DataModel[ViewId],
    settings: Settings,
    call_id: str,
) -> ExternalSourceServiceParams:
    """
    Build service parameters for an external source client.

    Args:
        data_model (DataModel[ViewId]): The data model to use.
        settings (Settings): The service settings.
        call_id (str): The unique call identifier.

    Returns:
        ExternalSourceServiceParams: The configured service parameters.

    """
    cognite_client = get_cognite_client_as_service()
    logging_service = LoggingService(call_id)

    return ExternalSourceServiceParams(
        cognite_client=cognite_client,
        data_model=data_model,
        settings=settings,
        logging=logging_service,
    )


class ExternalSourceClientFactory:
    """Factory for creating instances of ExternalSourceClient."""

    @staticmethod
    def retriever(
        settings: Optional[Settings] = None,
        call_id: str = str(uuid4()),
    ) -> ExternalSourceClient:
        """
        Create an ExternalSourceClient with configured parameters.

        Args:
            settings (Optional[Settings], optional): The service settings. Defaults to environment settings if None.
            call_id (str, optional): A unique call identifier. Defaults to a new UUID.

        Returns:
            ExternalSourceClient: An initialized external source client.

        """
        if settings is None:
            settings = Settings.from_env()

        rca_model = get_data_model(
            DataModelSpaceEnum.RCA_DATA_MODEL_SPACE,
            DataModelIdEnum.RCA_DATA_MODEL_ID,
        )
        qr_model = get_data_model(
            DataModelSpaceEnum.QUALITY_RELIABILITY_DATA_MODEL_SPACE,
            DataModelIdEnum.QUALITY_RELIABILITY_DATA_MODEL_ID,
        )
        quality_model = get_data_model(
            DataModelSpaceEnum.QUALITY_DATA_MODEL_SPACE,
            DataModelIdEnum.QUALITY_DATA_MODEL_ID,
        )

        rca_params = build_service_params(rca_model, settings, call_id)
        qr_params = build_service_params(qr_model, settings, call_id)
        quality_params = build_service_params(quality_model, settings, call_id)

        return ExternalSourceClient(rca_params, qr_params, quality_params)
