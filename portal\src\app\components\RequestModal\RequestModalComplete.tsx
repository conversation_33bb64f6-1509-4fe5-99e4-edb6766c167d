import React, { useState } from 'react'
import { Box, styled, Typography, useMediaQuery, useTheme } from '@mui/material'
import { Cln<PERSON>utton, ClnCircularProgress } from '@celanese/ui-lib'
import { SubmitHandler, useForm } from 'react-hook-form'
import { z } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'
import { ModalWrapper } from '../ModalComponent/Modal/ModalWrapper'
import { UploadFiles } from '../UploadFiles/uploadFile'
import { ActionDetailItem } from '@/app/common/models/action-detail'
import GenericTextField from '../FieldsComponent/GenericTextField'
import { AzureFunctionClient } from '@/app/common/clients/azure-function-client'
import { buildUploadFilesRequest } from '@/app/common/utils/files'
import { useCognite } from '@/app/common/hooks'
import { translate } from '@/app/common/utils/generate-translate'
import { FileSourceStepEnum } from '@/app/common/enums/FileSourceStepEnum'
import { UserRolesPermission } from '@celanese/celanese-sdk'
import { SITE_EXTERNAL_ID_REQUIRED_FIELD } from '@/app/common/utils/validate-codes'

interface RequestModelCompleteProps {
    evidenceRequired: boolean
    actionDetails?: ActionDetailItem
    activeUser: UserRolesPermission
    client: AzureFunctionClient | null
    onClose: () => void
    handleAlert: (message: string, error?: boolean) => void
    redirectToLastPage: () => void
    redirectToPage?: () => void
}

const createFormSchema = (evidenceRequired: boolean) =>
    z
        .object({
            comments: z.string().min(10).max(1000),
            attachments: z.array(z.any()).optional(),
        })
        .superRefine((v, ctx) => {
            if (evidenceRequired && v.attachments?.length === 0) {
                ctx.addIssue({
                    path: ['attachments'],
                    code: z.ZodIssueCode.custom,
                })
            }
        })

type RequestSchema = z.infer<ReturnType<typeof createFormSchema>>

const Form = styled('form')({
    width: '100% !important',
    '@media (max-width:600px)': {
        padding: '1.5rem',
    },
})

const actionButtonsStyle = {
    display: 'flex',
    justifyContent: 'flex-end',
    gap: 1,
    '@media (max-width:600px)': {
        flexDirection: 'column',
        gap: '0.5rem',
        alignItems: 'center',
    },
}
export function RequestModalComplete({
    evidenceRequired,
    actionDetails,
    activeUser,
    client,
    onClose,
    handleAlert,
    redirectToPage,
    redirectToLastPage,
}: RequestModelCompleteProps) {
    const formSchema = createFormSchema(evidenceRequired)
    const theme = useTheme()
    const isMobile = useMediaQuery(theme.breakpoints.down('md'))
    const [filesUploaded, setFilesUploaded] = useState<File[]>([])
    const [loadingComplete, setLoadingComplete] = useState<boolean>(false)

    const { cogniteClient } = useCognite()

    const {
        setValue,
        getValues,
        handleSubmit,
        reset,
        control,
        formState: { errors },
    } = useForm<RequestSchema>({
        defaultValues: {
            comments: actionDetails?.assigneeComment ?? '',
            attachments: [],
        },
        resolver: zodResolver(formSchema),
        mode: 'all',
    })

    const handleCancel = () => {
        reset({ comments: '', attachments: [] })
        onClose()
    }

    type ActionDetailEditFormSchema = z.infer<typeof formSchema>

    const [completeDisabled, setCompleteDisabled] = useState(false)

    const submitFn: SubmitHandler<ActionDetailEditFormSchema> = async (data) => {
        setLoadingComplete(true)
        setCompleteDisabled(true)

        try {
            let uploadedFileIds = []
            if (data.attachments?.length) {
                const uploadFilesRequest = await buildUploadFilesRequest(
                    cogniteClient,
                    data.attachments,
                    activeUser.displayName,
                    actionDetails?.reportingSite?.externalId ?? '',
                    actionDetails?.isPrivate,
                    FileSourceStepEnum.CompletedEvidence
                )

                uploadedFileIds = (await client?.uploadFiles(uploadFilesRequest)).externalIds
            }

            const result = await client?.upsertActionApprovalWorkflow(actionDetails?.externalId ?? '-', {
                externalId: actionDetails?.externalId,
                space: actionDetails?.space,
                activeUserEmail: activeUser.email,
                reportingSiteExternalId: actionDetails?.reportingSite?.externalId ?? SITE_EXTERNAL_ID_REQUIRED_FIELD,
                assigneeComment: data.comments,
                newFilesIds: uploadedFileIds,
            })

            handleAlert(`${translate('alerts.dataSavedWithSuccess')}: ${result.externalId}`)
            if (redirectToPage) {
                redirectToPage()
            } else {
                redirectToLastPage()
            }
        } catch (ex) {
            handleAlert(translate('alerts.unexpectedErrorOcurred'), true)
        } finally {
            setLoadingComplete(false)
            setCompleteDisabled(false)
        }
    }

    return (
        <ModalWrapper
            title={translate('requestModal.completeTheAction')}
            openModal={true}
            closeModal={handleCancel}
            sxProps={{ backgroundColor: 'rgba(0, 0, 0, 0.5)', minHeight: '200px' }}
            sxPropsTitle={{
                fontSize: '24px',
                marginRight: 'auto',
                paddingLeft: '20px',
            }}
            content={
                <>
                    {loadingComplete ? (
                        <Box
                            sx={{
                                display: 'flex',
                                justifyContent: 'center',
                                alignItems: 'center',
                                height: '200px',
                            }}
                        >
                            <ClnCircularProgress size={40} value={0} />
                        </Box>
                    ) : (
                        <Box
                            sx={{
                                width: isMobile ? '90vw' : '80vw',
                                maxWidth: '827px',
                                padding: isMobile ? '10px' : '20px',
                                fontFamily: 'Roboto',
                                paddingTop: 0,
                            }}
                        >
                            <Form onSubmit={handleSubmit(submitFn)} id="template-selector-form">
                                <Box>
                                    <Typography
                                        sx={{
                                            fontWeight: 'bold',
                                            color: 'grey[600]',
                                            fontSize: '16px',
                                            marginBottom: '16px',
                                        }}
                                    >
                                        {translate('requestModal.assigneeComments')}
                                    </Typography>
                                    <GenericTextField
                                        name={'comments'}
                                        control={control}
                                        error={Boolean(errors.comments)}
                                        valueController={getValues('comments')}
                                        label={translate('details.fields.assigneeComments')}
                                        helperText={translate('stepper.form.helperTextDescription')}
                                        rows={5}
                                        required
                                    />
                                </Box>
                                <UploadFiles
                                    newUploadedFiles={filesUploaded}
                                    isEditable={true}
                                    setNewUploadedFiles={(newValue: any) => {
                                        setFilesUploaded(newValue)
                                        setValue('attachments', newValue)
                                    }}
                                    sxProps={{ maxHeight: '100px', overflowY: 'auto', border: '10px' }}
                                    title={
                                        evidenceRequired
                                            ? translate('requestModal.uploadFilesEvidenceRequired')
                                            : translate('requestModal.uploadFiles')
                                    }
                                    error={Boolean(errors.attachments)}
                                />
                                <Box sx={{ ...actionButtonsStyle, marginTop: '1rem' }}>
                                    <ClnButton
                                        variant="text"
                                        label={translate('requestModal.cancel')}
                                        onClick={handleCancel}
                                        color="error"
                                    />
                                    <ClnButton
                                        type="submit"
                                        variant="contained"
                                        label={translate('requestModal.completeAction')}
                                        disabled={completeDisabled}
                                    />
                                </Box>
                            </Form>
                        </Box>
                    )}
                </>
            }
        />
    )
}

export default RequestModalComplete
