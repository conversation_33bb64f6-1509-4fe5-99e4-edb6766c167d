import { useRef, useState } from 'react'
import { Cln<PERSON>utton, MatIcon } from '@celanese/ui-lib'
import { Box, Typography } from '@mui/material'
import { AzureFunctionClient } from '@/app/common/clients/azure-function-client'
import { useSnackbar } from '@/app/common/contexts/SnackbarContext'
import { useBulkUpload } from '@/app/common/contexts/BulkUploadContext'
import { getBulkUploadColumns } from '@/app/common/utils/bulk-upload'
import { IconWithTooltip } from '@/app/common/utils/icons-helper'
import { translate } from '@/app/common/utils/generate-translate'
import { CustomDrawer } from '../../ModalComponent/Drawer/Drawer'
import { drawerStyles } from '@/app/components/ModalComponent/Drawer/styles'
import GenericAutocomplete from '../../FieldsComponent/GenericAutocomplete'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { UserRolesPermission } from '@celanese/celanese-sdk'

const cancelButtonContainerStyle = {
    display: 'flex',
    justifyContent: 'right',
    margin: '1.5rem 0 0 0',
}

const filterSchema = z.object({
    reportingSiteIds: z.string(),
})

type FilterSchema = z.infer<typeof filterSchema>

type BulkUploadDrawerProps = {
    activeUser: UserRolesPermission
    eventId?: string
    siteId?: string
    handleCloseDrawer: () => void
    debouncedFetchActions?: () => void
}

export function BulkUploadDrawer({
    activeUser,
    eventId,
    siteId,
    handleCloseDrawer,
    debouncedFetchActions,
}: BulkUploadDrawerProps) {
    const client = new AzureFunctionClient()
    const { showSnackbar } = useSnackbar()
    const { handleBulkUpload } = useBulkUpload()

    function getReportingSiteId(): string {
        if (siteId) {
            return siteId
        }

        if (activeUser.sites.length === 1) {
            return activeUser.sites[0].siteId
        }

        return ''
    }

    const { reset, setValue, control, watch } = useForm<FilterSchema>({
        defaultValues: {
            reportingSiteIds: getReportingSiteId(),
        },
        resolver: zodResolver(filterSchema),
    })

    const reportingSiteIds = watch('reportingSiteIds')

    const reportingSiteOptions = activeUser.applications[0].userSites.map((site) => ({
        value: site.siteId,
        label: site.siteName,
    }))

    const fileInputRef = useRef<HTMLInputElement | null>(null)
    const [isDownloadingTemplate, setIsDownloadingTemplate] = useState(false)

    const downloadTemplate = async () => {
        setIsDownloadingTemplate(true)
        const columns = getBulkUploadColumns(reportingSiteIds, eventId)

        try {
            await client.getBulkUploadTemplate(reportingSiteIds, columns)
        } catch {
            showSnackbar(
                translate('bulkUploadActionItems.errorDownloadingTemplate'),
                'error',
                'download-bulk-upload-template'
            )
        } finally {
            setIsDownloadingTemplate(false)
        }
    }

    const handleUploadFile = () => {
        if (fileInputRef.current) {
            fileInputRef.current.value = ''
            fileInputRef.current.click()
        }
    }

    const closeDrawer = () => {
        reset()
        handleCloseDrawer()
    }

    return (
        <CustomDrawer
            overlineMeta={translate('requestModal.actionItemManagement')}
            title={translate('bulkUploadActionItems.bulkUploadModalTitle')}
            openDrawer
            closeDrawer={closeDrawer}
            content={
                <Box sx={drawerStyles.container}>
                    <Box
                        sx={{
                            display: 'flex',
                            flexDirection: 'column',
                            overflowY: 'auto',
                            overflowX: 'hidden',
                            gap: 4,
                        }}
                    >
                        {!siteId && activeUser.applications?.[0]?.userSites?.length > 1 && (
                            <Box sx={{ display: 'flex', gap: 1, flexDirection: 'column' }}>
                                <Typography variant="h4" component="h2" sx={{ fontSize: '14px' }}>
                                    {translate('bulkUploadActionItems.selectSite')}
                                </Typography>
                                <GenericAutocomplete
                                    name="reportingSiteIds"
                                    control={control}
                                    options={reportingSiteOptions}
                                    onChange={(value) => {
                                        setValue('reportingSiteIds', value)
                                    }}
                                    multiple={false}
                                    label={translate('bulkUploadActionItems.reportingSite')}
                                    size="small"
                                />
                            </Box>
                        )}
                        <Box>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                <Typography variant="h4" component="h2" sx={{ fontSize: '14px' }}>
                                    {translate('bulkUploadActionItems.selectAction')}
                                </Typography>
                                <IconWithTooltip
                                    title={translate('bulkUploadActionItems.rowLimitWarning')}
                                    fontSizeIcon={'20px'}
                                />
                            </Box>
                            <Box sx={{ ...drawerStyles.buttonsContainer, flexDirection: 'column' }}>
                                <ClnButton
                                    label={translate('stepper.form.assignment.uploadFile')}
                                    endIcon={<MatIcon icon="upload_2" />}
                                    data-test="upload_action_items_flow-upload_file_button"
                                    data-origin="ui-lib"
                                    disabled={!reportingSiteIds}
                                    onClick={handleUploadFile}
                                />
                                <ClnButton
                                    variant="outlined"
                                    label={translate('bulkUploadActionItems.downloadTemplate')}
                                    endIcon={
                                        isDownloadingTemplate ? <MatIcon icon="sync" /> : <MatIcon icon="download_2" />
                                    }
                                    data-test="upload_action_items_flow-download_template_button"
                                    data-origin="ui-lib"
                                    onClick={downloadTemplate}
                                    disabled={!reportingSiteIds || isDownloadingTemplate}
                                />
                            </Box>
                        </Box>
                    </Box>
                    <Box sx={cancelButtonContainerStyle}>
                        <ClnButton variant="text" label={translate('requestModal.cancel')} onClick={closeDrawer} />
                    </Box>
                    <input
                        type="file"
                        accept=".xlsx"
                        ref={fileInputRef}
                        style={{ display: 'none' }}
                        onChange={(e) => {
                            closeDrawer()
                            handleBulkUpload(e, reportingSiteIds, activeUser.externalId, eventId, debouncedFetchActions)
                        }}
                    />
                </Box>
            }
        />
    )
}
