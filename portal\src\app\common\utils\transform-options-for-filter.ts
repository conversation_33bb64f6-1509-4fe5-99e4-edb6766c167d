import { AutocompleteOption } from '@/app/components/FieldsComponent/GenericAutocomplete'
import { compareUsers } from '../hooks/user-management/useUsers'
import { translate } from './generate-translate'

export const toCamelCase = (str: string): string => {
    return (
        str
            ?.replace(/(?:^\w|[A-Z]|\b\w|\s+|\W+)/g, (match, index) =>
                index === 0 ? match.toLowerCase() : match.toUpperCase()
            )
            ?.replace(/\s+/g, '')
            ?.replace(/[\W_]+/g, '') ?? ''
    )
}

export const transformOptions = (
    options: any[],
    labelKeyPrimary: string = 'description',
    labelKeyFallback: string = '',
    translationTag?: string
): AutocompleteOption[] => {
    const sortedOptions = [...options].sort((a, b) => {
        const keyA = a[labelKeyPrimary] || a[labelKeyFallback] || ''
        const keyB = b[labelKeyPrimary] || b[labelKeyFallback] || ''
        return keyA.localeCompare(keyB)
    })

    return sortedOptions.map((option) => {
        let label = option[labelKeyPrimary] || option[labelKeyFallback] || ''

        const siteCode = option.reportingSites?.[0]?.siteCode || option.reportingSite?.siteCode

        if (siteCode) {
            label = `${siteCode} - ${label}`
        }

        if (translationTag) {
            label = translationTag + '.' + toCamelCase(label)
            label = `${translate(label)}`
        }

        return {
            value: option.externalId,
            label: label,
        }
    })
}

export const transformStringOptions = (
    options: string[],
    translationTag?: string,
    skipSort: boolean = false
): AutocompleteOption[] => {
    const sortedOptions = skipSort ? options : [...options].sort((a, b) => a.localeCompare(b))

    return sortedOptions.map((option) => {
        const label = translationTag ? translate(`${translationTag}.${toCamelCase(option || '')}`) : option || ''

        return {
            value: option || '-',
            label: label,
        }
    })
}

export const transformOptionsForUser = (options: any[], email: boolean = false): AutocompleteOption[] => {
    const sortedOptions = [...options].sort(compareUsers)

    return sortedOptions.map((option) => {
        const emailLabel = email ? ` (${option['email']})` : ''
        const label = `${option['lastName']}, ${option['firstName']}${emailLabel}` || ''

        return {
            value: option.externalId,
            label: label,
        }
    })
}
