import { Autocomplete, Box, TextField, styled } from '@mui/material'
import { z } from 'zod'
import { Controller, SubmitHandler, useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { ClnButton } from '@celanese/ui-lib'
import { FilterOptionsReferencesConfiguration } from '@/app/common/models/admin-settings/filter-reference'
import { useEffect } from 'react'
import { translate } from '@/app/common/utils/generate-translate'

const categoryFilterSchema = z.object({
    application: z.array(z.string()),
})

type ReferenceFilterSchema = z.infer<typeof categoryFilterSchema>

type ReferenceTabFilterProps = {
    onSubmit: (filters: any) => void
    data: ReferenceFilterSchema
    defaultFilter?: FilterOptionsReferencesConfiguration
}

const Form = styled('form')({
    display: 'flex',
    flexDirection: 'column',
    width: '18rem',
    padding: '1.5rem',
    gap: '1rem',
})

export function ReferenceTabFilter({ data, defaultFilter, onSubmit }: ReferenceTabFilterProps) {
    const cleanFilter = { application: [] }

    const { reset, setValue, handleSubmit, control } = useForm<ReferenceFilterSchema>({
        defaultValues: cleanFilter,
        resolver: zodResolver(categoryFilterSchema),
    })

    function selectWithAllOption(op: string[]) {
        return [...op]
    }

    const clearFunction = () => {
        reset(cleanFilter)
    }

    const defaultFunction = () => {
        reset(cleanFilter)
    }

    const submitFn: SubmitHandler<ReferenceFilterSchema> = (data) => {
        onSubmit(data)
    }

    useEffect(() => {
        reset({ ...cleanFilter, ...defaultFilter })
    }, [defaultFilter])

    return (
        <Box>
            <Form onSubmit={handleSubmit(submitFn)}>
                <Box sx={{ display: 'flex', gap: '8px', justifyContent: 'flex-end' }}>
                    <ClnButton
                        size="small"
                        variant="outlined"
                        label={translate('table.filter.clear')}
                        onClick={() => {
                            clearFunction()
                        }}
                    />
                    <ClnButton
                        size="small"
                        variant="outlined"
                        label={translate('table.filter.default')}
                        onClick={() => {
                            defaultFunction()
                        }}
                    />
                </Box>
                <Controller
                    control={control}
                    name="application"
                    render={({ field }) => (
                        <Autocomplete
                            {...field}
                            multiple
                            limitTags={2}
                            id="application"
                            options={selectWithAllOption(data.application)}
                            value={field.value || []}
                            noOptionsText={translate('adminSettings.table.filter.noOptions')}
                            onChange={(event, value) => setValue('application', value as string[])}
                            renderInput={(params) => (
                                <TextField
                                    {...params}
                                    label={translate('adminSettings.table.filter.application')}
                                    size="small"
                                />
                            )}
                        />
                    )}
                />
                <Box
                    sx={{
                        display: 'flex',
                        width: '100%',
                        justifyContent: 'center',
                        margin: '20px 0 10px 0',
                        div: {
                            width: '100%',
                        },
                        button: {
                            width: '100%',
                        },
                    }}
                >
                    <ClnButton
                        type="submit"
                        size="small"
                        variant="contained"
                        label={translate('table.filter.applyFilter')}
                    />
                </Box>
            </Form>
        </Box>
    )
}
