from typing import Any
from cognite.client import CogniteClient
from services.logging_service import LoggingService
import os
import sys

script_dir = os.path.dirname(os.path.abspath(__file__))
project_dir = os.path.dirname(script_dir)
sys.path.append(project_dir)
from uuid import uuid4
from models.notification import Notification
from models.settings import Settings

NOTIFICATIONS_FUNCTION_EXTERNAL_ID = "ntf-event-receiver"


class NotificationService:
    def __init__(self, cognite_client: CogniteClient, settings: Settings):
        self._cognite_client = cognite_client
        self._settings = settings
        self._log = LoggingService(str(uuid4()))

    def send_notifications(self, notifications: list[Notification]):
        return self._cognite_client.functions.call(
            external_id=NOTIFICATIONS_FUNCTION_EXTERNAL_ID,
            data={
                "appId": self._settings.notifications_client_id,
                "items": [
                    notification.model_dump(by_alias=True)
                    for notification in notifications
                ],
            },
            wait=False,
        )

    def create_notification(
        self,
        action_item_dict: dict[str, Any],
        users_to_notify: list[str],
        notification_type: str,
        description: str | None = None,
    ) -> Notification:
        return Notification.create_notification_request(
            action_item_dict, users_to_notify, notification_type, description
        )
