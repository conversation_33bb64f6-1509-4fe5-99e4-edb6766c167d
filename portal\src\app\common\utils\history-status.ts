import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
import { generateNewExternalId } from '.'
import { UpsertStatusHistoryInstance } from '../models/upsert/upsertStatusHistoryInstance'
import { EntityType, GetSpace } from './space-util'

dayjs.extend(utc)

export const generateUpsertStatusHistoryInstance = (
    actionId: string,
    statusId: string,
    comments: string,
    currentSpace: string,
    activeUserExternalId: string | undefined,
    index?: number
): UpsertStatusHistoryInstance => {
    const currentSpaceStatus = GetSpace(EntityType.Static)
    const userSpace = GetSpace(EntityType.UMG)

    return {
        action: {
            node: {
                externalId: actionId,
                space: currentSpace,
            },
        },
        friendlyName: comments,
        changedAt: dayjs().utc().format('YYYY-MM-DDTHH:mm:ss'),
        status: { node: { externalId: statusId, space: currentSpaceStatus } },
        externalId: `${generateNewExternalId('SHINS', index ?? 1)}`,
        space: currentSpace,
        statusSubject: { node: { externalId: `UserAzureAttribute_${activeUserExternalId}`, space: userSpace } },
    }
}
