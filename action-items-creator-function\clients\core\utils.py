from __future__ import annotations

import logging
from collections.abc import Collection, Sequence
from datetime import datetime
from hashlib import sha256
from typing import (
    TYPE_CHECKING,
    Any,
    Callable,
    TypeVar,
    overload,
)

from cognite.client.data_classes.data_modeling import (
    EdgeApply,
    EdgeId,
    NodeApply,
    NodeId,
    NodeOrEdgeData,
    ViewId,
)
from cognite.client.data_classes.filters import And, Equals, ParameterValue
from pydantic import BaseModel

from .constants import DataModelSpaceEnum

if TYPE_CHECKING:
    from .models import BaseEntity


def map_excluing_keys(data: dict[str, Any] | None, keys: set[str]) -> dict[str, Any]:
    """Return a dict excluding specified keys."""
    return {k: v for k, v in data.items() if k not in keys} if data else {}


def hash_model(model: BaseModel) -> str:
    """Return a SHA-256 hash of a model's JSON dump."""
    return sha256(model.model_dump_json().encode("utf8")).hexdigest()


def edge_unwraper(data: Any) -> list[Any]:
    """Unwrap edge items from a list or dictionary."""
    if data is None:
        return []

    if isinstance(data, list):
        return data
    if isinstance(data, dict):
        return data.get("items", [])
    return []


def map_items(
    items: list[Any],
    item_mapper_func: Callable[[Any], Any | None],
) -> list[Any]:
    """Map and filter items using a mapper function."""
    result: list[Any] = []
    for item in items:
        entry = item_mapper_func(item)
        if entry is not None:
            result.append(entry)
    return result


def datetime_as_cdf_format(date: datetime) -> str:
    """Format datetime in CDF format (ISO, no microseconds)."""
    return date.replace(microsecond=0).isoformat()


def contains_node(items: Sequence[BaseEntity] | None, search: BaseEntity) -> bool:
    """Check if a node exists in a sequence."""
    return any(item == search for item in items) if items else False


def external_id_filter(
    space: str | ParameterValue,
    external_id: str | ParameterValue,
) -> And:
    """Create a filter for node externalId and space."""
    return And(
        Equals(["node", "externalId"], external_id),
        Equals(["node", "space"], space),
    )


def um_edge_filter(edge_external_id: str) -> Equals:
    """Create a filter for a specific edge type."""
    return Equals(
        ["edge", "type"],
        {
            "space": DataModelSpaceEnum.ACTION_DATA_MODEL_SPACE,
            "externalId": edge_external_id,
        },
    )


def to_node_apply(
    entity: BaseEntity,
    view_id: ViewId,
    properties_to_include: list[str] | None = None,
) -> NodeApply:
    """Convert an entity into a NodeApply."""
    properties = entity.model_dump(by_alias=True)
    properties.pop("space")
    properties.pop("externalId")
    properties = (
        {k: v for k, v in properties.items() if k in properties_to_include}
        if properties_to_include
        else properties
    )
    return NodeApply(
        space=entity.space,
        external_id=entity.external_id,
        sources=[
            NodeOrEdgeData(
                source=view_id,
                properties=properties,
            ),
        ],
    )


def to_edge_apply(
    entity: BaseEntity,
    view_id: ViewId,
    edge_name: str,
    reference_type: str,
    additional_suffix: str | None,
) -> list[EdgeApply]:
    """Convert an entity relationship into EdgeApply objects."""
    edges: list[EdgeApply] = []
    node_id = entity.external_id
    node_space = entity.space
    properties = entity.model_dump(by_alias=True)
    for edge in properties.pop(edge_name):
        if additional_suffix:
            edge_external_id = f"{node_id}-{additional_suffix}-{edge['externalId']}"
        else:
            edge_external_id = f"{node_id}-{edge['externalId']}"
        edges.append(
            EdgeApply(
                space=node_space,
                external_id=edge_external_id,
                type=(view_id.space, reference_type),
                start_node=(
                    node_space,
                    node_id,
                ),
                end_node=(
                    edge["space"],
                    edge["externalId"],
                ),
            ),
        )
    return edges


def to_node_delete(entity: BaseEntity, edge_name: str) -> list[NodeId]:
    """Create NodeId objects for deletion based on entity edge."""
    properties = entity.model_dump()
    return [
        NodeId(
            space=node_instance.get("space"),
            external_id=node_instance.get("external_id"),
        )
        for node_instance in properties.pop(edge_name)
    ]


def to_edge_delete(entity: BaseEntity, edge_name: str) -> list[EdgeId]:
    """Create EdgeId objects for deletion based on entity edge."""
    properties = entity.model_dump(by_alias=True)
    node_space = properties.get("space", "")
    return [
        EdgeId(
            space=node_space,
            external_id=edge_id,
        )
        for edge_id in properties.pop(edge_name)
    ]


T = TypeVar("T")


def get_list_diff(
    list1: Collection[T] | None,
    list2: Collection[T] | None,
) -> list[T]:
    """Return the elements in list1 that are not in list2. Note: Type must be hashable"""
    return list(set(list1 or []) - set(list2 or []))


def to_base_entity_list(entities: Sequence[BaseEntity]) -> list[BaseEntity]:
    """Convert entities to base entity format."""
    return [entity.as_base_entity() for entity in entities]


def is_from_reporting_site(entity: BaseEntity, reporting_site_external_id: str) -> bool:
    """Check if an entity belongs to the reporting site."""
    return entity.external_id[4:7] == reporting_site_external_id[4:7]


def is_unit_from_site(entity: BaseEntity, reporting_site_external_id: str) -> bool:
    """Check if a unit entity belongs to the reporting site."""
    return is_from_reporting_site(entity, reporting_site_external_id)


def is_location_from_site(entity: BaseEntity, reporting_site_external_id: str) -> bool:
    """Check if a location entity belongs to the reporting site."""
    return is_from_reporting_site(entity, reporting_site_external_id)


P_ = TypeVar("P_")


def create_chunks(items: list[P_], chunk_size: int) -> list[list[P_]]:
    """Split a list into chunks of specified size."""
    return [
        items[chunk_size * i : chunk_size * (i + 1)]
        for i in range(int(len(items) / chunk_size) + 1)
    ]


def get_logger(name: str) -> logging.Logger:
    """Get a logger with stream handler."""
    logging.basicConfig(
        handlers=[logging.StreamHandler()],
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        level=logging.INFO,
    )
    return logging.getLogger(name)


_VT = TypeVar("_VT")


def get_or(some_dict: dict, keys: list[str], default: _VT) -> _VT:
    """Get a nested value from a dictionary or return default."""
    try:
        if len(keys) == 1:
            return some_dict.get(keys[0], default) or default
        return (
            get_or(some_dict.get(keys[0], {}) or {}, keys[1:], default=default)
            or default
        )
    except KeyError:
        return default


@overload
def to_user_azure_attribute(user_external_id: str) -> str: ...
@overload
def to_user_azure_attribute(user_external_id: str | None) -> str | None: ...
def to_user_azure_attribute(user_external_id: str | None) -> str | None:
    """Format user external ID as Azure attribute."""
    if user_external_id is None or user_external_id.startswith("UserAzureAttribute_"):
        return user_external_id

    return f"UserAzureAttribute_{user_external_id}"
