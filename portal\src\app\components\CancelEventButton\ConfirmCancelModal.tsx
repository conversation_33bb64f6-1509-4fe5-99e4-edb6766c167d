import React from 'react'
import { Box, Typography } from '@mui/material'
import { ModalWrapper } from '../ModalComponent/Modal/ModalWrapper'
import { ClnButton } from '@celanese/ui-lib'
import { translate } from '@/app/common/utils/generate-translate'

interface ConfirmCancelModalProps {
    onClose: () => void
    onConfirm: () => void
}

export function ConfirmCancelModal({ onClose, onConfirm }: ConfirmCancelModalProps) {
    return (
        <ModalWrapper
            title={translate('requestModal.cancelEventQuestion')}
            openModal={true}
            closeModal={onClose}
            sxProps={{ backgroundColor: 'rgba(0, 0, 0, 0.5)' }}
            content={
                <Box sx={{ padding: '16px' }}>
                    <Typography variant="subtitle1" sx={{ fontWeight: 400, fontSize: '14px', marginBottom: '16px' }}>
                        {translate('requestModal.cancelEventSubtitle')}
                    </Typography>
                    <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: '8px' }}>
                        <ClnButton
                            variant="text"
                            label={translate('requestModal.close')}
                            onClick={onClose}
                        />
                        <ClnButton
                            variant="contained"
                            label={translate('source-event.cancelEvent')}
                            color="error"
                            onClick={onConfirm}
                        />
                    </Box>
                </Box>
            }
        />
    )
}
