import { Box } from '@mui/material'
import { ActionItemStateStatus } from '@/app/common/enums/KpiStatusEnum'
import LoaderCircular from '../../Loader'
import { KpiHome } from './KpiHome'
import GenericFieldTitle from '../../FieldsComponent/GenericFieldTitle'

export interface KpisParams {
    key: string
    label: string
    value: string
    statusName: ActionItemStateStatus
    disable?: boolean
    onClick: () => void
    dataTest?: string
}

interface KpisComponetProps {
    title: string
    currentState: ActionItemStateStatus
    kpisParams: KpisParams[]
    isLoadingInfo?: boolean
}

export function KpisComponent({ title, currentState, isLoadingInfo, kpisParams }: KpisComponetProps) {
    return (
        <Box
            sx={{
                display: 'flex',
                flexDirection: 'column',
                marginRight: '0px',
                height: '100%',
            }}
        >
            <GenericFieldTitle fieldName={title} isSectionTitle />
            <Box id="box-loading-kpis" p={0} mt={1} height={'100%'}>
                {isLoadingInfo && (
                    <Box
                        style={{
                            width: '100%',
                            height: '100%',
                            display: 'flex',
                            zIndex: 1,
                        }}
                    >
                        {LoaderCircular()}
                    </Box>
                )}

                <Box
                    id="box-kpis"
                    sx={{
                        display: 'flex',
                        gap: '0.5em',
                        flexWrap: 'nowrap',
                        justifyContent: 'space-between',
                        width: '100%',
                        height: '100%',
                        alignItems: 'stretch',
                        '@media (max-width: 1280px)': {
                            overflowX: 'auto',
                            overflowY: 'none',
                            scrollbarWidth: 'none',
                        },
                    }}
                >
                    {kpisParams?.map((kpi: KpisParams) => (
                        <Box
                            key={kpi.key}
                            sx={{
                                display: 'flex',
                                flexDirection: 'column',
                                flex: '1 1 10em',
                                minWidth: '1.9em',
                                height: '100%',
                                minHeight: '1.5em',
                                '@media (max-width: 1280px)': {
                                    width: '100%',
                                    minWidth: '175px',
                                },
                            }}
                        >
                            <KpiHome
                                id={kpi.key}
                                label={kpi.label}
                                statusName={kpi.statusName}
                                count={kpi.value}
                                onClick={kpi.onClick}
                                state={kpi.statusName}
                                currentState={currentState}
                                disable={kpi.disable}
                                dataTest={kpi.dataTest}
                            />
                        </Box>
                    ))}
                </Box>
            </Box>
        </Box>
    )
}
