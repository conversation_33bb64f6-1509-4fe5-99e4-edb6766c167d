from http import HTTPStatus
import azure.functions as func
from infra.action_item_client_factory import ActionItemClientFactory
from services.integration_service import IntegrationService
from clients.integration.requests import BaseIntegrationRequest, UserByNodeAccessRequest
from clients.core.constants import APPLICATION_JSON
import logging
import json as json

bp = func.Blueprint()


@bp.function_name(name="GetUser")
@bp.route("get-user", methods=["get"], auth_level=func.AuthLevel.ANONYMOUS)
async def main(req: func.HttpRequest) -> func.HttpResponse:
    logging.info("Function GetUser started")
    try:
        auth_header = req.headers.get("Authorization")
        if auth_header and auth_header.startswith("Bearer "):
            token_request = auth_header.split("Bearer ")[1].strip()
        else:
            token_request = auth_header if auth_header else None

        user_request_param = req.params.get("userRequest", "{}")
        user_request = json.loads(user_request_param)

        request = BaseIntegrationRequest.model_validate(user_request)

        service = IntegrationService(
            ActionItemClientFactory.retriever(id_token=token_request)
        )
        user = await service.get_user(request=request)

        logging.info("GetUser executed successfully")

        if user is None:
            return func.HttpResponse(
                json.dumps({"error": "User not found"}),
                status_code=HTTPStatus.NOT_FOUND,
                mimetype=APPLICATION_JSON,
            )

        return func.HttpResponse(
            json.dumps(user.model_dump(mode="json", by_alias=True)),
            status_code=HTTPStatus.OK,
            mimetype=APPLICATION_JSON,
        )
    except Exception as e:
        logging.error(f"Error in GetUser: {e}", exc_info=True)
        return func.HttpResponse(
            f"Error: {e}",
            status_code=HTTPStatus.INTERNAL_SERVER_ERROR,
            mimetype=APPLICATION_JSON,
        )


@bp.function_name(name="GetUsersByFeature")
@bp.route("get-users-by-feature", methods=["get"], auth_level=func.AuthLevel.ANONYMOUS)
async def main(req: func.HttpRequest) -> func.HttpResponse:
    logging.info("Function GetUser started")
    try:
        auth_header = req.headers.get("Authorization")
        if auth_header and auth_header.startswith("Bearer "):
            token_request = auth_header.split("Bearer ")[1].strip()
        else:
            token_request = auth_header if auth_header else None

        user_request_param = req.params.get("userRequest", "{}")
        user_request = json.loads(user_request_param)

        request = UserByNodeAccessRequest.model_validate(user_request)

        service = IntegrationService(
            ActionItemClientFactory.retriever(id_token=token_request)
        )
        user = await service.get_users_by_feature_code(request=request)

        if not user:
            user_data = []
        else:
            user_data = [
                {"featureCode": feature_code, "users": users}
                for feature_code, users in user.items()
            ]

        logging.info("GetUser executed successfully")

        return func.HttpResponse(
            json.dumps(user_data),
            status_code=HTTPStatus.OK,
            mimetype=APPLICATION_JSON,
        )
    except Exception as e:
        logging.error(f"Error in GetUser: {e}", exc_info=True)
        return func.HttpResponse(
            f"Error: {e}",
            status_code=HTTPStatus.INTERNAL_SERVER_ERROR,
            mimetype=APPLICATION_JSON,
        )
