import asyncio
import os
import sys
from copy import deepcopy
from typing import Any, Dict, List, Optional

script_dir = os.path.dirname(os.path.abspath(__file__))
project_dir = os.path.dirname(script_dir)
sys.path.append(project_dir)

from models.action_item import ActionItem
from models.notification import Notification
from models.settings import Settings
from services.cognite_service import CogniteService
from services.graphql_service import GraphqlService
from services.logging_service import LoggingService
from services.notification_service import NotificationService
from utils.general_utils import generate_query


class NotificationsByActionsIdsCreator:
    def __init__(
        self,
        cognite_service: CogniteService,
        graphql_service: GraphqlService,
        settings: Settings,
        logging_service: LoggingService,
        notification_service: NotificationService,
    ):
        self._cognite_service = cognite_service
        self._graphql_service = graphql_service
        self._log = logging_service
        self._settings = settings
        self._notification_service = notification_service

    async def execute(
        self,
        actions_list: List[str],
    ):
        if not actions_list:
            self._log.info("No action items to notifications create.")
            return None

        all_notifications = []
        all_notifications_dump = []
        try:
            external_ids = actions_list
            actions_details = await self._get_action_details_by_actions_id(external_ids)

            batch_size = 5
            batched_actions = [
                actions_details[i : i + batch_size]
                for i in range(0, len(actions_details), batch_size)
            ]

            for batch in batched_actions:
                notifications = await asyncio.gather(
                    *[self._process_action_item(action_item) for action_item in batch],
                )
                all_notifications.extend(
                    [item for sublist in notifications for item in sublist],
                )

            if all_notifications:
                self._notification_service.send_notifications(all_notifications)
                all_notifications_dump = [
                    notification.model_dump(by_alias=True)
                    for notification in all_notifications
                ]

        except Exception as err:
            self._log.error(f"Could not send notifications. Error: {err}")

        finally:
            await self._cleanup()
        return all_notifications_dump

    async def _process_action_item(self, action_item):
        action_item_dict = deepcopy(action_item)

        action_item_dict.update(
            {
                "currentStatus": {"name": "Assigned"},
                "actionItemKind": {"name": "One-Time"},
                "site": action_item.get("reportingSite", {}).get("externalId", "-"),
                "subCategory2": {
                    "description": (
                        action_item.get("siteSpecificCategory", {}).get(
                            "description",
                            "-",
                        )
                        if action_item.get("siteSpecificCategory", {})
                        else "-"
                    ),
                },
            },
        )

        approver, verifier = await asyncio.gather(
            self._get_approver_or_verifier(action_item, "AIM-approval"),
            self._get_approver_or_verifier(action_item, "AIM-verification"),
        )

        async def _get_user_if_exists(user_data):
            if not user_data:
                return None
            return await self._get_user_by_user_azure_attribute_id(
                user_data.get("externalId"),
            )

        assignee, owner, created_by = await asyncio.gather(
            _get_user_if_exists(action_item.get("assignedTo")),
            _get_user_if_exists(action_item.get("owner")),
            _get_user_if_exists(action_item.get("createdBy")),
        )

        def _extract_user_info(user, default_info=None):
            default_info = default_info or {}
            return {
                **default_info,
                "name": user.get("displayName", "-") if user else "-",
                "firstName": user.get("firstName", "-") if user else "-",
                "lastName": user.get("lastName", "-") if user else "-",
                "userMail": user.get("email", "-") if user else "-",
            }

        action_item_dict.update(
            {
                "approver": _extract_user_info(approver),
                "verifier": _extract_user_info(verifier),
                "assignedTo": _extract_user_info(
                    assignee,
                    action_item_dict.get("assignedTo"),
                ),
                "owner": _extract_user_info(owner, action_item_dict.get("owner")),
                "createdBy": _extract_user_info(
                    created_by,
                    action_item_dict.get("createdBy"),
                ),
            },
        )

        action_item_dict["siteSpecificCategory"] = {
            "description": "-",
            "space": "-",
            "externalId": "-",
            **(action_item_dict.get("siteSpecificCategory") or {}),
        }

        assigned_to_email = action_item_dict["assignedTo"]["userMail"]
        owner_email = action_item_dict["owner"]["userMail"]

        return [
            Notification.from_action_item_creation(
                action_item_dict,
                assigned_to_email,
                owner_email,
            ),
        ]

    async def _cleanup(self):
        await self._graphql_service.cleanup()

    async def _get_user_by_user_azure_attribute_id(
        self,
        external_id: str,
    ) -> Optional[Dict[str, Any]]:
        query_filter = {"externalId": {"eq": external_id}}

        user_azure_attributes = await self._graphql_service.get_all_results_list(
            generate_query(
                get_user_azure_attribute_query_list_name,
                get_user_azure_attribute_query_selection,
            ),
            get_user_azure_attribute_query_list_name,
            query_filter,
        )

        if not user_azure_attributes:
            return {}

        return user_azure_attributes[0].get("user")

    async def _get_action_details_by_actions_id(self, external_id: list[str]) -> list:
        if not external_id:
            return []

        query_filter = {"externalId": {"in": external_id}}

        actions = await self._graphql_service.get_all_results_list(
            generate_query(
                get_action_query_list_name,
                get_action_query_selection,
            ),
            get_action_query_list_name,
            query_filter,
        )

        return actions or []

    async def _get_approver_or_verifier(
        self,
        action_item: ActionItem,
        description: str,
    ) -> Optional[Dict[str, Any]]:
        approval_workflow = action_item.get("approvalWorkflow")

        if approval_workflow:
            external_id = approval_workflow.get("externalId")
            approval_workflow_step = (
                await self._get_approval_workflow_step_by_external_id_and_description(
                    external_id,
                    description,
                )
            )
            if approval_workflow_step and approval_workflow_step[0].get("users"):
                return approval_workflow_step[0]["users"][0]

        return None

    async def _get_approval_workflow_step_by_external_id_and_description(
        self,
        external_id: str,
        description: str,
    ) -> List[Dict[str, Any]]:
        self._log.info("Getting approval workflow step")

        query_filter = {
            "and": [
                {"approvalWorkflow": {"externalId": {"eq": external_id}}},
                {"description": {"eq": description}},
            ],
        }

        return await self._graphql_service.get_all_results_list(
            generate_query(
                get_approval_workflow_step_query_list_name,
                get_approval_worflow_step_query_selection,
            ),
            get_approval_workflow_step_query_list_name,
            query_filter,
        )


get_user_azure_attribute_query_list_name = "listUserAzureAttribute"

get_user_azure_attribute_query_selection = """
    externalId
    user {
        displayName
        firstName
        lastName
        email
    }
"""

get_action_query_list_name = "listAction"

get_action_query_selection = """
    externalId
    space
    createdBy {
        externalId
        space
    }
    owner {
        externalId
        space
    }
    assignedTo {
        externalId
        space
    }
    application {
        externalId
        space
        name
    }
    category {
        externalId
        space
        name
    }
    subCategory {
        externalId
        space
        name
    }
    siteSpecificCategory {
        externalId
        space
        description
    }
    reportingUnit {
        externalId
        space
        description
    }
    reportingLocation {
        externalId
        space
        description
    }
    reportingSite {
        externalId
        space
        description
    }
    reportingLine {
        externalId
        space
        description
    }
    businessLine {
        externalId
        space
        description
    }
    sourceType {
        externalId
        name
    }
    actionItemKind {
        externalId
        space
    }
    approvalWorkflow {
        externalId
        space
    }
    assignmentDate
    currentStatus {
        externalId
        space
    }
    description
    displayDueDate
    dueDate
    priority
    sourceInformation
    title
"""

get_approval_workflow_step_query_list_name = "listApprovalWorkflowStep"
get_approval_worflow_step_query_selection = """
    externalId
    users {
        items {
            externalId
            displayName
            firstName
            lastName
            email
        }
    }
"""
