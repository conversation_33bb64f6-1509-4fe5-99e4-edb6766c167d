from typing import ClassVar

from pydantic import computed_field

from clients.core.models import BaseCamelCaseModel
from utils.space_utils import (
    get_code_from_reporting_site_external_id,
    is_valid_reporting_site_id,
)


class GetFunctionalLocationRequest(BaseCamelCaseModel):
    """Model for retrieving functional location data based on reporting sites, units, and search criteria."""

    reporting_site_external_ids: list[str]
    external_ids: list[str] | None = None
    reporting_unit_external_ids: list[str] | None = None

    search: str | None = None
    search_properties: ClassVar[list[str]] = ["name"]

    @computed_field
    @property
    def spaces(self) -> list[str]:
        """Compute the data space identifiers based on the reporting sites."""
        return [
            f"SAP-{get_code_from_reporting_site_external_id(site_id)}-ALL-DAT"
            for site_id in self.reporting_site_external_ids
            if is_valid_reporting_site_id(site_id)
        ]
