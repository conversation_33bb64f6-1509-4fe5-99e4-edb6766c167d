import { type CogniteClient } from '@cognite/sdk'
import { EntityType, GetSpace } from './space-util'
import { PRIVATE_SPACE } from '.'

export async function getCurrentDatasetId(client: CogniteClient, siteId: string, isPrivate?: boolean): Promise<number> {
    if (!siteId) {
        throw new Error('No current site')
    }

    const datasetExternalId = isPrivate ? PRIVATE_SPACE : GetSpace(EntityType.Instance, siteId.slice(4))

    const datasets = await client.datasets.retrieve([{ externalId: datasetExternalId }])
    if (datasets.length === 0) {
        throw new Error('No dataset found')
    }

    return datasets[0].id
}
