'use client'
import '../../../common/utils/polyfills'
import { UserExternalContext, UserExternalContextState } from '@/app/common/contexts/UserExternalContext'
import AuthGuardWrapper from '@/app/common/wrapper/AuthGuardWrapper'
import ActionDetails from '@/app/components/ActionDetails'
import { UserRolesPermission } from '@celanese/celanese-sdk'
import { UserManagementContext, UserManagementContextState } from '@celanese/celanese-ui'
import { useContext, useEffect, useMemo, useState } from 'react'

type Props = {
    params: {
        slug: string[]
    }
}

export default function ActionItemDetailsPage({ params }: Props) {
    const { userInfo } = useContext<UserManagementContextState>(UserManagementContext)
    const { userExternalInfo } = useContext<UserExternalContextState>(UserExternalContext)
    const [activeUser, setActiveUser] = useState<UserRolesPermission>()

    const { id, siteId } = useMemo(() => {
        return params.slug?.length === 2
            ? { id: params.slug[1], siteId: `STS-${params.slug[0]}` }
            : { id: params.slug[0], siteId: undefined }
    }, [params])

    useEffect(() => {
        if (
            userExternalInfo.externalId &&
            userExternalInfo.externalId !== '' &&
            (activeUser?.externalId !== userExternalInfo.externalId ||
                activeUser?.applications !== userInfo.applications)
        ) {
            setActiveUser({
                ...userInfo,
                externalId: userExternalInfo.externalId,
                roles: userExternalInfo.roles,
                teams: userExternalInfo.teams,
            })
        }
    }, [userInfo, userExternalInfo])

    return (
        <AuthGuardWrapper
            componentName={[1, 2].includes(params.slug.length) ? ActionItemDetailsPage.name : 'UnknownComponent'}
        >
            {activeUser?.externalId && <ActionDetails id={id} siteId={siteId} activeUser={activeUser} />}
        </AuthGuardWrapper>
    )
}
