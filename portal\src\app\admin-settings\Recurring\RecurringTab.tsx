import { Box, Tooltip, useMediaQuery, useTheme } from '@mui/material'
import { ActionIcon, ClnButtonProps, ClnSelect, MatIcon } from '@celanese/ui-lib'
import { useContext, useEffect, useMemo, useState } from 'react'
import {
    getLocalUserSite,
    NoTranslate,
    TranslationContext,
    TranslationContextState,
    UserManagementContext,
    UserManagementContextState,
} from '@celanese/celanese-ui'
import { RecurringTabFilter } from './RecurringTabFilter'
import {
    ActionByRecurrenceQueryRequest,
    useActionByRecurrence,
} from '../../common/hooks/action-item-management/useActionByRecurrence'
import { ActionRecurringFilter, Status } from '../../common/models/action'
import dayjs from 'dayjs'
import { ActionDetailItem, ActionRequest, StatusHistoryInstance } from '../../common/models/action-detail'
import { EntityType, GetSpace } from '../../common/utils/space-util'
import { generateUpsertStatusHistoryInstance, ROWS_PER_PAGE_OPTIONS_ADMIN_SCREEN } from '../../common/utils'
import { UpsertStatusHistoryInstance } from '../../common/models/upsert/upsertStatusHistoryInstance'
import { useUpsertStatusHistoryInstance } from '../../common/hooks/mutations/useUpsertStatusHistoryInstance'
import { useRouter } from 'next/navigation'
import { FilterOptionsRecurring, FilterSelectedRecurring } from '../../common/models/admin-settings/filter-recurring'
import { ActionItemCategory } from '../../common/models/category'
import { ReportingUnit } from '../../common/models/reporting-unit'
import { ExternalEntityWithName } from '../../common/models/common'
import {
    StatusHistoryQuery,
    useStatusHistoryInstances,
} from '../../common/hooks/action-item-management/useStatusHistoryInstances'
import { useAuthGuard } from '../../common/hooks/useAuthGuard'
import { DataGridTable } from '@/app/components/PaginatedTable/DataGridTable'
import { GridColDef, GridRowsProp } from '@mui/x-data-grid-pro'
import { AllActionStatusExternalIdEnum } from '@/app/common/enums/ActionItemStatusEnum'
import RemoveItemModal from '@/app/components/ButtonsComponents/RemoveItemModal'
import { translate } from '@/app/common/utils/generate-translate'
import { determineActiveFilterCount } from '@/app/common/utils/active-filter-count'
import { toCamelCase, transformOptions, transformStringOptions } from '@/app/common/utils/transform-options-for-filter'
import { AutocompleteOption } from '@/app/components/FieldsComponent/GenericAutocomplete'
import { UserExternalContext, UserExternalContextState } from '@/app/common/contexts/UserExternalContext'
import { useUpsertAction } from '@/app/common/hooks/mutations/useUpsertAction'

type RecurringTabProps = {
    loading: boolean
    categories: ActionItemCategory[]
    units: ReportingUnit[]
    status: Status[]
    recurrenceTypes: ExternalEntityWithName[]
    handleAlert: (message: string, error?: boolean) => void
}

export const RecurringTab = (props: RecurringTabProps) => {
    const { categories, units, status, recurrenceTypes, handleAlert } = props

    const { siteCode } = getLocalUserSite() || {}

    const { locale } = useContext<TranslationContextState>(TranslationContext)

    const theme = useTheme()
    const isMobile = useMediaQuery(theme.breakpoints.down('md'))

    const { userInfo: activeUser } = useContext<UserManagementContextState>(UserManagementContext)
    const { userExternalInfo } = useContext<UserExternalContextState>(UserExternalContext)

    const activeUserExternalId = userExternalInfo.externalId
    const router = useRouter()
    const { checkPermissionsFromComponents } = useAuthGuard()
    const [updateAction] = useUpsertAction()
    const [updateStatusHistory] = useUpsertStatusHistoryInstance()

    const [modalOpen, setModalOpen] = useState(false)
    const [deleteRecurrence, setDeleteRecurrence] = useState<ActionDetailItem | undefined>()

    const [currentPage, setCurrentPage] = useState(0)
    const [rowsPerPage, setRowsPerPage] = useState(ROWS_PER_PAGE_OPTIONS_ADMIN_SCREEN[0])

    const [changeRecurringStatus, setChangeRecurringStatus] = useState<AutocompleteOption[]>([])

    const storedFilterInfo = sessionStorage.getItem(`admin-recurring-filterInfo`)
    const parsedFilterInfo = storedFilterInfo ? JSON.parse(storedFilterInfo) : {}

    const [filterActions, setFilterActions] = useState<FilterSelectedRecurring>({ ...parsedFilterInfo })

    const {
        loading,
        actions: actionsItens,
        refetchActions,
    } = useActionByRecurrence(
        useMemo<ActionByRecurrenceQueryRequest>(() => {
            const queryParams: ActionByRecurrenceQueryRequest = {
                unit: filterActions?.unit,
                category: filterActions?.category,
                status: filterActions?.status,
                recurrenceType: filterActions?.frequency,
                start: filterActions?.startDate
                    ? dayjs(filterActions?.startDate)
                          .startOf('day')
                          .utc()
                          .format('YYYY-MM-DD')
                    : null,
                noEndDate:
                    filterActions?.noEndDate === 'Yes' ? false : filterActions?.noEndDate === 'No' ? true : undefined,
            }
            setCurrentPage(0)
            return queryParams
        }, [filterActions])
    )
    const actionsIds = useMemo(() => actionsItens.map((x) => x.externalId), [actionsItens])

    const { historyInstance } = useStatusHistoryInstances(
        useMemo<StatusHistoryQuery>(
            () => ({
                externalId: actionsIds,
                statusId: null,
            }),
            [actionsIds]
        )
    )

    const historyInstanceArray = useMemo(() => {
        const historyIds: string[] = []
        const latestHistoryMap: StatusHistoryInstance[] = []

        historyInstance.forEach((x) => {
            if (!historyIds.includes(x?.action?.externalId) && x.statusSubject?.user?.email) {
                historyIds.push(x?.action?.externalId)
                latestHistoryMap.push(x)
            }
        })

        const latestHistoryArray = Array.from(latestHistoryMap.values())

        return latestHistoryArray
    }, [historyInstance])

    const actionsRecurring = useMemo(() => {
        const recurring = actionsItens.map((action) => {
            const historyInstance = historyInstanceArray.find((x) => x?.action?.externalId === action.externalId)
            const userLastName = historyInstance?.statusSubject?.user.lastName
            const userFirstName = historyInstance?.statusSubject?.user.firstName

            return {
                externalId: `${action.externalId}`,
                recurringExternalId: `${action.recurrenceInstance?.externalId}`,
                title: `${action.title}`,
                unit: `${action.reportingUnit?.description}`,
                category: `${action.category?.name}`,
                startDate: `${dayjs(action.recurrenceInstance?.startDate).format('MM/DD/YYYY')}`,
                frequency: `${action.recurrenceInstance?.recurrenceType?.name}`,
                owner: `${action.owner?.user.lastName}, ${action.owner?.user.firstName}`,
                updateBy: userLastName ? `${userLastName}, ${userFirstName}` : '',
                noEndDate: action.recurrenceInstance?.endDate
                    ? `${dayjs(action.recurrenceInstance?.endDate).format('MM/DD/YYYY')}`
                    : '-',
                status: `${action.currentStatus?.name ?? ''}`,
                action: action,
            }
        })
        return recurring
    }, [actionsItens, historyInstance])

    //Filter
    const [search, setSearch] = useState<string>('')
    //Filter data in frontend according to filter
    const filteredData = useMemo(() => {
        const filteredData = actionsRecurring.filter((sc) => {
            //Filter
            if (filterActions) {
                if (filterActions.owner?.length > 0) {
                    if (!filterActions.owner.includes(sc.owner)) {
                        return false
                    }
                }
                if (filterActions.updateBy?.length > 0) {
                    if (!filterActions.updateBy.includes(sc.updateBy)) {
                        return false
                    }
                }
            }
            //Search
            if (search && search.length > 0) {
                const searchData = search.toLowerCase()
                const recurringId = sc.externalId?.toLowerCase()
                const recurringTitle = sc.title?.toLowerCase()
                const recurringUnit = sc.unit?.toLowerCase()
                const recurringCategory = sc.category?.toLowerCase()
                const recurringStartDate = sc.startDate
                const recurringFrequency = sc.frequency.toLowerCase()
                const recurringStatus = sc.status.toLowerCase()
                const recurringUpdateBy = sc.updateBy.toLowerCase()
                const recurringOwner = sc.owner.toLowerCase()
                const recurringNoEndDate = sc.noEndDate.toLowerCase()

                if (
                    !recurringId?.includes(searchData) &&
                    !recurringTitle?.includes(searchData) &&
                    !recurringUnit?.includes(searchData) &&
                    !recurringCategory?.includes(searchData) &&
                    !recurringStartDate?.includes(searchData) &&
                    !recurringFrequency?.includes(searchData) &&
                    !recurringUpdateBy?.includes(searchData) &&
                    !recurringOwner?.includes(searchData) &&
                    !recurringNoEndDate?.includes(searchData) &&
                    !recurringStatus?.includes(searchData)
                ) {
                    return false
                }
            }
            return true
        })
        return filteredData
    }, [actionsRecurring, filterActions, search])

    const [filterOptions, setFilterOptions] = useState<FilterOptionsRecurring>({
        unit: [],
        owner: [],
        updateBy: [],
        category: [],
        startDate: null,
        frequency: [],
        status: [],
        noEndDate: [],
    })

    const setStatusAction = (actionId: string, actionSpace: string, newStatus: string): ActionRequest => {
        const actionRequest: ActionRequest = {
            externalId: actionId,
            space: actionSpace,
            currentStatus: {
                node: {
                    externalId: newStatus,
                    space: GetSpace(EntityType.Static),
                },
            },
        }

        return actionRequest
    }

    const saveRecurrenceStatus = async (): Promise<void> => {
        const matchingActions = actionsRecurring
            .filter((action) => changeRecurringStatus.some((item) => item.value === action.externalId))
            .map((action) => action.action)
        const updatedActions = matchingActions.map((action) => {
            const matchingTestItem = changeRecurringStatus.find((item) => item.value === action.externalId)

            const currentStatus = {
                externalId: matchingTestItem?.label ?? '',
                space: GetSpace(EntityType.Static),
            }

            return { ...action, currentStatus }
        })

        const editActions: ActionRequest[] = updatedActions.map((action) => {
            const { externalId, space, currentStatus } = action
            const actionId = externalId
            const actionSpace = space
            const newStatus = currentStatus?.externalId ?? ''

            return setStatusAction(actionId, actionSpace, newStatus)
        })

        const itemHistories: UpsertStatusHistoryInstance[] = updatedActions.map((action, index) => {
            const { externalId, space, currentStatus } = action

            const itemHistory = generateUpsertStatusHistoryInstance(
                externalId,
                currentStatus?.externalId ?? '',
                JSON.stringify({
                    actionUser: currentStatus?.externalId.substring(5),
                    comments: '',
                }),
                space,
                activeUserExternalId,
                index + 1
            )

            return itemHistory
        })

        try {
            await updateAction({ variables: { actionItems: editActions } })
            await updateStatusHistory({ variables: { statusHistoryInstances: itemHistories } })
            handleAlert(translate('alerts.dataSavedWithSuccess'))
            setChangeRecurringStatus([])
        } catch (ex) {
            handleAlert(translate('alerts.unexpectedErrorOcurred'), true)
        }
    }

    const applyFilters = async (filters: any) => {
        setFilterActions(filters)
        resetPageProps()
    }

    const buildFilterOptions = (): void => {
        const options: FilterOptionsRecurring = {
            unit: [],
            owner: [],
            updateBy: [],
            category: [],
            startDate: null,
            frequency: [],
            status: [],
            noEndDate: [],
        }
        options.category = transformOptions(categories, 'name', 'name', 'category')
        options.unit = transformOptions(units)

        const statusList = ['Active', 'Inactive']
        options.status = transformOptions(
            status.filter((x) => statusList.includes(x?.name ?? '')),
            'name',
            'name',
            'status'
        )
        options.frequency = transformOptions(recurrenceTypes, 'name', 'name', 'frequency')

        const owner = Array.from(new Set(actionsRecurring.map((x) => x.owner).filter(Boolean)))
        options.owner = transformStringOptions(owner)

        const updateByList = Array.from(new Set(actionsRecurring.map((x) => x.updateBy).filter(Boolean)))
        options.updateBy = transformStringOptions(updateByList)

        options.noEndDate = transformStringOptions(['Yes', 'No'])

        setFilterOptions(options)
    }

    const handleDeleteClick = (recurringAction?: ActionRecurringFilter) => {
        function mapToActionDetailItem(action: ActionRecurringFilter): ActionDetailItem {
            return {
                externalId: action.externalId,
                space: action.space,
                title: action.title,
            }
        }
        if (recurringAction) {
            const actionDetailItem: ActionDetailItem = mapToActionDetailItem(recurringAction)
            setDeleteRecurrence(actionDetailItem)
        }
        setModalOpen(true)
    }

    const handleCloseModal = () => {
        setModalOpen(false)
        refetchActions()
    }

    const headCells: GridColDef[] = useMemo(
        () => [
            {
                field: 'externalId',
                headerName: translate('adminSettings.table.headers.id'),
                filterable: false,
                flex: 1,
                minWidth: 100,
                renderHeader: () => (
                    <span data-test="admin-recurring-tab-id_table_sort" data-origin="aim">
                        {translate('adminSettings.table.headers.id')}
                    </span>
                ),
            },
            {
                field: 'title',
                headerName: translate('adminSettings.table.headers.title'),
                filterable: false,
                flex: 1,
                minWidth: 100,
                renderHeader: () => (
                    <span data-test="admin-recurring-tab-title_table_sort" data-origin="aim">
                        {translate('adminSettings.table.headers.title')}
                    </span>
                ),
            },
            {
                field: 'unit',
                headerName: translate('adminSettings.table.headers.unit'),
                filterable: false,
                flex: 1,
                minWidth: 100,
                renderHeader: () => (
                    <span data-test="admin-recurring-tab-unit_table_sort" data-origin="aim">
                        {translate('adminSettings.table.headers.unit')}
                    </span>
                ),
            },
            {
                field: 'category',
                headerName: translate('adminSettings.table.headers.category'),
                filterable: false,
                flex: 1,
                minWidth: 100,
                renderHeader: () => (
                    <span data-test="admin-recurring-tab-category_table_sort" data-origin="aim">
                        {translate('adminSettings.table.headers.category')}
                    </span>
                ),
                renderCell: (params) => (
                    <Box
                        sx={{
                            display: 'flex',
                            width: '100%',
                            height: '100%',
                            alignItems: 'center',
                        }}
                        data-test="admin-recurring-tab-category_content"
                        data-origin="aim"
                    >
                        {translate(`category.${toCamelCase(params.value)}`)}
                    </Box>
                ),
            },
            {
                field: 'startDate',
                headerName: translate('adminSettings.table.headers.startDate'),
                filterable: false,
                flex: 1,
                minWidth: 100,
                renderHeader: () => (
                    <div data-test="admin-recurring-tab-startDate_table_sort" data-origin="aim">
                        <Tooltip title={'MM/DD/YYYY'} arrow={true} placement="top">
                            {translate('adminSettings.table.headers.startDate')}
                        </Tooltip>
                    </div>
                ),
            },
            {
                field: 'frequency',
                headerName: translate('adminSettings.table.headers.frequency'),
                filterable: false,
                flex: 1,
                minWidth: 100,
                renderHeader: () => (
                    <span data-test="admin-recurring-tab-frequency_table_sort" data-origin="aim">
                        {translate('adminSettings.table.headers.frequency')}
                    </span>
                ),
                renderCell: (params) => (
                    <Box
                        sx={{
                            display: 'flex',
                            width: '100%',
                            height: '100%',
                            alignItems: 'center',
                        }}
                        data-test="admin-recurring-tab-frequency_content"
                        data-origin="aim"
                    >
                        {translate(`frequency.${toCamelCase(params.value)}`)}
                    </Box>
                ),
            },
            {
                field: 'owner',
                headerName: translate('adminSettings.table.headers.owner'),
                filterable: false,
                flex: 1,
                minWidth: 100,
                renderHeader: () => (
                    <span data-test="admin-recurring-tab-owner_table_sort" data-origin="aim">
                        {translate('adminSettings.table.headers.owner')}
                    </span>
                ),
            },
            {
                field: 'updateBy',
                headerName: translate('adminSettings.table.headers.updateBy'),
                filterable: false,
                flex: 1,
                minWidth: 100,
                renderHeader: () => (
                    <span data-test="admin-recurring-tab-updateBy_table_sort" data-origin="aim">
                        {translate('adminSettings.table.headers.updateBy')}
                    </span>
                ),
            },
            {
                field: 'noEndDate',
                headerName: translate('adminSettings.table.headers.noEndDate'),
                filterable: false,
                flex: 1,
                minWidth: 100,
                renderHeader: () => (
                    <span data-test="admin-recurring-tab-noEndDate_table_sort" data-origin="aim">
                        {translate('adminSettings.table.headers.noEndDate')}
                    </span>
                ),
            },
            {
                field: 'status',
                headerName: translate('adminSettings.table.headers.status'),
                filterable: false,
                flex: 1,
                minWidth: 100,
                renderCell: (params) => (
                    <Box
                        sx={{
                            display: 'flex',
                            width: '100%',
                            height: '100%',
                            alignItems: 'center',
                        }}
                        data-test="admin-recurring-tab-status_content"
                        data-origin="aim"
                    >
                        <NoTranslate>{selectStatus(params.row.id, params.value)}</NoTranslate>
                    </Box>
                ),
                renderHeader: () => (
                    <span data-test="admin-recurring-tab-status_table_sort" data-origin="aim">
                        {translate('adminSettings.table.headers.status')}
                    </span>
                ),
            },
        ],
        [locale, filteredData]
    )

    const resetPageProps = () => {
        setCurrentPage(0)
    }

    const selectStatus = (id: string, status: string) => {
        return (
            <Box
                sx={{
                    display: 'flex',
                    width: '100%',
                    height: '100%',
                    alignItems: 'center',
                }}
            >
                <ClnSelect
                    onChange={(event) => {
                        setChangeRecurringStatus((prev: any[]) => {
                            const itemExists = prev.some((item) => item.value === id)
                            if (itemExists) {
                                return prev.filter((item) => item.value !== id)
                            }
                            return [
                                ...prev,
                                {
                                    value: id,
                                    label:
                                        event === translate('adminSettings.recurrence.form.active')
                                            ? AllActionStatusExternalIdEnum.Active
                                            : AllActionStatusExternalIdEnum.Inactive,
                                },
                            ]
                        })
                    }}
                    size="small"
                    label=""
                    options={[
                        translate('adminSettings.recurrence.form.active'),
                        translate('adminSettings.recurrence.form.inactive'),
                    ]}
                    value={
                        status === 'Active'
                            ? translate('adminSettings.recurrence.form.active')
                            : translate('adminSettings.recurrence.form.inactive')
                    }
                    style={{ width: '110px' }}
                />
            </Box>
        )
    }

    const tableRows: GridRowsProp = useMemo(() => {
        function convertActionItemDataToRows(items: any[]) {
            return items.map((item) => ({
                id: `${item.externalId}`,
                externalId: `${item.recurringExternalId}`,
                title: `${item.title}`,
                unit: `${item.unit}`,
                category: `${item.category}`,
                startDate: `${item.startDate}`,
                frequency: `${item.frequency}`,
                owner: `${item.owner}`,
                updateBy: `${item.updateBy}`,
                noEndDate: `${item.noEndDate}`,
                status: `${item.status}`,
            }))
        }

        const actionsRows =
            filteredData != null && filteredData.length > 0 ? convertActionItemDataToRows(filteredData) : []

        return actionsRows
    }, [filteredData, historyInstanceArray])

    const actions: ActionIcon[] = [
        {
            icon: <MatIcon icon="edit" />,
            onClick: (id) => {
                localStorage.setItem('isEditForm', 'true')
                router.push(`/action-item/edit/${siteCode}/${id}`)
            },
        },
        {
            icon: <MatIcon icon="delete" color="error.main" />,
            onClick: (id) => {
                const recurringAction = actionsItens.find((action) => action.externalId == id)
                handleDeleteClick(recurringAction)
            },
        },
    ]

    const buttons: ClnButtonProps[] = [
        {
            label: isMobile ? '' : translate('adminSettings.table.saveChanges'),
            variant: 'contained',
            startIcon: isMobile ? <MatIcon icon="save" /> : undefined,
            sxProps: isMobile
                ? {
                      '& .MuiButton-startIcon': {
                          marginRight: 0,
                      },
                  }
                : undefined,
            onClick: () => {
                saveRecurrenceStatus()
            },
        },
    ]

    useEffect(() => {
        buildFilterOptions()
    }, [locale, categories, actionsRecurring, units, status, recurrenceTypes])

    enum TableTranslateKey {
        Search = 'Search',
        RowsPerPage = 'Rows per page',
        Of = 'of',
        Actions = 'Actions',
    }

    const translatedLabels = new Map<TableTranslateKey, string>()
    translatedLabels.set(TableTranslateKey.Search, translate('common.search'))
    translatedLabels.set(TableTranslateKey.RowsPerPage, translate('common.rowsPerPage'))
    translatedLabels.set(TableTranslateKey.Of, translate('common.of'))
    translatedLabels.set(TableTranslateKey.Actions, translate('common.actions'))

    const customPopoverContent = useMemo(() => {
        return (
            <RecurringTabFilter
                data={filterOptions}
                onSubmit={(filters) => {
                    applyFilters(filters)
                    sessionStorage.setItem(`admin-recurring-filterInfo`, JSON.stringify(filters))
                }}
                defaultFilter={filterActions}
                isAdminPage
            />
        )
    }, [locale, filterOptions, filterActions])

    return (
        <Box
            sx={{
                margin: '1rem 0px',
                display: 'flex',
                flexGrow: 1,
                flexDirection: 'column',
            }}
        >
            <DataGridTable
                id="admin-recurring"
                isLoading={props.loading || loading}
                initialColumnDefs={headCells}
                onSearchSubmit={(value: string) => setSearch(value)}
                customButtons={buttons}
                rows={tableRows.slice(currentPage * rowsPerPage, currentPage * rowsPerPage + rowsPerPage)}
                rowsPerPageOptions={ROWS_PER_PAGE_OPTIONS_ADMIN_SCREEN}
                currentPage={currentPage}
                rowsPerPage={rowsPerPage}
                setRowsPerPage={setRowsPerPage}
                customPopoverContent={customPopoverContent}
                totalPages={Math.ceil(tableRows.length / rowsPerPage)}
                setCurrentPage={setCurrentPage}
                actions={checkPermissionsFromComponents(RecurringTab.name) ? actions : undefined}
                paginationMode="server"
                activeFiltersCount={determineActiveFilterCount(filterActions)}
            />
            <RemoveItemModal
                actionDetails={deleteRecurrence}
                activeUserEmail={activeUser.email}
                routerPush="/admin-settings"
                openModal={modalOpen}
                handleClose={handleCloseModal}
                handleAlert={handleAlert}
                titleLabel={translate('requestModal.deleteRecurring')}
                questionLabel={translate('requestModal.deleteRecurringQuestion')}
                isDelete
                comments
            />
        </Box>
    )
}
