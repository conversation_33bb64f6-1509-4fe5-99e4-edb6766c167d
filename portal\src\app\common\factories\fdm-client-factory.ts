import { FdmClient } from '../clients'
import { cognite } from '../configurations/cognite'
import { EntityType, GetSpace, getCurrentInstancesSpace } from '../utils/space-util'

export const createFdmClient = (getAuthToken: () => Promise<string>) => {
    return new FdmClient({
        cogniteProject: cognite.project,
        cogniteBaseUrl: cognite.baseUrl,
        cogniteFdmModelSpace: GetSpace(EntityType.Model),
        cogniteFdmModel: cognite.cogniteFdmModel,
        cogniteFdmInstancesSpace: getCurrentInstancesSpace(),
        cogniteApiVersion: cognite.cogniteApiVersion,
        getToken: getAuthToken,
    })
}
