import calendar
from typing import Optional
from .constants import RecurrenceTypeExternalId


def _validate_weekly(week_days: Optional[list[int]] = None):
    if not week_days:
        raise ValueError("Week Days must be provided for weekly recurrence")

    for weekday in week_days:
        if weekday < 1 or weekday > 7:
            raise ValueError("weekday must be between 1 and 7")


def _validate_monthly(
    months: Optional[list[int]] = None,
    day_of_the_month: Optional[int] = None,
):
    if day_of_the_month is None or not months:
        raise ValueError(
            "Months and Day Of The Month must be provided for monthly recurrence"
        )

    for month in months:
        if month < 1 or month > 12:
            raise ValueError("month must be between 1 and 12")

        if month == 2 and day_of_the_month > 28:
            raise ValueError(
                "Day Of The Month must be equal or less than 28 for February"
            )

        last_day_of_month = calendar.monthrange(2023, month)[1]
        if day_of_the_month > last_day_of_month:
            raise ValueError(
                f"Day Of The Month must be less than {last_day_of_month} for Month {month}"
            )


def _validate_quarterly(
    quarters: Optional[list[int]] = None,
):
    if not quarters:
        raise ValueError("Quarters must be provided for quarterly recurrence")

    for quarter in quarters:
        if quarter < 1 or quarter > 4:
            raise ValueError("Quarter must be between 1 and 4")


def _validate_yearly_plus(
    day_of_the_month: Optional[int] = None,
    month_of_the_year: Optional[int] = None,
):
    if month_of_the_year is None or day_of_the_month is None:
        raise ValueError(
            "Month Of The Year and Day Of The Month must be provided for yearly+ recurrence"
        )

    if month_of_the_year == 2 and day_of_the_month > 28:
        raise ValueError("Day Of The Month must be equal or less than 28 for February")

    last_day_of_month = calendar.monthrange(2023, month_of_the_year)[1]
    if day_of_the_month > last_day_of_month:
        raise ValueError(
            f"Day Of The Month must be less than {last_day_of_month} for Month {month_of_the_year}"
        )


def _validate_custom(
    next_dates: Optional[list[str]] = None,
):
    if not next_dates:
        raise ValueError("nextDates must be provided for custom recurrence")


def validate_recurrence(
    recurrence_type_external_id: str,
    week_days: Optional[list[int]] = None,
    months: Optional[list[int]] = None,
    day_of_the_month: Optional[int] = None,
    quarters: Optional[list[int]] = None,
    month_of_the_year: Optional[int] = None,
    next_dates: Optional[list[str]] = None,
):
    available_external_ids = [e.value for e in RecurrenceTypeExternalId]
    if recurrence_type_external_id not in available_external_ids:
        raise ValueError(
            f"Invalid RecurrenceType, available values: {', '.join(available_external_ids)}"
        )

    match recurrence_type_external_id:
        case RecurrenceTypeExternalId.WEEKLY:
            _validate_weekly(week_days)

        case RecurrenceTypeExternalId.MONTHLY:
            _validate_monthly(months, day_of_the_month)

        case RecurrenceTypeExternalId.QUARTERLY:
            _validate_quarterly(quarters)

        case (
            RecurrenceTypeExternalId.YEARLY
            | RecurrenceTypeExternalId.BIENALLY
            | RecurrenceTypeExternalId.TRIENNIALLY
            | RecurrenceTypeExternalId.QUINQUENNIALLY
        ):
            _validate_yearly_plus(day_of_the_month, month_of_the_year)

        case RecurrenceTypeExternalId.CUSTOM:
            _validate_custom(next_dates)

        case _:
            return
