import { ActionItemLink } from './link'
import { ExternalEntity, ExternalEntityWithName } from './common'
import { BusinessLine } from './common/asset-hierarchy/business-line'
import { Country } from './common/asset-hierarchy/country'
import { GeoRegion } from './common/asset-hierarchy/geo-region'
import { ReportingLocation } from './common/asset-hierarchy/reporting-location'
import { ActionItemCategory } from './category'
import { ActionItemSubCategory } from './sub-category'
import { ActionItemType } from './action-type'
import { Workflow } from './common/approval-workflow/workflow'
import { ApprovalWorkflow, Assignees } from './action-detail'
import { ApprovalWorkflowStep } from './common/approval-workflow/workflow-step'
import { Application } from './application'
import { UserAzureAttribute } from './common/user-management/user-azure-attribute'
import { Role } from './common/user-management/role'

export interface Action extends ExternalEntity {
    currentStatus?: Status
    title?: string
    owner?: Owner
    application: Application
    reportingUnit?: ReportingUnitHomeTable
    reportingLocation?: ReportingLocation
    reportingLine?: ReportingLine
    description?: string
    links?: ActionItemLink[]
    sourceInformation?: string
    category?: ActionItemCategory
    subCategory?: ActionItemSubCategory
    siteSpecificCategory?: ActionItemSubCategory
    assignees?: { items: Assignees[] }
    actionItemKind?: ActionItemType
    assignedTo?: UserAzureAttribute
    assignmentDate?: Date
    stepApproval?: ApprovalWorkflowStep[]
    dueDate: Date
    approvalDate?: Date
    verificationDate?: Date
    conclusionDate?: Date
    displayDueDate?: string
    startDate?: Date
    endDate?: Date
    workflow?: Workflow
    approvalDueDate?: Date
    verificationDueDate?: Date
    voeActionItem?: string
    priority?: string
    approvalWorkflow?: ApprovalWorkflow
    estimatedCost?: number
    priceCurencyKey?: string
    estimatedGrade?: string
    isPlantShutdownRequired?: string
    actionTaken?: string
    objectType?: string
    objectExternalId?: string
    geoRegion?: GeoRegion
    country?: Country
    reportingSite?: ReportingSite
    businessLine?: BusinessLine
    createdBy?: User
    modifiedBy?: User
    createdTime: Date
    recurrenceInstance?: Recurrence
    templateConfiguration?: TemplateConfiguration
    isTemplate?: boolean
    evidenceRequired?: boolean
}

export interface ActionRecurringFilter extends ExternalEntity {
    currentStatus?: Status
    title?: string
    owner?: Owner
    reportingUnit?: ReportingUnitHomeTable
    reportingLocation?: ReportingLocation
    reportingLine?: ReportingLine
    description?: string
    links?: ActionItemLink[]
    sourceInformation?: string
    category?: ActionItemCategory
    subCategory?: ActionItemSubCategory
    siteSpecificCategory?: ActionItemSubCategory
    assignees?: { items: Assignees[] }
    actionItemKind?: ActionItemType
    assignedTo?: Owner
    assignmentDate?: Date
    stepApproval?: ApprovalWorkflowStep[]
    dueDate: Date
    displayDueDate?: Date
    startDate?: Date
    endDate?: Date
    workflow?: Workflow
    approvalDueDate?: Date
    verificationDueDate?: Date
    voeActionItem?: string
    priority?: string
    approvalWorkflow?: ApprovalWorkflow
    estimatedCost?: number
    priceCurencyKey?: string
    estimatedGrade?: string
    isPlantShutdownRequired?: string
    actionTaken?: string
    objectType?: string
    objectExternalId?: string
    geoRegion?: GeoRegion
    country?: Country
    reportingSite?: ReportingSite
    businessLine?: BusinessLine
    createdBy?: User
    modifiedBy?: User
    createdTime: Date
    recurrenceInstance?: RecurrenceFilter
    templateConfiguration?: TemplateConfiguration
    isTemplate?: boolean
}

export interface TemplateConfiguration extends ExternalEntity {
    name?: string
    description?: string
    users: { items: User[] }
    roles: { items: Role[] }
}

export interface ReportingUnitHomeTable extends ExternalEntity {
    description: string
    name: string
}

export interface User extends ExternalEntity {
    firstName: string
    lastName: string
    email?: string
}

export interface Owner extends ExternalEntity {
    user: User
}

export interface Category extends ExternalEntity {
    description: string
    name: string
}

export interface ReportingLine extends ExternalEntity {
    name: string
}

export interface Recurrence extends ExternalEntity {
    recurrenceType?: ExternalEntityWithName
    weekDays?: string
    months?: string
    dayOfTheMonth?: string
    quarters?: string
    monthOfTheYear?: string
    nextDates?: Date[]
    startDate?: Date
    endDate?: Date
}
export interface RecurrenceFilter extends ExternalEntity {
    recurrenceType?: ExternalEntityWithName
    weekDays?: string
    months?: string
    dayOfTheMonth?: string
    quarters?: string
    monthOfTheYear?: string
    nextDates?: Date[]
    startDate?: string
    endDate?: Date
    sourceEvent?: ExternalEntity
    title?: string
}

export interface ActionSourceType extends ExternalEntity {
    name: string
}

export interface ReportingSite extends ExternalEntity {
    siteCode: string
    name: string
}

export interface Status extends ExternalEntity {
    name: string
    description?: string
}
