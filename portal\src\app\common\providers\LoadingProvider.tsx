import { useState, useCallback, ReactNode, useEffect } from 'react'
import { LoadingContext } from '../contexts/LoadingContext'
import { Backdrop } from '@mui/material'
import { ClnCircularProgress } from '@celanese/ui-lib'
import { usePathname } from 'next/navigation'

export const LoadingProvider = ({ children }: { children: ReactNode }) => {
    const [open, setOpen] = useState(false)

    const showLoading = useCallback((value: boolean) => {
        setOpen(value)
    }, [])

    const pathname = usePathname()

    useEffect(() => {
        setOpen(false)
    }, [pathname])

    return (
        <LoadingContext.Provider value={{ showLoading }}>
            <Backdrop sx={{ zIndex: 10 }} open={open}>
                <ClnCircularProgress />
            </Backdrop>
            {children}
        </LoadingContext.Provider>
    )
}
