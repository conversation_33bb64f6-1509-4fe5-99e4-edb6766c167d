# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
portal/node_modules
portal/.pnp
.pnp.js

# testing
portal/coverage
local_tests/

# next.js
portal/.next/
/out/

# production
portal/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env*.local

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# python
.venv
__pycache__
.ruff_cache

# function
bin
obj
csx
.vs
edge
Publish

*.user
*.suo
*.cscfg
*.Cache
project.lock.json

/packages
/TestResults
icap/outputs/

/tools/NuGet.exe
/App_Data
/secrets
/data
.secrets
appsettings.json
local.settings.json
debugguer_function.py

node_modules
dist

.vscode/

# Local python packages
.python_packages/

# Python Environments
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# Azurite artifacts
__blobstorage__
__queuestorage__
__azurite_db*__.json
