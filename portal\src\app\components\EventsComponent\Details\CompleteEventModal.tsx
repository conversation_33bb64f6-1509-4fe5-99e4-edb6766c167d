import { Box, Typography, useMediaQuery, useTheme } from '@mui/material'
import { ClnButton } from '@celanese/ui-lib'
import { ModalWrapper } from '../../ModalComponent/Modal/ModalWrapper'
import { translate } from '@/app/common/utils/generate-translate'

interface CompleteEventModalProps {
    onClose: () => void
    onSave: () => void
}

const actionButtonsStyle = {
    display: 'flex',
    justifyContent: 'flex-end',
    gap: 1,
    '@media (max-width:600px)': {
        flexDirection: 'column',
        gap: '0.5rem',
        alignItems: 'center',
    },
}

export const CompleteEventModal: React.FC<CompleteEventModalProps> = ({ onClose, onSave }) => {
    const theme = useTheme()
    const isMobile = useMediaQuery(theme.breakpoints.down('md'))

    return (
        <ModalWrapper
            title={translate('requestModal.completeEvent')}
            openModal={true}
            closeModal={onClose}
            sxProps={{ backgroundColor: 'rgba(0, 0, 0, 0.5)' }}
            sxPropsTitle={{ fontSize: '24px', marginRight: 'auto', paddingLeft: '20px' }}
            content={
                <Box
                    sx={{
                        width: isMobile ? '90vw' : '80vw',
                        maxWidth: '827px',
                        padding: isMobile ? '10px' : '20px',
                        fontFamily: 'Roboto',
                        paddingTop: 0,
                    }}
                >
                    <Typography
                        sx={{
                            fontWeight: 'bold',
                            color: 'grey[600]',
                            fontSize: '16px',
                            marginBottom: '16px',
                        }}
                    >
                        {translate('requestModal.completeEventText')}
                    </Typography>
                    <Box sx={{ ...actionButtonsStyle, marginTop: '1rem' }}>
                        <ClnButton
                            onClick={onClose}
                            type="submit"
                            variant="text"
                            label={translate('requestModal.cancel')}
                            color="error"
                        />
                        <ClnButton
                            onClick={onSave}
                            type="submit"
                            variant="contained"
                            label={translate('requestModal.complete')}
                        />
                    </Box>
                </Box>
            }
        />
    )
}
