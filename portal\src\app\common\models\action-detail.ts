import { ExternalEntity } from './common'
import { ReportingLocation } from './common/asset-hierarchy/reporting-location'
import { ActionItemSubCategory } from './sub-category'
import { ActionItemType } from './action-type'
import { Workflow } from './common/approval-workflow/workflow'
import { NodeOf } from './node-of'
import { UpsertBase } from './upsert/upsertBase'
import { UpsertApprovalWorkFlow, UpsertBaseWithName } from './upsert/upsertActionItemChangeRequest'
import { EdgeOf } from './edge-of'
import { ReportingLine } from './common/asset-hierarchy/reporting-line'
import { UserAzureAttribute } from './common/user-management/user-azure-attribute'
import { Role } from './common/user-management/role'
import { Team } from './common/user-management/team'
import { UserComplement } from './common/user-management/user-complement'
import { ActionItemLink } from './link'

export interface ActionDetailsPermissions {
    edit: boolean
    delete: boolean
    view: boolean
}

export interface ResponseData {
    hasNextPage: boolean
    data: ActionDetailItem | ActionDetailItem[]
    totalItems: number
    totalActions: number
    access?: boolean
    cursor?: string
    permissions?: ActionDetailsPermissions
}

export interface ReportingUnitHomeTable extends ExternalEntity {
    description: string
    name: string
}

export interface TemplateConfiguration extends ExternalEntity {
    name: string
    description?: string
    users?: User[]
    roles?: Role[]
}

export interface ActionSourceType extends ExternalEntity {
    name: string
    description?: string
}

export interface User extends ExternalEntity {
    firstName?: string
    lastName?: string
    email?: string
    name?: string
    active?: boolean
    teams?: { items: Team[] }
    displayName?: string
    edgeExternalId?: string
}

export interface Category extends ExternalEntity {
    description: string
    name: string
}

export interface Application extends ExternalEntity {
    azureAppId?: string
    description?: string
    name?: string
    alias?: string
    url?: string | null
}

export interface ActionItemKind extends ExternalEntity {
    description: string
    name: string
}

export interface SourceEvent extends ExternalEntity {
    title: string
}

export interface Attachment {
    externalId: string
    name: string
    mimeType: string
    source?: string
    metadata?: Record<string, string>
    uploadedTime?: Date
    file?: File
}

export interface UserWorkflow extends User {
    edgeExternalId?: string
}

export interface ActionDetailItem extends ExternalEntity {
    actionItemKind?: ActionItemKind
    actionItemLink?: ActionItemLink[]
    application?: Application
    approvalDate?: string
    approvalDueDate?: Date
    approvalWorkflow?: ApprovalWorkflow
    approver?: UserWorkflow
    assignedTo?: UserWithCompanyDepartament
    assigneeComment?: string
    assignees?: User[]
    assignmentDate?: string
    attachments?: Attachment[]
    category?: Category
    categoryConfig?: string
    currentStatus?: Status
    description?: string
    displayDueDate?: string
    isPrivate?: boolean
    dueDate?: string
    verificationDate?: string
    conclusionDate?: string
    endDate?: Date
    estimatedCost?: number
    estimatedGrade?: number
    evidenceRequired?: boolean
    ext?: number
    isPlantShutdownRequired?: boolean
    isTemplate?: boolean
    links?: ActionItemLink[]
    manager?: User
    owner?: UserWithCompanyDepartament
    priceCurrencyKey?: number
    priority?: string
    recurrenceInstance?: RecurrenceInstance
    reportingLine?: ReportingLine
    reportingLocation?: ReportingLocation
    reportingSite?: ReportingSite
    reportingUnit?: ReportingUnitHomeTable
    siteSpecificCategory?: ActionItemSubCategory
    sourceEventTitle?: string
    sourceEvents?: SourceEvent[]
    sourceId?: string
    sourceInformation?: string
    sourceType?: ActionSourceType
    startDate?: Date
    subCategory?: ActionItemSubCategory
    taskType?: ActionItemType
    templateConfiguration?: TemplateConfiguration
    title?: string
    verifier?: UserWorkflow
    voeActionItem?: string
    workflow?: Workflow
    actionTaken?: number
    viewOnly?: boolean
    historyInstance?: StatusHistoryInstance[]
    changeRequests?: ChangeRequest[]
    unit?: string
    objectType?: string
    viewUsers?: UserAzureAttribute[]
    viewRoles?: Role[]
    viewTeams?: Team[]
    comments?: Comment[]
}

export interface Comment extends ExternalEntity {
    isLegacy: boolean
    comment: string
    timestamp?: string
    user?: User
}

export interface CommentRequest {
    comment: string
    actionExternalId: string
    userEmail: string
    isPrivate: boolean
    reportingSiteExternalId: string
    timestamp: string
}

export interface Assignees extends ExternalEntity {
    user: User
}

export interface ReportingSite extends ExternalEntity {
    siteCode: string
    name: string
}

export interface UserWithCompanyDepartament extends ExternalEntity {
    azureUserId: string
    companyName: string
    department: string
    user: User
}

export interface ApprovalWorkflow extends ExternalEntity {
    currentStep?: string
    description?: string
    status?: Status
    endDate?: string
    steps?: ApprovalWorkflowStep[]
}

export interface ApprovalWorkflowStep extends ExternalEntity {
    description: string
    status: StatusApprovalWorkflowStep
    users: User[]
}

export interface StatusApprovalWorkflowStep extends ExternalEntity {
    description: string
}

export interface Status extends ExternalEntity {
    name: string
    description: string
}

export interface PropertiesToChange extends ExternalEntity {
    newAssignee?: string
    newAssigneeName?: string
    newDueDate?: string
}

export interface ChangeRequest extends ExternalEntity {
    comments?: string
    propertiesToChange?: PropertiesToChange
    changeRequestType?: ExternalEntity
    approvalWorkflow?: ApprovalWorkflow
    attatchments?: Attachment[]
}

export interface StatusHistoryInstance extends ExternalEntity {
    externalId: string
    space: string
    friendlyName: string | null
    status: Status
    changedAt: Date
    createdTime: Date
    action: ExternalEntity
    statusSubject: UserAzureAttribute
}

export interface ActionItemKind extends ExternalEntity {
    name: string
    description: string
}

export interface RecurrenceType extends ExternalEntity {
    name: string
    description: string
}

export interface RecurrenceInstance extends ExternalEntity {
    name: string
    description: string
    recurrenceType: RecurrenceType
    weekDays?: number[]
    months?: number[]
    dayOfTheMonth?: number
    quarters?: number[]
    monthOfTheYear?: number
    nextDates?: Date[]
    startDate?: string
    endDate?: string
}

export interface ActionRequest {
    externalId: string
    space: string
    assigneeComment?: string
    completedDate?: string
    dueDate?: string
    displayDueDate?: string
    currentStatus: NodeOf<CurrentStatus>
    attachments?: string[]
    approvalDate?: string
    verificationDate?: string
    conclusionDate?: string
}

export interface RecurrenceInstanceForm {
    externalId?: string
    space?: string
    description?: string
    recurrenceType?: NodeOf<RecurrenceType>
    weekDays?: string
    months?: string
    dayOfTheMonth?: string
    quarters?: string
    monthOfTheYear?: string
    nextDates?: string[]
    startDate?: string
    endDate?: string
}

export interface CurrentStatus {
    externalId: string
    space: string
}

export interface WorkflowRequest {
    externalId: string
    space: string
    step?: number
    startDate?: string
    endDate?: string
    status?: NodeOf<CurrentStatus>
    users?: EdgeOf<UpsertBaseWithName, UpsertBase>
    description?: string
    approvalWorkflow?: NodeOf<UpsertApprovalWorkFlow>
    approvalCondition?: NodeOf<UpsertApprovalWorkFlow>
    approvalWorkflowConsentType?: NodeOf<UpsertApprovalWorkFlow>
}

interface UserRoleSite extends ExternalEntity {
    usersComplements: UserComplement[]
}

export interface DefaultRoleCategory extends ExternalEntity {
    userRoleSites: UserRoleSite[] | null
}

export interface CategoryConfigurationData extends ExternalEntity {
    category?: Category
    actionItemSubCategory?: ActionItemSubCategory
    siteSpecificCategory?: ActionItemSubCategory
    isApprovalRequired?: boolean
    isVerificationRequired?: boolean
    attachmentRequired?: boolean
    isExtensionsAllowed?: boolean
    isExtensionAttachmentRequired?: boolean
    daysToApproval?: number | null
    daysToVerification?: number | null
    daysFromAssignedDate?: number | null
    defaultApprovalRole?: DefaultRoleCategory | null
    defaultApprovalUser?: User | null
    defaultVerificationRole?: DefaultRoleCategory | null
    defaultVerificationUser?: User | null
    defaultExtensionApproverRole?: ExternalEntity | null
    hasEmailNotification?: boolean
}
