
from services.action_item_event_processor import ActionItemReference

from models.settings import Settings
from services.cognite_service import CogniteService
from services.graphql_service import GraphqlService
from services.logging_service import LoggingService

class ActionItemReferenceRetriever:
    def __init__(
        self,
        cognite_service: CogniteService,
        graphql_service: GraphqlService,
        settings: Settings,
        logging_service: LoggingService,
    ):
        self._cognite_service = cognite_service
        self._graphql_service = graphql_service
        self._log = logging_service
        self._settings = settings

    async def get_action_items_reference(self) -> list[ActionItemReference]:
        return await self._graphql_service.get_all_results_list(
            ACTION_ITEM_REFERENCE, "listActionItemReference"
        )
    
ACTION_ITEM_REFERENCE = """
query GetActionItemReference($after: String) {
    listActionItemReference(after: $after, first: 1000) {
      pageInfo {
        hasNextPage
        endCursor
      }
      items {
        externalId
        name
        description
        space
      }
    }
  }
"""