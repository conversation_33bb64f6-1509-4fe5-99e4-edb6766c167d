import { useEffect, useState } from 'react'
import { useGetAllResultsFunctionFromCustomClient } from '../../general-functions/useGetAllResultFunction'
import { Filter } from '@/app/common/models/base-hook-request'
import { OFWAEvent } from '@/app/common/models/integration/ofwa/ofwa-sol-event'

const ofwaObservationQuery = `
externalId
refUnit {
  description
}
refSubProcess{
  name
}
createdBy
refCategory {
  items {
    name
  }
}
observationStatus
refOFWAProcess {
  items {
    name
  }
}
date
`

export const useOFWAEvent = (externalId?: string) => {
  const [resultData, setResultData] = useState<{ data: OFWAEvent[]; loading: boolean }>({
    data: [],
    loading: true,
  })

  const { getAllItems: getAllData } = useGetAllResultsFunctionFromCustomClient<OFWAEvent>(
    ofwaObservationQuery,
    'listObservation',
    'ofwa'
  )

  const filter: Filter<OFWAEvent> = {
    externalId: { eq: externalId ?? '' },
  }

  useEffect(() => {
    if (!externalId) return

    getAllData(filter).then((res: any) => {
      if (res.length == 0) {
        setResultData({ data: [], loading: false })
      } else {
        setResultData({ data: res, loading: false })
      }
    })
  }, [externalId])

  return {
    loading: resultData.loading,
    data: resultData.data,
  }
}
