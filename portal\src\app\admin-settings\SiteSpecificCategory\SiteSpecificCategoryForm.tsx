import { ClnButton } from '@celanese/ui-lib'
import { Box } from '@mui/material'
import { z } from 'zod'
import { SubmitHandler, useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { useEffect } from 'react'
import { SiteSpecificCategory } from '../../common/models/site-specific-category'
import { MAX_TITLE_TEXT_FIELD, MIN_TEXT_FIELD, MAX_DEFAULT_TEXT_FIELD } from '@/app/common/utils'
import GenericTextField from '@/app/components/FieldsComponent/GenericTextField'
import { translate } from '@/app/common/utils/generate-translate'
import { CustomDrawer } from '@/app/components/ModalComponent/Drawer/Drawer'
import { drawerStyles } from '@/app/components/ModalComponent/Drawer/styles'

interface SiteSpecificCategoryFormProps {
    onClose: () => void
    openDrawer: boolean
    onSubmitCallback: (data: any, isDeleted: boolean, onSuccess?: () => void) => void
    onCloseCallback: () => void
    siteSpecificCategoryToEdit?: SiteSpecificCategory
    title: string
    isEdit: boolean
}

type SiteSpecificCategoryFormSchema = z.infer<typeof formSchema>

const formSchema = z.object({
    name: z.string().min(MIN_TEXT_FIELD).max(MAX_TITLE_TEXT_FIELD),
    description: z.string().min(MIN_TEXT_FIELD).max(MAX_DEFAULT_TEXT_FIELD),
    externalId: z.string().nullable(),
})

export const SiteSpecificCategoryForm: React.FC<SiteSpecificCategoryFormProps> = ({
    openDrawer,
    onSubmitCallback,
    onCloseCallback,
    siteSpecificCategoryToEdit,
    title,
    isEdit,
}) => {
    const {
        control,
        handleSubmit,
        reset,
        getValues,
        formState: { errors },
    } = useForm<SiteSpecificCategoryFormSchema>({
        resolver: zodResolver(formSchema),
    })

    useEffect(() => {
        reset({
            name: siteSpecificCategoryToEdit?.name ?? '',
            description: siteSpecificCategoryToEdit?.description ?? '',
            externalId: siteSpecificCategoryToEdit?.externalId ?? null,
        })
    }, [siteSpecificCategoryToEdit, reset])

    const onSubmit: SubmitHandler<SiteSpecificCategoryFormSchema> = (data) => {
        onSubmitCallback(data, false, () => reset())
    }

    const onCancel = () => {
        reset()
        onCloseCallback()
    }

    return (
        <CustomDrawer
            overlineMeta={title}
            title={title}
            openDrawer={openDrawer}
            closeDrawer={onCancel}
            content={
                <Box sx={drawerStyles.container}>
                    <form onSubmit={handleSubmit(onSubmit)} style={drawerStyles.formContainer}>
                        <GenericTextField
                            required
                            name="name"
                            control={control}
                            valueController={getValues('name')}
                            label={translate('adminSettings.specificCategories.form.name')}
                            helperText={translate('stepper.form.helperTextTitle')}
                            error={Boolean(errors.name)}
                        />
                        <GenericTextField
                            required
                            name="description"
                            control={control}
                            valueController={getValues('description')}
                            rows={6}
                            label={translate('adminSettings.specificCategories.form.description')}
                            helperText={translate('stepper.form.helperTextModal')}
                            error={Boolean(errors.description)}
                        />
                    </form>
                    <Box sx={drawerStyles.buttonsContainer}>
                        <ClnButton
                            label={translate('adminSettings.specificCategories.form.buttons.cancel')}
                            variant="text"
                            onClick={onCancel}
                            sx={{ flex: 1 }}
                        />
                        <ClnButton
                            label={
                                isEdit
                                    ? translate('adminSettings.specificCategories.form.buttons.save')
                                    : translate('adminSettings.specificCategories.form.buttons.create')
                            }
                            onClick={handleSubmit(onSubmit)}
                            sx={{ flex: 1 }}
                        />
                    </Box>
                </Box>
            }
        />
    )
}
