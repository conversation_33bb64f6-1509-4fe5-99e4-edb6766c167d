import os
import sys
from typing import Optional
from uuid import uuid4

script_dir = os.path.dirname(os.path.abspath(__file__))
project_dir = os.path.dirname(script_dir)
sys.path.append(project_dir)

from clients.action_item_client import ActionItemClient
from clients.core.constants import ViewEnum
from clients.core.models import ServiceParams
from infra.core_factory import (
    check_spaces_user_has_access,
    get_cognite_client_as_service,
    get_data_model,
    get_engine_async,
    get_graphql_client_as_service,
    get_notification_service,
)
from models.settings import Settings
from services.cognite_service import CogniteService
from services.graphql_service import GraphqlService
from services.logging_service import LoggingService


class ActionItemClientFactory:
    @staticmethod
    def retriever(
        settings: Settings = Settings.from_env(),
        call_id: str = str(uuid4()),
        override_token: Optional[str] = None,
        id_token: Optional[str] = None,
    ) -> ActionItemClient:

        spaces_user_has_access = check_spaces_user_has_access(
            ViewEnum.ACTION,
            override_token,
        )

        cognite_client = get_cognite_client_as_service()
        graphql_client = get_graphql_client_as_service()
        logging_service = LoggingService(call_id)
        notification_service = get_notification_service()
        engine = get_engine_async(override_token)

        params = ServiceParams(
            cognite_client=cognite_client,
            graphql_client=graphql_client,
            data_model=get_data_model(),
            engine=engine,
            settings=settings,
            logging=logging_service,
            notification_service=notification_service,
            spaces_user_has_access={ViewEnum.ACTION: spaces_user_has_access},
            cognite_service=CogniteService(cognite_client, settings, logging_service),
            graphql_service=GraphqlService(graphql_client, logging_service),
            token=id_token,
        )

        return ActionItemClient(params)
