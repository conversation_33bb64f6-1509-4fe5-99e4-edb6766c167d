trigger:
  branches:
    include:
      - dev
  paths:
    include:
      - action-items-creator-function/*

stages:
  - template: function-template.yml
    parameters:
      azureSubscription: "Action Item Management Deploy - DEV"
      functionAppName: "func-dplantactionitemmgmt-d-ussc-01"
      vmImageName: "ubuntu-latest"
      workingDirectory: "$(System.DefaultWorkingDirectory)/action-items-creator-function/"
