from datetime import date

from pydantic import Field

from models.node_reference import PossiblyNewNode


class BaseRecurrenceInstance(PossiblyNewNode):
    weekDays: list[int] | None = None
    months: list[int] | None = None
    quarters: list[int] | None = None
    monthOfTheYear: int | None = Field(ge=1, le=12, default=None)
    dayOfTheMonth: int | None = Field(ge=1, le=31, default=None)
    nextDates: list[str] | None = None
    startDate: date | str
    endDate: date | None = None
