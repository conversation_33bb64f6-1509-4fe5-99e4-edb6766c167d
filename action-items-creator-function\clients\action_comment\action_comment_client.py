from clients.core.models import ServiceParams
from clients.action_comment.models import ActionCommentCreate
from clients.core.constants import ViewEnum
from cognite.client.data_classes.data_modeling.ids import NodeId


class ActionCommentClient:
    def __init__(self, params: ServiceParams):
        """
        Initializes the ActionCommentClient instance with the given parameters.

        Parameters:
        - params (ServiceParams): Object containing configuration settings such as:
            - cognite_client: The Cognite client used to interact with the data model.
            - logging: Logging configuration for the class to log messages.
            - get_views: Method to retrieve the views, including the ACTION_COMMENT view.
        """
        self._cognite_client = params.cognite_client
        self._log = params.logging
        self._action_comment_view = params.get_views()[ViewEnum.ACTION_COMMENT]

    def create_action_comment(
        self, action_comment: ActionCommentCreate
    ) -> list[NodeId] | None:
        """
        Creates a new action comment by applying it to the Cognite data model.

        This method prepares the action comment to be inserted into the system
        by converting it into a node format that the Cognite Data Fusion API accepts.

        Parameters:
        - action_comment (ActionCommentCreate): An object that contains the properties of the action comment
          to be created. This includes:
            - externalId (str): Identifier for the action comment.
            - properties (dict): Other properties related to the comment (e.g., text content, timestamps, etc.)

        Returns:
        - list[NodeId] | None: A list of Node IDs of the successfully created action comment nodes in the system.
          If the creation fails, None will be returned.

        Side effects:
        - The action comment is inserted into the Cognite Data Fusion system using the `apply` method.
        - A log message is generated indicating whether the creation was successful.
        """

        nodes = []

        nodes.append(
            action_comment.to_node_apply(
                self._action_comment_view, action_comment.get_properties_to_include()
            )
        )

        apply_result = self._cognite_client.data_modeling.instances.apply(
            nodes=nodes
        ).nodes.as_ids()
        
        self._log.info("ActionComment created successfully.")
        return [node.external_id for node in apply_result]
