import yaml
from cognite.client import ClientConfig, CogniteClient
from cognite.client.credentials import OAuthClientCredentials

from .configs import Config


def get_config() -> Config:
    with open("config.yml") as file:
        return Config.model_validate(yaml.safe_load(file))


def get_cognite_client(config: Config):
    env_settings = config.env_settings
    credentials = OAuthClientCredentials(
        token_url=env_settings.token_uri,
        client_id=env_settings.client_id,
        client_secret=env_settings.secret,
        scopes=env_settings.get_scopes(),
    )
    return CogniteClient(
        config=ClientConfig(
            client_name=env_settings.client_name,
            project=env_settings.project,
            credentials=credentials,
            base_url=env_settings.base_uri,
        )
    )
