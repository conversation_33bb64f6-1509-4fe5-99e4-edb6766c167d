import { AzureFunctionClient } from '../../clients/azure-function-client'
import { FilterInfoProps } from '@/app/components/ActionTable/HomeFilter'
import { FilterInfoEventProps } from '@/app/components/EventsComponent/EventFilter'
import { useCallback } from 'react'
import { SourceEventTypeExternalIdClearEnum } from '../../enums/SourceEventTypeEnum'
import { SourceEventStatusClearEnum } from '../../enums/SourceEventStatusEnum'
import { translate } from '../../utils/generate-translate'
import { UserRolesPermission } from '@celanese/celanese-sdk'
import { useSnackbar } from '../../contexts/SnackbarContext'
import { PREFIX_USER_AZURE_ATTRIBUTE } from '../../utils'

type ExportProps = {
    client: AzureFunctionClient
    columnNames: Record<string, string>
    fileName: string
    filterInfo: FilterInfoProps & FilterInfoEventProps
    activeUser?: UserRolesPermission
    siteIds?: string | string[]
    worksheetName: string
    translations?: Record<string, string>
    setIsExportLoading: (value: boolean) => void
}

const mapUserExternalIds = (ids?: string[]) =>
    ids && ids.length > 0 ? ids.map((id) => `${PREFIX_USER_AZURE_ATTRIBUTE}${id}`) : undefined

export const useExportToExcel = ({
    client,
    columnNames,
    fileName,
    filterInfo,
    activeUser,
    siteIds,
    worksheetName,
    translations,
    setIsExportLoading,
}: ExportProps) => {
    const { showSnackbar } = useSnackbar()

    const handleAlert = (success: boolean) => {
        const message = success ? translate('alerts.successExport') : translate('alerts.errorExport')
        const variant = success ? 'success' : 'error'
        showSnackbar(message, variant, 'export-table')
    }

    const fetchActions = useCallback(async () => {
        await client.exportActionsToExcel(
            {
                ...filterInfo,
                reportingSiteExternalId: filterInfo.reportingSiteExternalIds ?? siteIds ?? [],
                ownerExternalId: mapUserExternalIds(filterInfo.ownerExternalId),
                assignedToExternalId: mapUserExternalIds(filterInfo.assignedToExternalId),
                icapActionIdPrefix: filterInfo.icapActionIdPrefix
                    ? `ICAPContinuousMigration-${filterInfo.icapActionIdPrefix}`
                    : undefined,
                activeUserEmail: activeUser?.email ?? '',
                cursor: undefined,
                pageSize: 1000,
            },
            columnNames,
            fileName,
            translations
        )
    }, [client, columnNames, fileName, filterInfo, activeUser, siteIds, translations])

    const fetchEvents = useCallback(async () => {
        await client.exportSourceEventsToExcel(
            {
                ...filterInfo,
                reportingSiteExternalId: filterInfo.reportingSiteExternalIds ?? siteIds ?? [],
                ownerExternalId: mapUserExternalIds(filterInfo.ownerExternalId),
                activeUserEmail: activeUser?.email ?? '',
                cursor: undefined,
                direction: filterInfo.direction === 'ASC' ? 'ascending' : 'descending',
                pageSize: 1000,
                eventTypeExternalIds:
                    filterInfo.eventTypeExternalIds ?? Object.values(SourceEventTypeExternalIdClearEnum),
                statusExternalIds: filterInfo.statusExternalIds ?? Object.values(SourceEventStatusClearEnum),
            },
            columnNames,
            fileName,
            translations
        )
    }, [client, filterInfo, siteIds, activeUser?.email, columnNames, fileName, translations])

    const exportWithLoading = async (exportFn: () => Promise<void>) => {
        setIsExportLoading(true)
        try {
            await exportFn()
            handleAlert(true)
        } catch {
            handleAlert(false)
        } finally {
            setIsExportLoading(false)
        }
    }

    const exportToExcel = async () => {
        switch (worksheetName) {
            case 'Action':
            case 'Dashboard':
                await exportWithLoading(fetchActions)
                break
            case 'Event':
                await exportWithLoading(fetchEvents)
                break
            default:
                handleAlert(false)
        }
    }

    return { exportToExcel }
}
