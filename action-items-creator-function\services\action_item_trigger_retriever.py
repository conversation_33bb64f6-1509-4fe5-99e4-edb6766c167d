
from models.action_item import ActionItem
from services.action_item_event_processor import <PERSON><PERSON><PERSON><PERSON>rigger, ActionItemTriggerField

from models.settings import Settings
from services.cognite_service import CogniteService
from services.graphql_service import GraphqlService
from services.logging_service import LoggingService

class ActionItemTriggerRetriever:
    def __init__(
        self,
        cognite_service: CogniteService,
        graphql_service: GraphqlService,
        settings: Settings,
        logging_service: LoggingService,
    ):
        self._cognite_service = cognite_service
        self._graphql_service = graphql_service
        self._log = logging_service
        self._settings = settings

    async def get_triggers(self, application_id: str = None) -> list[ActionItemTrigger]:
        filter = {}
        if application_id is not None:
            filter["application"] = {"externalId": {"eq": application_id}}

        return await self._graphql_service.get_all_results_list(
            ACTION_ITEM_TRIGGER, "listActionItemTrigger", filter
        )
    
    async def get_condition_options(self, application_id: str = None) -> list[ActionItemTriggerField]:
        filter = {}
        if application_id is not None:
            filter["application"] = {"externalId": {"eq": application_id}}

        return await self._graphql_service.get_all_results_list(
              ACTION_ITEM_TRIGGER_FIELD, "listActionItemTriggerField", filter
        )

ACTION_ITEM_TRIGGER = """
query listActionItemTrigger($filter: _ListActionItemTriggerFilter, $after: String) {
  listActionItemTrigger(filter: $filter, first: 100, after: $after) {
    pageInfo {
      hasNextPage
      endCursor
    }
    items {
      externalId
      space
      role {
        name
        externalId
        space
      }
      reportingUnit {
        externalId
        name
        space
      }
      daysUntilDueDate
      actionItemReference {
        externalId
        name
        space
      }
      reportingSite {
        externalId
        name
        space
      }
      application {
        externalId
        name
        space
      }
      conditions(first: 100) {
        items {
          externalId
          space
          metadataField {
            externalId
            space
            metadataFieldName
          }
          metadataValue
        }
      }
      createdTime
    }
  }
}

"""

ACTION_ITEM_TRIGGER_FIELD = """
query GetActionItemTriggerField($filter: _ListActionItemTriggerFieldFilter, $after: String) {
    listActionItemTriggerField(filter: $filter, first: 1000, after: $after) {
      pageInfo {
        hasNextPage
        endCursor
      }
      items {
        externalId
        metadataFieldName
        possibleValues
        application {
          externalId
          space
        }
        space
      }
    }
  }
"""