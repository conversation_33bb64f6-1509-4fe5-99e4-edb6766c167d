import { NoTranslate } from '@celanese/celanese-ui'
import { DashboardFilterOptions } from '@/app/common/models/dashboard/filter-dashboard'
import { ClnButton } from '@celanese/ui-lib'
import { zodResolver } from '@hookform/resolvers/zod'
import { Box, Grid, styled, useMediaQuery, useTheme } from '@mui/material'
import dayjs, { Dayjs } from 'dayjs'
import { useEffect, useMemo, useState } from 'react'
import { SubmitHandler, useForm } from 'react-hook-form'
import { z } from 'zod'
import {
    transformOptions,
    transformOptionsForUser,
    transformStringOptions,
} from '@/app/common/utils/transform-options-for-filter'
import GenericAutocomplete, { AutocompleteOption } from '../../FieldsComponent/GenericAutocomplete'
import GenericDateRangePicker from '../../FieldsComponent/GenericDateRangePicker'
import { FilterInfoProps } from '../../ActionTable/HomeFilter'
import { translate } from '@/app/common/utils/generate-translate'
import { useUsersSearch } from '@/app/common/hooks/user-management/useUsers'
import { UserByManagement } from '@/app/common/models/common/user-management/user-site-configuraion'
import useDebounce from '@/app/common/hooks/general-functions/useDebounce'
import { AzureFunctionClient } from '@/app/common/clients/azure-function-client'
import {
    ActionStatusExternalIdClearEnum,
    ActionStatusExternalIdHomeDefaultEnum,
    ActionUpdateStatusEnum,
} from '@/app/common/enums/ActionItemStatusEnum'
import { getLocalUserSite } from '@celanese/celanese-ui'
import { UserRolesPermission } from '@celanese/celanese-sdk'

const zodDate = z.instanceof(dayjs as unknown as typeof Dayjs).nullable()

const filterSchema = z.object({
    reportingUnitExternalIds: z.array(z.string()).optional(),
    reportingLocationExternalIds: z.array(z.string()).optional(),
    categoryExternalId: z.array(z.string()).optional(),
    subcategoryExternalId: z.array(z.string()).optional(),
    siteSpecificCategoryExternalId: z.array(z.string()).optional(),
    statusExternalIds: z.array(z.string()).optional(),
    onlyPrivate: z.string().optional(),
    dueDate: z.tuple([zodDate.optional(), zodDate.optional()]),
    updateStatus: z.array(z.string()).optional(),
    assignedToExternalId: z.array(z.string()).optional(),
    onSiteManagerToExternalId: z.array(z.string()).optional(),
    sourceEventTitleEq: z.string().optional(),
})

type FilterSchema = z.infer<typeof filterSchema>

type DashboardFilterProps = {
    activeUser?: UserRolesPermission
    filterInfo: FilterInfoProps
    data: DashboardFilterOptions
    onSubmit: (filters: FilterInfoProps) => void
    setPage: (value: number) => void
    isSiteTab?: boolean
    employeesName?: AutocompleteOption[]
    supervisorManagers?: UserByManagement[]
    updateStatus?: string[]
}

const Form = styled('form')({
    width: '100% !important',
})

export function DashboardFilter({
    activeUser,
    filterInfo,
    data,
    onSubmit,
    setPage,
    isSiteTab = false,
    employeesName,
    supervisorManagers,
    updateStatus,
}: DashboardFilterProps) {
    const client = new AzureFunctionClient()

    const theme = useTheme()
    const isMobile = useMediaQuery(theme.breakpoints.down('sm'))

    const [clearDefaultComand, setClearDefaultComand] = useState<'default' | 'clear' | undefined>(undefined)

    const [reportingLocationByUnitOptions, setReportingLocationByUnitOptions] = useState<AutocompleteOption[]>([])
    const [employees, setEmployees] = useState<AutocompleteOption[]>(employeesName ?? [])
    const [sourceEventTitleOptions, setSourceEventTitleOptions] = useState<AutocompleteOption[]>(
        transformStringOptions(filterInfo.sourceEventTitleEq ? [filterInfo.sourceEventTitleEq] : [])
    )
    const [sourceEventTitle, setSourceEventTitle] = useState<AutocompleteOption | undefined>(
        transformStringOptions(filterInfo.sourceEventTitleEq ? [filterInfo.sourceEventTitleEq] : [])[0]
    )

    const unitOptions = transformOptions(data.unit)
    const categoryOptions = transformOptions(data.category, 'name', 'name', 'category')
    const subcategory1Options = transformOptions(data?.subcategories1 ?? [], 'name', 'name', 'subCategory1')
    const subcategory2Options = transformOptions(data?.subcategories2 ?? [], 'name')
    const statusOptions = transformOptions(data.status, 'name', 'name', 'status.fullName')
    const managerOptions = transformOptions(data?.managerName ?? [], 'name')
    const updateStatusOptions = transformStringOptions(Object.values(ActionUpdateStatusEnum))

    const isPrivateOptions = transformOptions(
        [
            { externalId: 'true', value: translate('common.yes') },
            { externalId: 'false', value: translate('common.no') },
        ],
        'value'
    )

    const [usersParam, setUsersParam] = useState<string>('NULL_PARAM')
    const { users: filteredUsers, loading: loadingAssignees } = useUsersSearch(
        useMemo(() => {
            return usersParam
        }, [usersParam])
    )

    const sortedFilteredUsers = transformOptionsForUser([...filteredUsers])
    const sortedEmployeesOptions = Array.from(
        new Map([...sortedFilteredUsers, ...employees].map((item) => [item.value, item])).values()
    ).sort((a, b) => a.label.localeCompare(b.label))

    const [sourceEventTitleParam, setSourceEventTitleParam] = useState<string>('NULL_PARAM')
    const [loadingSourceEventTitle, setLoadingSourceEventTitle] = useState<boolean>(false)
    const debouncedSourceEventTitleParam = useDebounce(sourceEventTitleParam, 500)

    const { reset, setValue, handleSubmit, control, getValues } = useForm<FilterSchema>({
        defaultValues: {
            reportingUnitExternalIds: [],
            reportingLocationExternalIds: [],
            categoryExternalId: [],
            subcategoryExternalId: [],
            siteSpecificCategoryExternalId: [],
            statusExternalIds: filterInfo.statusExternalIds,
            dueDate: [null, null],
            onlyPrivate: undefined,
            assignedToExternalId: [],
            onSiteManagerToExternalId: isSiteTab ? [] : filterInfo.onSiteManagerToExternalId,
            updateStatus: [],
            sourceEventTitleEq: '',
        },
        resolver: zodResolver(filterSchema),
    })

    const getEmployeesFilter = (filters: FilterSchema) => {
        if (!filters.onSiteManagerToExternalId?.length) {
            return filters.assignedToExternalId
        }

        const filteredEmployees =
            supervisorManagers
                ?.filter((manager) => filters.onSiteManagerToExternalId?.includes(manager.externalId ?? ''))
                .flatMap((manager) => getFilteredEmployees(manager, filters.assignedToExternalId)) ?? []

        return filteredEmployees.length ? filteredEmployees : ['-']
    }

    const getFilteredEmployees = (manager: UserByManagement, assignedToExternalIds?: string[]) => {
        if (!assignedToExternalIds?.length) {
            return manager.employees?.map((employee) => employee.userAzureAttribute?.user?.externalId ?? '') ?? []
        }

        return (
            manager.employees
                ?.filter((employee) =>
                    assignedToExternalIds.includes(employee.userAzureAttribute?.user?.externalId ?? '')
                )
                .map((employee) => employee.userAzureAttribute?.user?.externalId ?? '') ?? []
        )
    }

    const setUpdateStatusFilter = (filterOptions: string[]) => {
        const updateStatusDate: any = []
        const currentDate = dayjs().startOf('day').format('YYYY-MM-DD')

        const isCurrentTasks = filterOptions.includes('Current tasks')
        const isDueWithin30Days = filterOptions.includes('Due Within 30 days')
        const isDueWithin7Days = filterOptions.includes('Due Within 7 days')
        const isOverdue = filterOptions.includes('Overdue')

        if (isOverdue) {
            updateStatusDate.push([null, dayjs().subtract(1, 'day').format('YYYY-MM-DD')])
        }

        if (isDueWithin7Days) {
            updateStatusDate.push([currentDate, dayjs().add(7, 'day').format('YYYY-MM-DD')])
        }

        if (isDueWithin30Days) {
            updateStatusDate.push([currentDate, dayjs().add(30, 'day').format('YYYY-MM-DD')])
        }

        if (isCurrentTasks) {
            updateStatusDate.push([dayjs().add(30, 'day').format('YYYY-MM-DD'), null])
        }

        return updateStatusDate?.length > 0 ? updateStatusDate : undefined
    }

    const clearFunction = () => {
        setClearDefaultComand('clear')
        setEmployees([])
        setSourceEventTitle(undefined)
        reset({
            reportingUnitExternalIds: [],
            reportingLocationExternalIds: [],
            categoryExternalId: [],
            subcategoryExternalId: [],
            siteSpecificCategoryExternalId: [],
            statusExternalIds: [],
            dueDate: [null, null],
            onlyPrivate: undefined,
            assignedToExternalId: [],
            onSiteManagerToExternalId: [],
            updateStatus: [],
            sourceEventTitleEq: '',
        })
    }

    const defaultFunction = () => {
        setClearDefaultComand('default')
        setEmployees([])
        setSourceEventTitle(undefined)
        reset({
            reportingUnitExternalIds: [],
            reportingLocationExternalIds: [],
            categoryExternalId: [],
            subcategoryExternalId: [],
            siteSpecificCategoryExternalId: [],
            statusExternalIds: [...Object.values(ActionStatusExternalIdHomeDefaultEnum)],
            dueDate: [null, null],
            onlyPrivate: undefined,
            assignedToExternalId: [],
            onSiteManagerToExternalId: [],
            updateStatus: [],
            sourceEventTitleEq: '',
        })
    }

    const submitFn: SubmitHandler<FilterSchema> = (data) => {
        const transformData = (input: any) => {
            if (Array.isArray(input) && input?.length === 0) return undefined
            if (typeof input === 'string' && input.trim() === '') return undefined
            return input
        }

        const filteredEmployees = getEmployeesFilter(data)

        const transformedData: FilterInfoProps = {
            ...filterInfo,
            cursor: undefined,
            statusExternalIds:
                transformData(data.statusExternalIds) ??
                Object.values(
                    clearDefaultComand === 'default'
                        ? ActionStatusExternalIdHomeDefaultEnum
                        : ActionStatusExternalIdClearEnum
                ),
            categoryExternalId: transformData(data.categoryExternalId),
            subcategoryExternalId: transformData(data.subcategoryExternalId),
            siteSpecificCategoryExternalId: transformData(data.siteSpecificCategoryExternalId),
            reportingUnitExternalIds: transformData(data.reportingUnitExternalIds),
            reportingLocationExternalIds: transformData(data.reportingLocationExternalIds),
            dueDateGte: data.dueDate[0] ? dayjs(data.dueDate[0]).format('YYYY-MM-DD') : undefined,
            dueDateLt: data.dueDate[1] ? dayjs(data.dueDate[1]).format('YYYY-MM-DD') : undefined,
            onlyPrivate: data.onlyPrivate ? data.onlyPrivate === 'true' : undefined,
            assignedToExternalId: transformData(filteredEmployees),
            onSiteManagerToExternalId: transformData(data.onSiteManagerToExternalId),
            updateStatusDate: data?.updateStatus ? setUpdateStatusFilter(data?.updateStatus ?? []) : undefined,
            sourceEventTitleEq: transformData(data.sourceEventTitleEq),
        }

        onSubmit({ ...transformedData })
        sessionStorage.setItem(
            `dashboard-filterInfo-${isSiteTab ? 'site' : 'supervisor'}-tab`,
            JSON.stringify({
                filter: transformedData,
                employeesName: employees,
                updateStatus: data?.updateStatus,
            })
        )
        setPage(0)
    }

    const handleChangeUnit = (selectedUnitIds: string[]) => {
        const locationsByUnit =
            selectedUnitIds?.length === 0
                ? data.location
                : data.location.filter((location) => selectedUnitIds.includes(location.reportingUnit?.externalId || ''))

        const transformedLocations = transformOptions(locationsByUnit)

        setReportingLocationByUnitOptions(transformedLocations)

        const reportingUnitExternalIds = selectedUnitIds.map(
            (unitId) => data.unit.find((unit) => unit.externalId === unitId)?.externalId || ''
        )

        const currentReportingLocationIds = getValues('reportingLocationExternalIds') || []
        const validReportingLocationIds = currentReportingLocationIds.filter((locationId: string) =>
            locationsByUnit.some((location) => location.externalId === locationId)
        )

        setValue('reportingUnitExternalIds', reportingUnitExternalIds)
        setValue('reportingLocationExternalIds', validReportingLocationIds)
    }

    useEffect(() => {
        setReportingLocationByUnitOptions(transformOptions(data.location))
    }, [data.location])

    useEffect(() => {
        const fetchData = async () => {
            if (debouncedSourceEventTitleParam && debouncedSourceEventTitleParam !== 'NULL_PARAM') {
                setLoadingSourceEventTitle(true)
                try {
                    const { siteId } = getLocalUserSite() || {}

                    const sourceEventsTitles = await client.getSourceEventTitle({
                        search: debouncedSourceEventTitleParam,
                        activeUserEmail: activeUser?.email ?? '',
                        reportingSiteExternalId: siteId ?? '-',
                    })
                    setSourceEventTitleOptions(
                        sourceEventsTitles.items.map((item: string) => {
                            return { label: item, value: item }
                        })
                    )
                } catch {
                } finally {
                    setLoadingSourceEventTitle(false)
                }
            }
        }

        fetchData()
    }, [debouncedSourceEventTitleParam])

    const renderForm = () => (
        <Form onSubmit={handleSubmit(submitFn)} id={'dashboard-filter'}>
            <Grid
                container
                spacing={2}
                sx={{
                    width: isMobile ? '100%' : '400px',
                    flexDirection: 'column',
                    padding: 2,
                }}
            >
                <Grid item id={'button-mobile-filter-dashboard-site'}>
                    <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end' }}>
                        <ClnButton
                            size="medium"
                            variant="outlined"
                            label={translate('table.filter.clear')}
                            onClick={clearFunction}
                        />
                        <ClnButton
                            size="medium"
                            variant="outlined"
                            label={translate('table.filter.default')}
                            onClick={defaultFunction}
                        />
                    </Box>
                </Grid>
                <Grid item id={'dueDate-filter-dashboard-site'}>
                    <GenericDateRangePicker name="dueDate" control={control} size="small" />
                </Grid>
                <Grid item id={'event-title-filter-dashboard-site'}>
                    <GenericAutocomplete
                        name="sourceEventTitleEq"
                        control={control}
                        options={sourceEventTitleOptions}
                        valueController={sourceEventTitle}
                        onChange={(newValue, fieldOption) => {
                            setValue('sourceEventTitleEq', newValue)
                            setSourceEventTitle(fieldOption)
                        }}
                        label={translate('dashboards.filters.sourceEventTitle')}
                        loading={loadingSourceEventTitle}
                        placeholderTextField={translate('stepper.form.typeAtLeast3')}
                        onInputChange={(event, newInputValue) => {
                            if (newInputValue?.length >= 3) {
                                setSourceEventTitleParam(newInputValue)
                            }
                        }}
                        size="small"
                        multiple={false}
                        disableCloseOnSelect={false}
                    />
                </Grid>
                <Grid item id={'unit-filter-dashboard-site'}>
                    <NoTranslate>
                        <GenericAutocomplete
                            name="reportingUnitExternalIds"
                            control={control}
                            options={unitOptions}
                            onChange={handleChangeUnit}
                            label={translate('dashboards.filters.unit')}
                            size="small"
                        />
                    </NoTranslate>
                </Grid>
                <Grid item id={'location-filter-dashboard-site'}>
                    <GenericAutocomplete
                        name="reportingLocationExternalIds"
                        control={control}
                        options={reportingLocationByUnitOptions}
                        onChange={(newValue) => setValue('reportingLocationExternalIds', newValue)}
                        label={translate('dashboards.filters.reportingLocation')}
                        size="small"
                    />
                </Grid>
                <Grid item id={'category-filter-dashboard-site'}>
                    <GenericAutocomplete
                        name="categoryExternalId"
                        control={control}
                        options={categoryOptions}
                        onChange={(newValue) => setValue('categoryExternalId', newValue)}
                        label={translate('dashboards.filters.category')}
                        size="small"
                    />
                </Grid>
                {isSiteTab && (
                    <>
                        <Grid item id={'subcategories1-filter-dashboard-site'}>
                            <GenericAutocomplete
                                name="subcategoryExternalId"
                                control={control}
                                options={subcategory1Options}
                                onChange={(newValue) => setValue('subcategoryExternalId', newValue)}
                                label={translate('dashboards.filters.subcategoryOne')}
                                size="small"
                            />
                        </Grid>
                        <Grid item id={'subcategories2-filter-dashboard-site'}>
                            <GenericAutocomplete
                                name="siteSpecificCategoryExternalId"
                                control={control}
                                options={subcategory2Options}
                                onChange={(newValue) => setValue('siteSpecificCategoryExternalId', newValue)}
                                label={translate('dashboards.filters.subcategoryTwo')}
                                size="small"
                            />
                        </Grid>
                    </>
                )}

                <Grid item id={'status-filter-dashboard-site'}>
                    <GenericAutocomplete
                        name="statusExternalIds"
                        control={control}
                        options={statusOptions}
                        onChange={(newValue) => setValue('statusExternalIds', newValue)}
                        label={translate('dashboards.filters.status')}
                        size="small"
                    />
                </Grid>
                {!isSiteTab && (
                    <>
                        <Grid item id={'employeeName-filter-dashboard-supervisor'}>
                            <GenericAutocomplete
                                name="assignedToExternalId"
                                control={control}
                                options={sortedEmployeesOptions}
                                valueController={employees}
                                onChange={(newValue, fieldOption) => {
                                    setValue('assignedToExternalId', newValue)
                                    setEmployees(fieldOption ?? [])
                                }}
                                label={translate('dashboards.filters.employeeName')}
                                loading={loadingAssignees}
                                placeholderTextField={translate('stepper.form.typeAtLeast3')}
                                onInputChange={(event, newInputValue) => {
                                    if (newInputValue?.length >= 3) {
                                        setUsersParam(newInputValue)
                                    }
                                }}
                                size="small"
                            />
                        </Grid>
                        <Grid item id={'managerName-filter-dashboard-supervisor'}>
                            <GenericAutocomplete
                                name="onSiteManagerToExternalId"
                                control={control}
                                options={managerOptions}
                                onChange={(newValue) => setValue('onSiteManagerToExternalId', newValue)}
                                label={translate('dashboards.filters.managerName')}
                                size="small"
                            />
                        </Grid>
                        <Grid item id={'updateStatus-filter-dashboard-supervisor'}>
                            <GenericAutocomplete
                                name="updateStatus"
                                control={control}
                                options={updateStatusOptions}
                                onChange={(newValue) => setValue('updateStatus', newValue)}
                                label={translate('dashboards.filters.updatedStatus')}
                                size="small"
                            />
                        </Grid>
                    </>
                )}
                <Grid item id={'is-private-filter-dashboard-site'}>
                    <GenericAutocomplete
                        name="onlyPrivate"
                        multiple={false}
                        control={control}
                        options={isPrivateOptions}
                        onChange={(newValue) => setValue('onlyPrivate', newValue)}
                        label={translate('dashboards.filters.isPrivate')}
                        size="small"
                    />
                </Grid>
                <Grid item id={'button-mobile-apply-filter-dashboard-site'}>
                    <Box sx={{ display: 'flex', justifyContent: 'center', width: '100%' }}>
                        <ClnButton
                            type="submit"
                            size="medium"
                            variant="contained"
                            label={translate('table.filter.applyFilter')}
                        />
                    </Box>
                </Grid>
            </Grid>
        </Form>
    )

    useEffect(() => {
        const onlyPrivateAsString = filterInfo.onlyPrivate ? 'true' : 'false'
        const transformedFilter = {
            ...filterInfo,
            onlyPrivate: filterInfo.onlyPrivate === undefined ? undefined : onlyPrivateAsString,
            dueDate: [
                filterInfo.dueDateGte ? dayjs(filterInfo.dueDateGte) : null,
                filterInfo.dueDateLt ? dayjs(filterInfo.dueDateLt) : null,
            ] as [Dayjs | null, Dayjs | null],
            updateStatus: updateStatus,
        }

        reset(transformedFilter)
    }, [filterInfo])

    return <Box>{renderForm()}</Box>
}
