import React from 'react'
import Popover, { PopoverOrigin, PopoverProps } from '@mui/material/Popover'
import { SxProps, Theme, useMediaQuery, useTheme } from '@mui/material'

export interface PopoverFilterBaseProps extends Omit<PopoverProps, 'open'> {
    anchorEl: HTMLElement | null
    onClose: () => void
    sxProps?: SxProps<Theme>
    id?: string
    anchorOrigin?: PopoverOrigin
    transformOrigin?: PopoverOrigin
}

export const PopoverFilterBase: React.FC<PopoverFilterBaseProps> = (props) => {
    const { anchorEl, anchorOrigin, transformOrigin, onClose, sxProps, children, id } = props
    const open = Boolean(anchorEl)

    const theme = useTheme()
    const isMobile = useMediaQuery(theme.breakpoints.down('md'))

    return (
        <Popover
            id={id ?? 'popover-default'}
            open={open}
            anchorEl={isMobile ? undefined : anchorEl}
            anchorPosition={isMobile ? { top: 0, left: 0 } : undefined}
            onClose={onClose}
            disableScrollLock={true}
            marginThreshold={isMobile ? 0 : 48}
            anchorOrigin={
                anchorOrigin ?? {
                    vertical: isMobile ? 'center' : 'bottom',
                    horizontal: isMobile ? 'center' : 'left',
                }
            }
            transformOrigin={
                transformOrigin ?? {
                    vertical: isMobile ? 'center' : 'top',
                    horizontal: isMobile ? 'center' : 'left',
                }
            }
            sx={{
                ...sxProps,
                width: {
                    xs: '100% !important',
                    sm: 'auto',
                },
                maxWidth: {
                    xs: '100% !important',
                },
            }}
        >
            {children}
        </Popover>
    )
}
