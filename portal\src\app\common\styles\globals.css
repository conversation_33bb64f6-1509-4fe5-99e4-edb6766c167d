.celia * {
  transition: bottom 0.5s ease 0s;
}

.mobile ::-webkit-scrollbar {
  width: 0px;
}

.desktop ::-webkit-scrollbar {
  width: 5px;
}

.desktop ::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.desktop ::-webkit-scrollbar-thumb {
  background: rgba(91, 91, 91, 0.5);
  border-radius: 10px;
}

.desktop ::-webkit-scrollbar-thumb:hover {
  background: #555;
}

.baseContainer {
  display: flex;
}

body {
  overflow-y: hidden;
}

.material-symbols-outlined {
  font-family: 'Material Symbols Outlined', sans-serif;
  line-height: 1;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.logo-icon {
  line-height: 1 !important;
}

.slick-slider .slick-track {
  gap: 0.5rem;
  display: flex;
  margin-left: 0;
}

.slick-slider .slick-disabled {
  display: none !important;
}

.slick-slider .slick-arrow {
  color: var(--mui-palette-grey-600);
}

.slick-slider .slick-arrow:hover {
  color: var(--mui-palette-info-main);
}

.rbc-calendar .rbc-time-content,
.rbc-calendar .rbc-time-content > * + * > *,
.rbc-calendar .rbc-timeslot-group,
.rbc-calendar .rbc-time-view {
  border: none !important;
}

.rbc-calendar .rbc-time-content {
  padding: 1rem 0.75rem 0 0;
}

.rbc-calendar .rbc-time-header {
  display: none !important;
}

.rbc-calendar .rbc-timeslot-group {
  background-color: var(--mui-palette-primary-contrastText);
  min-height: 3.25rem;
}

.rbc-calendar .rbc-time-slot {
  color: var(--mui-palette-grey-400);
  font-size: 0.875rem;
  transform: translateY(-0.8rem);
}

.rbc-calendar .rbc-label {
  margin-right: 0.25rem;
}

.rbc-calendar .rbc-day-slot .rbc-timeslot-group {
  border-top: 1px solid var(--mui-palette-grey-400) !important;
}

.rbc-calendar .rbc-day-slot .rbc-timeslot-group .rbc-time-slot {
  border: none;
}

.rbc-calendar .rbc-day-slot .rbc-event {
  border-radius: 0;
  background-color: var(--mui-palette-info-contrastText);
  border-width: 1px 0 1px 0 !important;
  border-color: var(--mui-palette-primary-contrastText);
  padding-left: 0.6rem;
  position: relative;
}

.rbc-calendar .rbc-day-slot .rbc-event:focus {
  outline: none;
}

.rbc-calendar .rbc-day-slot .rbc-event:before {
  content: '';
  height: 100%;
  width: 5px;
  position: absolute;
  left: 0;
  top: 0;
  background: var(--mui-palette-info-light);
}

.rbc-calendar .rbc-day-slot .rbc-events-container {
  margin-right: 0;
}

.rbc-calendar .rbc-day-slot .rbc-event-label,
.rbc-calendar .rbc-day-slot .rbc-current-time-indicator {
  display: none;
}

.rbc-calendar .rbc-day-slot * > .rbc-background-event:focus {
  outline: none !important;
}

.ql-container {
  padding: 0.5rem !important;
}

.ql-editor {
  padding: 0 !important;
}

.ql-editor ol {
  padding-left: 0.5rem !important;
}

.ql-container,
.ql-toolbar {
  border: none !important;
}

.ql-container button,
.ql-toolbar button {
  width: 1rem !important;
  height: 1.3rem !important;
  padding: 0 !important;
  display: flex !important;
  min-height: 0 !important;
  min-width: 0 !important;
}

.visited-link:visited {
  color: #4e7389;
}