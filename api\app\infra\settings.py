from pydantic import Field, computed_field
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Lists relevant environment variables."""

    # auth
    auth_token_uri: str = Field(
        default="",
    )
    auth_client_id: str = Field(
        default="",
    )
    auth_secret: str = Field(
        default="",
    )
    auth_scopes_raw: str = Field(
        default="",
        alias="auth_scopes",
    )
    override_token: str | None = Field(default=None)

    # cognite
    cognite_client_name: str = Field(
        default="app-aim-function",
    )
    cognite_project: str = Field(
        default="celanese-dev",
    )
    cognite_base_uri: str = Field(
        default="",
    )
    cognite_action_item_management_data_model_version: str = Field(
        default="",
    )

    # config
    valid_origins_regex: str = Field(
        default="",
    )

    @computed_field
    @property
    def auth_scopes(self) -> list[str]:
        """Return auth_scopes by splitting raw input."""
        return self.auth_scopes_raw.split(",")
