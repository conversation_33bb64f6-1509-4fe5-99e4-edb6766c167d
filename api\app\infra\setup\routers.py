from importlib import import_module
from pathlib import Path

from fastapi import APIRouter, FastAPI


def _add_routers(app: FastAPI, base_path: Path, import_path: str) -> None:
    for entry in base_path.iterdir():
        if entry.is_dir() and not entry.name.startswith("_"):
            _add_routers(app, entry, import_path + "." + entry.name)

        if entry.name == "router.py":
            router_module = import_module(import_path + ".router")
            router_obj = router_module.router
            if not isinstance(router_obj, APIRouter):
                msg = f"Variable \"router\" of type APIRouter was not found within {base_path.joinpath('router.py')}."
                raise ImportError(msg)
            app.include_router(router_obj)


def add_routers(app: FastAPI) -> None:
    """Recursively looks for router files with F and include them in the FastAPI application."""
    base_path = Path(__file__).parent.parent.parent.joinpath("modules")
    _add_routers(app, base_path, "app.modules")
