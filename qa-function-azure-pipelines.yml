trigger:
  branches:
    include:
      - qa
  paths:
    include:
      - action-items-creator-function/*

stages:
  - template: function-template.yml
    parameters:
      azureSubscription: "Action Item Management Deploy - QA"
      functionAppName: "func-dplantactionitemmgmt-qa-ussc-01"
      vmImageName: "ubuntu-latest"
      workingDirectory: "$(System.DefaultWorkingDirectory)/action-items-creator-function/"
