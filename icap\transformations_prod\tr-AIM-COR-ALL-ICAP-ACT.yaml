# Manifest file downloaded from fusion
externalId: tr-AIM-COR-ALL-ICAP-ACT
name: AIM-COR-ALL-ICAP-ACT
query: |-
  SELECT *
  FROM `AIM-COR`.`ICAP-STG-Action`
  WHERE
  	externalId IS NOT NULL
  	AND space IS NOT NULL
  	AND reportingSite IS NOT NULL 
    	AND reportingUnit IS NOT NULL
  	AND category IS NOT NULL
  	AND subCategory IS NOT NULL 
  	AND dueDate IS NOT NULL
  	AND displayDueDate IS NOT NULL
  	AND assignmentDate IS NOT NULL
  	AND assignedTo IS NOT NULL
  	AND owner IS NOT NULL
  	AND createdBy IS NOT NULL
  	AND currentStatus IS NOT NULL
  	AND viewOnly
  	-- AND isPrivate
destination:
  dataModel:
    space: AIM-COR-ALL-DMD
    externalId: ActionItemManagementDOM
    version: "6_0_0"
    destinationType: Action
  type: instances
ignoreNullFields: false
shared: true
action: upsert
dataSetExternalId: AIM-COR-ALL-DAT
# Specify credentials separately like this:
# authentication:
#   read:
#     clientId: ${READ_CLIENT_ID}
#     clientSecret: ${READ_CLIENT_SECRET}
#     tokenUrl: ${READ_TOKEN_URL}
#     cdfProjectName: ${READ_CDF_PROJECT_NAME}
#     # Optional: If idP requires providing the scopes
#     scopes:
#       - ${READ_SCOPES}
#     # Optional: If idP requires providing the audience
#     audience: ${READ_CDF_AUDIENCE}
#   write:
#     clientId: ${WRITE_CLIENT_ID}
#     clientSecret: ${WRITE_CLIENT_SECRET}
#     tokenUrl: ${WRITE_TOKEN_URL}
#     cdfProjectName: ${WRITE_CDF_PROJECT_NAME}
#     # Optional: If idP requires providing the scopes
#     scopes:
#       - ${WRITE_SCOPES}
#     # Optional: If idP requires providing the audience
#     audience: ${WRITE_CDF_AUDIENCE}
# Or together like this:
# authentication:
#   clientId: ${CLIENT_ID}
#   clientSecret: ${CLIENT_SECRET}
#   tokenUrl: ${TOKEN_URL}
#   # Optional: If idP requires providing the scopes
#   cdfProjectName: ${CDF_PROJECT_NAME}
#   scopes:
#    - ${SCOPES}
#   # Optional: If idP requires providing the audience
#   audience: ${AUDIENCE}