import { Dayjs } from 'dayjs'
import { ExternalEntity } from '..'
import { ApproverWorkflowEntity } from './approver-entity'
import { ApprovalWorkflowCondition } from './workflow-condition'
import { ApprovalWorkflowStepApproval } from './workflow-step-approval'
import { ApprovalWorkflowStatus } from './workflow-status'
import { ApprovalWorkflowConsentType } from './workflow-consent-type'
import { User } from '../user-management/user'
import { ApprovalWorkflow } from '../../action-detail'
import { EdgeInstance } from '../cognite/edge-instance'

export interface WorkflowStep extends ExternalEntity {
    step: number
    status: ApprovalWorkflowStatus
    description?: string
    users?: { items: User[]; edges: EdgeInstance[] }
    workflowStepApproval: ApprovalWorkflowStepApproval
    approverEntity: ApproverWorkflowEntity
    approvalCondition: ApprovalWorkflowCondition
}

export interface ApprovalWorkflowStep extends ExternalEntity {
    approvalWorkflow: ApprovalWorkflow
    step: number
    startDate: Dayjs
    endDate?: Dayjs
    status: ApprovalWorkflowStatus
    description?: string
    approvalCondition: ApprovalWorkflowCondition
    users?: { items: User[]; edges: EdgeInstance[] }
    approvalWorkflowConsentType: ApprovalWorkflowConsentType
    approvalDueDate?: string
    approverEntity?: ApproverWorkflowEntity
}
