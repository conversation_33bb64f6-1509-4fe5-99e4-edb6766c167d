import { Dayjs } from 'dayjs'
import { Category, ReportingUnitHomeTable, Status } from '../action'
import { ReportingLocation } from '../common/asset-hierarchy/reporting-location'
import { UserByManagement } from '../common/user-management/user-site-configuraion'
import { SiteSpecificCategory } from '../site-specific-category'
import { ActionItemSubCategory } from '../sub-category'

export interface DashboardFilterOptions {
    unit: ReportingUnitHomeTable[]
    location: ReportingLocation[]
    category: Category[]
    subcategories1?: ActionItemSubCategory[]
    subcategories2?: SiteSpecificCategory[]
    status: Status[]
    dueDate: [Dayjs | null, Dayjs | null]
    isClear?: boolean
    updateStatus?: string[]
    assignee?: string[]
    managerName?: UserByManagement[]
}
