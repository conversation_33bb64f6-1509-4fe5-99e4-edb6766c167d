import { gql } from '@apollo/client'
import { useEffect, useState } from 'react'
import { useGraphqlQuery } from '../cognite/useGraphqlQuery'
import { EntityType, GetSpace } from '../../utils/space-util'
import { Application } from '../../models/application'

const buildApplicationQuery = (): string => {
    const filters: string[] = []

    filters.push(`{ space: { eq: "${GetSpace(EntityType.UMG)}" } }`)

    const queryFilter = `{ and: [ ${filters.join(', ')} ] }`

    return `
        query GetApplication {
            listApplication(
                filter: ${queryFilter}
                , first: 1000
                , sort: { name: ASC }
            ) {
                items {
                    name
                    alias
                    space
                    externalId
                    url
                }
            }
        }
    `
}

export const useApplications = () => {
    const query = buildApplicationQuery()
    const { data: fdmData } = useGraphqlQuery<Application>(gql(query), 'listApplication', {})

    const [resultData, setResultData] = useState<{ data: Application[]; loading: boolean }>({
        data: [],
        loading: true,
    })

    useEffect(() => {
        if (fdmData.length == 0) {
            setResultData({ data: [], loading: false })
        } else {
            setResultData({ data: fdmData, loading: false })
        }
    }, [fdmData])

    return {
        loading: resultData.loading,
        applications: resultData.data,
    }
}
