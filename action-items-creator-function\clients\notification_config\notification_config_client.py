from typing import Optional

from .queries import GET_NOTIFICATION_CONFIGS

from clients.core.constants import AGGREGATE_LIMIT
from clients.core.filters import eq_filter
from clients.core.models import PaginatedData, ServiceParams
from clients.notification_config.models import NotificationConfigResult
from clients.notification_config.requests import GetNotificationConfigsRequest


class NotificationConfigClient:
    def __init__(
        self,
        params: ServiceParams,
    ):
        self._cognite_client = params.cognite_client
        self._data_model_id = params.data_model.as_id()

    def get_notification_configs(
        self,
        request: GetNotificationConfigsRequest,
    ) -> Optional[PaginatedData[NotificationConfigResult]]:
        variables = {
            "filter": {
                "notificationType": eq_filter("externalId", request.notification_type)
            }
        }

        try:
            result = self._cognite_client.data_modeling.graphql.query(
                id=self._data_model_id,
                query=GET_NOTIFICATION_CONFIGS,
                variables=variables,
            )

            return PaginatedData[NotificationConfigResult].from_graphql_response(
                result, AGGREGATE_LIMIT
            )
        except Exception:
            return None
