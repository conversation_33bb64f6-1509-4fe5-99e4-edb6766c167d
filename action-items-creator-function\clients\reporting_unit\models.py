from typing import Annotated

from clients.core.models import Node
from clients.core.validators import edge_unwraper_validator
from clients.reporting_site.models import ReportingSiteResult


class ReportingUnitResult(Node):
    """Represents a reporting unit, including its description, name, and related reporting sites."""
    
    description: str | None = None
    name: str | None = None
    reporting_sites: Annotated[list[ReportingSiteResult], edge_unwraper_validator]
