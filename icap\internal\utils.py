import os
from pathlib import Path
from tempfile import TemporaryDirectory
from zipfile import ZipFile

import json

import aiofiles
from croniter import croniter


def zip_folder(
    folder: str,
    ignore_folders: list[str] | None = None,
    ignore_files: list[str] | None = None,
) -> bytes:
    ignore_folders = ignore_folders or []
    ignore_files = ignore_files or []

    with TemporaryDirectory() as tmpdir:
        zip_path = Path(tmpdir, "function.zip")
        with ZipFile(zip_path, "w") as zf:
            for root, dirs, files in os.walk(folder):
                dirs[:] = [d for d in dirs if d not in ignore_folders]

                for file in files:
                    if file in ignore_files:
                        continue
                    file_path = os.path.join(root, file)

                    arcname = os.path.relpath(file_path, folder)
                    zf.write(file_path, arcname)

        return zip_path.read_bytes()


async def load_json_async(file_path):
    async with aiofiles.open(file_path, mode="r", encoding="utf-8") as f:
        content = await f.read()
        return json.loads(content)


def has_duplicates(arr: list[str]):
    return len(arr) != len(set(arr))


def is_valid_cron(expression):
    try:
        croniter(expression)  # Try to parse the cron expression
        return True
    except Exception:
        return False
