GET_ACTIONS_QUERY = """
query GetActions(
  $cursor: String,
  $pageSize: Int, $actionItemFilter: _ListActionFilter,
  $userSiteConfigurationsFilter: _ListUserSiteConfigurationFilter,
  $approvalWorkflowStepFilter: _ListApprovalWorkflowStepFilter,
  $userOnApprovalWorkflowStepFilter: _ListUserFilter,
  $sourceEventFilter: _ListSourceEventFilter,
  $changeRequestFilter: _ListActionItemChangeRequestFilter,
  $sorting: _ActionSort!
) {
  listAction(
    after: $cursor,
    first: $pageSize,
    filter: $actionItemFilter,
    sort: [$sorting]
  ) {
    items {
      externalId
      space
      title
      currentStatus {
        externalId
        space
        name
      }
      priority
      isPrivate
      dueDate
      displayDueDate
      assignmentDate
      approvalDate
      verificationDate
      conclusionDate
      category {
        externalId
        space
        name
      }
      sourceInformation
      sourceId
      sourceType {
        externalId
        space
        name
      }
      subCategory {
        externalId
        space
        name
      }
      siteSpecificCategory {
        externalId
        space
        name
        description
      }
      actionItemKind {
        externalId
        space
        name
      }
      reportingSite {
        externalId
        space
        siteCode
        name
        description
      }
      reportingUnit {
        externalId
        space
        name
        description
      }
      reportingLocation {
        externalId
        space
        name
        description
      }
      application {
        externalId
        space
        name
        alias
      }
      assignedTo {
        externalId
        space
        user {
          externalId
          space
          displayName
          firstName
          lastName
          email
        }
        userComplement {
          externalId
          space
          userSiteConfigurations(first: 1, filter: $userSiteConfigurationsFilter) {
            items {
              externalId
              space
              onSiteManager {
                externalId
                space
                displayName
                firstName
                lastName
                email
              }
            }
          }
        }
      }
      owner {
        externalId
        space
        user {
          externalId
          space
          displayName
          firstName
          lastName
          email
        }
        userComplement {
          externalId
          space
        }
      }
      approvalWorkflow {
        externalId
        space
        status {
          externalId
          space
          name
        }
        steps(first: 1000, filter: $approvalWorkflowStepFilter) {
          items {
            externalId
            space
            status {
              externalId
              space
              description
            }
            description
            users(first: 1, filter: $userOnApprovalWorkflowStepFilter) {
              items {
                externalId
                space
                displayName
                firstName
                lastName
                email
              }
            }
          }
        }
      }
      sourceEvents(first: 1, filter: $sourceEventFilter) {
        items {
          externalId
          space
          title
        }
      }
      changeRequests(first: 1000, filter: $changeRequestFilter) {
       items{
          externalId
          space
          changeRequestType{
            externalId
            space
          }
          approvalWorkflow{
            externalId
            space
            status{
              externalId
              space
              name
            }
            steps {
              items {
                externalId
                space
              }
            }
          }
        }
      }
      objectType
      metadatas {
        items{
          externalId
          space
          value
          metadataType {
            externalId
            space
            name
          }
        }
      }
    }
    pageInfo {
      endCursor
      hasNextPage
    }
  }
}
"""

GET_ACTION_BY_ID_QUERY = """
query GetActions(
  $cursor: String,
  $pageSize: Int,
  $actionItemFilter: _ListActionFilter,
  $userSiteConfigurationsFilter: _ListUserSiteConfigurationFilter,
  $approvalWorkflowStepFilter: _ListApprovalWorkflowStepFilter,
  $userOnApprovalWorkflowStepFilter: _ListUserFilter,
  $sourceEventFilter: _ListSourceEventFilter,
  $changeRequestFilter: _ListActionItemChangeRequestFilter,
  $sorting: _ActionSort!
) {
  listAction(
    after: $cursor
    first: $pageSize
    filter: $actionItemFilter
    sort: [$sorting]
  ) {
    items {
      externalId
      space
      title
      description
      assigneeComment
      currentStatus {
        externalId
        space
        name
      }
      dueDate
      displayDueDate
      assignmentDate
      approvalDate
      verificationDate
      conclusionDate
      evidenceRequired
      viewOnly
      isPrivate
      voeActionItem
      attachments {
        externalId
        name
        mimeType
        source
        uploadedTime
        metadata
      }
      actionItemLink {
        items {
          externalId
          space
          description
          link
        }
      }
      category {
        externalId
        space
        name
        description
      }
      priority
      sourceInformation
      sourceId
      sourceType {
        externalId
        space
        name
        description
      }
      subCategory {
        externalId
        space
        name
        description
      }
      siteSpecificCategory {
        externalId
        space
        name
        description
      }
      actionItemKind {
        externalId
        space
        name
        description
      }
      recurrenceInstance {
        externalId
        space
      }
      reportingUnit {
        externalId
        space
        name
        description
      }
      reportingLocation {
        externalId
        space
        name
        description
      }
      reportingSite {
        externalId
        space
        siteCode
        name
        description
      }
      application {
        externalId
        space
        name
        description
        alias
      }
      isTemplate
      assignedTo {
        externalId
        space
        user {
          externalId
          space
          displayName
          firstName
          lastName
          email
        }
        userComplement {
          externalId
          space
          userSiteConfigurations(first: 1, filter: $userSiteConfigurationsFilter) {
            items {
              externalId
              space
              onSiteManager {
                externalId
                space
                displayName
                firstName
                lastName
                email
              }
            }
          }
        }
      }
      owner {
        externalId
        space
        user {
          externalId
          space
          displayName
          firstName
          lastName
          email
        }
        userComplement {
          externalId
          space
        }
      }
      approvalWorkflow {
        externalId
        space
        status {
          externalId
          space
          name
        }
        steps(first: 1000, filter: $approvalWorkflowStepFilter) {
          items {
            externalId
            space
            status {
              externalId
              space
              description
            }
            description
            users(first: 1, filter: $userOnApprovalWorkflowStepFilter) {
              items {
                externalId
                space
                displayName
                firstName
                lastName
                email
              }
              edges {
                externalId
                space
              }
            }
          }
        }
      }
      sourceEvents(first: 1, filter: $sourceEventFilter) {
        items {
          externalId
          space
          title
          owner {
            externalId
            space
          }
          secondaryOwnerUsers {
            items {
              externalId
              space
            }
          }
          secondaryOwnerRoles {
            items {
              externalId
              space
            }
          }
          secondaryOwnerTeams {
            items {
              externalId
              space
            }
          }
        }
      }
      historyInstance (first: 1000){
        items {
          externalId
          space
          friendlyName
          changedAt
          statusSubject {
            externalId
            space
            user {
              externalId
              space
              displayName
              firstName
              lastName
              email
            }
            userComplement {
              externalId
              space
            }
          }
          status {
            externalId
            space
            name
          }
        }
      }
      changeRequests(first: 1000, filter: $changeRequestFilter) {
        items{
          externalId
          space
          comments
          propertiesToChange
          changeRequestType{
            externalId
            space
          }
          approvalWorkflow{
            externalId
            space
            status{
              externalId
              space
              name
            }
            steps {
              items {
                externalId
                space
              }
            }
          }
          attatchments{
            externalId
            mimeType
            uploadedTime
            metadata
            source
            name
          }
        }
      }
      comments {
        items {
          externalId
          space
          isLegacy
          comment
          timestamp
          user{
            externalId
            space
            displayName
            firstName
            lastName
            email
					}
        }
      }
    }
  }
}
"""

GET_ACTION_BY_ID_FOR_EDITING_QUERY = """
query GetActions(
  $cursor: String,
  $pageSize: Int,
  $actionItemFilter: _ListActionFilter,
  $userSiteConfigurationsFilter: _ListUserSiteConfigurationFilter,
  $approvalWorkflowStepFilter: _ListApprovalWorkflowStepFilter,
  $userOnApprovalWorkflowStepFilter: _ListUserFilter,
  $sourceEventFilter: _ListSourceEventFilter,
  $sorting: _ActionSort!
) {
  listAction(
    after: $cursor
    first: $pageSize
    filter: $actionItemFilter
    sort: [$sorting]
  ) {
    items {
      externalId
      space
      title
      description
      currentStatus {
        externalId
        space
        name
      }
      dueDate
      displayDueDate
      approvalDate
      verificationDate
      conclusionDate
      assignmentDate
      evidenceRequired
      isPrivate
      voeActionItem
      attachments {
        externalId
        name
        mimeType
        source
        uploadedTime
        metadata
      }
      actionItemLink {
        items {
          externalId
          space
          description
          link
        }
      }
      category {
        externalId
        space
        name
        description
      }
      priority
      sourceInformation
      sourceId
      sourceType {
        externalId
        space
        name
        description
      }
      subCategory {
        externalId
        space
        name
        description
      }
      siteSpecificCategory {
        externalId
        space
        name
        description
      }
      actionItemKind {
        externalId
        space
        name
        description
      }
      reportingSite {
        externalId
        space
        siteCode
        name
        description
      }
      reportingUnit {
        externalId
        space
        name
        description
      }
      reportingLocation {
        externalId
        space
        name
        description
      }
      application {
        externalId
        space
        name
        alias
      }
      reportingLine {
        externalId
        space
        name
        description
      }
      assignedTo {
        externalId
        space
        user {
          externalId
          space
          displayName
          firstName
          lastName
          email
        }
        userComplement {
          externalId
          space
          userSiteConfigurations(first: 1, filter: $userSiteConfigurationsFilter) {
            items {
              externalId
              space
              onSiteManager {
                externalId
                space
                displayName
                firstName
                lastName
                email
              }
            }
          }
        }
      }
      owner {
        externalId
        space
        user {
          externalId
          space
          displayName
          firstName
          lastName
          email
        }
        userComplement {
          externalId
          space
        }
      }
      approvalWorkflow {
        externalId
        space
        status {
          externalId
          space
          name
        }
        steps(first: 1000, filter: $approvalWorkflowStepFilter) {
          items {
            externalId
            space
            status {
              externalId
              space
              description
            }
            description
            users(first: 1, filter: $userOnApprovalWorkflowStepFilter) {
              items {
                externalId
                space
                displayName
                firstName
                lastName
                email
              }
              edges {
                externalId
                space
              }
            }
          }
        }
      }
      sourceEvents(first: 1, filter: $sourceEventFilter) {
        items {
          externalId
          space
          title
          owner {
            externalId
            space
          }
          secondaryOwnerUsers {
            items {
              externalId
              space
            }
          }
          secondaryOwnerRoles {
            items {
              externalId
              space
            }
          }
          secondaryOwnerTeams {
            items {
              externalId
              space
            }
          }
        }
      }
      viewUsers {
        items {
          externalId
          space
          user {
            externalId
            space
            displayName
            lastName
            firstName
            email
          }
        }
      }
      viewRoles {
        items {
          externalId
          space
          name
        }
      }
      viewTeams {
        items {
          externalId
          space
          name
        }
      }
      recurrenceInstance {
        externalId
        space
        description
        recurrenceType {
          name
          externalId
          space
          description
        }
        weekDays
        months
        dayOfTheMonth
        quarters
        monthOfTheYear
        nextDates
        startDate
        endDate
      }
      isTemplate
      assignees {
        items {
          user {
            externalId
            space
            email
            displayName
            lastName
            firstName
          }
        }
        edges {
          externalId
          space
        }
      }
    }
  }
}
"""

GET_ACTION_ITEMS_FOR_UPDATE_COMPARISON_QUERY = """
query GetActionsForUpdateComparison(
  $filter: _ListActionFilter,
) {
  listAction(
    first: 1000
    filter: $filter
  ) {
    items {
      externalId
      space
      isPrivate
      attachments {
        externalId
        mimeType
        uploadedTime
        metadata
        source
        name
      }
      assignedTo {
        externalId
        space
      }
      owner {
        externalId
        space
      }
      assignees(first: 1000) {
        items {
          externalId
          space
        }
        edges {
          externalId
          space
        }
      }
      actionItemLink {
        items {
          externalId
          space
        }
      }
      approvalWorkflow {
        externalId
        space
        status {
          externalId
          space
          name
        }
        steps(first: 1000) {
          items {
            externalId
            space
            status {
              externalId
              space
              description
            }
            description
            users(first: 1) {
              items {
                externalId
                space
                displayName
                firstName
                lastName
                email
              }
              edges {
                externalId
                space
              }
            }
          }
        }
      }
      viewUsers(first: 1000) {
        items {
          externalId
          space
        }
        edges {
          externalId
          space
        }
      }
      viewRoles(first: 1000) {
        items {
          externalId
          space
        }
        edges {
          externalId
          space
        }
      }
      viewTeams(first: 1000) {
        items {
          externalId
          space
        }
        edges {
          externalId
          space
        }
      }
      sourceEvents(first: 1) {
        items {
          externalId
          space
          title
          owner {
            externalId
            space
          }
          secondaryOwnerUsers(first: 1000) {
            items {
              externalId
              space
            }
            edges {
              externalId
              space
            }
          }
          secondaryOwnerRoles(first: 1000) {
            items {
              externalId
              space
            }
            edges {
              externalId
              space
            }
          }
          secondaryOwnerTeams(first: 1000) {
            items {
              externalId
              space
            }
            edges {
              externalId
              space
            }
          }
          viewUsers(first: 1000) {
            items {
              externalId
              space
            }
            edges {
              externalId
              space
            }
          }
          viewRoles(first: 1000) {
            items {
              externalId
              space
            }
            edges {
              externalId
              space
            }
          }
          viewTeams(first: 1000) {
            items {
              externalId
              space
            }
            edges {
              externalId
              space
            }
          }
        }
      }
    }
  }
}
"""

GET_ACTION_ITEM_FOR_ASSIGNEE_REQUEST_PROCESS_QUERY = """
query GetActions(
  $actionItemFilter: _ListActionFilter,
  $changeRequestFilter: _ListActionItemChangeRequestFilter,
) {
  listAction(
    first: 1000
    filter: $actionItemFilter
  ) {
    items {
      externalId
      space
      dueDate
      isPrivate
      assignedTo {
        externalId
        space
        user {
          externalId
          space
          email
        }
      }
      owner {
        externalId
        space
        user {
          externalId
          space
          email
        }
      }
      siteSpecificCategory {
        externalId
        space
        name
        description
      }
      approvalWorkflow {
        externalId
        space
        steps(first: 1000) {
          items {
            externalId
            space
            description
            users(first: 1) {
              items {
                externalId
                space
                email
              }
            }
          }
        }
      }
      sourceEvents(first: 1) {
        items {
          externalId
          space
          views
        }
      }
      changeRequests(first: 1000, filter: $changeRequestFilter) {
        items {
          externalId
          space
          approvalWorkflow{
            externalId
            space
            status {
              externalId
              space
            }
            steps {
              items {
                externalId
                space
              }
            }
          }
        }
      }
      viewUsers {
        items {
          externalId
          space
          user {
            externalId
            space
          }
        }
      }
      viewRoles {
        items {
          externalId
          space
        }
      }
      viewTeams {
        items {
          externalId
          space
        }
      }
    }
  }
}
"""
