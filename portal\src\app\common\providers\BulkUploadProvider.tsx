import { ChangeEvent, useState, ReactNode } from 'react'
import { useSnackbar } from '@/app/common/contexts/SnackbarContext'

import { AzureFunctionClient } from '@/app/common/clients/azure-function-client'
import { UploadError, BulkUploadContext, State } from '../contexts/BulkUploadContext'
import { getBulkUploadColumns } from '@/app/common/utils/bulk-upload'
import { translate } from '../utils/generate-translate'

export function BulkUploadProvider({ children }: { children: ReactNode }) {
    const client = new AzureFunctionClient()
    const { showSnackbar, handleCloseAlert } = useSnackbar()

    const [state, setState] = useState<State>({
        modalType: undefined,
        successMessage: '',
        errorMessage: '',
        errorsCount: 0,
        errors: [],
        isLoading: false,
    })

    const [siteIdInMemory, setSiteIdInMemory] = useState<string>('')
    const [userIdInMemory, setUserIdInMemory] = useState<string>('')
    const [eventIdInMemory, setEventIdInMemory] = useState<string | undefined>()

    const validateFile = (file: File, siteId: string, userId?: string): boolean => {
        if (!file.name.endsWith('.xlsx')) {
            showSnackbar(translate('bulkUploadActionItems.uploadValidExcel'), 'error', 'excel-bulk-upload')
            return false
        }

        if (!siteId) {
            showSnackbar(translate('bulkUploadActionItems.noResportingSite'), 'error', 'site-bulk-upload')
            return false
        }

        if (!userId) {
            showSnackbar(translate('bulkUploadActionItems.noUser'), 'error', 'user-bulk-upload')
            return false
        }

        return true
    }

    const handleBulkUpload = async (
        e: ChangeEvent<HTMLInputElement>,
        siteId: string,
        userId?: string,
        eventId?: string,

        debouncedFetchActions?: () => void
    ) => {
        const selectedFile = e.target.files?.[0]
        if (!selectedFile || !validateFile(selectedFile, siteId, userId)) return

        setState((prev) => ({ ...prev, isLoading: true, modalType: 'loading' }))
        setSiteIdInMemory(siteId)
        setUserIdInMemory(userId ?? '')
        setEventIdInMemory(eventId)

        showSnackbar(translate('bulkUploadActionItems.loadingFiles'), 'info', 'bulk-upload', null)

        try {
            const columns = getBulkUploadColumns(siteId, eventId)
            const result = await client.bulkUploadActions(selectedFile, siteId, columns, userId!, eventId)

            setState({
                modalType: 'result',
                successMessage: `${result.successCount} ${translate('bulkUploadActionItems.outOf')} ${
                    result.totalRows
                } ${translate('bulkUploadActionItems.successfullyImported')}`,
                errorMessage: `${result.errorCount} ${translate('bulkUploadActionItems.outOf')} ${
                    result.totalRows
                } ${translate('bulkUploadActionItems.errorImported')}`,
                errorsCount: result.errorCount,
                errors: result.errors,
                isLoading: false,
            })

            if (result.successCount > 0 && debouncedFetchActions) {
                debouncedFetchActions()
            }

            handleCloseAlert()
        } catch (error) {
            setState((prev) => ({ ...prev, isLoading: false, modalType: undefined }))
            showSnackbar(translate('bulkUploadActionItems.errorSendingFile'), 'error', 'bulk-upload')
        }
    }

    const generateErrorReportCsv = () => {
        if (state.errors.length === 0) {
            showSnackbar(translate('bulkUploadActionItems.noErrorsToExport'), 'info', 'generate-csv-bulk-upload')
            return
        }

        const headers = Object.keys(state.errors[0])
        const csvRows = [
            headers.map((item) => translate(`bulkUploadActionItems.${item}`)).join(','),
            ...state.errors.map((row) =>
                headers
                    .map((header) => `"${translate(String(row[header as keyof UploadError]).replace(/"/g, '""'))}"`)
                    .join(',')
            ),
        ]

        const csvContent = '\ufeff' + csvRows.join('\n')
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
        const link = document.createElement('a')
        link.href = URL.createObjectURL(blob)
        link.download = 'upload-errors-report.csv'
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
    }

    return (
        <BulkUploadContext.Provider
            value={{
                state,
                siteId: siteIdInMemory,
                userId: userIdInMemory,
                eventId: eventIdInMemory,
                setState,
                handleBulkUpload,
                generateErrorReportCsv,
            }}
        >
            {children}
        </BulkUploadContext.Provider>
    )
}
