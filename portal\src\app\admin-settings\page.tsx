'use client'
import '../common/utils/polyfills'
import { Box } from '@mui/material'
import { getLocalUserSite, TranslationContext, TranslationContextState } from '@celanese/celanese-ui'
import { ClnPage, ClnPanel } from '@celanese/ui-lib'
import { useContext, useEffect, useMemo, useState } from 'react'
import { RecurringTab } from './Recurring/RecurringTab'
import { useActionItemCategories } from '../common/hooks/action-item-management/useActionItemCategories'
import { useReportingUnits } from '../common/hooks/asset-hierarchy/useReportingUnits'
import { useActionItemStatuses } from '../common/hooks/action-item-management/useActionItemStatuses'
import { useRecurrenceTypes } from '../common/hooks/action-item-management/useRecurrenceTypes'
import { CategoriesTab } from './Categories/CategoriesTab'
import { useActionItemSubCategories } from '../common/hooks/action-item-management/useActionItemSubCategories'
import { useSiteSpecificCategories } from '../common/hooks/action-item-management/useSiteSpecificCategories'
import AuthGuardWrapper from '../common/wrapper/AuthGuardWrapper'
import { SiteSpecificCategoryTab } from './SiteSpecificCategory/SiteSpecificCategoryTab'
import { ReferenceTab } from './Reference/ReferenceTab'
import { useActionItemReferences } from '../common/hooks/action-item-management/useActionItemReferences'
import { ActionItemTemplateTab } from './ActionItemTemplate/ActionItemTemplateTab'
import { useActionByTemplateUser } from '../common/hooks/action-item-management/useActionByTemplate'
import { useAuthGuard } from '../common/hooks/useAuthGuard'
import { APP_NAME } from '../common/utils/constants'
import { SITE_EXTERNAL_ID_REQUIRED_FIELD } from '../common/utils/validate-codes'
import { AimTabs } from '../components/Tabs'
import { useSnackbar } from '../common/contexts/SnackbarContext'
import { translate } from '../common/utils/generate-translate'
import PageHeader from '../components/PageHeader'

export default function AdminSettingsPage() {
    const { checkPermissionsFromComponents } = useAuthGuard()
    const { locale } = useContext<TranslationContextState>(TranslationContext)
    const { siteId } = getLocalUserSite() || {}

    const { showSnackbar } = useSnackbar()

    const { loading: loadingUnits, units } = useReportingUnits({ siteId: siteId! })
    const { loading: loadingStatus, allStatus } = useActionItemStatuses({})
    const { loading: loadingRecurrenceTypes, allRecurrenceType } = useRecurrenceTypes()
    const { loading: loadingCategories, categories } = useActionItemCategories()
    const { loading: loadingSubCategories, subCategories } = useActionItemSubCategories()
    const {
        loading: loadingSiteSpecificCategories,
        siteSpecificCategories,
        refetchSiteSpecificCategory,
    } = useSiteSpecificCategories({ siteIds: [siteId!], excludeDeleted: true })
    const { loading: loadingReferences, references, refetchReferences } = useActionItemReferences()
    const { loading: loadingOwners, owners } = useActionByTemplateUser()

    const handleAlert = (message: string, error: boolean = false) => {
        showSnackbar(message, error ? 'error' : 'success', 'admin-page')
    }

    //Tabs
    const [currentTab, setCurrentTab] = useState(0)
    const tabs = useMemo(() => {
        const baseTabs = [
            {
                label: translate('adminSettings.tabs.recurring'),
                content: (
                    <RecurringTab
                        loading={loadingCategories && loadingUnits && loadingStatus && loadingRecurrenceTypes}
                        categories={categories}
                        units={units}
                        status={allStatus}
                        recurrenceTypes={allRecurrenceType}
                        handleAlert={handleAlert}
                    />
                ),
                auth: 'RecurringTab',
            },
            {
                label: translate('adminSettings.tabs.template'),
                content: (
                    <ActionItemTemplateTab
                        loading={loadingCategories && loadingUnits && loadingOwners}
                        categories={categories}
                        units={units}
                        owner={owners}
                        handleAlert={handleAlert}
                    />
                ),
                auth: 'ActionItemTemplateTab',
            },
            {
                label: translate('adminSettings.tabs.categories'),
                content: (
                    <CategoriesTab
                        loading={loadingCategories && loadingSubCategories && loadingSiteSpecificCategories}
                        categories={categories}
                        subCategories={subCategories}
                        siteSpecificCategories={siteSpecificCategories}
                    />
                ),
                auth: 'CategoriesTab',
            },
            {
                label: translate('adminSettings.tabs.subcategory2'),
                content: (
                    <SiteSpecificCategoryTab
                        loading={loadingSiteSpecificCategories}
                        siteSpecificCategories={siteSpecificCategories}
                        refetchSiteSpecificCategory={refetchSiteSpecificCategory}
                    />
                ),
                auth: 'SpecificCategoryTab',
            },
        ]

        if (siteId?.includes(SITE_EXTERNAL_ID_REQUIRED_FIELD)) {
            baseTabs.push({
                label: translate('adminSettings.tabs.reference'),
                content: (
                    <ReferenceTab
                        loading={loadingReferences}
                        references={references}
                        refetchReference={refetchReferences}
                    />
                ),
                auth: 'ReferenceTab',
            })
        }

        return baseTabs
    }, [
        locale,
        categories,
        units,
        allStatus,
        allRecurrenceType,
        owners,
        subCategories,
        siteSpecificCategories,
        refetchSiteSpecificCategory,
        references,
        refetchReferences,
    ])

    const authorizedTabs = tabs.filter((item) => checkPermissionsFromComponents(item.auth).isAuthorized)

    useEffect(() => {
        document.title = translate('pages.adminSettings.title') + ' ' + '|' + ' ' + APP_NAME
    }, [locale])

    return (
        <AuthGuardWrapper componentName={AdminSettingsPage.name}>
            <ClnPage>
                <Box sx={{ display: 'flex', flexDirection: 'column' }}>
                    <PageHeader title={translate('adminSettings.title')} />
                    <ClnPanel
                        id="admin-panel"
                        sx={{
                            padding: '1.5rem',
                            display: 'flex',
                            flexDirection: 'column',
                        }}
                    >
                        <AimTabs
                            value={currentTab}
                            tabs={authorizedTabs}
                            onChange={(_e, value) => setCurrentTab(value)}
                        />
                    </ClnPanel>
                </Box>
            </ClnPage>
        </AuthGuardWrapper>
    )
}
