import {
    DataGridPro,
    GridColDef,
    GridColumnMenu,
    GridColumnMenuProps,
    GridColumnVisibilityModel,
    GridFeatureMode,
    GridProSlotsComponent,
    GridRowHeightParams,
    GridRowHeightReturnValue,
    GridRowSelectionModel,
    GridRowsProp,
    GridSortModel,
    GridToolbarContainer,
} from '@mui/x-data-grid-pro'
import { Box, IconButton, Typography } from '@mui/material'
import { useEffect, useMemo, useState } from 'react'
import { translate } from '@/app/common/utils/generate-translate'
import { ActionIcon, ClnButtonProps } from '@celanese/ui-lib'
import { TablePagination } from './TablePagination'
import { TableOptionsBar } from './TableOptionsBar'
import * as S from './styles'

type PaginatedTableProps = {
    id?: string
    isLoading?: boolean
    isNoDataFilter?: boolean
    rows?: GridRowsProp
    initialColumnDefs?: GridColDef[]
    actions?: ActionIcon[]
    rowsPerPageOptions?: number[]
    currentPage?: number
    setCurrentPage?: (value: number) => void
    totalPages?: number
    rowsPerPage?: number
    setRowsPerPage?: (value: number) => void
    customButtons?: ClnButtonProps[]
    initialSearchValue?: string
    initialChipValue?: string
    showSearchInput?: boolean
    showOptionsBar?: boolean
    showCustomFilter?: boolean
    showInMemoryFilterButton?: boolean
    showColumnSelector?: boolean
    isMemorySearch?: boolean
    onSearchSubmit?: (search: string) => void
    paginationMode?: GridFeatureMode
    searchHelpMessage?: string
    searchInfoMessage?: string
    infinitePagination?: boolean
    removeOptionsColumnsManager?: boolean
    maxHeight?: number
    initialStateColumnVisibilityModel?: GridColumnVisibilityModel
    customPopoverContent?: JSX.Element
    sortModel?: GridSortModel
    activeFiltersCount?: number
    defaultMessageNoData?: string
    setSortModel?: (sortModel: GridSortModel) => void
    getEstimatedRowHeight?: (params: GridRowHeightParams) => number | null
    getRowHeight?: (params: GridRowHeightParams) => GridRowHeightReturnValue
    checkboxSelection?: boolean
    rowSelectionModel?: GridRowSelectionModel
    setRowSelectionModel?: (value: GridRowSelectionModel) => void
    disableSearch?: boolean
}

export function DataGridTable({
    id = 'data-grid-table-default',
    isLoading,
    isNoDataFilter = true,
    rows,
    initialColumnDefs,
    actions,
    rowsPerPageOptions = [],
    currentPage = 1,
    setCurrentPage,
    totalPages = 0,
    rowsPerPage,
    setRowsPerPage,
    customButtons,
    initialSearchValue,
    initialChipValue = '',
    showOptionsBar = true,
    showSearchInput = true,
    showCustomFilter = true,
    showColumnSelector = true,
    showInMemoryFilterButton,
    isMemorySearch,
    onSearchSubmit,
    paginationMode = 'client',
    searchHelpMessage,
    searchInfoMessage,
    infinitePagination = false,
    removeOptionsColumnsManager,
    maxHeight,
    initialStateColumnVisibilityModel,
    customPopoverContent,
    sortModel,
    activeFiltersCount,
    defaultMessageNoData = translate('common.noResultsForFilter'),
    setSortModel,
    getEstimatedRowHeight,
    getRowHeight,
    checkboxSelection,
    rowSelectionModel,
    setRowSelectionModel,
    disableSearch,
}: PaginatedTableProps) {
    const [columns, setColumns] = useState<GridColDef[]>(initialColumnDefs ?? [])
    const [activeSearchChip, setActiveSearchChip] = useState<string>(initialChipValue)
    const [columnVisibilityModel, setColumnVisibilityModel] = useState<GridColumnVisibilityModel | undefined>()

    useEffect(() => {
        setActiveSearchChip(initialChipValue)
    }, [initialChipValue])

    useEffect(() => {
        const savedOrder = getSavedColumnOrder()

        if (initialColumnDefs?.length) {
            savedOrder.length
                ? setColumns(sortColumnsByOrder(savedOrder, initialColumnDefs))
                : setColumns(initialColumnDefs)
        }
    }, [initialColumnDefs])

    const getSavedColumnOrder = () => {
        const saved = localStorage.getItem(`columnOrderIds-${id}`)
        return saved ? JSON.parse(saved) : []
    }

    const sortColumnsByOrder = (savedOrder: string[], allColumns: GridColDef[]) => {
        return allColumns?.toSorted((a, b) => {
            const indexA = savedOrder.indexOf(a.field)
            const indexB = savedOrder.indexOf(b.field)
            return indexA - indexB
        })
    }

    const columnsWithActions = useMemo(() => {
        if (!actions?.length) return columns
        const actionColumn = {
            field: 'actions',
            headerName: translate('table.headers.actions'),
            renderCell: (params: any) => (
                <>
                    {actions.map((action, index) => (
                        <IconButton key={index} onClick={() => action.onClick(params.row.id)} color="primary">
                            {action.icon}
                        </IconButton>
                    ))}
                </>
            ),
            renderHeader: () => (
                <span data-test="data-grid-pro-actions" data-origin="aim">
                    {translate('table.headers.actions')}
                </span>
            ),
            flex: 1,
        }

        const savedOrder = getSavedColumnOrder()
        const allColumns = [...columns, actionColumn]
        return savedOrder.length ? sortColumnsByOrder(savedOrder, allColumns) : allColumns
    }, [columns, actions])

    const saveColumnOrder = (order: string[]) => {
        localStorage.setItem(`columnOrderIds-${id}`, JSON.stringify(order))
    }

    const handleColumnOrderChange = ({ oldIndex, targetIndex }: any) => {
        const savedOrder = getSavedColumnOrder()
        const baseColumns = savedOrder?.length ? sortColumnsByOrder(savedOrder, columnsWithActions) : columnsWithActions

        const updated = [...baseColumns]
        const moved = updated.splice(oldIndex, 1)[0]
        updated.splice(targetIndex, 0, moved)

        setColumns(updated.filter((col) => col.field !== 'actions'))
        saveColumnOrder(updated.map((col) => col.field))
    }

    const filterRows = (rows?: GridRowsProp, searchTerm?: string) => {
        if (!searchTerm) return rows ?? []
        return (
            rows?.filter((row) =>
                Object.values(row).some((value) => value?.toString().toLowerCase().includes(searchTerm.toLowerCase()))
            ) ?? []
        )
    }

    const getSlots = (): Partial<GridProSlotsComponent> | undefined => {
        if (!showOptionsBar && !removeOptionsColumnsManager) return undefined

        const slots: Partial<GridProSlotsComponent> = {}

        if (isNoDataFilter) {
            slots.noRowsOverlay = CustomNoResultsOverlay
        }

        if (removeOptionsColumnsManager) {
            slots.columnMenu = CustomColumnMenu
        } else {
            slots.toolbar = CustomToolbar
        }

        return slots
    }

    function CustomToolbar() {
        return (
            <GridToolbarContainer sx={{ p: 0 }}>
                <TableOptionsBar
                    activeFiltersCount={activeFiltersCount}
                    customButtons={customButtons}
                    showSearchInput={showSearchInput}
                    showCustomFilter={showCustomFilter}
                    showColumnSelector={showColumnSelector}
                    onSearchSubmit={onSearchSubmit}
                    initialSearchValue={initialSearchValue}
                    activeSearchChip={activeSearchChip}
                    setActiveSearchChip={setActiveSearchChip}
                    searchHelpMessage={searchHelpMessage}
                    searchInfoMessage={searchInfoMessage}
                    customPopoverContent={customPopoverContent}
                    showInMemoryFilterButton={showInMemoryFilterButton}
                    disableSearchInput={disableSearch}
                />
            </GridToolbarContainer>
        )
    }

    function CustomNoResultsOverlay() {
        return (
            <Box sx={{ display: 'flex', flexDirection: 'row', padding: '0 0 0 1' }}>
                <Box sx={{ width: '100%', display: 'flex', alignItems: 'center' }}>
                    <Typography sx={{ width: '100%', fontSize: '18px', textAlign: 'center', color: 'primary.main' }}>
                        {defaultMessageNoData}
                    </Typography>
                </Box>
            </Box>
        )
    }

    function CustomColumnMenu(props: GridColumnMenuProps) {
        return <GridColumnMenu {...props} slots={{ columnMenuColumnsItem: null }} />
    }

    const localeText = {
        columnsPanelHideAllButton: translate('adminSettings.table.filter.hideAll'),
        columnsPanelShowAllButton: translate('adminSettings.table.filter.showAll'),
        columnsPanelTextFieldLabel: translate('adminSettings.table.filter.findColumn'),
        columnsPanelTextFieldPlaceholder: translate('adminSettings.table.filter.columnTitle'),
        toolbarColumns: translate('dataGridPro.columns'),
        columnMenuShowColumns: translate('dataGridPro.columns'),
        columnMenuManageColumns: translate('dataGridPro.manageColumn'),
        columnMenuHideColumn: translate('dataGridPro.hideColumns'),
        columnsManagementShowHideAllText: `${translate('dataGridPro.show')} / ${translate('dataGridPro.hideAll')}`,
        columnsManagementReset: translate('dataGridPro.reset'),
        pinToLeft: translate('dataGridPro.pinToLeft'),
        pinToRight: translate('dataGridPro.pinToRight'),
        unpin: translate('dataGridPro.unpin'),
        columnsManagementSearchTitle: translate('common.search'),
        columnMenuSortAsc: translate('dataGridPro.sortAsc'),
        columnMenuSortDesc: translate('dataGridPro.sortDesc'),
    }

    useEffect(() => {
        const stored = localStorage.getItem(`visibilityModelChange-${id}`)
        if (stored) {
            setColumnVisibilityModel(JSON.parse(stored))
        } else {
            setColumnVisibilityModel(initialStateColumnVisibilityModel)
        }
    }, [id])

    useEffect(() => {
        if (columnVisibilityModel) {
            localStorage.setItem(`visibilityModelChange-${id}`, JSON.stringify(columnVisibilityModel))
        }
    }, [columnVisibilityModel])

    return (
        <S.Container>
            <Box sx={maxHeight ? { height: maxHeight } : {}} id={id}>
                <DataGridPro
                    autoHeight={!maxHeight}
                    loading={isLoading}
                    rows={isMemorySearch ? filterRows(rows, activeSearchChip) : rows ?? []}
                    columns={columnsWithActions}
                    localeText={localeText}
                    hideFooter
                    paginationMode={paginationMode}
                    {...(setSortModel &&
                        sortModel && {
                            sortModel,
                            onSortModelChange: (model) => setSortModel(model),
                        })}
                    slots={getSlots()}
                    initialState={{
                        columns: {
                            columnVisibilityModel: initialStateColumnVisibilityModel ?? {},
                        },
                    }}
                    columnVisibilityModel={columnVisibilityModel}
                    onColumnVisibilityModelChange={setColumnVisibilityModel}
                    getEstimatedRowHeight={getEstimatedRowHeight}
                    getRowHeight={getRowHeight}
                    onColumnOrderChange={handleColumnOrderChange}
                    checkboxSelection={checkboxSelection}
                    rowSelectionModel={rowSelectionModel}
                    onRowSelectionModelChange={(model) => setRowSelectionModel?.(model)}
                    disableRowSelectionOnClick
                    keepNonExistentRowsSelected
                    sx={{
                        boxShadow: 0,
                        border: 0,
                        borderColor: 'none',
                        '--DataGrid-overlayHeight': '300px',
                    }}
                />
            </Box>

            {!infinitePagination && (
                <TablePagination
                    rowsLen={rows?.length ?? 0}
                    rowsPerPageOptions={rowsPerPageOptions}
                    currentPage={currentPage}
                    setCurrentPage={setCurrentPage}
                    totalPages={totalPages}
                    initialRowsPerPage={rowsPerPage}
                    setInitialRowsPerPage={setRowsPerPage}
                />
            )}
        </S.Container>
    )
}
