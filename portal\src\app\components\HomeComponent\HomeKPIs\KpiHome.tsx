import { ActionItemStateStatus, ActionItemStateStatusEnum } from '@/app/common/enums/KpiStatusEnum'
import { ClnCard } from '@celanese/ui-lib'
import { Box } from '@mui/material'

interface ButtonProps {
    id?: string
    state: ActionItemStateStatus
    label: string
    onClick: () => void
    count: string | number
    currentState: ActionItemStateStatus | undefined
    statusName: string
    disable?: boolean
    dataTest?: string
}

const getStatusColor = (status: string) => {
    switch (status) {
        case ActionItemStateStatusEnum.PendingApprovals:
            return 'warning'
        case ActionItemStateStatusEnum.PendingVerifications:
            return 'warning'
        case ActionItemStateStatusEnum.Overdue:
            return 'error'
        default:
            return 'info'
    }
}

export function KpiHome({
    id,
    state,
    label,
    onClick,
    count,
    currentState,
    statusName,
    disable,
    dataTest,
}: ButtonProps) {
    function getCardOpacity() {
        if (currentState == null) {
            return '100%'
        }
        return currentState === state ? '100%' : '50%'
    }

    return (
        <Box
            id={id ?? 'kpi-default'}
            onClick={() => {
                if (!disable) onClick()
            }}
            data-test={dataTest || ''}
            data-origin="aim"
            sx={{
                display: 'flex',
                flexDirection: 'column',
                height: '100%',
                '.MuiCard-root': {
                    height: '100% !important',
                },
            }}
        >
            <ClnCard
                cardHeader={count.toString()}
                subheader={label}
                type="performance"
                typeVariant={getStatusColor(statusName)}
                sxProps={{
                    flex: 1,
                    height: '100% !important',
                    opacity: getCardOpacity(),
                    cursor: disable ? '' : 'pointer',
                    width: '100%',
                    maxHeight: '6.0em',
                    minHeight: '1.5em',
                    minWidth: '1.9em',
                    ...(getStatusColor(statusName) === 'info' && {
                        borderColor: 'primary.main',
                        '.indicatorNum': {
                            color: 'primary.main',
                        },
                    }),
                    '@media (max-width: 1280px)': {
                        width: '100%',
                        minWidth: '150px',
                        height: '72px',
                    },
                    margin: '0px !important',
                }}
                sxHeader={{
                    '.indicatorNum': {
                        fontSize: '1.7rem !important',
                        paddingBottom: '0.5rem',
                        '@media (max-width: 1280px)': {
                            fontSize: '24px !important',
                        },
                    },
                }}
            />
        </Box>
    )
}
