import asyncio
from collections.abc import Iterator
from enum import St<PERSON><PERSON>num
from typing import List, TypeVar

from cognite.client import Cognite<PERSON>lient
from cognite.client.data_classes.files import FileMetadataList
from cognite.client.data_classes.raw import Row, RowWrite
from pydantic import BaseModel, Field

from ..constants import PROT_SECURITY_CATEGORY_NAME, RAW_DB


class RawTable(StrEnum):
    ICAP_STG_FILE = "ICAP-STG-File"
    ICAP_STG_FILE_DLQ = "ICAP-STG-File-DLQ"


class FileToMigrate(BaseModel):
    key: str = Field(exclude=True)
    source_external_id: str
    target_external_id: str
    target_data_set_id: int
    processed_count: int
    is_private: bool


T = TypeVar("T")


class FileMigrationService:
    BATCH_SIZE_DEFAULT = 10

    def __init__(self, client: CogniteClient):
        self._client = client

        self._unprocessed_files_to_retry: list[RowWrite] = []
        self._unprocessed_files_to_dlq: list[RowWrite] = []

        self._prot_security_category = self._get_security_category(
            PROT_SECURITY_CATEGORY_NAME
        )

    async def migrate(self, batch_size: int | None):
        batch_size = batch_size or self.BATCH_SIZE_DEFAULT
        files_to_process = self._get_rows(RawTable.ICAP_STG_FILE.value)
        source_files_metadata = self._get_files_metadata(
            [file.key for file in files_to_process]
        )

        print(f"{len(files_to_process)} files to process")

        count = 0
        for chunk in self._divide_in_chunks(files_to_process, batch_size):
            if count % batch_size == 0:
                print(
                    f"Progress: {count}/{len(files_to_process)} - {(100*count/len(files_to_process)):.2f}%"
                )

            process_file_tasks = []
            for file_to_migrate_data in chunk:
                process_file_tasks.append(
                    asyncio.to_thread(
                        self._process_file, file_to_migrate_data, source_files_metadata
                    )
                )

            await asyncio.gather(*process_file_tasks)
            count += len(chunk)

        self._save_results()

    def _process_file(
        self,
        file_to_migrate_data: Row,
        source_files_metadata: FileMetadataList,
    ):
        file_to_migrate = None
        try:
            file_to_migrate = FileToMigrate(
                **{"key": file_to_migrate_data.key, **file_to_migrate_data.columns}
            )

            source_file_metadata = source_files_metadata.get(
                external_id=file_to_migrate.source_external_id
            )
            if source_file_metadata is None:
                self._add_to_unprocessed_list(
                    file_to_migrate_data, "Source file was not found in Cognite."
                )
                return

            if file_to_migrate.is_private and self._prot_security_category is None:
                self._add_to_unprocessed_list(
                    file_to_migrate_data, "Security category was not found."
                )
                return

            self._client.files.upload_bytes(
                content=self._client.files.download_bytes(
                    external_id=file_to_migrate.source_external_id
                ),
                name=source_file_metadata.name,
                external_id=file_to_migrate.target_external_id,
                source=source_file_metadata.source,
                mime_type=source_file_metadata.mime_type,
                metadata=source_file_metadata.metadata,
                data_set_id=file_to_migrate.target_data_set_id,
                overwrite=True,
                security_categories=(
                    [self._prot_security_category]
                    if file_to_migrate.is_private
                    else None
                ),
            )

        except Exception as e:
            print(f"Error: {e}")
            self._add_to_unprocessed_list(
                file_to_migrate_data,
                f"Error while migrating file to AIM dataset: {e}",
            )

    def _get_rows(self, table: str, database: str = RAW_DB):
        return [
            *self._client.raw.rows.list(db_name=database, table_name=table, limit=-1)
        ]

    def _get_security_category(self, name: str) -> int | None:
        return next(
            (
                sc.id
                for sc in self._client.iam.security_categories.list(-1)
                if sc.name == name
            ),
            None,
        )

    def _erase_table(self, table: str, database: str = RAW_DB) -> None:
        self._client.raw.tables.delete(db_name=database, name=table)
        self._client.raw.tables.create(db_name=database, name=table)

    def _insert_rows(
        self, table: str, rows: list[RowWrite], database: str = RAW_DB
    ) -> None:
        self._client.raw.rows.insert(db_name=database, table_name=table, row=rows)

    def _get_files_metadata(self, external_ids: list[str]):
        return self._client.files.retrieve_multiple(external_ids=external_ids)

    def _add_to_unprocessed_list(self, file_to_migrate_data: Row, reason: str):
        file_to_migrate_data.columns["reason_to_fail"] = reason
        file_to_migrate_data.columns["processed_count"] += 1
        target_list = (
            self._unprocessed_files_to_dlq
            if file_to_migrate_data.columns["processed_count"] >= 3
            else self._unprocessed_files_to_retry
        )
        target_list.append(file_to_migrate_data.as_write())

    def _save_results(self):
        self._erase_table(RawTable.ICAP_STG_FILE.value)
        self._insert_rows(
            RawTable.ICAP_STG_FILE.value, self._unprocessed_files_to_retry
        )

        files_in_dlq = self._get_rows(RawTable.ICAP_STG_FILE_DLQ.value)
        files_in_dlq = [
            file.as_write() for file in files_in_dlq
        ] + self._unprocessed_files_to_dlq

        self._erase_table(RawTable.ICAP_STG_FILE_DLQ.value)
        self._insert_rows(RawTable.ICAP_STG_FILE_DLQ.value, files_in_dlq)

    @staticmethod
    def _divide_in_chunks(items: List[T], chunk_size: int) -> Iterator[List[T]]:
        for i in range(0, len(items), chunk_size):
            yield items[i : i + chunk_size]
