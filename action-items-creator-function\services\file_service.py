from typing import List

from clients.action_item_client import ActionItemClient
from clients.files.requests import GetFilesDownloadUrlsRequest, UploadFileRequest


class FileService:
    def __init__(
        self,
        action_item_client: ActionItemClient,
    ):
        self._action_item_client = action_item_client

    def upload_files(self, request: List[UploadFileRequest]) -> List[str]:
        """
        Uploads multiple files to the Cognite platform.

        Parameters:
        - request (List[UploadFileRequest]): A list of file objects to upload.

        Returns:
        - List[str]: A list of external IDs for the uploaded files.
        """
        return [self._action_item_client.files.upload_bytes(file) for file in request]

    def get_files_download_urls(
        self, request: GetFilesDownloadUrlsRequest
    ) -> dict[str | int, str]:
        """
        Retrieves download URLs for specified files from the Cognite platform.

        Parameters:
        - request (GetFilesDownloadUrlsRequest): Object containing a list of external IDs for the files to retrieve URLs for.

        Returns:
        - dict[str | int, str]: A dictionary mapping file identifiers to their respective download URLs.
        """
        return self._action_item_client.files.get_files_download_urls(request)
