import { ActionItemStateStatus } from '../enums/KpiStatusEnum'

export const actionStateTranslationMap: Partial<Record<ActionItemStateStatus, string>> = {
    AssignedToMe: 'kpis.assignedToMe',
    RelatedToMe: 'kpis.assigned',
    PendingApprovals: 'kpis.pendingApprovals',
    PendingVerifications: 'kpis.pendingVerifications',
    Overdue: 'kpis.overdue',
    MyApprovals: 'kpis.myApprovals',
    MyVerifications: 'kpis.myVerifications',
    MyChallenges: 'kpis.myChallenges',
    MyExtends: 'kpis.myExtends',
    MyReassignment: 'kpis.myReassignment',
    TotalActions: 'kpis.allActionItems',
}
