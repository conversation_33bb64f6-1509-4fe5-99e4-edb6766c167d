# Manifest file downloaded from fusion
externalId: tr-AIM-COR-ALL-RAW-ICAP-VAL-MISSING-ASSET
name: AIM-COR-ALL-RAW-ICAP-VAL-MISSING-ASSET
query: |-
  select
      concat("UNT-", key) as key,
      key as id,
      UnitName as name,
      "Unit is not mapped" as error_message
  from
      `ICAP-COR`.`UNT-tblUnit`
  where
      key not in (
          SELECT
              key
          from
              `AIM-COR`.`ICAP-MAP-ReportingUnit&Location`
      )
  UNION
  select
      concat("STS-", key),
      key as id,
      SiteName as name,
      "Site is not mapped" as error_message
  from
      `ICAP-COR`.`STS-tblSite`
  where
      key not in (
          SELECT
              key
          from
              `AIM-COR`.`ICAP-MAP-ReportingSite`
      )
destination:
  database: AIM-COR
  table: ICAP-VAL-MissingAsset
  type: raw
ignoreNullFields: true
shared: true
action: upsert
dataSetExternalId: AIM-COR-ALL-DAT
# Specify credentials separately like this:
# authentication:
#   read:
#     clientId: ${READ_CLIENT_ID}
#     clientSecret: ${READ_CLIENT_SECRET}
#     tokenUrl: ${READ_TOKEN_URL}
#     cdfProjectName: ${READ_CDF_PROJECT_NAME}
#     # Optional: If idP requires providing the scopes
#     scopes:
#       - ${READ_SCOPES}
#     # Optional: If idP requires providing the audience
#     audience: ${READ_CDF_AUDIENCE}
#   write:
#     clientId: ${WRITE_CLIENT_ID}
#     clientSecret: ${WRITE_CLIENT_SECRET}
#     tokenUrl: ${WRITE_TOKEN_URL}
#     cdfProjectName: ${WRITE_CDF_PROJECT_NAME}
#     # Optional: If idP requires providing the scopes
#     scopes:
#       - ${WRITE_SCOPES}
#     # Optional: If idP requires providing the audience
#     audience: ${WRITE_CDF_AUDIENCE}
# Or together like this:
# authentication:
#   clientId: ${CLIENT_ID}
#   clientSecret: ${CLIENT_SECRET}
#   tokenUrl: ${TOKEN_URL}
#   # Optional: If idP requires providing the scopes
#   cdfProjectName: ${CDF_PROJECT_NAME}
#   scopes:
#    - ${SCOPES}
#   # Optional: If idP requires providing the audience
#   audience: ${AUDIENCE}