import { Cln<PERSON>utton, SelectItem } from '@celanese/ui-lib'
import { Box } from '@mui/material'
import { z } from 'zod'
import { SubmitHandler, useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { useRoles } from '../../common/hooks/user-management/useRoles'
import { useEffect, useMemo } from 'react'
import { CategoryConfiguration } from '../../common/models/category-configuration'
import { ActionItemCategory } from '../../common/models/category'
import { ActionItemSubCategory } from '../../common/models/sub-category'
import { SiteSpecificCategory } from '../../common/models/site-specific-category'
import { SITE_EXTERNAL_ID_REQUIRED_FIELD, UNIT_MANAGER_ROLE_WAS } from '@/app/common/utils/validate-codes'
import { transformOptions } from '@/app/common/utils/transform-options-for-filter'
import GenericAutocomplete from '@/app/components/FieldsComponent/GenericAutocomplete'
import GenericTextField from '@/app/components/FieldsComponent/GenericTextField'
import { UserRoleSiteRequest, useUserRoleSites } from '@/app/common/hooks/user-management/useUserRoleSites2'
import { UserRoleSite } from '@/app/common/models/user-role-site'
import { translate } from '@/app/common/utils/generate-translate'
import { getLocalUserSite } from '@celanese/celanese-ui'
import { CustomDrawer } from '@/app/components/ModalComponent/Drawer/Drawer'
import { drawerStyles } from '@/app/components/ModalComponent/Drawer/styles'

interface CategoriesFormProps {
    onClose: () => void
    openDrawer: boolean
    onSubmitCallback: (data: any, onSuccess?: () => void) => void
    onCloseCallback: () => void
    categoriesConfigurationToEdit?: CategoryConfiguration
    categories: ActionItemCategory[]
    subCategories: ActionItemSubCategory[]
    siteSpecificCategories: SiteSpecificCategory[]
    isEdit: boolean
}

export type CategoriesConfigurationFormSchema = z.infer<ReturnType<typeof createFormSchema>>

const createFormSchema = (siteId: string) =>
    z
        .object({
            category: z.string().min(1),
            subCategory1: z.string().min(1),
            subCategory2: z.string().optional(),
            daysFromAssignedDate: z.string().optional(),
            hasEmailNotification: z.string().min(1),
            evidenceRequired: z.string().min(1),
            isApprovalRequired: z.string().min(1),
            defaultApprovalRole: z.string().optional(),
            defaultApprovalUser: z.string().optional(),
            daysToApproval: z.string().optional(),
            isVerificationRequired: z.string().min(1),
            defaultVerificationRole: z.string().optional(),
            defaultVerificationUser: z.string().optional(),
            daysToVerification: z.string().optional(),
            isExtensionsAllowed: z.string().min(1),
            defaultExtensionApproverRole: z.string().optional(),
            isExtensionAttachmentRequired: z.string().optional(),
        })
        .superRefine((v, ctx) => {
            if (v.isApprovalRequired === 'true') {
                if (!v.defaultApprovalRole) {
                    ctx.addIssue({
                        path: ['defaultApprovalRole'],
                        code: z.ZodIssueCode.custom,
                        message: translate('adminSettings.categories.form.errors.approverRoleRequired'),
                    })
                }
                if (siteId !== SITE_EXTERNAL_ID_REQUIRED_FIELD && v.defaultApprovalRole && !v.defaultApprovalUser) {
                    ctx.addIssue({
                        path: ['defaultApprovalUser'],
                        code: z.ZodIssueCode.custom,
                        message: translate('adminSettings.categories.form.errors.approverUserRequired'),
                    })
                }
            }

            if (v.isVerificationRequired === 'true') {
                if (!v.defaultVerificationRole) {
                    ctx.addIssue({
                        path: ['defaultVerificationRole'],
                        code: z.ZodIssueCode.custom,
                        message: translate('adminSettings.categories.form.errors.verifierRoleRequired'),
                    })
                }
                if (
                    siteId !== SITE_EXTERNAL_ID_REQUIRED_FIELD &&
                    v.defaultVerificationRole &&
                    !v.defaultVerificationUser
                ) {
                    ctx.addIssue({
                        path: ['defaultVerificationUser'],
                        code: z.ZodIssueCode.custom,
                        message: translate('adminSettings.categories.form.errors.verifierUserRequired'),
                    })
                }
            }

            if (siteId === SITE_EXTERNAL_ID_REQUIRED_FIELD && !v.subCategory2) {
                ctx.addIssue({
                    path: ['subCategory2'],
                    code: z.ZodIssueCode.custom,
                })
            }

            if (v.isExtensionsAllowed === 'true' && !v.isExtensionAttachmentRequired) {
                ctx.addIssue({
                    path: ['isExtensionAttachmentRequired'],
                    code: z.ZodIssueCode.custom,
                })
            }
        })

export const CategoriesForm: React.FC<CategoriesFormProps> = ({
    openDrawer,
    onSubmitCallback,
    onCloseCallback,
    categoriesConfigurationToEdit,
    categories,
    subCategories,
    siteSpecificCategories,
    isEdit,
}) => {
    const { siteId } = getLocalUserSite() || {}
    const formSchema = createFormSchema(siteId!)

    const { roles: rolesData } = useRoles({})

    const {
        control,
        handleSubmit,
        reset,
        setValue,
        watch,
        formState: { errors },
        getValues,
    } = useForm<CategoriesConfigurationFormSchema>({
        resolver: zodResolver(formSchema),
    })

    const watchIsApprovalRequired = watch('isApprovalRequired')
    const watchIsVerificationRequired = watch('isVerificationRequired')
    const watchIsExtensionsAllowed = watch('isExtensionsAllowed')

    const watchDefaultApprovalRole = watch('defaultApprovalRole')
    const watchDefaultVerificationRole = watch('defaultVerificationRole')

    const categoriesOptions: SelectItem[] = useMemo(() => {
        return transformOptions(categories, 'name')
    }, [categories])

    const subCategoriesOptions: SelectItem[] = useMemo(() => {
        return transformOptions(subCategories, 'name')
    }, [subCategories])

    const siteSpecificCategoriesOptions: SelectItem[] = useMemo(() => {
        return transformOptions(siteSpecificCategories, 'name')
    }, [siteSpecificCategories])

    const booleanOptions: SelectItem[] = useMemo(() => {
        return [
            { label: translate('adminSettings.common.yes'), value: 'true' },
            { label: translate('adminSettings.common.no'), value: 'false' },
        ]
    }, [translate])

    const rolesOptions: SelectItem[] = useMemo(() => {
        return transformOptions(rolesData, 'name')
    }, [rolesData])

    const { userRoleSite: approvalRole } = useUserRoleSites(
        useMemo<UserRoleSiteRequest>(
            () => ({
                roleId: watchDefaultApprovalRole ?? '-',
            }),
            [watchDefaultApprovalRole]
        )
    )

    const { userRoleSite: verificationRole } = useUserRoleSites(
        useMemo<UserRoleSiteRequest>(
            () => ({
                roleId: watchDefaultVerificationRole ?? '-',
            }),
            [watchDefaultVerificationRole]
        )
    )

    const extractUsers = (role: UserRoleSite | undefined) =>
        role?.usersComplements.items.flatMap((complement) => complement.userAzureAttribute.user) || []

    const approvalUsersOptions: SelectItem[] = useMemo(() => {
        return transformOptions(extractUsers(approvalRole[0]), 'email')
    }, [approvalRole])

    const verificationUsersOptions: SelectItem[] = useMemo(() => {
        return transformOptions(extractUsers(verificationRole[0]), 'email')
    }, [verificationRole])

    useEffect(() => {
        const {
            category,
            actionItemSubCategory,
            siteSpecificCategory,
            daysFromAssignedDate,
            hasEmailNotification,
            attachmentRequired,
            isApprovalRequired,
            defaultApprovalRole,
            defaultApprovalUser,
            daysToApproval,
            isVerificationRequired,
            defaultVerificationRole,
            defaultVerificationUser,
            daysToVerification,
            isExtensionsAllowed,
            defaultExtensionApproverRole,
            isExtensionAttachmentRequired,
        } = categoriesConfigurationToEdit ?? {}

        setValue('category', categoriesOptions.find((u) => u.value == category?.externalId)?.value ?? '')
        setValue(
            'subCategory1',
            subCategoriesOptions.find((u) => u.value == actionItemSubCategory?.externalId)?.value ?? ''
        )
        setValue(
            'subCategory2',
            siteSpecificCategoriesOptions.find((u) => u.value == siteSpecificCategory?.externalId)?.value ?? ''
        )
        setValue('daysFromAssignedDate', daysFromAssignedDate?.toString() ?? '0')
        setValue(
            'hasEmailNotification',
            booleanOptions.find((u) => u.value == hasEmailNotification?.toString())?.value ?? ''
        )
        setValue('evidenceRequired', booleanOptions.find((u) => u.value == attachmentRequired?.toString())?.value ?? '')
        setValue(
            'isApprovalRequired',
            booleanOptions.find((u) => u.value == isApprovalRequired?.toString())?.value ?? ''
        )
        setValue(
            'defaultApprovalRole',
            rolesOptions.find((u) => u.value == defaultApprovalRole?.externalId)?.value ?? ''
        )
        setValue('defaultApprovalUser', defaultApprovalUser?.externalId)
        setValue('daysToApproval', daysToApproval?.toString() ?? '0')
        setValue(
            'isVerificationRequired',
            booleanOptions.find((u) => u.value == isVerificationRequired?.toString())?.value ?? ''
        )
        setValue(
            'defaultVerificationRole',
            rolesOptions.find((u) => u.value == defaultVerificationRole?.externalId)?.value ?? ''
        )
        setValue('defaultVerificationUser', defaultVerificationUser?.externalId)
        setValue('daysToVerification', daysToVerification?.toString() ?? '0')
        setValue(
            'isExtensionsAllowed',
            booleanOptions.find((u) => u.value == isExtensionsAllowed?.toString())?.value ?? ''
        )
        setValue(
            'defaultExtensionApproverRole',
            rolesOptions.find((u) => u.value == defaultExtensionApproverRole?.externalId)?.value ?? ''
        )
        setValue(
            'isExtensionAttachmentRequired',
            booleanOptions.find((u) => u.value == isExtensionAttachmentRequired?.toString())?.value ?? ''
        )
    }, [categoriesConfigurationToEdit])

    useEffect(() => {
        const approvalUser = getValues('defaultApprovalUser')
        const isApprovalUserValid = approvalUsersOptions.find((option) => option.value === approvalUser)

        if (!isApprovalUserValid) {
            setValue('defaultApprovalUser', '')
        }
    }, [approvalUsersOptions])

    useEffect(() => {
        const verificationUser = getValues('defaultVerificationUser')
        const isVerificationUserValid = verificationUsersOptions.find((option) => option.value === verificationUser)

        if (!isVerificationUserValid) {
            setValue('defaultVerificationUser', '')
        }
    }, [verificationUsersOptions])

    const onSubmit: SubmitHandler<CategoriesConfigurationFormSchema> = (data: any) => {
        onSubmitCallback(data, () => reset())
    }

    const clearForm = () => {
        reset({
            category: '',
            subCategory1: '',
            subCategory2: '',
            daysFromAssignedDate: '0',
            hasEmailNotification: '',
            evidenceRequired: '',
            isApprovalRequired: '',
            defaultApprovalRole: '',
            defaultApprovalUser: '',
            daysToApproval: '0',
            isVerificationRequired: '',
            defaultVerificationRole: '',
            defaultVerificationUser: '',
            daysToVerification: '0',
            isExtensionsAllowed: '',
            defaultExtensionApproverRole: '',
            isExtensionAttachmentRequired: '',
        })
    }

    const onCancel = () => {
        clearForm()
        onCloseCallback()
    }

    return (
        <CustomDrawer
            overlineMeta={
                isEdit
                    ? translate('adminSettings.categories.drawerEditTitle')
                    : translate('adminSettings.categories.drawerCreateTitle')
            }
            title={
                isEdit
                    ? translate('adminSettings.categories.drawerEditTitle')
                    : translate('adminSettings.categories.drawerCreateTitle')
            }
            openDrawer={openDrawer}
            closeDrawer={onCancel}
            content={
                <Box sx={drawerStyles.container}>
                    <form onSubmit={handleSubmit(onSubmit)} style={drawerStyles.formContainer}>
                        <GenericAutocomplete
                            name="category"
                            control={control}
                            label={`${translate('adminSettings.categories.form.category')} *`}
                            onChange={(newValue) => setValue('category', newValue)}
                            options={categoriesOptions}
                            size="small"
                            multiple={false}
                            error={Boolean(errors.category)}
                            disabled={!!categoriesConfigurationToEdit}
                            disableCloseOnSelect={false}
                        />
                        <GenericAutocomplete
                            name="subCategory1"
                            multiple={false}
                            control={control}
                            disabled={!!categoriesConfigurationToEdit}
                            options={
                                subCategoriesOptions && subCategoriesOptions.length > 0 ? subCategoriesOptions : []
                            }
                            onChange={(newValue) => setValue('subCategory1', newValue)}
                            label={`${translate('adminSettings.categories.form.subcategoryOne')} *`}
                            error={Boolean(errors.subCategory1)}
                            disableCloseOnSelect={false}
                            size="small"
                        />
                        <GenericAutocomplete
                            name="subCategory2"
                            multiple={false}
                            control={control}
                            disabled={!!categoriesConfigurationToEdit}
                            options={
                                siteSpecificCategoriesOptions && siteSpecificCategoriesOptions.length > 0
                                    ? siteSpecificCategoriesOptions
                                    : []
                            }
                            onChange={(newValue) => setValue('subCategory2', newValue)}
                            label={`${translate('adminSettings.categories.form.subcategoryTwo')} ${
                                siteId === SITE_EXTERNAL_ID_REQUIRED_FIELD ? '*' : ''
                            }`}
                            error={Boolean(errors.subCategory2)}
                            disableCloseOnSelect={false}
                            size="small"
                        />
                        <GenericTextField
                            name="daysFromAssignedDate"
                            control={control}
                            label={translate('adminSettings.categories.form.daysFromAssignedDate')}
                            helperText=""
                            type="number"
                            error={Boolean(errors.daysFromAssignedDate)}
                            onChange={(newValue: any) =>
                                setValue('daysFromAssignedDate', Math.max(0, parseInt(newValue, 10)).toString())
                            }
                        />
                        <GenericAutocomplete
                            name="hasEmailNotification"
                            control={control}
                            label={`${translate('adminSettings.categories.form.hasEmailNotification')} *`}
                            onChange={(newValue) => setValue('hasEmailNotification', newValue)}
                            options={booleanOptions}
                            size="small"
                            multiple={false}
                            error={Boolean(errors.hasEmailNotification)}
                            disableCloseOnSelect={false}
                        />
                        <GenericAutocomplete
                            name="evidenceRequired"
                            control={control}
                            label={`${translate('stepper.form.assignment.evidenceRequired')} *`}
                            onChange={(newValue) => setValue('evidenceRequired', newValue)}
                            options={booleanOptions}
                            size="small"
                            multiple={false}
                            error={Boolean(errors.evidenceRequired)}
                            disableCloseOnSelect={false}
                        />
                        <GenericAutocomplete
                            name="isApprovalRequired"
                            control={control}
                            label={`${translate('adminSettings.categories.form.isApprovalRequired')} *`}
                            onChange={(newValue) => setValue('isApprovalRequired', newValue)}
                            options={booleanOptions}
                            size="small"
                            multiple={false}
                            error={Boolean(errors.isApprovalRequired)}
                            disableCloseOnSelect={false}
                        />
                        <GenericAutocomplete
                            name="defaultApprovalRole"
                            multiple={false}
                            control={control}
                            options={rolesOptions}
                            onChange={(newValue) => {
                                setValue('defaultApprovalRole', newValue)
                                setValue('defaultApprovalUser', '')
                            }}
                            label={`${translate('adminSettings.categories.form.defaultApprovalRole')} ${
                                watchIsApprovalRequired === 'true' ? '*' : ''
                            }`}
                            error={Boolean(errors.defaultApprovalRole)}
                            helperText={errors.defaultApprovalRole ? errors.defaultApprovalRole.message : ''}
                            disableCloseOnSelect={false}
                            size="small"
                            disabled={watchIsApprovalRequired !== 'true'}
                        />
                        <GenericAutocomplete
                            name="defaultApprovalUser"
                            multiple={false}
                            control={control}
                            options={approvalUsersOptions}
                            onChange={(newValue) => setValue('defaultApprovalUser', newValue)}
                            label={`${translate('adminSettings.categories.form.defaultApprovalUser')} ${
                                siteId !== SITE_EXTERNAL_ID_REQUIRED_FIELD && watchDefaultApprovalRole ? '*' : ''
                            }`}
                            error={Boolean(errors.defaultApprovalUser)}
                            helperText={errors.defaultApprovalUser ? errors.defaultApprovalUser.message : ''}
                            disableCloseOnSelect={false}
                            size="small"
                            disabled={
                                watchIsApprovalRequired !== 'true' ||
                                !watchDefaultApprovalRole ||
                                watchDefaultApprovalRole === UNIT_MANAGER_ROLE_WAS
                            }
                            noOptionsText={translate('adminSettings.categories.form.noUsersForThisRole')}
                        />
                        <GenericTextField
                            name="daysToApproval"
                            control={control}
                            label={translate('adminSettings.categories.form.daysToApproval')}
                            helperText=""
                            type="number"
                            error={Boolean(errors.daysToApproval)}
                            onChange={(newValue: any) =>
                                setValue('daysToApproval', Math.max(0, parseInt(newValue, 10)).toString())
                            }
                        />
                        <GenericAutocomplete
                            name="isVerificationRequired"
                            control={control}
                            label={`${translate('adminSettings.categories.form.isVerificationRequired')} *`}
                            onChange={(newValue) => setValue('isVerificationRequired', newValue)}
                            options={booleanOptions}
                            size="small"
                            multiple={false}
                            error={Boolean(errors.isVerificationRequired)}
                            disableCloseOnSelect={false}
                        />
                        <GenericAutocomplete
                            name="defaultVerificationRole"
                            multiple={false}
                            control={control}
                            options={rolesOptions}
                            onChange={(newValue) => {
                                setValue('defaultVerificationRole', newValue)
                                setValue('defaultVerificationUser', '')
                            }}
                            label={`${translate('adminSettings.categories.form.defaultVerificationRole')} ${
                                watchIsVerificationRequired === 'true' ? '*' : ''
                            }`}
                            error={Boolean(errors.defaultVerificationRole)}
                            helperText={errors.defaultVerificationRole ? errors.defaultVerificationRole.message : ''}
                            disableCloseOnSelect={false}
                            size="small"
                            disabled={watchIsVerificationRequired !== 'true'}
                        />
                        <GenericAutocomplete
                            name="defaultVerificationUser"
                            multiple={false}
                            control={control}
                            options={verificationUsersOptions}
                            onChange={(newValue) => setValue('defaultVerificationUser', newValue)}
                            label={`${translate('adminSettings.categories.form.defaultVerificationUser')} ${
                                siteId !== SITE_EXTERNAL_ID_REQUIRED_FIELD && watchDefaultVerificationRole ? '*' : ''
                            }`}
                            error={Boolean(errors.defaultVerificationUser)}
                            helperText={errors.defaultVerificationUser ? errors.defaultVerificationUser.message : ''}
                            disableCloseOnSelect={false}
                            size="small"
                            disabled={
                                watchIsVerificationRequired !== 'true' ||
                                !watchDefaultVerificationRole ||
                                watchDefaultVerificationRole === UNIT_MANAGER_ROLE_WAS
                            }
                            noOptionsText={translate('adminSettings.categories.form.noUsersForThisRole')}
                        />
                        <GenericTextField
                            name="daysToVerification"
                            control={control}
                            label={translate('adminSettings.categories.form.daysToVerification')}
                            helperText=""
                            type="number"
                            error={Boolean(errors.daysToVerification)}
                            onChange={(newValue: any) =>
                                setValue('daysToVerification', Math.max(0, parseInt(newValue, 10)).toString())
                            }
                        />
                        <GenericAutocomplete
                            name="isExtensionsAllowed"
                            control={control}
                            label={`${translate('adminSettings.categories.form.isExtensionsAllowed')} *`}
                            onChange={(newValue) => setValue('isExtensionsAllowed', newValue)}
                            options={booleanOptions}
                            size="small"
                            multiple={false}
                            error={Boolean(errors.isExtensionsAllowed)}
                            disableCloseOnSelect={false}
                        />
                        <GenericAutocomplete
                            name="defaultExtensionApproverRole"
                            multiple={false}
                            control={control}
                            options={rolesOptions}
                            onChange={(newValue) => setValue('defaultExtensionApproverRole', newValue)}
                            label={translate('adminSettings.categories.form.defaultExtensionApproverRole')}
                            error={Boolean(errors.defaultExtensionApproverRole)}
                            disableCloseOnSelect={false}
                            size="small"
                            disabled={watchIsExtensionsAllowed !== 'true'}
                        />
                        <GenericAutocomplete
                            name="isExtensionAttachmentRequired"
                            control={control}
                            label={`${translate('adminSettings.categories.form.isExtensionAttachmentRequired')} ${
                                watchIsExtensionsAllowed === 'true' ? '*' : ''
                            }`}
                            onChange={(newValue) => setValue('isExtensionAttachmentRequired', newValue)}
                            options={booleanOptions}
                            size="small"
                            multiple={false}
                            error={Boolean(errors.isExtensionAttachmentRequired)}
                            disabled={watchIsExtensionsAllowed !== 'true'}
                            disableCloseOnSelect={false}
                        />
                    </form>
                    <Box sx={drawerStyles.buttonsContainer}>
                        <ClnButton
                            label={translate('adminSettings.categories.form.buttons.cancel')}
                            variant="text"
                            onClick={onCancel}
                            sx={{ flex: 1 }}
                        />
                        <ClnButton
                            label={
                                isEdit
                                    ? translate('adminSettings.categories.form.buttons.save')
                                    : translate('adminSettings.categories.form.buttons.create')
                            }
                            onClick={handleSubmit(onSubmit)}
                            sx={{ flex: 1 }}
                        />
                    </Box>
                </Box>
            }
        />
    )
}
