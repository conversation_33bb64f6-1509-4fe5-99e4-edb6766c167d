import os
import sys
from typing import Optional

from clients.core.models import PaginatedData
from clients.category_configuration.models import CategoryConfigurationByFilterResult
from clients.category_configuration.requests import (
    GetCategoryConfigurationByFilterRequest,
)

from clients.action_item_client import ActionItemClient

script_dir = os.path.dirname(os.path.abspath(__file__))
project_dir = os.path.dirname(script_dir)
sys.path.append(project_dir)


class CategoryConfigurationService:
    def __init__(
        self,
        action_item_client: ActionItemClient,
    ):
        """
        Initializes the CategoryConfigurationService with necessary dependencies.

        Parameters:
        - action_item_client (ActionItemClient): The client used to interact with action items and category configurations.
        """
        self._action_item_client = action_item_client
        self._log = action_item_client.actions._logging

    def get_category_configurations_by_site(
        self, request: GetCategoryConfigurationByFilterRequest
    ) -> Optional[list[CategoryConfigurationByFilterResult]]:
        """
        Retrieves the category configurations for a specific site based on the provided filter parameters.

        Parameters:
        - request (GetCategoryConfigurationByFilterRequest): Contains the filter criteria used to retrieve the category configurations for the specified site.

        Returns:
        - list[CategoryConfigurationByFilterResult] | None: A list of category configurations for the specified site that match the filter criteria, or None if no configurations are found.

        Method Overview:
        - Uses the provided filter criteria to retrieve the category configurations specific to the site in the request.
        - Calls the action item client to fetch the configurations based on the site-specific filters.
        - Returns the data from the first page of results if any configurations are found, or None if no matching configurations are found.

        Error Handling:
        - Logs any errors encountered during the retrieval process, including issues related to the site or client communication.
        """
        self._log.info(
            f"Starting to get category configuration by site: {request.reporting_site_external_id}"
        )

        try:
            category_configurations: PaginatedData[
                CategoryConfigurationByFilterResult
            ] = self._action_item_client.category_configuration.get_category_configurations(
                request, True
            )

            return category_configurations.data
        except Exception as err:
            self._log.error(
                f"Error retrieving category configurations by site: {request.reporting_site_external_id}"
                f"Error: {err}"
            )
            return None

    def get_category_configuration_by_filter(
        self, request: GetCategoryConfigurationByFilterRequest
    ) -> Optional[CategoryConfigurationByFilterResult]:
        """
        Retrieves the category configuration based on the provided filter parameters (category, subcategory, and site-specific-category).

        Parameters:
        - request (GetCategoryConfigurationByFilterRequest): Contains the IDs for category, subcategory, and site-specific-category used to filter the configuration.

        Returns:
        - CategoryConfigurationByFilterResult | None: Returns the first matching category configuration if found, otherwise returns None.

        Method Details:
        - Creates a request using the provided category, subcategory, and site-specific category IDs.
        - Calls the action item client to retrieve the category configurations that match the filter.
        - Returns the first result found or None if no match is found.

        Raises:
        - Logs any exceptions encountered during the retrieval process.
        """
        self._log.info(
            f"Starting to get category configuration by category: {request.category_id}, "
            f"subcategory: {request.sub_category_id}, and site-specific-category: {request.site_specific_category_id}"
        )

        try:
            category_configurations: PaginatedData[
                CategoryConfigurationByFilterResult
            ] = self._action_item_client.category_configuration.get_category_configurations(
                request
            )

            if len(category_configurations.data) > 0:
                return category_configurations.data[0]

            return None

        except Exception as err:
            self._log.error(
                f"Error retrieving category configuration with category: {request.category_id}, "
                f"subcategory: {request.sub_category_id}, and site-specific category: {request.site_specific_category_id}. "
                f"Error: {err}"
            )
            return None
