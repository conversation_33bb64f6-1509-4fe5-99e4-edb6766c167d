import os
import sys
from datetime import UTC, datetime

from clients.action_item_client import ActionItemClient
from clients.approval_workflow.constants import (
    ApprovalWorkflowCondition,
    ApprovalWorkflowConsentType,
)
from clients.approval_workflow.models import (
    ApprovalWorkflowStepUpdate,
    ApprovalWorkflowUpdate,
)
from clients.approval_workflow.requests import GetApprovalWorkflowRequest
from clients.core.constants import (
    ApprovalWorkflowStatusEnum,
    DataSpaceEnum,
)
from clients.core.models import Node
from models.entities_enum import EntitiesEnum
from utils.id_generation_utils import IdGenerator

script_dir = os.path.dirname(os.path.abspath(__file__))
project_dir = os.path.dirname(script_dir)
sys.path.append(project_dir)


class ApprovalWorkflowService:
    """Service for managing approval workflows and workflow steps."""

    def __init__(
        self,
        action_item_client: ActionItemClient,
    ) -> None:
        """
        Initialize the ApprovalWorkflowService.

        Args:
            action_item_client (ActionItemClient): Client for interacting with action items and workflows.

        """
        self._action_item_client = action_item_client
        self._log = action_item_client.actions._logging
        self._now_datetime = datetime.now(tz=UTC)
        self._id_generator = IdGenerator[EntitiesEnum](self._now_datetime)

    def get_approval_workflows(self, request: GetApprovalWorkflowRequest) -> list[Node]:
        """
        Retrieve a list of approval workflow external IDs based on a request.

        Args:
            request (GetApprovalWorkflowRequest): Request containing the filters for retrieving approval workflows.

        Returns:
            list[Node]: A list of approval workflow nodes. Returns an empty list if an error occurs.

        """
        try:
            approval_workflow_nodes = (
                self._action_item_client.approval_workflow.get_approval_workflows(
                    request,
                )
            )
        except Exception as err:
            self._log.error(
                f"Error getting approval workflows. Error: {err}",
            )
            return []
        return approval_workflow_nodes

    def generate_upsert_approval_workflow_step(
        self,
        space: str,
        external_id: str | None = None,
        start_date: str | None = None,
        end_date: str | None = None,
        description: str | None = None,
        status: str | None = None,
        step: int | None = None,
        user: str | None = None,
    ) -> ApprovalWorkflowStepUpdate:
        """
        Generate an ApprovalWorkflowStepUpdate object for upserting an approval workflow step.

        Args:
            space (str): The space where the approval workflow step will be created.
            external_id (str, optional): The external ID of the step. If None, a new ID is generated.
            start_date (str, optional): Start date of the step in 'YYYY-MM-DD' format.
            end_date (str, optional): End date of the step in 'YYYY-MM-DD' format.
            description (str, optional): Description of the step.
            status (str, optional): Status external ID of the step. Defaults to 'PENDING' if not provided.
            step (int, optional): Step number. Defaults to 1 if external_id is None.
            user (str, optional): External ID of the assigned user.

        Returns:
            ApprovalWorkflowStepUpdate: The generated workflow step object.

        """
        status_node = Node(
            external_id=status or ApprovalWorkflowStatusEnum.PENDING,
            space=DataSpaceEnum.APW_REF_DATA_SPACE,
        )

        approval_condition = None
        approval_workflow_consent_type = None
        step_value = None
        users = None
        generated_external_id = external_id

        if external_id is None:
            step_value = step if step is not None else 1
            generated_external_id = self._id_generator.next_id(
                EntitiesEnum.ApprovalWorkflowStep,
            )
            approval_condition = Node(
                external_id=ApprovalWorkflowCondition.AND,
                space=DataSpaceEnum.APW_REF_DATA_SPACE,
            )
            approval_workflow_consent_type = Node(
                external_id=ApprovalWorkflowConsentType.USER,
                space=DataSpaceEnum.APW_REF_DATA_SPACE,
            )
            users = Node(
                external_id=user,
                space=DataSpaceEnum.UMG_DATA_SPACE,
            )

        return ApprovalWorkflowStepUpdate(
            external_id=generated_external_id,
            space=space,
            start_date=start_date,
            end_date=end_date,
            step=step_value,
            description=description,
            status=status_node,
            users=users,
            approval_condition=approval_condition,
            approval_workflow_consent_type=approval_workflow_consent_type,
        )

    def generate_upsert_approval_workflow(
        self,
        space: str,
        steps: list[ApprovalWorkflowStepUpdate],
        external_id: str | None = None,
        description: str | None = None,
        status: str | None = None,
        start_date: str | None = None,
        end_date: str | None = None,
        created_by_id: str | None = None,
    ) -> ApprovalWorkflowUpdate:
        """
        Generate an ApprovalWorkflowUpdate object for upserting an approval workflow.

        Args:
            space (str): The space where the approval workflow will be created.
            steps (list[ApprovalWorkflowStepUpdate]): List of workflow steps to include in the workflow.
            external_id (str, optional): The external ID of the workflow. If None, a new ID is generated.
            description (str, optional): Description of the workflow.
            status (str, optional): Status external ID of the workflow. Defaults to 'PENDING' if not provided.
            start_date (str, optional): Start date of the workflow in 'YYYY-MM-DD' format.
            end_date (str, optional): End date of the workflow in 'YYYY-MM-DD' format.
            created_by_id (str, optional): External ID of the user who created the workflow.

        Returns:
            ApprovalWorkflowUpdate: The generated workflow object.

        """
        generated_external_id = external_id or self._id_generator.next_id(
            EntitiesEnum.ApprovalWorkflowStep,
        )

        step_value = 1 if external_id is None else None

        status_node = Node(
            external_id=status or ApprovalWorkflowStatusEnum.PENDING,
            space=DataSpaceEnum.APW_REF_DATA_SPACE,
        )

        created_by = (
            Node(
                external_id=created_by_id,
                space=DataSpaceEnum.UMG_DATA_SPACE,
            )
            if created_by_id
            else None
        )

        return ApprovalWorkflowUpdate(
            external_id=generated_external_id,
            space=space,
            status=status_node,
            description=description,
            current_step=step_value,
            steps=steps,
            start_date=start_date,
            end_date=end_date,
            created_by=created_by,
        )
