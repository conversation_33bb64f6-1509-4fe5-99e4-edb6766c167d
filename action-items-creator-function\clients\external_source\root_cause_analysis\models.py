from datetime import datetime
from typing import Annotated

from pydantic import Field

from clients.core.models import DescribableEntity, Node
from clients.core.validators import edge_unwraper_validator

from .responses import (
    EventAnalysisResponse,
    EventInvestigationResponse,
    WorkProcessInvestigationResponse,
)


class _User(Node):
    display_name: str | None = Field(default=None)


class _UserAzureAttribute(Node):
    user: _User | None = Field(default=None)


class _UserComplement(Node):
    user_azure_attribute: _UserAzureAttribute | None = Field(default=None)

    def get_display_name(self) -> str | None:
        if self.user_azure_attribute is None or self.user_azure_attribute.user is None:
            return None

        return self.user_azure_attribute.user.display_name


class _Approver(Node):
    approver: _UserComplement | None = Field(default=None)

    def get_display_name(self) -> str | None:
        if self.approver is None:
            return None

        return self.approver.get_display_name()


class EventAnalysis(Node):
    """Represents an event analysis."""

    external_event_id: str | None = Field(default=None)
    event_title: str | None = Field(default=None)
    event_type: DescribableEntity | None = Field(default=None)
    start_date: datetime | None = Field(default=None)
    due_date: datetime | None = Field(default=None)
    assigned_to: _UserComplement | None = Field(default=None)
    team: Annotated[
        list[_UserComplement],
        edge_unwraper_validator,
    ] = Field(default_factory=list)
    investigation_type: DescribableEntity | None = Field(default=None)
    root_causes: list[str] | None = Field(default_factory=list)
    approver: Annotated[list[_Approver], edge_unwraper_validator] = Field(
        default_factory=list,
    )
    reporting_location: DescribableEntity | None = Field(default=None)

    def to_response(self) -> EventAnalysisResponse:
        """Cast the entity to its response type."""
        return EventAnalysisResponse(
            external_id=self.external_id,
            space=self.space,
            external_event_id=self.external_event_id,
            event_title=self.event_title,
            event_type=(self.event_type.name if self.event_type is not None else None),
            investigation_start_date=(
                self.start_date.date() if self.start_date is not None else None
            ),
            investigation_team=[
                display_name
                for a in self.team
                if (display_name := a.get_display_name()) is not None
            ],
            due_date=self.due_date.date() if self.due_date is not None else None,
            lead_investigator=(
                self.assigned_to.get_display_name()
                if self.assigned_to is not None
                else None
            ),
            rca_type=(
                self.investigation_type.name
                if self.investigation_type is not None
                else None
            ),
            root_causes=self.root_causes,
            approvers=[
                display_name
                for a in self.approver
                if (display_name := a.get_display_name()) is not None
            ],
            reporting_location=(
                self.reporting_location.description
                if self.reporting_location is not None
                else None
            ),
        )


class EventInvestigation(Node):
    """Represents an event investigation."""

    reporting_unit: DescribableEntity | None = Field(default=None)
    business_line: DescribableEntity | None = Field(default=None)
    functional_location: DescribableEntity | None = Field(default=None)
    equipment: DescribableEntity | None = Field(default=None)

    def to_response(self) -> EventInvestigationResponse:
        """Cast the entity to its response type."""
        return EventInvestigationResponse(
            external_id=self.external_id,
            space=self.space,
            reporting_unit=(
                self.reporting_unit.description
                if self.reporting_unit is not None
                else None
            ),
            business_line=(
                self.business_line.description
                if self.business_line is not None
                else None
            ),
            functional_location=(
                self.functional_location.description
                if self.functional_location is not None
                else None
            ),
            equipment=(
                self.equipment.description if self.equipment is not None else None
            ),
        )


class WorkProcessInvestigation(Node):
    """Represents a work process investigation."""

    reporting_unit: DescribableEntity | None = Field(default=None)
    functional_location: DescribableEntity | None = Field(default=None)
    equipment: DescribableEntity | None = Field(default=None)
    problem_description: str | None = Field(default=None)

    def to_response(self) -> WorkProcessInvestigationResponse:
        """Cast the entity to its response type."""
        return WorkProcessInvestigationResponse(
            external_id=self.external_id,
            space=self.space,
            reporting_unit=(
                self.reporting_unit.description
                if self.reporting_unit is not None
                else None
            ),
            functional_location=(
                self.functional_location.description
                if self.functional_location is not None
                else None
            ),
            equipment=(
                self.equipment.description if self.equipment is not None else None
            ),
            problem_description=self.problem_description,
        )
