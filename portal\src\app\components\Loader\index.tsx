import { ClnCircularProgress } from '@celanese/ui-lib'
import * as S from './styles'
import { Backdrop } from '@mui/material'

export function FullLoaderCircular(isLoading: boolean) {
    return (
        <Backdrop sx={{ zIndex: 10 }} open={isLoading}>
            <ClnCircularProgress />
        </Backdrop>
    )
}

export function LoaderCircularIcon(size?: number) {
    return <ClnCircularProgress size={size ?? 40} value={0} />
}

export default function LoaderCircular(size?: number) {
    return (
        <S.LoaderContainer>
            <ClnCircularProgress size={size ?? 40} value={0} />
        </S.LoaderContainer>
    )
}

export { LoaderCircular }
