import { NoTranslate } from '@celanese/celanese-ui'
import { Box, useMediaQuery, useTheme } from '@mui/material'
import { useEffect, useState } from 'react'
import { AzureFunctionClient } from '@/app/common/clients/azure-function-client'
import { FilterInfoProps } from '../../ActionTable/HomeFilter'
import { ClnButtonProps, MatIcon } from '@celanese/ui-lib'
import { ActionTable, allActionColumns } from '../../ActionTable'
import { translate } from '@/app/common/utils/generate-translate'
import { UserRolesPermission } from '@celanese/celanese-sdk'
import { PageType } from '@/app/common/enums/PageTypeEnum'

type SupervisorTableProps = {
    filterInfo: FilterInfoProps
    client: AzureFunctionClient
    currentPage: number
    activeUser?: UserRolesPermission
    siteId: string
    handleError: (err: any) => void
    setIsChart: (value: boolean) => void
    setCurrentPage: (value: number) => void
}

export function SupervisorTable({
    filterInfo,
    client,
    currentPage,
    activeUser,
    siteId,
    handleError,
    setIsChart,
    setCurrentPage,
}: SupervisorTableProps) {
    const theme = useTheme()
    const isMobile = useMediaQuery(theme.breakpoints.down('md'))

    const [memoryPage, setMemoryPage] = useState(0)

    const [filterTableInfo, setFilterTableInfo] = useState<FilterInfoProps>({ ...filterInfo })

    const additionalButtons: ClnButtonProps[] = [
        {
            startIcon: <MatIcon icon="bar_chart" />,
            label: isMobile ? '' : translate('table.chartView'),
            variant: 'contained',
            onClick: () => {
                setIsChart(true)
            },
            sxProps: isMobile
                ? {
                      color: 'primary.main',
                      minWidth: '12px !important',
                      '& .MuiButton-startIcon': {
                          marginRight: 0,
                      },
                  }
                : undefined,
        },
    ]

    const resetAllPages = () => {
        setMemoryPage(0)
        setCurrentPage(0)
    }

    useEffect(() => {
        resetAllPages()
        setFilterTableInfo({
            ...filterInfo,
            cursor: undefined,
            pageSize: filterTableInfo.pageSize,
            sortBy: filterTableInfo.sortBy,
            direction: filterTableInfo.direction,
            search: filterTableInfo.search,
        })
    }, [filterInfo])

    useEffect(() => {
        const visibleColumns = [
            'act',
            'externalId',
            'sortTitle',
            'sortCategory',
            'sortSubCategory',
            'sortSiteSpecificCategory',
            'sortAssignee',
            'displayDueDate',
            'manager',
            'sortCurrentStatus',
            'isPrivate',
        ]

        const defaultVisibilityModel = allActionColumns.reduce(
            (acc, column) => {
                if (!visibleColumns.includes(column)) {
                    acc[column] = false
                }
                return acc
            },
            {} as Record<string, boolean>
        )

        const defaultColumnOrder = [
            ...visibleColumns,
            ...allActionColumns.filter((col) => !visibleColumns.includes(col)),
        ]

        const visibilityKey = 'visibilityModelChange-dashboard-supervisor-filterInfo-tab'
        const columnOrderKey = 'columnOrderIds-dashboard-supervisor-filterInfo-tab'

        if (!localStorage.getItem(visibilityKey)) {
            localStorage.setItem(visibilityKey, JSON.stringify(defaultVisibilityModel))
        }

        if (!localStorage.getItem(columnOrderKey)) {
            localStorage.setItem(columnOrderKey, JSON.stringify(defaultColumnOrder))
        }
    }, [])

    return (
        <Box>
            <NoTranslate>
                <ActionTable
                    id="dashboard-supervisor"
                    setFilterInfo={setFilterTableInfo}
                    filterInfo={filterTableInfo}
                    setCurrentPage={setCurrentPage}
                    currentPage={currentPage}
                    activeUser={activeUser!}
                    client={client}
                    siteIds={[siteId]}
                    handleFetchFailure={handleError}
                    memoryPage={memoryPage}
                    setMemoryPage={setMemoryPage}
                    additionalButtons={additionalButtons}
                    showCustomFilter={false}
                    pageType={PageType.Dashboard}
                />
            </NoTranslate>
        </Box>
    )
}

export default SupervisorTable
