from typing import Any

from pydantic import BeforeValidator


def _edge_unwraper(data: Any, edge_id: bool | None = None) -> list[dict[str, Any]]:
    if data is None:
        return []

    if isinstance(data, list):
        return data
    elif isinstance(data, dict):
        items = data.get("items", [])

        if edge_id:
            edges = data.get("edges", [])
            for i, (item, edge) in enumerate(zip(items, edges)):
                first_key = next(iter(item.keys()), None)
                if first_key and isinstance(item[first_key], dict):
                    item = item[first_key]
                edge_external_id = edge.get("externalId")
                if edge_external_id:
                    item["edgeExternalId"] = edge_external_id
                items[i] = item

        return items
    else:
        return []


str_or_default_validator = BeforeValidator(lambda x: x if x is not None else "")
edge_unwraper_validator = BeforeValidator(lambda x: _edge_unwraper(x, edge_id=False))
edge_unwraper_validator_with_edge_id = BeforeValidator(
    lambda x: _edge_unwraper(x, edge_id=True)
)
