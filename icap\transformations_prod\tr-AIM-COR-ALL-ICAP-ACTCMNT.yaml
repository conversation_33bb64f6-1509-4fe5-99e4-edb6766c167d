# Manifest file downloaded from fusion
externalId: tr-AIM-COR-ALL-ICAP-ACTCMNT
name: AIM-COR-ALL-ICAP-ACTCMNT
query: >
  SELECT
      coalesce(
          aim_comment.externalId,
          concat(
              'ACTCMNT-',
              split(split(current_timestamp(), ' ') [0], '-') [0],
              split(split(current_timestamp(), ' ') [0], '-') [1],
              split(split(current_timestamp(), ' ') [0], '-') [2],
              split(split(current_timestamp(), ' ') [1], ':') [0],
              split(split(current_timestamp(), ' ') [1], ':') [1],
              '-',
              ROW_NUMBER() OVER(
                  ORDER BY stg_action.key ASC
              )
          )
      ) AS externalId,
      stg_action.space AS space, 
      node_reference(stg_action.space, stg_action.externalId) as action,
      stg_action.concatenated_comments AS comment,
      TRUE AS isLegacy
  FROM `AIM-COR`.`ICAP-STG-Action` stg_action

  INNER JOIN cdf_data_models("AIM-COR-ALL-DMD", "ActionItemManagementDOM", "6_0_0", "Action") aim_action
      ON split_part(aim_action.objectType, '-', 2) = stg_action.key
      AND startswith(aim_action.objectType, 'ICAP')
      AND aim_action.space = stg_action.space
  LEFT JOIN cdf_data_models("AIM-COR-ALL-DMD", "ActionItemManagementDOM", "6_0_0", "ActionComment") aim_comment
      ON aim_comment.action.externalId = stg_action.externalId 
      AND aim_comment.action.space = stg_action.space
      AND aim_comment.isLegacy
  WHERE coalesce(stg_action.concatenated_comments, '') <> ''
  	AND stg_action.viewOnly
destination:
  dataModel:
    space: AIM-COR-ALL-DMD
    externalId: ActionItemManagementDOM
    version: "6_0_0"
    destinationType: ActionComment
  type: instances
ignoreNullFields: false
shared: true
action: upsert
dataSetExternalId: AIM-COR-ALL-DAT
# Specify credentials separately like this:
# authentication:
#   read:
#     clientId: ${READ_CLIENT_ID}
#     clientSecret: ${READ_CLIENT_SECRET}
#     tokenUrl: ${READ_TOKEN_URL}
#     cdfProjectName: ${READ_CDF_PROJECT_NAME}
#     # Optional: If idP requires providing the scopes
#     scopes:
#       - ${READ_SCOPES}
#     # Optional: If idP requires providing the audience
#     audience: ${READ_CDF_AUDIENCE}
#   write:
#     clientId: ${WRITE_CLIENT_ID}
#     clientSecret: ${WRITE_CLIENT_SECRET}
#     tokenUrl: ${WRITE_TOKEN_URL}
#     cdfProjectName: ${WRITE_CDF_PROJECT_NAME}
#     # Optional: If idP requires providing the scopes
#     scopes:
#       - ${WRITE_SCOPES}
#     # Optional: If idP requires providing the audience
#     audience: ${WRITE_CDF_AUDIENCE}
# Or together like this:
# authentication:
#   clientId: ${CLIENT_ID}
#   clientSecret: ${CLIENT_SECRET}
#   tokenUrl: ${TOKEN_URL}
#   # Optional: If idP requires providing the scopes
#   cdfProjectName: ${CDF_PROJECT_NAME}
#   scopes:
#    - ${SCOPES}
#   # Optional: If idP requires providing the audience
#   audience: ${AUDIENCE}