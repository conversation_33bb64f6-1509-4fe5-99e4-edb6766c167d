import { gql, useMutation } from '@apollo/client'
import { WorkflowStep } from '../../models/common/approval-workflow/workflow-step'

const APPROVAL_WORKFLOW_STEP_MUTATION = gql`
    mutation UpsertApprovalWorkflowStep($workflowSteps: [_UpsertApprovalWorkflowStep!]!) {
        upsertApprovalWorkflowStep(items: $workflowSteps) {
            space
            externalId
        }
    }
`

export const useUpsertApprovalWorkflowStep = () => {
    return useMutation<WorkflowStep>(APPROVAL_WORKFLOW_STEP_MUTATION)
}
