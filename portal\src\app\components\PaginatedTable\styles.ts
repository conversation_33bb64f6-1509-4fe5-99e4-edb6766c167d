import { css, CSSObject, InputBase as MUIInputBase, TablePagination as MUITablePagination } from '@mui/material'
import { styled } from '@mui/system'

export const Container = styled('div')`
    width: 100%;
`

export const TablePagination = styled(MUITablePagination)`
    & .MuiTablePagination-displayedRows {
        display: none;
    }
    & .MuiInputBase-root {
        margin-right: 0 !important;
    }
    & .MuiTablePagination-selectLabel {
        margin-right: 0 !important;
    }
    & .MuiTablePagination-actions {
        margin-left: auto;
        margin-right: 0 !important;
    }
    border-bottom: 0 !important;
    margin-right: 0 !important;
    padding-right: 0 !important;
`

export const Search = styled('div')`
    ${({ theme }: any) => css`
        display: flex;
        flex-direction: row;
        align-content: space-between;
        border-radius: 4px;
        height: 100%;
        border: 1px solid ${theme.palette.divider};
        background-color: ${theme.palette.background.paper};
        &:hover {
            background-color: ${theme.palette.grey[300]};
        }
        margin-left: 0;
        min-width: 242px;
    `}
`

export const SearchIconWrapper = styled('div')`
    padding: 5px;
      marginLeft: 'auto',
      height: 100%;
      pointer-events: none;
      display: flex;
      align-items: center;
      justify-content: center;
  `

export const StyledInputBase = styled(MUIInputBase)`
    ${({ theme }) => css`
        color: ${theme.palette.text.primary};
        height: 36.5px;
        width: 100%;
        & .MuiInputBase-input {
            padding-left: 30px;
            width: 100%;
        }
    `}
`

export const ButtonFilter: CSSObject = {
    padding: '6px 12px',
    fontSize: '14px',
    fontWeight: 500,
    width: '100%',
    textTransform: 'uppercase',
    fontFamily: 'Roboto, Segoe UI, Arial, Helvetica, sans-serif',
    '@media (min-width: 600px)': {
        border: '1px solid primary.main',
        borderRadius: '4px',
    },
}
