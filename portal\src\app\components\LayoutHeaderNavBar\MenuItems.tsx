import { usePathname, useRouter } from 'next/navigation'
import { useContext, useEffect, useState } from 'react'

import { MaterialSymbol } from '@material-symbols/font-400'
import { useAuthGuard } from '@/app/common/hooks/useAuthGuard'
import {
    MenuItemsProps,
    TranslationContext,
    TranslationContextState,
    UserManagementContext,
    UserManagementContextState,
} from '@celanese/celanese-ui'
import { translate } from '@/app/common/utils/generate-translate'

type MenuItensProps = {
    setIsCancelModalOpen: (value: boolean) => void
    setRouteString: (value: string) => void
    showLoading: (value: boolean) => void
}

export const MenuItems = ({ setIsCancelModalOpen, setRouteString, showLoading }: MenuItensProps) => {
    const [authorizedMenuItems, setAuthorizedMenuItems] = useState<MenuItemsProps[]>([])

    const router = useRouter()
    const { checkPermissionsFromRoutes } = useAuthGuard()
    const { userInfo } = useContext<UserManagementContextState>(UserManagementContext)
    const { locale } = useContext<TranslationContextState>(TranslationContext)
    const pathUrl = usePathname()

    const activeMenu = (menu: string) => menu === pathUrl

    const cleanStorage = (isEdit: boolean) => {
        showLoading(true)
        sessionStorage.removeItem('challengeCompleted')
        sessionStorage.removeItem('lastPage')

        if (!isEdit) {
            localStorage.removeItem('eventDetails')
            localStorage.removeItem('isCreateNewActions')
        }
    }

    const itemsMenu = [
        {
            route: '/',
            active: activeMenu('/'),
            title: translate('pages.home.title'),
            icon: 'home' as MaterialSymbol,
            multiSite: true,
            onClickHandler: () => {
                const isEdit = localStorage.getItem('isEditForm') !== null
                cleanStorage(isEdit)
                if (isEdit) {
                    setIsCancelModalOpen(true)
                    setRouteString('/')
                } else {
                    location.pathname === '/' ? location.replace('/') : router.push('/')
                }
            },
        },
        {
            route: '/event-source',
            active: activeMenu('/event-source'),
            title: translate('pages.source-event.title'),
            icon: 'event' as MaterialSymbol,
            multiSite: true,
            onClickHandler: () => {
                const isEdit = localStorage.getItem('isEditForm') !== null
                cleanStorage(isEdit)
                if (isEdit) {
                    setIsCancelModalOpen(true)
                    setRouteString('/event-source')
                } else {
                    router.push('/event-source')
                }
            },
        },
        {
            route: '/dashboard',
            active: activeMenu('/dashboard'),
            title: translate('pages.dashboards.title'),
            icon: 'insert_chart' as MaterialSymbol,
            multiSite: false,
            onClickHandler: () => {
                const isEdit = localStorage.getItem('isEditForm') !== null
                cleanStorage(isEdit)
                if (isEdit) {
                    setIsCancelModalOpen(true)
                    setRouteString('/dashboard')
                } else {
                    router.push('/dashboard')
                }
            },
        },
        {
            route: '/admin-settings',
            active: activeMenu('/admin-settings'),
            title: translate('pages.adminSettings.title'),
            icon: 'assignment_ind' as MaterialSymbol,
            multiSite: false,
            onClickHandler: () => {
                const isEdit = localStorage.getItem('isEditForm') !== null
                cleanStorage(isEdit)
                if (isEdit) {
                    setIsCancelModalOpen(true)
                    setRouteString('/admin-settings')
                } else {
                    router.push('/admin-settings')
                }
            },
        },
        {
            route: '/support-feature',
            active: activeMenu('/support-feature'),
            title: translate('supportFeature.support'),
            icon: 'support_agent' as MaterialSymbol,
            multiSite: false,
            onClickHandler: () => {
                const isEdit = localStorage.getItem('isEditForm') !== null
                cleanStorage(isEdit)
                if (isEdit) {
                    setIsCancelModalOpen(true)
                    setRouteString('/support-feature')
                } else {
                    router.push('/support-feature')
                }
            },
        },
    ]

    useEffect(() => {
        const items = itemsMenu.filter((item) => checkPermissionsFromRoutes(item.route))

        setAuthorizedMenuItems(items)
    }, [userInfo, pathUrl])

    useEffect(() => {
        const checkLocalStorage = () => {
            const storageKey = `APP-AIMTranslationData${locale}`
            const rawData = window.localStorage?.getItem(storageKey)

            if (locale && rawData) {
                try {
                    const parsedData = JSON.parse(rawData)
                    const translatedItemsMenu = [...itemsMenu]

                    translatedItemsMenu.forEach((item) => {
                        const key = `APP-AIM.${locale}.${item.title}`
                        const translatedValue = parsedData[key]
                        if (translatedValue) {
                            item.title = translatedValue
                        }
                    })

                    setAuthorizedMenuItems(translatedItemsMenu)
                } catch (error) {
                    console.error('Error while fetching translation data from local storage:', error)
                }
            } else {
                setTimeout(checkLocalStorage, 2000)
            }
        }

        checkLocalStorage()
    }, [locale])

    return authorizedMenuItems
}
