from typing import Any

from cognite.client.data_classes import data_modeling

from clients.actions.models import <PERSON><PERSON><PERSON><PERSON>pdate, StatusHistoryInstance
from clients.core.cognite_mappers import CogniteDependencyMapper
from clients.core.constants import (
    AGGREGATE_LIMIT,
    IMPACTED_REPORTING_UNITS_TO_SOURCE_EVENTS,
    LIMIT,
    SEARCH_LIMIT,
    ViewEnum,
)
from clients.core.models import Node, PaginatedData, ServiceParams

from .cognite_filters import (
    get_source_event_filters_for_aggregate,
    get_source_event_filters_for_industrial_model,
    get_source_event_query_filters,
)
from .models import (
    SourceEventByIdResult,
    SourceEventExportView,
    SourceEventHistoryUpdate,
    SourceEventResult,
    SourceEventStatusUpdate,
    SourceEventWithActionsResult,
)
from .queries import GET_SOURCE_EVENT_BY_ID_QUERY, GET_SOURCE_EVENT_QUERY
from .requests import (
    BaseSourceEventRequest,
    GetSourceEventByIdRequest,
    GetSourceEventRequest,
    GetSourceEventRequestForIndustrialModel,
)


class SourceEventClient:
    """A client for interacting with source events in the Cognite Data Model."""

    def __init__(self, params: ServiceParams) -> None:
        """Initialize the SourceEventClient with the provided service parameters."""
        self._cognite_client = params.cognite_client
        self._graphql_client = params.graphql_client
        self._data_model_id = params.data_model.as_id()
        self._settings = params.settings
        self._logging = params.logging
        self._engine = params.engine
        self._cognite_service = params.cognite_service
        self._graphql_service = params.graphql_service
        self._source_event_view = params.get_views()[ViewEnum.SOURCE_EVENT]
        self._source_event_history_view = params.get_views()[
            ViewEnum.SOURCE_EVENT_HISTORY
        ]
        self._action_view = params.get_views()[ViewEnum.ACTION]
        self._action_history_view = params.get_views()[ViewEnum.STATUS_HISTORY_INSTANCE]

    def count_source_events(self, request: BaseSourceEventRequest) -> int:
        """Count the total number of source events based on the provided request filters."""
        aggregate_result = self._cognite_client.data_modeling.instances.aggregate(
            view=self._source_event_view,
            aggregates=data_modeling.aggregations.Count("externalId"),
            filter=data_modeling.filters.And(
                *get_source_event_filters_for_aggregate(
                    request,
                    self._source_event_view,
                ),
            ),
        )

        return int(aggregate_result.value or 0)

    def get_source_events(
        self,
        request: GetSourceEventRequest,
    ) -> PaginatedData[SourceEventResult]:
        """Retrieve source events based on the provided request parameters."""
        if request.search:
            self._handle_search(request)

        if request.impacted_reporting_units:
            self._get_all_events(request)

        result = self._cognite_client.data_modeling.graphql.query(
            id=self._data_model_id,
            query=GET_SOURCE_EVENT_QUERY,
            variables=get_source_event_query_filters(request),
        )

        return PaginatedData[SourceEventResult].from_graphql_response(
            result,
            request.page_size,
        )

    def get_source_event_by_id(
        self,
        request: GetSourceEventByIdRequest,
    ) -> PaginatedData[SourceEventByIdResult]:
        """Retrieve a specific source event by its ID based on the provided request."""
        result = self._cognite_client.data_modeling.graphql.query(
            id=self._data_model_id,
            query=GET_SOURCE_EVENT_BY_ID_QUERY,
            variables=get_source_event_query_filters(request),
        )

        return PaginatedData[SourceEventByIdResult].from_graphql_response(
            result,
            request.page_size,
        )

    def get_source_events_with_actions(
        self,
        request: GetSourceEventRequest,
    ) -> PaginatedData[SourceEventWithActionsResult]:
        """Retrieve source events along with their associated actions based on the provided request parameters."""
        result = self._cognite_client.data_modeling.graphql.query(
            id=self._data_model_id,
            query=GET_SOURCE_EVENT_QUERY,
            variables=get_source_event_query_filters(request),
        )

        return PaginatedData[SourceEventWithActionsResult].from_graphql_response(
            result,
            AGGREGATE_LIMIT,
        )

    def update_source_event_and_actions_with_history(
        self,
        source_event: SourceEventStatusUpdate,
        source_event_history: SourceEventHistoryUpdate,
        actions: list[ActionItemUpdate],
        actions_histories: list[StatusHistoryInstance],
    ) -> None:
        """
        Apply updates to a source event and its related actions, including their respective histories.

        This method updates the current status of a source event and optionally applies updates
        to associated action items and their historical records. All updates are bundled and applied
        in a single operation to ensure consistency.

        Args:
            source_event (SourceEventStatusUpdate): Data for updating the source event's status.
            source_event_history (SourceEventHistoryUpdate): Historical data for the source event.
            actions (list[ActionItemUpdate]): List of related action items to update.
            actions_histories (list[StatusHistoryInstance]): Historical records for the action items.

        Raises:
            ValueError: If the provided data is invalid or incomplete.
            Exception: If the data model update operation fails.

        """
        nodes = []

        nodes.append(
            source_event.to_node_apply(
                self._source_event_view,
                source_event.get_properties_to_include(),
            ),
        )

        nodes.append(
            source_event_history.to_node_apply(
                self._source_event_history_view,
                source_event_history.get_properties_to_include(),
            ),
        )

        nodes.extend(
            action.to_node_apply(
                self._action_view,
                action.get_properties_to_include(),
            )
            for action in actions
        )

        nodes.extend(
            history.to_node_apply(
                self._action_history_view,
                history.get_properties_to_include(),
            )
            for history in actions_histories
        )

        self._cognite_client.data_modeling.instances.apply(nodes=nodes)

    def _handle_search(
        self,
        request: GetSourceEventRequest | GetSourceEventRequestForIndustrialModel,
    ) -> None:
        """
        Handle the search functionality for source events based on the search term and properties specified in the request.

        This method searches for source events in the data model by applying a full-text query over the specified
        search properties. It filters results based on the search term provided in the request and maps the matching
        source events to their external IDs, which are then assigned to the request's `external_ids` attribute.

        Args:
            request (GetSourceEventRequest):
                The request object containing the search term, search properties, and any additional filters
                to be applied during the search.

        Returns:
            None: The method directly updates the `request.external_ids` attribute.

        """
        source_events = self._cognite_client.data_modeling.instances.search(
            view=self._source_event_view,
            query=request.search or "",
            properties=request.search_properties,
            filter=data_modeling.filters.And(
                *get_source_event_filters_for_aggregate(
                    request,
                    self._source_event_view,
                ),
            ),
            limit=SEARCH_LIMIT,
        )

        normalized_search_term = request.search.lower() if request.search else ""
        matching_source_event_ids: list[str] = []
        matching_source_event_ids = [
            source_event["externalId"]
            for source_event in CogniteDependencyMapper.map(source_events)
            if any(
                str(source_event[event_property]).lower()
                for event_property in request.search_properties
                if event_property in source_event
                and isinstance(source_event[event_property], str)
                and normalized_search_term in str(source_event[event_property]).lower()
            )
        ]

        request.external_ids = matching_source_event_ids or ["-"]

    def _get_all_events(
        self,
        request: GetSourceEventRequest | GetSourceEventRequestForIndustrialModel,
    ) -> None:
        """Retrieve all source events based on the provided request and processes the results."""
        select_clause = self._build_select_clause()
        with_clause = self._build_with_clause(request)

        select_clause_dict = {
            "generalEvent": select_clause,
        }

        data = self._cognite_client.data_modeling.instances.query(
            data_modeling.query.Query(
                with_=with_clause,
                select=select_clause_dict,
            ),
        )

        self._map_source_event_by_impacted_unit_result(data, request)

    def _build_with_clause(
        self,
        request: GetSourceEventRequest | GetSourceEventRequestForIndustrialModel,
    ) -> dict[str, Any]:
        """Build the 'with' clause for the data query, specifying relationships between source events and impacted reporting units."""
        return {
            "sourceEventByImpactedUnits": data_modeling.query.EdgeResultSetExpression(
                filter=data_modeling.filters.And(
                    data_modeling.filters.Equals(
                        ["edge", "type"],
                        IMPACTED_REPORTING_UNITS_TO_SOURCE_EVENTS,
                    ),
                    data_modeling.filters.In(
                        ["edge", "endNode"],
                        request.impacted_reporting_units,
                    ),
                ),
                direction="inwards",
                max_distance=1,
                limit=LIMIT,
            ),
            "generalEvent": data_modeling.query.NodeResultSetExpression(
                from_="sourceEventByImpactedUnits",
                filter=data_modeling.filters.And(
                    *get_source_event_filters_for_aggregate(
                        request,
                        self._source_event_view,
                    ),
                ),
                limit=LIMIT,
            ),
        }

    def _build_select_clause(self) -> data_modeling.query.Select:
        """Build the select clause for the data query, specifying the properties to be retrieved from the source events."""
        return data_modeling.query.Select(
            sources=[
                data_modeling.query.SourceSelector(
                    source=self._source_event_view,
                    properties=["title"],
                ),
            ],
        )

    def _map_source_event_by_impacted_unit_result(
        self,
        data: data_modeling.query.QueryResult,
        request: GetSourceEventRequest | GetSourceEventRequestForIndustrialModel,
    ) -> None:
        """Process the query result and updates the external_ids in the request based on the retrieved source events."""
        node_ids = [
            node.external_id
            for node in Node.from_node_list(data.get_nodes("generalEvent"))
        ]

        if not request.external_ids:
            request.external_ids = node_ids
        else:
            request.external_ids = list(set(node_ids) & set(request.external_ids))

    def get_source_events_title(self, request: GetSourceEventRequest) -> list[str]:
        """Retrieve the unique titles of the source events that match the search query."""
        source_events = self._cognite_client.data_modeling.instances.search(
            view=self._source_event_view,
            query=request.search or "",
            properties=request.search_properties,
            filter=data_modeling.filters.And(
                *get_source_event_filters_for_aggregate(
                    request,
                    self._source_event_view,
                ),
            ),
            limit=SEARCH_LIMIT,
        )

        normalized_search_term = request.search.lower() if request.search else ""
        matching_source_event_titles = {
            event["title"]
            for event in CogniteDependencyMapper.map(source_events)
            if normalized_search_term in str(event["title"]).lower()
        }

        return list(matching_source_event_titles)

    async def get_source_events_from_industrial_model(
        self,
        request: GetSourceEventRequestForIndustrialModel,
    ) -> list[SourceEventExportView]:
        """Retrieve source events for the industrial model based on the provided request parameters."""
        if request.search:
            self._handle_search(request)

        if request.impacted_reporting_units:
            self._get_all_events(request)

        query = request.to_statement(SourceEventExportView).sort(
            request.sort_by,
            request.direction,
        )

        query.where(get_source_event_filters_for_industrial_model(request))
        query.limit(LIMIT)

        return await self._engine.query_all_pages_async(
            query,
            validation_mode="ignoreOnError",
        )
