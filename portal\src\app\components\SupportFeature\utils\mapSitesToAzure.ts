export const filterAvailableAzureSites = (
    azureSites: string[],
    selectedSites: string[] | undefined,
    siteAccessed: string
): string => {
    if (!selectedSites || selectedSites.length === 0 || selectedSites.length > 1) {
        return (
            azureSites.find((site) => {
                const firstWord = siteAccessed?.split(' ')[0]
                return firstWord && site.includes(firstWord)
            }) || 'Global'
        )
    }

    const firstWord = selectedSites[0]?.split(' ')[0]

    return (
        azureSites.find((site) => {
            return firstWord && site.includes(firstWord)
        }) || 'Global'
    )
}
