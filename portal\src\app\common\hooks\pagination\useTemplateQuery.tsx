import { useEffect, useState } from 'react'
import { useRetrieveResultsFunction } from '../general-functions/useRetrieveResultsFunction'
import { useQueryResultsFunction } from '../general-functions/useQueryResultsFunction'
import { useGetCurrentUserExternalId } from '../user-management/useGetCurrentUserExternalId'
import { EntityType, GetSpace } from '../../utils/space-util'
import { AllActionStatusExternalIdEnum } from '../../enums/ActionItemStatusEnum'
import { getLocalUserSite } from '@celanese/celanese-ui'

export interface TemplateQueryRequest {
    siteId?: string
}

const buildTemplateSelectQuery = (versionAction: string, versionTemplateConfiguration: string): any => {
    const spaceAIM = GetSpace(EntityType.Model)

    const query = {
        actionsTemplate: {
            sources: [
                {
                    source: {
                        type: 'view',
                        space: spaceAIM,
                        externalId: 'Action',
                        version: `${versionAction}`,
                    },
                    properties: ['templateConfiguration'],
                },
            ],
        },
        templateConfiguration: {
            sources: [
                {
                    source: {
                        type: 'view',
                        space: spaceAIM,
                        externalId: 'TemplateConfiguration',
                        version: `${versionTemplateConfiguration}`,
                    },
                    properties: ['name'],
                },
            ],
        },
    }

    return query
}

export const buildTemplateNamesQuery = (
    request: TemplateQueryRequest,
    spaceAIM: string,
    versionAction: string,
    versionTemplateConfiguration: string
) => {
    const siteCode = request.siteId?.slice(4) || getLocalUserSite()?.siteCode || '-'

    return {
        actionsTemplate: {
            limit: 10000,
            nodes: {
                filter: {
                    and: [
                        {
                            hasData: [
                                {
                                    type: 'view',
                                    space: 'AIM-COR-ALL-DMD',
                                    externalId: 'Action',
                                    version: `${versionAction}`,
                                },
                            ],
                        },
                        {
                            and: [
                                {
                                    equals: {
                                        property: ['node', 'space'],
                                        value: `${GetSpace(EntityType.Instance, siteCode)}`,
                                    },
                                },
                                {
                                    nested: {
                                        scope: [spaceAIM, `Action/${versionAction}`, 'currentStatus'],
                                        filter: {
                                            equals: {
                                                property: ['node', 'externalId'],
                                                value: AllActionStatusExternalIdEnum.Active,
                                            },
                                        },
                                    },
                                },
                                {
                                    nested: {
                                        scope: [spaceAIM, `Action/${versionAction}`, 'templateConfiguration'],
                                        filter: {
                                            exists: {
                                                property: ['node', 'externalId'],
                                            },
                                        },
                                    },
                                },
                            ],
                        },
                    ],
                },
            },
        },
        templateConfiguration: {
            limit: 10000,
            nodes: {
                from: 'actionsTemplate',
                through: {
                    source: {
                        type: 'view',
                        space: 'AIM-COR-ALL-DMD',
                        externalId: 'Action',
                        version: `${versionAction}`,
                    },
                    identifier: 'templateConfiguration',
                },
                direction: 'outwards',
                filter: {
                    and: [
                        {
                            hasData: [
                                {
                                    type: 'view',
                                    space: 'AIM-COR-ALL-DMD',
                                    externalId: 'TemplateConfiguration',
                                    version: `${versionTemplateConfiguration}`,
                                },
                            ],
                        },
                    ],
                },
            },
        },
    }
}

const buildTemplateQuery = (
    request: TemplateQueryRequest,
    versionAction: string,
    versionTemplateConfiguration: string
): any => {
    const spaceAIM = GetSpace(EntityType.Model)

    const query = buildTemplateNamesQuery(request, spaceAIM, versionAction, versionTemplateConfiguration)

    return query
}

export const useTemplateQuery = (request: TemplateQueryRequest) => {
    const [versionAction, setVersionAction] = useState<string>('')
    const [versionTemplateConfiguration, setVersionTemplateConfiguration] = useState<string>('')
    const [filter, setFilter] = useState<any>()
    const [resultData, setResultData] = useState<{ data: string[]; cursor: string; loading: boolean }>({
        data: [],
        cursor: '',
        loading: true,
    })

    const [select, setSelect] = useState<any>()

    const cursors = {
        templateConfiguration: null,
    }

    const activeUserExternalId = useGetCurrentUserExternalId()
    const { getAllResults: getAllData } = useQueryResultsFunction<any>(cursors)
    const { getAllResults: getAllViews } = useRetrieveResultsFunction()

    useEffect(() => {
        getAllViews().then((res) => {
            const actionVersion = res.find((v) => v.externalId === 'Action')?.version
            const templateVersion = res.find((v) => v.externalId === 'TemplateConfiguration')?.version

            setVersionAction(actionVersion ?? '0')
            setVersionTemplateConfiguration(templateVersion ?? '0')
        })
    }, [])

    useEffect(() => {
        if (activeUserExternalId && versionAction !== '' && versionTemplateConfiguration !== '') {
            setFilter(buildTemplateQuery(request, versionAction, versionTemplateConfiguration))
            setSelect(buildTemplateSelectQuery(versionAction, versionTemplateConfiguration))
        }
    }, [versionAction, activeUserExternalId])

    useEffect(() => {
        if (filter && select) {
            const spaceAIM = GetSpace(EntityType.Model)
            getAllData(filter, select).then((res) => {
                const nameTemplateList = res?.items?.templateConfiguration.map(
                    (x: any) => x.properties[spaceAIM][`TemplateConfiguration/${versionTemplateConfiguration}`]?.name
                )
                setResultData({
                    data: nameTemplateList,
                    cursor: res?.nextCursor?.templateConfiguration ?? '',
                    loading: false,
                })
            })
        }
    }, [filter, select])

    return {
        loading: resultData.loading,
        queryResult: resultData.data,
        cursor: resultData.cursor,
    }
}
