from datetime import UTC, datetime

from fastapi import Depends, HTTPException, status

from ..list_reporting_unit.client import ListReportingUnitClient
from .client import CreateAction<PERSON>lient
from .errors import ErrorMessage
from .models import CreateActionRequest, CreateActionResponse


class CreateActionService:
    """Represents a simplified version of a CreateActionService."""

    def __init__(
        self,
        create_action_client: CreateActionClient = Depends(CreateActionClient),
        list_reporting_unit_client: ListReportingUnitClient = Depends(
            ListReportingUnitClient,
        ),
    ) -> None:
        self._create_action_client = create_action_client
        self._list_reporting_unit_client = list_reporting_unit_client

    async def create(self, request: CreateActionRequest) -> CreateActionResponse:
        """Simulate an action item creation."""
        if request.due_date < datetime.now(UTC).date():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=ErrorMessage.DUE_DATE_IN_THE_PAST,
            )

        reporting_units_dict = (await self._list_reporting_unit_client.list()).as_dict()
        if request.reporting_unit_external_id not in reporting_units_dict:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=ErrorMessage.REPORTING_UNIT_DOES_NOT_EXIST,
            )

        return self._create_action_client.create(request)
