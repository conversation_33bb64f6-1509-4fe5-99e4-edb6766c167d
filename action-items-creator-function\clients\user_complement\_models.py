from clients.core.models import Node
from typing import Annotated

from clients.core.validators import edge_unwraper_validator, str_or_default_validator


class _Team(Node):
    reporting_site: Node | None


class _User(Node):
    first_name: Annotated[str, str_or_default_validator]
    last_name: Annotated[str, str_or_default_validator]
    email: Annotated[str, str_or_default_validator]
    active: bool
    teams: Annotated[list[_Team], edge_unwraper_validator]


class _UserAzureAttribute(Node):
    user: _User | None


class _Role(Node):
    site: Node | None


class _UserRoleSite(Node):
    role: _Role | None
