import { useCallback } from 'react'
import { ItemsFunctionPromise, useFdmQuery } from '../cognite/useFdmQuery'

export function useQueryResultsFunction<T>(cursors: T) {
    const [queryFunctionFdm] = useFdmQuery()
    return {
        getAllResults: useCallback(
            async (withQuery: T, select: T): Promise<ItemsFunctionPromise> => {
                const res = await queryFunctionFdm({ withQuery: withQuery, select: select, cursors: cursors })
                return res
            },
            [cursors]
        ),
    }
}
