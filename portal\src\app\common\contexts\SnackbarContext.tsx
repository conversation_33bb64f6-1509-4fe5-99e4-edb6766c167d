import { SnackbarOrigin } from '@mui/material/Snackbar'
import { createContext, useContext } from 'react'

export type Severity = 'success' | 'error' | 'info' | 'warning'

export type SnackbarContextType = {
    showSnackbar: (
        message: string,
        severity?: Severity,
        id?: string | null,
        duration?: number | null,
        position?: SnackbarOrigin
    ) => void
    handleCloseAlert: (event?: React.SyntheticEvent | Event, reason?: string) => void
}

export const SnackbarContext = createContext<SnackbarContextType | undefined>(undefined)

export const useSnackbar = () => {
    const context = useContext(SnackbarContext)
    if (!context) {
        throw new Error('useSnackbar must be used inside SnackbarProvider')
    }
    return context
}
