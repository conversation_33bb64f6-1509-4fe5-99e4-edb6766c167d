from app.core.client import BaseClient
from app.core.models import PaginatedInstanceResponse
from app.modules.example.list_reporting_unit.queries import LIST_REPORTING_UNIT_QUERY

from .models import ListReportingUnitResponse, ReportingUnitResponse


class ListReportingUnitClient(BaseClient):
    """Represents a simplified version of a ListReportingUnitClient."""

    async def list(self) -> ListReportingUnitResponse:
        """Return a list of all reporting units."""
        result = await self.query(
            query=LIST_REPORTING_UNIT_QUERY,
            variables={"pageSize": 1000, "after": None},
        )

        reporting_units = PaginatedInstanceResponse[
            ReportingUnitResponse
        ].from_graphql_response(result, 1000)

        return ListReportingUnitResponse(reporting_units.data)
