'use client'
import { Box, SxProps, Theme, Tooltip } from '@mui/material'
import { ReactNode, useMemo } from 'react'
import { ActionDetailItem, CategoryConfigurationData } from '@/app/common/models/action-detail'
import { ClnButton } from '@celanese/ui-lib'
import { ActionStatusExternalIdClearEnum } from '@/app/common/enums/ActionItemStatusEnum'
import {
    EXTEND_APPROVER_TAG,
    REASSIGNMENT_APPROVER_TAG,
    SITE_EXTERNAL_ID_REQUIRED_FIELD,
} from '@/app/common/utils/validate-codes'
import { useAuthGuard } from '@/app/common/hooks/useAuthGuard'
import dayjs from 'dayjs'
import { translate } from '@/app/common/utils/generate-translate'
import { UserRolesPermission } from '@celanese/celanese-sdk'

type Props = {
    actionItem?: ActionDetailItem
    activeUser?: UserRolesPermission
    isModal?: boolean
    categoryConfig?: CategoryConfigurationData
    handleOpenCloseModal: (value: boolean, modal: string) => void
    handleCancel: () => void
    buttonsBoxStyle?: SxProps<Theme>
    fullWidthButtons?: boolean
}

interface ActionItem {
    route: string
    hidden: boolean
    disabled: boolean
    variant: 'text' | 'outlined' | 'contained' | undefined
    label: string
    onClick: () => void
}

export function DetailsMenu(params: Props) {
    const { actionItem, activeUser, categoryConfig, handleOpenCloseModal, handleCancel } = params

    const { checkPermissionsFromComponents, checkPermissionsFromRoutes } = useAuthGuard()

    const permissionsExtendReassingment = useMemo(() => {
        if (activeUser?.externalId === '') return
        const permissionsExtend = checkPermissionsFromComponents(
            EXTEND_APPROVER_TAG,
            actionItem?.reportingSite?.externalId
        )
        const permissionsReassingment = checkPermissionsFromComponents(
            REASSIGNMENT_APPROVER_TAG,
            actionItem?.reportingSite?.externalId
        )

        return {
            extendApproverRoleBoolean: permissionsExtend.isAuthorized,
            reassignmentApproverRoleBoolean: permissionsReassingment.isAuthorized,
        }
    }, [activeUser?.externalId])

    const isMoreThan10Days = useMemo(() => {
        if (actionItem?.assignmentDate) {
            const assignmentDate = dayjs(actionItem.assignmentDate)
            const currentDate = dayjs()
            const daysDifference = currentDate.diff(assignmentDate, 'day')
            return daysDifference >= 10
        }
        return false
    }, [actionItem?.assignmentDate])

    const isAssignee: boolean = useMemo(() => {
        return activeUser?.externalId === actionItem?.assignedTo?.user?.externalId
    }, [activeUser, actionItem])

    const viewCompleteButton =
        isAssignee &&
        (actionItem?.currentStatus?.externalId === ActionStatusExternalIdClearEnum.Assigned ||
            actionItem?.currentStatus?.externalId === ActionStatusExternalIdClearEnum.VerificationRejected ||
            actionItem?.currentStatus?.externalId === ActionStatusExternalIdClearEnum.ApprovalRejected)

    const viewExtendOrReassignButton =
        isAssignee && actionItem?.currentStatus?.externalId === ActionStatusExternalIdClearEnum.Assigned

    const viewChallengeButton =
        isAssignee &&
        (actionItem?.currentStatus?.externalId === ActionStatusExternalIdClearEnum.Assigned ||
            actionItem?.currentStatus?.externalId === ActionStatusExternalIdClearEnum.ChallengePeriod)

    const viewApprovalButton: boolean = useMemo(() => {
        return (
            (actionItem?.currentStatus?.externalId === ActionStatusExternalIdClearEnum.PendingApproval &&
                activeUser?.externalId === actionItem.approver?.externalId) ||
            (actionItem?.currentStatus?.externalId === ActionStatusExternalIdClearEnum.PendingVerification &&
                activeUser?.externalId === actionItem.verifier?.externalId)
        )
    }, [activeUser, actionItem])

    const isDueDateExtensionApprover: boolean = useMemo(() => {
        const defaultExtensionApproverRole = categoryConfig?.defaultExtensionApproverRole
        if (defaultExtensionApproverRole)
            return (
                (activeUser?.roles?.some((role: any) => role.externalId === defaultExtensionApproverRole.externalId) ??
                    false) &&
                actionItem?.currentStatus?.externalId === ActionStatusExternalIdClearEnum.DueDateExtension
            )

        if (actionItem?.reportingSite?.externalId === SITE_EXTERNAL_ID_REQUIRED_FIELD) {
            const isUserInReportingUnit =
                activeUser?.units?.some((unit) => unit.unitCode === actionItem?.reportingUnit?.externalId) ?? false

            return (
                (permissionsExtendReassingment?.extendApproverRoleBoolean ?? false) &&
                actionItem?.currentStatus?.externalId === ActionStatusExternalIdClearEnum.DueDateExtension &&
                isUserInReportingUnit
            )
        }

        return (
            (permissionsExtendReassingment?.extendApproverRoleBoolean ?? false) &&
            actionItem?.currentStatus?.externalId === ActionStatusExternalIdClearEnum.DueDateExtension
        )
    }, [permissionsExtendReassingment, activeUser, actionItem, categoryConfig])

    const isApproverReassign: boolean = useMemo(() => {
        if (actionItem?.reportingSite?.externalId === SITE_EXTERNAL_ID_REQUIRED_FIELD) {
            const isReportingUnitValid =
                activeUser?.units?.some((unit) => unit.unitCode === actionItem?.reportingUnit?.externalId) ?? false

            return (
                (permissionsExtendReassingment?.reassignmentApproverRoleBoolean ?? false) &&
                actionItem?.currentStatus?.externalId === ActionStatusExternalIdClearEnum.ReassignmentPeriod &&
                isReportingUnitValid
            )
        }

        return (
            (permissionsExtendReassingment?.reassignmentApproverRoleBoolean ?? false) &&
            actionItem?.currentStatus?.externalId === ActionStatusExternalIdClearEnum.ReassignmentPeriod
        )
    }, [permissionsExtendReassingment])

    const isApproverChallenge: boolean = useMemo(() => {
        return (
            activeUser?.externalId === actionItem?.owner?.user?.externalId &&
            actionItem?.currentStatus?.externalId === ActionStatusExternalIdClearEnum.ChallengePeriod
        )
    }, [activeUser])

    const actions: ActionItem[] = [
        {
            route: '/action-item-detail-extension',
            hidden: !viewExtendOrReassignButton || params.isModal,
            disabled:
                actionItem?.viewOnly ||
                (categoryConfig?.isExtensionsAllowed !== undefined ? !categoryConfig?.isExtensionsAllowed : false),
            variant: 'outlined',
            label: translate('details.buttons.extend'),
            onClick: () => {
                handleOpenCloseModal(true, ActionStatusExternalIdClearEnum.DueDateExtension)
            },
        },
        {
            route: '/action-item-detail-reassignment',
            hidden: !viewExtendOrReassignButton || params.isModal,
            disabled: actionItem?.viewOnly ?? false,
            variant: 'outlined',
            label: translate('details.buttons.reassign'),
            onClick: () => {
                handleOpenCloseModal(true, ActionStatusExternalIdClearEnum.ReassignmentPeriod)
            },
        },
        {
            route: '/action-item-detail-challenge',
            hidden: !viewChallengeButton || isApproverChallenge || params.isModal,
            disabled:
                actionItem?.viewOnly ||
                (isMoreThan10Days &&
                    actionItem?.currentStatus?.externalId !== ActionStatusExternalIdClearEnum.ChallengePeriod),
            variant: 'outlined',
            label: translate('details.buttons.challenge'),
            onClick: () => {
                handleOpenCloseModal(true, ActionStatusExternalIdClearEnum.ChallengePeriod)
            },
        },
        {
            route: '/action-item-detail-complete',
            hidden: !viewCompleteButton || params.isModal,
            disabled: actionItem?.viewOnly,
            variant: 'contained',
            label: translate('details.buttons.complete'),
            onClick: () => {
                handleOpenCloseModal(true, ActionStatusExternalIdClearEnum.Completed)
            },
        },
        {
            route: '/action-item-detail-assignee-request',
            hidden: !(isApproverReassign || isDueDateExtensionApprover || isApproverChallenge) || params.isModal,
            disabled: actionItem?.viewOnly ?? false,
            variant: 'contained',
            label: translate('details.buttons.answerRequest'),
            onClick: () => {
                handleOpenCloseModal(
                    true,
                    actionItem?.currentStatus?.externalId ?? ActionStatusExternalIdClearEnum.ReassignmentPeriod
                )
            },
        },
    ].filter((item) => checkPermissionsFromRoutes(item.route)) as ActionItem[]

    const generateActionButtons = (actions: ActionItem[], actionItem: ActionDetailItem | undefined): ReactNode => {
        return (
            <>
                <ClnButton
                    size="medium"
                    variant="text"
                    label={params.isModal ? translate('requestModal.close') : translate('details.buttons.cancel')}
                    onClick={handleCancel}
                />
                {actions.map((action) => {
                    return (
                        <Box key={action.route} hidden={action.hidden}>
                            {actionItem?.viewOnly && (
                                <Tooltip title={translate('details.fields.viewOnlyTooltip')}>
                                    <span>
                                        <ClnButton
                                            size="medium"
                                            variant={action.variant}
                                            label={action.label}
                                            disabled={action.disabled}
                                            onClick={action.onClick}
                                        />
                                    </span>
                                </Tooltip>
                            )}
                            {!actionItem?.viewOnly && (
                                <ClnButton
                                    size="medium"
                                    variant={action.variant}
                                    label={action.label}
                                    disabled={action.disabled}
                                    onClick={action.onClick}
                                    fullWidth={params.fullWidthButtons}
                                />
                            )}
                        </Box>
                    )
                })}
                {viewApprovalButton && !params.isModal && (
                    <ClnButton
                        size="medium"
                        variant="contained"
                        label={translate('details.buttons.answerRequest')}
                        onClick={() => {
                            handleOpenCloseModal(true, ActionStatusExternalIdClearEnum.PendingApproval)
                        }}
                    />
                )}
            </>
        )
    }

    return (
        <Box id="action-details-menu" paddingTop={2}>
            <Box className="flex-button" id="action-details-buttons" sx={params.buttonsBoxStyle}>
                {generateActionButtons(actions, actionItem)}
            </Box>
        </Box>
    )
}

export default DetailsMenu
