# Action Item Management API

This project is an api written in python using FastAPI. To set up the local environment and run the api, one can follow the steps below.

All commands listed here follows the assumption that one is already inside the `api/` folder.

## Requirements

- Python 3.11

## Environment Setup

1. Run the following command to create a virtual environment named `.venv`:

   ```shell
   python -m venv .venv
   or
   python3.11 -m venv .venv
   ```

3. Run the following command to change the directory to the Scripts folder inside the virtual environment:

   ```shell
   cd .\.venv\Scripts\
   ```

4. Run the following command to activate the virtual environment:

   ```shell
   .\activate
   ```

5. Go back to the project directory:

   ```shell
   cd ..\..
   ```

6. Run the following command to install the packages listed in the requirements file:

   ```shell
   pip install --no-cache-dir -r requirements.txt requirements-dev.txt
   ```

7. Create a copy of .env.example, naming it .env.

8. Update the newly created `.env` to ensure it includes the following settings:

   ```
   AUTH_CLIENT_ID, AUTH_SECRET
   ```

## Running the API

1. Activate the virtual environment created in [environment setup](#environment-setup).
2. Start the api:
    ```shell
   uvicorn app.main:app --reload
    ```
