from pydantic import computed_field

from clients.core.models import BaseCamelCaseModel
from utils.space_utils import get_code_from_reporting_site_external_id


class GetReportingLocationsRequest(BaseCamelCaseModel):
    """
    Represent a request for retrieving reporting locations based on a reporting site external ID
    and an optional description.

    Attributes:
        reporting_site_external_id (str): The external ID of the reporting site.
        description (str | None): Optional description for filtering the reporting locations.

    """

    reporting_site_external_id: str
    description: str | None = None

    @computed_field
    @property
    def site_code(self) -> str:
        """
        Extract the site code from the reporting site external ID by splitting the ID string at the dash (-).

        Returns:
            str: The extracted site code.

        """
        return get_code_from_reporting_site_external_id(self.reporting_site_external_id)


class GetReportingLocationsBySitesRequest(BaseCamelCaseModel):
    """
    Represent a request for retrieving reporting locations based on multiple reporting site external IDs,
    optional reporting units external IDs, search query, and search properties.

    Attributes:
        reporting_site_external_id (list[str]): A list of external IDs of the reporting sites.
        reporting_units_external_id (list[str] | None): Optional list of external IDs of reporting units.
        search (str | None): Optional search query to filter results.
        search_properties (list[str]): List of properties to search by, default is ["name", "description"].

    """

    reporting_site_external_ids: list[str]
    reporting_unit_external_ids: list[str] | None = None

    search: str | None = None
    search_properties: list[str] = ["name", "description"]

    @computed_field
    @property
    def site_codes(self) -> list[str]:
        """
        Extract the site codes from the list of reporting site external IDs by splitting each ID string at the dash (-).

        Returns:
            list[str]: A list of extracted site codes.

        """
        return [
            get_code_from_reporting_site_external_id(site_id)
            for site_id in self.reporting_site_external_ids
        ]
