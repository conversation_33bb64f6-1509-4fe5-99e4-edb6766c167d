import { green, cyan } from '@mui/material/colors'

export const styles = {
    container: {
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        width: '100%',
        height: {
            sm: '100%',
            md: 'calc(100vh - 140px)',
        },
        padding: '1rem 0',
        boxSizing: 'border-box',
    },
    iconWrapper: {
        position: 'relative',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        marginBottom: {
            sm: '1rem',
            md: '1rem',
            lg: '2rem',
        },
    },
    checkIcon: {
        fontSize: {
            xs: '80px',
            sm: '100px',
            md: '160px',
            lg: '220px',
            xl: '260px',
        },
        color: green[800],
        marginBottom: '1.5rem',
    },
    shadow: {
        position: 'absolute',
        bottom: '5px',
        width: '60%',
        height: '20px',
        backgroundColor: 'rgba(0, 0, 0, 0.2)',
        borderRadius: '50%',
        filter: 'blur(0px)',
    },
    message: {
        fontSize: {
            sm: '0.8rem',
            md: '1.1rem',
            lg: '1.2rem',
        },
        margin: '0 1rem',
        textAlign: 'center',
        wordWrap: 'break-word',
    },
    linkWrapper: {
        marginBottom: {
            sm: '2rem',
        },
    },
    newRequestLink: {
        cursor: 'pointer',
        textDecoration: 'underline',
        color: cyan[500],
        fontSize: {
            sm: '0.8rem',
            md: '1.1rem',
            lg: '1.2rem',
        },
    },
    messageEnd: {
        fontSize: {
            sm: '0.8rem',
            md: '1.1rem',
            lg: '1.2rem',
        },
    },
}
