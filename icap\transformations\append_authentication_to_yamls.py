from os.path import join, abspath, dirname
from pathlib import Path
from os import listdir

AUTHENTICATION_BLOCK = """
authentication:
  clientId: ${TRANSFORMATIONS_CLIENT_ID}
  clientSecret: ${TRANSFORMATIONS_CLIENT_SECRET}
  tokenUrl: ${TRANSFORMATIONS_TOKEN_URL}
  cdfProjectName: ${TRANSFORMATIONS_PROJECT}
  scopes:
   - ${TRANSFORMATIONS_SCOPES}
"""


def get_file_paths(root_path: str | Path) -> list[Path]:
    return [Path(join(root_path, file_name)) for file_name in listdir(root_path)]


def get_current_dir_path() -> Path:
    return Path(dirname(abspath(__file__)))


def main():
    for file_path in get_file_paths(get_current_dir_path()):
        if file_path.suffix not in (".yml", ".yaml"):
            continue

        target_path = file_path

        if len(file_path.name.split(" ")) > 1:
            file_name = file_path.name
            target_path = Path(file_path.parent).joinpath(
                file_name.split(" ")[0] + "." + file_name.split(".")[-1]
            )
            file_path.rename(target_path)

        with open(target_path, "a") as file:
            file.write(AUTHENTICATION_BLOCK)


if __name__ == "__main__":
    main()
