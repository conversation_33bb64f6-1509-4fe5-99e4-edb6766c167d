import { useCallback } from 'react'
import { useFdmRetrieveDatamodel } from '../cognite/useFdmRetrieveDatamodel'
import { CogniteViewsResponseItem } from '../../models/common'

export function useRetrieveResultsFunction() {
    const [retrieveFunctionFdm] = useFdmRetrieveDatamodel()
    return {
        getAllResults: useCallback(async (): Promise<CogniteViewsResponseItem[]> => {
            const res = await retrieveFunctionFdm()
            return res
        }, []),
    }
}
