# Manifest file downloaded from fusion
externalId: tr-AIM-COR-ALL-USRAZRATR
name: AIM-COR-ALL-USRAZRATR
query: |-
  SELECT
      *
  FROM
      (
          SELECT
              concat("UserAzureAttribute_", lower(trim(key))) as externalId,
              "UMG-COR-ALL-DAT" as space,
              "Celanese" as company<PERSON><PERSON>,
              node_reference("UMG-COR-ALL-DAT", lower(trim(key))) as user,
              uuid() as azureUserId
          FROM
              `AIM-COR`.`ICAP-MAP-NewUser`
      )
  WHERE
      externalId not in (
          SELECT
              externalId
          FROM
              cdf_data_models(
                  'UMG-COR-ALL-DMD',
                  'UserManagementDOM',
                  '8_7_7',
                  'UserAzureAttribute'
              )
      )
destination:
  dataModel:
    space: UMG-COR-ALL-DMD
    externalId: UserManagementDOM
    version: "8_7_5"
    destinationType: UserAzureAttribute
  type: instances
ignoreNullFields: true
shared: true
action: upsert
dataSetExternalId: AIM-COR-ALL-DAT
# Specify credentials separately like this:
# authentication:
#   read:
#     clientId: ${READ_CLIENT_ID}
#     clientSecret: ${READ_CLIENT_SECRET}
#     tokenUrl: ${READ_TOKEN_URL}
#     cdfProjectName: ${READ_CDF_PROJECT_NAME}
#     # Optional: If idP requires providing the scopes
#     scopes:
#       - ${READ_SCOPES}
#     # Optional: If idP requires providing the audience
#     audience: ${READ_CDF_AUDIENCE}
#   write:
#     clientId: ${WRITE_CLIENT_ID}
#     clientSecret: ${WRITE_CLIENT_SECRET}
#     tokenUrl: ${WRITE_TOKEN_URL}
#     cdfProjectName: ${WRITE_CDF_PROJECT_NAME}
#     # Optional: If idP requires providing the scopes
#     scopes:
#       - ${WRITE_SCOPES}
#     # Optional: If idP requires providing the audience
#     audience: ${WRITE_CDF_AUDIENCE}
# Or together like this:
# authentication:
#   clientId: ${CLIENT_ID}
#   clientSecret: ${CLIENT_SECRET}
#   tokenUrl: ${TOKEN_URL}
#   # Optional: If idP requires providing the scopes
#   cdfProjectName: ${CDF_PROJECT_NAME}
#   scopes:
#    - ${SCOPES}
#   # Optional: If idP requires providing the audience
#   audience: ${AUDIENCE}