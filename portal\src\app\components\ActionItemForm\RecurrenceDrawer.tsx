import {
    Box,
    Checkbox,
    FormControl,
    FormControlLabel,
    Grid,
    InputAdornment,
    InputLabel,
    OutlinedInput,
    Typography,
} from '@mui/material'
import { z } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'
import { useForm } from 'react-hook-form'
import { translate } from '@/app/common/utils/generate-translate'
import { ClnButton, MatIcon } from '@celanese/ui-lib'
import dayjs, { Dayjs } from 'dayjs'
import isSameOrAfter from 'dayjs/plugin/isSameOrAfter'
import { useMemo, useState } from 'react'
import { GenericDatePicker } from '../FieldsComponent/GenericDatePicker'
import GenericAutocomplete from '../FieldsComponent/GenericAutocomplete'
import { transformStringOptions } from '@/app/common/utils/transform-options-for-filter'
import { RecurrenceForm } from '@/app/common/models/forms/recurrence-form'
import { DataGridTable } from '../PaginatedTable/DataGridTable'
import { GridColDef, GridRowsProp } from '@mui/x-data-grid-pro'
import { RecurrenceSubTypeEnum } from '@/app/common/enums/RecurrenceSubTypeEnum'
import { WeekdayEnum } from '@/app/common/enums/WeekdayEnum'
import { RecurrenceTypeEnum } from '@/app/common/enums/RecurrenceTypeEnum'
import { MonthEnum } from '@/app/common/enums/MonthEnum'
import {
    monthStringToNumber,
    removeUndefinedValues,
    transformDataToFormInput,
} from '@/app/common/utils/transform-input'
import { CustomDrawer } from '../ModalComponent/Drawer/Drawer'
import { drawerStyles } from '@/app/components/ModalComponent/Drawer/styles'

import * as S from './styles'

dayjs.extend(isSameOrAfter)

const zodDate = z.instanceof(dayjs as unknown as typeof Dayjs).nullable()

const formCustomSchema = z
    .object({
        startDate: zodDate,
        endDate: zodDate.optional(),
        noEndDate: z.boolean(),
        selectCustomDate: zodDate.optional(),
        customDates: zodDate.array().optional(),
    })
    .superRefine((data, ctx) => {
        const { selectCustomDate, startDate, endDate, noEndDate, customDates } = data
        if (!noEndDate && !endDate) {
            ctx.addIssue({
                path: ['endDate'],
                message: 'stepper.form.assignment.customPickerDayOutOfRangeError',
                code: z.ZodIssueCode.custom,
            })
        }

        if (!selectCustomDate || (startDate && selectCustomDate < startDate)) {
            ctx.addIssue({
                path: ['selectCustomDate'],
                message: 'stepper.form.assignment.customPickerDayOutOfRangeError',
                code: z.ZodIssueCode.custom,
            })
        }

        if (!noEndDate && endDate && selectCustomDate && selectCustomDate > endDate) {
            ctx.addIssue({
                path: ['selectCustomDate'],
                message: 'stepper.form.assignment.customPickerDayOutOfRangeError',
                code: z.ZodIssueCode.custom,
            })
        }

        if (
            Array.isArray(customDates) &&
            customDates.some((date) => dayjs(date).isSame(dayjs(selectCustomDate).startOf('day'), 'day'))
        ) {
            ctx.addIssue({
                path: ['selectCustomDate'],
                message: 'stepper.form.assignment.customPickerSameDayError',
                code: z.ZodIssueCode.custom,
            })
        }
    })

const createFormSchema = () =>
    z
        .object({
            startDate: zodDate,
            endDate: zodDate.optional(),
            noEndDate: z.boolean(),
            recurrenceType: z.string().min(1),
            weekDays: z.number().array().optional(),
            months: z.number().array().optional(),
            quarters: z.number().array().optional(),
            dayOfTheMonth: z.number().optional(),
            monthOfTheYear: z.string().optional(),
            recurrenceSubType: z.number().array().optional(),
            customDates: zodDate.array().optional(),
        })
        .superRefine((data, ctx) => {
            if (!data.noEndDate) {
                if (!data.endDate || (data.startDate && dayjs(data.endDate).isBefore(dayjs(data.startDate)))) {
                    ctx.addIssue({
                        path: ['endDate'],
                        code: z.ZodIssueCode.custom,
                    })
                }
            }

            if (data.recurrenceType === RecurrenceTypeEnum.Weekly && (!data.weekDays || data.weekDays.length === 0)) {
                ctx.addIssue({
                    path: ['weekDays'],
                    message: 'stepper.form.assignment.customPickerSameDayError',
                    code: z.ZodIssueCode.custom,
                })
            }

            if (
                data.recurrenceType === RecurrenceTypeEnum.Quarterly &&
                (!data.quarters || data.quarters.length === 0)
            ) {
                ctx.addIssue({
                    path: ['quarters'],
                    message: 'stepper.form.assignment.customPickerSameDayError',
                    code: z.ZodIssueCode.custom,
                })
            }

            if (data.recurrenceType === RecurrenceTypeEnum.Monthly) {
                if (
                    data.dayOfTheMonth === undefined ||
                    data.dayOfTheMonth === null ||
                    data.dayOfTheMonth < 1 ||
                    data.dayOfTheMonth > 31
                ) {
                    ctx.addIssue({
                        path: ['dayOfTheMonth'],
                        message: 'stepper.form.assignment.monthPickerErrorMessage',
                        code: z.ZodIssueCode.custom,
                    })
                }

                if (!data.months || data.months.length === 0) {
                    ctx.addIssue({
                        path: ['months'],
                        message: 'stepper.form.assignment.monthPickerErrorMessage',
                        code: z.ZodIssueCode.custom,
                    })
                }
            }

            if (data.recurrenceType === RecurrenceTypeEnum.Yearly) {
                if (
                    data.dayOfTheMonth === undefined ||
                    data.dayOfTheMonth === null ||
                    data.dayOfTheMonth < 1 ||
                    data.dayOfTheMonth > 31
                ) {
                    ctx.addIssue({
                        path: ['dayOfTheMonth'],
                        message: 'stepper.form.assignment.monthPickerErrorMessage',
                        code: z.ZodIssueCode.custom,
                    })
                }
                if (data.monthOfTheYear === undefined || data.monthOfTheYear === null) {
                    ctx.addIssue({
                        path: ['monthOfTheYear'],
                        message: 'stepper.form.assignment.yearlyPickerErrorMessage',
                        code: z.ZodIssueCode.custom,
                    })
                }
                if (!data.recurrenceSubType) {
                    ctx.addIssue({
                        path: ['recurrenceSubType'],
                        message: 'stepper.form.assignment.yearlyPickerErrorMessage',
                        code: z.ZodIssueCode.custom,
                    })
                }
            }

            if (data.recurrenceType === RecurrenceTypeEnum.Custom) {
                if (!data.customDates || data.customDates.length === 0) {
                    ctx.addIssue({
                        path: ['customDates'],
                        message: 'stepper.form.assignment.customPickerDayOutOfRangeError',
                        code: z.ZodIssueCode.custom,
                    })
                }
            }
        })

type RecurrenceFormSchema = z.infer<ReturnType<typeof createFormSchema>>
type CustomFormSchema = z.infer<typeof formCustomSchema>

type RecurrenceFormProps = {
    recurrenceSavedValue?: RecurrenceForm
    setRecurrenceForm: (value: RecurrenceForm) => void
}

export function RecurrenceDrawer({ recurrenceSavedValue, setRecurrenceForm }: RecurrenceFormProps) {
    const [openDrawer, setOpenDrawer] = useState<boolean>(false)
    const [saveForms, setSaveForms] = useState<boolean>(!!recurrenceSavedValue)

    const weekdaysOptions = Object.keys(WeekdayEnum).filter((key) => isNaN(Number(key)))
    const monthsOptions = Object.keys(MonthEnum).filter((key) => isNaN(Number(key)))
    const quartersOptions = [1, 2, 3, 4]
    const yearlyOptions = Object.keys(RecurrenceSubTypeEnum).filter((key) => isNaN(Number(key)))

    const formSchema = createFormSchema()
    const {
        control,
        setValue,
        getValues,
        trigger,
        reset,
        watch,
        clearErrors,
        formState: { errors },
    } = useForm<RecurrenceFormSchema>({
        resolver: zodResolver(formSchema),
        defaultValues: {
            startDate: recurrenceSavedValue?.startDate
                ? dayjs(recurrenceSavedValue.startDate).startOf('day')
                : dayjs().startOf('day'),
            endDate: recurrenceSavedValue?.endDate
                ? dayjs(recurrenceSavedValue.endDate).startOf('day')
                : dayjs().add(1, 'day').startOf('day'),
            noEndDate: recurrenceSavedValue?.noEndDate ?? false,
            recurrenceType: recurrenceSavedValue?.recurrenceType ?? RecurrenceTypeEnum.Daily,
            weekDays: recurrenceSavedValue?.weekDays ?? undefined,
            months: recurrenceSavedValue?.months ?? undefined,
            quarters: recurrenceSavedValue?.quarters ?? undefined,
            dayOfTheMonth: recurrenceSavedValue?.dayOfTheMonth ?? undefined,
            monthOfTheYear: recurrenceSavedValue?.monthOfTheYear
                ? Array.from(Object.values(MonthEnum))[recurrenceSavedValue?.monthOfTheYear - 1]
                : undefined,
            recurrenceSubType: recurrenceSavedValue?.recurrenceSubType
                ? [Object.values(RecurrenceSubTypeEnum).indexOf(recurrenceSavedValue?.recurrenceSubType) + 1]
                : undefined,
            customDates: recurrenceSavedValue?.customDates ?? undefined,
        },
    })

    const {
        control: controlCustom,
        setValue: setValueCustom,
        trigger: triggerCustom,
        reset: resetCustom,
        watch: watchCustom,
        formState: { errors: errorsCustom },
    } = useForm<CustomFormSchema>({
        resolver: zodResolver(formCustomSchema),
        defaultValues: {
            startDate: recurrenceSavedValue?.startDate
                ? dayjs(recurrenceSavedValue.startDate).startOf('day')
                : dayjs().startOf('day'),
            noEndDate: false,
        },
    })

    const {
        startDate,
        endDate,
        noEndDate,
        recurrenceType,
        weekDays,
        months,
        quarters,
        recurrenceSubType,
        customDates,
    } = watch()

    const { selectCustomDate } = watchCustom()

    const headCells: GridColDef[] = useMemo(() => {
        return [
            {
                field: 'date',
                label: translate('table.headers.date'),
                sortable: false,
                filterable: false,
                flex: 1,
                renderCell: (params) => (
                    <Box
                        sx={{
                            display: 'flex',
                            width: '100%',
                            height: '100%',
                            justifyContent: 'center',
                        }}
                        data-test="custum_dates-date_content"
                        data-origin="aim"
                    >
                        {params.row.date}
                    </Box>
                ),
                renderHeader: () => (
                    <span data-test="custum_dates-date_table" data-origin="aim">
                        {translate('table.headers.date')}
                    </span>
                ),
            },
            {
                field: 'action',
                headerName: translate('stepper.form.assignment.actions'),
                sortable: false,
                filterable: false,
                flex: 0.3,
                renderCell: (params) => {
                    return (
                        <Box
                            sx={{
                                display: 'flex',
                                width: '100%',
                                height: '100%',
                                alignItems: 'center',
                                justifyContent: 'center',
                            }}
                            data-test="custum_dates-actions_content"
                            data-origin="aim"
                        >
                            <MatIcon
                                icon="delete"
                                color="primary.main"
                                onClick={() => removeCustomDate(params.row.date)}
                                sx={{
                                    cursor: 'pointer',
                                }}
                            />
                        </Box>
                    )
                },
                renderHeader: () => (
                    <span data-test="custum_dates-actions_table" data-origin="aim">
                        {translate('stepper.form.assignment.actions')}
                    </span>
                ),
            },
        ]
    }, [translate, customDates])

    const tableRows: GridRowsProp = useMemo(() => {
        function convertActionItemDataToRows(items: (Dayjs | null)[]) {
            const sortedDates = [...items].sort((a, b) => {
                const changedAtA = dayjs(a)
                const changedAtB = dayjs(b)
                return changedAtA.diff(changedAtB)
            })

            return sortedDates.map((item, index) => {
                return {
                    id: `${index}`,
                    date: `${dayjs(item).format('MM/DD/YYYY')}`,
                }
            })
        }

        const datesRows = customDates && customDates.length > 0 ? convertActionItemDataToRows(customDates) : []

        return datesRows
    }, [customDates])

    const removeCustomDate = (date: string) => {
        const filterCustomDates = getValues('customDates')?.filter((d) => !dayjs(d).isSame(dayjs(date), 'day'))

        setValue('customDates', filterCustomDates)
        setValueCustom('customDates', filterCustomDates)
    }

    const handleClose = () => {
        reset()
        resetCustom()
        setOpenDrawer(false)
    }

    const addCustomDate = async () => {
        const isValid = await triggerCustom()
        if (!isValid || !selectCustomDate) return

        setValue('customDates', customDates ? [...customDates, selectCustomDate] : [selectCustomDate])
        setValueCustom('customDates', customDates ? [...customDates, selectCustomDate] : [selectCustomDate])
        setValueCustom('selectCustomDate', undefined)
    }

    const submit = async () => {
        const isValid = await trigger()
        if (!isValid) return

        const data = getValues()

        const transformedData: RecurrenceForm = {
            startDate: transformDataToFormInput(data.startDate),
            endDate: transformDataToFormInput(data.endDate),
            noEndDate: transformDataToFormInput(data.noEndDate),
            recurrenceType: transformDataToFormInput(data.recurrenceType),
            weekDays: transformDataToFormInput(data.weekDays),
            months: transformDataToFormInput(data.months),
            quarters: transformDataToFormInput(data.quarters),
            dayOfTheMonth: transformDataToFormInput(data.dayOfTheMonth),
            monthOfTheYear: monthStringToNumber(data.monthOfTheYear),
            recurrenceSubType: transformDataToFormInput(
                data.recurrenceSubType !== undefined && data.recurrenceSubType.length > 0
                    ? RecurrenceSubTypeEnum[
                          Object.keys(RecurrenceSubTypeEnum)[
                              data.recurrenceSubType[0] - 1
                          ] as keyof typeof RecurrenceSubTypeEnum
                      ]
                    : data.recurrenceSubType
            ),
            customDates: transformDataToFormInput(data.customDates),
        }
        const cleanedData = removeUndefinedValues(transformedData) as RecurrenceForm
        setRecurrenceForm(cleanedData)
        setSaveForms(true)
        setOpenDrawer(false)
    }

    function getRecurrenceLabel() {
        if (!recurrenceType) return ''

        const firstLetterUppercasedType = recurrenceType.charAt(0).toUpperCase() + recurrenceType.slice(1)
        return translate(`stepper.form.assignment.recurrenceLabel${firstLetterUppercasedType}`)
    }

    const handleKeyPress = (event: React.KeyboardEvent<HTMLInputElement>) => {
        const regex = /^[0-9\b]+$/
        if (!regex.test(event.key) && event.key !== 'Backspace') {
            event.preventDefault()
        }
    }

    const renderSelectableItems = (
        fieldName: 'weekDays' | 'quarters' | 'months' | 'recurrenceSubType',
        options: string[] | number[],
        selectedItems: number[],
        labelPrefix?: string,
        selectedOne?: boolean
    ) => {
        return (
            <>
                {['months', 'recurrenceSubType'].includes(fieldName) ? (
                    <>
                        <Box sx={S.monthPicker}>
                            {options.map((option, index) => (
                                <S.MonthCircle
                                    key={option}
                                    className={selectedItems?.includes(index + 1) ? 'selected' : ''}
                                    onClick={() => {
                                        if (selectedOne) {
                                            setValue(fieldName, [index + 1])
                                            return
                                        }
                                        const newSelectedItems = getValues(fieldName)?.includes(index + 1)
                                            ? getValues(fieldName)?.filter((selectedItem) => selectedItem !== index + 1)
                                            : [...(getValues(fieldName) || []), index + 1]

                                        setValue(fieldName, newSelectedItems)
                                    }}
                                >
                                    <Typography variant="overline">
                                        {typeof option === 'number'
                                            ? option
                                            : translate(
                                                  `stepper.form.assignment.${labelPrefix}.${option.toLowerCase()}`
                                              )}
                                    </Typography>
                                </S.MonthCircle>
                            ))}
                        </Box>

                        <Box sx={{ marginTop: 3 }}>
                            <FormControl fullWidth sx={{ m: 1 }} size="small" error={Boolean(errors.dayOfTheMonth)}>
                                <InputLabel htmlFor="outlined-adornment-amount">
                                    {translate('stepper.form.assignment.dayOfMonth')}
                                </InputLabel>
                                <OutlinedInput
                                    id="outlined-adornment-amount"
                                    onKeyDown={handleKeyPress}
                                    onChange={(e) => setValue('dayOfTheMonth', Number(e.target.value))}
                                    value={getValues('dayOfTheMonth')}
                                    endAdornment={<InputAdornment position="end">th</InputAdornment>}
                                    label={translate('stepper.form.estimatedCost')}
                                />
                            </FormControl>
                        </Box>
                    </>
                ) : (
                    <Box sx={S.weekdayPicker}>
                        {options.map((option, index) => {
                            const value = typeof option === 'string' ? index + 1 : option
                            return (
                                <S.WeekdayCircle
                                    key={option}
                                    className={selectedItems?.includes(value) ? 'selected' : ''}
                                    onClick={() => {
                                        const newSelectedItems = getValues(fieldName)?.includes(value)
                                            ? getValues(fieldName)?.filter((selectedItem) => selectedItem !== value)
                                            : [...(getValues(fieldName) || []), value]

                                        setValue(fieldName, newSelectedItems)
                                    }}
                                >
                                    <Typography variant="overline">
                                        {typeof option === 'number'
                                            ? option
                                            : translate(
                                                  `stepper.form.assignment.${labelPrefix}.${option.toLowerCase()}`
                                              ).charAt(0)}
                                    </Typography>
                                </S.WeekdayCircle>
                            )
                        })}
                    </Box>
                )}
            </>
        )
    }

    const renderErrorMessage = (errors: Record<string, any>) => {
        const errorMessage =
            errors.weekDays?.message ||
            errors.months?.message ||
            errors.dayOfTheMonth?.message ||
            errors.quarters?.message ||
            errors.recurrenceSubType?.message ||
            errors.customDates?.message ||
            errors.selectCustomDate?.message

        return errorMessage ? (
            <Typography color="error.main" variant="overline">
                {translate(errorMessage)}
            </Typography>
        ) : null
    }

    const repeteForType = () => (
        <Box sx={{ width: '100%' }}>
            <Typography fontSize={16} marginTop={1} fontWeight={'bold'} color="grey[400]">
                {translate('stepper.form.assignment.drawerRepeat') + ':'}
            </Typography>

            {recurrenceType === RecurrenceTypeEnum.Weekly && (
                <>{renderSelectableItems('weekDays', weekdaysOptions, weekDays ?? [], 'weekdays')}</>
            )}

            {recurrenceType === RecurrenceTypeEnum.Monthly && (
                <>{renderSelectableItems('months', monthsOptions, months ?? [], 'months')}</>
            )}

            {recurrenceType === RecurrenceTypeEnum.Quarterly && (
                <>{renderSelectableItems('quarters', quartersOptions, quarters ?? [])}</>
            )}

            {recurrenceType === RecurrenceTypeEnum.Yearly && (
                <>
                    {renderSelectableItems(
                        'recurrenceSubType',
                        yearlyOptions,
                        recurrenceSubType ?? [],
                        'yearlyOptions',
                        true
                    )}
                    <Box sx={{ width: '100%', m: 1 }}>
                        <GenericAutocomplete
                            name="monthOfTheYear"
                            control={control}
                            options={
                                transformStringOptions(
                                    Array.from(Object.values(MonthEnum)),
                                    'stepper.form.assignment.months',
                                    true
                                ) ?? []
                            }
                            onChange={(newValue) => {
                                setValue('monthOfTheYear', newValue)
                            }}
                            size="small"
                            error={Boolean(errors.monthOfTheYear)}
                            helperText={
                                Boolean(errors.monthOfTheYear)
                                    ? translate('stepper.form.assignment.yearlyPickerErrorMessage')
                                    : ''
                            }
                            label={`${translate('stepper.form.assignment.monthOfYear')} *`}
                            multiple={false}
                            disableCloseOnSelect={false}
                            data-test="new_action_item_flow_3_recurrence_pattern-type_field"
                            data-origin="aim"
                        />
                    </Box>
                </>
            )}

            {recurrenceType === RecurrenceTypeEnum.Custom && (
                <>
                    <GenericDatePicker
                        control={controlCustom}
                        name="selectCustomDate"
                        minDate={dayjs(startDate || dayjs()).startOf('day')}
                        error={Boolean(errorsCustom.selectCustomDate)}
                        data-test="new_action_item_flow_3_recurrence_pattern-end_choose_date_button"
                        data-origin="ui-lib"
                    />
                    {renderErrorMessage(errorsCustom)}
                    <Box sx={{ width: '100%', display: 'flex', justifyContent: 'end', marginTop: 2 }}>
                        <ClnButton
                            variant="text"
                            size="small"
                            onClick={() => {
                                addCustomDate()
                            }}
                            label={translate('stepper.form.assignment.customPickerAddButton')}
                        />
                    </Box>
                    <DataGridTable
                        initialColumnDefs={headCells}
                        rows={tableRows}
                        maxHeight={tableRows.length > 0 ? 400 : 150}
                        showOptionsBar={false}
                        infinitePagination
                        paginationMode="server"
                        defaultMessageNoData={translate('common.noData')}
                    />
                </>
            )}

            {renderErrorMessage(errors)}
        </Box>
    )

    const renderForm = () => (
        <form id={'forms-recurrence'} style={{ ...drawerStyles.formContainer, gap: 0 }}>
            <Grid item xs={12} md={12} id={'date-fields-forms-recurrence'}>
                <Grid item md={12} id={'start-date-forms-recurrence'} sx={S.itemsForm}>
                    <GenericDatePicker
                        control={control}
                        name="startDate"
                        placement="left-start"
                        subHeader={translate('stepper.form.assignment.draweStartDate')}
                        error={Boolean(errors.startDate) || Boolean(errorsCustom.startDate)}
                        onChange={(date: dayjs.Dayjs | null) => {
                            setValue('startDate', date)
                            setValueCustom('startDate', date)
                        }}
                        minDate={dayjs(recurrenceSavedValue?.startDate || dayjs()).startOf('day')}
                        data-test="new_action_item_flow_3_recurrence_pattern-start_choose_date_button"
                        data-origin="ui-lib"
                    />
                </Grid>
                {!noEndDate && (
                    <Grid item md={12} id={'due-date-forms-recurrence'} sx={S.itemsForm}>
                        <Box sx={S.itemsDateColumnForm}>
                            <GenericDatePicker
                                control={control}
                                name="endDate"
                                placement="left-start"
                                subHeader={translate('stepper.form.assignment.drawerEndDate')}
                                minDate={dayjs(startDate || dayjs())
                                    .add(1, 'day')
                                    .startOf('day')}
                                error={Boolean(errors.endDate) || Boolean(errorsCustom.endDate)}
                                onChange={(date: dayjs.Dayjs | null) => {
                                    setValue('endDate', date)
                                    setValueCustom('endDate', date)
                                }}
                                data-test="new_action_item_flow_3_recurrence_pattern-end_choose_date_button"
                                data-origin="ui-lib"
                            />
                            {(Boolean(errors.endDate) || Boolean(errorsCustom.endDate)) && (
                                <Typography color="error.main" variant="overline">
                                    {translate('stepper.form.assignment.customPickerDayOutOfRangeError')}
                                </Typography>
                            )}
                        </Box>
                    </Grid>
                )}
                <Grid item md={12} id={'no-end-date-forms-recurrence'} sx={S.itemsForm}>
                    <FormControlLabel
                        control={
                            <Checkbox
                                checked={noEndDate}
                                onChange={(e) => {
                                    setValue('noEndDate', e.target.checked)
                                    setValueCustom('noEndDate', e.target.checked)
                                }}
                            />
                        }
                        label={translate('stepper.form.assignment.drawerNoEndDate')}
                        data-test="new_action_item_flow_3_recurrence_pattern-no_end_date_checkbox"
                        data-origin="aim"
                    />
                </Grid>
            </Grid>
            <Grid item xs={12} sx={S.itemsForm} id={'field-recurrence-type-forms-recurrence'}>
                <GenericAutocomplete
                    name="recurrenceType"
                    control={control}
                    options={
                        transformStringOptions(
                            Array.from(Object.values(RecurrenceTypeEnum)),
                            'stepper.form.assignment.recurrenceTypes',
                            true
                        ) ?? []
                    }
                    onChange={(newValue) => {
                        setValue('recurrenceType', newValue)
                        setValue('recurrenceSubType', undefined)
                        clearErrors()
                    }}
                    size="small"
                    error={Boolean(errors.recurrenceType)}
                    label={`${translate('stepper.form.assignment.draweRecurrenceType')} *`}
                    helperText={getRecurrenceLabel()}
                    multiple={false}
                    disableCloseOnSelect={false}
                    data-test="new_action_item_flow_3_recurrence_pattern-type_field"
                    data-origin="aim"
                />
            </Grid>
            {recurrenceType && recurrenceType !== RecurrenceTypeEnum.Daily && (
                <Grid item xs={12} sx={S.itemsForm} id={'field-repeat-forms-recurrence'}>
                    {repeteForType()}
                </Grid>
            )}
        </form>
    )

    return (
        <Box id={'recurrence'} width={'100%'}>
            <ClnButton
                variant="outlined"
                size="small"
                onClick={() => {
                    setOpenDrawer(true)
                }}
                label={translate(`stepper.form.assignment.${saveForms ? 'editRecurrence' : 'createRecurrence'}`)}
            />
            {saveForms && (
                <Box>
                    <Typography fontSize={16} color="grey[400]">
                        {translate(`stepper.form.assignment.recurrenceTypes.${recurrenceType.toLowerCase()}`) +
                            ' ' +
                            translate('stepper.form.assignment.recurrence')}
                    </Typography>
                    <Typography fontSize={16} color="grey[400]">
                        {'Start Date' +
                            ' - ' +
                            dayjs(startDate).format('MM/DD/YYYY') +
                            ' ~ End Date' +
                            ' - ' +
                            (noEndDate
                                ? translate('stepper.form.assignment.drawerNoEndDate')
                                : dayjs(endDate).format('MM/DD/YYYY'))}
                    </Typography>
                    <Typography fontSize={12} color="grey[400]">
                        {getRecurrenceLabel()}
                    </Typography>
                </Box>
            )}
            <CustomDrawer
                title={translate('stepper.form.assignment.drawerCaption')}
                overlineMeta={translate('stepper.form.assignment.drawerOverLineMeta')}
                header={translate('stepper.form.assignment.frequence')}
                openDrawer={openDrawer}
                closeDrawer={handleClose}
                content={
                    <Box sx={drawerStyles.container}>
                        {renderForm()}
                        <Box id={'forms-edit-buttons'} sx={drawerStyles.buttonsContainer}>
                            <ClnButton
                                variant="text"
                                onClick={handleClose}
                                label={translate('stepper.form.cancel')}
                                data-test="new_action_item_flow_3_recurrence_pattern-cancel_button"
                                data-origin="ui-lib"
                                sx={{ flex: 1 }}
                            />
                            <ClnButton
                                onClick={submit}
                                label={translate('stepper.save')}
                                data-test="new_action_item_flow_3_recurrence_pattern-save_button"
                                data-origin="ui-lib"
                                sx={{ flex: 1 }}
                            />
                        </Box>
                    </Box>
                }
            />
        </Box>
    )
}
