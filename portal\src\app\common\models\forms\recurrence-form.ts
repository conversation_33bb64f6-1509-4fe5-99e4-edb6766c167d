import { Dayjs } from 'dayjs'
import { RecurrenceSubTypeEnum } from '../../enums/RecurrenceSubTypeEnum'
import { RecurrenceTypeEnum } from '../../enums/RecurrenceTypeEnum'

export class RecurrenceForm {
    startDate!: Dayjs
    endDate?: Dayjs
    noEndDate?: boolean
    recurrenceType!: RecurrenceTypeEnum
    weekDays?: number[]
    months?: number[]
    quarters?: number[]
    dayOfTheMonth?: number
    monthOfTheYear?: number
    recurrenceSubType?: RecurrenceSubTypeEnum
    customDates?: Dayjs[]
}
