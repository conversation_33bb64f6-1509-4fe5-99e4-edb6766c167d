import { Box, Grid } from '@mui/material'
import { Category, Status } from '@/app/common/models/action'
import { StackedChart } from '../StackedChart'
import { RoundChartWrapper } from '../RoundChart'
import { NoTranslate, TranslationContext, TranslationContextState } from '@celanese/celanese-ui'
import { useCallback, useContext, useEffect, useMemo, useState } from 'react'
import { AzureFunctionClient } from '@/app/common/clients/azure-function-client'
import { ClnButton, MatIcon } from '@celanese/ui-lib'

import { useDebounceFunction } from '@/app/common/hooks/general-functions/useDebounce'
import { FilterInfoProps } from '../../ActionTable/HomeFilter'
import { ActionStatusExternalIdClearEnum } from '@/app/common/enums/ActionItemStatusEnum'
import { toCamelCase } from '@/app/common/utils/transform-options-for-filter'
import { translate } from '@/app/common/utils/generate-translate'
import { getLocalUserSite } from '@celanese/celanese-ui'
import { UserRolesPermission } from '@celanese/celanese-sdk'

type SiteChartsProps = {
    categories: Category[]
    allStatus: Status[]
    filterInfo: FilterInfoProps
    client: AzureFunctionClient
    activeUser?: UserRolesPermission
    handleError: (err: any) => void
    setIsChart: (value: boolean) => void
}

export function SiteCharts({
    categories,
    allStatus,
    filterInfo,
    client,
    activeUser,
    handleError,
    setIsChart,
}: SiteChartsProps) {
    const { siteId: site } = getLocalUserSite() || {}
    const { locale } = useContext<TranslationContextState>(TranslationContext)

    const [groupsUnitChart, setGroupsUnitChart] = useState<string[]>([])

    const [actionsByUnitGroup, setActionsByUnitGroup] = useState<any[]>([])
    const [actionsByCategoryGroup, setActionsByCategoryGroup] = useState<any[]>([])

    const [loadingUnitChart, setLoadingUnitChart] = useState<boolean>(false)

    const unitChart = useMemo(() => {
        if (!actionsByUnitGroup?.length) {
            setGroupsUnitChart([])
            return []
        }

        const desiredOrder = [
            'Assigned',
            'Due Date Extension',
            'Reassignment Period',
            'Pending Approval',
            'Challenge Period',
            'Pending Verification',
            'Approval Rejected',
            'Verification Rejected',
            'Cancelled',
            'Completed',
        ]

        const unitExternalIds: any[] = []
        actionsByUnitGroup.forEach(
            (item: any) =>
                !unitExternalIds.includes(item.group?.reportingUnit?.externalId ?? '') &&
                unitExternalIds.push(item.group?.reportingUnit?.externalId)
        )

        const statusMap: any = {}

        actionsByUnitGroup.forEach((item: any) => {
            const unitId = item.group?.reportingUnit?.externalId ?? ''
            const status = item.group?.currentStatus?.externalId ?? ''
            const value = item.aggregates?.[0].value

            if (!statusMap[status]) {
                statusMap[status] = {}
            }

            statusMap[status][unitId] = value
        })

        const convertedData = Object.keys(statusMap).map((status) => {
            return {
                label: allStatus.find((x) => x.externalId === status)?.name,
                value: unitExternalIds.map((unitId: any) => statusMap[status][unitId] || 0),
            }
        })

        const sortedConvertedData = convertedData
            .sort((a, b) => {
                return desiredOrder.indexOf(a?.label ?? '') - desiredOrder.indexOf(b?.label ?? '')
            })
            .map((item: any) => {
                const camelLabel = toCamelCase(item.label)
                const fullLabel = `status.fullName.${camelLabel}`
                const translatedLabel = translate(fullLabel)

                return {
                    ...item,
                    label: translatedLabel,
                }
            })

        setGroupsUnitChart(unitExternalIds?.map((item: string) => item?.slice(7)))

        return sortedConvertedData
    }, [actionsByUnitGroup, locale])

    const categoryChart = useMemo(() => {
        if (!actionsByCategoryGroup?.length) {
            return []
        }

        const convertedCategoryData = actionsByCategoryGroup.map((item: any) => {
            const camelLabel = toCamelCase(
                categories.find((x) => x.externalId === item.group?.category?.externalId)?.name ?? ''
            )
            const fullLabel = `category.${camelLabel}`
            const translatedLabel = translate(fullLabel)

            return {
                label: translatedLabel,
                value: item.aggregates?.[0].value,
            }
        })

        return convertedCategoryData.sort((a: { label: string }, b: { label: string }) =>
            a.label.localeCompare(b.label)
        )
    }, [actionsByCategoryGroup])

    const fetchCharts = useCallback(async () => {
        try {
            const filters = {
                reportingSiteExternalId: site,
                dueDateGte: filterInfo?.dueDateGte ?? null,
                dueDateLt: filterInfo?.dueDateLt ?? null,
                reportingUnitExternalIds: filterInfo.reportingUnitExternalIds,
                reportingLocationExternalIds: filterInfo.reportingLocationExternalIds,
                categoryExternalIds: filterInfo.categoryExternalId,
                subcategoryExternalIds: filterInfo.subcategoryExternalId,
                siteSpecificCategoryExternalIds: filterInfo.siteSpecificCategoryExternalId,
                statusExternalIds:
                    filterInfo && filterInfo?.statusExternalIds && filterInfo?.statusExternalIds?.length > 0
                        ? filterInfo?.statusExternalIds
                        : Object.values(ActionStatusExternalIdClearEnum),
                onlyPrivate: filterInfo.onlyPrivate,
                sourceEventTitleEq: filterInfo.sourceEventTitleEq ?? undefined,
            }

            const byUnitPromise = client.getActionsGroupedBy({
                activeUserEmail: activeUser?.email,
                groupBy: ['reportingUnit', 'currentStatus'],
                filters,
            })

            const byCategoryPromise = client.getActionsGroupedBy({
                activeUserEmail: activeUser?.email,
                groupBy: ['category'],
                filters,
            })

            const [byUnitResponse, byCategoryResponse] = await Promise.all([byUnitPromise, byCategoryPromise])

            setActionsByUnitGroup(byUnitResponse.items || {})
            setActionsByCategoryGroup(byCategoryResponse.items || {})
        } catch (err) {
            handleError(err)
        } finally {
            setLoadingUnitChart(false)
        }
    }, [filterInfo])

    const debouncedFetchChartSite = useCallback(useDebounceFunction(fetchCharts, 500), [fetchCharts])

    useEffect(() => {
        if (loadingUnitChart) return

        setLoadingUnitChart(true)
        debouncedFetchChartSite()
    }, [fetchCharts])

    return (
        <Box sx={{ width: '100%', display: 'flex', flexDirection: 'column', gap: '1rem' }} id={'charts-site'}>
            <Box sx={{ width: '100%', display: 'flex', justifyContent: 'flex-end' }}>
                <ClnButton
                    label={translate('table.listView')}
                    variant="outlined"
                    onClick={() => {
                        setIsChart(false)
                    }}
                    startIcon={<MatIcon icon="lists" />}
                />
            </Box>
            <Grid container spacing={2} sx={{ height: '100%' }}>
                <Grid item lg={8} md={6} sm={12}>
                    <NoTranslate>
                        <StackedChart
                            key={'unit-chart-site'}
                            dataChart={unitChart}
                            groupsChart={groupsUnitChart}
                            type="vertical"
                            label={translate('dashboards.charts.byUnit')}
                            width={'100%'}
                            loading={loadingUnitChart}
                        />
                    </NoTranslate>
                </Grid>
                <Grid item lg={4} md={6} sm={12}>
                    <NoTranslate>
                        <RoundChartWrapper
                            key={'category-chart-site'}
                            dataChart={categoryChart}
                            type="pie"
                            label={translate('dashboards.charts.byCategory')}
                            height={250}
                            width={'100%'}
                            loading={loadingUnitChart}
                        />
                    </NoTranslate>
                </Grid>
            </Grid>
        </Box>
    )
}

export default SiteCharts
