def get_code_from_reporting_site_external_id(site_id: str) -> str:
    """
    Extract the site code from a valid reporting site external ID (e.g., 'STS-XYZ').

    Args:
        site_id (str): The reporting site external ID.

    Returns:
        str: The site code extracted from the ID.

    Raises:
        ValueError: If the site_id is not a valid reporting site external ID.

    """
    if not is_valid_reporting_site_id(site_id):
        msg = f"Invalid site ID format: {site_id}"
        raise ValueError(msg)
    return site_id.split("-")[-1]


def is_valid_reporting_site_id(site_id: str) -> bool:
    """
    Check if the given site ID is a valid reporting site external ID.

    A valid ID starts with 'STS-' and has exactly 7 characters (e.g., 'STS-001').

    Args:
        site_id (str): The reporting site external ID to validate.

    Returns:
        bool: True if the site ID is valid, False otherwise.

    """
    return site_id.startswith("STS-") and len(site_id) == 7


def get_transactional_space_from_site_id(
    site_id: str,
    private: bool | None = None,
) -> str:
    """
    Compute the transactional space name for a given reporting site external ID.

    If the 'private' flag is True, returns a protected space. Otherwise,
    extracts the site code and returns the default space format.

    Args:
        site_id (str): The reporting site external ID (e.g., 'STS-XYZ').
        private (bool | None): Whether the data space is private.

    Returns:
        str: The name of the transactional data space.

    Raises:
        ValueError: If the site_id is not a valid reporting site external ID.

    """
    if private:
        return "AIM-COR-ALL-PROT"
    return f"AIM-{get_code_from_reporting_site_external_id(site_id)}-ALL-DAT"
