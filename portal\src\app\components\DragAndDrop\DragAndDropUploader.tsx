import React, { useCallback } from 'react'
import { useDropzone } from 'react-dropzone'
import { Container, DNDContainer } from './styles'
import { Typography, useTheme } from '@mui/material'
import { MatIcon } from '@celanese/ui-lib'
import { translate } from '@/app/common/utils/generate-translate'

interface DragAndDropUploaderProps {
    onFileUpload: (files: File[]) => void
    files: File[]
    dataTest?: string
}

export const DragAndDropUploader: React.FC<DragAndDropUploaderProps> = ({ onFileUpload, files, dataTest }) => {
    const onDrop = useCallback(
        (acceptedFiles: File[]) => {
            onFileUpload(acceptedFiles)
        },
        [onFileUpload]
    )

    const { getRootProps, getInputProps } = useDropzone({ onDrop })

    const theme = useTheme()

    return (
        <Container {...getRootProps()} data-test={dataTest} data-origin="aim">
            <input {...getInputProps()} />
            <DNDContainer>
                <MatIcon icon="upload_2" fontSize="32px" color="grey[900]" />
                <Typography fontSize={16} textAlign={'center'}>
                    {translate('stepper.form.assignment.uploadText01')}{' '}
                    <span
                        style={{
                            fontWeight: 'bold',
                            textDecoration: 'underline',
                            color: `${theme.palette.primary.main}`,
                        }}
                    >
                        {' '}
                        {translate('stepper.form.assignment.uploadText02')}
                    </span>
                </Typography>
                <Typography fontSize={16}>{translate('stepper.form.assignment.uploadTypes')}</Typography>
            </DNDContainer>
        </Container>
    )
}
