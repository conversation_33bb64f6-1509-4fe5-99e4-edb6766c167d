﻿import { ClnDatePicker } from '@celanese/ui-lib'
import { Box, PopperPlacementType, useTheme } from '@mui/material'
import { Dayjs } from 'dayjs'
import GenericFieldTitle from './GenericFieldTitle'
import { Controller } from 'react-hook-form'

type GenericDatePickerProps = {
    control: any
    name: string
    defaultValue?: Dayjs | null
    label?: string
    disabled?: boolean | undefined
    minDate?: Dayjs
    subHeader?: string
    error?: boolean
    placement?: PopperPlacementType
    onChange?: (date: Dayjs | null) => void
}

export const GenericDatePicker = ({
    control,
    name,
    defaultValue,
    label,
    disabled,
    minDate,
    subHeader,
    error,
    placement,
    onChange,
}: GenericDatePickerProps) => {
    const theme = useTheme()
    return (
        <Box
            sx={{
                display: 'flex',
                flexDirection: 'column',
                width: '100%',
                gap: 1,
            }}
        >
            {subHeader && <GenericFieldTitle fieldName={subHeader} isSubHeader />}
            <Controller
                name={name}
                control={control}
                render={({ field: { value, onChange: onChangeField } }) => (
                    <ClnDatePicker
                        value={value}
                        defaultValue={defaultValue}
                        onChange={(event) => {
                            onChange && onChange(event)
                            onChangeField(event)
                        }}
                        label={label ?? ''}
                        disabled={disabled}
                        minDate={minDate}
                        slotProps={{
                            popper: {
                                sx: {
                                    zIndex: 1300,
                                },
                                placement: placement ?? 'top-start',
                                modifiers: [
                                    {
                                        name: 'offset',
                                        options: {
                                            offset: [0, 8],
                                        },
                                    },
                                ],
                            },
                        }}
                        sx={{
                            display: 'flex',
                            justifyContent: 'center',
                            alignItems: 'center',
                            '& .MuiOutlinedInput-root': {
                                display: 'flex',
                                justifyContent: 'center',
                                alignItems: 'center',
                                '& fieldset': {
                                    borderColor: error ? theme.palette.error.main : 'primary.main',
                                },
                                '&:hover fieldset': {
                                    borderColor: error ? theme.palette.error.main : 'primary.main',
                                },
                            },
                            '& .MuiInputBase-root': {
                                borderRadius: '4px',
                                width: '100%',
                                color: 'primary.main',
                                display: 'flex',
                                justifyContent: 'center',
                                alignItems: 'center',
                            },
                            '& .MuiInputBase-input': {
                                padding: '8px 0',
                                textAlign: 'center',
                                width: 'auto',
                                color: error ? theme.palette.error.main : 'primary.main',
                            },
                            '& .MuiIconButton-root': {
                                color: error ? theme.palette.error.main : 'primary.main',
                                marginLeft: 'auto',
                            },
                        }}
                    />
                )}
            />
        </Box>
    )
}
