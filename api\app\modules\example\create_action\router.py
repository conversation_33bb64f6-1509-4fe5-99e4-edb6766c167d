from typing import Annotated

from fastapi import APIRouter, Depends

from ..validations import is_running_locally
from .models import CreateActionRequest, CreateActionResponse
from .service import CreateActionService

router = APIRouter(
    prefix="/example/create-action",
    dependencies=[Depends(is_running_locally)],
    include_in_schema=False,
)


@router.post("")
async def create_action(
    request: CreateActionRequest,
    service: Annotated[CreateActionService, Depends()],
) -> CreateActionResponse:
    """Mimmick a simplified create action endpoint."""
    return await service.create(request)
