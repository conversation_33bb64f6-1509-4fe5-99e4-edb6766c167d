from enum import StrEnum


class SourceEventStatus(StrEnum):
    IN_PROGRESS = "SEVS-InProgress"
    COMPLETED = "SEVS-Completed"
    ON_HOLD = "SEVS-OnHold"
    CANCELLED = "SEVS-Cancelled"


EVENT_EXPORT_FIELD_MAP = {
    "externalId": "external_id",
    "title": "title",
    "description": "description",
    "actionsCount": "actions_count",
    "actionsCompletedCount": "actions_completed_count",
    "status": "status",
    "reportingSite": "reporting_site",
    "reportingUnit": "reporting_unit",
    "reportingLine": "reporting_line",
    "category": "category",
    "subCategory": "sub_category",
    "siteSpecificCategory": "site_specific_category",
    "owner": "owner",
    "assignmentDate": "assignment_date",
    "dueDate": "display_due_date",
    "isPrivate": "is_private",
}
