import { css, Tab as <PERSON><PERSON><PERSON>ab, Tabs as M<PERSON>Tabs, styled } from '@mui/material'

export const Tab = styled(MUITab)`
    ${({ theme }) => css`
        padding-bottom: 0px !important;
        text-transform: none !important;
        font-weight: 500;
        font-size: 14px;
        width: '100%'
        color: ${theme.palette.text.secondary};
        min-height: 48px !important;
        & .Mui-selected {
            color: ${theme.palette.primary.main};
            padding-bottom: 8px;
        }
        & .Mui-disabled {
            color: ${theme.palette.text.disabled};
        }
    `}
`

export const Tabs = styled(MUITabs)`
    ${({ theme }) => css`
        & .MuiTabs-indicator {
            background-color: ${theme.palette.primary.main};
            height: 3px;
        }
    `}
`

export const BorderTab = styled(MUITabs)`
    ${({ theme }) => css`
        border-bottom: 2px solid ${theme.palette.divider};
    `}
`

export const ButtonsBox = styled('div')`
    ${({ theme }: any) => css`
        display: flex;
        flex-direction: row;
        align-items: center;
    `}
`

export const ButtonsBoxMobile = styled('div')`
    ${({ theme }: any) => css`
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;
        margin-top: 1rem;
        padding: 0 30px;
    `}
`
