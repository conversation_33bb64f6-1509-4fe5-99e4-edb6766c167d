from http import <PERSON><PERSON><PERSON><PERSON><PERSON>
from http.client import <PERSON><PERSON><PERSON><PERSON>x<PERSON>
from typing import Optional

from clients.action_item_client import ActionItemClient
from clients.core.constants import (
    USER_AZURE_ATTRIBUTE_PREFIX,
)
from clients.templates.requests import GetTemplatesByUserIdRequest
from clients.user_complement.requests import (
    GetUserRolesAndTeamsRequest,
)
from services.user_complements_service import UserByEmailResult, UserComplementsService


class TemplateService:
    """Service that templates data related to action items."""

    def __init__(self, action_item_client: ActionItemClient) -> None:
        """
        Initialize the TemplateService.

        Args:
            action_item_client (ActionItemClient): Client for interacting with action item data.

        """
        self._action_item_client = action_item_client

    async def get_templates_by_user_id(
        self,
        request: GetTemplatesByUserIdRequest,
    ) -> list[dict[str, str]]:
        """
        Retrieve templates based on the provided user ID.

        Args:
            request (GetTemplatesByUserIdRequest): The request object containing the user's email and reporting site external ID.

        Returns:
            list[dict[str, str]]: A list of templates associated with the provided user, each represented as a dictionary with string keys and values.

        """
        user = await self._get_user_by_email(
            request.active_user_email or "",
            request.reporting_site_external_id or "",
        )
        if not user:
            return []

        request.active_user_external_id = user.external_id.removeprefix(
            USER_AZURE_ATTRIBUTE_PREFIX,
        )

        return await self._action_item_client.templates.get_templates_by_user_id(
            request,
        )

    async def _get_user_by_email(
        self,
        email: str,
        site_external_id: str,
    ) -> Optional[UserByEmailResult]:
        """
        Retrieve a user by email and site.

        This function searches for a user based on their email address and the external ID of the reporting site.
        If the user is found, it returns the user's details; otherwise, it returns None.

        Args:
            email (str): The email address of the user to be retrieved.
            site_external_id (str): The external ID of the reporting site associated with the user.

        Returns:
            Optional[UserByEmailResult]:
                - The user details if the user is found.
                - None if no matching user is found.

        """
        user_by_email_request = GetUserRolesAndTeamsRequest(
            email=email,
            reporting_site_external_id=site_external_id,
        )

        try:
            user_service = UserComplementsService(self._action_item_client)
            user: Optional[UserByEmailResult] = await user_service.get_user_by_email(
                user_by_email_request,
            )

            if user is None:
                self._raise_unauthorized(email)

        except Exception:
            self._raise_unauthorized(email)
        else:
            return user

    def _raise_unauthorized(self, email: str) -> None:
        self._log.error(f"No user found with email: {email}")
        raise HTTPException(status_code=HTTPStatus.UNAUTHORIZED)
