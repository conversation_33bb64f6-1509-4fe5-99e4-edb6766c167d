import asyncio
import calendar
import copy
import json
import os
import sys
from datetime import UTC, date, datetime, timedelta
from typing import Any, List

from pydantic import ValidationError

from models.notification import Notification
from services.notification_service import NotificationService

script_dir = os.path.dirname(os.path.abspath(__file__))
project_dir = os.path.dirname(script_dir)
sys.path.append(project_dir)
from clients.action_item_client import ActionItemClient
from clients.actions.constants import RecurrenceTypeExternalId
from clients.core.constants import ActionStatusEnum, FileMetadataKey
from clients.sort_mapper import GetSortMapperRequest, SortMapper
from models.action_item import (
    ActionItem,
    ActionItemLink,
    RecurrenceInstance,
    SourceEvent,
    StatusHistoryInstance,
)
from models.entities_enum import EntitiesEnum
from models.node_reference import NodeReference
from models.settings import Settings
from services.cognite_service import CogniteService
from services.graphql_service import GraphqlService
from services.logging_service import LoggingService
from utils.date_utils import get_first_month_of_quarter, get_quarter
from utils.general_utils import generate_query
from utils.id_generation_utils import IdGenerator

YEAR_FACTOR = {
    "RCT-yearly": 1,
    "RCT-biennially": 2,
    "RCT-triennially": 3,
    "RCT-quinquennially": 5,
}


class RecurrentActionItemsCreator:
    def __init__(
        self,
        cognite_service: CogniteService,
        graphql_service: GraphqlService,
        settings: Settings,
        logging_service: LoggingService,
        notification_service: NotificationService,
        action_item_client: ActionItemClient,
    ) -> None:
        self._cognite_service = cognite_service
        self._graphql_service = graphql_service
        self._notification_service = notification_service
        self._settings = settings
        self._log = logging_service
        self._action_item_client = action_item_client
        self._now = datetime.now(tz=UTC)
        self._now_str = self._now.replace(microsecond=0).isoformat()
        self._today = self._now.date()
        self._id_generator = IdGenerator[EntitiesEnum](self._now)

    async def execute(self) -> tuple:
        recurrent_action_items = await self._get_recurrent_action_items()

        if len(recurrent_action_items) == 0:
            self._log.info("No recurrent action items to process.")
            await self._cleanup()
            return [], []

        errors_by_action_id = {}
        items_to_upsert: List[ActionItem] = []
        source_event_to_upsert: List[SourceEvent] = []
        users_in_action = set()

        items_to_process: list[ActionItem] = []
        user_azure_attribute_external_ids: list[str] = []

        for item_data in recurrent_action_items:
            item: ActionItem
            actions_count_by_recurring = 0
            try:
                item = ActionItem(**item_data)
                item._is_new = False
            except ValidationError as err:
                errors_by_action_id[item_data["externalId"]] = err
                self._log.error(f"Invalid item: {item_data}. Error: {err}")
                continue

            if (
                item.recurrenceInstance is None
                or item.assignees is None
                or len(item.assignees) == 0
                or item.owner is None
                or item.reportingSite is None
            ):
                continue

            assert item.externalId is not None
            assert item.space is not None

            user_azure_attribute_external_ids.extend(
                a.externalId for a in item.assignees
            )

            items_to_process.append(item)

        sort_mapper = self._action_item_client.sort_mapper.get_sort_mapper(
            GetSortMapperRequest(
                user_azure_attribute_external_ids=user_azure_attribute_external_ids,
            ),
        )

        for item in items_to_process:
            self._log.info(f"Processing recurrent action item: {item.externalId}")

            recurrence_type = item.recurrenceInstance.recurrenceType.externalId
            next_dates = [
                date.fromisoformat(dt)
                for dt in (item.recurrenceInstance.nextDates or [])
            ]

            if recurrence_type == RecurrenceTypeExternalId.CUSTOM:
                remaining_dates = list(filter(lambda rd: rd > self._today, next_dates))
                if len(remaining_dates) == 0:
                    item.currentStatus = NodeReference(
                        externalId=ActionStatusEnum.INACTIVE,
                        space=item.currentStatus.space,
                    )
                    item.statusHistory.append(
                        StatusHistoryInstance(
                            externalId=self._id_generator.next_id(
                                EntitiesEnum.StatusHistoryInstance,
                            ),
                            action=NodeReference(
                                externalId=item.externalId,
                                space=item.space,
                            ),
                            space=item.space,
                            status=NodeReference(
                                externalId=ActionStatusEnum.INACTIVE,
                                space=item.currentStatus.space,
                            ),
                            changedAt=self._now_str,
                        ),
                    )

                item.recurrenceInstance.nextDates = [str(rd) for rd in remaining_dates]
                items_to_create = (
                    self._get_one_time_action_items_from_recurrent_action_item(
                        item,
                        sort_mapper,
                    )
                )
                self._add_users_related_to_action_items(
                    items_to_create,
                    users_in_action,
                )
                actions_count_by_recurring += len(items_to_create)
                items_to_upsert.extend(items_to_create)

            else:
                if len(next_dates) > 1:
                    self._log.warning(
                        f"Only one next date is expected for non-custom recurrent action items, but {len(next_dates)} were found. Skipping item.",
                    )
                    continue

                item_was_scheduled_before = len(next_dates) == 1
                create_one_time_items_now = (
                    item_was_scheduled_before
                    or self._does_date_match_recurrence(
                        item.recurrenceInstance,
                        self._today,
                    )
                )

                if create_one_time_items_now:
                    one_time_action_items = (
                        self._get_one_time_action_items_from_recurrent_action_item(
                            item,
                            sort_mapper,
                        )
                    )
                    self._add_users_related_to_action_items(
                        one_time_action_items,
                        users_in_action,
                    )

                    actions_count_by_recurring += len(one_time_action_items)
                    items_to_upsert.extend(one_time_action_items)

                    for file_external_id in item.attachments or []:
                        metadata = self._cognite_service.get_file_metadata(
                            file_external_id=file_external_id,
                        )
                        assert metadata is not None
                        related_actions = json.loads(
                            metadata.get(FileMetadataKey.RELATED_ACTIONS.value, "[]"),
                        )

                        related_actions.extend(
                            [otai.externalId for otai in one_time_action_items],
                        )
                        metadata[FileMetadataKey.RELATED_ACTIONS.value] = json.dumps(
                            related_actions,
                        )

                        self._cognite_service.set_file_metadata(
                            file_external_id=file_external_id,
                            metadata=metadata,
                        )

                end_date = item.recurrenceInstance.endDate

                next_occurrence = self._get_next_occurrence(
                    item.recurrenceInstance,
                    self._today,
                )

                should_keep_recurrence_active = end_date is None or (
                    next_occurrence is not None and next_occurrence <= end_date
                )

                next_date = next_occurrence if should_keep_recurrence_active else None
                item.recurrenceInstance.nextDates = (
                    [str(next_date)] if next_date is not None else []
                )
                if not should_keep_recurrence_active:
                    item.currentStatus = NodeReference(
                        externalId=ActionStatusEnum.INACTIVE,
                        space=item.currentStatus.space,
                    )
                    item.statusHistory.append(
                        StatusHistoryInstance(
                            externalId=self._id_generator.next_id(
                                EntitiesEnum.StatusHistoryInstance,
                            ),
                            action=NodeReference(
                                externalId=item.externalId,
                                space=item.space,
                            ),
                            space=item.space,
                            status=NodeReference(
                                externalId=ActionStatusEnum.INACTIVE,
                                space=item.currentStatus.space,
                            ),
                            changedAt=self._now_str,
                        ),
                    )

            if item.recurrenceInstance.sourceEvent is not None:
                source_event = copy.deepcopy(item.recurrenceInstance.sourceEvent)
                actions_by_event = [
                    NodeReference(externalId=action.externalId, space=action.space)
                    for action in items_to_upsert
                    if action.sourceId == source_event.externalId
                ]

                existing_event = next(
                    (
                        event
                        for event in source_event_to_upsert
                        if event.externalId == source_event.externalId
                    ),
                    None,
                )

                if existing_event:
                    existing_event.actionsCount += actions_count_by_recurring
                    existing_event.actions = actions_by_event
                else:
                    source_event.actionsCount += actions_count_by_recurring
                    source_event.actions = actions_by_event
                    source_event_to_upsert.append(source_event)

                item.recurrenceInstance.sourceEvent = None

            items_to_upsert.append(item)

        if len(items_to_upsert) == 0:
            return [], errors_by_action_id

        res, err = self._cognite_service.upsert_action_items(items_to_upsert)
        if len(err) > 0:
            await self._cleanup()
            self._log.error(f"Error while upserting action items: {err}")
            return res, err

        if len(source_event_to_upsert) > 0:
            res, err = self._cognite_service.upsert_source_events(
                source_event_to_upsert,
            )

            if len(err) > 0:
                await self._cleanup()
                self._log.error(f"Error while upserting source events: {err}")
                return res, err
        try:
            (
                categories,
                sub_categories,
                site_specific_categories,
                applications,
                reporting_units,
                reporting_locations,
                reporting_sites,
                business_lines,
                reporting_lines,
                source_types,
                _,
                users,
            ) = await self._graphql_service.get_existing_entities(list(users_in_action))

            notifications = []
            for action_item in list(filter(lambda x: x._is_new, items_to_upsert)):
                action_item_dict = action_item.model_dump()
                action_item_dict["currentStatus"]["name"] = "Assigned"
                action_item_dict["actionItemKind"]["name"] = "One-Time"
                action_item_dict["site"] = action_item.reportingSite.externalId or "-"
                user_related_props_to_add_display_name = [
                    ("approverName", action_item._approver_id),
                    ("verifierName", action_item._verifier_id),
                    ("createdByName", action_item.createdBy.externalId),
                    ("ownerName", action_item.owner.externalId),
                ]
                for prop_to_add_name, user_id in user_related_props_to_add_display_name:
                    if user_id is None:
                        continue

                    user_name = users[user_id]["user"]["displayName"]
                    if user_name is None:
                        continue

                    action_item_dict[prop_to_add_name] = user_name

                fields_to_add_props = [
                    ("application", applications, "name"),
                    ("category", categories, "name"),
                    ("subCategory", sub_categories, "name"),
                    ("siteSpecificCategory", site_specific_categories, "description"),
                    ("reportingUnit", reporting_units, "description"),
                    ("reportingLocation", reporting_locations, "description"),
                    ("reportingSite", reporting_sites, "description"),
                    ("reportingLine", reporting_lines, "description"),
                    ("businessLine", business_lines, "description"),
                    ("sourceType", source_types, "name"),
                ]

                for (
                    action_field,
                    entities_dict,
                    prop_to_add_name,
                ) in fields_to_add_props:
                    prop = action_item_dict.get(action_field)
                    if prop is None:
                        if action_field == "siteSpecificCategory":
                            if not isinstance(action_item_dict.get(action_field), dict):
                                action_item_dict[action_field] = {}
                            action_item_dict[action_field].setdefault(
                                "description",
                                "-",
                            )
                            action_item_dict[action_field].setdefault("space", "-")
                            action_item_dict[action_field].setdefault("externalId", "-")

                        continue

                    external_id = prop.get("externalId")
                    if external_id is None:
                        continue

                    entity = entities_dict.get(external_id)
                    if entity is None:
                        continue

                    entity_prop_to_add = entity.get(prop_to_add_name)
                    if entity_prop_to_add is None:
                        continue

                    action_item_dict[action_field][
                        prop_to_add_name
                    ] = entity_prop_to_add

                assigned_to_email = (
                    await self._get_user_email_by_user_azure_attribute_id(
                        action_item_dict["assignedTo"]["externalId"],
                    )
                )
                owner_email = await self._get_user_email_by_user_azure_attribute_id(
                    action_item_dict["owner"]["externalId"],
                )

                notifications.append(
                    Notification.from_action_item_creation(
                        action_item_dict,
                        assigned_to_email,
                        owner_email,
                    ),
                )

            self._notification_service.send_notifications(notifications)

        except Exception as err:
            self._log.error(f"Could not send notifications. Error: {err}")

        await self._cleanup()

        return res, []

    def _get_one_time_action_items_from_recurrent_action_item(
        self,
        recurrent_action_item: ActionItem,
        sort_mapper: SortMapper,
    ) -> List[ActionItem]:
        one_time_action_items = []
        for assignee in recurrent_action_item.assignees or []:
            item = copy.deepcopy(recurrent_action_item)
            item._is_new = True
            item.externalId = self._id_generator.next_id(EntitiesEnum.Action)
            assert item.space is not None
            assert recurrent_action_item.externalId is not None
            assert recurrent_action_item.space is not None
            assert recurrent_action_item.recurrenceInstance is not None
            item.parent = NodeReference(
                externalId=recurrent_action_item.externalId,
                space=recurrent_action_item.space,
            )
            item.assignedTo = assignee
            item.sortAssignee = sort_mapper.get_user_azure_attribute_sort_value(
                assignee.externalId,
            )
            item.assignees = []
            item.actionItemKind.externalId = "ACTK-oneTime"
            item.recurrenceInstance = None
            item.statusHistory = [
                StatusHistoryInstance(
                    externalId=self._id_generator.next_id(
                        EntitiesEnum.StatusHistoryInstance,
                    ),
                    space=item.space,
                    action=NodeReference(
                        externalId=item.externalId,
                        space=item.space,
                    ),
                    status=NodeReference(
                        externalId=ActionStatusEnum.ASSIGNED,
                        space=item.currentStatus.space,
                    ),
                    friendlyName='{"actionUser":"createdBy","comments":""}',
                    statusSubject=item.createdBy,
                    changedAt=self._now_str,
                ),
                StatusHistoryInstance(
                    externalId=self._id_generator.next_id(
                        EntitiesEnum.StatusHistoryInstance,
                    ),
                    space=item.space,
                    action=NodeReference(
                        externalId=item.externalId,
                        space=item.space,
                    ),
                    status=NodeReference(
                        externalId=ActionStatusEnum.ASSIGNED,
                        space=item.currentStatus.space,
                    ),
                    friendlyName='{"actionUser":"assignedTo","comments":""}',
                    statusSubject=item.assignedTo,
                    changedAt=(self._now + timedelta(seconds=1))
                    .replace(microsecond=0)
                    .isoformat(),
                ),
            ]
            item.currentStatus = NodeReference(
                externalId=ActionStatusEnum.ASSIGNED,
                space=item.currentStatus.space,
            )
            item.assignmentDate = str(self._today)
            item.dueDate = str(
                self._get_due_date(
                    recurrent_action_item.recurrenceInstance.recurrenceType.externalId,
                    self._today,
                ),
            )
            item.displayDueDate = str(
                self._get_due_date(
                    recurrent_action_item.recurrenceInstance.recurrenceType.externalId,
                    self._today,
                ),
            )
            item.sourceId = (
                recurrent_action_item.sourceId
                if recurrent_action_item.sourceId is not None
                else None
            )
            item.sourceType = (
                NodeReference(
                    externalId=recurrent_action_item.sourceType.externalId,
                    space=recurrent_action_item.sourceType.space,
                )
                if recurrent_action_item.sourceType is not None
                else None
            )
            item.links = (
                self._duplicate_action_links(
                    recurrent_action_item.links,
                    item.externalId,
                )
                if recurrent_action_item.links is not None
                else None
            )
            item.createdAt = self._id_generator.get_timestamp()
            if item.approvalWorkflow is not None:
                approval_workflow_external_id = self._id_generator.next_id(
                    EntitiesEnum.ApprovalWorkflow,
                )
                item.approvalWorkflow.externalId = approval_workflow_external_id
                item.approvalWorkflow.startDate = self._today
                for step in item.approvalWorkflow.steps or []:
                    step.approvalWorkflow.externalId = approval_workflow_external_id
                    step.externalId = self._id_generator.next_id(
                        EntitiesEnum.ApprovalWorkflowStep,
                    )
                    step.startDate = self._today

                    if len(step.users or []) == 0:
                        continue

                    user_id = step.users[0].externalId

                    step.approvalCondition = {
                        "externalId": "APWCO-AND",
                        "space": "APW-COR-ALL-REF",
                    }

                    step.approvalWorkflowConsentType = {
                        "externalId": "APWCT-User",
                        "space": "APW-COR-ALL-REF",
                    }

                    if step.description == "AIM-approval":
                        item._approver_id = "UserAzureAttribute_" + user_id

                    if step.description == "AIM-verification":
                        item._verifier_id = "UserAzureAttribute_" + user_id

            one_time_action_items.append(item)

        return one_time_action_items

    def _add_users_related_to_action_items(
        self,
        action_item: list[ActionItem],
        users: set,
    ) -> None:
        add_if_not_none = lambda x: users.add(x) if x is not None else None
        for item in action_item:
            assert item.assignedTo is not None

            users.add(item.assignedTo.externalId)
            users.add(item.createdBy.externalId)
            users.add(item.owner.externalId)
            add_if_not_none(item._approver_id)
            add_if_not_none(item._verifier_id)

    def _duplicate_action_links(
        self,
        links: list[ActionItemLink],
        action_external_id: str,
    ) -> list:
        links_to_return = []
        for link in links:
            new = copy.deepcopy(link)
            new.externalId = self._id_generator.next_id(EntitiesEnum.ActionItemLink)
            assert link.action is not None
            new.action = NodeReference(
                externalId=action_external_id,
                space=link.action.space,
            )
            links_to_return.append(new)

        return links_to_return

    @staticmethod
    def _get_due_date(
        recurrence_type: RecurrenceTypeExternalId,
        start_date: date,
    ) -> date:
        match recurrence_type:
            case RecurrenceTypeExternalId.DAILY:
                return start_date
            case RecurrenceTypeExternalId.WEEKLY:
                return start_date + timedelta(days=6)
            case RecurrenceTypeExternalId.MONTHLY:
                last_day_of_month = calendar.monthrange(
                    start_date.year,
                    start_date.month,
                )[1]
                return date(start_date.year, start_date.month, last_day_of_month)
            case RecurrenceTypeExternalId.QUARTERLY:
                last_month_of_quarter = (
                    get_first_month_of_quarter(get_quarter(start_date.month)) + 2
                )
                last_day_of_quarter = calendar.monthrange(
                    start_date.year,
                    last_month_of_quarter,
                )[1]
                return date(start_date.year, last_month_of_quarter, last_day_of_quarter)
            case (
                RecurrenceTypeExternalId.YEARLY
                | RecurrenceTypeExternalId.BIENALLY
                | RecurrenceTypeExternalId.TRIENNIALLY
                | RecurrenceTypeExternalId.QUINQUENNIALLY
            ):
                return start_date + timedelta(days=45)
            case RecurrenceTypeExternalId.CUSTOM:
                return start_date + timedelta(days=30)

    @staticmethod
    def _does_date_match_recurrence(recurrence: RecurrenceInstance, date: date) -> bool:
        match recurrence.recurrenceType.externalId:
            case RecurrenceTypeExternalId.DAILY:
                return True
            case RecurrenceTypeExternalId.WEEKLY if recurrence.weekDays is not None:
                return date.isoweekday() in recurrence.weekDays
            case RecurrenceTypeExternalId.MONTHLY if (
                recurrence.months is not None and recurrence.dayOfTheMonth is not None
            ):
                last_day_of_month = calendar.monthrange(date.year, date.month)[1]
                return (
                    date.day == recurrence.dayOfTheMonth
                    and date.month in recurrence.months
                ) or (
                    date.month in recurrence.months
                    and recurrence.dayOfTheMonth > last_day_of_month
                )
            case RecurrenceTypeExternalId.QUARTERLY if recurrence.quarters is not None:
                return get_quarter(date.month) in recurrence.quarters
            case (
                RecurrenceTypeExternalId.YEARLY
                | RecurrenceTypeExternalId.BIENALLY
                | RecurrenceTypeExternalId.TRIENNIALLY
                | RecurrenceTypeExternalId.QUINQUENNIALLY
            ):
                return (
                    date.day == recurrence.dayOfTheMonth
                    and date.month == recurrence.monthOfTheYear
                )
            case _:
                return False

    def _get_next_occurrence(
        self,
        recurrence: RecurrenceInstance,
        from_date: date,
    ) -> date | None:
        match recurrence.recurrenceType.externalId:
            case RecurrenceTypeExternalId.DAILY:
                return from_date + timedelta(days=1)
            case RecurrenceTypeExternalId.WEEKLY if recurrence.weekDays is not None:
                current_weekday = from_date.isoweekday()
                # assumes weekdays come in integers
                week_days = sorted(recurrence.weekDays)
                next_weekday = next(
                    filter(lambda x: x > current_weekday, week_days),
                    week_days[0],
                )
                next_weekday_is_in_same_week = next_weekday > current_weekday
                days_to_next_weekday = (
                    next_weekday - current_weekday
                    if next_weekday_is_in_same_week
                    else 7 - (current_weekday - next_weekday)
                )
                return from_date + timedelta(days=days_to_next_weekday)

            case RecurrenceTypeExternalId.MONTHLY if (
                recurrence.months is not None and recurrence.dayOfTheMonth is not None
            ):
                current_month = from_date.month
                current_day = from_date.day

                months = sorted(recurrence.months)  # 1-12
                day_of_the_month = recurrence.dayOfTheMonth

                date_is_in_same_month = (
                    current_month in months and day_of_the_month > current_day
                )
                if date_is_in_same_month:
                    last_day_of_month = calendar.monthrange(
                        from_date.year,
                        from_date.month,
                    )[1]
                    return from_date + timedelta(
                        days=((min(day_of_the_month, last_day_of_month)) - current_day),
                    )

                next_month = next(
                    filter(lambda x: x > current_month, months),
                    months[0],
                )
                next_month_is_in_same_year = next_month > current_month
                year = (
                    from_date.year if next_month_is_in_same_year else from_date.year + 1
                )
                last_day_of_next_month = calendar.monthrange(year, next_month)[1]
                return date(
                    year,
                    next_month,
                    (min(day_of_the_month, last_day_of_next_month)),
                )

            case RecurrenceTypeExternalId.QUARTERLY if recurrence.quarters is not None:
                current_month = from_date.month
                current_quarter = get_quarter(current_month)
                quarters = sorted(recurrence.quarters)  # 1-4

                next_quarter = next(
                    filter(lambda x: x > current_quarter, quarters),
                    quarters[0],
                )
                next_quarter_is_in_same_year = next_quarter > current_quarter
                year = (
                    from_date.year
                    if next_quarter_is_in_same_year
                    else from_date.year + 1
                )
                next_month = get_first_month_of_quarter(next_quarter)

                return date(year, next_month, 1)

            case (
                RecurrenceTypeExternalId.YEARLY
                | RecurrenceTypeExternalId.BIENALLY
                | RecurrenceTypeExternalId.TRIENNIALLY
                | RecurrenceTypeExternalId.QUINQUENNIALLY
            ) if (
                recurrence.dayOfTheMonth is not None
                and recurrence.monthOfTheYear is not None
            ):
                current_month = from_date.month
                current_day = from_date.day

                day_of_the_month = recurrence.dayOfTheMonth
                month_of_the_year = recurrence.monthOfTheYear

                date_is_in_same_year = (month_of_the_year > current_month) or (
                    month_of_the_year == current_month
                    and day_of_the_month > current_day
                )  # first trigger, if it's not in the same year, it may or may not be first trigger

                is_being_triggered = self._does_date_match_recurrence(
                    recurrence,
                    from_date,
                )  # if it's being triggered, next occurrence will not be the first trigger

                return (
                    date(from_date.year, month_of_the_year, day_of_the_month)
                    if date_is_in_same_year
                    else date(
                        from_date.year
                        + (
                            YEAR_FACTOR[recurrence.recurrenceType.externalId]
                            if is_being_triggered
                            else 1
                        ),
                        month_of_the_year,
                        day_of_the_month,
                    )
                )
            case _:
                return None

    async def _get_recurrent_action_items(self) -> list[dict[str, Any]]:
        action_item_filter = {
            "recurrenceInstance": {
                "startDate": {
                    "lte": str(self._today),
                },
            },
        }
        action_items = await self._graphql_service.get_all_results_list(
            generate_query(
                get_scheduled_action_items_query_list_name,
                get_scheduled_action_items_query_selection,
            ),
            get_scheduled_action_items_query_list_name,
            action_item_filter,
        )

        if len(action_items) == 0:
            return []

        status_and_links_filter = {
            "action": {
                "externalId": {
                    "in": [ai["externalId"] for ai in action_items],
                },
            },
        }

        approval_workflow_ids = [
            action_item["approvalWorkflow"]["externalId"]
            for action_item in action_items
            if action_item.get("approvalWorkflow") is not None
        ]

        approval_workflow_steps_filter = (
            {
                "approvalWorkflow": {
                    "externalId": {
                        "in": approval_workflow_ids,
                    },
                },
            }
            if len(approval_workflow_ids) > 0
            else None
        )

        links, approval_workflow_steps = await asyncio.gather(
            self._graphql_service.get_all_results_list(
                generate_query(
                    get_action_links_list_name,
                    get_action_links_query_selection,
                ),
                get_action_links_list_name,
                status_and_links_filter,
            ),
            (
                self._graphql_service.get_all_results_list(
                    generate_query(
                        get_approval_workflow_steps_list_name,
                        get_approval_workflow_steps_query_selection,
                    ),
                    get_approval_workflow_steps_list_name,
                    approval_workflow_steps_filter,
                )
                if approval_workflow_steps_filter is not None
                else self.return_empty_list()
            ),
        )

        links_by_action_id = {}
        approval_steps_by_approval_id = {}

        for link in links:
            action_id = link["action"]["externalId"]
            if action_id not in links_by_action_id:
                links_by_action_id[action_id] = []

            links_by_action_id[action_id].append(link)

        for approval_workflow_step in approval_workflow_steps:
            approval_workflow_id = approval_workflow_step["approvalWorkflow"][
                "externalId"
            ]
            if approval_workflow_id not in approval_steps_by_approval_id:
                approval_steps_by_approval_id[approval_workflow_id] = []

            approval_steps_by_approval_id[approval_workflow_id].append(
                approval_workflow_step,
            )

        action_items_to_proccess = []
        for action_item in action_items:
            external_id = action_item["externalId"]

            if (
                action_item.get("currentStatus") is None
                or action_item.get("currentStatus").get("externalId") != "ACTS-active"
                or action_item.get("isTemplate") is True
                or action_item.get("templateConfiguration") is not None
            ):
                continue

            action_item["links"] = links_by_action_id.get(external_id)
            action_item["statusHistory"] = []
            action_item["sourceEventHistory"] = []
            action_item["sourceEvent"] = []

            if action_item.get("approvalWorkflow") is not None:
                approval_workflow_id = action_item["approvalWorkflow"]["externalId"]
                if (
                    steps := approval_steps_by_approval_id.get(approval_workflow_id)
                ) is not None:
                    action_item["approvalWorkflow"]["steps"] = steps
                else:
                    action_item.pop("approvalWorkflow")
            action_item["attachments"] = (
                None
                if "attachments" not in action_item
                or action_item["attachments"] is None
                else [
                    attachment["externalId"]
                    for attachment in action_item["attachments"]
                ]
            )

            next_dates = action_item["recurrenceInstance"]["nextDates"]

            if (
                next_dates is None
                or len(next_dates) == 0
                or any(date.fromisoformat(nd) == self._today for nd in next_dates)
            ):
                action_items_to_proccess.append(action_item)

        return action_items_to_proccess

    async def _cleanup(self) -> None:
        await self._graphql_service.cleanup()

    @staticmethod
    async def return_empty_list() -> list:
        return []

    async def _get_user_email_by_user_azure_attribute_id(
        self,
        external_id: str | None,
    ) -> str:
        if external_id is None:
            return ""

        query_filter = {"externalId": {"eq": external_id}}

        user_azure_attributes = await self._graphql_service.get_all_results_list(
            generate_query(
                get_user_azure_attribute_query_list_name,
                get_user_azure_attribute_query_selection,
            ),
            get_user_azure_attribute_query_list_name,
            query_filter,
        )

        if len(user_azure_attributes) == 0:
            return ""

        first_user = user_azure_attributes[0]

        return first_user.get("user", {}).get("email", "")


get_scheduled_action_items_query_list_name = "listAction"

get_scheduled_action_items_query_selection = """
    externalId
    space
    application {
        externalId
        space
    }
    title
    description
    currentStatus {
        externalId
        space
    }
    approvalWorkflow {
        externalId
        space
        currentStep
        startDate
        createdBy {
            externalId
            space
        }
        status {
            externalId
            space
        }

    }
    assignees {
        items {
            externalId
            space
        }
    }
    attachments {
        externalId
    }
    owner {
        externalId
        space
    }
    createdBy {
        externalId
        space
    }
    actionItemKind {
        externalId
        space
    }
    reportingUnit {
        externalId
        space
    }
    reportingLocation {
        externalId
        space
    }
    sourceInformation
    category {
        externalId
        space
    }
    subCategory {
        externalId
        space
    }
    siteSpecificCategory {
        externalId
        space
    }
    recurrenceInstance {
        externalId
        space
        recurrenceType {
            externalId
            space
        }
        sourceEvent {
          externalId
          space
          actionsCount
        }
        startDate
        endDate
        dayOfTheMonth
        monthOfTheYear
        weekDays
        months
        quarters
        nextDates
    }
    voeActionItem
    estimatedCost
    price
    priceCurrencyKey
    estimatedGrade
    isPlantShutdownRequired
    actionTaken
    objectType
    objectExternalId
    reportingSite {
        externalId
        space
    }
    businessLine {
        externalId
        space
    }
    priority
    evidenceRequired
    isTemplate
    templateConfiguration {
        externalId
        space
    }
    sourceId
    sourceType {
        externalId
        space
    }
    isPrivate
    sortCategory
    sortSubCategory
    sortSiteSpecificCategory
    sortOwner
    sortReportingUnit
    sortReportingLocation
    sortAssignee
    sortApplication
    sortCurrentStatus
    sortApprover
    sortVerifier
    sortSourceEventTitle
    sortTitle
    sortSourceInformation
"""

get_action_status_list_name = "listStatusHistoryInstance"

get_action_status_query_selection = """
    externalId
    space
    action {
        externalId
        space
    }
    status {
        externalId
        space
    }
    changedAt
"""

get_action_links_list_name = "listActionItemLink"

get_action_links_query_selection = """
    externalId
    space
    action {
        externalId
        space
    }
    link
    description
"""

get_approval_workflow_steps_list_name = "listApprovalWorkflowStep"

get_approval_workflow_steps_query_selection = """
    externalId
    space
    approvalWorkflow {
        externalId
        space
    }
    step
    startDate
    description
    status {
        externalId
        space
    }
    approvalCondition {
        externalId
        space
    }
    users {
        items {
            externalId
            space
        }
    }
    approvalWorkflowConsentType {
        externalId
        space
    }
"""

get_user_azure_attribute_query_list_name = "listUserAzureAttribute"

get_user_azure_attribute_query_selection = """
    externalId
    user {
        email
    }
"""
