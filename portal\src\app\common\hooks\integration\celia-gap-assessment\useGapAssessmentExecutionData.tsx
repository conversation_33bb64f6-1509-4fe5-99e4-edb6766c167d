import { useEffect, useState } from 'react'
import { useGetAllResultsFunctionFromCustomClient } from '../../general-functions/useGetAllResultFunction'
import { Filter } from '@/app/common/models/base-hook-request'
import {
    GapAssessmentExecutionData,
    GapAssessmentResponse,
} from '@/app/common/models/integration/celia-gap-assessment/gap-assessment-execution-data'

const gapAssessmentExecutionDataQuery = `
    externalId
    space
    draftGapActions {
        items {
            externalId
            space
            categoryID
        }
    }
    requirement
    verification
    systemInPlace
    gapDetails
    gapAssessmentExecutions {
        items {
            externalId
            space
            topic {
                items {
                    externalId
                    space
                    name
                    assessmentCategory {
                        externalId
                        space
                        categoryName
                    }
                }
            }
        }
    }
`

export const useGapAssessmentExecutionData = (externalId?: string) => {
    const [resultData, setResultData] = useState<{ data: GapAssessmentResponse[]; loading: boolean }>({
        data: [],
        loading: true,
    })

    const { getAllItems: getExecutionData } = useGetAllResultsFunctionFromCustomClient<GapAssessmentExecutionData>(
        gapAssessmentExecutionDataQuery,
        'listGapAssessmentExecutionData',
        'gapAssessment'
    )

    const filter: Filter<GapAssessmentExecutionData> = {
        externalId: { eq: externalId ?? '' },
    }

    useEffect(() => {
        if (!externalId) return

        const fetchExecutionData = async () => {
            try {
                const executionData = await getExecutionData(filter)

                if (!executionData.length) {
                    setResultData({ data: [], loading: false })
                    return
                }

                const enrichedData = executionData.map((item: any) => {
                    const topic = item.gapAssessmentExecutions?.items[0]?.topic?.items[0]

                    return {
                        ...item,
                        categoryName: topic?.assessmentCategory?.categoryName,
                        topicName: topic?.name,
                    }
                })

                setResultData({ data: enrichedData, loading: false })
            } catch (error) {
                setResultData({ data: [], loading: false })
            }
        }

        fetchExecutionData()
    }, [externalId])

    return resultData
}
