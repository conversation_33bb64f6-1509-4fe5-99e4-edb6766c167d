import json
import os
import re
import sys
from collections.abc import <PERSON><PERSON><PERSON>
from datetime import UTC, date, datetime, timed<PERSON>ta
from http import HTTPStatus
from io import BytesIO
from typing import Any, Optional, cast, overload
from xmlrpc.client import Boolean

import azure.functions as func
import pandas as pd
from cognite.client.data_classes.data_modeling import EdgeId, NodeId
from openpyxl import Workbook
from pandas import Series

from clients.action_item_client import ActionItemClient
from clients.actions._models import (
    _SourceEvent,
    _User,
    _UserAzureAttribute,
)
from clients.actions.constants import (
    ACTION_EXPORT_FIELD_MAP,
    STATUS_ID_TO_STATUS_NAME,
    BulkUploadErrorMessageTypes,
    NotificationTypes,
)
from clients.actions.models import (
    ActionByIdForEditingResult,
    ActionByIdResult,
    ActionExportResponse,
    ActionExportResult,
    ActionItemChangeRequest,
    ActionItemForAssigneeRequestProcess,
    ActionItemLink,
    ActionItemUpdate,
    ActionResult,
    ChangeRequest,
    RecurrenceInstance,
    StatusHistoryInstance,
)
from clients.actions.requests import (
    ActionEditNotificationRequest,
    CancelActionRequest,
    DeleteActionRequest,
    ExportActionsRequest,
    FormLink,
    GetActionByIdRequest,
    GetActionRequest,
    GetActionRequestForIndustrialModel,
    RecurrenceInstanceRequest,
    UpdateActionApprovalWorkflowRequest,
    UpdateActionAssigneeRequest,
    UpdateActionRequest,
)
from clients.approval_workflow.constants import (
    ApprovalWorkflowCondition,
    ApprovalWorkflowConsentType,
    HistoryActionUser,
    WorkflowStatus,
)
from clients.approval_workflow.models import (
    ApprovalWorkflowStepUpdate,
    ApprovalWorkflowUpdate,
)
from clients.approval_workflow.requests import GetApprovalWorkflowRequest
from clients.category_configuration.models import (
    CategoryConfigurationByFilterResult,
    CategoryResult,
    SiteSpecificCategoryResult,
    SubCategoryResult,
)
from clients.category_configuration.requests import (
    GetCategoriesRequest,
    GetCategoryConfigurationByFilterRequest,
    GetSiteSpecificCategoriesRequest,
    GetSubCategoriesRequest,
)
from clients.core.constants import (
    AGGREGATE_LIMIT,
    USER_AZURE_ATTRIBUTE_PREFIX,
    WAS_SITE_EXTERNAL_ID,
    ActionStatusEnum,
    ApprovalWorkflowDescriptionEnum,
    ApprovalWorkflowStatusEnum,
    ApprovalWorkflowStepDescriptionEnum,
    ChangeRequestTypeEnum,
    DataSpaceEnum,
    KpiNameEnum,
)
from clients.core.models import Node, PaginatedData
from clients.core.utils import to_user_azure_attribute
from clients.reporting_location.models import ReportingLocationResult
from clients.reporting_location.requests import GetReportingLocationsRequest
from clients.reporting_unit.models import ReportingUnitResult
from clients.reporting_unit.requests import GetReportingUnitsRequest
from clients.sort_mapper import GetSortMapperRequest, SortMapper
from clients.user.requests import GetUserByIdRequest
from clients.user_complement.models import UsersBySiteResult
from clients.user_complement.requests import (
    GetUserRolesAndTeamsRequest,
    GetUsersRequest,
)
from models.entities_enum import EntitiesEnum
from models.notification import Notification
from services.approval_workflow_service import ApprovalWorkflowService
from services.category_configuration_service import CategoryConfigurationService
from services.user_complements_service import UserByEmailResult, UserComplementsService
from utils.id_generation_utils import IdGenerator
from utils.space_utils import get_transactional_space_from_site_id

script_dir = os.path.dirname(os.path.abspath(__file__))
project_dir = os.path.dirname(script_dir)
sys.path.append(project_dir)


class ActionService:
    """Service for managing actions."""

    def __init__(
        self,
        action_item_client: ActionItemClient,
    ) -> None:
        """
        Initialize the ActionService.

        Args:
            action_item_client (ActionItemClient): Client for interacting with action items.

        """
        self._action_item_client = action_item_client
        self._notification_service = action_item_client.actions._notification_service
        self._log = action_item_client.actions._logging
        self._now_datetime = datetime.now(tz=UTC)
        self._now_datetime_str = self._now_datetime.date().strftime("%Y-%m-%d")
        self._id_generator = IdGenerator[EntitiesEnum](self._now_datetime)

    async def get_all_actions_by_site(
        self,
        request: GetActionRequest,
    ) -> list[ActionResult]:
        """
        Get all actions for a site using pagination.

        This method retrieves all available actions by repeatedly calling
        `get_actions_by_site` while pagination is available. It accumulates the
        results into a single list and returns it.

        Args:
            request (GetActionRequest): The request object containing filters
                and parameters for querying actions. The `cursor` field will be
                updated internally for pagination.

        Returns:
            list[ActionResult]: A list of all actions retrieved across pages.

        """
        actions: list[ActionResult] = []
        cursor = None

        while True:
            request.cursor = cursor
            response = await self.get_actions_by_site(request)
            actions.extend(response[0].data)
            if not response[0].has_next_page or cursor is None:
                break
            cursor = response[0].cursor

        return actions

    @overload
    async def get_request_configurations(
        self,
        request: GetActionRequest,
    ) -> GetActionRequest: ...

    @overload
    async def get_request_configurations(
        self,
        request: GetActionRequestForIndustrialModel,
    ) -> GetActionRequestForIndustrialModel: ...

    async def get_request_configurations(
        self,
        request: GetActionRequest | GetActionRequestForIndustrialModel,
    ) -> GetActionRequest | GetActionRequestForIndustrialModel:
        """
        Enrich the action request with user, category, and workflow information based on provided filters.

        This method updates the request with additional data such as:
            - User identifiers and role/team IDs (if `active_user_email` is provided).
            - Category configurations (if `kpi_filter` requires them).
            - Approval workflow nodes (if `kpi_filter` involves approvals or verifications).

        Args:
            request (GetActionRequest | GetActionRequestForIndustrialModel):
                Request object containing action filters and user context.

        Returns:
            GetActionRequest | GetActionRequestForIndustrialModel:
                The same request object enriched with user data, category configurations,
                and approval workflow information.

        Raises:
            ValueError: If `active_user_email` is provided but no user is found.

        """
        if request.active_user_email:
            user = await self._get_user_by_email(
                request.active_user_email,
                request.reporting_site_external_ids,
            )
            if user is None:
                msg = "User not found"
                raise ValueError(msg)

            request.active_user = user.external_id
            request.active_user_roles_ids = user.active_user_roles_ids
            request.active_user_teams_ids = user.active_user_teams_ids
            active_user_id = user.user_external_id or "-"
        elif request.active_user:
            active_user_id = request.active_user.replace(
                USER_AZURE_ATTRIBUTE_PREFIX,
                "",
            )
        else:
            active_user_id = "-"

        if request.kpi_filter in {
            KpiNameEnum.RELATED_TO_ME,
            KpiNameEnum.OVERDUE,
            KpiNameEnum.MY_EXTENDS,
        }:
            category_service = CategoryConfigurationService(self._action_item_client)
            request.category_configurations = (
                category_service.get_category_configurations_by_site(
                    GetCategoryConfigurationByFilterRequest(
                        reporting_site_external_id=request.reporting_site_external_ids,
                        non_null_extension_approver_role=True,
                        category_id=None,
                        sub_category_id=None,
                        site_specific_category_id=None,
                    ),
                )
            )

        if request.kpi_filter in {
            KpiNameEnum.RELATED_TO_ME,
            KpiNameEnum.OVERDUE,
            KpiNameEnum.MY_APPROVALS,
            KpiNameEnum.MY_VERIFICATIONS,
        }:
            workflow_service = ApprovalWorkflowService(self._action_item_client)
            workflows = workflow_service.get_approval_workflows(
                GetApprovalWorkflowRequest(
                    user_external_ids=[active_user_id],
                    reporting_site_external_id=request.reporting_site_external_ids,
                    approval_workflow_step_status_external_ids=[
                        ApprovalWorkflowStatusEnum.PROGRESS,
                        ApprovalWorkflowStatusEnum.PENDING,
                    ],
                ),
            )
            request.approval_workflow_nodes = workflows
        return request

    async def get_actions_by_site(
        self,
        request: GetActionRequest,
    ) -> tuple[PaginatedData[ActionResult], int]:
        """
        Retrieve paginated actions and their total count based on site and user filters.

        Args:
            request (GetActionRequest): Parameters to filter and fetch actions.

        Returns:
            tuple[PaginatedData[ActionResult], int]:
                - PaginatedData[ActionResult]: Paginated actions data.
                - int: Total number of actions matching the filters.

        """
        request = await self.get_request_configurations(request)

        actions = self._action_item_client.actions.get_actions(request)
        total = self._action_item_client.actions.count_actions(request)

        return actions, total

    async def get_action_by_id(
        self,
        request: GetActionByIdRequest,
    ) -> tuple[
        Optional[
            ActionByIdResult
            | ActionByIdForEditingResult
            | ActionItemForAssigneeRequestProcess
        ],
        dict[str, bool],
    ]:
        """
        Retrieve an action item by its ID using the action item client.

        This method first ensures that the active user is set, retrieving their details if necessary.
        It then fetches the action data and determines the permissions associated with the user.

        Args:
            request (GetActionByIdRequest): The request object containing the external ID of the action
                                            and user details for access validation.

        Returns:
            tuple: A tuple containing:
                - Optional[ActionByIdResult | ActionByIdForEditingResult | ActionItemForAssigneeRequestProcess]: The action details if found, otherwise None.
                - dict[str, bool]: A dictionary with permission flags indicating whether the user can
                  "edit", "delete", or "view" the action.

        """
        try:
            if not request.active_user:
                user = await self._get_user_by_email(
                    request.active_user_email or "",
                    request.reporting_site_external_id or "",
                )
                if not user:
                    return None, {}
                (
                    request.active_user,
                    request.active_user_roles_ids,
                    request.active_user_teams_ids,
                ) = (
                    user.external_id,
                    user.active_user_roles_ids,
                    user.active_user_teams_ids,
                )

            actions = self._action_item_client.actions.get_action_by_id(request)
            if not actions.data:
                return None, {}

            action = actions.data[0]
            permissions = (
                self._compute_permissions(request, action)
                if not isinstance(action, ActionItemForAssigneeRequestProcess)
                else {}
            )

        except Exception as err:
            self._log.error(
                f"Error getting action item by ID: {request.external_id}. Error: {err}",
            )
            return None, {}

        return action, permissions

    def _compute_permissions(
        self,
        request: GetActionByIdRequest,
        action_by_id: ActionByIdResult | ActionByIdForEditingResult,
    ) -> dict[str, bool]:
        """Compute the permissions for the action based on the user's role."""
        is_owner = self._get_external_id(action_by_id.owner) == request.active_user

        has_source_events = bool(action_by_id.source_events)

        is_event_owner = (
            has_source_events
            and self._get_external_id(action_by_id.source_events[0].owner)
            == request.active_user
        )

        is_event_secondary_owner = (
            has_source_events
            and self._check_secondary_ownership(
                action_by_id.source_events[0],
                request,
            )
        )

        owner_can_edit_and_delete = action_by_id.current_status.external_id in [
            ActionStatusEnum.ASSIGNED,
            ActionStatusEnum.CHALLENGE_PERIOD,
        ]

        return {
            "edit": (is_owner or is_event_owner or is_event_secondary_owner)
            and owner_can_edit_and_delete
            and not action_by_id.recurrence_instance
            and not action_by_id.is_template,
            "delete": (is_owner or is_event_owner or is_event_secondary_owner)
            and owner_can_edit_and_delete,
            "view": True,
        }

    def _get_external_id(
        self,
        owner: Optional[_UserAzureAttribute | Node],
    ) -> Optional[str]:
        """Return the external ID of the owner if available."""
        return owner.external_id if owner else None

    def _check_secondary_ownership(
        self,
        event: _SourceEvent,
        request: GetActionByIdRequest,
    ) -> bool:
        """Check if the user has secondary ownership permissions."""
        secondary_users = [
            self._get_external_id(user)
            for user in event.secondary_owner_users or []
            if user
        ]
        secondary_roles = [
            role.external_id for role in event.secondary_owner_roles or [] if role
        ]
        secondary_teams = [
            team.external_id for team in event.secondary_owner_teams or [] if team
        ]

        return (
            request.active_user in secondary_users
            or any(
                role in secondary_roles for role in request.active_user_roles_ids or []
            )
            or any(
                team in secondary_teams for team in request.active_user_teams_ids or []
            )
        )

    async def delete_actions(
        self,
        request: DeleteActionRequest,
    ) -> tuple[list[str], dict]:
        """
        Delete multiple action items by marking them as "DELETED" in the system.

        This method updates the status of action items to "DELETED" while keeping track of their status history.
        The user performing the action must be authenticated and authorized to update the items.

        Args:
            request (DeleteActionRequest): The request object containing necessary information to delete action items,
                including the external IDs of the actions and the active user's email.

        Returns:
            tuple:
                - external_ids (list[str]): The external IDs of the actions that were processed.
                - errors (dict): A dictionary containing any internal errors encountered during the process.

        Raises:
            Exception: If any unexpected error occurs during the process, it will be logged, and the request will be returned
                with an empty error response.

        Details:
            1. Retrieves the active user based on the provided email and reporting site.
            2. Returns an error if the user is not found.
            3. Fetches the action items based on the provided external IDs and reporting site.
            4. Updates each action item with a "DELETED" status and adds an entry to the status history.
            5. Upserts the action items to reflect the changes.
            6. Returns an error message if any action items cannot be found or updated.

        Example:
            # To delete action items by their external IDs
            request = DeleteActionRequest(
                external_ids=["ACT-20241210164044-0001", "ACT-20241210164044-0002"],
                active_user_email="<EMAIL>",
                reporting_site_external_id="STS-WAS",
            )
            external_ids, errors = await action_service.delete_actions(request)

        """
        try:
            user = await self._get_user_by_email(
                request.active_user_email,
                request.reporting_site_external_id,
            )

            if user is None:
                return request.external_ids, {"internal": ["User not found"]}

            update_actions: list[ActionItemUpdate] = []
            get_actions_by_ids = GetActionRequest(
                external_ids=request.external_ids,
                reporting_site_external_id=request.reporting_site_external_id,
                active_user=user.external_id,
                active_user_roles_ids=user.active_user_roles_ids,
                active_user_teams_ids=user.active_user_teams_ids,
                page_size=1000,
            )

            actions: PaginatedData[ActionResult] = (
                self._action_item_client.actions.get_actions(get_actions_by_ids)
            )

            sort_mapper = self._action_item_client.sort_mapper.get_sort_mapper()

            for action in actions.data:
                history = self.generate_upsert_status_history_instance(
                    external_id=action.external_id,
                    space=action.space,
                    status_id=ActionStatusEnum.DELETED,
                    action_user=HistoryActionUser.DELETED,
                    comment="",
                    created_by_id=user.external_id,
                )

                update_actions.append(
                    self.generate_upsert_action(
                        external_id=action.external_id,
                        space=action.space,
                        status_id=ActionStatusEnum.DELETED,
                        history=[history],
                        sort_mapper=sort_mapper,
                    ),
                )

            self._log.info(f"Upserting {len(update_actions)} action items.")
            self._action_item_client.actions.upsert_action_item(update_actions)
        except Exception as err:
            self._log.error(
                f"Error delete actions by ids: {request.external_ids}. Error: {err}",
            )
            return request.external_ids, {}
        else:
            return request.external_ids, {}

    async def cancel_actions(
        self,
        request: CancelActionRequest,
    ) -> tuple[list[str], dict]:
        """
        Cancel multiple action items by marking them as "CANCELLED" in the system.

        This method performs the cancellation of action items by updating their status to "CANCELLED",
        while keeping track of the status history. The user performing the action must be authenticated
        and authorized to update the items.

        Args:
            request (CancelActionRequest): The request object containing necessary information
                to cancel action items, such as the external IDs of the actions, the active user's email,
                and a comment explaining the cancellation.

        Returns:
            tuple: A tuple containing:
                - `external_ids` (list[str]): The external IDs of the actions that were processed.
                - `errors` (dict): A dictionary containing any internal errors encountered during the process.

        Raises:
            Exception: If any unexpected error occurs during the process, it will be logged and the request will be returned with an empty error response.

        Details:
            1. The function retrieves the active user based on the provided email and reporting site.
            2. If the user is not found, the function returns an error.
            3. The function fetches the action items based on the provided external IDs and reporting site.
            4. Each action item is updated with a "CANCELLED" status and a status history entry.
            5. The action items are then upserted to reflect the changes.
            6. If any actions cannot be found or updated, the function will return an error message.

        Example:
            # To cancel action items by their external IDs
            request = CancelActionRequest(
                external_ids=["ACT-20241210164044-0001", "ACT-20241210164044-0002"],
                active_user_email="<EMAIL>",
                reporting_site_external_id="STS-WAS",
                comments="Actions canceld as part of workflow cleanup."
            )
            external_ids, errors = await action_service.cancel_actions(request)

        """
        try:
            user = await self._get_user_by_email(
                request.active_user_email,
                request.reporting_site_external_id,
            )

            if user is None:
                return request.external_ids, {"internal": ["User not found"]}

            update_actions: list[ActionItemUpdate] = []
            update_change_requests: list[ActionItemChangeRequest] = []
            get_actions_by_ids = GetActionRequest(
                external_ids=request.external_ids,
                reporting_site_external_id=request.reporting_site_external_id,
                active_user=user.external_id,
                active_user_roles_ids=user.active_user_roles_ids,
                active_user_teams_ids=user.active_user_teams_ids,
                page_size=1000,
            )

            actions: PaginatedData[ActionResult] = (
                self._action_item_client.actions.get_actions(get_actions_by_ids)
            )

            sort_mapper = self._action_item_client.sort_mapper.get_sort_mapper()

            for action in actions.data:
                history = self.generate_upsert_status_history_instance(
                    external_id=action.external_id,
                    space=action.space,
                    status_id=ActionStatusEnum.CANCELLED,
                    action_user=HistoryActionUser.CANCELLED,
                    comment=request.comments,
                    created_by_id=user.external_id,
                )

                update_actions.append(
                    self.generate_upsert_action(
                        external_id=action.external_id,
                        space=action.space,
                        status_id=ActionStatusEnum.CANCELLED,
                        history=[history],
                        sort_mapper=sort_mapper,
                    ),
                )

                if request.challenge_request:
                    request_challenge = UpdateActionAssigneeRequest(
                        external_id=action.external_id,
                        space=action.space,
                        active_user_email=request.active_user_email,
                        reporting_site_external_id=request.reporting_site_external_id,
                        challenge_status=WorkflowStatus.REJECTED.value,
                        approver_email=None,
                        reassign_comment=None,
                        assignee_comment=None,
                        extension_comment=None,
                        new_assignee=None,
                        new_due_date=None,
                        new_files_ids=None,
                    )

                    update_change_request, _ = self._process_challenge(
                        action=action,
                        request=request_challenge,
                        created_by_id=user.external_id,
                    )
                    update_change_requests.append(update_change_request)

            self._log.info(f"Upserting {len(update_actions)} action items.")
            self._action_item_client.actions.upsert_action_item(update_actions)
            if update_change_requests and len(update_change_requests) > 0:
                self._action_item_client.actions.upsert_change_request(
                    update_change_requests,
                )
        except Exception as err:
            self._log.error(
                f"Error cancel actions by ids: {request.external_ids}. Error: {err}",
            )
            return request.external_ids, {}
        else:
            return request.external_ids, {}

    async def update_action_assignee_request(
        self,
        request: UpdateActionAssigneeRequest,
    ) -> tuple[str, dict[str, list[str]]]:
        r"""
        Update the assignee or due date of an action item based on the provided request.

        This function handles the reassignment or due date extension of an action item.
        It validates the requesting user, retrieves the relevant action item, processes the requested changes,
        and performs the necessary updates. Notifications are also managed when applicable.

        Args:
            request (UpdateActionAssigneeRequest):
                The request object containing the details for the update, including:
                - `external_id` (str): The external ID of the action item to update.
                - `reporting_site_external_id` (str): The reporting site context.
                - `active_user_email` (str): The email of the user initiating the request.
                - `new_assignee` (Optional[str]): The new assignee details (if applicable).
                - `new_due_date` (Optional[str]): The new due date details (if applicable).
                - `assignee_comment` (Optional[str]): Additional comments from the assignee.
                - `new_files_ids` (list[str]): IDs of files to attach to the update.

        Returns:
            tuple[str, dict[str, list[str]]]:
                - The `external_id` of the action item that was updated.
                - A dictionary of errors by action type, if any occurred during the process.

        Raises:
            Exception: Logs and returns errors encountered during the update process.

        Process Flow:
            1. Validates the requesting user via `_get_user_by_email`.
            2. Retrieves the action item using `get_action_by_id`.
            3. Determines the type of update (reassignment or due date extension) and processes the request.
            4. Performs the update using the `_action_item_client` and logs the operation.
            5. Manages notifications if an assignee comment is provided.
            6. Logs and handles any errors encountered.

        Example:
            request = UpdateActionAssigneeRequest(
                external_id="ACT-20241203184516-0001",
                reporting_site_external_id="STS-WAS",
                active_user_email="<EMAIL>",
                new_assignee="{\"newAssignee\": \"<EMAIL>\"}",
                new_due_date="{\"newDueDate\": \"2024-12-31\"}",
                assignee_comment="Updated due to priority change",
                new_files_ids=["file123", "file456"]
            )

            result = await update_action_assignee_request(request)
            print(result)
            # Output: ("ACT-20241203184516-0001", {})

        """
        try:
            user = await self._get_user_by_email(
                request.active_user_email,
                request.reporting_site_external_id,
            )

            if user is None:
                return request.external_id, {"internal": ["User not found"]}

            errors_by_action = {}
            created_by_id: str = user.external_id

            action_by_id_request = GetActionByIdRequest(
                external_id=request.external_id,
                reporting_site_external_id=request.reporting_site_external_id,
                active_user=user.external_id,
                active_user_roles_ids=user.active_user_roles_ids,
                active_user_teams_ids=user.active_user_teams_ids,
                is_assignee_request_process=Boolean(request.new_assignee),
            )

            action, _ = await self.get_action_by_id(action_by_id_request)

            if action is None:
                return request.external_id, {"internal": ["No result or no access"]}

            update_change_request: ActionItemChangeRequest
            user_to_notify: list[str]
            if request.new_assignee:
                update_change_request, user_to_notify = self._process_reassigment(
                    action=cast("ActionItemForAssigneeRequestProcess", action),
                    request=request,
                    created_by_id=created_by_id,
                )
            elif request.new_due_date:
                update_change_request, user_to_notify = self._process_extension(
                    action=cast("ActionByIdResult", action),
                    request=request,
                    created_by_id=created_by_id,
                )
            else:
                update_change_request, user_to_notify = self._process_challenge(
                    action=cast("ActionByIdResult", action),
                    request=request,
                    created_by_id=created_by_id,
                )
            try:
                self._log.info(f"Upserting {1} action item.")
                update_change_request.attatchments = request.new_files_ids
                self._action_item_client.actions.upsert_change_request(
                    [update_change_request],
                )

                if request.assignee_comment and user_to_notify:
                    try:
                        new_assignee_id = (
                            request.new_assignee
                            if request.new_assignee and not request.extension_status
                            else None
                        )

                        new_due_date = (
                            request.new_due_date
                            if request.new_due_date and not request.reassign_status
                            else None
                        )

                        notification_data = self._build_notification_data(
                            (
                                update_change_request.action.current_status.external_id
                                if update_change_request.action.current_status
                                else ""
                            ),
                            action,
                            request.reporting_site_external_id,
                            user_to_notify,
                            new_assignee_id,
                            new_due_date,
                        )

                        self._notification_service.send_notifications(
                            notifications=[notification_data],
                        )

                    except Exception as err:
                        self._log.error(f"Could not send notifications. Error: {err}")

            except Exception as err:
                self._log.error(f"Could not upsert change request. Error: {err}")
                return request.external_id, {"internal": [str(err)]}
        except Exception as err:
            self._log.error(
                f"Error updating change request by external id: {request.external_id}. Error: {err}",
            )
            return request.external_id, {}
        else:
            return request.external_id, errors_by_action

    async def get_aggregate_actions_by_source_id_by_site(
        self,
        request: GetActionRequest,
    ) -> list[dict[str, Any]]:
        """
        Retrieve actions by site along with their total count.

        Args:
            request (GetActionRequest): The request object containing parameters to filter the actions.

        Returns:
            int: The total count of actions matching the specified criteria.
                Returns 0 if an error occurs.

        """
        try:
            total = await self._action_item_client.actions.count_actions_by_source_id(
                request,
            )
        except Exception as err:
            self._log.error(
                f"Error getting action items by site: {request.reporting_site_external_id}. Error: {err}",
            )
            return [{}]
        else:
            return total

    async def update_action_approval_workflow(
        self,
        request: UpdateActionApprovalWorkflowRequest,
    ) -> tuple[str, dict]:
        """
        Update the approval workflow for an action, applying assignee, approver, and verifier updates as needed.

        Args:
            request (UpdateActionApprovalWorkflowRequest): The request object containing the data required to update the
                action's approval workflow, including comments and IDs for assignee, approver, and verifier roles.

        Returns:
            tuple[str, dict]:
                - The external ID of the action.
                - A dictionary containing any errors encountered during processing, if any.

        Method Details:
            1. Retrieves the action by its external ID and reporting site ID.
            2. Fetches the category configuration based on the action's category, subcategory, and site-specific category.
            3. Applies updates based on the request's assignee, approver, and verifier comments, invoking respective processing methods.
            4. Upserts the action item with the updated workflow, handling any errors gracefully and logging issues.

        Raises:
            Exception: Logs any exceptions that occur during the upsert operation and returns the error in a dictionary with the action's external ID.

        """
        self._log.info(
            f"Starting to update action approval workflow with ID: {request.external_id}",
        )

        user = await self._get_user_by_email(
            request.active_user_email,
            request.reporting_site_external_id,
        )

        if user is None:
            return request.external_id, {"internal": ["User not found"]}

        errors_by_action = {}
        created_by_id: str = user.external_id

        get_action_id = GetActionByIdRequest(
            external_id=request.external_id,
            reporting_site_external_id=request.reporting_site_external_id,
            active_user=user.external_id,
            active_user_roles_ids=user.active_user_roles_ids,
            active_user_teams_ids=user.active_user_teams_ids,
        )

        action, _ = await self.get_action_by_id(get_action_id)

        if action is None or not isinstance(action, ActionByIdResult):
            return request.external_id, {"internal": ["No result or no access"]}

        category_configuration = self._get_category_configuration(
            request.reporting_site_external_id,
            action.category.external_id,
            (
                action.sub_category.external_id
                if action.sub_category is not None
                else "-"
            ),
            (
                action.site_specific_category.external_id
                if action.site_specific_category is not None
                else None
            ),
        )

        users_to_notify: list[str] = []
        update_action: ActionItemUpdate | None = None
        if request.assignee_comment:
            update_action, users_to_notify = self._process_assignee(
                cast("ActionByIdResult", action),
                request,
                created_by_id,
                category_configuration,
            )

        if request.approver_comment:
            update_action, users_to_notify = self._process_approver(
                cast("ActionByIdResult", action),
                request,
                created_by_id,
                category_configuration,
            )

        if request.verifier_comment:
            update_action, users_to_notify = self._process_verifier(
                cast("ActionByIdResult", action),
                request,
                created_by_id,
            )

        assert update_action is not None

        try:
            self._log.info(f"Upserting {1} action item.")
            update_action.attachments = list(
                set(
                    (request.new_files_ids or [])
                    + [
                        attachment.external_id
                        for attachment in action.attachments or []
                    ],
                ),
            )
            self._action_item_client.actions.upsert_action_item([update_action])

            if users_to_notify:
                try:
                    notification_data = self._build_notification_data(
                        (
                            update_action.current_status.external_id
                            if update_action.current_status
                            else ""
                        ),
                        action,
                        request.reporting_site_external_id,
                        users_to_notify,
                    )

                    self._notification_service.send_notifications(
                        notifications=[notification_data],
                    )
                except Exception as err:
                    self._log.error(f"Could not send notifications. Error: {err}")

        except Exception as err:
            self._log.error(f"Could not upsert action item. Error: {err}")
            return request.external_id, {"internal": [str(err)]}
        else:
            return request.external_id, errors_by_action

    async def update_action(
        self,
        request: UpdateActionRequest,
    ) -> tuple[str, dict[str, Any]]:
        """
        Update the action, applying all updates to the fields based on user changes.

        Args:
            request (UpdateActionRequest): The request object containing the data required to update the action.

        Returns:
            tuple[str, dict[str, Any]]:
                - The external ID of the action.
                - A dictionary containing any errors encountered during processing, if any.

        Method Details:
            1. Retrieves the action by its external ID and reporting site ID.
            2. Fetches the category configuration based on the action's category, subcategory, and site-specific category.
            3. Applies updates based on the request's assignee, approver, and verifier comments, invoking respective processing methods.
            4. Upserts the action item with the updated information, handling errors gracefully and logging any issues.

        Raises:
            Exception: Logs any exceptions that occur during the upsert operation and returns the error in a dictionary with the action's external ID.

        """
        self._log.info(f"Starting to update action with ID: {request.external_id}")

        user = await self._get_user_by_email(
            request.active_user_email,
            request.reporting_site_id,
        )

        if user is None:
            return request.external_id, {"internal": ["User not found"]}

        errors_by_action: dict[str, Any] = {}

        get_action_id = GetActionByIdRequest(
            external_id=request.external_id,
            reporting_site_external_id=request.reporting_site_id,
            active_user=user.external_id,
            active_user_roles_ids=user.active_user_roles_ids,
            active_user_teams_ids=user.active_user_teams_ids,
        )

        action, _ = await self.get_action_by_id(get_action_id)

        if action is None or not isinstance(action, ActionByIdResult):
            return request.external_id, {"internal": ["No result or no access"]}

        history = self.generate_upsert_status_history_instance(
            external_id=action.external_id,
            space=action.space,
            status_id=action.current_status.external_id,
            action_user=HistoryActionUser.EDITED,
            comment="",
            created_by_id=user.external_id,
        )

        user_azure_attribute_external_ids: list[str] = []

        nodes_to_delete: list[NodeId] = []
        edges_to_delete: list[EdgeId] = []

        approval_step = None
        verification_step = None
        if (
            action.approval_workflow is not None
            and action.approval_workflow.steps is not None
        ):
            approval_step = next(
                (
                    step
                    for step in action.approval_workflow.steps
                    if step.description == ApprovalWorkflowStepDescriptionEnum.APPROVAL
                ),
                None,
            )
            verification_step = next(
                (
                    step
                    for step in action.approval_workflow.steps
                    if step.description
                    == ApprovalWorkflowStepDescriptionEnum.VERIFICATION
                ),
                None,
            )

        steps: list[ApprovalWorkflowStepUpdate] = []
        if request.approver_id.was_updated:
            if request.approver_id.value is None:
                if approval_step is not None:  # remove
                    nodes_to_delete.append(approval_step.to_node_id())
            else:  # upsert
                approver_id = request.approver_id.value
                user_azure_attribute_external_ids.append(approver_id)
                step_approval = self._generate_upsert_approval_workflow_step(
                    external_id=approval_step.external_id if approval_step else None,
                    space=action.space,
                    start_date=None if approval_step else self._now_datetime_str,
                    end_date=None if approval_step else self._now_datetime_str,
                    description=ApprovalWorkflowStepDescriptionEnum.APPROVAL,
                    status=(
                        ApprovalWorkflowStatusEnum.PROGRESS
                        if action.current_status.external_id
                        == ActionStatusEnum.PENDING_APPROVAL
                        else ApprovalWorkflowStatusEnum.PENDING
                    ),
                    user=approver_id,
                )
                steps.append(step_approval)

                if (
                    approval_step is not None
                    and approval_step.users is not None
                    and approval_step.users[0].external_id != approver_id
                    and approval_step.users[0].edge_external_id is not None
                ):
                    edges_to_delete.append(
                        EdgeId(
                            external_id=approval_step.users[0].edge_external_id,
                            space=action.space,
                        ),
                    )
        elif (
            approval_step is not None
            and approval_step.users is not None
            and request.verifier_id.was_updated
        ):  # always send full approval workflow, even if approval step was not changed but verification step was
            step_approval = self._generate_upsert_approval_workflow_step(
                external_id=approval_step.external_id,
                space=action.space,
                description=ApprovalWorkflowStepDescriptionEnum.APPROVAL,
                status=(
                    ApprovalWorkflowStatusEnum.PROGRESS
                    if action.current_status.external_id
                    == ActionStatusEnum.PENDING_APPROVAL
                    else ApprovalWorkflowStatusEnum.PENDING
                ),
                user=approval_step.users[0].external_id,
            )
            steps.append(step_approval)

        if request.verifier_id.was_updated:
            if request.verifier_id.value is None:
                if verification_step is not None:  # remove
                    nodes_to_delete.append(verification_step.to_node_id())
            else:  # upsert
                verifier_id = request.verifier_id.value
                user_azure_attribute_external_ids.append(verifier_id)
                step_verification = self._generate_upsert_approval_workflow_step(
                    external_id=(
                        verification_step.external_id if verification_step else None
                    ),
                    space=action.space,
                    start_date=None if verification_step else self._now_datetime_str,
                    end_date=None if verification_step else self._now_datetime_str,
                    description=ApprovalWorkflowStepDescriptionEnum.VERIFICATION,
                    status=(
                        ApprovalWorkflowStatusEnum.PROGRESS
                        if action.current_status.external_id
                        == ActionStatusEnum.PENDING_VERIFICATION
                        else ApprovalWorkflowStatusEnum.PENDING
                    ),
                    user=verifier_id,
                )
                steps.append(step_verification)

                if (
                    verification_step is not None
                    and verification_step.users is not None
                    and verification_step.users[0].external_id != verifier_id
                    and verification_step.users[0].edge_external_id is not None
                ):
                    edges_to_delete.append(
                        EdgeId(
                            external_id=verification_step.users[0].edge_external_id,
                            space=action.space,
                        ),
                    )
        elif (
            verification_step is not None
            and verification_step.users is not None
            and request.approver_id.was_updated
        ):  # always send full approval workflow, even if verification step was not changed but approval step was
            step_verification = self._generate_upsert_approval_workflow_step(
                external_id=verification_step.external_id,
                space=action.space,
                description=ApprovalWorkflowStepDescriptionEnum.VERIFICATION,
                status=(
                    ApprovalWorkflowStatusEnum.PROGRESS
                    if action.current_status.external_id
                    == ActionStatusEnum.PENDING_VERIFICATION
                    else ApprovalWorkflowStatusEnum.PENDING
                ),
                user=verification_step.users[0].external_id,
            )
            steps.append(step_verification)

        approval_workflow = None
        approval_should_be_deleted = False
        if steps:  # upsert
            email = user.external_id
            if user.external_id.startswith("UserAzureAttribute_"):
                email = user.external_id[len("UserAzureAttribute_") :]
            approval_workflow = self._generate_upsert_approval_workflow(
                external_id=(
                    action.approval_workflow.external_id
                    if action.approval_workflow
                    else None
                ),
                space=action.space,
                steps=steps,
                description=(
                    None
                    if action.approval_workflow
                    else ApprovalWorkflowDescriptionEnum.WORKFLOW
                ),
                status=(
                    None
                    if action.approval_workflow
                    else (
                        ApprovalWorkflowStatusEnum.PROGRESS
                        if action.current_status.external_id
                        in [
                            ActionStatusEnum.PENDING_VERIFICATION,
                            ActionStatusEnum.PENDING_APPROVAL,
                        ]
                        else ApprovalWorkflowStatusEnum.PENDING
                    )
                ),
                start_date=None if action.approval_workflow else self._now_datetime_str,
                end_date=None if action.approval_workflow else self._now_datetime_str,
                created_by_id=None if action.approval_workflow else email,
            )
        elif action.approval_workflow is not None and (
            request.approver_id.was_updated or request.verifier_id.was_updated
        ):  # remove
            approval_should_be_deleted = True
            nodes_to_delete.append(action.approval_workflow.to_node_id())

        if (
            request.recurrence_instance.was_updated
            and request.recurrence_instance.value is None
            and action.recurrence_instance is not None
        ):  # remove
            nodes_to_delete.append(action.recurrence_instance.to_node_id())

        if (
            request.assigned_to_id.was_updated
            and request.assigned_to_id.value is not None
        ):
            user_azure_attribute_external_ids.append(request.assigned_to_id.value)

        sort_mapper = self._action_item_client.sort_mapper.get_sort_mapper(
            GetSortMapperRequest(
                user_azure_attribute_external_ids=user_azure_attribute_external_ids,
                reporting_unit_external_ids=(
                    [request.reporting_unit_id.value]
                    if request.reporting_unit_id.was_updated
                    and request.reporting_unit_id.value is not None
                    else []
                ),
                reporting_location_external_ids=(
                    [request.reporting_location_id.value]
                    if request.reporting_location_id.was_updated
                    and request.reporting_location_id.value is not None
                    else []
                ),
                category_external_ids=(
                    [request.category_id.value]
                    if request.category_id.was_updated
                    and request.category_id.value is not None
                    else []
                ),
                sub_category_external_ids=(
                    [request.sub_category_id.value]
                    if request.sub_category_id.was_updated
                    and request.sub_category_id.value is not None
                    else []
                ),
                site_specific_category_instances=(
                    [
                        (
                            get_transactional_space_from_site_id(
                                request.reporting_site_id,
                            ),
                            request.site_specific_category_id.value,
                        ),
                    ]
                    if request.site_specific_category_id.was_updated
                    and request.site_specific_category_id.value is not None
                    else []
                ),
            ),
        )

        update_action = self.generate_upsert_action(
            external_id=action.external_id,
            space=action.space,
            display_due_date=request.due_date.value,
            new_due_date=request.due_date.value,
            history=[history],
            new_assigned_to=request.assigned_to_id.value,
            approval_workflow=approval_workflow,
            request=request,
            sort_mapper=sort_mapper,
        )

        if approval_should_be_deleted:
            update_action.approval_workflow = None

        update_change_request: ActionItemChangeRequest | None = None
        if request.challenge_edit:
            request_challenge = UpdateActionAssigneeRequest(
                external_id=action.external_id,
                space=action.space,
                active_user_email=request.active_user_email,
                reporting_site_external_id=request.reporting_site_id,
                challenge_status=WorkflowStatus.APPROVED.value,
            )

            update_change_request, _ = self._process_challenge(
                action=action,
                request=request_challenge,
                created_by_id=user.external_id,
            )

        update_action.nodes_to_delete.extend(nodes_to_delete)
        update_action.edges_to_delete.extend(edges_to_delete)

        try:
            self._log.info("Upserting action item.")
            if update_change_request:
                self._action_item_client.actions.upsert_change_request(
                    [update_change_request],
                )
            self._action_item_client.actions.upsert_action_item([update_action])
            # TODO: add notification here (only if approver or verifier has changed and is on pending approval/verification) and remove from front

        except Exception as err:
            self._log.error(f"Could not upsert action item. Error: {err}")
            return "", {"internal": [str(err)]}

        return request.external_id, errors_by_action
    
    async def update_multiple_actions(
        self,
        requests: list[UpdateActionRequest],
    ) -> tuple[list[str], dict[str, dict[str, Any]]]:
        """
        Update multiple actions in batch, applying all updates to the fields based on user changes.

        Args:
            requests (list[UpdateActionRequest]): List of request objects containing the data required to update each action.

        Returns:
            tuple[list[str], dict[str, dict[str, Any]]]:
                - List of external IDs of the actions that were processed.
                - A dictionary mapping external IDs to any errors encountered during processing for each action.

        Method Details:
            1. Processes each action update request individually using the existing update_action method.
            2. Collects results and errors for all actions.
            3. Returns a consolidated response with all processed action IDs and any errors.

        Raises:
            Exception: Logs any exceptions that occur during the batch update operation.
        """
        self._log.info(f"Starting to update {len(requests)} actions in batch")

        processed_external_ids: list[str] = []
        errors_by_external_id: dict[str, dict[str, Any]] = {}

        try:
            # Process each action update request
            for request in requests:
                try:
                    external_id, errors = await self.update_action(request)
                    processed_external_ids.append(external_id)

                    if errors:
                        errors_by_external_id[external_id] = errors

                except Exception as err:
                    self._log.error(
                        f"Error updating action {request.external_id}: {err}"
                    )
                    processed_external_ids.append(request.external_id)
                    errors_by_external_id[request.external_id] = {
                        "internal": [f"Failed to update action: {str(err)}"]
                    }

            self._log.info(
                f"Completed batch update. Processed: {len(processed_external_ids)}, "
                f"Errors: {len(errors_by_external_id)}"
            )

        except Exception as err:
            self._log.error(f"Error in batch update operation: {err}")
            # Return whatever we managed to process

        return processed_external_ids, errors_by_external_id

    def validate_bulk_upload_actions(
        self,
        site_id: str,
        file: Any,
        columns: dict,
        user_id: str,
        event_id: Optional[str],
    ) -> tuple[list[dict], int, int, list[dict]]:
        """
        Validate bulk upload actions, ensuring all required fields are correct.

        Args:
            site_id (str): Reporting site external ID for validations.
            file (Any): The uploaded file containing action data.
            columns (dict): Mapping of column names from the uploaded file.
            user_id (str): User ID to set the "createdBy" attribute.
            event_id (Optional[str]): Event ID for event-related actions.

        Returns:
            tuple[list[dict], int, int, list[dict]]:
                - A list of validated action items.
                - The count of valid rows.
                - The count of invalid rows.
                - A list of errors encountered during validation.

        """
        success_count, error_count = 0, 0
        errors_list, action_items_request = [], []

        try:
            bulk_upload_df = pd.read_excel(file)
        except Exception as e:
            msg = f"Error reading Excel file: {e}"
            raise ValueError(msg)

        (
            units,
            locations,
            categories,
            sub_categories,
            site_specific_categories,
            category_configs,
            users,
        ) = self._get_options(site_id)

        for index, row in bulk_upload_df.iloc[:1000].iterrows():
            action_item = self._initialize_action_item(site_id, user_id, event_id)

            row_errors = []

            text_fields = {
                "title": (10, 200, BulkUploadErrorMessageTypes.INVALID_TITLE),
                "description": (
                    10,
                    1000,
                    BulkUploadErrorMessageTypes.INVALID_DESCRIPTION,
                ),
            }

            if not event_id:
                text_fields["sourceInformation"] = (
                    10,
                    200,
                    BulkUploadErrorMessageTypes.INVALID_SOURCE_INFORMATION,
                )

            for field, (min_len, max_len, error_msg) in text_fields.items():
                self._validate_text_field(
                    row,
                    site_id,
                    columns[field]["header"],
                    field,
                    min_len,
                    max_len,
                    action_item,
                    row_errors,
                    error_msg,
                )

            user_fields = {
                "owner": ("ownerId", False),
                "assignee": ("assignedToIds", True),
            }
            for field, (attr, as_list) in user_fields.items():
                self._validate_user_field(
                    row,
                    columns[field]["header"],
                    attr,
                    action_item,
                    row_errors,
                    users,
                    as_list=as_list,
                )

            fields_with_site_id = ["location", "siteSpecificCategory"]

            dropdown_fields = [
                ("unit", units, self._validate_unit_field),
                ("location", locations, self._validate_location_field),
                ("category", categories, self._validate_category_field),
                ("subCategory", sub_categories, self._validate_sub_category_field),
                (
                    "siteSpecificCategory",
                    site_specific_categories,
                    self._validate_site_specific_category_field,
                ),
            ]

            for field, options, method in dropdown_fields:
                if field in fields_with_site_id:
                    method(
                        row,
                        site_id,
                        columns[field]["header"],
                        action_item,
                        row_errors,
                        options,
                    )
                else:
                    method(
                        row,
                        columns[field]["header"],
                        action_item,
                        row_errors,
                        options,
                    )

            category_headers = [
                columns[f]["header"]
                for f in ["category", "subCategory", "siteSpecificCategory"]
            ]
            self._validate_category_configuration(
                category_headers,
                action_item,
                row_errors,
                category_configs,
            )

            self._validate_priority_field(
                row,
                columns["priority"]["header"],
                action_item,
                row_errors,
            )

            date_headers = [columns[f]["header"] for f in ["assignmentDate", "dueDate"]]
            self._validate_date_fields(row, date_headers, action_item, row_errors)

            if not row_errors:
                action_items_request.append(action_item)
                success_count += 1
            else:
                self._log_errors(index, row_errors, errors_list)
                error_count += 1

        return action_items_request, success_count, error_count, errors_list

    async def _get_user_by_email(
        self,
        email: str,
        site_external_id: str | list[str],
    ) -> Optional[UserByEmailResult]:
        """
        Retrieve a user by email and site.

        This function searches for a user based on their email address and the external ID of the reporting site.
        If the user is found, it returns the user's details; otherwise, it returns None.

        Args:
            email (str): The email address of the user to be retrieved.
            site_external_id (str): The external ID of the reporting site associated with the user.

        Returns:
            Optional[UserByEmailResult]:
                - The user details if the user is found.
                - None if no matching user is found.

        """
        user_by_email_request = GetUserRolesAndTeamsRequest(
            email=email,
            reporting_site_external_id=site_external_id,
        )

        try:
            user_service = UserComplementsService(self._action_item_client)
            user: Optional[UserByEmailResult] = await user_service.get_user_by_email(
                user_by_email_request,
            )

            if user is None:
                self._log.error(f"No user found with email: {email}")

        except Exception as err:
            self._log.error(f"Error retrieving user by email {email}. Error: {err}")
            raise ValueError(BulkUploadErrorMessageTypes.NOT_FOUND_EMAIL)
        else:
            return user

    def _get_category_configuration(
        self,
        reporting_site_external_id: str,
        category_id: str,
        sub_category_id: str,
        site_specific_category_id: str | None = None,
    ) -> Optional[CategoryConfigurationByFilterResult]:
        """
        Retrieve category configuration based on the provided filter parameters.

        This function fetches the category configuration by applying filters based on the provided reporting site ID, category ID,
        subcategory ID, and optionally, the site-specific category ID. If a matching category configuration is found, it is returned.

        Args:
            reporting_site_external_id (str): The external ID of the reporting site for which the category configuration is retrieved.
            category_id (str): The external ID of the category to filter by.
            sub_category_id (str): The external ID of the subcategory to filter by.
            site_specific_category_id (str | None, optional): The external ID of the site-specific category to filter by. Defaults to None.

        Returns:
            Optional[CategoryConfigurationByFilterResult]:
                - The first matching category configuration if found.
                - None if no matching configuration is found.

        """
        get_category_configuration_by_filter = GetCategoryConfigurationByFilterRequest(
            reporting_site_external_id=reporting_site_external_id,
            category_id=category_id,
            sub_category_id=sub_category_id,
            site_specific_category_id=site_specific_category_id,
        )

        try:
            category_configuration_service = CategoryConfigurationService(
                self._action_item_client,
            )

            category_configuration = (
                category_configuration_service.get_category_configuration_by_filter(
                    get_category_configuration_by_filter,
                )
            )

            if category_configuration is None:
                self._log.info(
                    f"No category configuration found for category: {category_id}, "
                    f"subcategory: {sub_category_id}, and site-specific category: {site_specific_category_id}",
                )
                return None

        except Exception as err:
            self._log.error(
                f"Error retrieving category configuration for category: {category_id}, "
                f"subcategory: {sub_category_id}, and site-specific category: {site_specific_category_id}. "
                f"Error: {err}",
            )
            return None
        else:
            return category_configuration

    async def create_action_edit_notification(
        self,
        request: ActionEditNotificationRequest,
    ) -> None:
        """
        Create notifications for users based on action status changes and send them.

        This method retrieves the details of an action, determines the users to notify based on the action's status,
        and constructs the notification data. The notifications are then sent to the appropriate users, informing them about
        the status changes of the action.

        Args:
            request (ActionEditNotificationRequest): The request object containing:
                - action IDs (str): The IDs of the actions to be processed.
                - active user information (str): The details of the active user initiating the notification.
                - reporting site details (str): Information about the reporting site related to the action.

        Returns:
            None

        """
        notifications = []

        user = await self._get_user_by_email(
            request.active_user_email,
            request.reporting_site_id,
        )

        if user is None:
            return

        for action_id in request.action_ids:
            action_by_id_request = GetActionByIdRequest(
                external_id=action_id,
                reporting_site_external_id=request.reporting_site_id,
                active_user=user.external_id,
                active_user_roles_ids=user.active_user_roles_ids,
                active_user_teams_ids=user.active_user_teams_ids,
            )

            action, _ = await self.get_action_by_id(action_by_id_request)

            if action is None or not isinstance(action, ActionByIdResult):
                return

            status_id = action.current_status.external_id

            user_to_notify = None
            if status_id == ActionStatusEnum.PENDING_APPROVAL:
                user_to_notify = (
                    action.approval_workflow.get_approver()
                    if action.approval_workflow
                    else None
                )
            elif status_id == ActionStatusEnum.PENDING_VERIFICATION:
                user_to_notify = (
                    action.approval_workflow.get_verifier()
                    if action.approval_workflow
                    else None
                )

            if user_to_notify and user_to_notify.email:
                notification_data = self._build_notification_data(
                    status_id,
                    action,
                    request.reporting_site_id,
                    [user_to_notify.email],
                )
                notifications.append(notification_data)

        if notifications:
            self._notification_service.send_notifications(notifications=notifications)

    async def get_export_actions(
        self,
        request: ExportActionsRequest,
    ) -> func.HttpResponse:
        """Generate an Excel actions based on the provided request."""
        action_request = GetActionRequestForIndustrialModel.model_validate(
            request.model_dump(exclude={"columns", "translations"}),
        )

        all_data: list[ActionExportResult] = []

        try:
            action_request = await self.get_request_configurations(action_request)

            all_data = (
                await self._action_item_client.actions.get_actions_for_industrial_model(
                    action_request,
                    all_pages=True,
                )
            )

            actions = [action.to_response(request.translations) for action in all_data]

            workbook = self._create_excel_file(actions, request.columns)
            return self._generate_file_response(workbook)

        except Exception as err:
            self._log.error(
                f"Error exporting actions: {err}",
            )
            return func.HttpResponse(
                f"Failed to export actions: {err}",
                status_code=HTTPStatus.INTERNAL_SERVER_ERROR,
            )

    def _create_excel_file(
        self,
        all_data: list[ActionExportResponse],
        columns: dict[str, str],
    ) -> Workbook:
        workbook = Workbook()
        ws = workbook.active
        if ws is None:
            msg = "Failed to create a new worksheet in the workbook."
            raise RuntimeError(msg)

        ws.title = "AIM Actions Export"

        headers = list(columns.values())
        keys = list(columns.keys())
        ws.append(headers)

        for item in all_data:
            row = [
                str(getattr(item, ACTION_EXPORT_FIELD_MAP.get(key) or "", "") or "")
                for key in keys
            ]
            ws.append(row)

        return workbook

    def _build_notification_data(
        self,
        status_id: str,
        action: (
            ActionByIdResult
            | ActionByIdForEditingResult
            | ActionItemForAssigneeRequestProcess
        ),
        reporting_site_external_id: str,
        user_to_notify: list[str],
        new_assignee_id: Optional[str] = None,
        new_due_date: Optional[str] = None,
    ) -> Notification:
        """
        Build the notification data for an action based on its current status.

        Args:
            status_id (str): The identifier of the current status of the action.
            action (ActionByIdResult): The action object containing all the relevant details for the notification.
            reporting_site_external_id (str): The external ID of the site reporting the action.
            user_to_notify (list[str]): A list of email addresses of users who should be notified about the action's status change.
            new_assignee_id (Optional[str], optional): The new assignee ID for the action, if applicable. Defaults to None.
            new_due_date (Optional[str], optional): The new due date for the action, if applicable. Defaults to None.

        Returns:
            Notification: The data object containing all the necessary information for creating the notification.

        """
        status_name = STATUS_ID_TO_STATUS_NAME.get(status_id, "")

        try:
            notification_type = NotificationTypes[status_name.replace(" ", "_").upper()]
        except KeyError:
            notification_type = NotificationTypes.COMPLETED

        notification_type_description = self._get_notification_type_description(
            notification_type,
        )

        action_to_notification = action.model_dump()

        action_to_notification.pop("history_instance", None)
        action_to_notification.pop("change_requests", None)

        action_to_notification["current_status"] = {
            "externalId": status_id,
            "name": status_name,
        }
        action_to_notification["sub_category_2"] = {
            "description": (
                action.site_specific_category.description
                if action.site_specific_category
                else "-"
            ),
        }

        def _extract_user_info(
            user: _User | None,
            default_info: dict | None = None,
        ) -> dict:
            if default_info is None:
                default_info = {}
            return {
                **default_info,
                "name": user.display_name or "-" if user else "-",
                "first_name": user.first_name or "-" if user else "-",
                "last_name": user.last_name or "-" if user else "-",
                "user_mail": user.email or "-" if user else "-",
            }

        action_to_notification.update(
            {
                "approver": _extract_user_info(
                    action.approver,
                    action_to_notification.get("approver") or {},
                ),
                "verifier": _extract_user_info(
                    action.verifier,
                    action_to_notification.get("verifier") or {},
                ),
                "assigned_to": _extract_user_info(
                    action.assigned_to.user if action.assigned_to else None,
                    (action_to_notification.get("assigned_to") or {}).get("user") or {},
                ),
            },
        )

        action_to_notification["owner_name"] = (
            action.owner.user.display_name
            if action.owner and action.owner.user
            else "-"
        )

        if notification_type == NotificationTypes.DUE_DATE_EXTENSION and new_due_date:
            action_to_notification["new_due_date"] = new_due_date
        elif notification_type == NotificationTypes.COMPLETED:
            action_to_notification["completion_date"] = datetime.now(tz=UTC).date()
        elif notification_type == NotificationTypes.REASSIGNMENT and new_assignee_id:
            new_assignee = self._action_item_client.user.get_user_by_id(
                GetUserByIdRequest(external_id=new_assignee_id),
            )
            action_to_notification.update(
                {
                    "new_assignee": (
                        _extract_user_info(new_assignee) if new_assignee else None
                    ),
                },
            )

        if action_to_notification.get("site_specific_category") is None:
            action_to_notification["site_specific_category"] = {
                "external_id": "-",
                "space": "-",
                "name": "-",
                "description": "-",
            }

        return self._notification_service.create_notification(
            {**action_to_notification, "site": reporting_site_external_id},
            list(set(user_to_notify)),
            status_name,
            notification_type_description,
        )

    def _get_notification_type_description(
        self,
        notification_type: NotificationTypes,
    ) -> str:
        """
        Retrieve the name of the notification type based on the provided notification type enum value.

        Args:
            notification_type (NotificationTypes): The enum value representing the notification type.

        Returns:
            str: The name of the notification type (e.g., 'DUE_DATE_EXTENSION') if found; otherwise, an empty string.

        """
        return next(
            (
                name
                for name, member in NotificationTypes.__members__.items()
                if member == notification_type
            ),
            "",
        )

    def generate_upsert_status_history_instance(
        self,
        external_id: str,
        space: str,
        status_id: str,
        action_user: str,
        comment: str,
        created_by_id: str,
    ) -> StatusHistoryInstance:
        """
        Generate a status history instance to be used in updating the action workflow.

        Args:
            external_id (str): The external ID of the action associated with the status history instance.
            space (str): The space of the action associated with the status history instance.
            status_id (str): The ID of the status to be assigned to the status history.
            action_user (str): The type of action that originated the history.
            comment (str): The comment associated with the status update.
            created_by_id (str): The ID of the user who created the action.

        Returns:
            StatusHistoryInstance: A new status history instance containing the provided data, including a unique ID and timestamp.

        Method Details:
            - Generates a `friendly_name` based on the action type and the provided comment.
            - Creates and returns a `StatusHistoryInstance` with the provided status, action, user, and other parameters.
            - The generated instance is assigned a new unique ID and the current timestamp (with a 1-second offset for precision).

        """
        friendly_name = f'{{"actionUser": "{action_user}", "comments": "{comment}"}}'

        return StatusHistoryInstance(
            external_id=self._id_generator.next_id(EntitiesEnum.StatusHistoryInstance),
            space=space,
            action=Node(
                external_id=external_id,
                space=space,
            ),
            status=Node(
                external_id=status_id,
                space=DataSpaceEnum.AIM_REF_DATA_SPACE,
            ),
            friendly_name=friendly_name,
            status_subject=Node(
                external_id=created_by_id,
                space=DataSpaceEnum.UMG_DATA_SPACE,
            ),
            changed_at=(self._now_datetime + timedelta(seconds=1))
            .replace(microsecond=0)
            .isoformat(),
        )

    def _generate_recurrence_instance(
        self,
        space: str,
        recurrence: RecurrenceInstanceRequest,
    ) -> RecurrenceInstance:
        """
        Generate a recurrence instance based on the provided recurrence data.

        Args:
            space (str): The space in which the recurrence instance will be created.
            recurrence (RecurrenceInstanceRequest): The data object containing the details of the recurrence, such as
                external ID, description, recurrence type, days of the week, months, and other recurrence-specific attributes.

        Returns:
            RecurrenceInstance: A newly created recurrence instance with the provided or generated external ID and the mapped recurrence data.

        """
        generated_external_id = recurrence.external_id or self._id_generator.next_id(
            EntitiesEnum.RecurrenceInstance,
        )

        return RecurrenceInstance(
            external_id=generated_external_id,
            space=space,
            description=recurrence.description,
            recurrence_type=Node(
                external_id=recurrence.recurrence_type,
                space=DataSpaceEnum.AIM_REF_DATA_SPACE,
            ),
            week_days=recurrence.week_days,
            months=recurrence.months,
            day_of_the_month=recurrence.day_of_the_month,
            quarters=recurrence.quarters,
            month_of_the_year=recurrence.month_of_the_year,
            next_dates=recurrence.next_dates,
            start_date=recurrence.start_date,
            end_date=recurrence.end_date,
        )

    def _generate_upsert_link(
        self,
        update: ActionItemUpdate,
        links: list[FormLink],
    ) -> list[ActionItemLink]:
        """
        Generate or update ActionItemLink objects for a specific action.

        This function takes a list of links and creates a new ActionItemLink for each item in the list.
        Each ActionItemLink will be associated with the provided action and will include the link and description
        provided in the input list.

        Args:
            update (ActionItemUpdate): The action to which the links are associated. This object provides the
                space and external_id required for the link.
            links (list[ActionItemLink]): A list of ActionItemLink objects containing the initial data for link
                and description. The function will generate a new ActionItemLink for each item in the list.

        Returns:
            list[ActionItemLink]: A list of ActionItemLink objects created or updated with the provided links
                and associated action details.

        Example:
            # Example of how this method can be used:
            update = ActionItemUpdate(external_id="action-001", space="action-space")
            links = [ActionItemLink(link="http://example.com", description="Sample Link 1"),
                     ActionItemLink(link="http://example.org", description="Sample Link 2")]

            upserted_links = self._generate_upsert_link(update=update, links=links)

        Notes:
            - The `external_id` for each new link is generated using the `_id_generator`.
            - The `space` and `action` properties for the links are populated based on the provided `action`.

        """
        upserted_links: list[ActionItemLink] = []

        for link in links:
            generated_external_id: str | None = link.external_id
            upserted_links.append(
                ActionItemLink(
                    external_id=(
                        generated_external_id
                        if generated_external_id
                        else self._id_generator.next_id(EntitiesEnum.ActionItemLink)
                    ),
                    space=update.space,
                    action=Node(
                        external_id=update.external_id,
                        space=update.space,
                    ),
                    link=link.link,
                    description=link.description,
                ),
            )

        return upserted_links

    def _generate_upsert_approval_workflow_step(
        self,
        space: str,
        external_id: str | None = None,
        start_date: str | None = None,
        end_date: str | None = None,
        description: str | None = None,
        status: str | None = None,
        step: int | None = None,
        user: str | None = None,
    ) -> ApprovalWorkflowStepUpdate:
        """
        Generate or update an approval workflow step.

        This function constructs an `ApprovalWorkflowStepUpdate` object, either by generating
        new attributes for a step or using provided ones. It ensures the correct initialization
        of default values when certain parameters are not supplied, particularly for new steps.

        Args:
            space (str): The data space where the workflow step resides.
            external_id (Optional[str]): The unique identifier for the workflow step. If not provided,
                a new one is generated.
            start_date (Optional[str]): The start date of the workflow step in ISO 8601 format.
            end_date (Optional[str]): The end date of the workflow step in ISO 8601 format.
            description (Optional[str]): A brief description of the workflow step.
            status (Optional[str]): The current status of the workflow step. Defaults to `PENDING`.
            step (Optional[int]): The step number. Defaults to `1` if not provided for new steps.
            user (Optional[str]): The user associated with the workflow step. This is required for new steps.

        Returns:
            ApprovalWorkflowStepUpdate: An object representing the workflow step update.

        Raises:
            ValueError: If `user` is not provided for new workflow steps.

        Details:
            - For existing steps, the function uses the provided `external_id` and associated values.
            - For new steps:
                - Generates a new `external_id` if not provided.
                - Assigns default values for `approval_condition`, `approval_workflow_consent_type`,
                  and `users` based on the step context.
                - Initializes `step` to `1` if not specified.

        Example:
            # For a new workflow step
            step_update = self._generate_upsert_approval_workflow_step(
                space="approval_space",
                start_date="2024-12-01",
                end_date="2024-12-10",
                description="Approval Step 1",
                user="user123"
            )

            # For an existing workflow step
            step_update = self._generate_upsert_approval_workflow_step(
                space="approval_space",
                external_id="step-001",
                status="APPROVED"
            )

        """
        status_node = (
            Node(
                external_id=status or ApprovalWorkflowStatusEnum.PENDING,
                space=DataSpaceEnum.APW_REF_DATA_SPACE,
            )
            if status
            else None
        )

        approval_condition = None
        approval_workflow_consent_type = None
        step_value = None
        users = None

        if external_id is None:
            step_value = step if step is not None else 1
            generated_external_id = self._id_generator.next_id(
                EntitiesEnum.ApprovalWorkflowStep,
            )
            approval_condition = Node(
                external_id=ApprovalWorkflowCondition.AND,
                space=DataSpaceEnum.APW_REF_DATA_SPACE,
            )
            approval_workflow_consent_type = Node(
                external_id=ApprovalWorkflowConsentType.USER,
                space=DataSpaceEnum.APW_REF_DATA_SPACE,
            )
        else:
            generated_external_id = external_id

        if user:
            users = [
                Node(
                    external_id=user,
                    space=DataSpaceEnum.UMG_DATA_SPACE,
                ),
            ]

        return ApprovalWorkflowStepUpdate(
            external_id=generated_external_id,
            space=space,
            start_date=start_date,
            end_date=end_date,
            step=step_value,
            description=description,
            status=status_node,
            users=users,
            approval_condition=approval_condition,
            approval_workflow_consent_type=approval_workflow_consent_type,
        )

    def _generate_upsert_approval_workflow(
        self,
        space: str,
        steps: list[ApprovalWorkflowStepUpdate],
        external_id: str | None = None,
        description: str | None = None,
        status: str | None = None,
        start_date: str | None = None,
        end_date: str | None = None,
        created_by_id: str | None = None,
    ) -> ApprovalWorkflowUpdate:
        """
        Generate or update an approval workflow.

        This function constructs an `ApprovalWorkflowUpdate` object, either by generating
        a new workflow or updating an existing one based on the provided attributes.
        Default values are used where necessary, particularly for new workflows that do not
        provide certain parameters.

        Args:
            space (str): The data space where the approval workflow resides.
            steps (list[ApprovalWorkflowStepUpdate]): A list of `ApprovalWorkflowStepUpdate` objects
                representing the steps of the workflow.
            external_id (Optional[str]): The unique identifier for the approval workflow.
                If not provided, a new one is generated.
            description (Optional[str]): A brief description of the approval workflow.
            status (Optional[str]): The status of the workflow. Defaults to `PENDING`.
            start_date (Optional[str]): The start date of the workflow in ISO 8601 format.
            end_date (Optional[str]): The end date of the workflow in ISO 8601 format.
            created_by_id (Optional[str]): The ID of the user who created the workflow. If provided,
                a node for the user is created.

        Returns:
            ApprovalWorkflowUpdate: An object representing the approval workflow update.

        Raises:
            ValueError: If required arguments are missing or incorrect.

        Details:
            - If `external_id` is not provided, a new ID is generated for the workflow.
            - The `status` is set to `PENDING` by default if not provided.
            - If `created_by_id` is provided, the user is included in the workflow as the creator.
            - The `current_step` is set to `1` for new workflows.

        Example:
            # For a new approval workflow
            workflow_update = self._generate_upsert_approval_workflow(
                space="approval_space",
                steps=[step1, step2],
                description="Approval Workflow for Process X",
                created_by_id="user123"
            )

            # For an existing approval workflow
            workflow_update = self._generate_upsert_approval_workflow(
                space="approval_space",
                external_id="workflow-001",
                steps=[step1, step2],
                status="APPROVED"
            )

        """
        generated_external_id = external_id or self._id_generator.next_id(
            EntitiesEnum.ApprovalWorkflow,
        )

        step_value = 1 if external_id is None else None

        status_node = Node(
            external_id=status or ApprovalWorkflowStatusEnum.PENDING,
            space=DataSpaceEnum.APW_REF_DATA_SPACE,
        )

        created_by = (
            Node(
                external_id=created_by_id,
                space=DataSpaceEnum.UMG_DATA_SPACE,
            )
            if created_by_id
            else None
        )

        return ApprovalWorkflowUpdate(
            external_id=generated_external_id,
            space=space,
            status=status_node,
            description=description,
            current_step=step_value,
            steps=steps,
            start_date=start_date,
            end_date=end_date,
            created_by=created_by,
        )

    def _generate_change_request(
        self,
        space: str,
        action: ActionItemUpdate,
        external_id: str | None = None,
        approval_workflow: ApprovalWorkflowUpdate | None = None,
        comments: str | None = None,
        change_request_type: str | None = None,
        properties_to_change: str | None = None,
    ) -> ActionItemChangeRequest:
        """
        Generate or update a change request for an action item.

        This function creates an `ActionItemChangeRequest` object with the provided attributes,
        including optional values like `comments`, `action`, and `approval_workflow`. It also
        handles generating a new `external_id` if one is not provided, and processes certain
        attributes like `properties_to_change` (parsing it as JSON if it's a string).

        Args:
            space (str): The data space where the change request resides.
            external_id (Optional[str]): The unique identifier for the change request.
                If not provided, a new one is generated.
            action (Optional[ApprovalWorkflowStepUpdate]): The action associated with the change request.
            approval_workflow (Optional[ApprovalWorkflowStepUpdate]): The approval workflow associated with the change request.
            comments (Optional[str]): Comments for the change request. If provided, it is added to the request.
            change_request_type (Optional[str]): The type of change request, represented as a Node.
                If not provided, it will not be included.
            properties_to_change (Optional[str]): A JSON string representing the properties to be changed.
                If provided, it will be parsed into a dictionary.

        Returns:
            ActionItemChangeRequest: An object representing the change request.

        Raises:
            ValueError: If required parameters are missing or invalid.

        Details:
            - If `external_id` is not provided, a new ID is generated for the change request.
            - If `change_request_type` is not provided, it is omitted from the change request.
            - If `properties_to_change` is provided, it is parsed from a JSON string into a Python dictionary.
            - If `comments` is provided, it is included in the change request as a list of strings.

        Example:
            # For a new change request
            change_request = self._generate_change_request(
                space="change-request-space",
                action=action_step,
                approval_workflow=workflow_step,
                comments="Requesting extension",
                change_request_type="EXTENSION",
                properties_to_change='{"dueDate": "2024-12-15"}'
            )

            # For an existing change request
            change_request = self._generate_change_request(
                space="change-request-space",
                external_id="change-request-001",
                comments="Change request updated",
            )

        """
        generated_external_id = external_id or self._id_generator.next_id(
            EntitiesEnum.ChangeRequest,
        )

        return ActionItemChangeRequest(
            external_id=generated_external_id,
            space=space,
            change_request_type=(
                Node(
                    external_id=change_request_type,
                    space=DataSpaceEnum.AIM_REF_DATA_SPACE,
                )
                if change_request_type is not None
                else None
            ),
            properties_to_change=(
                json.loads(properties_to_change)
                if properties_to_change is not None
                else None
            ),
            comments=[comments] if comments is not None else None,
            action=action,
            approval_workflow=approval_workflow,
        )

    def generate_upsert_action(
        self,
        external_id: str,
        space: str,
        history: list[StatusHistoryInstance],
        status_id: str | None = None,
        display_due_date: str | None = None,
        approval_workflow: ApprovalWorkflowUpdate | None = None,
        new_assigned_to: str | None = None,
        new_due_date: str | None = None,
        request: UpdateActionRequest | None = None,
        sort_mapper: SortMapper = SortMapper(),
    ) -> ActionItemUpdate:
        """
        Generate or update an action item based on the provided parameters.

        This function creates or updates an `ActionItemUpdate` object, either by using the
        provided `external_id` and other parameters, or by generating a new item if necessary.
        It allows for the addition of status history, assignment changes, and approval workflow updates.

        Args:
            external_id (str): The unique identifier for the action item.
            space (str): The data space where the action item resides.
            status_id (str): The status of the action item, represented by a `Node`.
            history (list[StatusHistoryInstance]): A list of `StatusHistoryInstance` objects
                representing the status changes of the action item.
            display_due_date (Optional[str]): The due date for displaying purposes, in ISO 8601 format.
            approval_workflow (Optional[ApprovalWorkflowUpdate]): The approval workflow associated
                with the action item, if applicable.
            new_assigned_to (Optional[str]): The external ID of the user to whom the action item is assigned.
            new_due_date (Optional[str]): The new due date for the action item, in ISO 8601 format.
            request: (Optional[UpdateActionRequest]): Update object containing any other changes to be made
            sort_mapper: (Optional[SortMapper]): Mapper to be used when filling sort fields, should be present
                if any property that has a sort column is being changed

        Returns:
            ActionItemUpdate: An object representing the action item update.

        Raises:
            ValueError: If any required argument is invalid or missing.

        Details:
            - The `status_id` is used to set the current status of the action item.
            - If `new_assigned_to` is provided, the action item will be reassigned to the specified user.
            - If `new_due_date` is provided, the action item's due date will be updated accordingly.
            - The `history` parameter is mandatory to track the status history of the action item.
            - If `approval_workflow` is provided, it will be included in the update.

        Example:
            # For a new action item update
            action_item_update = self.generate_upsert_action(
                external_id="action-001",
                space="action-item-space",
                status_id="status-pending",
                history=[status_history],
                display_due_date="2024-12-31",
                new_assigned_to="user123",
                new_due_date="2024-12-15"
            )

            # For an existing action item update
            action_item_update = self.generate_upsert_action(
                external_id="action-001",
                space="action-item-space",
                status_id="status-completed",
                history=[status_history],
                approval_workflow=approval_workflow_update
            )

        """
        update = ActionItemUpdate(
            external_id=external_id,
            space=space,
            history_instance=history,
        )
        if status_id is not None:
            update.current_status = Node(
                external_id=status_id,
                space=DataSpaceEnum.AIM_REF_DATA_SPACE,
            )
            update.sort_current_status = sort_mapper.get_action_status_sort_value(
                status_id,
            )
        if new_due_date is not None:
            update.due_date = new_due_date
        if display_due_date is not None:
            update.display_due_date = display_due_date
        if approval_workflow is not None:
            update.approval_workflow = approval_workflow
        if new_assigned_to is not None:
            update.assigned_to = Node(
                external_id=new_assigned_to,
                space=DataSpaceEnum.UMG_DATA_SPACE,
            )
            update.sort_assignee = sort_mapper.get_user_azure_attribute_sort_value(
                new_assigned_to,
            )

        if request is None:
            return update

        site_space = get_transactional_space_from_site_id(request.reporting_site_id)

        if request.links.was_updated:
            update.action_item_link = (
                self._generate_upsert_link(update, request.links.value)
                if request.links.value is not None
                else []
            )

        if (
            request.action_item_kind.was_updated
            and request.action_item_kind.value is not None
        ):
            update.action_item_kind = request.action_item_kind.to_node(
                DataSpaceEnum.AIM_REF_DATA_SPACE,
            )

        if request.title.was_updated and request.title.value is not None:
            update.title = request.title.value

        if request.description.was_updated and request.description.value is not None:
            update.description = request.description.value

        if request.owner_id.was_updated and request.owner_id.value is not None:
            update.owner = request.owner_id.to_node(DataSpaceEnum.UMG_DATA_SPACE)
            update.sort_owner = sort_mapper.get_user_azure_attribute_sort_value(
                request.owner_id.value,
            )

        if (
            request.reporting_unit_id.was_updated
            and request.reporting_unit_id.value is not None
        ):
            update.reporting_unit = request.reporting_unit_id.to_node(
                DataSpaceEnum.REF_DATA_SPACE,
            )
            update.sort_reporting_unit = sort_mapper.get_reporting_unit_sort_value(
                request.reporting_unit_id.value,
            )

        if request.reporting_location_id.was_updated:
            update.reporting_location = request.reporting_location_id.to_node(
                DataSpaceEnum.REF_DATA_SPACE,
            )
            update.sort_reporting_location = (
                sort_mapper.get_reporting_location_sort_value(
                    request.reporting_location_id.value,
                )
            )

        if request.reporting_line_id.was_updated:
            update.reporting_line = request.reporting_line_id.to_node(
                DataSpaceEnum.REF_DATA_SPACE,
            )

        if request.source_information.was_updated:
            update.source_information = request.source_information.value

        if request.category_id.was_updated and request.category_id.value is not None:
            update.category = request.category_id.to_node(
                DataSpaceEnum.AIM_REF_DATA_SPACE,
            )
            update.sort_category = sort_mapper.get_category_sort_value(
                request.category_id.value,
            )

        if request.sub_category_id.was_updated:
            update.sub_category = request.sub_category_id.to_node(
                DataSpaceEnum.AIM_REF_DATA_SPACE,
            )
            update.sort_sub_category = sort_mapper.get_sub_category_sort_value(
                request.sub_category_id.value,
            )

        if request.site_specific_category_id.was_updated:
            update.site_specific_category = request.site_specific_category_id.to_node(
                site_space,
            )
            update.sort_site_specific_category = (
                sort_mapper.get_site_specific_category_sort_value(
                    request.site_specific_category_id.value,
                    site_space,
                )
            )

        if request.priority.was_updated:
            update.priority = request.priority.value

        if (
            request.assignment_date.was_updated
            and request.assignment_date.value is not None
        ):
            update.assignment_date = request.assignment_date.value

        if request.evidence_required.was_updated:
            update.evidence_required = request.evidence_required.value

        if request.assignee_ids.was_updated:
            update.assignees = request.assignee_ids.value or []

        if request.view_users.was_updated:
            update.view_users = (
                request.view_users.to_nodes(DataSpaceEnum.UMG_DATA_SPACE) or []
            )

        if request.view_roles.was_updated:
            update.view_roles = (
                request.view_roles.to_nodes(DataSpaceEnum.UMG_DATA_SPACE) or []
            )

        if request.view_teams.was_updated:
            update.view_teams = (
                request.view_teams.to_nodes(DataSpaceEnum.UMG_DATA_SPACE) or []
            )

        if request.recurrence_instance.was_updated:
            update.recurrence_instance = (
                self._generate_recurrence_instance(
                    space,
                    request.recurrence_instance.value,
                )
                if request.recurrence_instance.value is not None
                else None
            )

        if request.approver_id.was_updated:
            update.sort_approver = sort_mapper.get_user_azure_attribute_sort_value(
                request.approver_id.value,
            )

        if request.verifier_id.was_updated:
            update.sort_verifier = sort_mapper.get_user_azure_attribute_sort_value(
                request.verifier_id.value,
            )

        if request.attachment_ids.was_updated:
            update.attachments = request.attachment_ids.value or []

        return update

    def _update_action_approval_workflow_status(
        self,
        action: ActionByIdResult,
        status_id: str,
        history: StatusHistoryInstance,
        approval_workflow_status: str | None = None,
        approval_workflow_step_approval_status: str | None = None,
        approval_workflow_step_verification_status: str | None = None,
        display_due_date: str | None = None,
        approval_date: str | None = None,
        verification_date: str | None = None,
        conclusion_date: str | None = None,
        assignee_comment: str | None = None,
    ) -> ActionItemUpdate:
        """
        Update the approval workflow status of an action, including approval, verification steps, and generates an updated action item.

        Args:
            action (ActionByIdResult): The action object that the approval workflow is associated with.
            status_id (str): The status ID to be assigned to the action in its approval workflow.
            history (StatusHistoryInstance): The status history instance to be associated with the action, providing the history of status changes.
            approval_workflow_status (str, optional): The overall approval status for the workflow.
            approval_workflow_step_approval_status (str, optional): The approval status of the specific workflow step.
            approval_workflow_step_verification_status (str, optional): The verification status of the specific workflow step.
            display_due_date (str, optional): The due date to be displayed for the action.
            approval_date (str, optional): The date when the action was approved.
            verification_date (str, optional): The date when the action was verified.
            conclusion_date (str, optional): The date when the action was concluded.
            assignee_comment (str, optional): A comment provided by the assignee of the action.

        Returns:
            ActionItemUpdate: The updated action item, including the new status, approval workflow steps, dates, and other relevant details.

        Method Details:
            - The method checks if the approval and verification steps are provided and creates updates accordingly.
            - Generates an `ApprovalWorkflowUpdate` object to reflect the changes in the approval workflow steps.
            - Returns an `ActionItemUpdate` object with the updated details, including status, workflow changes, dates, and comments.

        """
        approval_workflow_update = None

        if action.approval_workflow is not None:
            approval_step = next(
                (
                    step
                    for step in action.approval_workflow.steps or []
                    if step.description == ApprovalWorkflowStepDescriptionEnum.APPROVAL
                ),
                None,
            )
            verification_step = next(
                (
                    step
                    for step in action.approval_workflow.steps or []
                    if step.description
                    == ApprovalWorkflowStepDescriptionEnum.VERIFICATION
                ),
                None,
            )

            approval_status = (
                Node(
                    external_id=approval_workflow_step_approval_status,
                    space=DataSpaceEnum.APW_REF_DATA_SPACE.value,
                )
                if approval_workflow_step_approval_status
                else None
            )

            approval_step_update = (
                ApprovalWorkflowStepUpdate(
                    external_id=approval_step.external_id,
                    space=approval_step.space,
                    end_date=approval_date,
                    status=approval_status,
                    users=[  # needed for private action upsert views logic
                        Node(external_id=u.external_id, space=u.space)
                        for u in approval_step.users
                    ],
                )
                if approval_step is not None and approval_step.users is not None
                else None
            )

            verification_status = (
                Node(
                    external_id=approval_workflow_step_verification_status,
                    space=DataSpaceEnum.APW_REF_DATA_SPACE.value,
                )
                if approval_workflow_step_verification_status
                else None
            )

            verification_step_update = (
                ApprovalWorkflowStepUpdate(
                    external_id=verification_step.external_id,
                    space=verification_step.space,
                    end_date=verification_date,
                    status=verification_status,
                    users=[  # needed for private action upsert views logic
                        Node(external_id=u.external_id, space=u.space)
                        for u in verification_step.users
                    ],
                )
                if verification_step is not None and verification_step.users is not None
                else None
            )

            approval_workflow_status_update = (
                Node(
                    external_id=approval_workflow_status,
                    space=DataSpaceEnum.APW_REF_DATA_SPACE,
                )
                if approval_workflow_status
                else None
            )

            approval_workflow_update = (
                ApprovalWorkflowUpdate(
                    external_id=action.approval_workflow.external_id,
                    space=action.space,
                    status=approval_workflow_status_update,
                    steps=[
                        step
                        for step in [approval_step_update, verification_step_update]
                        if step
                    ],
                )
                if action.approval_workflow and approval_workflow_status
                else None
            )

        sort_mapper = self._action_item_client.sort_mapper.get_sort_mapper()

        update = ActionItemUpdate(
            external_id=action.external_id,
            space=action.space,
            history_instance=[history],
            current_status=Node(
                external_id=status_id,
                space=DataSpaceEnum.AIM_REF_DATA_SPACE,
            ),
            sort_current_status=sort_mapper.get_action_status_sort_value(status_id),
        )

        # needed as "None" is now considered a deletion
        if assignee_comment is not None:
            update.assignee_comment = assignee_comment

        if display_due_date is not None:
            update.display_due_date = display_due_date

        if approval_date is not None:
            update.approval_date = approval_date

        if verification_date is not None:
            update.verification_date = verification_date

        if conclusion_date is not None:
            update.conclusion_date = conclusion_date

        if approval_workflow_update is not None:
            update.approval_workflow = approval_workflow_update

        return update

    def _upsert_assignee_requests(
        self,
        action: ActionByIdResult | ActionItemForAssigneeRequestProcess | ActionResult,
        status_id: str,
        history: StatusHistoryInstance,
        display_due_date: str,
        workflow_status: str,
        change_request_type: str | None = None,
        comments: str | None = None,
        properties_to_change: str | None = None,
        step_description: str | None = None,
        workflow_created_by_id: str | None = None,
        start_date: str | None = None,
        end_date: str | None = None,
        change_request: ChangeRequest | None = None,
        new_assigned_to: str | None = None,
        new_due_date: str | None = None,
    ) -> ActionItemChangeRequest:
        """
        Create or update an assignee request, generating the associated change request, approval workflow, and action item update based on the provided input data.

        This function handles the generation of the required entities for upserting a change
        request related to the assignment of an action item, including handling its
        approval workflow, associated comments, and related history. It prepares the action
        and change request instances, and returns the newly created or updated change request.

        Args:
            action (ActionByIdResult | ActionItemForAssigneeRequestProcess | ActionResult):
                The action item being updated.
            status_id (str):
                The status ID to be applied to the action item.
            history (StatusHistoryInstance):
                The history instance to be associated with the action item update.
            display_due_date (str):
                The due date to be displayed for the action item.
            workflow_status (str):
                The status of the approval workflow (e.g., "PROGRESS", "APPROVED").
            change_request_type (str | None, optional):
                The type of change request (e.g., "REASSIGNMENT", "DUE_DATE"). Default is `None`.
            comments (str | None, optional):
                The comments associated with the change request. Default is `None`.
            properties_to_change (str | None, optional):
                The properties of the action item to change (e.g., assignee, due date). Default is `None`.
            step_description (str | None, optional):
                The description for the approval workflow step. Default is `None`.
            workflow_created_by_id (str | None, optional):
                The ID of the user who created the workflow. Default is `None`.
            start_date (str | None, optional):
                The start date for the approval workflow or action item. Default is `None`.
            end_date (str | None, optional):
                The end date for the approval workflow or action item. Default is `None`.
            change_request (ChangeRequest | None, optional):
                The existing change request to be updated. Default is `None`.
            new_assigned_to (str | None, optional):
                The external ID of the user to whom the action item will be reassigned. Default is `None`.
            new_due_date (str | None, optional):
                The new due date for the action item. Default is `None`.

        Returns:
            ActionItemChangeRequest:
                The generated or updated change request for the action item.

        Process Flow:
            1. **Approval Workflow Step**: Generates or updates an approval workflow step, including the start and end dates, status, and description.
            2. **Approval Workflow**: Generates or updates the approval workflow, including the steps, status, and user who created it.
            3. **Action Item Update**: Prepares the action item update, including status, new assignee, due date, and history.
            4. **Change Request**: Generates the change request using the updated action item and approval workflow details.

        Example:
            action = ActionByIdResult(
                external_id="ACT-20241203184516-0001",
                space="Space_123",
                assigned_to=User(external_id="user123"),
                change_requests=[],
            )
            history = StatusHistoryInstance(status_id="PROGRESS", comment="Reassigned to new user")
            change_request = _upsert_assignee_requests(
                action=action,
                status_id="ASSIGNED",
                history=history,
                display_due_date="2024-12-20",
                workflow_status="PROGRESS",
                change_request_type="REASSIGNMENT",
                comments="Assignee updated",
                step_description="Reassignment step",
                workflow_created_by_id="user123",
                start_date="2024-12-01",
                end_date="2024-12-31",
                new_assigned_to="user456",
            )
            print(change_request)

        """
        step_external_id = (
            change_request.approval_workflow.steps[0].external_id
            if change_request
            and change_request.approval_workflow
            and change_request.approval_workflow.steps
            else None
        )
        workflow_external_id = (
            change_request.approval_workflow.external_id
            if change_request and change_request.approval_workflow
            else None
        )
        change_request_external_id = (
            change_request.external_id if change_request else None
        )
        workflow_description = (
            None if change_request else ApprovalWorkflowDescriptionEnum.ASSIGNEE
        )
        workflow_step_description = None if change_request else step_description

        step = self._generate_upsert_approval_workflow_step(
            external_id=step_external_id,
            space=action.space,
            start_date=start_date,
            end_date=end_date,
            description=workflow_step_description,
            status=workflow_status,
            user=workflow_created_by_id,
        )

        approval_workflow = self._generate_upsert_approval_workflow(
            external_id=workflow_external_id,
            space=action.space,
            steps=[step],
            description=workflow_description,
            status=workflow_status,
            start_date=start_date,
            end_date=end_date,
            created_by_id=workflow_created_by_id,
        )

        sort_mapper = self._action_item_client.sort_mapper.get_sort_mapper(
            GetSortMapperRequest(
                user_azure_attribute_external_ids=(
                    [new_assigned_to] if new_assigned_to is not None else []
                ),
            ),
        )

        update_action = self.generate_upsert_action(
            external_id=action.external_id,
            space=action.space,
            status_id=status_id,
            display_due_date=display_due_date,
            new_due_date=new_due_date,
            history=[history],
            new_assigned_to=new_assigned_to,
            sort_mapper=sort_mapper,
        )

        if (
            isinstance(action, ActionItemForAssigneeRequestProcess)
            and new_assigned_to
            and action.is_private
        ):
            views = set()
            views.add(new_assigned_to)

            if action.owner:
                views.add(action.owner.external_id)

            approver = (
                action.approval_workflow.get_approver()
                if action.approval_workflow
                else None
            )
            verifier = (
                action.approval_workflow.get_verifier()
                if action.approval_workflow
                else None
            )
            if approver:
                views.add(to_user_azure_attribute(approver.external_id))

            if verifier:
                views.add(to_user_azure_attribute(verifier.external_id))

            if action.view_users:
                views.update(user.external_id for user in action.view_users if user)

            if action.view_roles:
                views.update(role.external_id for role in action.view_roles if role)

            if action.view_teams:
                views.update(team.external_id for team in action.view_teams if team)

            if (
                action.source_events
                and action.source_events[0]
                and action.source_events[0].views
            ):
                views.update(action.source_events[0].views)

            update_action.views = list(views)

        return self._generate_change_request(
            external_id=change_request_external_id,
            space=action.space,
            action=update_action,
            approval_workflow=approval_workflow,
            comments=comments,
            change_request_type=change_request_type,
            properties_to_change=properties_to_change,
        )

    def _process_due_date(self, days: int) -> str:
        """
        Calculate and return a due date based on the current date and a specified number of days.

        Args:
            days (int): The number of days to add to the current date to calculate the due date.

        Returns:
            str: The calculated due date in the format 'YYYY-MM-DD'.

        Method Details:
            - The method uses the current date (retrieved from `self._now_datetime`).
            - The specified number of days is added to the current date using `timedelta`.
            - The resulting date is formatted as a string in the 'YYYY-MM-DD' format and returned.

        """
        return (self._now_datetime.date() + timedelta(days=days)).strftime("%Y-%m-%d")

    def _process_assignee(
        self,
        action: ActionByIdResult,
        request: UpdateActionApprovalWorkflowRequest,
        created_by_id: str,
        category_configuration: CategoryConfigurationByFilterResult | None = None,
    ) -> tuple[ActionItemUpdate, list[str]]:
        """
        Process the assignment of an action, updating its status and other relevant attributes based on the current state and category configurations.

        This method handles the action assignment process by checking the current status of the action and updating it accordingly. It determines the appropriate next status (approval, verification, or completion) and applies any necessary category-specific configuration for deadlines.

        Args:
            action (ActionByIdResult): The action to be processed, containing details such as current status and assignee.
            request (UpdateActionApprovalWorkflowRequest): The request object that contains data for updating the action's approval workflow.
            created_by_id (str): The ID of the user who is making the update (usually the creator or modifier of the action).
            category_configuration (CategoryConfigurationByFilterResult, optional): Configuration details that define the number of days for approval and verification deadlines based on the category.

        Returns:
            tuple[ActionItemUpdate, list[str] | None]: A tuple containing:
                - The updated action item after processing the changes.
                - List of users to be notified.

        """
        days_to_approval = (
            category_configuration.days_to_approval
            if category_configuration and category_configuration.days_to_approval
            else 10
        )
        days_to_verification = (
            category_configuration.days_to_verification
            if category_configuration and category_configuration.days_to_verification
            else 45
        )
        assignee_email: str | None = (
            action.assigned_to.user.email
            if action.assigned_to and action.assigned_to.user
            else None
        )

        if action.approver and action.current_status.external_id in {
            ActionStatusEnum.ASSIGNED,
            ActionStatusEnum.APPROVAL_REJECTED,
        }:
            history = self.generate_upsert_status_history_instance(
                action.external_id,
                action.space,
                ActionStatusEnum.PENDING_APPROVAL,
                HistoryActionUser.COMPLETED,
                request.assignee_comment or "",
                created_by_id,
            )
            approval_date = self._process_due_date(days_to_approval)
            return (
                self._update_action_approval_workflow_status(
                    action,
                    ActionStatusEnum.PENDING_APPROVAL,
                    history,
                    approval_workflow_status=ApprovalWorkflowStatusEnum.PROGRESS,
                    approval_workflow_step_approval_status=ApprovalWorkflowStatusEnum.PROGRESS,
                    display_due_date=approval_date,
                    assignee_comment=request.assignee_comment,
                ),
                [
                    email
                    for email in [
                        action.approver.email if action.approver else None,
                        assignee_email,
                    ]
                    if email
                ],
            )
        if action.verifier and action.current_status.external_id in {
            ActionStatusEnum.ASSIGNED,
            ActionStatusEnum.VERIFICATION_REJECTED,
        }:
            history = self.generate_upsert_status_history_instance(
                action.external_id,
                action.space,
                ActionStatusEnum.PENDING_VERIFICATION,
                HistoryActionUser.COMPLETED,
                request.assignee_comment or "",
                created_by_id,
            )
            verification_date = self._process_due_date(days_to_verification)
            return (
                self._update_action_approval_workflow_status(
                    action,
                    ActionStatusEnum.PENDING_VERIFICATION,
                    history,
                    approval_workflow_status=ApprovalWorkflowStatusEnum.PROGRESS,
                    approval_workflow_step_verification_status=ApprovalWorkflowStatusEnum.PROGRESS,
                    display_due_date=verification_date,
                    assignee_comment=request.assignee_comment,
                ),
                [
                    email
                    for email in [
                        action.verifier.email if action.verifier else None,
                        assignee_email,
                    ]
                    if email
                ],
            )

        history = self.generate_upsert_status_history_instance(
            action.external_id,
            action.space,
            ActionStatusEnum.COMPLETED,
            HistoryActionUser.COMPLETED,
            request.assignee_comment or "",
            created_by_id,
        )
        return (
            self._update_action_approval_workflow_status(
                action,
                ActionStatusEnum.COMPLETED,
                history,
                conclusion_date=self._now_datetime_str,
                assignee_comment=request.assignee_comment,
            ),
            (
                [
                    email
                    for email in [
                        (
                            action.owner.user.email
                            if action.owner and action.owner.user
                            else None
                        ),
                        assignee_email,
                    ]
                    if email
                ]
                if category_configuration
                and category_configuration.has_email_notification
                else []
            ),
        )

    def _process_approver(
        self,
        action: ActionByIdResult,
        request: UpdateActionApprovalWorkflowRequest,
        created_by_id: str,
        category_configuration: CategoryConfigurationByFilterResult | None = None,
    ) -> tuple[ActionItemUpdate, list[str]]:
        """
        Process the approval of an action, updating its status and determining the next steps in the approval workflow, such as verification or finalization.

        Args:
            action (ActionByIdResult): The action to be processed, containing details such as current status and approval state.
            request (UpdateActionApprovalWorkflowRequest): The request data to update the action's approval workflow.
            created_by_id (str): The ID of the user performing the approval action.
            category_configuration (CategoryConfigurationByFilterResult, optional): Category configurations that define the number of days for verification deadlines or any other applicable settings based on the category.

        Returns:
            tuple[ActionItemUpdate, list[str]]: A tuple containing:
                - The updated action item after processing the approval status and any subsequent steps.
                - List of users to be notified.

        """
        assignee_email: str | None = (
            action.assigned_to.user.email
            if action.assigned_to and action.assigned_to.user
            else None
        )

        if request.approval_status == WorkflowStatus.APPROVED:
            if action.verifier:
                history = self.generate_upsert_status_history_instance(
                    action.external_id,
                    action.space,
                    ActionStatusEnum.PENDING_VERIFICATION,
                    HistoryActionUser.APPROVED,
                    request.approver_comment or "",
                    created_by_id,
                )
                verification_date = self._process_due_date(
                    (
                        category_configuration.days_to_verification
                        if category_configuration
                        and category_configuration.days_to_verification
                        else 45
                    ),
                )
                return (
                    self._update_action_approval_workflow_status(
                        action,
                        ActionStatusEnum.PENDING_VERIFICATION,
                        history,
                        approval_workflow_status=ApprovalWorkflowStatusEnum.PROGRESS,
                        approval_workflow_step_approval_status=ApprovalWorkflowStatusEnum.APPROVED,
                        approval_workflow_step_verification_status=ApprovalWorkflowStatusEnum.PROGRESS,
                        approval_date=self._now_datetime_str,
                        display_due_date=verification_date,
                        verification_date=verification_date,
                    ),
                    [
                        email
                        for email in [
                            action.verifier.email if action.verifier else None,
                            assignee_email,
                        ]
                        if email
                    ],
                )

            history = self.generate_upsert_status_history_instance(
                action.external_id,
                action.space,
                ActionStatusEnum.COMPLETED,
                HistoryActionUser.COMPLETED,
                request.approver_comment or "",
                created_by_id,
            )
            return (
                self._update_action_approval_workflow_status(
                    action,
                    ActionStatusEnum.COMPLETED,
                    history,
                    approval_workflow_status=ApprovalWorkflowStatusEnum.APPROVED,
                    approval_workflow_step_approval_status=ApprovalWorkflowStatusEnum.APPROVED,
                    approval_date=self._now_datetime_str,
                    conclusion_date=self._now_datetime_str,
                ),
                [
                    email
                    for email in [
                        (
                            action.owner.user.email
                            if action.owner and action.owner.user
                            else None
                        ),
                        assignee_email,
                    ]
                    if email
                ],
            )

        history = self.generate_upsert_status_history_instance(
            action.external_id,
            action.space,
            ActionStatusEnum.APPROVAL_REJECTED,
            HistoryActionUser.APPROVED_REJECTED,
            request.approver_comment or "",
            created_by_id,
        )
        return (
            self._update_action_approval_workflow_status(
                action,
                ActionStatusEnum.APPROVAL_REJECTED,
                history,
                approval_workflow_status=ApprovalWorkflowStatusEnum.PROGRESS,
                approval_workflow_step_approval_status=ApprovalWorkflowStatusEnum.REJECTED,
                display_due_date=self._process_due_date(10),
            ),
            [assignee_email] if assignee_email else [],
        )

    def _process_verifier(
        self,
        action: ActionByIdResult,
        request: UpdateActionApprovalWorkflowRequest,
        created_by_id: str,
    ) -> tuple[ActionItemUpdate, list[str]]:
        """
        Process the verification of an action, updating the action's status based on the verification result, and determining if the action is completed or rejected.

        Args:
            action (ActionByIdResult): The action to be processed, containing details such as the current status and verification state.
            request (UpdateActionApprovalWorkflowRequest): The request data used to update the approval workflow, including the verification status.
            created_by_id (str): The ID of the user performing the verification.

        Returns:
            tuple[ActionItemUpdate, list[str]]: A tuple containing:
                - The updated action item after processing the verification status.
                - List of users to be notified.

        """
        assignee_email: str = (
            action.assigned_to.user.email or ""
            if action.assigned_to and action.assigned_to.user
            else ""
        )

        if request.verification_status == WorkflowStatus.APPROVED:
            history = self.generate_upsert_status_history_instance(
                action.external_id,
                action.space,
                ActionStatusEnum.COMPLETED,
                HistoryActionUser.VERIFICATION,
                request.verifier_comment or "",
                created_by_id,
            )
            return (
                self._update_action_approval_workflow_status(
                    action,
                    ActionStatusEnum.COMPLETED,
                    history,
                    approval_workflow_status=ApprovalWorkflowStatusEnum.APPROVED,
                    approval_workflow_step_verification_status=ApprovalWorkflowStatusEnum.APPROVED,
                    verification_date=self._now_datetime_str,
                    conclusion_date=self._now_datetime_str,
                ),
                [
                    email
                    for email in [
                        (
                            action.owner.user.email
                            if action.owner and action.owner.user
                            else None
                        ),
                        assignee_email,
                    ]
                    if email
                ],
            )

        history = self.generate_upsert_status_history_instance(
            action.external_id,
            action.space,
            ActionStatusEnum.VERIFICATION_REJECTED,
            HistoryActionUser.VERIFICATION_REJECTED,
            request.verifier_comment or "",
            created_by_id,
        )
        return (
            self._update_action_approval_workflow_status(
                action,
                ActionStatusEnum.VERIFICATION_REJECTED,
                history,
                approval_workflow_status=ApprovalWorkflowStatusEnum.PROGRESS,
                approval_workflow_step_verification_status=ApprovalWorkflowStatusEnum.REJECTED,
                display_due_date=self._process_due_date(10),
            ),
            [assignee_email],
        )

    def _process_reassigment(
        self,
        action: ActionItemForAssigneeRequestProcess,
        request: UpdateActionAssigneeRequest,
        created_by_id: str,
    ) -> tuple[ActionItemChangeRequest, list[str]]:
        """
        Process the reassignment of an action item, handling changes to assignee, status, comments, and other related properties based on the provided request.

        This function manages the logic for processing the reassignment of an action item, including
        determining the appropriate status, handling workflow transitions, and updating relevant
        properties like assignee and due date. It also prepares a change request and generates
        a status history entry for the action.

        Args:
            action (ActionItemForAssigneeRequestProcess):
                The action item that is being updated.
            request (UpdateActionAssigneeRequest):
                The request object containing reassignment details, including:
                - `new_assignee` (Optional[str]): The new assignee for the action item.
                - `reassign_status` (Optional[str]): The status of the reassignment (if any).
                - `assignee_comment` (Optional[str]): A comment from the assignee.
                - `approver_email` (list[str]): A list of email addresses for approvers.
            created_by_id (str):
                The external ID of the user who is initiating the reassignment.

        Returns:
            dict[ActionItemChangeRequest, list[str]]:
                - `ActionItemChangeRequest`: The generated change request for the reassignment.
                - `list[str]`: A list of user email addresses to be notified about the reassignment.

        Raises:
            Exception: If any errors occur during the reassignment process.

        Process Flow:
            1. **Reassignment Date**: Determines the reassignment date based on the request or defaults to a new due date.
            2. **Workflow Status**: Sets the workflow status to `PROGRESS` or approved/rejected based on the reassignment status.
            3. **Step Description**: Sets the step description to `REASSIGNMENT` for the reassignment process.
            4. **Change Request Type**: Sets the change request type to `REASSIGNMENT`.
            5. **History Action User**: Determines the history action user based on the reassignment status (e.g., `REASSIGN`, `REASSIGN_APPROVED`, `REASSIGN_REJECTED`).
            6. **Change Request Generation**: Prepares the change request and history entry for the reassignment.
            7. **Notification**: Identifies users (e.g., the original assignee and approvers) who should be notified about the reassignment.

        Example:
            action = ActionByIdResult(
                external_id="ACT-20241203184516-0001",
                assigned_to=User(email="<EMAIL>"),
                change_requests=[ChangeRequest(approval_workflow=ApprovalWorkflow(status=Node(external_id="PROGRESS")))],
                due_date="2024-12-31",
            )
            request = UpdateActionAssigneeRequest(
                external_id="ACT-20241203184516-0001",
                new_assignee="<EMAIL>",
                reassign_status=None,
                assignee_comment="Reassigned due to priority change",
                approver_email=["<EMAIL>"]
            )
            created_by_id = "user123"

            change_request, users_to_notify = _process_reassigment(action, request, created_by_id)
            print(change_request)
            print(users_to_notify)

        """
        reassignment_date = (
            self._process_due_date(5)
            if request.reassign_status is None
            else action.due_date.strftime("%Y-%m-%d") if action.due_date else ""
        )

        workflow_status = (
            ApprovalWorkflowStatusEnum.PROGRESS
            if request.reassign_status is None
            else (
                ApprovalWorkflowStatusEnum.APPROVED
                if request.reassign_status == WorkflowStatus.APPROVED
                else ApprovalWorkflowStatusEnum.REJECTED
            )
        )

        step_description = (
            ApprovalWorkflowStepDescriptionEnum.REASSIGNMENT
            if request.reassign_status is None
            else None
        )
        properties_to_change = (
            request.new_assignee if request.reassign_status is None else None
        )
        change_request_type = (
            ChangeRequestTypeEnum.REASSIGNMENT
            if request.reassign_status is None
            else None
        )
        comments = (
            request.assignee_comment
            if request.reassign_status is None
            else request.reassign_comment
        )
        action_user_history = (
            HistoryActionUser.REASSIGN
            if request.reassign_status is None
            else (
                HistoryActionUser.REASSIGN_APPROVED
                if request.reassign_status == WorkflowStatus.APPROVED
                else HistoryActionUser.REASSIGN_REJECTED
            )
        )
        action_status = (
            ActionStatusEnum.REASSIGNMENT_PERIOD
            if request.reassign_status is None
            else ActionStatusEnum.ASSIGNED
        )

        change_request = None
        email = created_by_id
        if created_by_id.startswith("UserAzureAttribute_"):
            email = created_by_id[len("UserAzureAttribute_") :]
        workflow_created_by_id = email if request.reassign_status is None else None
        start_date = self._now_datetime_str if request.reassign_status is None else None
        end_date = None if request.reassign_status is None else self._now_datetime_str

        new_assigned_to = (
            f"UserAzureAttribute_{request.new_assignee}"
            if request.reassign_status == WorkflowStatus.APPROVED
            else None
        )

        if request.reassign_status is not None:
            change_request = next(
                (
                    cr
                    for cr in action.change_requests or []
                    if cr.approval_workflow
                    and cr.approval_workflow.status
                    and cr.approval_workflow.status.external_id
                    == ApprovalWorkflowStatusEnum.PROGRESS
                ),
                None,
            )

        history = self.generate_upsert_status_history_instance(
            external_id=action.external_id,
            space=action.space,
            status_id=action_status,
            action_user=action_user_history,
            comment=comments or "",
            created_by_id=created_by_id,
        )

        change_request_upsert = self._upsert_assignee_requests(
            action=action,
            status_id=action_status,
            history=history,
            display_due_date=reassignment_date,
            workflow_status=workflow_status,
            change_request_type=change_request_type,
            comments=request.assignee_comment,
            start_date=start_date,
            end_date=end_date,
            properties_to_change=properties_to_change,
            step_description=step_description,
            change_request=change_request,
            workflow_created_by_id=workflow_created_by_id,
            new_assigned_to=new_assigned_to,
        )

        assignee_email: str | None = (
            action.assigned_to.user.email
            if action.assigned_to and action.assigned_to.user
            else None
        )

        users_to_notify = [assignee_email] if assignee_email else []
        if request.reassign_status is None:
            approver_emails = request.approver_email or []
            users_to_notify = list(set(users_to_notify + approver_emails))

        return change_request_upsert, users_to_notify

    def _process_extension(
        self,
        action: ActionByIdResult,
        request: UpdateActionAssigneeRequest,
        created_by_id: str,
    ) -> tuple[ActionItemChangeRequest, list[str]]:
        """
        Process the extension of an action item's due date, handling changes to the due date, status, comments, and other related properties based on the provided request.

        This function manages the logic for extending the due date of an action item, including
        determining the appropriate status, handling workflow transitions, and updating relevant
        properties like due date and comments. It also prepares a change request and generates
        a status history entry for the action.

        Args:
            action (ActionByIdResult):
                The action item that is being updated.
            request (UpdateActionAssigneeRequest):
                The request object containing extension details, including:
                - `new_due_date` (Optional[str]): The new due date for the action item.
                - `extension_status` (Optional[str]): The status of the extension (if any).
                - `assignee_comment` (Optional[str]): A comment from the assignee.
                - `approver_email` (list[str]): A list of email addresses for approvers.
            created_by_id (str):
                The external ID of the user who is initiating the extension.

        Returns:
            dict[ActionItemChangeRequest, list[str]]:
                - `ActionItemChangeRequest`: The generated change request for the extension.
                - `list[str]`: A list of user email addresses to be notified about the extension.

        Raises:
            Exception: If any errors occur during the extension process.

        Process Flow:
            1. **Extension Date**: Determines the new extension date, considering the request or defaults.
            2. **Workflow Status**: Sets the workflow status to `PROGRESS` or approved/rejected based on the extension status.
            3. **Step Description**: Sets the step description to `EXTENSION` for the extension process.
            4. **Change Request Type**: Defines the type of change request as `DUE_DATE` for the extension.
            5. **History Action User**: Determines the history action user based on the extension status (e.g., `EXTENSION`, `EXTENSION_APPROVED`, `EXTENSION_REJECTED`).
            6. **Change Request Generation**: Prepares the change request and history entry for the extension.
            7. **Notification**: Identifies users (e.g., the original assignee and approvers) who should be notified about the extension.

        Example:
            action = ActionByIdResult(
                external_id="ACT-20241203184516-0001",
                assigned_to=User(email="<EMAIL>"),
                change_requests=[ChangeRequest(approval_workflow=ApprovalWorkflow(status=Node(external_id="PROGRESS")))],
                due_date="2024-12-31",
            )
            request = UpdateActionAssigneeRequest(
                external_id="ACT-20241203184516-0001",
                new_due_date="2025-01-15",
                extension_status=None,
                assignee_comment="Extended due date due to unforeseen circumstances",
                approver_email=["<EMAIL>"]
            )
            created_by_id = "user123"

            change_request, users_to_notify = _process_extension(action, request, created_by_id)
            print(change_request)
            print(users_to_notify)

        """
        extension_date = (
            self._process_due_date(5)
            if request.extension_status is None
            else (
                request.new_due_date
                if request.extension_status == WorkflowStatus.APPROVED
                else action.due_date.strftime("%Y-%m-%d") if action.due_date else ""
            )
        )
        workflow_status = (
            ApprovalWorkflowStatusEnum.PROGRESS
            if request.extension_status is None
            else (
                ApprovalWorkflowStatusEnum.APPROVED
                if request.extension_status == WorkflowStatus.APPROVED
                else ApprovalWorkflowStatusEnum.REJECTED
            )
        )

        step_description = (
            ApprovalWorkflowStepDescriptionEnum.EXTENSION
            if request.extension_status is None
            else None
        )
        properties_to_change = (
            request.new_due_date if request.extension_status is None else None
        )
        change_request_type = (
            ChangeRequestTypeEnum.DUE_DATE if request.extension_status is None else None
        )
        comments = (
            request.assignee_comment
            if request.extension_status is None
            else request.extension_comment
        )
        action_user_history = (
            HistoryActionUser.EXTENSION
            if request.extension_status is None
            else (
                HistoryActionUser.EXTENSION_APPROVED
                if request.extension_status == WorkflowStatus.APPROVED
                else HistoryActionUser.EXTENSION_REJECTED
            )
        )
        action_status = (
            ActionStatusEnum.DUE_DATE_EXTENSION_PERIOD
            if request.extension_status is None
            else ActionStatusEnum.ASSIGNED
        )

        change_request = None
        email = created_by_id
        if created_by_id.startswith("UserAzureAttribute_"):
            email = created_by_id[len("UserAzureAttribute_") :]
        workflow_created_by_id = email if request.extension_status is None else None
        start_date = (
            self._now_datetime_str if request.extension_status is None else None
        )
        end_date = None if request.extension_status is None else self._now_datetime_str

        new_due_date = (
            request.new_due_date
            if request.extension_status == WorkflowStatus.APPROVED
            else None
        )

        if request.extension_status is not None:
            change_request = next(
                (
                    cr
                    for cr in action.change_requests or []
                    if cr.approval_workflow
                    and cr.approval_workflow.status
                    and cr.approval_workflow.status.external_id
                    == ApprovalWorkflowStatusEnum.PROGRESS
                ),
                None,
            )

        history = self.generate_upsert_status_history_instance(
            external_id=action.external_id,
            space=action.space,
            status_id=action_status,
            action_user=action_user_history,
            comment=comments or "",
            created_by_id=created_by_id,
        )

        change_request_upsert = self._upsert_assignee_requests(
            action=action,
            status_id=action_status,
            history=history,
            display_due_date=extension_date or "",
            workflow_status=workflow_status,
            change_request_type=change_request_type,
            comments=request.assignee_comment,
            start_date=start_date,
            end_date=end_date,
            properties_to_change=properties_to_change,
            step_description=step_description,
            change_request=change_request,
            workflow_created_by_id=workflow_created_by_id,
            new_due_date=new_due_date,
        )

        assignee_email: str | None = (
            action.assigned_to.user.email
            if action.assigned_to and action.assigned_to.user
            else None
        )

        users_to_notify = [assignee_email] if assignee_email else []
        if request.extension_status is None:
            approver_emails = request.approver_email or []
            users_to_notify = list(set(users_to_notify + approver_emails))

        return change_request_upsert, users_to_notify

    def _process_challenge(
        self,
        action: ActionByIdResult | ActionResult,
        request: UpdateActionAssigneeRequest,
        created_by_id: str,
    ) -> tuple[ActionItemChangeRequest, list[str]]:
        """
        Process the reassignment of an action item, handling changes to assignee, status, comments, and other related properties based on the provided request.

        This function manages the logic for processing the reassignment of an action item, including
        determining the appropriate status, handling workflow transitions, and updating relevant
        properties like assignee and due date. It also prepares a change request and generates
        a status history entry for the action.

        Args:
            action (ActionByIdResult):
                The action item that is being updated.
            request (UpdateActionAssigneeRequest):
                The request object containing reassignment details, including:
                - `new_assignee` (Optional[str]): The new assignee for the action item.
                - `reassign_status` (Optional[str]): The status of the reassignment (if any).
                - `assignee_comment` (Optional[str]): A comment from the assignee.
                - `approver_email` (list[str]): A list of email addresses for approvers.
            created_by_id (str):
                The external ID of the user who is initiating the reassignment.

        Returns:
            dict[ActionItemChangeRequest, list[str]]:
                - `ActionItemChangeRequest`: The generated change request for the reassignment.
                - `list[str]`: A list of user email addresses to be notified about the reassignment.

        Raises:
            Exception: If any errors occur during the reassignment process.

        Process Flow:
            1. **Reassignment Date**: Determines the reassignment date based on the request or defaults to a new due date.
            2. **Workflow Status**: Sets the workflow status to `PROGRESS` or approved/rejected based on the reassignment status.
            3. **Step Description**: Sets the step description to `REASSIGNMENT` for the reassignment process.
            4. **Change Request Type**: Sets the change request type to `REASSIGNMENT`.
            5. **History Action User**: Determines the history action user based on the reassignment status (e.g., `REASSIGN`, `REASSIGN_APPROVED`, `REASSIGN_REJECTED`).
            6. **Change Request Generation**: Prepares the change request and history entry for the reassignment.
            7. **Notification**: Identifies users (e.g., the original assignee and approvers) who should be notified about the reassignment.

        Example:
            action = ActionByIdResult(
                external_id="ACT-20241203184516-0001",
                assigned_to=User(email="<EMAIL>"),
                change_requests=[ChangeRequest(approval_workflow=ApprovalWorkflow(status=Node(external_id="PROGRESS")))],
                due_date="2024-12-31",
            )
            request = UpdateActionAssigneeRequest(
                external_id="ACT-20241203184516-0001",
                new_assignee="<EMAIL>",
                reassign_status=None,
                assignee_comment="Reassigned due to priority change",
                approver_email=["<EMAIL>"]
            )
            created_by_id = "user123"

            change_request, users_to_notify = _process_challenge(action, request, created_by_id)
            print(change_request)
            print(users_to_notify)

        """
        challenge_date = (
            self._process_due_date(5)
            if request.challenge_status is None
            else action.due_date.strftime("%Y-%m-%d") if action.due_date else ""
        )
        workflow_status = (
            ApprovalWorkflowStatusEnum.PROGRESS
            if request.challenge_status is None
            else ApprovalWorkflowStatusEnum.APPROVED
        )

        step_description = (
            ApprovalWorkflowStepDescriptionEnum.CHALLENGE
            if request.reassign_status is None
            else None
        )
        properties_to_change = (
            request.new_assignee if request.reassign_status is None else None
        )
        change_request_type = (
            ChangeRequestTypeEnum.CHALLENGE
            if request.challenge_status is None
            else None
        )
        comments = request.assignee_comment if request.challenge_status is None else ""
        action_user_history = (
            HistoryActionUser.CHALLENGE
            if request.challenge_status is None
            else HistoryActionUser.CHALLENGE_APPROVED
        )
        action_status = (
            ActionStatusEnum.CHALLENGE_PERIOD
            if request.challenge_status is None
            else (
                ActionStatusEnum.ASSIGNED
                if request.challenge_status == WorkflowStatus.APPROVED
                else ActionStatusEnum.CANCELLED
            )
        )

        change_request = None
        email = created_by_id
        if created_by_id.startswith("UserAzureAttribute_"):
            email = created_by_id[len("UserAzureAttribute_") :]
        workflow_created_by_id = email if request.challenge_status is None else None
        start_date = (
            self._now_datetime_str if request.challenge_status is None else None
        )
        end_date = None if request.challenge_status is None else self._now_datetime_str

        if request.challenge_status is not None:
            change_request = next(
                (
                    cr
                    for cr in action.change_requests or []
                    if cr.approval_workflow
                    and cr.approval_workflow.status
                    and cr.approval_workflow.status.external_id
                    == ApprovalWorkflowStatusEnum.PROGRESS
                ),
                None,
            )

        history = self.generate_upsert_status_history_instance(
            external_id=action.external_id,
            space=action.space,
            status_id=action_status,
            action_user=action_user_history,
            comment=comments or "",
            created_by_id=created_by_id,
        )

        change_request_upsert = self._upsert_assignee_requests(
            action=action,
            status_id=action_status,
            history=history,
            display_due_date=challenge_date,
            workflow_status=workflow_status,
            change_request_type=change_request_type,
            comments=request.assignee_comment,
            start_date=start_date,
            end_date=end_date,
            properties_to_change=properties_to_change,
            step_description=step_description,
            change_request=change_request,
            workflow_created_by_id=workflow_created_by_id,
        )

        owner_email: str | None = (
            action.owner.user.email if action.owner and action.owner.user else None
        )

        users_to_notify = [owner_email] if owner_email else []

        return change_request_upsert, users_to_notify

    def _get_options(
        self,
        site_id: str,
    ) -> tuple[
        list[ReportingUnitResult],
        list[ReportingLocationResult],
        list[CategoryResult],
        list[SubCategoryResult],
        list[SiteSpecificCategoryResult],
        list[CategoryConfigurationByFilterResult],
        list[UsersBySiteResult],
    ]:
        """Fetch dropdown options (units, locations, categories, subcategories, site specific categories) for the template."""
        client = self._action_item_client

        units = client.reporting_unit.get_reporting_units(
            GetReportingUnitsRequest(reporting_site_external_id=site_id),
        )

        locations = client.reporting_location.get_reporting_locations(
            GetReportingLocationsRequest(reporting_site_external_id=site_id),
        )

        categories = client.category_configuration.get_categories(
            GetCategoriesRequest(),
        )

        sub_categories = client.category_configuration.get_sub_categories(
            GetSubCategoriesRequest(),
        )

        site_specific_categories = (
            client.category_configuration.get_site_specific_categories(
                GetSiteSpecificCategoriesRequest(reporting_site_external_id=site_id),
            )
        )

        category_configs = client.category_configuration.get_category_configurations(
            GetCategoryConfigurationByFilterRequest(
                reporting_site_external_id=site_id,
                page_size=AGGREGATE_LIMIT,
            ),
            filter_only_by_site=True,
        ).data

        users = client.user_complement.get_users_by_site(
            GetUsersRequest(reporting_site_external_id=site_id),
        )

        return (
            units.data if units else [],
            locations.data if locations else [],
            categories.data if categories else [],
            sub_categories.data if sub_categories else [],
            site_specific_categories.data if site_specific_categories else [],
            category_configs,
            users,
        )

    def _initialize_action_item(
        self,
        site_external_id: str,
        user_id: str,
        event_id: Optional[str],
    ) -> dict[str, Optional[Any]]:
        """Initialize default values for an action item."""
        action_item: dict[str, Optional[Any]] = dict.fromkeys(
            [
                "title",
                "description",
                "ownerId",
                "assignedToIds",
                "reportingUnitId",
                "reportingLocationId",
                "sourceInformation",
                "categoryId",
                "subCategoryId",
                "siteSpecificCategoryId",
                "assignmentDate",
                "dueDate",
            ],
            None,
        )

        action_item.update(
            {
                "reportingSiteId": site_external_id,
                "createdById": f"UserAzureAttribute_{user_id}",
                "applicationId": "APP-AIM",
                "actionItemKindId": "ACTK-oneTime",
                "evidenceRequired": False,
                "sourceTypeId": (
                    "AST-AIM-AIMEvent" if event_id else "AST-AIM-AIMScratch"
                ),
            },
        )

        if event_id:
            action_item.update(
                {
                    "sourceEventId": event_id,
                    "sourceId": event_id,
                    "sourceInformation": event_id,
                },
            )

        return action_item

    def _validate_text_field(
        self,
        row: Series,
        site_id: str,
        column_name: str,
        field_key: str,
        min_length: int,
        max_length: int,
        action_item: dict[str, Optional[Any]],
        row_errors: list[dict[str, str]],
        error_message: str,
    ) -> None:
        """Validate and assign a text field, with site validation for 'sourceInformation'."""
        try:
            text_value = row[column_name]

            if field_key == "sourceInformation" and pd.isna(text_value):
                if site_id == WAS_SITE_EXTERNAL_ID:
                    raise ValueError(BulkUploadErrorMessageTypes.MISSING_FIELD)
                return

            if pd.isna(text_value):
                raise ValueError(BulkUploadErrorMessageTypes.MISSING_FIELD)

            if not isinstance(text_value, str) or not (
                min_length <= len(text_value) <= max_length
            ):
                raise ValueError(error_message)

            action_item[field_key] = text_value

        except ValueError as ve:
            row_errors.append({"field": column_name, "error": str(ve)})

    def _validate_user_field(
        self,
        row: Series,
        column_name: str,
        field_key: str,
        action_item: dict[str, Optional[Any]],
        row_errors: list[dict[str, str]],
        users: list[UsersBySiteResult],
        as_list: bool = False,
    ):
        """Validate and assign an owner or assignee field."""
        try:
            user = self._validate_email_field(row[column_name], users)
            user_azure_attribute_id = (
                f"UserAzureAttribute_{user.external_id}" if user else ""
            )
            action_item[field_key] = (
                [user_azure_attribute_id] if as_list else user_azure_attribute_id
            )
        except ValueError as ve:
            row_errors.append({"field": column_name, "error": str(ve)})

    def _validate_email_field(
        self,
        email: str,
        users: list[UsersBySiteResult],
    ) -> UsersBySiteResult | None:
        """Validate user email format and existence."""
        if pd.isna(email):
            raise ValueError(BulkUploadErrorMessageTypes.MISSING_FIELD)

        if not re.fullmatch(r"^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$", email):
            raise ValueError(BulkUploadErrorMessageTypes.INVALID_EMAIL)

        try:
            user = next((u for u in users if u.email == email), None)

            if user is None:
                raise ValueError(BulkUploadErrorMessageTypes.NOT_FOUND_EMAIL)

        except Exception as err:
            self._log.error(f"Error retrieving user by email {email}. Error: {err}")
            raise ValueError(BulkUploadErrorMessageTypes.NOT_FOUND_EMAIL)
        else:
            return user

    def _validate_unit_field(
        self,
        row: Series,
        column_name: str,
        action_item: dict[str, Optional[Any]],
        row_errors: list[dict[str, str]],
        units: list[ReportingUnitResult],
    ) -> None:
        """Validate and assign unit field."""
        try:
            unit_row = row[column_name]

            if pd.isna(unit_row):
                raise ValueError(BulkUploadErrorMessageTypes.MISSING_FIELD)

            unit = next((u for u in units if u.description == unit_row), None)

            if unit is None:
                raise ValueError(BulkUploadErrorMessageTypes.INVALID_UNIT)

            action_item["reportingUnitId"] = unit.external_id

        except ValueError as ve:
            row_errors.append({"field": column_name, "error": str(ve)})

    def _validate_location_field(
        self,
        row: Series,
        site_id: str,
        column_name: str,
        action_item: dict[str, Optional[Any]],
        row_errors: list[dict[str, str]],
        locations: list[ReportingLocationResult],
    ) -> None:
        """Validate and assign location field."""
        try:
            location_row = row[column_name]

            if pd.isna(location_row):
                if site_id == WAS_SITE_EXTERNAL_ID:
                    raise ValueError(BulkUploadErrorMessageTypes.MISSING_FIELD)
                return

            location = next(
                (loc for loc in locations if loc.description == location_row),
                None,
            )

            if location is None:
                raise ValueError(BulkUploadErrorMessageTypes.INVALID_LOCATION)

            if (
                not location.reporting_unit
                or location.reporting_unit.external_id != action_item["reportingUnitId"]
            ):
                raise ValueError(BulkUploadErrorMessageTypes.INVALID_UNIT_AND_LOCATION)

            action_item["reportingLocationId"] = location.external_id

        except ValueError as ve:
            row_errors.append({"field": column_name, "error": str(ve)})

    def _validate_category_field(
        self,
        row: Series,
        column_name: str,
        action_item: dict[str, Optional[Any]],
        row_errors: list[dict[str, str]],
        categories: list[CategoryResult],
    ) -> None:
        """Validate and assign category field."""
        try:
            category_row = row[column_name]

            if pd.isna(category_row):
                raise ValueError(BulkUploadErrorMessageTypes.MISSING_FIELD)

            category = next(
                (cat for cat in categories if cat.name == category_row),
                None,
            )

            if category is None:
                raise ValueError(BulkUploadErrorMessageTypes.INVALID_CATEGORY)

            action_item["categoryId"] = category.external_id

        except ValueError as ve:
            row_errors.append({"field": column_name, "error": str(ve)})

    def _validate_sub_category_field(
        self,
        row: Series,
        column_name: str,
        action_item: dict[str, Optional[Any]],
        row_errors: list[dict[str, str]],
        sub_categories: list[SubCategoryResult],
    ) -> None:
        """Validate and assign subcategory field."""
        try:
            sub_category_row = row[column_name]

            if pd.isna(sub_category_row):
                raise ValueError(BulkUploadErrorMessageTypes.MISSING_FIELD)

            sub_category = next(
                (sub for sub in sub_categories if sub.name == sub_category_row),
                None,
            )

            if sub_category is None:
                raise ValueError(BulkUploadErrorMessageTypes.INVALID_SUB_CATEGORY)

            action_item["subCategoryId"] = sub_category.external_id

        except ValueError as ve:
            row_errors.append({"field": column_name, "error": str(ve)})

    def _validate_site_specific_category_field(
        self,
        row: Series,
        site_id: str,
        column_name: str,
        action_item: dict[str, Optional[Any]],
        row_errors: list[dict[str, str]],
        site_specific_categories: list[SiteSpecificCategoryResult],
    ) -> None:
        """Validate and assign site-specific category field."""
        try:
            site_specific_category_row = row[column_name]

            if pd.isna(site_specific_category_row):
                if site_id == WAS_SITE_EXTERNAL_ID:
                    raise ValueError(BulkUploadErrorMessageTypes.MISSING_FIELD)
                return

            site_specific_category = next(
                (
                    cat
                    for cat in site_specific_categories
                    if cat.name == site_specific_category_row
                ),
                None,
            )

            if site_specific_category is None:
                raise ValueError(
                    BulkUploadErrorMessageTypes.INVALID_SITE_SPECIFIC_CATEGORY,
                )

            action_item["siteSpecificCategoryId"] = site_specific_category.external_id

        except ValueError as ve:
            row_errors.append({"field": column_name, "error": str(ve)})

    def _validate_category_configuration(
        self,
        column_names: list[str],
        action_item: dict[str, Optional[Any]],
        row_errors: list[dict[str, str]],
        category_configs: list[CategoryConfigurationByFilterResult],
    ) -> None:
        """Validate the category configuration for a given action item."""

        def _is_site_specific_category_match(
            config: CategoryConfigurationByFilterResult,
            site_specific_category: str,
        ) -> bool:
            if config.site_specific_category and site_specific_category:
                return (
                    config.site_specific_category.external_id == site_specific_category
                )
            return bool(
                not config.site_specific_category and not site_specific_category,
            )

        try:
            category = action_item["categoryId"]
            sub_category = action_item["subCategoryId"]
            site_specific_category = action_item["siteSpecificCategoryId"] or None

            category_config = next(
                (
                    config
                    for config in category_configs
                    if config.category
                    and config.category.external_id == category
                    and config.action_item_sub_category
                    and config.action_item_sub_category.external_id == sub_category
                    and site_specific_category
                    and _is_site_specific_category_match(config, site_specific_category)
                ),
                None,
            )

            if category_config:
                raise ValueError(
                    BulkUploadErrorMessageTypes.INVALID_CATEGORY_CONFIGURATION,
                )
        except ValueError as ve:
            row_errors.append(
                {
                    "field": f"{column_names[0]}, {column_names[1]} and {column_names[2]}",
                    "error": str(ve),
                },
            )

    def _validate_priority_field(
        self,
        row: Series,
        column_name: str,
        action_item: dict[str, Optional[Any]],
        row_errors: list[dict[str, str]],
    ) -> None:
        """Validate and assign priority field."""
        try:
            priority_row = row[column_name]
            if pd.isna(priority_row):
                return
            if priority_row not in {"High", "Medium", "Low"}:
                raise ValueError(BulkUploadErrorMessageTypes.INVALID_PRIORITY)
            action_item["priority"] = priority_row.lower()
        except ValueError as ve:
            row_errors.append({"field": column_name, "error": str(ve)})

    def _validate_date_fields(
        self,
        row: Series,
        column_names: list[str],
        action_item: dict[str, Optional[Any]],
        row_errors: list[dict[str, str]],
    ) -> None:
        """Validate and assign date fields."""
        assignment_date = None
        today = datetime.now(tz=UTC).date()

        try:
            assignment_date = self._validate_date(row[column_names[0]])
            if assignment_date < today:
                raise ValueError(BulkUploadErrorMessageTypes.INVALID_DATE)
            action_item["assignmentDate"] = assignment_date
        except ValueError as ve:
            row_errors.append({"field": column_names[0], "error": str(ve)})

        try:
            due_date = self._validate_date(row[column_names[1]])
            if assignment_date and due_date < assignment_date:
                raise ValueError(BulkUploadErrorMessageTypes.INVALID_DUE_DATE)
            if due_date < today:
                raise ValueError(BulkUploadErrorMessageTypes.INVALID_DATE)
            action_item["dueDate"] = due_date
        except ValueError as ve:
            row_errors.append({"field": column_names[1], "error": str(ve)})

    def _validate_date(self, date_value: Any) -> date:  # noqa: ANN401
        if pd.isna(date_value):
            raise ValueError(BulkUploadErrorMessageTypes.MISSING_FIELD)
        if isinstance(date_value, datetime):
            return date_value.date()
        raise ValueError(BulkUploadErrorMessageTypes.INVALID_DATE_TYPE)

    def _log_errors(
        self,
        index: Hashable,
        row_errors: list[dict[str, str]],
        errors_list: list[dict[str, str]],
    ) -> None:
        """Log errors for a specific row."""
        line_number = (index + 2) if isinstance(index, (int, float)) else None
        errors_list.extend(
            [
                {
                    "line": str(line_number) if line_number is not None else "Unknown",
                    "field": error["field"].encode("utf-8").decode("utf-8"),
                    "error": error["error"].encode("utf-8").decode("utf-8"),
                }
                for error in row_errors
            ],
        )

    def _extract_icap_action_id_from_object_type(self, input_string: str | None) -> str:
        """Extract the ICAP Action ID from an objectType string (e.g., 'ICAP-123-XYZ')."""
        if input_string is None:
            return ""
        parts = input_string.split("-")

        if len(parts) >= 3:
            return parts[1]
        return ""

    def _generate_file_response(self, workbook):
        """Generate an HTTP response to download the Excel workbook."""
        excel_file_buffer = BytesIO()
        workbook.save(excel_file_buffer)
        excel_file_buffer.seek(0)

        headers = {
            "Content-Disposition": "attachment; filename=action_items_template.xlsx",
            "Content-Type": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            "Access-Control-Allow-Origin": "*",
        }

        return func.HttpResponse(
            excel_file_buffer.read(),
            headers=headers,
            status_code=HTTPStatus.OK,
        )
