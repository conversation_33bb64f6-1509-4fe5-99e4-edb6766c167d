/* eslint-disable no-console */
/* eslint-disable node/prefer-global/process */
// eslint-disable-next-line unicorn/prefer-node-protocol
const { execSync } = require('child_process')
const fs = require('node:fs')
const path = require('node:path')

const portalDir = path.resolve(__dirname)
const currentDir = path.resolve(process.cwd())
if (currentDir !== portalDir) {
    if (!fs.existsSync(portalDir)) {
        console.error(`Error: Directory ${portalDir} does not exist.`)
        process.exit(1)
    }
    process.chdir(portalDir) // Change to portal directory
}
const args = process.argv.slice(2) // Get command-line arguments
const shouldFix = args.includes('--fix') // Check if --fix is passed

// Run the git command to get staged files
const files = execSync('git diff --cached --name-only --diff-filter=ACMR', {
    encoding: 'utf-8',
})
    .split('\n')
    .map((file) => file.trim())
    .filter((file) => file.endsWith('.ts') || file.endsWith('.tsx'))

if (!files.length) {
    console.warn('No staged')
    process.exit(0)
}

// Replace 'portal/' with an empty string and escape spaces
const modifiedFiles = files.map((file) => file.replace('portal/', '').replace(/ /g, '\\ '))

// Construct the ESLint command
const eslintCommand = `npx eslint@8.57.1 ${shouldFix ? '--fix' : ''} ${modifiedFiles.join(' ')}`

// Execute ESLint on the modified files
let eslintFailed = false
try {
    console.log('Running ESLint on staged files...')
    execSync(eslintCommand, { stdio: 'inherit' })
} catch {
    eslintFailed = true
}

if (shouldFix) {
    // Check if any of the originally staged files were modified by ESLint
    const newFiles = execSync('git diff --name-only', {
        encoding: 'utf-8',
    })
        .split('\n')
        .map((file) => file.trim().replace('portal/', '').replace(/ /g, '\\ '))
        .filter((file) => modifiedFiles.includes(file))

    if (newFiles.length > 0) {
        console.warn('🔨 ESLint auto-fixed these files:')
        modifiedFiles.forEach((file) => console.log(`  - ${file}`))
    }
}
if (eslintFailed) {
    process.exit(1)
}
