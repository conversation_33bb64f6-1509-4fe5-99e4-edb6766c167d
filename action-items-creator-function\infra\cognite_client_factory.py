from typing import Optional
from cognite.client import Cognite<PERSON>lient, ClientConfig
from cognite.client.credentials import OAuthClientCredentials, Token
import os
import sys


script_dir = os.path.dirname(os.path.abspath(__file__))
project_dir = os.path.dirname(script_dir)
sys.path.append(project_dir)
from models.settings import Settings


class CogniteClientFactory:
    @staticmethod
    def create(
        settings: Settings, override_token: Optional[str] = None
    ) -> CogniteClient:
        return CogniteClient(
            config=CogniteClientFactory.__create_client_config(settings, override_token)
        )

    @staticmethod
    def __create_credentials(
        settings: Settings, override_token: Optional[str]
    ) -> OAuthClientCredentials | Token:
        if override_token is not None:
            return Token(lambda: override_token)

        return OAuthClientCredentials(
            token_url=settings.auth_token_uri,
            client_id=settings.auth_client_id,
            client_secret=settings.auth_secret,
            scopes=settings.get_auth_scopes,
        )

    @staticmethod
    def __create_client_config(
        settings: Settings, override_token: Optional[str]
    ) -> ClientConfig:
        return ClientConfig(
            client_name=settings.cognite_client_name,
            project=settings.cognite_project,
            credentials=CogniteClientFactory.__create_credentials(
                settings, override_token
            ),
            base_url=settings.cognite_base_uri,
        )
