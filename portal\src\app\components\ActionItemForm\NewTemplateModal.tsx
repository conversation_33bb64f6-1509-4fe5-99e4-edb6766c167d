import { useLoading } from '@/app/common/contexts/LoadingContext'
import { useUpsertActionItemLink } from '@/app/common/hooks/mutations/useUpsertActionItemLink'
import { useUpsertApprovalWorkflowStep } from '@/app/common/hooks/mutations/useUpsertApprovalWorkflowStep'
import { useUpsertStatusHistoryInstance } from '@/app/common/hooks/mutations/useUpsertStatusHistoryInstance'
import { useUpsertAction } from '@/app/common/hooks/mutations/useUpsertAction'
import { TemplateConfigForm } from '@/app/common/models/template-configuration'
import {
    generateNewExternalId,
    generateUpsertStatusHistoryInstance,
    PREFIX_USER_AZURE_ATTRIBUTE,
} from '@/app/common/utils'
import { EntityType, GetSpace } from '@/app/common/utils/space-util'
import { ActionFormSchema } from '.'
import { formatRecurrence } from '@/app/common/utils/format-Recurrence'
import { RecurrenceForm } from '@/app/common/models/forms/recurrence-form'
import { useCognite } from '@/app/common/hooks'
import { buildUploadFilesRequest } from '@/app/common/utils/files'
import { AzureFunctionClient } from '@/app/common/clients/azure-function-client'
import { Attachment, RecurrenceInstanceForm } from '@/app/common/models/action-detail'
import { TemplateRequest } from '@/app/common/models/template-action'
import { AllActionStatusExternalIdEnum } from '@/app/common/enums/ActionItemStatusEnum'
import { ActionItemKindExternalIdEnum } from '@/app/common/enums/ActionItemKindEnum'
import dayjs from 'dayjs'
import { generateWorkflowStep } from '@/app/common/utils/workflow-step'
import { translate } from '@/app/common/utils/generate-translate'
import { useSnackbar } from '@/app/common/contexts/SnackbarContext'
import TemplateModal from '../ModalComponent/TemplateModal'
import { useState } from 'react'
import { useTemplateQuery } from '@/app/common/hooks/pagination/useTemplateQuery'
import { ClnButton } from '@celanese/ui-lib'
import { ActionItemLink } from '@/app/common/models/link'
import { FileSourceStepEnum } from '@/app/common/enums/FileSourceStepEnum'
import { UserRolesPermission } from '@celanese/celanese-sdk'

type NewTemplateModalProps = {
    siteId: string
    activeUser: UserRolesPermission
    disabled: boolean
    formValues: ActionFormSchema
    linkList?: ActionItemLink[]
    recurrenceSavedValue?: RecurrenceForm
}
export function NewTemplateModal({
    siteId,
    activeUser,
    linkList,
    disabled,
    formValues,
    recurrenceSavedValue,
}: NewTemplateModalProps) {
    const azureFunctionClient = new AzureFunctionClient()

    const { cogniteClient } = useCognite()
    const { showSnackbar } = useSnackbar()
    const { showLoading } = useLoading()

    const [updateAction] = useUpsertAction()
    const [updateStatusHistory] = useUpsertStatusHistoryInstance()
    const [updateWorkflowStep] = useUpsertApprovalWorkflowStep()
    const [updateLink] = useUpsertActionItemLink()

    const [saveTemplateModalOpen, setSaveTemplateModalOpen] = useState<boolean>(false)
    const { queryResult: actionsTemplateName, loading: loadingTemplate } = useTemplateQuery({ siteId })

    const handleAlert = (message: string, error: boolean = false) => {
        showSnackbar(message, error ? 'error' : 'success', 'new-template')
    }
    const createTemplate = async (template: TemplateConfigForm) => {
        try {
            showLoading(true)
            const templateExternalId = generateNewExternalId('TPLTCONF', 1)
            const actionExternalId = generateNewExternalId('ACT', 1)
            const actionSpace = GetSpace(EntityType.Instance, siteId.replace(/^STS-/, ''))

            const mappedTemplateRole: any[] | undefined =
                template?.roles?.map((role) => ({
                    node: {
                        externalId: role || '',
                        space: GetSpace(EntityType.UMG),
                    },
                    edge: {
                        externalId: `${templateExternalId}-${role}`,
                        space: actionSpace,
                    },
                })) ?? undefined

            const mappedTemplateUsers: any[] | undefined =
                template?.users?.map((user) => ({
                    node: {
                        externalId: user || '',
                        space: GetSpace(EntityType.UMG),
                    },
                    edge: {
                        externalId: `${templateExternalId}-${user}`,
                        space: actionSpace,
                    },
                })) ?? undefined

            const mappedAssignees = formValues.assignees?.map((userId) => {
                const formattedUserId = userId.startsWith(PREFIX_USER_AZURE_ATTRIBUTE)
                    ? userId
                    : `${PREFIX_USER_AZURE_ATTRIBUTE}${userId}`

                return {
                    node: {
                        externalId: formattedUserId,
                        space: GetSpace(EntityType.UMG),
                    },
                    edge: {
                        externalId: `${actionExternalId}-${formattedUserId}`,
                        space: actionSpace,
                    },
                }
            })

            const recurrenceInstance = recurrenceSavedValue ? formatRecurrence(siteId, recurrenceSavedValue) : undefined

            const formattedRecurrence = recurrenceInstance
                ? {
                      externalId: generateNewExternalId('RCI', 1),
                      space: actionSpace,
                      weekDays: recurrenceInstance?.weekDays,
                      months: recurrenceInstance?.months,
                      dayOfTheMonth: recurrenceInstance?.dayOfTheMonth,
                      quarters: recurrenceInstance?.quarters,
                      monthOfTheYear: recurrenceInstance?.monthOfTheYear,
                      nextDates: recurrenceInstance?.nextDates ?? undefined,
                      startDate: recurrenceInstance?.startDate,
                      endDate: recurrenceInstance?.endDate,
                      recurrenceType: {
                          node: {
                              externalId: recurrenceInstance?.recurrenceType,
                              space: GetSpace(EntityType.Static),
                          },
                      },
                  }
                : undefined

            let newUploadedFilesIds = []
            if (formValues.newUploadedFiles && formValues.newUploadedFiles.length) {
                const uploadFilesRequest = await buildUploadFilesRequest(
                    cogniteClient,
                    formValues.newUploadedFiles,
                    activeUser.displayName,
                    siteId,
                    formValues.isPrivate,
                    FileSourceStepEnum.ActionCreation
                )

                newUploadedFilesIds = (await azureFunctionClient.uploadFiles(uploadFilesRequest)).externalIds
            }
            const oldUploadedFilesIds =
                formValues.oldUploadedFiles?.map((attachment: Attachment) => attachment.externalId) ?? []

            const templateRequest: TemplateRequest = {
                externalId: actionExternalId,
                space: actionSpace,
                application: { node: { externalId: 'APP-AIM', space: GetSpace(EntityType.UMG) } },
                currentStatus: {
                    node: {
                        externalId: AllActionStatusExternalIdEnum.Active,
                        space: GetSpace(EntityType.Static),
                    },
                },
                title: formValues.title,
                owner: {
                    node: {
                        externalId: `UserAzureAttribute_${formValues.owner}`,
                        space: GetSpace(EntityType.UMG),
                    },
                },
                reportingUnit: { node: { externalId: formValues.reportingUnit, space: GetSpace(EntityType.REF) } },
                reportingLocation:
                    formValues.reportingLocation && formValues.reportingLocation !== ''
                        ? { node: { externalId: formValues.reportingLocation, space: GetSpace(EntityType.REF) } }
                        : undefined,

                description: formValues.description,
                sourceInformation: formValues.sourceInformation,
                category: {
                    node: {
                        externalId: formValues.category,
                        space: GetSpace(EntityType.Static),
                    },
                },
                subCategory:
                    formValues.subCategory1 && formValues.subCategory1 !== ''
                        ? {
                              node: {
                                  externalId: formValues.subCategory1,
                                  space: GetSpace(EntityType.Static),
                              },
                          }
                        : undefined,
                siteSpecificCategory:
                    formValues.subCategory2 && formValues.subCategory2 !== ''
                        ? {
                              node: {
                                  externalId: formValues.subCategory2,
                                  space: actionSpace,
                              },
                          }
                        : undefined,
                assignees: mappedAssignees,
                attachments:
                    newUploadedFilesIds.length || oldUploadedFilesIds.length
                        ? [...newUploadedFilesIds, ...oldUploadedFilesIds]
                        : undefined,
                actionItemKind: {
                    node: { externalId: formValues.taskType, space: GetSpace(EntityType.Static) },
                },
                recurrenceInstance:
                    formValues.taskType === ActionItemKindExternalIdEnum.Recurring
                        ? {
                              node: {
                                  externalId: generateNewExternalId('RCI', 1),
                                  space: actionSpace,
                                  ...(formattedRecurrence as RecurrenceInstanceForm),
                              },
                          }
                        : undefined,
                voeActionItem: formValues.verificationOfEffectiveness,
                reportingSite: {
                    node: { externalId: siteId, space: GetSpace(EntityType.REF) },
                },
                priority: formValues.priority,
                createdBy: {
                    node: { externalId: `UserAzureAttribute_${activeUser.email}`, space: GetSpace(EntityType.UMG) },
                },
                isTemplate: true,
                evidenceRequired: formValues.evidenceRequired,
                templateConfiguration: {
                    node: {
                        externalId: templateExternalId,
                        space: actionSpace,
                        name: template.name,
                        description: template.description,
                        users: mappedTemplateUsers,
                        roles: mappedTemplateRole,
                    },
                },
            }

            if (formValues.approvalRequired || formValues.verificationRequired) {
                templateRequest.approvalWorkflow = {
                    node: {
                        externalId: generateNewExternalId('APWE', 1),
                        currentStep: 1,
                        startDate: dayjs().format('YYYY-MM-DD'),
                        space: actionSpace,
                        status: { node: { externalId: 'APWST-InProgress', space: 'APW-COR-ALL-REF' } },
                    },
                }
            }

            if (formValues.reportingLine) {
                templateRequest.reportingLine = {
                    node: { externalId: formValues.reportingLine, space: GetSpace(EntityType.REF) },
                }
            }

            const templateApproverWorkflowStep = formValues.approver
                ? generateWorkflowStep(
                      formValues.approver,
                      actionSpace,
                      true,
                      templateRequest.approvalWorkflow?.node.externalId
                  )
                : undefined

            const templateVerifierWorkflowStep = formValues.verification
                ? generateWorkflowStep(
                      formValues.verification,
                      actionSpace,
                      false,
                      templateRequest.approvalWorkflow?.node.externalId
                  )
                : undefined

            const itemHistory = generateUpsertStatusHistoryInstance(
                actionExternalId,
                AllActionStatusExternalIdEnum.Active,
                JSON.stringify({
                    actionUser: 'createdBy',
                    comments: '',
                }),
                GetSpace(EntityType.Instance, siteId.slice(4)),
                activeUser.email
            )
            let counter = 1

            const links = linkList?.map((x) => {
                const currentCounterValue = counter++

                return {
                    externalId: generateNewExternalId('ACTL', currentCounterValue),
                    space: actionSpace,
                    link: x.link,
                    description: x.description,
                    action: { node: { externalId: actionExternalId, space: actionSpace } },
                }
            })

            await updateAction({ variables: { actionItems: templateRequest } })
            await updateStatusHistory({ variables: { statusHistoryInstances: itemHistory } })
            if (links && links.length > 0) {
                await updateLink({ variables: { actionItemLinks: links } })
            }
            if (formValues.approvalRequired) {
                await updateWorkflowStep({ variables: { workflowSteps: templateApproverWorkflowStep } })
            }
            if (formValues.verificationRequired) {
                await updateWorkflowStep({ variables: { workflowSteps: templateVerifierWorkflowStep } })
            }
            handleAlert(translate('alerts.templateSavedWithSuccess'))
        } catch (error) {
            handleAlert(translate('alerts.unexpectedErrorOcurred'), true)
        } finally {
            showLoading(false)
        }
    }

    return (
        <>
            <ClnButton
                variant="text"
                onClick={() => setSaveTemplateModalOpen(true)}
                label={translate('stepper.saveTemplate')}
                disabled={disabled}
                formsValue-test="new_action_item_flow_3-save_as_template_button"
                formsValue-origin="ui-lib"
            />
            {saveTemplateModalOpen && (
                <TemplateModal
                    siteId={siteId}
                    open={saveTemplateModalOpen}
                    actionsTemplateName={actionsTemplateName}
                    loadingTemplate={loadingTemplate}
                    handleClose={() => setSaveTemplateModalOpen(false)}
                    createFunction={(template: TemplateConfigForm) => createTemplate(template)}
                />
            )}
        </>
    )
}
