import { Category, ReportingLine } from '@/app/common/models/action'
import { ClnButton } from '@celanese/ui-lib'
import { zodResolver } from '@hookform/resolvers/zod'
import { Box, Grid, Typography, styled, useMediaQuery, useTheme } from '@mui/material'
import dayjs, { Dayjs } from 'dayjs'
import { useCallback, useEffect, useMemo, useState } from 'react'
import { SubmitHandler, useForm } from 'react-hook-form'
import { z } from 'zod'
import { SiteSpecificCategoryRequest } from '@/app/common/models/site-specific-category'
import { SourceEventStatus, SourceEventType } from '@/app/common/models/source-event'
import GenericDateRangePicker from '../../FieldsComponent/GenericDateRangePicker'
import GenericAutocomplete, { AutocompleteOption } from '../../FieldsComponent/GenericAutocomplete'
import GenericTextField from '../../FieldsComponent/GenericTextField'
import { transformOptions, transformOptionsForUser } from '@/app/common/utils/transform-options-for-filter'
import { useUsersSearch } from '@/app/common/hooks/user-management/useUsers'
import { SourceEventTypeExternalIdEnum } from '@/app/common/enums/SourceEventTypeEnum'
import { SourceEventStatusEnum } from '@/app/common/enums/SourceEventStatusEnum'
import { translate } from '@/app/common/utils/generate-translate'
import { transformDataToFormInput } from '@/app/common/utils/transform-input'
import GenericFieldTitle from '../../FieldsComponent/GenericFieldTitle'
import { UserRolesPermission } from '@celanese/celanese-sdk'
import { FunctionalLocationRequest } from '@/app/common/models/common'
import { EquipmentRequest } from '@/app/common/models/common/asset-hierarchy/equipment'
import { ReportingUnitRequest } from '@/app/common/models/common/asset-hierarchy/reporting-unit'
import { useAsyncAutocomplete } from '@/app/common/hooks/general-functions/useAsyncAutocomplete'
import { ActionItemSubCategory } from '@/app/common/models/sub-category'

interface BaseFieldConfig {
    id: string
    fieldType: 'autocomplete' | 'dateRange' | 'text'
    label: string
    name: string
    size?: 'small' | 'medium'
    dataTest?: string
    dataOrigin?: string
    preText?: string
    disabled?: boolean
    error?: boolean
}

interface AutocompleteFieldConfig extends BaseFieldConfig {
    fieldType: 'autocomplete' | 'text'
    options: any[]
    valueController?: any
    loading?: boolean
    placeholder?: string
    onChange: (newValue: any, fieldOption?: any) => void
    onInputChange?: (event: React.ChangeEvent<any>, newInputValue: string) => void
}

interface DateRangeFieldConfig extends BaseFieldConfig {
    fieldType: 'dateRange'
}

type FieldConfig = BaseFieldConfig | AutocompleteFieldConfig | DateRangeFieldConfig

type FormSchema = z.infer<typeof formSchema>

const zodDate = z.instanceof(dayjs as unknown as typeof Dayjs).nullable()

const formSchema = z.object({
    externalIdPrefix: z.string().optional(),
    titlePrefix: z.string().optional(),
    dueDate: z.tuple([zodDate, zodDate]),
    statusExternalIds: z.array(z.string()).optional(),
    categoryExternalIds: z.array(z.string()).optional(),
    subcategoryExternalIds: z.array(z.string()).optional(),
    siteSpecificCategoryExternalIds: z.array(z.string()).optional(),
    ownerExternalId: z.array(z.string()).optional(),
    reportingSiteExternalIds: z.array(z.string()).optional(),
    reportingUnitExternalIds: z.array(z.string()).optional(),
    impactedReportingUnitExternalIds: z.array(z.string()).optional(),
    reportingLineExternalIds: z.array(z.string()).optional(),
    eventTypeExternalIds: z.array(z.string()).optional(),
    functionalLocationExternalIds: z.array(z.string()).optional(),
    equipmentExternalIds: z.array(z.string()).optional(),
})

export interface FilterInfoEventProps {
    reportingSiteExternalId: string | string[]
    activeUserEmail?: string
    activeUser?: string
    activeUserRolesIds?: string[]
    activeUserTeamsIds?: string[]
    externalIdPrefix?: string
    titlePrefix?: string
    dueDateGte?: Dayjs | string | null
    dueDateLt?: Dayjs | string | null
    statusExternalIds?: string[]
    categoryExternalIds?: string[]
    subcategoryExternalIds?: string[]
    siteSpecificCategoryExternalIds?: string[]
    ownerExternalId?: string[]
    reportingSiteExternalIds?: string[]
    reportingUnitExternalIds?: string[]
    impactedReportingUnitExternalIds?: string[]
    reportingLineExternalIds?: string[]
    eventTypeExternalIds?: string[]
    functionalLocationExternalIds?: string[]
    equipmentExternalIds?: string[]
    search?: string
    pageSize: number
    sortBy?: string
    direction?: string
    cursor?: string
}

interface SourceEventFilterProps {
    onClose?: () => void
    onFilter: (dataInfo: FilterInfoEventProps) => void
    activeUser: UserRolesPermission
    reportingLine: ReportingLine[]
    status: SourceEventStatus[]
    categories: Category[]
    subCategories: ActionItemSubCategory[]
    sourceEventType: SourceEventType[]
    defaultFilter: FilterInfoEventProps
    defaultOwnerNames?: AutocompleteOption[]
    defaultSiteSpecificCategories?: AutocompleteOption[]
    defaultFunctionalLocations?: AutocompleteOption[]
    defaultEquipments?: AutocompleteOption[]
    defaultReportingUnits?: AutocompleteOption[]
    defaultImpactedReportingUnits?: AutocompleteOption[]
    siteId?: string
    setDefaultOwnerNames: (value: AutocompleteOption[]) => void
    setDefaultSiteSpecificCategories: (value: AutocompleteOption[]) => void
    setDefaultFunctionalLocations: (value: AutocompleteOption[]) => void
    setDefaultEquipments: (value: AutocompleteOption[]) => void
    setDefaultReportingUnits: (value: AutocompleteOption[]) => void
    setDefaultImpactedReportingUnits: (value: AutocompleteOption[]) => void
}

const Form = styled('form')({
    padding: '1rem',
})

export function EventFilter({
    onClose,
    onFilter,
    activeUser,
    reportingLine,
    status,
    categories,
    subCategories,
    sourceEventType,
    defaultFilter,
    defaultOwnerNames,
    defaultSiteSpecificCategories,
    defaultFunctionalLocations,
    defaultEquipments,
    defaultReportingUnits,
    defaultImpactedReportingUnits,
    siteId,
    setDefaultOwnerNames,
    setDefaultSiteSpecificCategories,
    setDefaultFunctionalLocations,
    setDefaultEquipments,
    setDefaultReportingUnits,
    setDefaultImpactedReportingUnits,
}: SourceEventFilterProps) {
    const theme = useTheme()
    const isTablet = useMediaQuery(theme.breakpoints.down('md'))
    const isMobile = useMediaQuery(theme.breakpoints.down('sm'))

    const selectedReportingSites = useMemo(
        () => activeUser.selectedSites?.map((site) => site.siteId) ?? [],
        [activeUser.selectedSites]
    )

    const activeUserOption: AutocompleteOption = useMemo(
        () => ({
            value: activeUser.externalId ?? '',
            label: `${activeUser.lastName}, ${activeUser.firstName}`,
        }),
        [activeUser]
    )

    const {
        control,
        setValue,
        handleSubmit,
        getValues,
        reset,
        formState: { errors },
    } = useForm<FormSchema>({
        resolver: zodResolver(formSchema),
        defaultValues: {
            externalIdPrefix: '',
            titlePrefix: '',
            dueDate: [null, null],
            statusExternalIds: [],
            categoryExternalIds: [],
            subcategoryExternalIds: [],
            siteSpecificCategoryExternalIds: [],
            ownerExternalId: [],
            reportingSiteExternalIds: siteId ? [siteId] : [],
            reportingUnitExternalIds: [],
            reportingLineExternalIds: [],
            eventTypeExternalIds: [],
            functionalLocationExternalIds: [],
            equipmentExternalIds: [],
        },
    })

    const reportingSiteOptions =
        activeUser.selectedSites?.map((site) => ({
            value: site.siteId,
            label: site.siteName,
        })) ?? []
    const categoryOptions = transformOptions(categories, 'name', 'name', 'category')
    const subcategory1Options = transformOptions(subCategories, 'name', 'name', 'subCategory1')
    const statusOptions = transformOptions(status, 'name', 'name', 'status.fullName')
    const reportingLineOptions = transformOptions(reportingLine)
    const sourceEventTypeOptions = transformOptions(sourceEventType, 'sourceEventTypeName')

    const [ownerNames, setOwnerNames] = useState<AutocompleteOption[]>(
        defaultFilter.ownerExternalId ? defaultOwnerNames ?? [] : []
    )

    const [usersParam, setUsersParam] = useState<string>('NULL_PARAM')
    const { users: filteredUsers, loading } = useUsersSearch(
        useMemo(() => {
            return usersParam
        }, [usersParam])
    )

    const sortedFilteredUsers = transformOptionsForUser([...filteredUsers])
    const sortedOwnerOptions = Array.from(
        new Map([...sortedFilteredUsers, ...ownerNames].map((item) => [item.value, item])).values()
    ).sort((a, b) => a.label.localeCompare(b.label))

    const buildSiteSpecificCategoryRequest = useCallback(
        (search: string): SiteSpecificCategoryRequest => ({
            search,
            reportingSiteExternalIds: selectedReportingSites,
        }),
        [selectedReportingSites]
    )
    const buildFunctionalLocationRequest = useCallback(
        (search: string): FunctionalLocationRequest => ({
            search,
            reportingSiteExternalIds: selectedReportingSites,
        }),
        [selectedReportingSites]
    )
    const buildEquipmentRequest = useCallback(
        (search: string): EquipmentRequest => ({
            search,
            reportingSiteExternalIds: selectedReportingSites,
        }),
        [selectedReportingSites]
    )
    const buildReportingUnitRequest = useCallback(
        (search: string): ReportingUnitRequest => ({
            search,
            reportingSiteExternalIds: selectedReportingSites,
        }),
        [selectedReportingSites]
    )
    const buildImpactedReportingUnitRequest = useCallback(
        (search: string): ReportingUnitRequest => ({
            search,
            reportingSiteExternalIds: selectedReportingSites,
        }),
        [selectedReportingSites]
    )

    const {
        selectedOptions: selectedSiteSpecificCategoryOptions,
        setSelectedOptions: setSelectedSiteSpecificCategoryOptions,
        setSearch: setSiteSpecificCategorySearch,
        options: siteSpecificCategoryOptions,
        loading: isLoadingSiteSpecificCategories,
    } = useAsyncAutocomplete(
        defaultFilter.siteSpecificCategoryExternalIds ? defaultSiteSpecificCategories ?? [] : [],
        (client, req: SiteSpecificCategoryRequest) => client.getSiteSpecificCategories(req),
        buildSiteSpecificCategoryRequest,
        (req) => req.search === 'NULL_PARAM',
        'name',
        500,
        reportingSiteOptions.length == 1
    )

    const {
        selectedOptions: selectedFunctionalLocationOptions,
        setSelectedOptions: setSelectedFunctionalLocationOptions,
        setSearch: setFunctionalLocationSearch,
        options: functionalLocationOptions,
        loading: isLoadingFunctionalLocations,
    } = useAsyncAutocomplete(
        defaultFilter.functionalLocationExternalIds ? defaultFunctionalLocations ?? [] : [],
        (client, req: FunctionalLocationRequest) => client.getFunctionalLocations(req),
        buildFunctionalLocationRequest,
        (req) => req.search === 'NULL_PARAM',
        'name'
    )

    const {
        selectedOptions: selectedEquipmentOptions,
        setSelectedOptions: setSelectedEquipmentOptions,
        setSearch: setEquipmentSearch,
        options: equipmentOptions,
        loading: isLoadingEquipments,
    } = useAsyncAutocomplete(
        defaultFilter.equipmentExternalIds ? defaultEquipments ?? [] : [],
        (client, req: EquipmentRequest) => client.getEquipments(req),
        buildEquipmentRequest,
        (req) => req.search === 'NULL_PARAM',
        'label'
    )

    const {
        selectedOptions: selectedReportingUnitOptions,
        setSelectedOptions: setSelectedReportingUnitOptions,
        setSearch: setReportingUnitSearch,
        options: reportingUnitOptions,
        loading: isLoadingReportingUnits,
    } = useAsyncAutocomplete(
        defaultFilter.reportingUnitExternalIds ? defaultReportingUnits ?? [] : [],
        (client, req: ReportingUnitRequest) => client.getReportingUnits(req),
        buildReportingUnitRequest,
        (req) => req.search === 'NULL_PARAM',
        'description',
        500,
        reportingSiteOptions.length == 1
    )

    const {
        selectedOptions: selectedImpactedReportingUnitOptions,
        setSelectedOptions: setSelectedImpactedReportingUnitOptions,
        setSearch: setImpactedReportingUnitSearch,
        options: impactedReportingUnitOptions,
        loading: isLoadingImpactedReportingUnits,
    } = useAsyncAutocomplete(
        defaultFilter.impactedReportingUnitExternalIds ? defaultImpactedReportingUnits ?? [] : [],
        (client, req: ReportingUnitRequest) => client.getReportingUnits(req),
        buildImpactedReportingUnitRequest,
        (req) => req.search === 'NULL_PARAM',
        'description',
        500,
        reportingSiteOptions.length == 1
    )

    const formSubmit: SubmitHandler<FormSchema> = (data) => {
        setDefaultOwnerNames(ownerNames)
        setDefaultSiteSpecificCategories(selectedSiteSpecificCategoryOptions)
        setDefaultFunctionalLocations(selectedFunctionalLocationOptions)
        setDefaultEquipments(selectedEquipmentOptions)
        setDefaultReportingUnits(selectedReportingUnitOptions)
        setDefaultImpactedReportingUnits(selectedImpactedReportingUnitOptions)

        const transformedData: FilterInfoEventProps = {
            ...defaultFilter,
            cursor: undefined,
            externalIdPrefix: transformDataToFormInput(data.externalIdPrefix),
            titlePrefix: transformDataToFormInput(data.titlePrefix),
            eventTypeExternalIds: transformDataToFormInput(data.eventTypeExternalIds),
            statusExternalIds: transformDataToFormInput(data.statusExternalIds),
            categoryExternalIds: transformDataToFormInput(data.categoryExternalIds),
            subcategoryExternalIds: transformDataToFormInput(data.subcategoryExternalIds),
            siteSpecificCategoryExternalIds: transformDataToFormInput(data.siteSpecificCategoryExternalIds),
            ownerExternalId: transformDataToFormInput(data.ownerExternalId),
            reportingSiteExternalIds: transformDataToFormInput(data.reportingSiteExternalIds),
            reportingUnitExternalIds: transformDataToFormInput(data.reportingUnitExternalIds),
            reportingLineExternalIds: transformDataToFormInput(data.reportingLineExternalIds),
            impactedReportingUnitExternalIds: transformDataToFormInput(data.impactedReportingUnitExternalIds),
            dueDateGte: data.dueDate[0] ? dayjs(data.dueDate[0]).format('YYYY-MM-DD') : undefined,
            dueDateLt: data.dueDate[1] ? dayjs(data.dueDate[1]).format('YYYY-MM-DD') : undefined,
            equipmentExternalIds: transformDataToFormInput(data.equipmentExternalIds),
            functionalLocationExternalIds: transformDataToFormInput(data.functionalLocationExternalIds),
        }

        onFilter({ ...transformedData })
        onClose && onClose()
    }

    const clearFunction = () => {
        reset({
            externalIdPrefix: '',
            titlePrefix: '',
            dueDate: [null, null],
            statusExternalIds: [],
            categoryExternalIds: [],
            subcategoryExternalIds: [],
            siteSpecificCategoryExternalIds: [],
            ownerExternalId: [],
            reportingSiteExternalIds: siteId ? [siteId] : [],
            reportingUnitExternalIds: [],
            impactedReportingUnitExternalIds: [],
            reportingLineExternalIds: [],
            eventTypeExternalIds: [],
            functionalLocationExternalIds: [],
            equipmentExternalIds: [],
        })
        setOwnerNames([])
        setSelectedSiteSpecificCategoryOptions([])
        setSelectedFunctionalLocationOptions([])
        setSelectedEquipmentOptions([])
        setSelectedReportingUnitOptions([])
        setSelectedImpactedReportingUnitOptions([])
    }

    const defaultFunction = () => {
        reset({
            externalIdPrefix: '',
            titlePrefix: '',
            dueDate: [null, null],
            statusExternalIds: Object.values(SourceEventStatusEnum),
            categoryExternalIds: [],
            subcategoryExternalIds: [],
            siteSpecificCategoryExternalIds: [],
            ownerExternalId: activeUser.externalId ? [activeUser.externalId] : [],
            reportingSiteExternalIds: siteId ? [siteId] : [],
            reportingUnitExternalIds: [],
            impactedReportingUnitExternalIds: [],
            reportingLineExternalIds: [],
            eventTypeExternalIds: Object.values(SourceEventTypeExternalIdEnum),
            functionalLocationExternalIds: [],
            equipmentExternalIds: [],
        })

        setOwnerNames([activeUserOption])
        setSelectedSiteSpecificCategoryOptions([])
        setSelectedFunctionalLocationOptions([])
        setSelectedEquipmentOptions([])
        setSelectedReportingUnitOptions([])
        setSelectedImpactedReportingUnitOptions([])
    }

    const renderField = useCallback(
        (config: FieldConfig, control: any) => {
            switch (config.fieldType) {
                case 'text':
                    return (
                        <>
                            {config.preText && <Typography>{config.preText}</Typography>}
                            <GenericTextField
                                name={config.name}
                                control={control}
                                error={config.error}
                                valueController={getValues(config.name as any) as string}
                                isSearchIcon
                                data-test={config.dataTest}
                                data-origin={config.dataOrigin}
                            />
                        </>
                    )
                case 'autocomplete':
                    const acConfig = config as AutocompleteFieldConfig
                    return (
                        <GenericAutocomplete
                            name={acConfig.name}
                            control={control}
                            options={acConfig.options}
                            valueController={acConfig.valueController}
                            size={acConfig.size}
                            onChange={acConfig.onChange}
                            loading={acConfig.loading}
                            placeholderTextField={acConfig.placeholder}
                            onInputChange={acConfig.onInputChange}
                            disabled={acConfig.disabled}
                            data-test={acConfig.dataTest}
                            data-origin={acConfig.dataOrigin}
                        />
                    )
                case 'dateRange':
                    return (
                        <GenericDateRangePicker
                            name={config.name}
                            control={control}
                            size={config.size}
                            data-test={config.dataTest}
                            data-origin={config.dataOrigin}
                        />
                    )
                default:
                    return null
            }
        },
        [getValues]
    )

    const fields = useMemo(() => {
        const baseFields: FieldConfig[] = [
            {
                id: 'dueDate',
                fieldType: 'dateRange',
                name: 'dueDate',
                label: translate('table.headers.dueDate'),
                size: 'small',
                dataTest: 'new_action_item_create_new_event_no_filters_modal-due_date_field',
                dataOrigin: 'aim',
            },
            {
                id: 'externalIdPrefix',
                fieldType: 'text',
                name: 'externalIdPrefix',
                label: translate('table.headers.id'),
                error: Boolean(errors.externalIdPrefix),
                preText: translate('table.headers.startWith'),
                dataTest: 'new_action_item_create_new_event_no_filters_modal-id_start_with_field',
                dataOrigin: 'aim',
            },
            {
                id: 'titlePrefix',
                fieldType: 'text',
                name: 'titlePrefix',
                label: translate('table.headers.title'),
                error: Boolean(errors.titlePrefix),
                preText: translate('table.headers.startWith'),
                valueController: getValues('titlePrefix'),
                dataTest: 'new_action_item_create_new_event_no_filters_modal-title_start_with_field',
                dataOrigin: 'aim',
            },
            {
                id: 'ownerExternalId',
                fieldType: 'autocomplete',
                label: translate('table.headers.primaryOwner'),
                name: 'ownerExternalId',
                options: sortedOwnerOptions,
                valueController: ownerNames,
                size: 'small',
                onChange: (newValue, fieldOption) => {
                    setValue('ownerExternalId', newValue)
                    setOwnerNames(fieldOption ?? [])
                },
                loading,
                placeholder: translate('stepper.form.typeAtLeast3'),
                onInputChange: (_, newInputValue) => {
                    if (newInputValue.length >= 3) {
                        setUsersParam(newInputValue)
                    }
                },
                dataTest: 'new_action_item_create_new_event_no_filters_modal-primary_owner_field',
                dataOrigin: 'aim',
            },
            {
                id: 'reportingUnitExternalIds',
                fieldType: 'autocomplete',
                name: 'reportingUnitExternalIds',
                valueController: selectedReportingUnitOptions,
                options: reportingUnitOptions,
                onChange: (newValue, fieldOption) => {
                    setValue('reportingUnitExternalIds', newValue)
                    setSelectedReportingUnitOptions(fieldOption ?? [])
                    setReportingUnitSearch(reportingSiteOptions.length == 1 ? '' : 'NULL_PARAM')
                },
                label: translate('table.headers.unit'),
                placeholder: translate('stepper.form.typeAtLeast3'),
                onInputChange: (_, newInputValue) => {
                    if (newInputValue.length >= 3) {
                        setReportingUnitSearch(newInputValue)
                    }
                },
                size: 'small',
                loading: isLoadingReportingUnits,
                dataTest: 'new_action_item_create_new_event_no_filters_modal-reporting_unit_field',
                dataOrigin: 'aim',
            },
            {
                id: 'impactedReportingUnitExternalIds',
                fieldType: 'autocomplete',
                name: 'impactedReportingUnitExternalIds',
                valueController: selectedImpactedReportingUnitOptions,
                options: impactedReportingUnitOptions,
                onChange: (newValue, fieldOption) => {
                    setValue('impactedReportingUnitExternalIds', newValue)
                    setSelectedImpactedReportingUnitOptions(fieldOption ?? [])
                    setImpactedReportingUnitSearch(reportingSiteOptions.length == 1 ? '' : 'NULL_PARAM')
                },
                label: translate('table.headers.impactedUnit'),
                placeholder: translate('stepper.form.typeAtLeast3'),
                onInputChange: (_, newInputValue) => {
                    if (newInputValue.length >= 3) {
                        setImpactedReportingUnitSearch(newInputValue)
                    }
                },
                size: 'small',
                loading: isLoadingImpactedReportingUnits,
                dataTest: 'new_action_item_create_new_event_no_filters_modal-impacted_units_field',
                dataOrigin: 'aim',
            },
            {
                id: 'reportingLineExternalIds',
                fieldType: 'autocomplete',
                name: 'reportingLineExternalIds',
                options: reportingLineOptions,
                label: translate('stepper.form.reportingLine'),
                size: 'small',
                onChange: (newValue) => setValue('reportingLineExternalIds', newValue),
                dataTest: 'new_action_item_create_new_event_no_filters_modal-reporting_line_field',
                dataOrigin: 'aim',
            },
            {
                id: 'statusExternalIds',
                fieldType: 'autocomplete',
                name: 'statusExternalIds',
                options: statusOptions,
                onChange: (newValue) => setValue('statusExternalIds', newValue),
                label: translate('table.headers.status'),
                size: 'small',
            },
            {
                id: 'eventTypeExternalIds',
                fieldType: 'autocomplete',
                name: 'eventTypeExternalIds',
                options: sourceEventTypeOptions,
                onChange: (newValue) => setValue('eventTypeExternalIds', newValue),
                label: translate('table.headers.eventType'),
                size: 'small',
                dataTest: 'new_action_item_create_new_event_no_filters_modal-event_type_field',
                dataOrigin: 'aim',
            },
            {
                id: 'categoryExternalIds',
                fieldType: 'autocomplete',
                name: 'categoryExternalIds',
                options: categoryOptions,
                onChange: (newValue) => setValue('categoryExternalIds', newValue),
                label: translate('table.headers.category'),
                size: 'small',
                dataTest: 'new_action_item_create_new_event_no_filters_modal-category_field',
                dataOrigin: 'aim',
            },
            {
                id: 'subcategoryExternalIds',
                fieldType: 'autocomplete',
                name: 'subcategoryExternalIds',
                options: subcategory1Options,
                onChange: (newValue) => setValue('subcategoryExternalIds', newValue),
                label: translate('table.headers.subcategoryOne'),
                size: 'small',
                dataTest: 'new_action_item_create_new_event_no_filters_modal-subcategory_1_field',
                dataOrigin: 'aim',
            },
            {
                id: 'siteSpecificCategoryExternalIds',
                fieldType: 'autocomplete',
                name: 'siteSpecificCategoryExternalIds',
                valueController: selectedSiteSpecificCategoryOptions,
                options: siteSpecificCategoryOptions,
                onChange: (newValue, fieldOption) => {
                    setValue('siteSpecificCategoryExternalIds', newValue)
                    setSelectedSiteSpecificCategoryOptions(fieldOption ?? [])
                    setSiteSpecificCategorySearch(reportingSiteOptions.length == 1 ? '' : 'NULL_PARAM')
                },
                label: translate('table.headers.subcategoryTwo'),
                placeholder: translate('stepper.form.typeAtLeast3'),
                onInputChange: (_, newInputValue) => {
                    if (newInputValue.length >= 3) {
                        setSiteSpecificCategorySearch(newInputValue)
                    }
                },
                size: 'small',
                loading: isLoadingSiteSpecificCategories,
                dataTest: 'new_action_item_create_new_event_no_filters_modal-subcategory_2_field',
                dataOrigin: 'aim',
            },
            {
                id: 'functionalLocationIdEq',
                fieldType: 'autocomplete',
                name: 'functionalLocationIdEq',
                valueController: selectedFunctionalLocationOptions,
                options: functionalLocationOptions,
                onChange: (newValue, fieldOption) => {
                    setValue('functionalLocationExternalIds', newValue)
                    setSelectedFunctionalLocationOptions(fieldOption ?? [])
                    setFunctionalLocationSearch('NULL_PARAM')
                },
                label: translate('table.headers.functionalLocation'),
                placeholder: translate('stepper.form.typeAtLeast3'),
                onInputChange: (_, newInputValue) => {
                    if (newInputValue.length >= 3) {
                        setFunctionalLocationSearch(newInputValue)
                    }
                },
                size: 'small',
                loading: isLoadingFunctionalLocations,
                dataTest: 'new_action_item_create_new_event_no_filters_modal-functional-location_field',
                dataOrigin: 'aim',
            },
            {
                id: 'equipmentIdEq',
                fieldType: 'autocomplete',
                name: 'equipmentIdEq',
                valueController: selectedEquipmentOptions,
                options: equipmentOptions,
                onChange: (newValue, fieldOption) => {
                    setValue('equipmentExternalIds', newValue)
                    setSelectedEquipmentOptions(fieldOption ?? [])
                    setEquipmentSearch('NULL_PARAM')
                },
                label: translate('table.headers.equipment'),
                placeholder: translate('stepper.form.typeAtLeast3'),
                onInputChange: (_, newInputValue) => {
                    if (newInputValue.length >= 3) {
                        setEquipmentSearch(newInputValue)
                    }
                },
                size: 'small',
                loading: isLoadingEquipments,
                dataTest: 'new_action_item_create_new_event_no_filters_modal-equipment_field',
                dataOrigin: 'aim',
            },
        ]

        if (activeUser.applications?.[0]?.userSites?.length > 1) {
            const siteField: FieldConfig = {
                id: 'reportingSiteExternalIds',
                fieldType: 'autocomplete',
                label: translate('table.headers.site'),
                name: 'reportingSiteExternalIds',
                options: reportingSiteOptions,
                size: 'small',
                onChange: (newValue) => setValue('reportingSiteExternalIds', newValue),
                disabled: !!siteId,
                dataTest: 'new_action_item_create_new_event_no_filters_modal--site_field',
                dataOrigin: 'aim',
            }

            const unitIndex = baseFields.findIndex((f) => f.id === 'reportingUnitExternalIds')

            if (unitIndex !== -1) {
                baseFields.splice(unitIndex, 0, siteField)
            } else {
                baseFields.push(siteField)
            }
        }

        return baseFields
    }, [
        errors,
        sortedOwnerOptions,
        ownerNames,
        loading,
        siteSpecificCategoryOptions,
        selectedSiteSpecificCategoryOptions,
        isLoadingSiteSpecificCategories,
        selectedReportingUnitOptions,
        reportingUnitOptions,
        isLoadingReportingUnits,
        selectedImpactedReportingUnitOptions,
        impactedReportingUnitOptions,
        isLoadingImpactedReportingUnits,
        reportingLineOptions,
        statusOptions,
        sourceEventTypeOptions,
        categoryOptions,
        subcategory1Options,
        selectedFunctionalLocationOptions,
        functionalLocationOptions,
        isLoadingFunctionalLocations,
        selectedEquipmentOptions,
        equipmentOptions,
        isLoadingEquipments,
        reportingSiteOptions,
    ])

    const renderForm = () => (
        <Form onSubmit={handleSubmit(formSubmit)} id={'filter-events-home'}>
            <Grid
                container
                sx={{
                    height: '100%',
                    width: '100%',
                    flexDirection: 'column',
                    gap: 2,
                }}
            >
                <Grid item md={12} sm={12} id={'button-mobile-filter-events-home'}>
                    <Box sx={{ display: 'flex', gap: '8px', justifyContent: 'flex-end' }}>
                        <ClnButton
                            size="medium"
                            variant="outlined"
                            label={translate('table.filter.clear')}
                            onClick={clearFunction}
                            data-test="new_action_item_create_new_event_no_filters_modal-clear_button"
                            data-origin="ui-lib"
                        />
                        <ClnButton
                            size="medium"
                            variant="outlined"
                            label={translate('table.filter.default')}
                            onClick={defaultFunction}
                            data-test="new_action_item_create_new_event_no_filters_modal-default_button"
                            data-origin="ui-lib"
                        />
                    </Box>
                </Grid>

                <Grid
                    item
                    sx={{
                        padding: '1rem 0',
                        flexGrow: 1,
                        overflowY: 'auto',
                        width: isMobile ? '100%' : '350px',
                        height: '250px',
                        order: isMobile ? 1 : 2,
                    }}
                    id="options-filter-action-home"
                >
                    <Grid
                        container
                        sx={{
                            width: '100%',
                            flexDirection: 'column',
                            gap: 1,
                        }}
                    >
                        {fields.map((fieldConfig) => (
                            <Grid
                                key={fieldConfig.id}
                                item
                                md={12}
                                xs={12}
                                sx={{
                                    paddingRight: isTablet ? 0 : 2,
                                    paddingLeft: 0,

                                    width: '100%',
                                }}
                            >
                                <GenericFieldTitle isBorder fieldName={fieldConfig.label} />
                                {renderField(fieldConfig, control)}
                            </Grid>
                        ))}
                    </Grid>
                </Grid>

                <Grid item id={'button-mobile-apply-filter-events-home'} sx={{ order: 3 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'center', width: '100%' }}>
                        <ClnButton
                            type="submit"
                            size="medium"
                            variant="contained"
                            label={translate('table.filter.applyFilter')}
                            data-test="new_action_item_create_new_event_no_filters_modal-apply_filter_button"
                            data-origin="ui-lib"
                        />
                    </Box>
                </Grid>
            </Grid>
        </Form>
    )

    useEffect(() => {
        reset({
            ...defaultFilter,
            dueDate: [
                defaultFilter.dueDateGte ? dayjs(defaultFilter.dueDateGte) : null,
                defaultFilter.dueDateLt ? dayjs(defaultFilter.dueDateLt) : null,
            ],
        })
    }, [defaultFilter])

    return <Box>{renderForm()}</Box>
}
