import { TranslationContext, TranslationContextState } from '@celanese/celanese-ui'
import { Box } from '@mui/material'
import { useCallback, useContext, useEffect, useMemo, useState } from 'react'
import { AzureFunctionClient } from '@/app/common/clients/azure-function-client'
import { ActionStatusExternalIdClearEnum } from '@/app/common/enums/ActionItemStatusEnum'
import { KPICard } from '../KPICard'
import LoaderCircular from '../../Loader'
import { useDebounceFunction } from '@/app/common/hooks/general-functions/useDebounce'
import { FilterInfoProps } from '../../ActionTable/HomeFilter'
import { SiteTabKpisResponseData } from '@/app/common/models/kpis_response'
import { translate } from '@/app/common/utils/generate-translate'
import { getLocalUserSite } from '@celanese/celanese-ui'
import { UserRolesPermission } from '@celanese/celanese-sdk'

type SiteKpisProps = {
    filterInfo: FilterInfoProps
    client: AzureFunctionClient
    activeUser?: UserRolesPermission
    handleError: (err: any) => void
}

export const stylesSite = {
    kpiWrapper: {
        gap: '1rem',
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(180px, 1fr))',
        '@media (max-width:800px)': {
            gridTemplateColumns: 'repeat(1, 1fr)',
        },
    },
}

export function SiteKpis({ filterInfo, client, activeUser, handleError }: SiteKpisProps) {
    const { siteId: site } = getLocalUserSite() || {}
    const { locale } = useContext<TranslationContextState>(TranslationContext)

    const [kpisValues, setKpisValues] = useState<SiteTabKpisResponseData>()

    const [loadingKpisFilter, setLoadingKpisFilter] = useState<boolean>(false)

    const kpisParams = useMemo(() => {
        return [
            {
                key: 'kpi-total-actions',
                label: translate('dashboards.kpis.totalEntered'),
                value: `${kpisValues?.totalEntered ?? 0}`,
            },
            {
                key: 'kpi-overdue-actions',
                label: translate('dashboards.kpis.overdue'),
                value: `${kpisValues?.overdue ?? 0}`,
            },
            {
                key: 'kpi-open-actions',
                label: translate('dashboards.kpis.open'),
                value: `${kpisValues?.open ?? 0}`,
            },
            {
                key: 'kpi-pending-actions',
                label: translate('dashboards.kpis.pending'),
                value: `${kpisValues?.pending ?? 0}`,
            },
            {
                key: 'kpi-overduePercentage-actions',
                label: translate('dashboards.kpis.overduePercentage'),
                value: `${kpisValues?.overduePercentage ?? 0} %`,
            },
        ]
    }, [translate, locale, kpisValues])

    const fetchKpi = useCallback(async () => {
        if (activeUser?.email === undefined) return undefined
        try {
            const response = await client.getSiteTabKpis({
                activeUserEmail: activeUser?.email,
                tab: 'Site',
                filters: {
                    reportingSiteExternalId: site,
                    dueDateGte: filterInfo?.dueDateGte ?? null,
                    dueDateLt: filterInfo?.dueDateLt ?? null,
                    reportingUnitExternalIds: filterInfo.reportingUnitExternalIds,
                    reportingLocationExternalIds: filterInfo.reportingLocationExternalIds,
                    categoryExternalIds: filterInfo.categoryExternalId,
                    subcategoryExternalIds: filterInfo.subcategoryExternalId,
                    siteSpecificCategoryExternalIds: filterInfo.siteSpecificCategoryExternalId,
                    statusExternalIds:
                        filterInfo && filterInfo?.statusExternalIds && filterInfo?.statusExternalIds?.length > 0
                            ? filterInfo?.statusExternalIds
                            : Object.values(ActionStatusExternalIdClearEnum),
                    onlyPrivate: filterInfo.onlyPrivate,
                    sourceEventTitleEq: filterInfo.sourceEventTitleEq ?? undefined,
                },
            })

            setKpisValues(response)
        } catch (err) {
            handleError(err)
        } finally {
            setLoadingKpisFilter(false)
        }
    }, [filterInfo])

    const debouncedFetchKpiSite = useCallback(useDebounceFunction(fetchKpi, 500), [fetchKpi])

    useEffect(() => {
        if (loadingKpisFilter) return

        setLoadingKpisFilter(true)
        debouncedFetchKpiSite()
    }, [fetchKpi])

    return (
        <Box sx={stylesSite.kpiWrapper} id={'kpis-site'}>
            {loadingKpisFilter ? (
                LoaderCircular()
            ) : (
                <>
                    {kpisParams.map((x) => (
                        <KPICard key={x.key} id={x.key} label={x.label} amount={x.value} />
                    ))}
                </>
            )}
        </Box>
    )
}

export default SiteKpis
