import { ClnButton } from '@celanese/ui-lib'
import { Box, Typography, useMediaQuery, useTheme } from '@mui/material'
import { ModalWrapper } from '../ModalComponent/Modal/ModalWrapper'
import { translate } from '@/app/common/utils/generate-translate'

type ConfirmationModalProps = {
    onClose: () => void
    isCancel: boolean
}

export default function ConfirmationModal({ onClose, isCancel }: ConfirmationModalProps) {
    const theme = useTheme()
    const isMobile = useMediaQuery(theme.breakpoints.down('sm'))

    const modalTitle = translate(
        isCancel ? 'bulkDeleteActionItems.cancelledActionsLabel' : 'bulkDeleteActionItems.deletedActionsLabel'
    )

    const modalMessage = translate(
        isCancel ? 'bulkDeleteActionItems.cancelSuccessMessage' : 'bulkDeleteActionItems.deleteSuccessMessage'
    )

    return (
        <ModalWrapper
            title={modalTitle}
            openModal
            closeModal={onClose}
            hideBackdrop={false}
            sxPropsTitle={{
                color: 'text.primary',
                fontSize: '22px',
                fontWeight: 400,
                marginRight: 'auto',
                wordBreak: 'break-word',
                whiteSpace: 'normal',
                maxWidth: 'calc(100% - 60px)',
            }}
            content={
                <Box
                    sx={{
                        display: 'flex',
                        flexDirection: 'column',
                        padding: '0px 24px 24px 24px',
                        width: isMobile ? '100vw' : '30vw',
                        minWidth: isMobile ? '100vw' : '400px',
                        overflowY: 'auto',
                    }}
                >
                    <Typography
                        sx={{
                            color: 'text.primary',
                            fontSize: '16px',
                            fontWeight: 400,
                        }}
                    >
                        {modalMessage}
                    </Typography>
                    <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2 }}>
                        <ClnButton variant="text" label={translate('requestModal.close')} onClick={onClose} />
                    </Box>
                </Box>
            }
        />
    )
}
