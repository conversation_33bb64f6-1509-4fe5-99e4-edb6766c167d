from typing import List

from pydantic import BaseModel, ConfigDict
from pydantic.alias_generators import to_camel


class UploadFileRequest(BaseModel):
    model_config = ConfigDict(
        alias_generator=to_camel,
        populate_by_name=True,
        from_attributes=True,
        arbitrary_types_allowed=True,
    )

    content: bytes
    name: str
    external_id: str | None = None
    source: str | None = None
    mime_type: str | None = None
    file_size: str | None = None
    user: str | None = None
    metadata: dict[str, str] | None = None
    data_set_id: int | None = None
    is_private: bool | None = False


class GetFilesDownloadUrlsRequest(BaseModel):
    model_config = ConfigDict(
        alias_generator=to_camel,
        populate_by_name=True,
        from_attributes=True,
        arbitrary_types_allowed=True,
    )

    external_ids: List[str]
