# Manifest file downloaded from fusion
externalId: tr-AIM-COR-ALL-ICAP-SEVT-VIEWER
name: AIM-COR-ALL-ICAP-SEVT-VIEWER
query: >-
  SELECT
    concat(
    	'SourceEvent.viewUsers-', 
    	aim_event.externalId, '-', 
    	utl_user.user_azure_attribute.externalId
    ) AS externalId,
    aim_event.space,
    node_reference(aim_event.space, aim_event.externalId) AS startNode,
    utl_user.user_azure_attribute AS endNode
  FROM (
    SELECT 
    	icap_event.key AS event_id, 
    	icap_event.EventAddedByOwner AS user_id
    FROM `ICAP-COR`.`EVNT-tblEvent` icap_event

    UNION 

    SELECT
    	cast(icap_event_secondary_owner.EventID AS STRING) AS event_id,
    	icap_event_secondary_owner.OwnerID AS user_id
    FROM `ICAP-COR`.`EVNT-tblEventSecondaryOwners` icap_event_secondary_owner

    UNION 

    SELECT
    	cast(icap_event_viewer.EventID AS STRING) AS event_id,
    	icap_event_viewer.ViewerID AS user_id
    FROM `ICAP-COR`.`EVNT-tblEventViewer` icap_event_viewer
  ) icap_viewer

  INNER JOIN cdf_data_models("AIM-COR-ALL-DMD", "ActionItemManagementDOM", "6_0_0", "SourceEvent") AS aim_event
    	ON aim_event.iCAPEventID = icap_viewer.event_id
  INNER JOIN `AIM-COR`.`ICAP-MAP-User` utl_user
  	ON utl_user.key = cast(icap_viewer.user_id AS STRING)
  WHERE aim_event.isPrivate
  	AND 1 = 0 -- freeze
destination:
  dataModel:
    space: AIM-COR-ALL-DMD
    externalId: ActionItemManagementDOM
    version: "6_0_0"
    destinationType: SourceEvent
    destinationRelationshipFromType: viewUsers
  type: instances
ignoreNullFields: true
shared: true
action: upsert
dataSetExternalId: AIM-COR-ALL-DAT
# Specify credentials separately like this:
# authentication:
#   read:
#     clientId: ${READ_CLIENT_ID}
#     clientSecret: ${READ_CLIENT_SECRET}
#     tokenUrl: ${READ_TOKEN_URL}
#     cdfProjectName: ${READ_CDF_PROJECT_NAME}
#     # Optional: If idP requires providing the scopes
#     scopes:
#       - ${READ_SCOPES}
#     # Optional: If idP requires providing the audience
#     audience: ${READ_CDF_AUDIENCE}
#   write:
#     clientId: ${WRITE_CLIENT_ID}
#     clientSecret: ${WRITE_CLIENT_SECRET}
#     tokenUrl: ${WRITE_TOKEN_URL}
#     cdfProjectName: ${WRITE_CDF_PROJECT_NAME}
#     # Optional: If idP requires providing the scopes
#     scopes:
#       - ${WRITE_SCOPES}
#     # Optional: If idP requires providing the audience
#     audience: ${WRITE_CDF_AUDIENCE}
# Or together like this:
# authentication:
#   clientId: ${CLIENT_ID}
#   clientSecret: ${CLIENT_SECRET}
#   tokenUrl: ${TOKEN_URL}
#   # Optional: If idP requires providing the scopes
#   cdfProjectName: ${CDF_PROJECT_NAME}
#   scopes:
#    - ${SCOPES}
#   # Optional: If idP requires providing the audience
#   audience: ${AUDIENCE}