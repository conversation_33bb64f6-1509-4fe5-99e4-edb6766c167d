from datetime import datetime

from pydantic import Field

from clients.core.models import Node

from .responses import QualityNotificationResponse


class _DescribableNode(Node):
    name: str | None = Field(default=None)
    description: str | None = Field(default=None)


class QualityNotification(Node):
    """Represents a Quality Notification from QualityDOM."""

    notification_number: str | None = Field(default=None)
    notification_description: str | None = Field(default=None)
    notification_date: datetime | None = Field(default=None)
    batch_number: str | None = Field(default=None)
    defect_desc: str | None = Field(default=None)
    severity: str | None = Field(default=None)
    material: _DescribableNode | None = Field(default=None)
    returned_quantity: float | None = Field(default=None)
    subject_code: str | None = Field(default=None)
    system_status: str | None = Field(default=None)

    def to_response(self) -> QualityNotificationResponse:
        """Cast the entity to its response type."""
        return QualityNotificationResponse(
            external_id=self.external_id,
            space=self.space,
            notification_number=self.notification_number,
            notification_description=self.notification_description,
            notification_date=(
                self.notification_date.date() if self.notification_date else None
            ),
            batch_number=self.batch_number,
            defect_desc=self.defect_desc,
            severity=self.severity,
            material=self.material.description if self.material is not None else None,
            returned_quantity=self.returned_quantity,
            subject_code=self.subject_code,
            system_status=self.system_status,
        )
