{"externalId": "wf-AIM-COR-ICAP-MIGRATION", "version": "5_1_0", "description": "Migrate iCAP Actions, Events and Files to Action Item Management", "schedules": [{"externalId": "wfs-AIM-COR-ICAP-MIGRATION-01", "cron": "0 */2 * * *", "data": null}], "tasks": [{"externalId": "task-transformation-127", "type": "transformation", "name": "Upsert actions in staging table", "parameters": {"transformation": {"externalId": "tr-AIM-COR-ALL-RAW-ICAP-STG-ACTION", "concurrencyPolicy": "fail", "useTransformationCredentials": false}}, "timeout": 10000, "onFailure": "abortWorkflow", "dependsOn": []}, {"externalId": "task-transformation-441", "type": "transformation", "name": "Upsert actions in ActionItemManagementDOM", "parameters": {"transformation": {"externalId": "tr-AIM-COR-ALL-ICAP-ACT", "concurrencyPolicy": "fail", "useTransformationCredentials": false}}, "timeout": 10000, "onFailure": "abortWorkflow", "dependsOn": [{"externalId": "task-transformation-127"}]}, {"externalId": "task-transformation-507", "type": "transformation", "name": "Upsert events in staging table", "parameters": {"transformation": {"externalId": "tr-AIM-COR-ALL-RAW-ICAP-STG-EVENT", "concurrencyPolicy": "fail", "useTransformationCredentials": false}}, "timeout": 10000, "onFailure": "abortWorkflow", "dependsOn": []}, {"externalId": "task-transformation-824", "type": "transformation", "name": "Upsert events in ActionItemManagementDOM", "parameters": {"transformation": {"externalId": "tr-AIM-COR-ALL-ICAP-SEVT", "concurrencyPolicy": "fail", "useTransformationCredentials": false}}, "timeout": 10000, "onFailure": "abortWorkflow", "dependsOn": [{"externalId": "task-transformation-507"}]}, {"externalId": "task-transformation-628", "type": "transformation", "name": "Link actions to events", "parameters": {"transformation": {"externalId": "tr-AIM-COR-ALL-ICAP-SEVT-ACT", "concurrencyPolicy": "fail", "useTransformationCredentials": false}}, "timeout": 10000, "onFailure": "abortWorkflow", "dependsOn": [{"externalId": "task-transformation-824"}, {"externalId": "task-transformation-441"}]}, {"externalId": "task-transformation-660", "type": "transformation", "name": "Fills sourceId field of actions", "parameters": {"transformation": {"externalId": "tr-AIM-COR-ALL-ICAP-ACT-SOURCEID", "concurrencyPolicy": "fail", "useTransformationCredentials": false}}, "timeout": 10000, "onFailure": "abortWorkflow", "dependsOn": [{"externalId": "task-transformation-441"}]}, {"externalId": "task-transformation-545", "type": "transformation", "name": "Link secondary owners to events", "parameters": {"transformation": {"externalId": "tr-AIM-COR-ALL-ICAP-SEVT-SECOWNR", "concurrencyPolicy": "fail", "useTransformationCredentials": false}}, "timeout": 10000, "onFailure": "abortWorkflow", "dependsOn": [{"externalId": "task-transformation-824"}]}, {"externalId": "task-function-220", "type": "function", "name": "Migrate files to AIM datasets", "parameters": {"function": {"externalId": "FUNC-AIM-COR-ALL-ICAP-FILE", "data": {}}}, "timeout": 10000, "onFailure": "abortWorkflow", "dependsOn": [{"externalId": "task-transformation-531"}]}, {"externalId": "task-transformation-960", "type": "transformation", "name": "Link files to actions", "parameters": {"transformation": {"externalId": "tr-AIM-COR-ALL-ICAP-ACT-FILE", "concurrencyPolicy": "fail", "useTransformationCredentials": false}}, "timeout": 10000, "onFailure": "abortWorkflow", "dependsOn": [{"externalId": "task-function-220"}, {"externalId": "task-transformation-441"}]}, {"externalId": "task-transformation-342", "type": "transformation", "name": "Link files to events", "parameters": {"transformation": {"externalId": "tr-AIM-COR-ALL-ICAP-SEVT-FILE", "concurrencyPolicy": "fail", "useTransformationCredentials": false}}, "timeout": 10000, "onFailure": "abortWorkflow", "dependsOn": [{"externalId": "task-transformation-824"}, {"externalId": "task-function-220"}]}, {"externalId": "task-transformation-531", "type": "transformation", "name": "Upsert files to migrate in staging table", "parameters": {"transformation": {"externalId": "tr-AIM-COR-ALL-ICAP-STG-FILE", "concurrencyPolicy": "fail", "useTransformationCredentials": false}}, "timeout": 10000, "onFailure": "abortWorkflow", "dependsOn": []}, {"externalId": "task-transformation-257", "type": "transformation", "name": "Upsert incorrect actions to delete in staging table", "parameters": {"transformation": {"externalId": "tr-AIM-COR-ALL-RAW-ICAP-STG-ACTION-DEL", "concurrencyPolicy": "fail", "useTransformationCredentials": false}}, "timeout": 10000, "onFailure": "abortWorkflow", "dependsOn": [{"externalId": "task-transformation-441"}]}, {"externalId": "task-transformation-789", "type": "transformation", "name": "Delete incorrect actions", "parameters": {"transformation": {"externalId": "tr-AIM-COR-ALL-ICAP-ACTION-DEL", "concurrencyPolicy": "fail", "useTransformationCredentials": false}}, "onFailure": "skipTask", "dependsOn": [{"externalId": "task-transformation-257"}]}, {"externalId": "task-transformation-82", "type": "transformation", "name": "Link viewers to private actions", "parameters": {"transformation": {"externalId": "tr-AIM-COR-ALL-ICAP-ACT-VIEWER", "concurrencyPolicy": "fail", "useTransformationCredentials": false}}, "timeout": 10000, "onFailure": "abortWorkflow", "dependsOn": [{"externalId": "task-transformation-441"}]}, {"externalId": "task-transformation-998", "type": "transformation", "name": "Link viewers to private events", "parameters": {"transformation": {"externalId": "tr-AIM-COR-ALL-ICAP-SEVT-VIEWER", "concurrencyPolicy": "fail", "useTransformationCredentials": false}}, "timeout": 10000, "onFailure": "abortWorkflow", "dependsOn": [{"externalId": "task-transformation-824"}]}, {"externalId": "task-transformation-876", "type": "transformation", "name": "Fills MetadataFields of actions", "parameters": {"transformation": {"externalId": "tr-AIM-COR-ALL-ICAP-MTDTF", "concurrencyPolicy": "fail", "useTransformationCredentials": false}}, "timeout": 10000, "onFailure": "abortWorkflow", "dependsOn": [{"externalId": "task-transformation-441"}]}]}