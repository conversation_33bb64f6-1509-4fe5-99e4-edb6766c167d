import { Box, Typography } from '@mui/material'
import { ModalWrapper } from './Modal/ModalWrapper'
import { ClnButton, MatIcon } from '@celanese/ui-lib'
import { useBulkUpload } from '@/app/common/contexts/BulkUploadContext'
import { useRef } from 'react'
import LoaderCircular from '../Loader'
import { translate } from '@/app/common/utils/generate-translate'

const modalTitleStyles = {
    color: 'black',
    fontSize: '22px',
    marginRight: 'auto',
    paddingLeft: '16px',
}

const modalContentStyles = {
    padding: '0px 16px 16px 16px',
    width: '80vw',
    maxWidth: '600px',
    '@media (max-width: 600px)': { width: '100%' },
}

const messageContainerStyles = {
    display: 'flex',
    gap: '12px',
}

const messageBoxStyles = {
    display: 'flex',
    flexDirection: 'column',
    gap: '12px',
    border: 'solid 1px',
    borderColor: 'rgba(196, 196, 196, 1)',
    borderRadius: '8px',
    padding: '20px',
}

const actionButtonsContainerStyles = {
    display: 'flex',
    justifyContent: 'flex-end',
    marginTop: '12px',
    gap: '12px',
}

const loaderContainerStyles = {
    display: 'flex',
    justifyContent: 'flex-end',
}

export function BulkUploadModal() {
    const { state, setState, siteId, userId, eventId, generateErrorReportCsv, handleBulkUpload } = useBulkUpload()
    const fileInputRef = useRef<HTMLInputElement | null>(null)

    const handleReuploadFile = () => {
        if (fileInputRef.current) {
            fileInputRef.current.value = ''
            fileInputRef.current.click()
        }
    }

    const onCloseModal = () => {
        setState((prev) => ({
            ...prev,
            modalType: undefined,
            successMessage: '',
            errorMessage: '',
            errorsCount: 0,
            errors: [],
        }))
    }

    return (
        <ModalWrapper
            title={
                state.modalType === 'loading'
                    ? translate('bulkUploadActionItems.uploadLoading')
                    : `${translate('table.headers.actions')} (${state.errorsCount} ${translate('common.errors')})`
            }
            openModal={!!state.modalType}
            closeModal={onCloseModal}
            sxProps={{ backgroundColor: 'rgba(0, 0, 0, 0.5)' }}
            sxPropsTitle={modalTitleStyles}
            content={
                <Box sx={modalContentStyles}>
                    {state.modalType === 'loading' ? (
                        <>
                            <Typography sx={{ fontSize: '16px' }}>
                                {translate('bulkUploadActionItems.uploadLoadingMessage')}
                            </Typography>
                            {LoaderCircular()}
                            <Box sx={loaderContainerStyles}>
                                <ClnButton
                                    variant="text"
                                    label={translate('requestModal.close')}
                                    onClick={onCloseModal}
                                    color="primary"
                                />
                            </Box>
                        </>
                    ) : (
                        <>
                            <Box sx={messageBoxStyles}>
                                {state.errorsCount > 0 && (
                                    <Typography variant="h4" component="h4" sx={{ fontSize: '20px' }}>
                                        {translate('bulkUploadActionItems.errorList')}
                                    </Typography>
                                )}
                                <Box sx={messageContainerStyles}>
                                    <MatIcon icon="check" color="success.main" />
                                    <Typography component="p" sx={{ fontSize: '16px' }}>
                                        {state.successMessage}
                                    </Typography>
                                </Box>
                                {state.errorsCount > 0 && (
                                    <Box sx={messageContainerStyles}>
                                        <MatIcon
                                            icon="warning"
                                            color="error.main"
                                            sx={{
                                                fontVariationSettings: "'FILL' 1",
                                            }}
                                        />
                                        <Typography component="p" sx={{ fontSize: '16px' }}>
                                            {state.errorMessage}
                                        </Typography>
                                    </Box>
                                )}
                            </Box>
                            <Box sx={actionButtonsContainerStyles}>
                                <ClnButton
                                    variant="text"
                                    label={translate('requestModal.close')}
                                    onClick={onCloseModal}
                                    color="error"
                                />
                                {state.errorsCount > 0 && (
                                    <ClnButton
                                        variant="outlined"
                                        label={translate('bulkUploadActionItems.downloadErrorReport')}
                                        data-origin="ui-lib"
                                        onClick={generateErrorReportCsv}
                                    />
                                )}
                                <ClnButton
                                    label={translate('bulkUploadActionItems.reuploadFile')}
                                    data-origin="ui-lib"
                                    onClick={handleReuploadFile}
                                />
                            </Box>
                            <input
                                type="file"
                                accept=".xlsx"
                                ref={fileInputRef}
                                style={{ display: 'none' }}
                                onChange={(e) => handleBulkUpload(e, siteId, userId, eventId)}
                            />
                        </>
                    )}
                </Box>
            }
        />
    )
}
