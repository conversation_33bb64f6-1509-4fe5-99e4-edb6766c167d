import azure.functions as func
from infra.action_item_reference_retriever_factory import ActionItemReferenceRetrieverFactory

bp = func.Blueprint()

@bp.function_name(name="GetActionItemsReference")
@bp.route("get-action-items-reference", methods=["get"], auth_level=func.AuthLevel.ANONYMOUS)
async def main(req: func.HttpRequest) -> func.HttpResponse:
    import logging
    import json as json

    try:
        logging.info("Function GetActionItems started")
        items = await ActionItemReferenceRetrieverFactory.retriever().get_action_items_reference()
        logging.info(f"Finishing execution - Items Retrieved : {items.count}")
        return func.HttpResponse(json.dumps(items))
    except Exception as e:
        logging.info("Exception found!")
        return func.HttpResponse(f"Error: {e}", status_code=500)
    