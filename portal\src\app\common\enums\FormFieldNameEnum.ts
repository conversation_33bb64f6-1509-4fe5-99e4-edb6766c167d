export enum FormFieldNameEnum {
    SourceInformation = 'SourceInformation',
    Owner = 'Owner',
    Unit = 'Unit',
    Location = 'Location',
    Category = 'Category',
    Subcategory = 'Subcategory',
    SiteSpecificCategory = 'SiteSpecificCategory',
    TaskType = 'TaskType',
    AssigmentDate = 'AssigmentDate',
    DueDate = 'DueDate',
    ApproverToggle = 'ApproverToggle',
    Approver = 'Approver',
    VerifierToggle = 'VerifierToggle',
    Verifier = 'Verifier',
    EvidenceRequired = 'EvidenceRequired',
}
