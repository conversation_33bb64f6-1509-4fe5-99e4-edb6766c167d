import React from 'react'
import { Box, CSSObject, Typography } from '@mui/material'

type GenericFieldTitleProps = {
    fieldName: string
    isSubHeader?: boolean
    isBorder?: boolean
    isSectionTitle?: boolean
    isSectionSubtitle?: boolean
    isParagraphText?: boolean
    isLocation?: boolean
    isDetailsExternalId?: boolean
    isBold?: boolean
    isError?: boolean
    icon?: React.JSX.Element
}

const tertiaryColor = 'text.disabled'

const getDynamicStyles = (props: GenericFieldTitleProps): CSSObject => ({
    fontSize:
        props.isSubHeader || props.isLocation || props.isSectionSubtitle
            ? '16px'
            : props.isSectionTitle
              ? '18px'
              : props.isParagraphText || props.isDetailsExternalId
                ? '13px'
                : 'inherit',

    fontWeight:
        props.isBold || props.isSubHeader || props.isSectionTitle || props.isLocation
            ? '600 !important'
            : '400 !important',

    color: props.isError
        ? 'erro.main'
        : props.isSubHeader
          ? 'text.secondary'
          : props.isSectionTitle || props.isLocation
            ? 'primary.main'
            : props.isDetailsExternalId
              ? 'text.secondary'
              : 'inherit',

    textTransform: props.isSubHeader ? 'uppercase' : 'none',

    display: 'flex',
    justifyContent: 'flex-start',
    justifyItems: 'center',
    alignItems: 'center',
    gap: '1rem',
})

const getDynamicStylesBorder = (props: GenericFieldTitleProps): CSSObject => ({
    gap: '1rem',
    ...(props.isBorder && {
        borderBottom: `1px solid`,
        borderColor: tertiaryColor,
        margin: '10px 0px',
    }),
})

const GenericFieldTitle = (props: GenericFieldTitleProps) => (
    <Box sx={props.isBorder ? getDynamicStylesBorder(props) : {}}>
        <Typography sx={getDynamicStyles(props)}>
            {props.fieldName}
            {props.icon && props.icon}
        </Typography>
    </Box>
)

export default GenericFieldTitle
