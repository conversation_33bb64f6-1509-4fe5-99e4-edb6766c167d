from enum import Enum, StrEnum


class DataModelIdEnum(StrEnum):
    """Represents data model external ids"""

    AIM_DATA_MODEL_ID = "ActionItemManagementDOM"
    QUALITY_RELIABILITY_DATA_MODEL_ID = "QualityReliabilitySOL"
    RCA_DATA_MODEL_ID = "RCASOL"
    QUALITY_DATA_MODEL_ID = "QualityDOM"


class DataModelSpaceEnum(StrEnum):
    """Represents data model spaces"""

    APPROVAL_WORKFLOW_DATA_MODEL_SPACE = "APW-COR-ALL-DMD"
    ACTION_DATA_MODEL_SPACE = "AIM-COR-ALL-DMD"
    UMG_DATA_MODEL_SPACE = "UMG-COR-ALL-DMD"
    QUALITY_RELIABILITY_DATA_MODEL_SPACE = "QAR-COR-ALL-DML"
    RCA_DATA_MODEL_SPACE = "INO-COR-ALL-DML"
    ASSET_HIERARCHY_DATA_MODEL_SPACE = "EDG-COR-ALL-DMD"
    QUALITY_DATA_MODEL_SPACE = "EDG-COR-ALL-DMD"


class DataSpaceEnum(StrEnum):
    """Represents instance spaces"""

    REF_DATA_SPACE = "REF-COR-ALL-DAT"
    AIM_REF_DATA_SPACE = "AIM-COR-ALL-REF"
    APW_REF_DATA_SPACE = "APW-COR-ALL-REF"
    UMG_DATA_SPACE = "UMG-COR-ALL-DAT"
    PRIVATE_SPACE = "AIM-COR-ALL-PROT"
    COR_SPACE = "AIM-COR-ALL-DAT"


class ApplicationEnum(StrEnum):
    """Represent application spaces"""

    AIM = "APP-AIM"


class EntitiesEnum(StrEnum):
    """Represent entities codes"""

    Action = "ACT"
    ActionItemLink = "ACTL"
    StatusHistoryInstance = "SHINS"
    RecurrenceInstance = "RCI"
    Attachment = "ACTATT"
    ApprovalWorkflow = "APWE"
    ApprovalWorkflowStep = "APWSP"
    ProcessedActionItemEvent = "PACTEVT"
    ProcessedActionItemEventStatus = "PACTEVTS"
    SourceEvent = "SEVT"
    SourceEventHistory = "SEVTH"


class ActionStatusEnum(StrEnum):
    """Represents ActionItemStatus externalIds"""

    ASSIGNED = "ACTS-assigned"
    DUE_DATE_EXTENSION_PERIOD = "ACTS-dueDateExtensionPeriod"
    REASSIGNMENT_PERIOD = "ACTS-reassignmentPeriod"
    PENDING_APPROVAL = "ACTS-pendingApproval"
    PENDING_VERIFICATION = "ACTS-pendingVerification"
    CHALLENGE_PERIOD = "ACTS-challengePeriod"
    VERIFICATION_REJECTED = "ACTS-verificationRejected"
    APPROVAL_REJECTED = "ACTS-approvalRejected"
    DELETED = "ACTS-deleted"
    CANCELLED = "ACTS-cancelled"
    COMPLETED = "ACTS-completed"
    ACTIVE = "ACTS-active"
    INACTIVE = "ACTS-inactive"


class ActionStatusClearEnum(StrEnum):
    """Represents ActionItemStatus externalIds to be used upon filter clear"""

    ASSIGNED = "ACTS-assigned"
    DUE_DATE_EXTENSION_PERIOD = "ACTS-dueDateExtensionPeriod"
    REASSIGNMENT_PERIOD = "ACTS-reassignmentPeriod"
    PENDING_APPROVAL = "ACTS-pendingApproval"
    PENDING_VERIFICATION = "ACTS-pendingVerification"
    CHALLENGE_PERIOD = "ACTS-challengePeriod"
    VERIFICATION_REJECTED = "ACTS-verificationRejected"
    APPROVAL_REJECTED = "ACTS-approvalRejected"


class ActionStatusOpenEnum(StrEnum):
    """Represents ActionItemStatus externalIds for "open" actions"""

    ASSIGNED = "ACTS-assigned"
    DUE_DATE_EXTENSION_PERIOD = "ACTS-dueDateExtensionPeriod"
    REASSIGNMENT_PERIOD = "ACTS-reassignmentPeriod"
    CHALLENGE_PERIOD = "ACTS-challengePeriod"
    VERIFICATION_REJECTED = "ACTS-verificationRejected"
    APPROVAL_REJECTED = "ACTS-approvalRejected"


class ActionStatusClosedEnum(StrEnum):
    """Represents ActionItemStatus externalIds for "closed" actions"""

    CANCELLED = "ACTS-cancelled"
    COMPLETED = "ACTS-completed"


class ApprovalWorkflowStepDescriptionEnum(StrEnum):
    """Represents ApprovalWorkflowStep descriptions"""

    VERIFICATION = "AIM-verification"
    APPROVAL = "AIM-approval"
    REASSIGNMENT = "AIM-reassignment"
    EXTENSION = "AIM-extension"
    CHALLENGE = "AIM-challenge"


class ApprovalWorkflowDescriptionEnum(StrEnum):
    """Represents ApprovalWorkflow descriptions"""

    WORKFLOW = "AIM-workflow"
    ASSIGNEE = "AIM-assignee-request"


class ApprovalWorkflowStatusEnum(StrEnum):
    """Represents ApprovalWorkflowStatus externalIds"""

    APPROVED = "APWST-Approved"
    PROGRESS = "APWST-InProgress"
    PENDING = "APWST-Pending"
    REJECTED = "APWST-Rejected"


class ChangeRequestTypeEnum(StrEnum):
    """Represents ActionItemChangeRequestType externalIds"""

    DUE_DATE = "AICRT-dueDateExtension"
    REASSIGNMENT = "AICRT-reassignment"
    CHALLENGE = "AICRT-challenge"


class ViewEnum(StrEnum):
    """Represents views externalIds"""

    ACTION = "Action"
    ACTION_ITEM_LINK = "ActionItemLink"
    APPROVAL_WORKFLOW = "ApprovalWorkflow"
    APPROVAL_WORKFLOW_STEP = "ApprovalWorkflowStep"
    SOURCE_EVENT = "SourceEvent"
    USER_COMPLEMENT = "UserComplement"
    STATUS_HISTORY_INSTANCE = "StatusHistoryInstance"
    USER_AZURE_ATTRIBUTE = "UserAzureAttribute"
    CHANGE_REQUEST = "ActionItemChangeRequest"
    RECURRENCE = "RecurrenceInstance"
    SOURCE_EVENT_HISTORY = "SourceEventHistory"
    ACTION_COMMENT = "ActionComment"
    REPORTING_SITE = "ReportingSite"
    USER = "User"
    CATEGORY = "ActionItemCategory"
    SUB_CATEGORY = "ActionItemSubCategory"
    SITE_SPECIFIC_CATEGORY = "SiteSpecificCategory"
    ACTION_STATUS = "ActionItemStatus"
    APPLICATION = "Application"
    REPORTING_UNIT = "ReportingUnit"
    REPORTING_LOCATION = "ReportingLocation"
    SOURCE_EVENT_STATUS = "SourceEventStatus"
    FUNCTIONAL_LOCATION = "FunctionalLocation"
    EQUIPMENT = "Equipment"
    TEMPLATE_CONFIGURATION = "TemplateConfiguration"
    USER_ROLE_SITE = "UserRoleSite"


class ApprovalWorkflowStepStatusEnum(StrEnum):
    """Represents ApprovalWorkflowStepStatus externalIds"""

    IN_PROGRESS = "APWST-InProgress"
    PENDING = "APWST-Pending"


class EmployeeStatusEnum(StrEnum):
    """Represents EmployeeStatus externalIds"""

    INACTIVE = "EMST_INACTIVE"


class ActionItemKind(StrEnum):
    """Represents ActionItemKind externalIds"""

    ONE_TIME = "ACTK-oneTime"
    RECURRING = "ACTK-recurring"


class FileMetadataKey(StrEnum):
    """Represents metadata keys for AIM files"""

    RELATED_ACTIONS = "relatedActions"
    FILE_SIZE = "fileSize"
    SOURCE_FILE_EXTERNAL_ID = "sourceFileExternalId"
    USER = "user"
    RELATED_EVENTS = "relatedEvent"


class EdgeType(Enum):
    """Represents edgeIds (model_space, externalId)"""

    ACTION_TO_VIEW_USERS = (
        DataModelSpaceEnum.ACTION_DATA_MODEL_SPACE,
        "Action.viewUsers",
    )
    ACTION_TO_VIEW_ROLES = (
        DataModelSpaceEnum.ACTION_DATA_MODEL_SPACE,
        "Action.viewRoles",
    )
    ACTION_TO_VIEW_TEAMS = (
        DataModelSpaceEnum.ACTION_DATA_MODEL_SPACE,
        "Action.viewTeams",
    )
    ACTION_TO_ASSIGNEES = (
        DataModelSpaceEnum.ACTION_DATA_MODEL_SPACE,
        "Action.assignees",
    )
    APPROVAL_WORKFLOW_STEP_TO_USERS = (
        DataModelSpaceEnum.APPROVAL_WORKFLOW_DATA_MODEL_SPACE,
        "ApprovalWorkflowStep.users",
    )


# TODO: include types below in above enum
APPROVAL_WORKFLOW_STEP_TO_USERS = {
    "externalId": "ApprovalWorkflowStep.users",
    "space": DataModelSpaceEnum.APPROVAL_WORKFLOW_DATA_MODEL_SPACE,
}

IMPACTED_REPORTING_UNITS_TO_SOURCE_EVENTS = {
    "externalId": "SourceEvent.reportingUnits",
    "space": DataModelSpaceEnum.ACTION_DATA_MODEL_SPACE,
}

REPORTING_UNITS_TO_USER_COMPLEMENTS = {
    "externalId": "UserComplement.reportingUnits",
    "space": DataModelSpaceEnum.UMG_DATA_MODEL_SPACE,
}

REPORTING_LOCATIONS_TO_USER_COMPLEMENTS = {
    "externalId": "UserComplement.reportingLocations",
    "space": DataModelSpaceEnum.UMG_DATA_MODEL_SPACE,
}


class KpiNameEnum(StrEnum):
    """Represents available KPI types"""

    TOTAL_ACTIONS = "TotalActions"
    ASSIGNED_TO_ME = "AssignedToMe"
    RELATED_TO_ME = "RelatedToMe"
    PENDING_APPROVALS = "PendingApprovals"
    PENDING_VERIFICATIONS = "PendingVerifications"
    OVERDUE = "Overdue"
    MY_APPROVALS = "MyApprovals"
    MY_VERIFICATIONS = "MyVerifications"
    MY_CHALLENGES = "MyChallenges"
    MY_EXTENDS = "MyExtends"
    MY_REASSIGNMENT = "MyReassignment"
    MY_ACTIONS = "MyActions"


class ActionSourceTypeEnum(StrEnum):
    """Represents available Action Source types"""

    MOC_MDR_EQUIPMENT_REQUEST = "AST-MOC-MDREquipmentRequest"
    MOC_MDR_PDPM_REQUEST = "AST-MOC-MDRPdpmRequest"
    AIM_EVENT = "AST-AIM-AIMEvent"
    RCA_EVENT = "AST-RCA-RCAEvent"
    MOC_MDR_MDM_REQUEST = "AST-MOC-MDRMdmRequest"
    AIM_SCRATCH = "AST-AIM-AIMScratch"
    ICAP_MOC_REPORT = "AST-ICAP-ICAPMOCReport"
    ICAP_MOOC_REPORT = "AST-ICAP-ICAPMOOCReport"
    CIA_GAP_ASSESSMENT_EXECUTION_DATA = "AST-CIA-GapAssessmentExecutionData"
    OFWA_EVENT = "AST-OFWA-OFWAEvent"
    CTW_EMERGING_RISK = "AST-CTW-EmergingRisk"
    MOCP_DEVIATION = "AST-MOCP-Deviation"
    MOCP_PSMC = "AST-MOCP-PSMC"
    MOCP_PSMC_MSR = "AST-MOCP-PSMC-MSR"
    MOCP_PSMC_DR = "AST-MOCP-PSMC-DR"
    MOCP_PSMC_PSSR = "AST-MOCP-PSMC-PSSR"
    MOCP_PSMC_PRE = "AST-MOCP-PSMC-PRE"
    MOCP_PSMC_POST = "AST-MOCP-PSMC-POST"
    MOCP_PSMC_MSR_PRE = "AST-MOCP-PSMC-MSR-PRE"
    MOCP_PSMC_MSR_POST = "AST-MOCP-PSMC-MSR-POST"
    FRP_INCIDENT = "AST-FRP-Incident"
    RCA_EVN_INV_RCACEVENT = "AST-RCA-EVN-INV-RCACEVENT"
    RCA_EVN_INV_RCAABEVENT = "AST-RCA-EVN-INV-RCAABEVENT"
    RCA_QR_EVTY_CCI = "AST-RCA-Q&R-EVTY-CCI"
    RCA_QR_EVTY_RAR = "AST-RCA-Q&R-EVTY-RAR"
    RCA_QR_EVTY_QAR = "AST-RCA-Q&R-EVTY-QAR"
    RCA_WI_EVTY_ADM = "AST-RCA-WI-EVTY-ADM"
    RCA_WI_EVTY_ENG = "AST-RCA-WI-EVTY-ENG"
    RCA_WI_EVTY_MNTC = "AST-RCA-WI-EVTY-MNTC"
    RCA_WI_EVTY_QA = "AST-RCA-WI-EVTY-QA"
    RCA_WI_SAP_Q1 = "AST-RCA-SAP-EVTY-Q1"
    RCA_WI_SAP_Q3 = "AST-RCA-SAP-EVTY-Q3"


class SourceEventTypeEnum(StrEnum):
    """Represents available Source Event types"""

    INTERNAL = "SEVTY-Internal"
    EXTERNAL = "SEVTY-External"
    AUTOMATIC = "SEVTY-Automatic"
    ICAP_EVENT = "SEVTY-ICAP-Event"


USER_AZURE_ATTRIBUTE_PREFIX = "UserAzureAttribute_"
USER_COMPLEMENT_PREFIX = "UserComplement_"


APPLICATION_JSON = "application/json"
LIMIT = 10_000
AGGREGATE_LIMIT = 1_000
SEARCH_LIMIT = 1_000
SEARCH_FILTER_LIMIT = 100

APPLICATION_APP_ICAP = "APP-ICAP"
WAS_SITE_EXTERNAL_ID = "STS-WAS"
