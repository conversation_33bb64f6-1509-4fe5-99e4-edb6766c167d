from clients.core.models import ExternalSourceServiceParams
from clients.external_source.quality.quality_client import (
    QualityClient,
)
from clients.external_source.quality_reliability.quality_reliability_client import (
    QualityReliabilityClient,
)
from clients.external_source.root_cause_analysis.root_cause_analysis_client import (
    RootCauseAnalysisClient,
)


class ExternalSourceClient:
    """Client to access external source services."""

    def __init__(
        self,
        rca_params: ExternalSourceServiceParams,
        qr_params: ExternalSourceServiceParams,
        quality_params: ExternalSourceServiceParams,
    ) -> None:
        """
        Initialize the ExternalSourceClient.

        Args:
            rca_params (ExternalSourceServiceParams): Parameters for Root Cause Analysis client.
            qr_params (ExternalSourceServiceParams): Parameters for Quality & Reliability client.
            quality_params (ExternalSourceServiceParams): Parameters for Quality client.

        """
        self.root_cause_analysis = RootCauseAnalysisClient(rca_params)
        self.quality_reliability = QualityReliabilityClient(qr_params)
        self.quality = QualityClient(quality_params)
