import { Box } from '@mui/material'
import { ComponentProps } from 'react'
import { <PERSON><PERSON><PERSON> } from './charts/PieChart'
import LoaderCircular from '../Loader'
import { ClnPanel } from '@celanese/ui-lib'
import GenericFieldTitle from '../FieldsComponent/GenericFieldTitle'

type Props = {
    key?: string
    type: ComponentProps<typeof PieChart>['type']
    dataChart: any
    label: string
    height?: number
    width?: number | string
    loading?: boolean
}

export function RoundChartWrapper({ key, dataChart, type, label, height, width, loading }: Props) {
    return (
        <ClnPanel
            sx={{
                padding: '1rem',
                display: 'flex',
                flexDirection: 'column',
                marginBottom: '1rem',
                height: '100%',
            }}
            id={key ?? 'round-chart-default'}
        >
            <GenericFieldTitle fieldName={label} isSubHeader />
            {loading ? (
                LoaderCircular()
            ) : (
                <Box
                    sx={{
                        width: '100%',
                        height: '100%',
                        alignItems: 'center',
                    }}
                >
                    <PieChart id={label} data={dataChart} type={type} height={height} width={width} />
                </Box>
            )}
        </ClnPanel>
    )
}
