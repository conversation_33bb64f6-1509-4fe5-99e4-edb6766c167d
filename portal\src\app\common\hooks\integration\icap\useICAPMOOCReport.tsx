import { Filter } from '@/app/common/models/base-hook-request'
import { useState, useEffect } from 'react'
import { useGetAllResultsFunctionFromCustomClient } from '../../general-functions/useGetAllResultFunction'
import { ICAPMOOCReport } from '@/app/common/models/integration/icap/icap-mooc-report'

const moocReportQuery = `
    event {
        owner {
            firstName
            lastName
        }
        name
        description
        number
        createdTime
        businessLine
        reportingUnit {
            description
        }
    }
    newEmployee {
        firstName
        lastName
    }
    positionImpacted
`

export const useICAPMOOCReport = (externalId?: string) => {
    const [resultData, setResultData] = useState<{ data: ICAPMOOCReport[]; loading: boolean }>({
        data: [],
        loading: true,
    })

    const { getAllItems: getAllData } = useGetAllResultsFunctionFromCustomClient<ICAPMOOCReport>(
        moocReportQuery,
        'listICAPMOOCReport',
        'icap'
    )

    const filter: Filter<ICAPMOOCReport> = {
        externalId: { eq: externalId ?? '' },
    }

    useEffect(() => {
        if (!externalId) return

        getAllData(filter).then((res: any) => {
            if (res.length == 0) {
                setResultData({ data: [], loading: false })
            } else {
                setResultData({ data: res, loading: false })
            }
        })
    }, [externalId])

    return {
        loading: resultData.loading,
        data: resultData.data,
    }
}
