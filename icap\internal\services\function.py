from cognite.client import CogniteClient

from ..configs import (
    Config,
    FunctionConfig,
    Settings,
)
from ..constants import COR_DATA_SET_EXTERNAL_ID
from ..utils import zip_folder


class FunctionService:
    def __init__(self, cognite_client: CogniteClient):
        self._cognite_client = cognite_client
        self._target_data_set_id = self._get_data_set_id()

    def deploy_functions(self, config: Config):
        for function_config in config.functions:
            self._deploy_function(config.env_settings, function_config)

    def _deploy_function(self, env_settings: Settings, function_config: FunctionConfig):
        print("zipping the folder...")
        function_content = zip_folder(
            ".",
            ignore_folders=function_config.ignore_folders,
            ignore_files=function_config.ignore_files,
        )

        print(self._target_data_set_id)
        print(len(function_content))

        print("uploading the folder...")
        file = self._cognite_client.files.upload_bytes(
            content=function_content,
            name=f"{function_config.name}.zip",
            external_id=f"{function_config.name}",
            overwrite=True,
            data_set_id=self._target_data_set_id,
        )

        if self._cognite_client.functions.retrieve(external_id=function_config.name):
            print("deleting previous function...")
            self._cognite_client.functions.delete(external_id=function_config.name)

        print("creating function...")
        self._cognite_client.functions.create(
            function_config.name,
            external_id=function_config.name,
            function_path=function_config.function_path,
            file_id=file.id,
        )

        if function_config.cron is not None:
            print("creating function schedule...")
            self._cognite_client.functions.schedules.create(
                name=f"schedule-{function_config.name}",
                function_external_id=function_config.name,
                cron_expression=function_config.cron,
                client_credentials={
                    "client_id": env_settings.client_id,
                    "client_secret": env_settings.secret,
                },
            )

    def _get_data_set_id(self) -> int:
        return self._cognite_client.data_sets.retrieve(
            external_id=COR_DATA_SET_EXTERNAL_ID
        ).id
