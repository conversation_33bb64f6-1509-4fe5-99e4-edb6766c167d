import { ActionItemCategory } from './category'
import { SiteSpecificCategory } from './site-specific-category'
import { ActionItemSubCategory } from './sub-category'

export class CategoryConfiguration {
    externalId!: string
    category!: ActionItemCategory
    actionItemSubCategory!: ActionItemSubCategory
    siteSpecificCategory!: SiteSpecificCategory
    isApprovalRequired!: boolean
    isVerificationRequired!: boolean
    attachmentRequired!: boolean
    isExtensionsAllowed!: boolean
    isExtensionAttachmentRequired!: boolean
    daysToApproval!: number | null | undefined
    daysToVerification!: number | null | undefined
    daysFromAssignedDate!: number | null | undefined
    defaultApprovalRole!: DefaultRole | null | undefined
    defaultVerificationRole!: DefaultRole | null | undefined
    defaultExtensionApproverRole!: DefaultRole | null | undefined
    hasEmailNotification!: boolean
    defaultApprovalUser!: DefaultUser | null | undefined
    defaultVerificationUser!: DefaultUser | null | undefined
}

export class DefaultRole {
    externalId!: string
    space!: string
}

export class DefaultUser {
    externalId!: string
    space!: string
    email?: string
}
