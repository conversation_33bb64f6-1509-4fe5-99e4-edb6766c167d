# Manifest file downloaded from fusion
externalId: tr-AIM-COR-ALL-ICAP-ACT-SOURCEID
name: AIM-COR-ALL-ICAP-ACT-SOURCEID
query: >-
  WITH mooc AS (
    SELECT
      aim_action.externalId AS externalId,
      aim_action.space AS space,
      node_reference('AIM-COR-ALL-REF', 'AST-ICAP-ICAPMOOCReport') AS sourceType,
      icap_mooc.externalId AS sourceId,
      ROW_NUMBER() OVER (
        PARTITION BY aim_action.externalId, aim_action.space
        ORDER BY aim_action.space
      ) AS row_num
    FROM cdf_data_models('AIM-COR-ALL-DMD','ActionItemManagementDOM','6_5_0','Action') AS aim_action
    INNER JOIN `ICAP-COR`.`EVNT-tblEvent` AS e
      ON e.EventID = split_part(aim_action.objectType, '-', -1)
    INNER JOIN cdf_data_models('ICAP-COR-ALL-DMD','ICAPDOM','1_4_2','ICAPActionReport') AS icap_action_report
      ON concat(split_part(aim_action.objectType, '-', 2), '') = concat(cast(icap_action_report.`number` AS STRING), '')
    INNER JOIN cdf_data_models('ICAP-COR-ALL-DMD','ICAPDOM','1_4_2','ICAPMOOCReport','actionReports') AS icap_moc_to_action_report
      ON concat(icap_moc_to_action_report.endNode.externalId, '') = concat(icap_action_report.externalId, '')
    INNER JOIN cdf_data_models('ICAP-COR-ALL-DMD','ICAPDOM','1_4_2','ICAPMOOCReport') AS icap_mooc
      ON concat(icap_moc_to_action_report.startNode.externalId, '') = concat(icap_mooc.externalId, '')
    WHERE startswith(aim_action.objectType, 'ICAP')
      AND split_part(aim_action.objectType,'-', -2) = 'Event'
      AND aim_action.sourceId IS NULL
      AND e.EventCategoryID = 21
  ),
  moc AS (
    SELECT
      aim_action.externalId AS externalId,
      aim_action.space AS space,
      CASE
        WHEN moc_action.ReviewID IS NOT NULL THEN node_reference('AIM-COR-ALL-REF', 'AST-MOCP-PSMC-DR')
        WHEN moc_action.PSSRReviewID IS NOT NULL THEN node_reference('AIM-COR-ALL-REF', 'AST-MOCP-PSMC-PSSR')
        WHEN moc_action.ActivityTypeID = 4 THEN node_reference('AIM-COR-ALL-REF', 'AST-MOCP-PSMC-POST')
        WHEN moc_action.ActivityTypeID = 3 THEN node_reference('AIM-COR-ALL-REF', 'AST-MOCP-PSMC-PRE')
        ELSE NULL
      END AS sourceType,
      CONCAT('PSMC-', moc_action.MOCID) AS sourceId,
      ROW_NUMBER() OVER (
          PARTITION BY aim_action.externalId, aim_action.space
          ORDER BY aim_action.space
        ) AS row_num
    FROM cdf_data_models('AIM-COR-ALL-DMD','ActionItemManagementDOM','6_5_0','Action') AS aim_action
    INNER JOIN `ICAP-COR`.`MOC-tblMOCActionItem` AS moc_action
      ON moc_action.ActionItemID = split_part(aim_action.objectType, '-', 2)
    WHERE startswith(aim_action.objectType, 'ICAP')
      AND split_part(aim_action.objectType, '-', -2) = 'Event'
  ),
  rca_event_investigation AS (
    SELECT
      aim_action.externalId AS externalId,
      aim_action.space AS space,
      if(
        e.EventCategoryID = 5 AND ei.category = 'RCA-C',
        node_reference('AIM-COR-ALL-REF', 'AST-RCA-EVN-INV-RCACEVENT'),
        if(
          e.EventCategoryID = 9 AND ei.category = 'RCA-A/B',
          node_reference('AIM-COR-ALL-REF', 'AST-RCA-EVN-INV-RCAABEVENT'),
          aim_action.sourceType
        )
      ) AS sourceType,
      concat('EVEN-EVN-INV-', e.EventID) AS sourceId,
      ROW_NUMBER() OVER (PARTITION BY aim_action.externalId, aim_action.space ORDER BY aim_action.space) AS row_num
    FROM cdf_data_models("AIM-COR-ALL-DMD", "ActionItemManagementDOM", "6_5_0", "Action") AS aim_action
    INNER JOIN `ICAP-COR`.`EVNT-tblEvent` AS e ON e.EventID = split_part(aim_action.objectType, '-', -1)
    INNER JOIN cdf_data_models("INO-COR-ALL-DML", "RCASOL", "7_0_0", "EventAnalysis") AS ea ON ea.externalId = concat('EVEN-EVN-INV-', e.EventID)
    INNER JOIN cdf_data_models("INO-COR-ALL-DML", "RCASOL", "7_0_0", "EventInvestigation") AS ei ON ei.externalId = concat('EVN-INV-', e.EventID)
    WHERE startswith(aim_action.objectType, 'ICAP')
      AND split_part(aim_action.objectType, '-', -2) = 'Event'
      AND aim_action.sourceId IS NULL
      AND e.EventCategoryID IN (5, 9)
  ),
  rca_work_investigation AS (
    SELECT
      aim_action.externalId AS externalId,
      aim_action.space AS space,
      node_reference(
        "AIM-COR-ALL-REF",
        CASE ea.eventType.externalId
          WHEN 'EVTY-ADM' THEN 'AST-RCA-WI-EVTY-ADM'
          WHEN 'EVTY-ENG' THEN 'AST-RCA-WI-EVTY-ENG'
          WHEN 'EVTY-MNTC' THEN 'AST-RCA-WI-EVTY-MNTC'
          WHEN 'EVTY-QA' THEN 'AST-RCA-WI-EVTY-QA'
          WHEN 'EVTY-Q1' THEN 'AST-RCA-SAP-EVTY-Q1'
          WHEN 'EVTY-Q3' THEN 'AST-RCA-SAP-EVTY-Q3'
        END
      ) AS sourceType,
      concat('EVEN-WI-', rcac.key) AS sourceId,
      ROW_NUMBER() OVER (PARTITION BY aim_action.externalId, aim_action.space ORDER BY aim_action.space) AS row_num
    FROM cdf_data_models("AIM-COR-ALL-DMD", "ActionItemManagementDOM", "6_5_0", "Action") AS aim_action
    INNER JOIN `ICAP-COR`.`RCAC-tblRCAC` rcac ON rcac.key = split_part(aim_action.objectType, '-', -1)
    INNER JOIN cdf_data_models("INO-COR-ALL-DML", "RCASOL", "7_0_0", "EventAnalysis") AS ea ON ea.externalId = concat('EVEN-WI-', rcac.key)
    INNER JOIN cdf_data_models("INO-COR-ALL-DML", "RCASOL", "7_0_0", "WorkProcessInvestigation") AS wpi ON wpi.externalId = concat('WI-', rcac.key)
    WHERE startswith(aim_action.objectType, 'ICAP')
      AND instr(aim_action.objectType, 'RCAC-WI') > 0
      AND aim_action.sourceId IS NULL
  ),
  union_cte as (
    SELECT * FROM mooc
    UNION
    SELECT * FROM moc
    UNION
    SELECT * FROM rca_event_investigation
    UNION
    SELECT * FROM rca_work_investigation
  )

  SELECT
    externalId,
    space,
    sourceType,
    sourceId
  FROM union_cte
  WHERE row_num = 1
destination:
  dataModel:
    space: AIM-COR-ALL-DMD
    externalId: ActionItemManagementDOM
    version: "6_5_0"
    destinationType: Action
  type: instances
ignoreNullFields: false
shared: true
action: upsert
dataSetExternalId: AIM-COR-ALL-DAT
# Specify credentials separately like this:
# authentication:
#   read:
#     clientId: ${READ_CLIENT_ID}
#     clientSecret: ${READ_CLIENT_SECRET}
#     tokenUrl: ${READ_TOKEN_URL}
#     cdfProjectName: ${READ_CDF_PROJECT_NAME}
#     # Optional: If idP requires providing the scopes
#     scopes:
#       - ${READ_SCOPES}
#     # Optional: If idP requires providing the audience
#     audience: ${READ_CDF_AUDIENCE}
#   write:
#     clientId: ${WRITE_CLIENT_ID}
#     clientSecret: ${WRITE_CLIENT_SECRET}
#     tokenUrl: ${WRITE_TOKEN_URL}
#     cdfProjectName: ${WRITE_CDF_PROJECT_NAME}
#     # Optional: If idP requires providing the scopes
#     scopes:
#       - ${WRITE_SCOPES}
#     # Optional: If idP requires providing the audience
#     audience: ${WRITE_CDF_AUDIENCE}
# Or together like this:
# authentication:
#   clientId: ${CLIENT_ID}
#   clientSecret: ${CLIENT_SECRET}
#   tokenUrl: ${TOKEN_URL}
#   # Optional: If idP requires providing the scopes
#   cdfProjectName: ${CDF_PROJECT_NAME}
#   scopes:
#    - ${SCOPES}
#   # Optional: If idP requires providing the audience
#   audience: ${AUDIENCE}
