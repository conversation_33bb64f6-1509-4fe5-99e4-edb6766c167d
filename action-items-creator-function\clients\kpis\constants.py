from clients.core.constants import (
    ActionStatusEnum,
    ApprovalWorkflowStepStatusEnum,
    DataSpaceEnum,
)


APPROVAL_WORKFLOW_STEP_ACTIVE_STATUS = [
    {
        "externalId": ApprovalWorkflowStepStatusEnum.IN_PROGRESS,
        "space": DataSpaceEnum.APW_REF_DATA_SPACE,
    },
    {
        "externalId": ApprovalWorkflowStepStatusEnum.PENDING,
        "space": DataSpaceEnum.APW_REF_DATA_SPACE,
    },
]


USER_AZURE_ATTRIBUTE_PREFIX = "UserAzureAttribute_"


ACTION_ACTIVE_STATUS = [
    {
        "externalId": ActionStatusEnum.ASSIGNED,
        "space": DataSpaceEnum.AIM_REF_DATA_SPACE,
    },
    {
        "externalId": ActionStatusEnum.PENDING_APPROVAL,
        "space": DataSpaceEnum.AIM_REF_DATA_SPACE,
    },
    {
        "externalId": ActionStatusEnum.PENDING_VERIFICATION,
        "space": DataSpaceEnum.AIM_REF_DATA_SPACE,
    },
    {
        "externalId": ActionStatusEnum.DUE_DATE_EXTENSION_PERIOD,
        "space": DataSpaceEnum.AIM_REF_DATA_SPACE,
    },
    {
        "externalId": ActionStatusEnum.REASSIGNMENT_PERIOD,
        "space": DataSpaceEnum.AIM_REF_DATA_SPACE,
    },
    {
        "externalId": ActionStatusEnum.CHALLENGE_PERIOD,
        "space": DataSpaceEnum.AIM_REF_DATA_SPACE,
    },
    {
        "externalId": ActionStatusEnum.APPROVAL_REJECTED,
        "space": DataSpaceEnum.AIM_REF_DATA_SPACE,
    },
    {
        "externalId": ActionStatusEnum.VERIFICATION_REJECTED,
        "space": DataSpaceEnum.AIM_REF_DATA_SPACE,
    },
]

ACTION_COLSED_STATUS = [
    {
        "externalId": ActionStatusEnum.COMPLETED,
        "space": DataSpaceEnum.AIM_REF_DATA_SPACE,
    },
    {
        "externalId": ActionStatusEnum.CANCELLED,
        "space": DataSpaceEnum.AIM_REF_DATA_SPACE,
    },
]
