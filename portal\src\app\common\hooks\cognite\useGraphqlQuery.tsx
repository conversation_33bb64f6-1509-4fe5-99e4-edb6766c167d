import { ApolloError, ApolloQueryResult, DocumentNode, QueryHookOptions, useQuery } from '@apollo/client'
import { useMemo } from 'react'

export type UseGraphqlQueryResult<T> = {
    data: T[]
    loading: boolean
    error?: ApolloError
    refetch: () => Promise<ApolloQueryResult<any>>
    pageInfo: PageInfo
}

export interface PageInfo {
    hasNextPage: boolean
    startCursor: string
    endCursor: string
}

export function useGraphqlQuery<T>(
    query: DocumentNode,
    listName: string,
    options?: QueryHookOptions<any, any>
): UseGraphqlQueryResult<T> {
    const defaultOptions = useMemo(
        () => ({
            notifyOnNetworkStatusChange: true,
            ...options,
        }),
        [options]
    )
    const { data, loading, error, refetch } = useQuery(query, defaultOptions)

    const listData = useMemo<T[]>(() => data?.[listName]?.items ?? [], [data, listName])
    const pageInfo = useMemo<PageInfo>(() => data?.[listName]?.pageInfo ?? {}, [data, listName])

    return useMemo(
        () => ({ data: listData, loading, error, refetch, pageInfo: pageInfo }),
        [error, listData, loading, refetch, pageInfo]
    )
}
