export interface BulkEditFormData {
    title?: string
    description?: string
    dueDate?: string
    assignedToId?: string
    approverId?: string
    verifierId?: string
    priorityId?: string
    categoryId?: string
    subCategoryId?: string
    reportingUnitId?: string
    reportingLocationId?: string
}

export interface BulkEditFieldConfig {
    key: keyof BulkEditFormData
    label: string
    type: 'text' | 'textarea' | 'date' | 'autocomplete'
    required?: boolean
    multiline?: boolean
    rows?: number
    autocompleteConfig?: {
        endpoint: string
        labelField: string
        valueField: string
        searchParam?: string
    }
}

export interface UpdateActionRequest {
    externalId: string
    activeUserEmail: string
    reportingSiteId: string
    title?: UpdatableField<string>
    description?: UpdatableField<string>
    dueDate?: UpdatableField<string>
    assignedToId?: UpdatableField<string>
    approverId?: UpdatableField<string>
    verifierId?: UpdatableField<string>
    priorityId?: UpdatableField<string>
    categoryId?: UpdatableField<string>
    subCategoryId?: UpdatableField<string>
    reportingUnitId?: UpdatableField<string>
    reportingLocationId?: UpdatableField<string>
}

export interface UpdatableField<T> {
    wasUpdated: boolean
    value: T | null
}

export interface BulkUpdateResponse {
    externalIds: string[]
    errors: Record<string, { internal: string[] }>
    processedCount: number
    errorCount: number
    batchSize?: number
}
