import React, { useState } from 'react'
import {
    <PERSON>alog,
    DialogTitle,
    DialogContent,
    DialogActions,
    Box,
    Typography,
    Divider,
    Chip,
    Alert,
    CircularProgress,
} from '@mui/material'
import { ClnButton } from '@celanese/ui-lib'
import { translate } from '@celanese/celanese-ui'
import { UserRolesPermission } from '@celanese/celanese-sdk'

import { AzureFunctionClient } from '../../common/clients/azure-function-client'
import { useSnackbar } from '../../common/contexts/SnackbarContext'
import { BulkEditForm } from './BulkEditForm'
import { BulkEditFormData } from './types'

interface BulkEditModalProps {
    onClose: (success?: boolean) => void
    selectedActionIds: string[]
    activeUser: UserRolesPermission
    siteId: string
}

export const BulkEditModal: React.FC<BulkEditModalProps> = ({
    onClose,
    selectedActionIds,
    activeUser,
    siteId,
}) => {
    const [isLoading, setIsLoading] = useState(false)
    const [formData, setFormData] = useState<BulkEditFormData>({})
    const [errors, setErrors] = useState<string[]>([])
    const { showSnackbar } = useSnackbar()
    const client = new AzureFunctionClient()

    const handleSubmit = async () => {
        setIsLoading(true)
        setErrors([])

        try {
            // Build the update requests array
            const updateRequests = selectedActionIds.map(actionId => ({
                externalId: actionId,
                activeUserEmail: activeUser.email,
                reportingSiteId: siteId,
                ...buildUpdateFields(formData),
            }))
            console.log('Update requests:', updateRequests)
            const response = await client.updateMultipleActions(updateRequests)

            // Call the bulk update API
            // const response = await client.updateMultipleActionsBatch(updateRequests, 25)

            if (response.errors && Object.keys(response.errors).length > 0) {
                const errorMessages = Object.entries(response.errors).map(
                    ([actionId, error]: [string, any]) =>
                        `${actionId}: ${error.internal?.join(', ') || 'Unknown error'}`
                )
                setErrors(errorMessages)
                
                showSnackbar(
                    translate('bulkEditActionItems.partialSuccess'),
                    'warning'
                )
            } else {
                showSnackbar(
                    translate('bulkEditActionItems.successMessage'),
                    'success'
                )
                onClose(true)
            }
        } catch (error) {
            console.error('Error updating actions:', error)
            showSnackbar(translate('bulkEditActionItems.errorMessage'), 'error')
            setErrors([error instanceof Error ? error.message : 'Unknown error occurred'])
        } finally {
            setIsLoading(false)
        }
    }

    const buildUpdateFields = (data: BulkEditFormData) => {
        const fields: any = {}

        if (data.title !== undefined) {
            fields.title = data.title
        }
        if (data.description !== undefined) {
            fields.description = data.description
        }
        if (data.dueDate !== undefined) {
            fields.dueDate = data.dueDate
        }
        if (data.assignedToId !== undefined) {
            fields.assignedToId = "UserAzureAttribute_" + data.assignedToId
        }
        if (data.approverId !== undefined) {
            fields.approverId = data.approverId
        }
        if (data.verifierId !== undefined) {
            fields.verifierId = data.verifierId
        }
        if (data.priorityId !== undefined) {
            fields.priorityId = data.priorityId
        }
        if (data.categoryId !== undefined) {
            fields.categoryId = data.categoryId
        }
        if (data.subCategoryId !== undefined) {
            fields.subCategoryId = data.subCategoryId
        }
        if (data.reportingUnitId !== undefined) {
            fields.reportingUnitId = data.reportingUnitId
        }
        if (data.reportingLocationId !== undefined) {
            fields.reportingLocationId = data.reportingLocationId
        }

        return fields
    }

    const hasChanges = Object.keys(formData).some(key => formData[key as keyof BulkEditFormData] !== undefined)

    return (
        <Dialog
            open={true}
            onClose={() => onClose(false)}
            maxWidth="md"
            fullWidth
            PaperProps={{
                sx: {
                    minHeight: '60vh',
                    maxHeight: '90vh',
                }
            }}
        >
            <DialogTitle>
                <Typography variant="h6" component="div">
                    {translate('bulkEditActionItems.modalTitle')}
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                    {translate('bulkEditActionItems.modalSubtitle')+` (${selectedActionIds.length} ${translate('bulkEditActionItems.itemsSelected')})`}
                </Typography>
            </DialogTitle>

            <DialogContent dividers>
                <Box sx={{ mb: 2 }}>
                    <Typography variant="subtitle2" gutterBottom>
                        {translate('bulkEditActionItems.selectedActions')}:
                    </Typography>
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, maxHeight: '100px', overflow: 'auto' }}>
                        {selectedActionIds.map(actionId => (
                            <Chip
                                key={actionId}
                                label={actionId}
                                size="small"
                                variant="outlined"
                            />
                        ))}
                    </Box>
                </Box>

                <Divider sx={{ my: 2 }} />

                <Typography variant="subtitle2" gutterBottom>
                    {translate('bulkEditActionItems.fieldsToUpdate')}:
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                    {translate('bulkEditActionItems.fieldsInstruction')}
                </Typography>

                <BulkEditForm
                    formData={formData}
                    setFormData={setFormData}
                    activeUser={activeUser}
                    siteId={siteId}
                />
                {errors.length > 0 && (
                    <Alert severity="error" sx={{ mt: 2 }}>
                        <Typography variant="subtitle2" gutterBottom>
                            {translate('bulkEditActionItems.errorsOccurred')}:
                        </Typography>
                        <ul style={{ margin: 0, paddingLeft: '20px' }}>
                            {errors.map((error, index) => (
                                <li key={index}>
                                    <Typography variant="body2">{error}</Typography>
                                </li>
                            ))}
                        </ul>
                    </Alert>
                )}
            </DialogContent>

            <DialogActions sx={{ p: 2 }}>
                <ClnButton
                    variant="text"
                    color="primary"
                    onClick={() => onClose(false)}
                    disabled={isLoading}
                    label={translate('stepper.form.cancel')}
                />
                <ClnButton
                    variant="contained"
                    color="primary"
                    onClick={handleSubmit}
                    disabled={isLoading || !hasChanges}
                    startIcon={isLoading ? <CircularProgress size={20} /> : undefined}
                    label={isLoading
                        ? translate('bulkEditActionItems.updating')
                        : translate('bulkEditActionItems.updateActions')
                    }
                />
            </DialogActions>
        </Dialog>
    )
}
