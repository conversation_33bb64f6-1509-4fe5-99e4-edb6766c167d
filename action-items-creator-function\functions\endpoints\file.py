import json
import logging
import os
import sys
from http import HTT<PERSON>tatus
from typing import List

import azure.functions as func

from clients.core.constants import APPLICATION_JSON
from clients.files.requests import GetFilesDownloadUrlsRequest, UploadFileRequest
from infra.action_item_client_factory import ActionItemClientFactory
from services.file_service import FileService

bp = func.Blueprint()

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "..", "..")))


@bp.function_name(name="UploadFiles")
@bp.route("upload-files", methods=["post"], auth_level=func.AuthLevel.ANONYMOUS)
def upload_files(req: func.HttpRequest) -> func.HttpResponse:
    logging.info("Function UploadFiles started")
    try:
        auth_header = req.headers.get("Authorization")
        if auth_header and auth_header.startswith("Bearer "):
            token_request = auth_header[len("Bearer ") :]
        else:
            token_request = auth_header

        uploaded_files = req.files.getlist("files")
        metadata_list = req.form.getlist("metadata")

        validated_files: List[UploadFileRequest] = []
        for file, metadata in zip(uploaded_files, metadata_list):
            metadata_dict = json.loads(metadata)

            file_data = {
                "content": file.stream.read(),
                **metadata_dict,
                "metadata": {
                    "fileSize": metadata_dict.get("fileSize", ""),
                    "user": metadata_dict.get("user", ""),
                },
            }

            validated_file = UploadFileRequest.model_validate(file_data)
            validated_files.append(validated_file)

        logging.info("Function UploadFiles started")

        external_ids = FileService(
            ActionItemClientFactory.retriever(override_token=token_request),
        ).upload_files(validated_files)

        logging.info("UploadFiles executed successfully")

        return func.HttpResponse(
            json.dumps({"externalIds": external_ids}),
            status_code=(
                HTTPStatus.OK if external_ids else HTTPStatus.INTERNAL_SERVER_ERROR
            ),
            mimetype=APPLICATION_JSON,
        )
    except Exception as e:
        logging.error(f"Error in UploadFiles: {e}", exc_info=True)
        return func.HttpResponse(
            f"Error: {e}",
            status_code=HTTPStatus.INTERNAL_SERVER_ERROR,
            mimetype=APPLICATION_JSON,
        )


@bp.function_name(name="GetFilesDownloadUrls")
@bp.route(
    "get-files-download-urls",
    methods=["get"],
    auth_level=func.AuthLevel.ANONYMOUS,
)
def get_files_download_urls(req: func.HttpRequest) -> func.HttpResponse:
    logging.info("Function GetFilesDownloadUrls started")
    try:
        auth_header = req.headers.get("Authorization")
        if auth_header and auth_header.startswith("Bearer "):
            token_request = auth_header[len("Bearer ") :]
        else:
            token_request = auth_header

        file_request_param = req.params.get("fileRequest", "{}")
        file_request = json.loads(file_request_param)

        request = GetFilesDownloadUrlsRequest.model_validate(file_request)

        logging.info("Function GetFilesDownloadUrls started")

        urls = FileService(
            ActionItemClientFactory.retriever(override_token=token_request),
        ).get_files_download_urls(request)

        logging.info("GetFilesDownloadUrls executed successfully")

        return func.HttpResponse(
            json.dumps({"urls": urls}),
            status_code=HTTPStatus.OK,
            mimetype=APPLICATION_JSON,
        )
    except Exception as e:
        logging.error(f"Error in GetFilesDownloadUrls: {e}", exc_info=True)
        return func.HttpResponse(
            f"Error: {e}",
            status_code=HTTPStatus.INTERNAL_SERVER_ERROR,
            mimetype=APPLICATION_JSON,
        )
