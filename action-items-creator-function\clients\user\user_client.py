import json as json
from typing import Optional

from clients.actions._models import _User
from clients.core.filters import eq_filter
from clients.core.models import PaginatedData, ServiceParams
from clients.user.queries import GET_USER_BY_ID_QUERY
from clients.user.requests import GetUserByIdRequest


class UserClient:
    def __init__(self, params: ServiceParams):
        self._cognite_client = params.cognite_client
        self._data_model_id = params.data_model.as_id()

    def get_user_by_id(self, request: GetUserByIdRequest) -> Optional[_User]:
        filters = [eq_filter("externalId", request.external_id)]

        variables = {"filter": {"and": filters}}

        try:
            result = self._cognite_client.data_modeling.graphql.query(
                id=self._data_model_id,
                query=GET_USER_BY_ID_QUERY,
                variables=variables,
            )

            return (
                PaginatedData[_User]
                .from_graphql_response(result, 1)
                .first_or_default()
            )
        except Exception:
            return None
