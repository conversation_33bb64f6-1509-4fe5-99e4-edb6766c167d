from typing import Any

from cognite.client.data_classes import data_modeling

from clients.core.constants import DataSpaceEnum
from clients.core.filters import (
    eq_filter,
    equals_sdk_filter,
    in_filter,
    in_sdk_filter,
    nested_sdk_filter,
    prefix_filter,
)
from clients.user_complement.requests import (
    GetUserRolesAndTeamsRequest,
)


def get_not_equals_filter(
    property_name: str,
    node: dict[str, str],
    view: data_modeling.ViewId,
) -> data_modeling.filters.Not:
    """
    Generate a 'Not' filter to check for inequality.

    Args:
        property_name (str): The property name to apply the filter.
        node (dict[str, str]): The node containing filter details.
        view (data_modeling.ViewId): The view ID for data filtering.

    Returns:
        data_modeling.filters.Not: A 'Not' filter with the inequality condition.

    """
    return data_modeling.filters.Not(
        equals_sdk_filter(
            property_name,
            node,
            view,
        ),
    )


def get_nested_in_filter(
    property_table: str,
    property_filter: str,
    external_ids: list[str],
    view: data_modeling.ViewId,
) -> data_modeling.filters.Nested:
    """
    Generate a 'Nested' filter for checking if a value exists in a list.

    Args:
        property_table (str): The table property to apply the filter.
        property_filter (str): The property to filter the nested values.
        external_ids (list[str]): The list of external IDs for the filter.
        view (data_modeling.ViewId): The view ID for filtering.

    Returns:
        data_modeling.filters.Nested: A 'Nested' filter with the condition.

    """
    return nested_sdk_filter(
        property_name=property_table,
        sdk_filter=in_sdk_filter(
            property_filter,
            external_ids,
            view,
        ),
        view=view,
    )


def get_user_complements_by_units_filters(
    user_complements_ids: list[str],
) -> dict[str, Any]:
    """
    Create filters for user complements based on unit IDs.

    Args:
        user_complements_ids (list[str]): The list of user complement IDs.

    Returns:
        dict: A dictionary containing the filters for the user complements.

    """
    filters: list[Any] = [
        in_filter("externalId", user_complements_ids),
        {"userAzureAttribute": {"user": eq_filter("active", value=True)}},
    ]

    return {"and": filters}


def get_user_complement_by_email_filters(
    request: GetUserRolesAndTeamsRequest,
) -> dict[str, Any]:
    """
    Create filters for user complements based on email.

    Args:
        request (GetUserRolesAndTeamsRequest): The request object containing the user's email.

    Returns:
        dict: A dictionary containing the filters for the user complement by email.

    """
    filters: list[Any] = [
        {"userAzureAttribute": {"user": eq_filter("email", request.email)}},
        {"userAzureAttribute": {"user": eq_filter("active", value=True)}},
    ]

    return {"and": filters}


def get_user_complement_role_filters(
    request: GetUserRolesAndTeamsRequest,
) -> dict[str, Any]:
    """
    Create filters for user complements based on role.

    Args:
        request (GetUserRolesAndTeamsRequest): The request object containing the user's role and reporting site.

    Returns:
        dict: A dictionary containing the filters for the user complement role.

    """
    filters: list[Any] = [
        {"role": {"roleCategory": prefix_filter("externalId", "RoleSite")}},
    ]

    if request.reporting_site_external_ids:
        filters.append(
            {
                "reportingSite": in_filter(
                    "externalId",
                    request.reporting_site_external_ids,
                ),
            },
        )

    return {"and": filters}


def get_user_complement_team_filters(
    request: GetUserRolesAndTeamsRequest,
) -> dict[str, Any]:
    """
    Create filters for user complements based on team.

    Args:
        request (GetUserRolesAndTeamsRequest): The request object containing the user's team and reporting site.

    Returns:
        dict: A dictionary containing the filters for the user complement team.

    """
    filters: list[Any] = [eq_filter("space", DataSpaceEnum.UMG_DATA_SPACE)]

    if request.reporting_site_external_ids:
        filters.append(
            {
                "reportingSite": in_filter(
                    "externalId",
                    request.reporting_site_external_ids,
                ),
            },
        )

    return {"and": filters}


def get_reporting_site_filters() -> dict[str, Any]:
    """
    Create filters for reporting sites.

    Returns:
        dict: A dictionary containing the filters for reporting sites.

    """
    filters: list[Any] = [eq_filter("isActive", value=True)]

    return {"and": filters}


def get_user_complement_filters(request: GetUserRolesAndTeamsRequest) -> dict[str, Any]:
    """
    Create filters for user complements based on the request's data.

    Args:
        request (GetUserRolesAndTeamsRequest): The request object containing the user's filters.

    Returns:
        dict: A dictionary containing filters for the user complement, role, and team.

    """
    if request.reporting_site_external_ids:
        return {
            "userFilter": get_user_complement_by_email_filters(request),
            "roleFilter": get_user_complement_role_filters(request),
            "teamFilter": get_user_complement_team_filters(request),
        }
    return {
        "userFilter": get_user_complement_by_email_filters(request),
        "roleFilter": get_user_complement_role_filters(request),
    }
