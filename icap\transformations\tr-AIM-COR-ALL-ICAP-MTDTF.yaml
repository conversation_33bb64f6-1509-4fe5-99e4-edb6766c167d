# Manifest file downloaded from fusion
externalId: tr-AIM-COR-ALL-ICAP-MTDTF
name: AIM-COR-ALL-ICAP-MTDTF
query: >-
  WITH all_moc_action AS (
    SELECT 
    	stg_action.key AS ActionItemID,
    	aim_action.externalId,
    	aim_action.space,
    	icap_moc_action.ActivityTypeID,
    	icap_moc_action.MOCActionNumber,
    	ROW_NUMBER() OVER(
    		PARTITION BY stg_action.key 
    		ORDER BY icap_moc_action.MOCActionNumber ASC
    	) AS row_num
    FROM `AIM-COR`.`ICAP-STG-Action` stg_action
    INNER JOIN cdf_data_models("AIM-COR-ALL-DMD", "ActionItemManagementDOM", "6_0_0", "Action") AS aim_action 
    	ON aim_action.externalId = stg_action.externalId
    	AND  aim_action.space = stg_action.space
    INNER JOIN `ICAP-COR`.`MOC-tblMOCActionItem` icap_moc_action
    	ON CAST(icap_moc_action.ActionItemID AS STRING) = stg_action.key
    INNER JOIN `ICAP-COR`.`MOC-tblMOC` icap_moc
    	ON icap_moc.MOCID = icap_moc_action.MOCID
    	AND icap_moc.EventID = split_part(stg_action.objectType, "-", 4)
    WHERE stg_action.sourceType.externalId = 'AST-ICAP-ICAPMOCReport'
  ),

  moc_action AS (
    SELECT *
    FROM all_moc_action
    WHERE row_num = 1
  ),

  metadata_field AS (
    SELECT
    	moc_action.space,
      node_reference(moc_action.space, moc_action.externalId) AS action,
      node_reference('AIM-COR-ALL-REF', 'MTDTFT-activityType') AS metadataType,
    	icap_activity_type.ActivityTypeName AS value
    FROM moc_action
    INNER JOIN `ICAP-COR`.`ACT-tblActivityType` icap_activity_type
    	ON icap_activity_type.key = moc_action.ActivityTypeID
    LEFT JOIN cdf_data_models("AIM-COR-ALL-DMD", "ActionItemManagementDOM", "6_0_0", "MetadataField") aim_metadata_field
    	ON aim_metadata_field.action.externalId = moc_action.externalId
    	AND aim_metadata_field.action.space = moc_action.space
    	AND aim_metadata_field.metadataType.externalId = 'MTDTFT-activityType'
    WHERE aim_metadata_field.externalId IS NULL

    UNION

    SELECT
    	moc_action.space,
      node_reference(moc_action.space, moc_action.externalId) AS action,
      node_reference('AIM-COR-ALL-REF', 'MTDTFT-mocActionId') AS metadataType,
    	moc_action.MOCActionNumber AS value
    FROM moc_action
    LEFT JOIN cdf_data_models("AIM-COR-ALL-DMD", "ActionItemManagementDOM", "6_0_0", "MetadataField") aim_metadata_field
    	ON aim_metadata_field.action.externalId = moc_action.externalId
    	AND aim_metadata_field.action.space = moc_action.space
    	AND aim_metadata_field.metadataType.externalId = 'MTDTFT-mocActionId'
    WHERE aim_metadata_field.externalId IS NULL
  )


  SELECT 
      concat(
          'MTDTF-',
          split(split(current_timestamp(), ' ') [0], '-') [0],
          split(split(current_timestamp(), ' ') [0], '-') [1],
          split(split(current_timestamp(), ' ') [0], '-') [2],
          split(split(current_timestamp(), ' ') [1], ':') [0],
          split(split(current_timestamp(), ' ') [1], ':') [1],
          '-',
          ROW_NUMBER() OVER(
            ORDER BY 
              metadata_field.action.space, 
              metadata_field.action.externalId, 
              metadata_field.metadataType.externalId
          )
      ) AS externalId,
    	metadata_field.*
  FROM metadata_field
destination:
  dataModel:
    space: AIM-COR-ALL-DMD
    externalId: ActionItemManagementDOM
    version: "6_0_0"
    destinationType: MetadataField
  type: instances
ignoreNullFields: true
shared: true
action: upsert
dataSetExternalId: AIM-COR-ALL-DAT
# Specify credentials separately like this:
# authentication:
#   read:
#     clientId: ${READ_CLIENT_ID}
#     clientSecret: ${READ_CLIENT_SECRET}
#     tokenUrl: ${READ_TOKEN_URL}
#     cdfProjectName: ${READ_CDF_PROJECT_NAME}
#     # Optional: If idP requires providing the scopes
#     scopes:
#       - ${READ_SCOPES}
#     # Optional: If idP requires providing the audience
#     audience: ${READ_CDF_AUDIENCE}
#   write:
#     clientId: ${WRITE_CLIENT_ID}
#     clientSecret: ${WRITE_CLIENT_SECRET}
#     tokenUrl: ${WRITE_TOKEN_URL}
#     cdfProjectName: ${WRITE_CDF_PROJECT_NAME}
#     # Optional: If idP requires providing the scopes
#     scopes:
#       - ${WRITE_SCOPES}
#     # Optional: If idP requires providing the audience
#     audience: ${WRITE_CDF_AUDIENCE}
# Or together like this:
# authentication:
#   clientId: ${CLIENT_ID}
#   clientSecret: ${CLIENT_SECRET}
#   tokenUrl: ${TOKEN_URL}
#   # Optional: If idP requires providing the scopes
#   cdfProjectName: ${CDF_PROJECT_NAME}
#   scopes:
#    - ${SCOPES}
#   # Optional: If idP requires providing the audience
#   audience: ${AUDIENCE}