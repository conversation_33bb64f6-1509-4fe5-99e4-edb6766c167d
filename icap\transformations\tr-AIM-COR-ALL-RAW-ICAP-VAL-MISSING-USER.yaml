# Manifest file downloaded from fusion
externalId: tr-AIM-COR-ALL-RAW-ICAP-VAL-MISSING-USER
name: AIM-COR-ALL-RAW-ICAP-VAL-MISSING-USER
query: >-
  with user_management as (
      SELECT
          lower(externalId) as external_id,
          lower(replace(split(externalId, "@") [0], "_", ".")) as external_id_prefix
      FROM
          cdf_data_models(
              'UMG-COR-ALL-DMD',
              'UserManagementDOM',
              '8_7_2',
              'User'
          )
  ),

  missing_users as (
      SELECT
          key,
          lower(`Email`) as email,
          lower(replace(split(`Email`, "@") [0], "_", ".")) as email_prefix,
          `FirstName` as first_name,
          `LastName` as last_name
      FROM
          `ICAP-COR`.`USR-tblUser`
      WHERE
          key NOT IN (
              SELECT
                  key
              from
                  `AIM-COR`.`ICAP-MAP-User`
          )
  ),

  missing_events as (
      SELECT
          event.`key` as key,
          event.`EventAddedOn` as timestamp,
          missing_users.`email` as user_email,
          site.`ah_reporting_site` as reporting_site
      FROM
          `ICAP-COR`.`EVNT-tblEvent` as event
          JOIN `AIM-COR`.`ICAP-MAP-ReportingSite` as site on site.`key` = event.`SiteID`
          JOIN missing_users on event.`EventAddedByOwner` = missing_users.`key`
      union
      SELECT
          event.`key` as key,
          event.`EventAddedOn` as timestamp,
          missing_users.`email` as user_email,
          site.`ah_reporting_site` as reporting_site
      FROM
          `ICAP-COR`.`EVNT-tblEvent` as event
          JOIN `AIM-COR`.`ICAP-MAP-ReportingSite` as site on site.`key` = event.`SiteID`
          JOIN `ICAP-COR`.`EVNT-tblEventSecondaryOwners` as event_sec on event_sec.`EventID` = event.`key`
          JOIN missing_users on event_sec.`OwnerID` = missing_users.`key`
  ),

  missing_actions as (
      SELECT
          action.`key` as key,
          action.`ActionAddedOn` as timestamp,
          missing_users.`email` as user_email,
          site.`ah_reporting_site` as reporting_site
      FROM
          `ICAP-COR`.`AXN-tblActionItem` as action
          JOIN `ICAP-COR`.`EVNT-tblEvent` as event on action.`EventID` = event.`EventID`
          JOIN `AIM-COR`.`ICAP-MAP-ReportingSite` as site on site.`key` = event.`SiteID`
          JOIN missing_users on action.`PPRID` = missing_users.`key`
      union
      SELECT
          action.`key` as key,
          action.`ActionAddedOn` as timestamp,
          missing_users.`email` as user_email,
          site.`ah_reporting_site` as reporting_site
      FROM
          `ICAP-COR`.`AXN-tblActionItem` as action
          JOIN `ICAP-COR`.`EVNT-tblEvent` as event on action.`EventID` = event.`EventID`
          JOIN `AIM-COR`.`ICAP-MAP-ReportingSite` as site on site.`key` = event.`SiteID`
          JOIN missing_users on action.`SecondaryPPRID` = missing_users.`key`
  )

  select
      *
  from
      (
          SELECT
              missing_users.email as key,
              missing_users.email as email,
              max(missing_users.first_name) as first_name,
              max(missing_users.last_name) as last_name,
              min(user_management.external_id) as possible_match,
              min(missing_actions.reporting_site) as reporting_site,
              count(missing_actions.key) as count_actions,
              count(missing_events.key) as count_events,
              max(missing_actions.timestamp) as last_action_timestamp,
              max(missing_events.timestamp) as last_event_timestamp,
              unix_timestamp() as ingestion_date
          FROM
              missing_users
              LEFT JOIN missing_actions ON missing_users.email = missing_actions.user_email
              LEFT JOIN missing_events ON missing_users.email = missing_events.user_email
              LEFT JOIN user_management on user_management.external_id_prefix = missing_users.email_prefix
          group by
              missing_users.email
      ) as main
  where
      main.count_actions > 0
      or main.count_events > 0
  order by
      main.key
destination:
  database: AIM-COR
  table: ICAP-VAL-MissingUser
  type: raw
ignoreNullFields: true
shared: true
action: upsert
dataSetExternalId: AIM-COR-ALL-DAT
# Specify credentials separately like this:
# authentication:
#   read:
#     clientId: ${READ_CLIENT_ID}
#     clientSecret: ${READ_CLIENT_SECRET}
#     tokenUrl: ${READ_TOKEN_URL}
#     cdfProjectName: ${READ_CDF_PROJECT_NAME}
#     # Optional: If idP requires providing the scopes
#     scopes:
#       - ${READ_SCOPES}
#     # Optional: If idP requires providing the audience
#     audience: ${READ_CDF_AUDIENCE}
#   write:
#     clientId: ${WRITE_CLIENT_ID}
#     clientSecret: ${WRITE_CLIENT_SECRET}
#     tokenUrl: ${WRITE_TOKEN_URL}
#     cdfProjectName: ${WRITE_CDF_PROJECT_NAME}
#     # Optional: If idP requires providing the scopes
#     scopes:
#       - ${WRITE_SCOPES}
#     # Optional: If idP requires providing the audience
#     audience: ${WRITE_CDF_AUDIENCE}
# Or together like this:
# authentication:
#   clientId: ${CLIENT_ID}
#   clientSecret: ${CLIENT_SECRET}
#   tokenUrl: ${TOKEN_URL}
#   # Optional: If idP requires providing the scopes
#   cdfProjectName: ${CDF_PROJECT_NAME}
#   scopes:
#    - ${SCOPES}
#   # Optional: If idP requires providing the audience
#   audience: ${AUDIENCE}