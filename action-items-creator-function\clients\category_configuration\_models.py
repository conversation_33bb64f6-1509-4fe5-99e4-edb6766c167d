from typing import Annotated, Optional

from clients.core.models import Node
from clients.core.validators import edge_unwraper_validator, str_or_default_validator


class _User(Node):
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    email: Optional[str] = None


class _UserAzureAttribute(Node):
    user: Optional[_User] = None


class _UserComplement(Node):
    user_azure_attribute: _UserAzureAttribute | None
    reporting_units: Annotated[list[Node], edge_unwraper_validator] = []


class _UserRoleSites(Node):
    users_complements: Annotated[list[_UserComplement], edge_unwraper_validator] = []


class _Role(Node):
    user_role_sites: Annotated[list[_UserRoleSites], edge_unwraper_validator] = []
