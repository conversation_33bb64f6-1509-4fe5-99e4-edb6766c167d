'use client'
import { Box, Grid } from '@mui/material'
import { Attachment } from '@/app/common/models/action-detail'
import LinksComponent from '../../LinksComponent'
import { ClnCircularProgress, ClnPanel } from '@celanese/ui-lib'
import { ActionItemLink } from '@/app/common/models/link'
import { UploadFiles } from '../../UploadFiles/uploadFile'

type Props = {
    attachments?: Attachment[]
    links?: ActionItemLink[]
    canUploadFiles?: boolean
    onUploadFiles?: (files: File[]) => Promise<void>
    isLoading?: boolean
}

export function FilesAndLinksTab(params: Props) {
    const { attachments, links, canUploadFiles, onUploadFiles, isLoading } = params

    return (
        <Box id="action-details-file-and-link-tab" paddingTop={2}>
            <Grid container spacing={2} sx={{ height: '100%' }}>
                <Grid item md={6} sm={6} xs={12}>
                    <ClnPanel
                        id="action-id-panel"
                        sx={{
                            padding: '1rem',
                            display: 'flex',
                            flexDirection: 'column',
                            marginBottom: '1rem',
                            height: '100%',
                            overflowY: 'auto',
                        }}
                    >
                        {isLoading ? (
                            <Box
                                sx={{
                                    display: 'flex',
                                    justifyContent: 'center',
                                    alignItems: 'center',
                                    height: '100%',
                                }}
                            >
                                <ClnCircularProgress size={40} value={0} />
                            </Box>
                        ) : (
                            <UploadFiles
                                canUpload={canUploadFiles}
                                oldUploadedFiles={attachments ?? []}
                                newUploadedFiles={[]}
                                setNewUploadedFiles={async (files: File[]) => {
                                    await onUploadFiles?.(files)
                                }}
                                sxProps={{
                                    paddingTop: '1rem',
                                    height: '8rem',
                                    flexGrow: 1,
                                    overflow: 'auto',
                                }}
                            />
                        )}
                    </ClnPanel>
                </Grid>
                <Grid item md={6} sm={6} xs={12}>
                    <ClnPanel
                        id="action-id-panel"
                        sx={{
                            padding: '1rem',
                            display: 'flex',
                            flexDirection: 'column',
                            marginBottom: '1rem',
                            height: '100%',
                            overflowY: 'auto',
                        }}
                    >
                        <LinksComponent
                            links={links ?? []}
                            sxProps={{
                                paddingTop: '1rem',
                                display: 'flex',
                                overflow: 'auto',
                            }}
                        />
                    </ClnPanel>
                </Grid>
            </Grid>
        </Box>
    )
}

export default FilesAndLinksTab
