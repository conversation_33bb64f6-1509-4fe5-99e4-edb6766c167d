{"app": {"acronym": "AIM", "fullTitle": "Action Item Management", "boldTitle": "Action Item", "lightTitle": "Management", "navBar": {"notifications": "Notifications", "logOut": "Log out"}}, "pages": {"home": {"title": "Home", "details": "Action Details"}, "source-event": {"title": "Events", "details": "Event Details"}, "dashboards": {"title": "Dashboards"}, "adminSettings": {"title": "Admin"}}, "home": {"title": "Action Item Home", "showLocation": "Showing"}, "kpis": {"allActionItems": "All Action Items", "assignedToMe": "Assigned To Me", "assigned": "Related To Me", "pendingApprovals": "Pending Approvals", "pendingVerifications": "Pending Verifications", "overdue": "Overdue", "myApprovals": "My Approvals", "myVerifications": "My Verifications", "myChallenges": "My Challenges", "myExtends": "My Extends", "myReassignment": "My Reassignment", "headers": {"myActions": "My Actions", "myApprovalsAndVerifications": "My Approvals"}, "infoMessage": {"toDo": {"totalActions": "Showing all open action items", "assignedToMe": "Showing all open actions assigned to you", "relatedToMe": "Showing all actions where you are involved as Assignee, Owner, Approver, Verifier, Extension Approver, or Reassignment Approver", "pendingApprovals": "Showing all actions assigned to you with status 'Pending Approval'", "pendingVerifications": "Showing all actions assigned to you with status 'Pending Verification'", "overdue": "Showing all actions involving you with a past due date", "myApprovals": "Showing all actions where you are the Approver and status is 'Pending Approval'", "myVerifications": "Showing all actions where you are the Verifier and status is 'Pending Verification'", "myChallenges": "Showing all actions with status 'Challenge' where you are the Owner", "myExtends": "Showing all actions where you are the Approver of due date extension requests", "myReassignment": "Showing all actions where you are the Approver of reassignment requests"}, "closed": {"totalActions": "Showing all completed or cancelled action items", "assignedToMe": "Showing all completed or cancelled actions assigned to you"}}}, "dataGridPro": {"columns": "Columns", "manageColumn": "Manage columns", "hideColumns": "Hide columns", "hideAll": "Hide all", "show": "Show", "reset": "Reset", "pinToLeft": "<PERSON>n to left", "pinToRight": "Pin to right", "unpin": "Unpin", "sortAsc": "Sort by ASC", "sortDesc": "Sort by DESC"}, "table": {"tabs": {"todo": "To Do", "closed": "Closed"}, "headers": {"id": "ID", "title": "Title", "description": "Description", "unit": "Reporting Unit", "eventRecurringUnit": "Unit", "frequency": "Frequency", "impactedUnit": "Impacted Units", "impactedLocation": "Impacted Locations", "owner": "Owner", "select": "Select", "assignee": "Assignee", "approver": "Approver", "verifier": "Verifier", "category": "Category", "subcategoryOne": "Subcategory 1", "subcategoryTwo": "Subcategory 2", "ext": "EXT", "app": "Application", "businessLine": "Business Line", "assignmentDate": "Assignment Date", "startDate": "Start Date", "dueDate": "Due Date", "endDate": "End Date", "date": "Date", "beforeStartup": "Before Startup", "conclusionDate": "Conclusion Date", "verificationDate": "Verification Date", "approvalDate": "Approval Date", "manager": "Manager", "status": "Status", "occurence": "<PERSON><PERSON><PERSON><PERSON>", "sourceInformation": "Source Information", "sourceID": "Source ID", "dueDateTooltip": "The Due Date will always correspond to the Action Item Status. For example, if it's pending approval, the Due Date will align with the end date by which the approver needs to complete their review.", "checkboxTooltip": "This checkbox will only select items on the current page. To select more items, you can increase the number of rows per page or navigate to the next page.", "actions": "Actions", "startWith": "Start with", "users": "Users", "type": "Type", "assignments": "Assignments", "sourceEventTitle": "Source Event Title", "eventType": "Event Type", "private": "Private?", "primaryOwner": "Primary Owner", "numberOfActionsItems": "Number of Action Items", "numberOfActionsCompleted": "Number of Action Completed", "location": "Location", "overdue": "Overdue", "icapActionId": "ICAP Action ID", "priority": "Priority", "activityType": "Activity Type", "mOCActionId": "MOC Action Id", "checkbox": "Checkbox", "functionalLocation": "Functional Location", "equipment": "Equipment", "site": "Reporting Site"}, "filter": {"default": "DEFAULT", "clear": "CLEAR", "applyFilter": "APPLY FILTER", "writeHere": "Write here...", "all": "All", "infoMessage": "Please note that changing the filter will deselect any currently selected actions in the checkbox."}, "search": {"enterAtLeast3": "Enter at least 3 characters", "searchHelpMessage": "Enter Action ID, Title, or Source Information", "searchForTitleOrSourceInfo": "Enter Title or Source Information", "searchForTitleOrDescription": "Enter Title or Description", "searchForUsersOrAssignments": "Enter Users or Assignments", "defaultSearchHelpMessage": "Enter Action ID or Title", "searchForTitle": "Enter Title"}, "newActionItem": "New Action Item", "newRecurringItem": "New Recurring Item", "download": "Download", "export": "Export", "exporting": "Exporting", "newEvent": "New Event", "listView": "List View", "chartView": "Chart View"}, "dashboards": {"dashboard": "Dashboard", "tabs": {"site": "Site", "supervisor": "Supervisor"}, "emailTeam": {"emailTeam": "Email Team", "subject": "Action Item Required", "body": "Please review your action item at the following link:"}, "filters": {"filters": "Filters", "unit": "Unit", "reportingLocation": "Reporting location", "category": "Category", "subcategoryOne": "Subcategory 1", "subcategoryTwo": "Subcategory 2", "status": "Status", "all": "All", "updatedStatus": "Update Status", "employeeName": "Employee Name", "managerName": "Manager Name", "isPrivate": "Is Private", "sourceEventTitle": "Source Event Title"}, "kpis": {"totalEntered": "Total Entered", "overdue": "Overdue", "open": "Open", "pending": "Pending", "overduePercentage": "Overdue Percentage", "employees": "Employees", "taskWithin7Days": "Task Within 7 Days", "tasksWithinThisMonth": "Tasks Within This Month", "dueDateOver30Days": "Due Date Over 30 Days", "pendingApproval": "Pending Approval", "pendingVerification": "Pending Verification"}, "charts": {"byStatus": "By Status", "byUnit": "By Unit", "byCategory": "By Category", "actionItemsCategories": "Action Items Categories", "listOfEmployees": "List of Employees"}}, "stepper": {"next": "NEXT", "back": "Back", "submit": "SUBMIT", "save": "save", "skip": "<PERSON><PERSON>", "saveTemplate": "Save as Template", "form": {"linksPlural": "Links", "finished": "Action item created successfully", "requiredFieldsErro": "Please fill all required fields", "selectTemplate": "Select a template", "template": {"template": "Template", "templateName": "Template Name", "person": "Person", "role": "Role", "selectName": "Select Name", "namePerson": "Name Person", "selectRoles": "Select roles", "roles": "Roles"}, "or": "or", "createNew": "CREATE NEW", "general": "General", "title": "Title", "eventTitle": "Event title", "actionItemOwner": "Action item owner", "unit": "Unit", "primaryOwner": "Primary owner", "secondaryOwners": "Secondary owner(s)", "addViewers": "Add viewers", "addSecondaryOwners": "Add Secondary Owners", "businessLine": "Business Line", "reportingLocation": "Reporting location", "impactedUnit": "Impacted Units", "impactedLocation": "Impacted Locations", "reportingLine": "Reporting line", "noOptionsReportingLine": "You must select a reporting unit that have at least one reporting line associated", "description": "Description", "eventDescription": "Event description", "estimatedCost": "Estimated cost", "priceCurrencyKey": "Price currency key", "estimatedGrade": "Estimated grade", "isPlantShutdownRequired": "Is plant shutdown required?", "actionTaken": "Action taken", "adictional": "Adictional", "actions": "Actions", "cancel": "Cancel", "save": "Save", "yes": "Yes", "no": "No", "helperTextDescription": "*Minimum length of 10 and maximum of 1000 characters", "helperTextTitle": "*Minimum length of 10 and maximum of 200 characters", "helperTextModal": "*Minimum length of 10 and maximum of 300 characters", "helperTextApprovalModal": "*Minimum length of 10 characters", "helperTextSourceInfo": "Maximum length of 200 characters", "helperTextTemplateNameSaveTemplate": "• Minimum length of 1 and maximum of 100 characters", "helperTextAssignmentSaveTemplate": "• Both information entered for person and role will be saved", "addLinkModalTitle": "Add link", "addLinkSameUrlError": "Url already exists", "addLinkEmptyFieldsError": "Please fill all required fields", "typeAtLeast3": "Type at least 03 characters", "typeAtLeast1": "Type at least 01 character", "user": "User", "role": "Role", "team": "Team", "source": {"name": "Source", "reference": "Reference", "sourceInformation": "Source information", "category": "Category", "subcategory01": "Subcategory 1", "subcategory02": "Subcategory 2", "eventId": "Event ID (Automatically Populated)"}, "assignment": {"name": "Assignment", "priority": "Priority", "priorityHigh": "High", "priorityLow": "Low", "assignee": "Assignee", "selectUsers": "Select users or group of users", "selectUsersDesc": "You can click on each group to expand the emails and send the notification to people in the group separately.", "usersSearch": {"name": "User search", "previewAssignee": "Preview assignee", "advancedSearch": "Advanced Search", "addUsers": "Add Users", "teams": "Teams", "roles": "Roles", "units": "Units", "owners": "Owners", "selectedUsers": "Selected Users", "noUsers": "No users found for the selected filters", "reportingLocation": "Reporting location", "addAssigness": "Add Assigness"}, "manageSecondaryOwners": "Add and manage secondary owners", "clearAll": "Clear all", "taskType": "Task type", "taskTypeOneTime": "One-Time", "taskTypeRecurring": "Recurring", "uploadFile": "Upload file", "assignementDate": "Assignment date", "chooseDate": "Choose date", "dueDate": "Due Date", "dueDateRestrictionReason": "You cannot change the due date because of the category configuration.", "uploadTableName": "Name", "uploadTableType": "Type", "uploadTableActions": "Actions", "uploadText01": "Drag and drop here or", "uploadText02": "Click to upload", "uploadTypes": "All types are accepted", "createRecurrence": "Create recurrence", "frequence": "Frequence", "helperTextVerificationOfEddectiveness": "Maximum length of 500 characters", "recurrenceRequired": "Recurrence required", "recurrenceTypes": {"daily": "Daily", "weekly": "Weekly", "monthly": "Monthly", "quarterly": "Quarterly", "yearly": "Yearly", "custom": "Custom"}, "priorityOptions": {"high": "High", "medium": "Medium", "low": "Low"}, "recurrenceLabelDaily": "* Due date will be the same as assignment date", "recurrenceLabelWeekly": "* Due date will be each day selected plus 6", "recurrenceLabelMonthly": "* Due date will be end of each month (no matter the start date)", "recurrenceLabelQuarterly": "* Due date will be last day of the Quarter selected", "recurrenceLabelYearly": "* Due date will be the Assignment date plus 45 days", "recurrenceLabelCustom": "* Due date will be the Assignment date plus 30 days", "drawerOverLineMeta": "Action item management", "drawerCaption": "Recurrence <PERSON>", "draweStartDate": "Start date", "drawerEndDate": "End date", "drawerNoEndDate": "No end date", "draweRecurrenceType": "Type", "drawerRepeat": "Repeat", "editRecurrence": "Edit recurrence", "recurrence": "recurrence", "yearPickerYears": "Yearly,Biennial,Triennial,Quinquennial", "monthPickerMonths": "Jan,Feb,Mar,Apr,May,Jun,Jul,Aug,Sep,Oct,Nov,Dec", "monthPickerErrorMessage": "Invalid date for selected months", "yearlyPickerErrorMessage": "Invalid day for selected month", "dayOfMonth": "Day of month", "monthOfYear": "Month of year", "weekPickerWeek": "<PERSON>,<PERSON>,<PERSON><PERSON>,<PERSON><PERSON>,<PERSON><PERSON>,<PERSON><PERSON>,<PERSON><PERSON>", "customPickerSameDayError": "Selected date is already in the list", "customPickerDayOutOfRangeError": "Selected date have to be between the start and end date", "customPickerAddButton": "Add", "approvalAndVerification": "Approval and Verification", "approvalRequired": "Approval required", "approvalBy": "Approval by", "approvalDueDate": "Approval due date wil be {} days after close of action", "verificationDueDate": "Verification due date wil be {} days after close of action", "verificationRequired": "Verification required", "verifyBy": "Verify by", "verificationOfEffectiveness": "Verification of effectiveness", "evidenceRequired": "Evidence required", "weekdays": {"monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday", "sunday": "Sunday"}, "months": {"january": "January", "february": "February", "march": "March", "april": "April", "may": "May", "june": "June", "july": "July", "august": "August", "september": "September", "october": "October", "november": "November", "december": "December"}, "yearlyOptions": {"yearly": "Yearly", "biennially": "Biennially", "triennially": "Triennially", "quinquennially": "Quinquennially"}}, "functionalLocation": "Functional Location", "equipment": "Equipment", "reportingSite": "Reporting Site"}}, "details": {"tab": {"details": "Details", "filesAndLinks": "Files & Links"}, "fields": {"title": "Title", "unit": "Unit", "reportingLocation": "Reporting Location", "site": "Site", "owner": "Owner", "eventOwner": "Event Owner", "dueDate": "Due Date", "taskType": "Task Type", "assignee": "Assignee", "approvalRequired": "Approval Required", "approver": "Approver", "verificationRequired": "Verification Required", "conclusionDate": "Conclusion Date", "verificationDate": "Verification Date", "approvalDate": "Approval Date", "verifier": "Verifier", "category": "Category", "priority": "Priority", "subcategoryOne": "Subcategory 1", "subcategoryTwo": "Subcategory 2", "voeActionItem": "Verification of Effectiveness", "sourceInformation": "Source Information", "description": "Description", "assigneeComments": "As<PERSON><PERSON>", "businessLine": "Business Line", "subcategory1": "Subcategory 1", "subcategory2": "Subcategory 2", "secondaryOwners": "Secondary owner(s)", "impactedUnit": "Impacted Units", "impactedLocation": "Impacted Locations", "viewOnly": "View Only", "viewOnlyToolTip": "View-only actions created in an external application to enable users to view all action data on KPIs and dashboards.", "eventLink": "Event Link", "allDatesExpressed": "All dates expressed as follow: ", "functionalLocation": "Functional Location", "equipment": "Equipment"}, "components": {"link": "Link", "description": "Description", "uploadFiles": "Upload Files", "uploadFilesByAndOn": {"uploaded": "Uploaded", "by": "by", "on": "on"}, "uploadFilesSteps": {"actionItemEdition": "Action In Progress", "actionItemCreation": "Action Creation", "sourceEventInProgress": "Event In Progress", "sourceEventCreation": "Event Creation", "dueDateExtensionRequest": "Extension Request", "completeAction": "Completed Evidence", "historicalData": "Historical Data", "iCAP": "ICAP"}, "comments": {"expandEAddComments": "Expand & Add Comments", "noCommentsAvailable": "There are no comments available yet.", "historicalDataFrom": "Historical Data from", "createdVia": "Created via", "commentSent": "Comment sent", "by": "by", "addComment": "Add Comment", "actionItem": "Action Item", "fullComments": "Full comments are in the comment log", "addAComment": "Add a comment"}, "noLinks": "No links", "noFiles": "No files"}, "buttons": {"cancel": "Cancel", "extend": "Extend", "reassign": "Reassign", "challenge": "Challenge", "complete": "Complete", "answerRequest": "Answer Request"}}, "requestModal": {"actionItemManagement": "ACTION ITEM MANAGEMENT", "requestExtension": "Request Extension", "pendingApproval": "Pending Approval", "pendingVerification": "Pending Verification", "action": "Action", "title": "Title", "assignee": "Assignee", "dueDate": "Due Date", "challengeComments": "Challenge Comments", "extensionComments": "Extension Comments", "approvalComments": "Approve or Reject Comments", "textHelperComments": "*Minimum length of 10 and maximum of 300 characters", "newDueDate": "New Due Date", "save": "Save", "cancel": "Cancel", "reject": "Reject", "approve": "Approve", "leave": "Leave", "close": "Close", "complete": "Complete", "completeEvent": "Complete Event", "noteDueDateExtension": "Note: If Due Date Extension is requested and requires an attached form, it must be completed and attached before approval of the request is granted. (See Action Item Details screen for this item for attachments)", "newAssignee": "New Assignee", "reassignComments": "Reassign Comments", "approverComments": "Approver Comments", "requestReassign": "Request Reassigment", "requestChallenge": "Request Challenge", "comments": "Comments", "unitManagerNotFound": "Unit Manager not found", "usersToApproverNotFound": "Users to approve not found", "siteQualityManagerNotFound": "Site Quality Manager not found", "Assignee": "Assignee", "delete": "Delete", "deleteRecurringQuestion": "Are you sure you want to delete this Recurring Configuration?", "deleteTemplateQuestion": "Are you sure you want to delete this Template?", "deleteQuestion": "Are you sure you want to delete this action item? Actions deleted will no longer be accessible to any users.", "cancelQuestion": "Are you sure you want to cancel this action item? Cancelled actions will be available with the 'Cancelled' status.", "closeQuestion": "Are you sure you want to leave? All entered information will be lost.", "cancelEventQuestion": "Do you want to cancel this event?", "cancelEventAndActionsQuestion": "Do you want to cancel this event and actions?", "applyFilterOrSearchQuestion": "Are you sure you want to proceed?", "applyFilterOrSearchSubtitle": "If you filter/search again, any previously deselected actions will be selected.", "cancelEventSubtitle": "This action cannot be undone.", "selectActionsToCancel": "Select actions to cancel", "onlyCancelEvent": "Only Cancel Event", "cancelSelectedActions": "Cancel Selected Actions", "deleteSelectedActions": "Delete Selected Actions", "selectedActions": "Selected Actions", "createFromTemplate": "Create from template", "completeTheAction": "Complete the Action", "completeAction": "Complete Action", "deleteActionItem": "Delete Action Item", "cancelActionItem": "Cancel Action Item", "deleteAction": "Delete Action", "cancelAction": "Cancel Action", "deleteRecurring": "Delete Recurring Configuration", "deleteTemplate": "Delete Template", "editAction": "Edit Action Item", "uploadFilesEvidenceRequired": "UPLOAD FILES (EVIDENCE REQUIRED)", "uploadFiles": "UPLOAD FILES", "assigneeComments": "ASSIGNEE COMMENTS *", "completeEventText": "This action cannot be undone. Are you sure you want to change the status to Completed?", "confirm": "Confirm", "confirmUpload": "Confirm Upload?", "uploadFile": "Upload File?", "uploadFileText": "You are uploading {} file(s):", "event": {"recurringActionSavedTitle": "Recurring Action saved!", "recurringActionSavedText": "Recurring Action Saved! Since you selected the recurring option, the configuration has been saved. However, the resulting actions will depend on the frequency you set."}, "history": {"history": "History", "when": "When", "actions": "Actions", "change": "Change", "expand": "Expand", "to": "To", "createdBy": "Created By", "assignedTo": "Assignee To", "extensionBy": "Extension By", "extensionRejected": "Extension Rejected By", "extensionApprove": "Extension Approved By", "reassignBy": "Reassign By", "reassignRejected": "Reassign Rejected By", "reassignApprove": "Reassign Approved By", "challengeBy": "Challenge By", "challengeRejected": "Challenge Rejected By", "challengeApprove": "Challenge Approved By", "completedBy": "Completed By", "approvalRejected": "Approval Rejected By", "approvedBy": "Approved By", "verificationRejected": "Verification Rejected By", "verifyBy": "Verify By", "editedBy": "Edited By", "deletedBy": "Deleted By", "description": "Description", "cancelledBy": "Cancelled By"}, "cancelActionsInBulkQuestion": "Cancel {} action item(s)?", "deleteActionsInBulkQuestion": "Delete {} action item(s)?", "cancelActionsInBulkSubtitle": "Are you sure you want to cancel {} action item(s)?", "deleteActionsInBulkSubtitle": "Are you sure you want to delete {} action item(s)?", "fromAssignee": "Assignee", "fromDueDate": "Due Date"}, "alerts": {"cancelDisclaimerTitle": "Cancel operation", "changesWillBeLost": "Changes will be lost. Do you want to continue?", "dataSavedWithSuccess": "Data was saved with success", "templateSavedWithSuccess": "Template was saved with success", "actionsSavedWithSuccess": "Actions was saved with success", "saveDisclaimer": "Are you sure?", "saveDisclaimerTitle": "Save Changes", "unexpectedErrorOcurred": "Unexpected error occurred", "defaultSuccess": "Success", "errorOcurred": "Data already exists", "successExport": "Your data has been successfully exported!", "errorExport": "An error occurred while exporting data, please try again later", "alertPrivateForm": "Private actions cannot have recurrent.", "alertPrivateFormFile": "Private event/action cannot have files.", "noAuthorizedAccess": "No Authorized Access", "noAccess": "You don't have access to this content.", "redirect": "You can request access by clicking", "here": "here.", "loading": "Loading access...", "redirectToPage": "Redirect to page", "actionsOriginatedFromKPI": "The actions shown have originated from {} KPI", "errorCreatingNotification": "Error creating notification"}, "edit": {"pageTitle": "Edit Action Item", "fields": {"title": "Action Item Title", "unit": "Unit", "reportingLocation": "Reporting Location", "site": "Site", "owner": "Owner", "dueDate": "Due Date", "taskType": "Task Type", "assignee": "Assignee", "approver": "Approver", "verifier": "Verifier", "category": "Category", "subcategoryOne": "Subcategory 1", "subcategoryTwo": "Subcategory 2", "sourceInformation": "Source Information", "description": "Description", "assigneeComments": "As<PERSON><PERSON>"}, "components": {"link": "Link", "description": "Description", "action": "Action", "uploadFiles": "Upload Files"}}, "source-event": {"title": "Events Home", "changeEvent": "Do you need to create event?", "search": "Search for the Event", "create": "Create new event", "home": "Back to events", "actions": "Actions Itens", "save": "save & back to home", "details": "Details", "action-items": "Action Items", "recurring": "Recurring", "cancelEvent": "Cancel Event", "completeEvent": "Complete Event", "checkBoxPrivate": "<PERSON> as private", "privateSettingsButton": "Private event settings", "privateSettingsTitle": "Private Event Settings", "addAndManageSecondaryOwners": "+ Add And Manage Secondary Owners", "deleteAllEventViewers": "Delete All Event Viewers", "deleteAllEventSecondaryOwners": "Delete All Secondary Owners", "tooltipPrivateText": "A Private Event will only be visible for the Primary and Secondary owners of the Event and for the users, teams or roles defined in Viewers configuration. In summary: ", "tooltipPrivateList1": "Owners and Viewers can see details of the event and uploaded documents.", "tooltipPrivateList2": "All actions created from a private event will also be private.", "tooltipPrivateList3": "Assignees from those actions will only be able to see the details of their own actions.", "tooltipPrivateList4": "All other users will not see Events/Actions data listed on the tables.", "eventsCount": "All events", "events": "events", "recurringTab": {"id": "ID", "title": "Title", "unit": "Unit", "category": "Category", "startDate": "Start Date", "frequency": "Frequency", "updateBy": "Update By", "endDate": "End Date", "status": "Status", "owner": "Owner", "active": "Active", "inactive": "Inactive", "saveChanges": "Save Changes", "recurring": "Recurring"}}, "new-action-item": {"title": "Events", "selectSite": "Select reporting site", "reportingSite": "Reporting site", "description": "Select your action", "titleCard": "Create from", "events": "Events", "incident": "Incident", "template": "Template", "scratch": "<PERSON><PERSON><PERSON>", "back": "Back to home"}, "supportFeature": {"incident": "Incident", "improvement": "Improvement", "successMessage": "Thank you for your request. Our experts are analyzing it and will reach out to you soon. If you need to submit another request,", "newRequestLink": "click here", "successMessageEnd": "to open a new ticket.", "ticketNumber": "ticket number", "checkTicketInfo": "ticket information", "copy": "algo sobre copia", "ticketNumberCopied": "Ticket number copied", "dragAndDropHere": "Drag and drop here", "acceptFiles": "Accept file types:", "email": "Email", "siteAccessed": "Site Accessed", "jobTitle": "Job Title", "natureOfIncident": "Nature of Incident", "previouslyFunctioning": "Previously Functioning", "incidentStepByStepDescription": "Incident Step-by-Step Description", "expectedFunctionality": "Expected Functionality", "description": "Description", "impactedSites": "Impacted Sites", "action": "Action", "objective": "Objective", "reviewMessage": "Please review your request and ensure all data is correct before proceeding.", "name": "Name", "role": "Role", "site": "Site", "subject": "Subject", "typeOfRequest": "Type of Request", "impactOnWork": "Impact on Work", "actionIWant": "Action (I Want)", "objectiveSoThat": "Objective (So That)", "descriptionOptional": "Description (Optional)", "welcomeMessage": "Welcome to the Celanese Support page for this application. As ONE CELANESE team, we can achieve success together. Got any issues or ideas for improvement? Open a ticket to reach our support experts. Your satisfaction is our priority. Thank you for trusting us!", "subjectPlaceholder": "Brief title for your request", "natureOfIncidentPlaceHolder": "What is the nature of the incident?", "natureOfIncidentHelper": "Ex: Certain Action Item management flow is showing errors.", "previouslyFunctioningPlaceHolder": "Was the system working properly before?", "previouslyFunctioningHelper": "Ex: It was possible for the user to approve an Action Item", "incidentStepDecisionPlaceHolder": "Where is the incident happening?", "incidentStepDecisionHelper": "Ex: Action Item Management > Home Page > Action Details", "expectedFunctionalityPlaceHolder": "How should it be working?", "expectedFunctionalityHelper": "Ex: Be possible to approve the Action item", "impactOnWorkPlaceHolder": "Select the type of impact", "impactedSitesPlaceHolder": "Select impacted sites", "actionPlaceHolder": "Specify the action you wish to perform", "actionHelper": "Ex: To do the bulk verification process in Action Item management ", "objectivePlaceHolder": "Specify the action you wish to perform", "objectiveHelper": "Ex: Verify several actions at the same time", "descriptionPlaceHolder": "Describe with as much detail as possible.", "reviewButton": "Review", "support": "Support", "errorSubmit": "An error occurred while creating the ticket", "mandatoryFieldsWarning": "Mandatory fields require a minimum of 5 characters."}, "adminSettings": {"title": "Admin", "tabs": {"recurring": "Recurring Action Items", "template": "Action Items Template", "categories": "Categories Configuration", "subcategory2": "Subcategory 2", "reference": "Reference"}, "table": {"headers": {"id": "ID", "title": "Title", "unit": "Unit", "category": "Category", "subcategoryOne": "Subcategory 1", "subcategoryTwo": "Subcategory 2", "startDate": "Start Date", "frequency": "Frequency", "updateBy": "Update By", "noEndDate": "No end Date", "status": "Status", "ext": "EXT", "app": "Application", "actions": "Actions", "description": "Description", "application": "Application", "owner": "Owner"}, "filter": {"default": "DEFAULT", "clear": "CLEAR", "applyFilter": "APPLY FILTER", "writeHere": "Write here...", "unit": "Unit", "category": "Category", "subcategoryOne": "Subcategory 1", "subcategoryTwo": "Subcategory 2", "status": "Status", "startDate": "Start Date", "noEndDate": "No end Date", "frequency": "Frequency", "application": "Application", "id": "Id", "title": "Title", "description": "Description", "noOptions": "No Options", "hideAll": "Hide All", "showAll": "Show All", "findColumn": "Find column", "columnTitle": "column title", "owner": "Owner"}, "newSubCategory2": "Create a New Subcategory 2", "newCategory": "New Category Configuration", "newReference": "New Reference", "saveChanges": "Save Changes"}, "common": {"yes": "Yes", "no": "No"}, "categories": {"drawerCreateTitle": "New Category Configuration", "drawerEditTitle": "Edit Category Configuration", "alertDelete": "There are Action Items Recurring linked to that Category Configuration", "form": {"category": "Category", "subcategoryOne": "Subcategory 1", "subcategoryTwo": "Subcategory 2", "daysFromAssignedDate": "Days from assigned date", "hasEmailNotification": "Has email notification", "attachmentRequired": "Attachment required", "isApprovalRequired": "Approval required", "defaultApprovalRole": "Default approval role", "defaultApprovalUser": "Default approval user", "daysToApproval": "Days to approval", "isVerificationRequired": "Verification required", "defaultVerificationRole": "Default verification role", "defaultVerificationUser": "Default verification user", "daysToVerification": "Days to verification", "isExtensionsAllowed": "Extensions allowed", "defaultExtensionApproverRole": "Default extension approver role", "isExtensionAttachmentRequired": "Extension attachment required", "buttons": {"save": "Save", "create": "Create", "cancel": "Cancel"}, "errors": {"approverRoleRequired": "Approver role must be selected when approval is required", "verifierRoleRequired": "Verifier role must be selected when verification is required", "approverUserRequired": "Please select a user for the Default Approver Role", "verifierUserRequired": "Please select a user for the Default Verification Role"}, "selectCategory": "Select Category", "selectSubcategory": "Select Subcategory", "selectSubcategory2": "Select Site Specific Category", "reportingUnit": "Reporting Unit", "selectReportingLoc": "Select Reporting Location", "businessLine": "Business Line", "impactedUnits": "Impacted Units", "impactedLocations": "Impacted Locations", "noUsersForThisRole": "No users available for this role"}}, "specificCategories": {"drawerCreateTitle": "Create a new Subcategory 2", "drawerEditTitle": "Edit Subcategory 2", "alertDelete": "There are Action Items Recurring and Categories configurations linked to this subcategory 2:", "form": {"name": "Subcategory 2 name", "description": "Description", "buttons": {"save": "Save", "create": "Create", "cancel": "Cancel"}}}, "recurrence": {"form": {"active": "Active", "inactive": "Inactive"}}, "reference": {"drawerCreateTitle": "Create a new Reference", "drawerEditTitle": "Edit Reference", "form": {"id": "Id", "title": "Title", "description": "Description", "application": "Application", "buttons": {"save": "Save", "create": "Create", "cancel": "Cancel"}}}, "template": {"updateView": "Update views"}}, "actionItemModal": {"commonValues": {"seeMoreDetails": "See More Details", "detailsExternalId": "Viewing detail of the event", "detailsOfItemRequest": "Viewing detail of the item of the request", "requestor": "Requestor", "unit": "Unit", "fcrOriginator": "FCR Originator", "status": "Status", "description": "Description", "functionalLocation": "Functional Location", "businessLine": "Business Line", "category": "Category", "sapNumber": "SAP Number", "keynoun": "Keynoun", "newKeyNounNeeded": "Is the new KeyNoun needed?", "sapSuperiorEquipment": "SAP # of Superior Equipment", "class": "Class", "additionalInfo": "Additional Info", "equipmentCriticality": "Equipment Criticality", "typeOfCriticality": "Type of Criticality", "replacingExistingEquipment": "Replacing Existing Equipment?", "sparePartsRequired": "Are spare parts required?", "permit": "Permit", "startupDate": "Start-up Date", "newManufacturerNeeded": "New Manufacturer Needed", "locationBuilding": "Location Building", "newFunctionalLocationNeeded": "New Functional Location Needed?", "suggestedTechnicalId": "Suggested Technical ID", "requestDate": "Request Date", "requestInfo": "Request Info", "basicInfo": "Basic Info", "generalInfo": "General Info", "locationData": "Location/Data", "equipment": "Equipment", "creationDate": "Creation Date"}, "modalEquipmentRequestRawData": {"projectNumber": "Project Number", "installDate": "Install Date"}, "modalPdpmRequestRawData": {"reliabilityLead": "Reliability Lead", "type": "Type", "activityReason": "Activity Reason", "newDueDate": "New Due Date", "newFrequency": "New Frequency", "leadTime": "Lead Time", "sapActivityType": "Sap Activity Type", "skill": "Skill", "equipmentShutdown": "Equipment Shutdown", "criticality": "Criticality", "isUrgent": "Is this Urgent?", "mpDescription": "MP Description", "justification": "Justification", "toWhatRegulation": "To What Regulation", "itemsAndObjects": "Items and Objects", "technicalId": "Technical Id", "isNewEquipment": "Is New Equipment?", "objectsToAdd": "Objects To Add", "itemsToAdd": "Items To Add"}, "modalMdmRequestRawData": {"safetyPPE": "Safety PPE"}, "modalRcaEventRawData": {"eventDetails": "Event Details", "investigationDetails": "Investigation Details", "investigationStartDate": "Investigation Start Date", "approvers": "Approvers", "dueDate": "Due Date", "leadInvestigator": "Lead Investigator", "rcaType": "RCA Type", "investigationTeam": "Investigation Team", "rootCauses": "Root Causes", "preliminary": "Preliminary", "eventDate": "Event Date", "eventType": "Event Type", "reportTitle": "Report Title", "reportDate": "Report Date", "location": "Location", "batchNumber": "Batch Number", "eventId": "Event ID", "eventDescription": "Event Description", "problemCategory": "Problem Category", "initiator": "Initiator", "proceduresInvolved": "Procedures Involved", "immediateActionTaken": "Immediate Action Taken", "rcaCode": "RCA Code", "problemDescription": "Problem Description", "eventTitle": "Event Title", "notificationDate": "Notification Date", "materialDescription": "Material Description", "returnedQuantity": "Returned Quantity", "notificationNumber": "Notification Number", "defectDescription": "Defect Description", "subjectCode": "Subject Code", "notificationDescription": "Notification Description", "severity": "Severity", "systemStatus": "System Status", "reportingLocation": "Reporting Location", "investigationEvent": "Investigation Event"}, "modalSourceEventRawData": {"aimEventInfo": "AIM Event Info", "title": "Title", "reportingLocation": "Location", "subCategory": "Subcategory 1", "siteSpecificCategory": "Subcategory 2"}, "modalICAPMOCReportRawData": {"changeInitiator": "Change Initiator", "moc": "MOC", "changeTitle": "Change Title", "changeDescription": "Change Description"}, "modalGapAssessmentExecutionDataRawData": {"info": "Info", "topic": "Topic", "tier2Requirement": "Tier 2 Requirement", "systemInPlace": "System in Place", "gapDetails": "Gap Details", "verification": "Verification"}, "modalOFWAEventRawData": {"observationType": "Observation Type", "createdBy": "Created By", "subcategory": "Subcategory", "createdOn": "Created On"}, "modalICAPMOOCReportRawData": {"moocOwner": "MOOC Owner", "moocDescription": "MOOC Description", "moocTitle": "MOOC Title", "newEmployee": "New  Employee", "positionImpacted": "Position Impacted"}, "title": {"MDREquipmentRequest": "Viewing details of MDM Equipment Request", "MDRPdpmRequest": "Viewing details from MDR PDPM Request", "MDRMdmRequest": "Viewing details from MDR MDM Request", "RCAEvent": "Viewing details from RCA", "AIMEvent": "Viewing details AIM Event", "ICAPMOCReport": "ICAP MOC Info", "GapAssessmentExecutionData": "Celia Gap Assessment Info", "OFWAEvent": "OFWA", "ICAPMOOCReport": "ICAP MOOC Info", "eventAnalysis": "Event Analysis Info"}}, "status": {"assigned": "Assigned", "pendingApproval": "<PERSON><PERSON>", "pendingVerification": "P.Verification", "dueDateExtensionPeriod": "DueDateExtension", "reassignmentPeriod": "Reassignment", "challengePeriod": "Challenge", "approvalRejected": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "verificationRejected": "VerificationRej", "draft": "Draft", "completed": "Completed", "Completed": "Completed", "deleted": "Deleted", "InProgress": "InProgress", "OnHold": "OnHold", "cancelled": "Cancelled", "Cancelled": "Cancelled", "private": "Private", "viewOnly": "ViewOnly", "active": "Active", "inactive": "Inactive", "fullName": {"assigned": "Assigned", "pendingApproval": "Pending Approval", "pendingVerification": "Pending Verification", "dueDateExtension": "Due Date Extension", "reassignmentPeriod": "Reassignment Period", "challengePeriod": "Challenge Period", "approvalRejected": "<PERSON><PERSON><PERSON><PERSON> Rejected", "verificationRejected": "Verification Rejected", "draft": "Draft", "completed": "Completed", "Completed": "Completed", "deleted": "Deleted", "InProgress": "In progress", "OnHold": "On hold", "cancelled": "Cancelled", "Cancelled": "Cancelled", "private": "Private", "viewOnly": "View only"}}, "category": {"capitalProject": "Capital Project", "engagement": "Engagement", "environmental": "Environmental", "fire": "Fire", "general": "General", "industrialHygiene": "Industrial Hygiene", "maintenance": "Maintenance", "occupationalHealthMedical": "Occupational Health/Medical", "peopleSafety": "People Safety", "processSafety": "Process Safety", "quality": "Quality", "rD": "R&D", "reliability": "Reliability", "security": "Security", "technology": "Technology"}, "subCategory1": {"administrative": "Administrative", "assessment": "Assessment", "audit": "Audit", "controlledDocument": "Controlled Document", "cost": "Cost", "customerComplaint": "Customer <PERSON><PERSON><PERSON><PERSON>", "digitalization": "Digitalization", "exposureAssessment": "Exposure Assessment", "feedback": "<PERSON><PERSON><PERSON>", "hazards": "Hazards", "housekeeping": "Housekeeping", "incident": "Incident", "jobCycleCheck": "Job Cycle Check", "jobSafetyCheck": "Job Safety Check", "managementOfChange": "Management of Change", "managementOfOrganizationalChange": "Management of Organizational Change", "managementReview": "Management Review", "mechanicalIntegrity": "Mechanical Integrity", "other": "Other", "preStartupSafetyReview": "Pre Startup Safety Review", "processHazardAnalysis": "Process Hazard Analysis", "productivity": "Productivity", "regulatory": "Regulatory", "rootCauseAnalysis": "Root Cause Analysis", "samplePlan": "Sample Plan", "statisticalAnalysisOfResults": "Statistical Analysis of Results", "threatAndRiskAssessment": "Threat and Risk Assessment", "trainingAndLearning": "Training and Learning", "uTPBTP": "UTP/BTP"}, "frequency": {"monthly": "Monthly", "custom": "Custom", "daily": "Daily", "weekly": "Weekly", "quarterly": "Quarterly", "yearly": "Yearly", "biennially": "Biennially", "triennially": "Triennially", "quinquennially": "Quinquennially"}, "common": {"yes": "Yes", "no": "No", "of": "of", "rowsPerPage": "Rows per page", "search": "Search", "filters": "Filters", "noData": "No Data", "noResultsForFilter": "No results match the selected filter", "close": "Close", "cancel": "Cancel", "return": "Return", "action": "Action", "errors": "errors", "submit": "Submit", "copy": "Copy"}, "bulkUploadActionItems": {"bulkUploadModalTitle": "Bulk upload action", "rowLimitWarning": "File upload will only process the first 1000 rows. Please ensure the file contains no more than this limit.", "downloadTemplate": "Download template", "reuploadFile": "Reupload file", "errorList": "Error list", "selectAction": "Select the action", "errorSendingFile": "Error: Invalid file format. Please upload a valid Excel file.", "downloadErrorReport": "Download error report", "noResportingSite": "No reporting site found", "noUser": "No user found", "outOf": "out of", "successfullyImported": "lines successfully imported.", "errorImported": "lines were not imported due to incorrect data.", "uploadValidExcel": "Please upload a valid .xlsx file.", "errorDownloadingTemplate": "Error downloading template:", "uploadLoadingMessage": "This may take a moment. Thank you for your patience!", "uploadLoading": "Loading...", "line": "Line", "error": "Error", "field": "Field", "loadingFiles": "Loading files...", "selectSite": "Select Reporting Site to download the corresponding template", "reportingSite": "Reporting Site", "columns": {"headers": {"title": "Title", "owner": "Owner (email)", "unit": "Unit", "location": "Location", "description": "Description", "sourceInformation": "Source Information", "category": "Category", "subcategory1": "Subcategory 1", "subcategory2": "Subcategory 2", "assignee": "Assignee (email)", "priority": "Priority", "assignmentDate": "Assignment Date", "dueDate": "Due Date"}, "placeholders": {"title": "Enter title (10-200 characters)", "owner": "Enter valid email", "unit": "Select unit", "location": "Select location", "description": "Enter description (10-1000 characters)", "sourceInformation": "Enter source information (10-200 characters)", "category": "Select category", "subcategory1": "Select subcategory 1", "subcategory2": "Select subcategory 2", "assignee": "Enter assignee email", "priority": "Select priority", "assignmentDate": "Enter assignment date (local format)", "dueDate": "Enter due date (local format)"}}, "errors": {"missingField": "Field is missing", "invalidTitle": "Title must have between 10 and 200 characters", "invalidDescription": "Description must have between 10 and 1000 characters", "invalidSourceInformation": "Source information must have between 10 and 200 characters", "invalidEmail": "<PERSON><PERSON> is invalid", "notFoundEmail": "No match found for the provided email", "invalidUnit": "Unit is invalid", "invalidLocation": "Location is invalid", "invalidUnitAndLocation": "Unit and location combination is invalid", "invalidCategory": "Category is invalid", "invalidSubCategory": "Subcategory 1 is invalid", "invalidSiteSpecificCategory": "Subcategory 2 is invalid", "invalidCategoryConfiguration": "This action matched a category configuration. Submit it using the form 'New Action Item - Action from Scratch'", "invalidPriority": "Priority is invalid", "invalidDateType": "Date type is invalid", "invalidDueDate": "Due date is invalid. It must be on or after the assignment date", "invalidDate": "Date is invalid. It must be on or after today"}}, "bulkDeleteActionItems": {"cancelOrDelete": "Cancel or delete actions", "deleteActionsAdvice": "Actions deleted will no longer be accessible to any user.", "cancelActionsAdvice": "Actions cancelled will be available with the status 'Cancelled'.", "itemsSelected": "item(s) selected", "deletedActionsLabel": "Actions deleted", "cancelledActionsLabel": "Actions cancelled", "deleteSuccessMessage": "Actions were deleted successfully.", "cancelSuccessMessage": "Actions were cancelled successfully."}, "bulkEditActionItems": {"editMultiple": "Edit multiple actions", "itemsSelected": "item(s) selected", "selectActionsAdvice": "Select the actions you want to edit from the table below.", "editFieldsAdvice": "Choose which fields to update and provide new values.", "changesApplyAdvice": "Changes will be applied to all selected actions.", "editSelected": "Edit Selected Actions", "modalTitle": "Bulk Edit Actions", "modalSubtitle": "Editing {count} action item(s)", "selectedActions": "Selected Actions", "fieldsToUpdate": "Fields to Update", "fieldsInstruction": "Only fields with values will be updated. Leave fields empty to keep current values.", "selectFieldsInstruction": "Check the fields you want to update and provide new values:", "updateActions": "Update Actions", "updating": "Updating...", "successMessage": "{count} action item(s) updated successfully", "partialSuccess": "{success} of {total} action item(s) updated successfully", "errorMessage": "Failed to update action items. Please try again.", "errorsOccurred": "The following errors occurred"}, "actionItem": {"title": "Title", "description": "Description", "dueDate": "Due Date", "assignee": "Assignee", "approver": "Approver", "verifier": "Verifier", "priority": "Priority", "category": "Category", "subCategory": "Sub Category", "reportingUnit": "Reporting Unit", "reportingLocation": "Reporting Location", "owner": "Owner", "status": "Status"}, "createNewActionItem": {"title": "Create Action Item", "buttonLabel": "New Action Item", "formValidations": {"messageValidation1": "Title must be at least 10 characters", "messageValidation2": "Title cannot exceed 200 characters", "messageValidation3": "Description must be at least 10 characters", "messageValidation4": "Description cannot exceed 200 characters", "messageValidation5": "Assignment date is required and must be a valid dates", "messageValidation6": "Assignment date must be today or later", "messageValidation7": "Due date is required and must be a valid date", "messageValidation8": "Due date must be on or after the assignment date", "messageValidation9": "This field is required"}, "snackMessages": {"message1": "Submit successful!", "message2": "Submit failed. Please try again."}, "submitButtonLabel": "Submit", "form": {"title": "Title", "description": "Description", "application": "Application", "sourceType": "Source Type", "sourceInformation": "Source Information", "owner": "Owner", "assigne": "Assignee", "assignmentDate": "Assignment Date", "dueDate": "Due Date", "reportingUnit": "Reporting Unit", "reportingLocation": "Reporting Location", "category": "Category", "subcategory": "Subcategory", "subcategory2": "Subcategory2", "evidenceRequired": "Evidence Required", "verifier": "Verifier", "priority": "Priority", "helperText": {"helper1": "Minimum length of 10 and maximum of 200 characters", "helper2": "Minimum length of 10 and maximum of 1000 characters"}, "noSourceType": "This App does not have a source type. So you can not create an action item."}}, "privateComponent": {"Action": "Action", "Event": "Event", "privateSettingsButton": "Private {} settings", "privateSettingsTitle": "Private {} Settings", "tooltipPrivateEventText": "A Private Event will only be visible for the Primary and Secondary owners of the Event and for the users, teams or roles defined in Viewers configuration. In summary: ", "tooltipPrivateEventList1": "Owners and Viewers can see details of the event and uploaded documents.", "tooltipPrivateEventList2": "All actions created from a private event will also be private.", "tooltipPrivateEventList3": "Assignees from those actions will only be able to see the details of their own actions.", "tooltipPrivateEventList4": "All other users will not see Events/Actions data listed on the tables.", "deleteAllViewers": "Delete All {} Viewers", "tooltipPrivateActionText": "A Private Action, and its details and documents, will only be visible for:", "tooltipPrivateActionList1": "Owner: user defined who is able to edit the action details", "tooltipPrivateActionList2": "Viewers: users, teams or roles defined in Viewers configuration", "tooltipPrivateActionList3": "Assignee: user assigned to complete the action", "tooltipPrivateActionList4": "Approvers: user assigned to approve the action", "tooltipPrivateActionList5": "Verifiers: user assigned to verify the effectiveness of the action", "tooltipPrivateActionList6": "All other users will not see Actions data listed on the tables."}, "notifications": {"title": "Notifications", "noRecords": "No notifications found.", "viewMore": "View More"}}