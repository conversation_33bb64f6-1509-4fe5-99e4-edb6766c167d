GET_USER_ROLE_SITES = """
query GetUserRoleSites ($filter: _ListUserRoleSiteFilter, $reportingSiteFilter: _ListReportingSiteFilter) {
  listUserRoleSite (filter: $filter, first: 1000) {
    items {
      externalId
      space
      role {
        externalId
        space
      }
      reportingSite (filter: $reportingSiteFilter) {
        externalId
        space
      }
      usersComplements {
        items {
          externalId
          space
          userAzureAttribute {
            externalId
            space
            user {
              externalId
              space
              firstName
              lastName
              email
            }
          }
        }
      }
    }
  }
}
"""
