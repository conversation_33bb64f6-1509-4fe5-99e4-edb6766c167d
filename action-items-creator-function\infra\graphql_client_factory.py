from cognite.client import Cognite<PERSON>lient
from gql.transport.aiohttp import AIOHTTPTransport
from gql import Client
import os
import sys


script_dir = os.path.dirname(os.path.abspath(__file__))
project_dir = os.path.dirname(script_dir)
sys.path.append(project_dir)
from models.settings import Settings


class GraphqlClientFactory:
    @staticmethod
    def create(cognite_client: CogniteClient, settings: Settings):
        key, value = cognite_client.config.credentials.authorization_header()
        headers = {key: value}

        headers["X-Cdp-App"] = settings.cognite_client_name

        transport = AIOHTTPTransport(
            url=settings.graphql_uri,
            headers=headers,
        )

        return Client(transport=transport, fetch_schema_from_transport=False)
