import { translate } from './generate-translate'
import { SITE_EXTERNAL_ID_REQUIRED_FIELD } from './validate-codes'

export const getColumn = (headerKey: string, placeholderKey?: string, mandatory = false) => ({
    header: `${translate(headerKey)}${mandatory ? '*' : ''}`,
    placeholder: translate(placeholderKey ?? headerKey),
})

export const getBulkUploadColumns = (siteId: string, eventId?: string) => {
    return {
        title: getColumn(
            'bulkUploadActionItems.columns.headers.title',
            'bulkUploadActionItems.columns.placeholders.title',
            true
        ),
        owner: getColumn(
            'bulkUploadActionItems.columns.headers.owner',
            'bulkUploadActionItems.columns.placeholders.owner',
            true
        ),
        unit: getColumn(
            'bulkUploadActionItems.columns.headers.unit',
            'bulkUploadActionItems.columns.placeholders.unit',
            true
        ),
        location: getColumn(
            'bulkUploadActionItems.columns.headers.location',
            'bulkUploadActionItems.columns.placeholders.location',
            siteId === SITE_EXTERNAL_ID_REQUIRED_FIELD
        ),
        description: getColumn(
            'bulkUploadActionItems.columns.headers.description',
            'bulkUploadActionItems.columns.placeholders.description',
            true
        ),
        ...(!eventId && {
            sourceInformation: getColumn(
                'bulkUploadActionItems.columns.headers.sourceInformation',
                'bulkUploadActionItems.columns.placeholders.sourceInformation',
                siteId === SITE_EXTERNAL_ID_REQUIRED_FIELD
            ),
        }),
        category: getColumn(
            'bulkUploadActionItems.columns.headers.category',
            'bulkUploadActionItems.columns.placeholders.category',
            true
        ),
        subCategory: getColumn(
            'bulkUploadActionItems.columns.headers.subcategory1',
            'bulkUploadActionItems.columns.placeholders.subcategory1',
            true
        ),
        siteSpecificCategory: getColumn(
            'bulkUploadActionItems.columns.headers.subcategory2',
            'bulkUploadActionItems.columns.placeholders.subcategory2',
            siteId === SITE_EXTERNAL_ID_REQUIRED_FIELD
        ),
        assignee: getColumn(
            'bulkUploadActionItems.columns.headers.assignee',
            'bulkUploadActionItems.columns.placeholders.assignee',
            true
        ),
        priority: getColumn(
            'bulkUploadActionItems.columns.headers.priority',
            'bulkUploadActionItems.columns.placeholders.priority',
            false
        ),
        assignmentDate: getColumn(
            'bulkUploadActionItems.columns.headers.assignmentDate',
            'bulkUploadActionItems.columns.placeholders.assignmentDate',
            true
        ),
        dueDate: getColumn(
            'bulkUploadActionItems.columns.headers.dueDate',
            'bulkUploadActionItems.columns.placeholders.dueDate',
            true
        ),
    }
}
