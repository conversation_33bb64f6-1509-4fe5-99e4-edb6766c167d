import { ExternalEntity } from '..'
import { User } from '../user-management/user'
import { ApprovalWorkflowCondition } from './workflow-condition'
import { ApprovalWorkflowStatus } from './workflow-status'
import { WorkflowStep } from './workflow-step'

export interface Workflow extends ExternalEntity {
    externalId: string
    stepCondition?: ApprovalWorkflowCondition
    steps?: WorkflowStep[]
    startDate?: Date
    endDate?: Date
    createdBy?: User
    description?: string
    status?: ApprovalWorkflowStatus
}
