import { gql } from '@apollo/client'
import { useEffect, useState } from 'react'
import { useGraphqlQuery } from '../cognite/useGraphqlQuery'
import { Team } from '../../models/common/user-management/team'
import { ArrayEntity } from '../../models/common'
import { User } from '../../models/common/user-management/user'
import { EntityType, GetSpace } from '../../utils/space-util'
import { useSearchResultsFunction } from '../general-functions/useSearchResultsFunction'
import { useRetrieveResultsFunction } from '../general-functions/useRetrieveResultsFunction'
import { getLocalUserSite } from '@celanese/celanese-ui'

const PROPERTIES = ['name']

export interface TeamSearchRequest {
    search: string
}

export interface TeamQueryRequest {
    siteId?: string
    teamIds?: string[]
}

const buildTeamQuery = (request: TeamQueryRequest): string => {
    const teamFilters: string[] = []
    const memberFilters: string[] = ['{ active: { eq: true } }']

    const siteId = request.siteId || getLocalUserSite()?.siteId || '-'

    teamFilters.push(`
        { space: { eq: "${GetSpace(EntityType.UMG)}" } },
        { reportingSite: { externalId: { eq: "${siteId}" } } }
    `)

    if (request.teamIds?.length) {
        const idsList = request.teamIds.map((id) => `"${id}"`).join(', ')
        teamFilters.push(`{ externalId: { in: [${idsList}] } }`)
    }

    const queryFilter = `{ and: [${teamFilters.join(', ')}] }`
    const membersQueryFilter = `{ and: [${memberFilters.join(', ')}] }`

    return `
        query GetTeam {
            listTeam(
                filter: ${queryFilter}
                , first: 1000
            ) {
                items {
                    externalId
                    space
                    name
                    description
                    reportingSite {
                        externalId
                        name
                        description
                        aliases
                        createdTime
                        space
                        reportingUnits {
                            items {
                                externalId
                                name
                                description
                                space
                            }
                        }
                    }
                    reportingUnits {
                        items {
                            externalId
                            name
                            description
                            aliases
                            createdTime
                            space
                            refersTo {
                                items {
                                    name
                                    externalId
                                    space
                                }
                            }
                            reportingSites {
                                items {
                                    externalId
                                    siteCode
                                    space
                                }
                            }
                        }
                    }
                    members(
                        filter: ${membersQueryFilter},
                        first: 1000
                    ) {
                        items {
                            externalId
                            firstName
                            lastName
                            email
                            space
                            active
                        }
                    }
                    teamLeader {
                        externalId
                        firstName
                        lastName
                        email
                        space
                    }
                    isActive
                }
            }
        }
    `
}

export const useTeams = (request: TeamQueryRequest) => {
    const query = buildTeamQuery(request)
    const { data: fdmData } = useGraphqlQuery<Team>(gql(query), 'listTeam', {})

    const [resultData, setResultData] = useState<{ data: Team[]; loading: boolean }>({
        data: [],
        loading: true,
    })

    useEffect(() => {
        if (fdmData.length == 0 || (request?.teamIds && request.teamIds.length === 0)) {
            setResultData({ data: [], loading: false })
        } else {
            const fdmDataParsed = fdmData.map((d) => {
                const arrayEntity = d.members as any as ArrayEntity<User>
                return {
                    ...d,
                    members: arrayEntity.items,
                }
            })
            setResultData({ data: fdmDataParsed, loading: false })
        }
    }, [fdmData])

    return {
        loading: resultData.loading,
        teams: resultData.data,
    }
}

export const useTeamSearch = (request: TeamSearchRequest) => {
    const [teamViewVersion, setTeamViewVersion] = useState<string>('')
    const [view, setView] = useState<any>()
    const [resultData, setResultData] = useState<{ data: string[]; loading: boolean }>({
        data: [],
        loading: true,
    })

    const { getAllResults: getAllData } = useSearchResultsFunction()
    const { getAllResults: getAllViews } = useRetrieveResultsFunction()

    useEffect(() => {
        const fetchViews = async () => {
            try {
                const res = await getAllViews()
                const teamView = res.find((v) => v.externalId === 'Team')
                const teamViewVersion = teamView?.version

                setView(teamView)
                setTeamViewVersion(teamViewVersion ?? '0')
            } catch (error) {}
        }

        fetchViews()
    }, [])

    useEffect(() => {
        const performSearch = async () => {
            if (request.search !== '' && view) {
                setResultData({ data: [], loading: true })
                try {
                    const res = await getAllData(view, request.search, 'node', PROPERTIES, undefined)
                    const lowerSearchText = request.search.toLowerCase()
                    const externalIds =
                        res?.items
                            .filter((item: any) => {
                                const teamProperties = item.properties['UMG-COR-ALL-DMD'][`Team/${teamViewVersion}`]
                                return teamProperties.name?.toLowerCase().includes(lowerSearchText)
                            })
                            .map((item: any) => item.externalId) || []
                    setResultData({ data: externalIds, loading: false })
                } catch (error) {
                    setResultData({ data: [], loading: false })
                }
            } else {
                setResultData({ data: [], loading: false })
            }
        }

        performSearch()
    }, [request.search])

    return {
        loading: resultData.loading,
        searchResult: resultData.data,
    }
}
