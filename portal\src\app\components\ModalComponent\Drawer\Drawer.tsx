import React, { useMemo } from 'react'
import { ClnDrawer } from '@celanese/ui-lib'
import { boxClasses, useMediaQuery, useTheme } from '@mui/material'

interface ModalDrawerProps {
    title: string
    overlineMeta?: string
    header?: string
    content: React.ReactNode
    openDrawer: boolean
    closeDrawer: () => void
    customWidth?: boolean
}

export const CustomDrawer: React.FC<ModalDrawerProps> = ({
    title,
    overlineMeta,
    header,
    content,
    openDrawer,
    closeDrawer,
    customWidth,
}) => {
    const theme = useTheme()
    const isSmallScreen = useMediaQuery(theme.breakpoints.down('md'))

    const fixedWidth = useMemo(() => (isSmallScreen ? '100vw !important' : '600px !important'), [isSmallScreen])

    const responsiveWidth = {
        width: '40vw !important',
        '@media (max-width: 600px)': {
            width: '100vw !important',
        },
        '@media (min-width: 600px) and (max-width: 1024px)': {
            width: '50vw !important',
        },
        '@media (min-width: 1500px)': {
            width: '25vw !important',
        },
    }

    const drawerWidthStyles = customWidth ? { width: fixedWidth } : responsiveWidth

    return (
        <ClnDrawer
            anchor="right"
            variant="persistent"
            overlineMeta={overlineMeta}
            title={title}
            header={header}
            open={openDrawer}
            closeDrawer={closeDrawer}
            sx={{
                display: 'flex',
                flexDirection: 'column',
                overflowX: 'hidden',
                padding: '1rem !important',
                ...drawerWidthStyles,
                [`& > .${boxClasses.root}:nth-of-type(3), & > .${boxClasses.root}:nth-of-type(4)`]: {
                    height: '100%',
                    overflowY: 'auto',
                },
            }}
        >
            {content}
        </ClnDrawer>
    )
}
