from typing import Annotated

from clients.core.models import Node
from clients.core.validators import edge_unwraper_validator

from ._models import _UserAzureAttribute, _UserRoleSite


class UserComplementResult(Node):
    """User complement result containing basic user information."""

    first_name: str | None
    last_name: str | None
    email: str | None
    name: str | None
    label: str | None
    user_azure_attribute: _UserAzureAttribute | None


class UserRolesAndTeamsResult(Node):
    """User roles and teams result."""

    user_role_site: Annotated[list[_UserRoleSite], edge_unwraper_validator]
    user_azure_attribute: _UserAzureAttribute | None


class UserByEmailResult(Node):
    """User by email result with active roles and teams information."""

    active_user_roles_ids: list[str] | None
    active_user_teams_ids: list[str] | None
    user_external_id: str | None


class UsersBySiteResult(Node):
    """User by site result containing email information."""

    email: str | None
