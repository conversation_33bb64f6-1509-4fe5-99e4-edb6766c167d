import dayjs, { Dayjs } from 'dayjs'
import { Theme, Typography } from '@mui/material'
import { translate } from './generate-translate'
import { isActionSourceTypeExternalIdEnumType, validSourceTypeIdsForDate } from '../enums/ActionSourceTypeEnum'

export const formatDate = (date: Dayjs | string | null) => {
    return date ? dayjs(date).format('YYYY-MM-DD') : null
}

export function formatDisplayDueDate(date?: string | Date, sourceType?: string): string {
    const formatted = dayjs(date).format('MM/DD/YYYY')
    const isBeforeStartupDate = formatted === '12/31/9999'
    const isValidSourceType =
        sourceType && isActionSourceTypeExternalIdEnumType(sourceType) && validSourceTypeIdsForDate.includes(sourceType)

    return isBeforeStartupDate && isValidSourceType ? translate('table.headers.beforeStartup') : formatted
}

export const generateDueDateColor = (date: string | undefined, theme: Theme, sourceType?: string) => {
    return (
        <Typography
            fontSize={14}
            fontFamily={'Roboto, Helvetica, Arial, sans-serif'}
            style={{
                color: dayjs(date).isBefore(dayjs().startOf('day'))
                    ? theme.palette.error.main
                    : theme.palette.text.primary,
            }}
            data-test="home_table-due_date_content"
            data-origin="aim"
        >
            {formatDisplayDueDate(date, sourceType)}
        </Typography>
    )
}
