import { useMediaQuery, useTheme } from '@mui/material'
import { ModalWrapper } from '../../ModalComponent/Modal/ModalWrapper'
import { ClnButton, ClnTabs, MatIcon } from '@celanese/ui-lib'
import { ModalData } from './modalData'
import { useEffect, useMemo, useState } from 'react'
import {
    useMDREquipmentRequest,
    useMDRMdmRequest,
    useMDRPdpmRequest,
} from '@/app/common/hooks/integration/master-data-request/useMDREquipmentRequest'
import { useActionSourceDetailConfigs } from '@/app/common/hooks/action-item-management/useActionSourceDetailConfigs'
import { useSourceEvents } from '@/app/common/hooks/action-item-management/useSourceEvents'
import { buildModalBody, buildModalTitle, RCA_SOURCE_TYPES } from './utils'
import { useICAPMOCReport } from '@/app/common/hooks/integration/icap/useICAPMOCReport'
import { useGapAssessmentExecutionData } from '@/app/common/hooks/integration/celia-gap-assessment/useGapAssessmentExecutionData'
import LoaderCircular from '../../Loader'
import GenericFieldTitle from '../../FieldsComponent/GenericFieldTitle'
import { useOFWAEvent } from '@/app/common/hooks/integration/ofwa/useOFWAEvent'
import { useICAPMOOCReport } from '@/app/common/hooks/integration/icap/useICAPMOOCReport'
import { ActionSourceTypeExternalIdEnum } from '@/app/common/enums/ActionSourceTypeEnum'
import { translate } from '@/app/common/utils/generate-translate'
import { AzureFunctionClient } from '@/app/common/clients/azure-function-client'
import { useSnackbar } from '@/app/common/contexts/SnackbarContext'
import { ExternalSourceDetails } from '@/app/common/models/external-source-details'
import { UserRolesPermission } from '@celanese/celanese-sdk'
import { SubSection } from './SubSection'
import {
    ButtonsContainer,
    ContentBox,
    LoadingBox,
    modalBackgroundSxProps,
    ModalContentBox,
    modalTitleSxProps,
    SectionContainer,
    StyledTitle,
    SubtitleContainer,
    TabContainer,
} from './styles'
import { RCA_SOURCE_TYPE_PREFIX } from '@/app/common/utils'
import { SourceEvent } from '@/app/common/models/source-event'

type ActionItemModalProps = {
    handleCloseModal: () => void
    sourceId?: string
    sourceTypeId?: string
    activeUser: UserRolesPermission
    siteId: string
}

export function ActionItemModal({
    handleCloseModal,
    sourceId,
    sourceTypeId,
    activeUser,
    siteId,
}: ActionItemModalProps) {
    const { showSnackbar } = useSnackbar()

    const theme = useTheme()
    const isMobile = useMediaQuery(theme.breakpoints.down('sm'))

    const [currentTab, setCurrentTab] = useState(0)

    const [modalTitle, setModalTitle] = useState<string>('')
    const [modalData, setModalData] = useState<ModalData>()

    const { actionSourceDetailConfigs, loading: loadingActionSourceDetailConfig } = useActionSourceDetailConfigs({
        sourceTypeId: sourceTypeId ?? '-',
    })
    const [rcaEventData, setRcaEventData] = useState<ExternalSourceDetails>()

    const { data: MDREquipmentRequestData } = useMDREquipmentRequest(
        useMemo(
            () => (sourceTypeId === ActionSourceTypeExternalIdEnum.MDREquipmentRequest ? sourceId : undefined),
            [sourceId, sourceTypeId]
        )
    )
    const { data: MDRPdpmRequestData } = useMDRPdpmRequest(
        useMemo(
            () => (sourceTypeId === ActionSourceTypeExternalIdEnum.MDRPdpmRequest ? sourceId : undefined),
            [sourceId, sourceTypeId]
        )
    )
    const { data: MDRMdmRequestData } = useMDRMdmRequest(
        useMemo(
            () => (sourceTypeId === ActionSourceTypeExternalIdEnum.MDRMdmRequest ? sourceId : undefined),
            [sourceId, sourceTypeId]
        )
    )
    const { data: ICAPMOCReportData } = useICAPMOCReport(
        useMemo(
            () => (sourceTypeId === ActionSourceTypeExternalIdEnum.ICAPMOCReport ? sourceId : undefined),
            [sourceId, sourceTypeId]
        )
    )
    const { data: OFWAEventData } = useOFWAEvent(
        useMemo(
            () => (sourceTypeId === ActionSourceTypeExternalIdEnum.OFWAEvent ? sourceId : undefined),
            [sourceId, sourceTypeId]
        )
    )
    const { data: gapAssessmentExecutionData } = useGapAssessmentExecutionData(
        useMemo(
            () => (sourceTypeId === ActionSourceTypeExternalIdEnum.GapAssessmentExecutionData ? sourceId : undefined),
            [sourceId, sourceTypeId]
        )
    )
    const { data: ICAPMOOCReportData } = useICAPMOOCReport(
        useMemo(
            () => (sourceTypeId === ActionSourceTypeExternalIdEnum.ICAPMOOCReport ? sourceId : undefined),
            [sourceId, sourceTypeId]
        )
    )

    const { sourceEvents: sourceEventData } = useSourceEvents(
        activeUser,
        useMemo(
            () => (sourceTypeId === ActionSourceTypeExternalIdEnum.AIMEvent ? sourceId ?? '-' : undefined),
            [sourceId, sourceTypeId]
        ),
        siteId
    )

    useEffect(() => {
        const modalData = buildModalBody(
            MDREquipmentRequestData,
            MDRPdpmRequestData,
            MDRMdmRequestData,
            rcaEventData,
            ICAPMOCReportData,
            gapAssessmentExecutionData,
            OFWAEventData,
            ICAPMOOCReportData,
            sourceEventData,
            sourceTypeId
        )

        const title = buildModalTitle(
            MDREquipmentRequestData,
            MDRPdpmRequestData,
            MDRMdmRequestData,
            rcaEventData,
            ICAPMOCReportData,
            gapAssessmentExecutionData,
            OFWAEventData,
            ICAPMOOCReportData,
            sourceEventData,
            sourceTypeId
        )

        setModalTitle(title)
        setModalData(modalData)
    }, [
        MDREquipmentRequestData,
        MDRMdmRequestData,
        MDRPdpmRequestData,
        rcaEventData,
        ICAPMOCReportData,
        gapAssessmentExecutionData,
        sourceEventData,
        OFWAEventData,
        ICAPMOOCReportData,
        sourceTypeId,
    ])

    const link = useMemo(() => {
        if (!sourceTypeId) return ''

        const getIdAndSpace = (): { id: string; space: string; siteCode: string } => {
            const base = { id: '', space: '', siteCode: '' }
            if (sourceTypeId === ActionSourceTypeExternalIdEnum.AIMEvent) {
                const data =
                    Array.isArray(sourceEventData?.data) && sourceEventData.data.length > 0
                        ? sourceEventData.data[0]
                        : (sourceEventData?.data as SourceEvent | undefined)
                return {
                    ...base,
                    id: sourceId ?? '',
                    siteCode: data?.reportingSite?.siteCode ?? '',
                }
            }
            if (sourceTypeId === ActionSourceTypeExternalIdEnum.ICAPMOCReport) {
                return {
                    ...base,
                    id: ICAPMOCReportData[0]?.number?.split('-')[0] ?? '',
                }
            }

            if (sourceTypeId === ActionSourceTypeExternalIdEnum.ICAPMOOCReport) {
                return {
                    ...base,
                    id: ICAPMOOCReportData[0]?.event?.number ?? '',
                }
            }

            if (sourceTypeId === ActionSourceTypeExternalIdEnum.GapAssessmentExecutionData) {
                return {
                    ...base,
                    id: gapAssessmentExecutionData[0]?.gapAssessmentExecutions?.items[0]?.externalId ?? '',
                }
            }

            if (RCA_SOURCE_TYPES.has(sourceTypeId)) {
                return {
                    ...base,
                    id: sourceId?.replace('EVEN-', '') ?? '',
                    space: rcaEventData?.general?.space ?? '',
                }
            }

            return {
                ...base,
                id: sourceId ?? '',
            }
        }

        const { id, space, siteCode } = getIdAndSpace()
        const baseUrl = actionSourceDetailConfigs?.[0]?.properties?.url || ''

        return baseUrl.replace('{id}', id).replace('{space}', space).replace('{siteCode}', siteCode)
    }, [
        sourceTypeId,
        sourceEventData,
        ICAPMOCReportData,
        ICAPMOOCReportData,
        gapAssessmentExecutionData,
        sourceId,
        rcaEventData?.general?.space,
        actionSourceDetailConfigs,
    ])

    useEffect(() => {
        if (!sourceTypeId || !sourceId || !sourceTypeId.startsWith(RCA_SOURCE_TYPE_PREFIX)) return

        const controller = new AbortController()
        const timeout = setTimeout(async () => {
            try {
                const client = new AzureFunctionClient()

                const result = await client.getExternalSourceDetails({
                    sourceType: sourceTypeId,
                    sourceId: sourceId,
                })

                setRcaEventData({
                    general: result.general,
                    specific: result.specific,
                })
            } catch (err) {
                showSnackbar(translate('alerts.unexpectedErrorOcurred'), 'error', 'action-item-modal')
            }
        }, 500)

        return () => {
            clearTimeout(timeout)
            controller.abort()
        }
    }, [sourceTypeId, sourceId, showSnackbar])

    const isLoading = useMemo(
        () => loadingActionSourceDetailConfig || !modalData?.sections.length,
        [loadingActionSourceDetailConfig, modalData]
    )

    const tabs = useMemo(
        () => [
            {
                label: translate('source-event.details'),
                content: (
                    <TabContainer>
                        {isLoading ? (
                            <LoadingBox>{LoaderCircular()}</LoadingBox>
                        ) : (
                            <ContentBox>
                                {modalData?.title && <StyledTitle variant="h6">{modalData.title}</StyledTitle>}

                                {modalData?.subtitle && (
                                    <SubtitleContainer>
                                        <GenericFieldTitle isDetailsExternalId fieldName={modalData.subtitle} />
                                        <GenericFieldTitle
                                            isDetailsExternalId
                                            isBold
                                            fieldName={modalData.externalId ?? 'N/A'}
                                        />
                                    </SubtitleContainer>
                                )}

                                {modalData?.sections?.map((sec) => (
                                    <SectionContainer key={sec.title}>
                                        {sec.subsections?.map((subSec) => (
                                            <SubSection
                                                key={subSec.title}
                                                title={subSec.title}
                                                subtitle={subSec.subtitle}
                                                externalId={subSec.externalId}
                                                columns={subSec.columns}
                                                rows={subSec.rows}
                                            />
                                        ))}
                                    </SectionContainer>
                                ))}
                            </ContentBox>
                        )}
                    </TabContainer>
                ),
            },
        ],
        [isLoading, modalData]
    )

    return (
        <ModalWrapper
            title={modalTitle}
            openModal
            closeModal={handleCloseModal}
            sxProps={modalBackgroundSxProps}
            sxPropsTitle={modalTitleSxProps}
            content={
                <ModalContentBox isMobile={isMobile}>
                    <ClnTabs
                        sxProps={{ border: '0' }}
                        value={currentTab}
                        tabs={tabs}
                        onChange={(_e, value) => setCurrentTab(value)}
                    />

                    {Boolean(actionSourceDetailConfigs?.[0]?.properties) && (
                        <ButtonsContainer>
                            <ClnButton
                                size="medium"
                                variant="text"
                                label={translate('common.cancel')}
                                onClick={handleCloseModal}
                            />
                            <ClnButton
                                size="medium"
                                variant="outlined"
                                label={translate('actionItemModal.commonValues.seeMoreDetails')}
                                onClick={() => window.open(link, '_blank')}
                                startIcon={isMobile ? undefined : <MatIcon icon="link" />}
                                endIcon={isMobile ? <MatIcon icon="link" /> : undefined}
                            />
                        </ButtonsContainer>
                    )}
                </ModalContentBox>
            }
        />
    )
}
