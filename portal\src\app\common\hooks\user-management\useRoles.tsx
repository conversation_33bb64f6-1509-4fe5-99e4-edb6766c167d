import { gql } from '@apollo/client'
import { useEffect, useState } from 'react'
import { Role } from '../../models/common/user-management/role'
import { useGraphqlQuery } from '../cognite/useGraphqlQuery'
import { EntityType, GetSpace } from '../../utils/space-util'
import { useSearchResultsFunction } from '../general-functions/useSearchResultsFunction'
import { useRetrieveResultsFunction } from '../general-functions/useRetrieveResultsFunction'
import { getLocalUserSite } from '@celanese/celanese-ui'

const PROPERTIES = ['name']

export interface RoleSearchRequest {
    search: string
}

export interface RoleQueryRequest {
    siteId?: string
    externalIds?: string[]
}

const buildRoleQuery = (request: RoleQueryRequest): string => {
    const filters: string[] = []

    const siteId = request.siteId || getLocalUserSite()?.siteId || '-'

    if (request.externalIds?.length) {
        const externalIds = request.externalIds.map((id) => `"${id}"`).join(', ')
        filters.push(`{ externalId: { in: [${externalIds}] } }`)
    }

    filters.push(
        `{ space: { eq: "${GetSpace(EntityType.UMG)}" } }`,
        `{ site: { externalId: { eq: "${siteId}" } } }`,
        `{ roleCategory: { externalId: { prefix: "RoleSite" } } }`
    )

    const queryFilter = `{ and: [ ${filters.join(', ')} ] }`

    return `
        query GetRole {
            listRole(
                filter: ${queryFilter}
                , first: 1000
            ) {
                items {
                    externalId
                    name
                    description
                    createdTime
                    space
                    roleCategory {
                        externalId
                        name
                        description
                        space
                    }
                }
            }
        }
    `
}

export const useRoles = (request: RoleQueryRequest) => {
    const query = buildRoleQuery(request)
    const { data: fdmData } = useGraphqlQuery<Role>(gql(query), 'listRole', {})

    const [resultData, setResultData] = useState<{ data: Role[]; loading: boolean }>({
        data: [],
        loading: true,
    })

    useEffect(() => {
        if (fdmData.length == 0 || (request?.externalIds && request.externalIds.length === 0)) {
            setResultData({ data: [], loading: false })
        } else {
            setResultData({ data: fdmData, loading: false })
        }
    }, [fdmData])

    return {
        loading: resultData.loading,
        roles: resultData.data,
    }
}

export const useRoleSearch = (request: RoleSearchRequest) => {
    const [roleViewVersion, setRoleViewVersion] = useState<string>('')
    const [view, setView] = useState<any>()
    const [resultData, setResultData] = useState<{ data: string[]; loading: boolean }>({
        data: [],
        loading: true,
    })

    const { getAllResults: getAllData } = useSearchResultsFunction()
    const { getAllResults: getAllViews } = useRetrieveResultsFunction()

    useEffect(() => {
        const fetchViews = async () => {
            try {
                const res = await getAllViews()
                const roleView = res.find((v) => v.externalId === 'Role')
                const roleViewVersion = roleView?.version

                setView(roleView)
                setRoleViewVersion(roleViewVersion ?? '0')
            } catch (error) {}
        }

        fetchViews()
    }, [])

    useEffect(() => {
        const performSearch = async () => {
            if (request.search !== '' && view) {
                setResultData({ data: [], loading: true })
                try {
                    const res = await getAllData(view, request.search, 'node', PROPERTIES, undefined)
                    const lowerSearchText = request.search.toLowerCase()
                    const externalIds =
                        res?.items
                            .filter((item: any) => {
                                const roleProperties = item.properties['UMG-COR-ALL-DMD'][`Role/${roleViewVersion}`]
                                return roleProperties.name?.toLowerCase().includes(lowerSearchText)
                            })
                            .map((item: any) => item.externalId) || []
                    setResultData({ data: externalIds, loading: false })
                } catch (error) {
                    setResultData({ data: [], loading: false })
                }
            } else {
                setResultData({ data: [], loading: false })
            }
        }

        performSearch()
    }, [request.search])

    return {
        loading: resultData.loading,
        searchResult: resultData.data,
    }
}
