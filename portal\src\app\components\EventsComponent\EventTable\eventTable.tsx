import { Box, Tooltip, useMediaQuery, useTheme } from '@mui/material'
import { EventFilter, FilterInfoEventProps } from '../EventFilter'
import { getMessages, NoTranslate, TranslationContext, TranslationContextState } from '@celanese/celanese-ui'
import { useCallback, useContext, useEffect, useMemo, useState } from 'react'
import { useRouter } from 'next/navigation'
import { SourceEvent, SourceEventResponse } from '@/app/common/models/source-event'
import { useActionItemCategories } from '@/app/common/hooks/action-item-management/useActionItemCategories'
import { useSourceEventStatuses } from '@/app/common/hooks/action-item-management/useSourceEventStatuses'
import { useActionItemSubCategories } from '@/app/common/hooks/action-item-management/useActionItemSubCategories'
import dayjs from 'dayjs'
import { AzureFunctionClient } from '@/app/common/clients/azure-function-client'
import {
    DEFAULT_SORT_FIELD,
    DEFAULT_SORT_DIRECTION,
    ROWS_PER_PAGE_OPTIONS,
    getIdTokenFromMsal,
} from '@/app/common/utils'
import { GridColDef, GridColumnVisibilityModel, GridRowsProp, GridSortModel } from '@mui/x-data-grid-pro'
import { DataGridTable } from '../../PaginatedTable/DataGridTable'
import { useSourceEventTypes } from '@/app/common/hooks/action-item-management/useSourceEventTypes'
import {
    SourceEventTypeExternalIdClearEnum,
    SourceEventTypeExternalIdEnum,
} from '@/app/common/enums/SourceEventTypeEnum'
import { SourceEventStatusClearEnum, SourceEventStatusEnum } from '@/app/common/enums/SourceEventStatusEnum'
import { useDebounceFunction } from '@/app/common/hooks/general-functions/useDebounce'
import { ReportingUnit } from '@/app/common/models/reporting-unit'
import { useExportToExcel } from '@/app/common/hooks/export/useExportToExcel '
import GenericFieldTitle from '../../FieldsComponent/GenericFieldTitle'
import { createPublicClientApplication } from '@/app/common/factories/msal-factory'
import { environment } from '@/app/common/configurations/environment'
import { ClnButtonProps, MatIcon } from '@celanese/ui-lib'
import { GenerateStatusChip } from '../../StatusComponet'
import { useSnackbar } from '@/app/common/contexts/SnackbarContext'
import { useReportingLines } from '@/app/common/hooks/asset-hierarchy/useReportingLines'
import { translate } from '@/app/common/utils/generate-translate'
import { useLoading } from '@/app/common/contexts/LoadingContext'
import { determineActiveFilterCount } from '@/app/common/utils/active-filter-count'
import { AutocompleteOption } from '../../FieldsComponent/GenericAutocomplete'
import { UserRolesPermission } from '@celanese/celanese-sdk'
import { getNullsLastDateComparator, getNullsLastStringOrNumberComparator } from '@/app/common/utils/sort'
import { buildTranslations } from '@/app/common/utils/build-translations'

type EventTableProps = {
    buttomNewEvent?: boolean
    activeUser: UserRolesPermission
    showSourceEventForm?: () => void
    siteId?: string
}

export default function EventTable({ buttomNewEvent, activeUser, showSourceEventForm, siteId }: EventTableProps) {
    const router = useRouter()

    const { showSnackbar } = useSnackbar()
    const { showLoading } = useLoading()

    const theme = useTheme()
    const isMobile = useMediaQuery(theme.breakpoints.down('md'))

    const siteIds = useMemo(
        () => activeUser.selectedSites?.map((site) => site.siteId) || [],
        [activeUser.selectedSites]
    )

    const client = new AzureFunctionClient()

    const [currentPage, setCurrentPage] = useState<number>(0)
    const [memoryPage, setMemoryPage] = useState<number>(0)
    const [totalPages, setTotalPages] = useState<number>(1)

    const [sourceEventResponse, setSourceEventResponse] = useState<SourceEventResponse>()
    const [sourceEvent, setSourceEvent] = useState<SourceEvent[]>([])
    const [visibilitySourceEvent, setVisibilitySourceEvent] = useState<SourceEvent[]>([])

    const storedFilterInfo = sessionStorage.getItem(`event-home-filterInfo`)
    const parsedFilterInfo = storedFilterInfo ? JSON.parse(storedFilterInfo) : {}

    const [filterInfo, setFilterInfo] = useState<FilterInfoEventProps>({
        eventTypeExternalIds: Object.values(SourceEventTypeExternalIdEnum),
        statusExternalIds: Object.values(SourceEventStatusEnum),
        ownerExternalId: parsedFilterInfo?.filter
            ? parsedFilterInfo?.filter?.ownerExternalId
            : activeUser.externalId
              ? [activeUser.externalId]
              : [],
        equipmentExternalIds: parsedFilterInfo?.filter ? parsedFilterInfo.filter.equipmentExternalIds : [],
        functionalLocationExternalIds: parsedFilterInfo?.filter
            ? parsedFilterInfo.filter.functionalLocationExternalIds
            : [],
        pageSize: 10,
        ...parsedFilterInfo?.filter,
        ...(siteId && { reportingSiteExternalIds: [siteId] }),
        search: '',
    })

    const activeUserOption: AutocompleteOption = {
        value: activeUser.externalId ?? '',
        label: `${activeUser.lastName}, ${activeUser.firstName}`,
    }

    const [ownerNames, setOwnerNames] = useState<AutocompleteOption[]>(
        parsedFilterInfo?.ownerName ? parsedFilterInfo?.ownerName : [activeUserOption]
    )

    const [siteSpecificCategories, setSiteSpecificCategories] = useState<AutocompleteOption[]>(
        parsedFilterInfo?.siteSpecificCategories || []
    )
    const [equipments, setEquipments] = useState<AutocompleteOption[]>(parsedFilterInfo?.equipments || [])
    const [functionalLocations, setFunctionalLocations] = useState<AutocompleteOption[]>(
        parsedFilterInfo?.functionalLocations || []
    )
    const [reportingUnits, setReportingUnits] = useState<AutocompleteOption[]>(parsedFilterInfo?.reportingUnits || [])
    const [impactedReportingUnits, setImpactedReportingUnits] = useState<AutocompleteOption[]>(
        parsedFilterInfo?.impactedReportingUnits || []
    )

    const { eventStatus } = useSourceEventStatuses()
    const { categories } = useActionItemCategories()
    const { subCategories } = useActionItemSubCategories()
    const { sourceEventType } = useSourceEventTypes()
    const { lines: reportingLines } = useReportingLines({ siteIds })

    const [loadingTable, setLoadingTable] = useState<boolean>(false)
    const [isExportLoading, setIsExportLoading] = useState<boolean>(false)

    const [messages, setMessages] = useState<any>()
    const { locale } = useContext<TranslationContextState>(TranslationContext)

    useEffect(() => {
        const msalInstance = createPublicClientApplication()
        const getAuthToken = (): Promise<string> => getIdTokenFromMsal(msalInstance)
        const getIdToken = (): Promise<string> => getIdTokenFromMsal(msalInstance)

        getMessages(locale, getAuthToken, getIdToken, environment.userManagementAppCode).then((m) => {
            if (m) {
                setMessages(m)
            }
        })
    }, [locale])

    const [sortModel, setSortModel] = useState<GridSortModel>([
        {
            field: DEFAULT_SORT_FIELD,
            sort: DEFAULT_SORT_DIRECTION,
        },
    ])

    const initialStateColumnVisibilityModel: GridColumnVisibilityModel = useMemo(() => {
        return {
            sortReportingSite:
                Array.isArray(activeUser.applications?.[0]?.userSites) &&
                activeUser.applications?.[0]?.userSites?.length > 1,
            impactedUnit: false,
            reportingLine: false,
            description: false,
            equipment: false,
            functionalLocation: false,
        }
    }, [activeUser])

    const headCells: GridColDef[] = useMemo(
        () => [
            {
                field: 'act',
                headerName: translate('table.headers.actions'),
                sortable: false,
                filterable: false,
                width: 100,
                renderCell: (params) => {
                    const siteCode = params.row.reportingSiteCode
                    return (
                        <Box
                            sx={{
                                display: 'flex',
                                width: '100%',
                                height: '100%',
                                alignItems: 'center',
                                gap: '0.5rem',
                            }}
                            data-test="new_action_item_create_new_event_no_table-actions_content"
                            data-origin="aim"
                        >
                            <a
                                href={`/event-source/details/${siteCode}/${params.row.id}`}
                                target="_self"
                                style={{
                                    textDecoration: 'none',
                                    color: 'inherit',
                                    fontSize: 0,
                                    display: 'flex',
                                    alignItems: 'center',
                                }}
                            >
                                <MatIcon
                                    icon="visibility"
                                    color="primary.main"
                                    onClick={(e) => {
                                        e.preventDefault()
                                        showLoading(true)
                                        router.push(`/event-source/details/${siteCode}/${params.row.id}`)
                                    }}
                                />
                            </a>
                        </Box>
                    )
                },
                renderHeader: () => (
                    <span data-test="new_action_item_create_new_event_no-actions_table_sort" data-origin="aim">
                        {translate('table.headers.actions')}
                    </span>
                ),
            },
            {
                field: 'externalId',
                headerName: translate('table.headers.id'),
                filterable: false,
                flex: 1,
                minWidth: 250,
                renderCell: (params) => {
                    const siteCode = params.row.reportingSiteCode
                    return (
                        <a
                            href={`/event-source/details/${siteCode}/${params.value}`}
                            style={{ cursor: 'pointer', textDecoration: 'underline', color: 'inherit' }}
                            onClick={(e) => {
                                e.preventDefault()
                                showLoading(true)
                                router.push(`/event-source/details/${siteCode}/${params.value}`)
                            }}
                            data-test="new_action_item_create_new_event_no_table-id_content"
                            data-origin="aim"
                        >
                            {params.value}
                        </a>
                    )
                },
                renderHeader: () => (
                    <span data-test="new_action_item_create_new_event_no-id_table_sort" data-origin="aim">
                        {translate('table.headers.id')}
                    </span>
                ),
            },
            {
                field: 'sortTitle',
                headerName: translate('table.headers.title'),
                filterable: false,
                flex: 1,
                minWidth: 150,
                renderCell: (params) => (
                    <NoTranslate>
                        <span data-test="new_action_item_create_new_event_no_table-title_content" data-origin="aim">
                            {params.row.sortTitle}
                        </span>
                    </NoTranslate>
                ),
                renderHeader: () => (
                    <span data-test="new_action_item_create_new_event_no-title_table_sort" data-origin="aim">
                        {translate('table.headers.title')}
                    </span>
                ),
                getSortComparator: getNullsLastStringOrNumberComparator,
            },
            {
                field: 'description',
                headerName: translate('table.headers.description'),
                filterable: false,
                sortable: false,
                flex: 1,
                minWidth: 150,
                renderCell: (params) => (
                    <NoTranslate>
                        <span
                            data-test="new_action_item_create_new_event_no_table-description_content"
                            data-origin="aim"
                        >
                            {params.row.description}
                        </span>
                    </NoTranslate>
                ),
                renderHeader: () => (
                    <span data-test="new_action_item_create_new_event_no-description_table_sort" data-origin="aim">
                        {translate('table.headers.description')}
                    </span>
                ),
            },
            {
                field: 'sortNumberOfActionItems',
                headerName: translate('table.headers.numberOfActionsItems'),
                filterable: false,
                sortable: false,
                flex: 1,
                align: 'center',
                minWidth: 200,
                renderCell: (params) => (
                    <NoTranslate>
                        <span
                            data-test="new_action_item_create_new_event_no_table-number-of-action-items_content"
                            data-origin="aim"
                        >
                            {params.row.sortNumberOfActionItems}
                        </span>
                    </NoTranslate>
                ),
                renderHeader: () => (
                    <span
                        data-test="new_action_item_create_new_event_no-number-of-action-items_table_sort"
                        data-origin="aim"
                    >
                        {translate('table.headers.numberOfActionsItems')}
                    </span>
                ),
            },
            {
                field: 'sortNumberOfActionsCompleted',
                headerName: translate('table.headers.numberOfActionsCompleted'),
                filterable: false,
                sortable: false,
                flex: 1,
                align: 'center',
                minWidth: 250,
                renderCell: (params) => (
                    <NoTranslate>
                        <span
                            data-test="new_action_item_create_new_event_no_table-number-of-action-completed_content"
                            data-origin="aim"
                        >
                            {params.row.sortNumberOfActionsCompleted}
                        </span>
                    </NoTranslate>
                ),
                renderHeader: () => (
                    <span
                        data-test="new_action_item_create_new_event_no-number-of-action-completed_table_sort"
                        data-origin="aim"
                    >
                        {translate('table.headers.numberOfActionsCompleted')}
                    </span>
                ),
            },
            {
                field: 'sortStatus',
                headerName: translate('table.headers.status'),
                filterable: false,
                flex: 1,
                minWidth: 150,
                renderCell: (params) => (
                    <Box
                        sx={{
                            display: 'flex',
                            width: '100%',
                            height: '100%',
                            alignItems: 'center',
                        }}
                        data-test="new_action_item_create_new_event_no_table-status_content"
                        data-origin="aim"
                    >
                        <NoTranslate>
                            <GenerateStatusChip
                                statusId={params.value != '' ? params.value : SourceEventStatusClearEnum.inProgress}
                            />
                        </NoTranslate>
                    </Box>
                ),
                renderHeader: () => (
                    <span data-test="new_action_item_create_new_event_no-status_table_sort" data-origin="aim">
                        {translate('table.headers.status')}
                    </span>
                ),
                getSortComparator: getNullsLastStringOrNumberComparator,
            },
            {
                field: 'sortReportingSite',
                headerName: translate('table.headers.site'),
                filterable: false,
                sortable: false,
                flex: 1,
                minWidth: 150,
                renderCell: (params) => (
                    <NoTranslate>
                        <span data-test="event_table-site_content" data-origin="aim">
                            {params.row.sortReportingSite}
                        </span>
                    </NoTranslate>
                ),
                renderHeader: () => (
                    <span data-test="event-site_table_sort" data-origin="aim">
                        {translate('table.headers.site')}
                    </span>
                ),
            },
            {
                field: 'sortReportingUnit',
                headerName: translate('table.headers.unit'),
                filterable: false,
                flex: 1,
                minWidth: 150,
                renderCell: (params) => (
                    <NoTranslate>
                        <span
                            data-test="new_action_item_create_new_event_no_table-reporting_unit_content"
                            data-origin="aim"
                        >
                            {params.row.sortReportingUnit}
                        </span>
                    </NoTranslate>
                ),
                renderHeader: () => (
                    <span data-test="new_action_item_create_new_event_no-reporting_unit_table_sort" data-origin="aim">
                        {translate('table.headers.unit')}
                    </span>
                ),
                getSortComparator: getNullsLastStringOrNumberComparator,
            },
            {
                field: 'impactedUnit',
                headerName: translate('table.headers.impactedUnit'),
                filterable: false,
                sortable: false,
                flex: 1,
                minWidth: 150,
                renderCell: (params) => (
                    <NoTranslate>
                        <span
                            data-test="new_action_item_create_new_event_no_table-impacted_units_content"
                            data-origin="aim"
                        >
                            {params.row.impactedUnit}
                        </span>
                    </NoTranslate>
                ),
                renderHeader: () => (
                    <span data-test="new_action_item_create_new_event_no-impacted_units_table_sort" data-origin="aim">
                        {translate('table.headers.impactedUnit')}
                    </span>
                ),
            },
            {
                field: 'reportingLine',
                headerName: translate('stepper.form.reportingLine'),
                filterable: false,
                sortable: false,
                flex: 1,
                minWidth: 150,
                renderCell: (params) => (
                    <NoTranslate>
                        <span
                            data-test="new_action_item_create_new_event_no_table-reporting_line_content"
                            data-origin="aim"
                        >
                            {params.row.reportingLine}
                        </span>
                    </NoTranslate>
                ),
                renderHeader: () => (
                    <span data-test="new_action_item_create_new_event_no-reporting_line_table_sort" data-origin="aim">
                        {translate('stepper.form.reportingLine')}
                    </span>
                ),
            },
            {
                field: 'sortCategory',
                headerName: translate('table.headers.category'),
                filterable: false,
                flex: 1,
                minWidth: 150,
                renderCell: (params) => (
                    <NoTranslate>
                        <span data-test="new_action_item_create_new_event_no_table-category_content" data-origin="aim">
                            {params.row.sortCategory}
                        </span>
                    </NoTranslate>
                ),
                renderHeader: () => (
                    <span data-test="new_action_item_create_new_event_no-category_table_sort" data-origin="aim">
                        {translate('table.headers.category')}
                    </span>
                ),
                getSortComparator: getNullsLastStringOrNumberComparator,
            },
            {
                field: 'sortSubCategory',
                headerName: translate('table.headers.subcategoryOne'),
                filterable: false,
                flex: 1,
                minWidth: 150,
                renderCell: (params) => (
                    <NoTranslate>
                        <span
                            data-test="new_action_item_create_new_event_no_table-subcategory_1_content"
                            data-origin="aim"
                        >
                            {params.row.sortSubCategory}
                        </span>
                    </NoTranslate>
                ),
                renderHeader: () => (
                    <span data-test="new_action_item_create_new_event_no-subcategory_1_table_sort" data-origin="aim">
                        {translate('table.headers.subcategoryOne')}
                    </span>
                ),
                getSortComparator: getNullsLastStringOrNumberComparator,
            },
            {
                field: 'sortSiteSpecificCategory',
                headerName: translate('table.headers.subcategoryTwo'),
                filterable: false,
                flex: 1,
                minWidth: 150,
                renderCell: (params) => (
                    <NoTranslate>
                        <span
                            data-test="new_action_item_create_new_event_no_table-subcategory_2_content"
                            data-origin="aim"
                        >
                            {params.row.sortSiteSpecificCategory}
                        </span>
                    </NoTranslate>
                ),
                renderHeader: () => (
                    <span data-test="new_action_item_create_new_event_no-subcategory_2_table_sort" data-origin="aim">
                        {translate('table.headers.subcategoryTwo')}
                    </span>
                ),
                getSortComparator: getNullsLastStringOrNumberComparator,
            },
            {
                field: 'owner',
                headerName: translate('table.headers.primaryOwner'),
                sortable: false,
                filterable: false,
                flex: 1,
                minWidth: 150,
                renderCell: (params) => (
                    <NoTranslate>
                        <span data-test="new_action_item_create_new_event_no_table-owner_content" data-origin="aim">
                            {params.row.owner}
                        </span>
                    </NoTranslate>
                ),
                renderHeader: () => (
                    <span data-test="new_action_item_create_new_event_no-primary-owner_table_sort" data-origin="aim">
                        {translate('table.headers.primaryOwner')}
                    </span>
                ),
            },
            {
                field: 'assignmentDate',
                headerName: translate('table.headers.startDate'),
                filterable: false,
                flex: 1,
                minWidth: 150,
                renderCell: (params) => (
                    <NoTranslate>
                        <span
                            data-test="new_action_item_create_new_event_no_table-start_date_content"
                            data-origin="aim"
                        >
                            {params.row.assignmentDate}
                        </span>
                    </NoTranslate>
                ),
                renderHeader: () => (
                    <div>
                        <Tooltip title={'MM/DD/YYYY'} arrow={true} placement="top">
                            <span
                                data-test="new_action_item_create_new_event_no-start_date_table_sort"
                                data-origin="aim"
                            >
                                {translate('table.headers.startDate')}
                            </span>
                        </Tooltip>
                    </div>
                ),
                getSortComparator: getNullsLastDateComparator,
            },
            {
                field: 'displayDueDate',
                headerName: translate('table.headers.dueDate'),
                filterable: false,
                flex: 1,
                minWidth: 150,
                renderCell: (params) => (
                    <NoTranslate>
                        <span data-test="new_action_item_create_new_event_no_table-due_date_content" data-origin="aim">
                            {params.row.displayDueDate}
                        </span>
                    </NoTranslate>
                ),
                renderHeader: () => (
                    <div>
                        <Tooltip title={'MM/DD/YYYY'} arrow={true} placement="top">
                            <span data-test="new_action_item_create_new_event_no-due_date_table_sort" data-origin="aim">
                                {translate('table.headers.dueDate')}
                            </span>
                        </Tooltip>
                    </div>
                ),
                getSortComparator: getNullsLastDateComparator,
            },
            {
                field: 'isPrivate',
                headerName: translate('table.headers.private'),
                filterable: false,
                flex: 1,
                minWidth: 100,
                renderCell: (params) => (
                    <NoTranslate>
                        <span data-test="new_action_item_create_new_event_no_table-private_content" data-origin="aim">
                            {params.row.isPrivate ? translate('common.yes') : translate('common.no')}
                        </span>
                    </NoTranslate>
                ),
                renderHeader: () => (
                    <span data-test="new_action_item_create_new_event_no-private_table_sort" data-origin="aim">
                        {translate('table.headers.private')}
                    </span>
                ),
            },
            {
                field: 'functionalLocation',
                headerName: translate('table.headers.functionalLocation'),
                filterable: false,
                sortable: false,
                flex: 1,
                minWidth: 200,
                renderCell: (params) => (
                    <NoTranslate>
                        <span
                            data-test="new_action_item_create_new_event_functional_location_content"
                            data-origin="aim"
                        >
                            {params.row.functionalLocation}
                        </span>
                    </NoTranslate>
                ),
                renderHeader: () => (
                    <span data-test="new_action_item_create_new_event_functional_location_table" data-origin="aim">
                        {translate('table.headers.functionalLocation')}
                    </span>
                ),
            },
            {
                field: 'equipment',
                headerName: translate('table.headers.equipment'),
                filterable: false,
                sortable: false,
                flex: 1,
                minWidth: 300,
                renderCell: (params) => (
                    <NoTranslate>
                        <span data-test="new_action_item_create_new_event_equipment_content" data-origin="aim">
                            {params.row.equipment}
                        </span>
                    </NoTranslate>
                ),
                renderHeader: () => (
                    <span data-test="new_action_item_create_new_event_equipment_table" data-origin="aim">
                        {translate('table.headers.equipment')}
                    </span>
                ),
            },
        ],
        [locale, messages, sourceEvent]
    )

    const tableRows: GridRowsProp = useMemo(() => {
        function convertEventsToRows(items: SourceEvent[]) {
            return items.map((item) => ({
                id: item.externalId,
                externalId: item.externalId,
                sortTitle: item.title,
                description: item.description ?? '-',
                sortNumberOfActionItems: item.actionsCount ?? 0,
                sortNumberOfActionsCompleted: item.actionsCompletedCount ?? 0,
                sortStatus: item.status?.externalId ?? SourceEventStatusClearEnum.inProgress,
                sortReportingSite: item?.reportingSite?.name ?? '',
                reportingSiteCode: item?.reportingSite?.siteCode ?? '',
                sortReportingUnit: item.reportingUnit?.description ?? '-',
                impactedUnit: item.reportingUnits?.map((unit: ReportingUnit) => unit.description).join(', ') || '-',
                reportingLine: item.reportingLine?.description ?? '-',
                sortCategory: item.category?.name,
                sortSubCategory: item.subCategory?.name ?? '-',
                sortSiteSpecificCategory: item.siteSpecificCategory?.name ?? '-',
                owner: item.owner ? `${item.owner?.user?.lastName}, ${item.owner?.user?.firstName}` : '',
                assignmentDate: item.assignmentDate ? dayjs.utc(item.assignmentDate).format('MM/DD/YYYY') : '-',
                displayDueDate: item.displayDueDate ? dayjs.utc(item.displayDueDate).format('MM/DD/YYYY') : '-',
                isPrivate: item.isPrivate ?? false,
                functionalLocation: item.functionalLocations[0]?.name ?? '-',
                equipment: item.equipments[0]
                    ? `${item.equipments[0].number} | ${item.equipments[0].name} | ${item.equipments[0].description}`
                    : '-',
            }))
        }

        const eventsRows =
            visibilitySourceEvent != null && visibilitySourceEvent.length > 0
                ? convertEventsToRows(visibilitySourceEvent)
                : []

        return eventsRows
    }, [visibilitySourceEvent])

    const columnNames = {
        externalId: translate('table.headers.id'),
        title: translate('table.headers.title'),
        description: translate('table.headers.description'),
        actionsCount: translate('table.headers.numberOfActionsItems'),
        actionsCompletedCount: translate('table.headers.numberOfActionsCompleted'),
        status: translate('table.headers.status'),
        reportingSite: translate('table.headers.site'),
        reportingUnit: translate('table.headers.unit'),
        reportingLine: translate('stepper.form.reportingLine'),
        category: translate('table.headers.category'),
        subCategory: translate('table.headers.subcategoryOne'),
        siteSpecificCategory: translate('table.headers.subcategoryTwo'),
        owner: translate('table.headers.primaryOwner'),
        assignmentDate: translate('table.headers.startDate'),
        dueDate: translate('table.headers.dueDate'),
        isPrivate: translate('table.headers.private'),
    }

    const statusTranslations = buildTranslations(eventStatus, 'status.fullName')
    const categoryTranslations = buildTranslations(categories, 'category')
    const subCategoryTranslations = buildTranslations(subCategories, 'subCategory1')
    const booleanTranslations = {
        true: translate('common.yes'),
        false: translate('common.no'),
    }

    const { exportToExcel } = useExportToExcel({
        client,
        columnNames,
        fileName: 'Action Item Management Event Export',
        filterInfo,
        activeUser,
        siteIds,
        worksheetName: 'Event',
        translations: {
            ...categoryTranslations,
            ...statusTranslations,
            ...subCategoryTranslations,
            ...booleanTranslations,
        },
        setIsExportLoading,
    })

    const buttons: ClnButtonProps[] = buttomNewEvent
        ? [
              {
                  label: isMobile ? '' : translate('table.newEvent'),
                  startIcon: isMobile ? <MatIcon icon="add" /> : undefined,
                  variant: 'contained',
                  sxProps: isMobile
                      ? {
                            color: 'primary.main  !important',
                            minWidth: '12px !important',
                            '& .MuiButton-startIcon': {
                                marginRight: 0,
                            },
                        }
                      : undefined,
                  onClick: () => {
                      showSourceEventForm && showSourceEventForm()
                      localStorage.setItem('isEditForm', 'true')
                  },
              },
              {
                  label: isMobile ? '' : isExportLoading ? translate('table.exporting') : translate('table.download'),
                  variant: 'text',
                  startIcon: isExportLoading ? <MatIcon icon="sync" /> : <MatIcon icon="download" />,
                  sxProps: isMobile
                      ? {
                            color: 'primary.main  !important',
                            minWidth: '12px !important',
                            '& .MuiButton-startIcon': {
                                marginRight: 0,
                            },
                        }
                      : undefined,
                  onClick: () => {
                      setIsExportLoading(true)
                      exportToExcel()
                  },
                  disabled: sourceEventResponse?.totalSourceEvent === 0 || isExportLoading,
              },
          ]
        : []

    const setValueFilterInfo = useCallback(
        (value: FilterInfoEventProps) => {
            setFilterInfo(value)
            sessionStorage.setItem(
                `event-home-filterInfo`,
                JSON.stringify({
                    filter: value,
                    ownerName: ownerNames,
                    equipments,
                    functionalLocations,
                    reportingUnits,
                    impactedReportingUnits,
                    siteSpecificCategories,
                })
            )
        },
        [ownerNames, equipments, functionalLocations, reportingUnits, impactedReportingUnits]
    )

    const customPopoverContent = useMemo(() => {
        return (
            <EventFilter
                onFilter={(dataInfo) => {
                    resetAllPages()
                    setValueFilterInfo(dataInfo)
                }}
                status={eventStatus}
                reportingLine={reportingLines}
                categories={categories}
                subCategories={subCategories}
                sourceEventType={sourceEventType}
                defaultFilter={filterInfo}
                defaultOwnerNames={ownerNames}
                defaultSiteSpecificCategories={siteSpecificCategories}
                defaultFunctionalLocations={functionalLocations}
                defaultEquipments={equipments}
                defaultReportingUnits={reportingUnits}
                defaultImpactedReportingUnits={impactedReportingUnits}
                setDefaultOwnerNames={setOwnerNames}
                setDefaultSiteSpecificCategories={setSiteSpecificCategories}
                setDefaultFunctionalLocations={setFunctionalLocations}
                setDefaultEquipments={setEquipments}
                setDefaultReportingUnits={setReportingUnits}
                setDefaultImpactedReportingUnits={setImpactedReportingUnits}
                activeUser={activeUser}
                siteId={siteId}
            />
        )
    }, [
        translate,
        eventStatus,
        reportingLines,
        categories,
        subCategories,
        siteSpecificCategories,
        sourceEventType,
        filterInfo,
        siteId,
    ])

    const activeFiltersCount = useMemo(() => {
        const filters = {
            statusExternalIds: filterInfo.statusExternalIds,
            dueDateGte: filterInfo.dueDateGte,
            dueDateLt: filterInfo.dueDateLt,
            eventTypeExternalIds: filterInfo.eventTypeExternalIds,
            reportingSiteExternalIds: filterInfo.reportingSiteExternalIds,
            reportingUnitExternalIds: filterInfo.reportingUnitExternalIds,
            reportingLines: filterInfo.reportingLineExternalIds,
            impactedReportingUnitExternalIds: filterInfo.impactedReportingUnitExternalIds,
            categoryExternalIds: filterInfo.categoryExternalIds,
            subcategoryExternalIds: filterInfo.subcategoryExternalIds,
            siteSpecificCategoryExternalIds: filterInfo.siteSpecificCategoryExternalIds,
            ownerExternalId: filterInfo.ownerExternalId,
            externalIdPrefix: filterInfo.externalIdPrefix,
            titlePrefix: filterInfo.titlePrefix,
            equipmentIds: filterInfo.equipmentExternalIds,
            functionalLocationIds: filterInfo.functionalLocationExternalIds,
        }

        const count = determineActiveFilterCount(filters)
        return count
    }, [filterInfo])

    const resetAllPages = () => {
        setMemoryPage(0)
        setCurrentPage(0)
        setTotalPages(1)
    }

    const handleError = (err: any) => {
        const errorMessage = err instanceof Error ? err.message : `${translate('alerts.unexpectedErrorOcurred')}`
        showSnackbar(`${translate('alerts.unexpectedErrorOcurred')}: ${errorMessage}`, 'error', 'event-table')
    }

    const fetchEvent = useCallback(async () => {
        try {
            const result: SourceEventResponse = await client.getSourceEvent({
                ...filterInfo,
                reportingSiteExternalId: filterInfo.reportingSiteExternalIds ?? siteIds,
                activeUserEmail: activeUser.email,
                ownerExternalId:
                    filterInfo.ownerExternalId && filterInfo.ownerExternalId?.length > 0
                        ? filterInfo.ownerExternalId.map((owner) => `UserAzureAttribute_${owner}`)
                        : undefined,
                eventTypeExternalIds:
                    filterInfo.eventTypeExternalIds ?? Object.values(SourceEventTypeExternalIdClearEnum),
                statusExternalIds: filterInfo.statusExternalIds ?? Object.values(SourceEventStatusClearEnum),
            })

            const resultData = result.data as SourceEvent[]
            const filteredResultData = filterInfo.cursor
                ? resultData.filter(
                      (item) => !sourceEvent.some((existingItem) => existingItem.externalId === item.externalId)
                  )
                : resultData

            setSourceEventResponse(result)

            const allItems = filterInfo.cursor ? [...sourceEvent, ...filteredResultData] : filteredResultData
            setSourceEvent(allItems)
            setVisibilitySourceEvent(filteredResultData)
        } catch (err) {
            handleError(err)
        } finally {
            setLoadingTable(false)
        }
    }, [filterInfo])

    const debouncedFetchEvent = useCallback(useDebounceFunction(fetchEvent, 800), [fetchEvent])

    useEffect(() => {
        if (filterInfo !== undefined) {
            if (loadingTable) return

            setLoadingTable(true)
            debouncedFetchEvent()
        }
    }, [debouncedFetchEvent])

    useEffect(() => {
        const pages = Math.ceil(sourceEvent.length / filterInfo.pageSize)
        setTotalPages(sourceEventResponse?.hasNextPage ? pages + 1 : pages)
    }, [sourceEventResponse])

    useEffect(() => {
        sessionStorage.setItem(
            `event-home-filterInfo`,
            JSON.stringify({
                filter: filterInfo,
                ownerName: ownerNames,
                functionalLocations,
                equipments,
                reportingUnits,
                impactedReportingUnits,
                siteSpecificCategories,
            })
        )
    }, [ownerNames, functionalLocations, equipments, reportingUnits, impactedReportingUnits])

    useEffect(() => {
        const visibleColumns = [
            'act',
            'externalId',
            'sortTitle',
            'sortNumberOfActionItems',
            'sortNumberOfActionsCompleted',
            'sortStatus',
            'sortReportingUnit',
            'sortCategory',
            'sortSubCategory',
            'sortSiteSpecificCategory',
            'owner',
            'assignmentDate',
            'displayDueDate',
            'isPrivate',
        ]

        if (activeUser.applications?.[0]?.userSites?.length > 1) {
            const index = visibleColumns.indexOf('sortReportingUnit')
            if (index !== -1) {
                visibleColumns.splice(index + 1, 0, 'sortReportingSite')
            }
        }

        const allActionColumns = headCells.map((x) => x.field)

        const defaultVisibilityModel = allActionColumns.reduce(
            (acc, column) => {
                if (!visibleColumns.includes(column)) {
                    acc[column] = false
                }
                return acc
            },
            {} as Record<string, boolean>
        )

        const defaultColumnOrder = [
            ...visibleColumns,
            ...allActionColumns.filter((col) => !visibleColumns.includes(col)),
        ]

        const visibilityKey = 'visibilityModelChange-event-home-filterInfo-tab'
        const columnOrderKey = 'columnOrderIds-event-home-filterInfo-tab'

        if (!localStorage.getItem(visibilityKey)) {
            localStorage.setItem(visibilityKey, JSON.stringify(defaultVisibilityModel))
        }

        if (!localStorage.getItem(columnOrderKey)) {
            localStorage.setItem(columnOrderKey, JSON.stringify(defaultColumnOrder))
        }
    }, [])

    return (
        <Box>
            <GenericFieldTitle
                fieldName={
                    translate('source-event.eventsCount') +
                    ' ' +
                    `(${sourceEventResponse?.totalSourceEvent ?? 0} ${translate('source-event.events')})`
                }
                isSectionSubtitle
            />
            <NoTranslate>
                <DataGridTable
                    id="event-home-filterInfo-tab"
                    isLoading={loadingTable}
                    totalPages={totalPages}
                    rows={tableRows}
                    initialColumnDefs={headCells}
                    initialStateColumnVisibilityModel={initialStateColumnVisibilityModel}
                    customButtons={buttons}
                    currentPage={currentPage}
                    setCurrentPage={(value: number) => {
                        setCurrentPage(value)
                        if (value > memoryPage) {
                            setMemoryPage(value)
                            if (sourceEventResponse?.cursor) {
                                const newFilterInfo = { ...filterInfo, cursor: sourceEventResponse.cursor }
                                setValueFilterInfo(newFilterInfo)
                            }
                        } else {
                            setVisibilitySourceEvent(
                                sourceEvent.slice(
                                    value * filterInfo.pageSize,
                                    value * filterInfo.pageSize + filterInfo.pageSize
                                )
                            )
                        }
                    }}
                    rowsPerPageOptions={ROWS_PER_PAGE_OPTIONS}
                    rowsPerPage={filterInfo.pageSize}
                    setRowsPerPage={(value: number) => {
                        resetAllPages()
                        const newFilterInfo = {
                            ...filterInfo,
                            pageSize: value,
                            cursor: undefined,
                        }
                        setValueFilterInfo(newFilterInfo)
                    }}
                    onSearchSubmit={(value: string) => {
                        resetAllPages()
                        const newFilterInfo = {
                            ...filterInfo,
                            search: value,
                            cursor: undefined,
                        }
                        setValueFilterInfo(newFilterInfo)
                    }}
                    searchHelpMessage={translate('table.search.searchForTitle')}
                    customPopoverContent={customPopoverContent}
                    sortModel={sortModel}
                    setSortModel={(value: GridSortModel) => {
                        resetAllPages()
                        setSortModel(value)
                        const newFilterInfo = {
                            ...filterInfo,
                            cursor: undefined,
                            sortBy: value?.[0]?.sort ? value?.[0]?.field ?? undefined : undefined,
                            direction: value?.[0]?.sort ? value[0].sort.toUpperCase() : undefined,
                        }
                        setValueFilterInfo(newFilterInfo)
                    }}
                    activeFiltersCount={activeFiltersCount}
                ></DataGridTable>
            </NoTranslate>
        </Box>
    )
}
