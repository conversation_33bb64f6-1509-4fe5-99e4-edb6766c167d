import os
import sys
from typing import Any
import asyncio


script_dir = os.path.dirname(os.path.abspath(__file__))
project_dir = os.path.dirname(script_dir)
sys.path.append(project_dir)

from action_service import ActionService
from infra.action_item_client_factory import ActionItemClientFactory
from clients.actions.requests import UpdateActionRequest

# region: TEST
request: dict[str, Any] = {
    "externalId": "ACT-20241017121047-0000",
    "space": "AIM-WAS-ALL-DAT",
    "activeUserEmail": "<EMAIL>",
    "reportingSiteId": "STS-WAS",
    "actionItemKind": "ACTK-recurring",
    "attachmentsIds": [],
    "filesIdsToDelete": [],
    "edgesIdsToDelete": [
        "<EMAIL>"
    ],
    "title": "Test Lari Recurring - Custom",
    "description": "Test Lari Recurring - Custom test",
    "ownerId": "<EMAIL>",
    "reportingUnitId": "UNT-WASACV",
    "reportingLocationId": "LOC-WASACVPKG",
    "links": [],
    "linksIdsToDelete": [],
    "sourceInformation": "Recurring",
    "categoryId": "ACTC-General",
    "subCategoryId": "ACSC-Administrative",
    "siteSpecificCategoryId": "SSC-STS-WAS-1st",
    "voeActionItem": "",
    "evidenceRequired": False,
    "challengeEdit": False,
    "assigneesIds": ["<EMAIL>"],
    "recurrenceInstance": {
        "recurrenceType": "RCT-custom",
        "startDate": "2024-10-29",
        "weekDays": [],
        "months": [],
        "quarters": [],
        "nextDates": [],
        "externalId": "RCI-20241017121047-0002",
        "space": "AIM-WAS-ALL-DAT",
    },
}
# endregion

# region: 1 - TITLE, DESCRIPTION, UNIT, LOCATION AND SUBCATEGORY BEING FILLED -> CHECK
request = {
    "externalId": "SEVT-20241018190801-0002-0001",
    "space": "AIM-WAS-ALL-DAT",
    "title": "Title Edit Refactoring - Normal Fields Test - 01",
    "description": "Description Edit Refactoring - Normal Fields Test - 01",
    "reportingUnitId": "UNT-WASFIL",
    "reportingLocationId": "LOC-WASFILLEV",
    "subCategoryId": "ACSC-Other",
}
# endregion

# region: 2 - REMOVE LOCATION -> CHECK
request = {
    "externalId": "SEVT-20241018190801-0002-0001",
    "space": "AIM-WAS-ALL-DAT",
    "reportingLocationId": None,
}
# endregion

# region: 3 - ADD APPROVER (NO APPROVAL WORKFLOW) -> CHECK
request = {
    "externalId": "SEVT-20241018190801-0002-0001",
    "space": "AIM-WAS-ALL-DAT",
    "approverId": "<EMAIL>",
}
# endregion

# region: 4 -> REMOVE APPROVER -> CHECK
request = {
    "externalId": "SEVT-20241018190801-0002-0001",
    "space": "AIM-WAS-ALL-DAT",
    "approverId": None,
}
# endregion

# region: 5 - ADD APPROVER AND VERIFIER (NO APPROVAL WORKFLOW) -> CHECK
request = {
    "externalId": "SEVT-20241018190801-0002-0001",
    "space": "AIM-WAS-ALL-DAT",
    "approverId": "<EMAIL>",
    "verifierId": "<EMAIL>",
}
# endregion

# region: 6 - REMOVE VERIFIER (EXISTING APPROVAL AND VERIFIER) -> CHECK
request = {
    "externalId": "SEVT-20241018190801-0002-0001",
    "space": "AIM-WAS-ALL-DAT",
    "verifierId": None,
}
# endregion

# region: 7 - EMPTY REQUEST -> CHECK
request = {
    "externalId": "SEVT-20241018190801-0002-0001",
    "space": "AIM-WAS-ALL-DAT",
}
# endregion

# region: 8 - ATTACHMENTS - REMOVE ONE OF THEM -> CHECK
request = {
    "externalId": "ACT-20241112141959-0001",
    "attachmentIds": [
        "ACTATT-Action Item Managemant Home Export (8).xlsx-20241112111955-3",
        "ACTATT-Action Item Managemant Home Export (10).xlsx-20241112111955-1",
    ],
    "activeUserEmail": "<EMAIL>",
}
# endregion

# region: 9 - ATTACHMENTS - ADD ONE AND REMOVE OTHER -> CHECK
request = {
    "externalId": "ACT-20241112141959-0001",
    "attachmentIds": [
        "ACTATT-Action Item Managemant Home Export (8).xlsx-20241112111955-3",
        "ACTATT-dog.png-20240902145457-1",
    ],
    "activeUserEmail": "<EMAIL>",
}
# endregion

# region: 10 - ATTACHMENTS - REMOVE ONE THAT IS IN MORE THAN ONE ACTION -> CHECK
request = {
    "externalId": "ACT-20241112141959-0001",
    "attachmentIds": [
        "ACTATT-Action Item Managemant Home Export (8).xlsx-20241112111955-3",
    ],
    "activeUserEmail": "<EMAIL>",
}
# endregion

# region: 11 - PRIVATE/VIEWS - ADD APPROVER (NO APPROVAL WORKFLOW / NO SOURCE EVENT) -> CHECK
request = {
    "externalId": "ACT-20241029205156-0000",
    "activeUserEmail": "<EMAIL>",
    "approverId": "<EMAIL>",
}
# endregion

# region: 12 - PRIVATE/VIEWS - ADD VERIFIER (NO SOURCE EVENT) -> CHECK
request = {
    "externalId": "ACT-20241029205156-0000",
    "activeUserEmail": "<EMAIL>",
    "verifierId": "<EMAIL>",
}
# endregion

# region: 13 - PRIVATE/VIEWS - REMOVE VERIFIER (NO SOURCE EVENT) -> CHECK
request = {
    "externalId": "ACT-20241029205156-0000",
    "activeUserEmail": "<EMAIL>",
    "verifierId": None,
}
# endregion

# region: 14 - PRIVATE/VIEWS - REMOVE APPROVER / DELETE APPROVAL WORKFLOW (NO SOURCE EVENT) -> CHECK
request = {
    "externalId": "ACT-20241029205156-0000",
    "activeUserEmail": "<EMAIL>",
    "approverId": None,
}
# endregion

# region: 15 - PRIVATE/VIEWS - ADD APPROVER AND VIEW USERS -> CHECK
request = {
    "externalId": "ACT-20241029205156-0000",
    "activeUserEmail": "<EMAIL>",
    "approverId": "<EMAIL>",
    "viewUsers": ["<EMAIL>"],
}
# endregion

# region: 16 - PRIVATE/VIEWS - ADD VIEW USERS -> CHECK
request = {
    "externalId": "ACT-20241029205156-0000",
    "activeUserEmail": "<EMAIL>",
    "viewUsers": [
        "<EMAIL>",
        "<EMAIL>",
    ],
}
# endregion

# region: 17 - PRIVATE/VIEWS - REMOVE ONE VIEW USER BUT ADD IT AS APPROVER -> CHECK
request = {
    "externalId": "ACT-20241029205156-0000",
    "activeUserEmail": "<EMAIL>",
    "approverId": "<EMAIL>",
    "viewUsers": [
        "<EMAIL>",
    ],
}
# endregion

# region: 18 - PRIVATE/VIEWS - CHANGE OWNER/ASSIGNEE -> CHECK
request = {
    "externalId": "SEVT-20241015150850-0000-0001",
    "activeUserEmail": "<EMAIL>",
    "ownerId": "<EMAIL>",
    "assignedToId": "<EMAIL>",
}
# endregion

# region: 19 - PRIVATE/VIEWS - ADD VIEW USERS -> CHECK
request = {
    "externalId": "SEVT-20241015150850-0000-0001",
    "activeUserEmail": "<EMAIL>",
    "viewUsers": [
        "<EMAIL>",
        "<EMAIL>",
    ],
}
# endregion

# region: 20 - PRIVATE/VIEWS - CLEAR VIEW USERS -> CHECK
request = {
    "externalId": "SEVT-20241015150850-0000-0001",
    "activeUserEmail": "<EMAIL>",
    "viewUsers": None,
}
# endregion


# region: 21 - PRIVATE/VIEWS - CLEAR VIEW ROLES (NO SELECTED ROLES) -> CHECK
request = {
    "externalId": "SEVT-20241015150850-0000-0001",
    "activeUserEmail": "<EMAIL>",
    "viewRoles": None,
}
# endregion

# region: 22 - PRIVATE/VIEWS - ADD VIEW ROLES -> CHECK
request = {
    "externalId": "SEVT-20241015150850-0000-0001",
    "activeUserEmail": "<EMAIL>",
    "viewRoles": [
        "2b7f32d2-8645-4d90-b121-b723b2843089",
        "9ff5d040-9124-4e59-aea0-d146b47864d3",
    ],
}
# endregion

# region: 23 - PRIVATE/VIEWS - REMOVE VIEW ROLES -> CHECK
request = {
    "externalId": "SEVT-20241015150850-0000-0001",
    "activeUserEmail": "<EMAIL>",
    "viewRoles": [
        "2b7f32d2-8645-4d90-b121-b723b2843089",
    ],
}
# endregion

# region: 24 - PRIVATE/VIEWS - ADD APPROVER, REMOVE ROLE, ADD TEAM -> CHECK
request = {
    "externalId": "SEVT-20241015150850-0000-0001",
    "activeUserEmail": "<EMAIL>",
    "approverId": "<EMAIL>",
    "viewRoles": [],
    "viewTeams": ["34752f3d-c3ec-4a5a-b604-d8b88a2c251c"],
}
# endregion


# region: 25 - PRIVATE/VIEWS - REMOVE TEAMS -> CHECK
request = {
    "externalId": "SEVT-20241015150850-0000-0001",
    "activeUserEmail": "<EMAIL>",
    "viewRoles": [],
    "viewTeams": [],
}
# endregion

# region: 26 - CANCEL/DELETE (FRONT)
# endregion

# region: 27 - UPSERT ASSIGNEE REQUEST (FRONT)
# endregion

# region: 28 - TEST WITH LARI
request = {
    "externalId": "ACT-20250115121132-0001",
    "activeUserEmail": "<EMAIL>",
    "reportingSiteId": "STS-BIS",
    "challengeEdit": False,
    "reportingLocationId": None,
    "reportingLineId": None,
    "sourceInformation": None,
    "siteSpecificCategoryId": "SSC-STS-BIS-20250219123757-0000",
    "priority": None,
    "views": [],
}

# endregion

# region: PRIVATE/VIEWS - WITH/WITHOUT APPROVER BEING CHANGED

# endregion

# region: LINKS - ADD LINK / REMOVE LINK / REMOVE ALL LINKS
# endregion

# region: RECURRENCE - JUST CHANGE (USE TEST AS EXAMPLE)
# endregion

request = {
    "activeUserEmail": "<EMAIL>",
    "reportingSiteId": "STS-WAS",
    **request,
}

update = UpdateActionRequest.model_validate(request)

ext_id, errors = asyncio.run(
    ActionService(ActionItemClientFactory.retriever()).update_action(update)
)

print(ext_id)
print(errors)

# CASES
# 1 -> title and description -> CHECK
# 2 ->
