'use client'
import { Box, Typography } from '@mui/material'
import * as S from './styles'
import { ActionStatusExternalIdClearEnum } from '@/app/common/enums/ActionItemStatusEnum'
import { translate } from '@/app/common/utils/generate-translate'

type ActionItemTableProps = {
    statusId: string
    title?: string
}

function GenerateStatusChip({ statusId, title }: ActionItemTableProps) {
    function getStatusSuffix(statusId: string) {
        const parts = statusId?.split('-')
        return parts?.length > 1 ? parts[1] : parts?.[0] ?? ActionStatusExternalIdClearEnum.Assigned
    }

    const status = getStatusSuffix(statusId)

    const statusMapping: Record<string, typeof S.WarningStyle> = {
        [translate('status.draft')]: S.WarningStyle,
        [translate('status.OnHold')]: S.WarningStyle,
        [translate('status.pendingApproval')]: S.WarningStyle,
        [translate('status.pendingVerification')]: S.WarningStyle,

        [translate('status.assigned')]: S.PrimaryMainStyle,
        [translate('status.dueDateExtensionPeriod')]: S.PrimaryMainStyle,
        [translate('status.reassignmentPeriod')]: S.PrimaryMainStyle,
        [translate('status.challengePeriod')]: S.PrimaryMainStyle,

        [translate('status.completed')]: S.SuccessStyle,
        [translate('status.Completed')]: S.SuccessStyle,
        [translate('status.active')]: S.SuccessStyle,

        [translate('status.approvalRejected')]: S.ErrorStyle,
        [translate('status.verificationRejected')]: S.ErrorStyle,
        [translate('status.deleted')]: S.ErrorStyle,
        [translate('status.cancelled')]: S.ErrorStyle,
        [translate('status.Cancelled')]: S.ErrorStyle,

        [translate('status.private')]: S.NeutralStyle,
        [translate('status.viewOnly')]: S.NeutralStyle,
        [translate('status.inactive')]: S.NeutralStyle,

        [translate('status.InProgress')]: S.InfoStyle,
    }

    return (
        <Box
            sx={{
                ...S.Box,
                backgroundColor: status
                    ? statusMapping[`${translate(`status.${status}`)}`]?.backgroundColor
                    : 'text.disabled',
            }}
        >
            <Typography
                fontSize={12}
                color={status ? statusMapping[`${translate(`status.${status}`)}`]?.color : 'primary.main'}
                fontWeight={500}
            >
                {title ?? `${translate(`status.${status}`)}`}
            </Typography>
        </Box>
    )
}

export { GenerateStatusChip }
