import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
import { Entity } from '../models/common'

dayjs.extend(utc)

export type CogniteSdkEntity = Entity & { __typename?: string; [key: string]: any }

export function generateNewExternalId(systemCode: string, index: number) {
    const dateFormat = dayjs().format('YYYYMMDDHHmmss')
    const paddedIndex = String(index).padStart(4, '0')
    return `${systemCode}-${dateFormat}-${paddedIndex}`
}
