REFERENCE_DATA_FOLDER = "reference_data"
RAW_DB = "AIM-COR"
PROT_SECURITY_CATEGORY_NAME = "AIM-COR-ALL-PROT"
COR_DATA_SET_EXTERNAL_ID = "AIM-COR-ALL-DAT"

REFERENCE_DATA_TRANSFORMATIONS = [
    ("tr-AIM-COR-ALL-USR", True),
    ("tr-AIM-COR-ALL-USRAZRATR", True),
    ("tr-AIM-COR-ALL-USRCMP", False),
    ("tr-AIM-COR-ALL-RAW-ICAP-MAP-USER", False),
]


VALIDATION_INFO = [
    ("tr-AIM-COR-ALL-RAW-ICAP-VAL-ACTION", "ICAP-VAL-Action", False, True, True),
    ("tr-AIM-COR-ALL-RAW-ICAP-VAL-EVENT", "ICAP-VAL-Event", False, True, True),
    (
        "tr-AIM-COR-ALL-RAW-ICAP-VAL-MISSING-USER",
        "ICAP-VAL-MissingUser",
        False,
        False,
        True,
    ),
    (
        "tr-AIM-COR-ALL-RAW-ICAP-VAL-MISSING-ASSET",
        "ICAP-VAL-MissingAsset",
        False,
        False,
        True,
    ),
    (
        "tr-AIM-COR-ALL-RAW-ICAP-VAL-MISSING-ACTION",
        "ICAP-VAL-MissingAction",
        False,
        True,
        True,
    ),
    (
        "tr-AIM-COR-ALL-RAW-ICAP-VAL-MISSING-EVENT",
        "ICAP-VAL-MissingEvent",
        False,
        True,
        True,
    ),
    (
        "tr-AIM-COR-ALL-RAW-ICAP-STG-ACTION-DEL",
        "ICAP-STG-Action-DEL",
        False,
        False,
        False,
    ),
]
