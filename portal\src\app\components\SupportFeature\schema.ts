import { RequestTypeEnum } from '@/app/common/enums/RequestTypeEnum'
import * as z from 'zod'

export const fileSchema = typeof File !== 'undefined' ? z.instanceof(File) : z.any()

export const supportFeatureValidationSchema = z
    .object({
        subjectTitle: z.string().min(4, ''),

        typeOfRequest: z.string().default(RequestTypeEnum.INCIDENT),
        description: z.string().optional(),

        uploadedFiles: z.array(fileSchema).optional(),
        // incident
        impactOnWork: z.string().optional(),
        impactedSites: z.array(z.string()).optional(),
        natureOfIncident: z.string().optional(),
        previouslyFunctioning: z.string().optional(),
        expectedFunctionality: z.string().optional(),
        incidentStepByStep: z.string().optional(),

        //  improvement
        action: z.string().optional(),
        objective: z.string().optional(),
    })
    .superRefine((data, ctx) => {
        if (data.typeOfRequest === RequestTypeEnum.INCIDENT) {
            if (!data.natureOfIncident) {
                ctx.addIssue({
                    code: z.ZodIssueCode.custom,
                    path: ['natureOfIncident'],
                })
            }
            if (!data.impactedSites || data.impactedSites.length === 0) {
                ctx.addIssue({
                    code: z.ZodIssueCode.custom,
                    path: ['impactedSites'],
                })
            }
            if (!data.impactOnWork || data.impactOnWork.length === 0) {
                ctx.addIssue({
                    code: z.ZodIssueCode.custom,
                    path: ['impactOnWork'],
                })
            }
            if (!data.previouslyFunctioning) {
                ctx.addIssue({
                    code: z.ZodIssueCode.custom,
                    path: ['previouslyFunctioning'],
                })
            }
            if (!data.expectedFunctionality) {
                ctx.addIssue({
                    code: z.ZodIssueCode.custom,
                    path: ['expectedFunctionality'],
                })
            }
        }
        if (data.typeOfRequest === RequestTypeEnum.IMPROVEMENT) {
            if (!data.action) {
                ctx.addIssue({
                    code: z.ZodIssueCode.custom,
                    path: ['action'],
                })
            }
            if (!data.objective) {
                ctx.addIssue({
                    code: z.ZodIssueCode.custom,
                    path: ['objective'],
                })
            }
        }
    })
export type SupportFeatureValidationSchema = z.infer<typeof supportFeatureValidationSchema>
