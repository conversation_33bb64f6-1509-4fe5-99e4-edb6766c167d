import { styled } from '@mui/material'
import { CSSObject } from 'styled-components'

export const drawerContainer: CSSObject = {
    width: '25vw !important',
    padding: '42px !important',
    '& > div:nth-of-type(3)': {
        height: '100%',
    },
}

export const drawerContentContainer: CSSObject = {
    display: 'grid',
    gridTemplateRows: 'auto auto auto 1fr auto',
    gap: '16px',
    height: '100%',
}

export const subHeader: CSSObject = {
    fontSize: '16px',
    fontWeight: 'bold',
    color: 'primary.main',
}

export const itemsForm: CSSObject = {
    width: '100%',
    display: 'flex',
    flexDirection: 'inherit',
    marginBottom: '1.5rem',
    '& .MuiTextField-root': { width: '100%' },
    '& .MuiFormControl-root': { minWidth: '100%' },
}

export const itemsButtonsOptions: CSSObject = {
    width: '100%',
    display: 'flex',
    flexDirection: 'inherit',
    alignItems: 'center',
    justifyContent: 'space-around',
}

export const itemsOptions: CSSObject = {
    ...itemsForm,
    marginBottom: 0,
    alignItems: 'center',
    justifyContent: 'space-around',
}

export const suggestionsContainter: CSSObject = {
    width: 'calc(43vw - 84px)',
    position: 'absolute',
    backgroundColor: 'white',
    border: '1px solid',
    borderColor: 'otherColor.outlineBorder',
    borderRadius: '4px',
    zIndex: '2',
    maxHeight: 'calc(35vh - 84px)',
    overflowY: 'auto',
}

export const searchSugestion: CSSObject = {
    alignSelf: 'start !important',
    overflowWrap: 'anywhere',
    textAlign: 'left',
    width: '100%',
    justifyContent: 'start',
    textTransform: 'uppercase !important' as 'uppercase',
    font: '16px !important',
    fontFamily: 'Roboto',
}
export const buttons: CSSObject = {
    whiteSpace: 'nowrap',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    display: 'block',
    maxWidth: '100%',
}

export const selectedUsersContainer: CSSObject = {
    display: 'flex',
    flexWrap: 'wrap',
    gap: '5px',
    overflowX: 'hidden',
    maxHeight: '200px',
    overflowY: 'auto',
}

export const selectedUsersAdvancedSetings: CSSObject = {
    display: 'flex',
    flexWrap: 'wrap',
    gap: '5px',
    overflowX: 'hidden',
}

export const selectedUsersAdvancedContainer: CSSObject = {
    overflowY: 'auto',
    maxHeight: 'calc(40vh - 100px)',
    border: '1px solid #cccccc',
    borderRadius: '5px',
    div: {
        maxWidth: '98%',
    },
    '@media (max-height: 910px)': {
        maxHeight: 'calc(30vh - 50px)',
    },
}

export const circularButton: CSSObject = {
    backgroundColor: 'action.hover',
    borderRadius: '50px',
    textTransform: 'none !important' as 'none',
}

export const changeThisName: CSSObject = {
    display: 'flex',
    alignItems: 'center',
    backgroundColor: 'action.hover',
    padding: '5px 8px',
    gap: '1px',
    borderRadius: '50px',
    maxWidth: 'calc(25vw - 84px)',
    '@media (max-width: 600px)': {
        maxWidth: 'calc(50vw - 84px)',
    },
    '& .MuiButtonBase-root': {
        minWidth: 0,
        padding: 0,
    },
    ':hover': {
        backgroundColor: 'action.selected',
        cursor: 'pointer',
    },
}

export const buttonLabel: CSSObject = {
    textOverflow: 'ellipsis',
    whiteSpace: 'nowrap',
    overflow: 'hidden',
}

export const icon: CSSObject = {
    color: 'black',
}

export const expandIcon: CSSObject = {
    ...icon,
    fontSize: '20px',
}

export const saveButton: CSSObject = {
    width: '100%',
}

export const BoxWithPositionAbsolute = styled('div')`
    ${({ theme }) => `
        position: absolute;
        left: 0;
        right: 0;
        bottom: 0;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        box-sizing: border-box;
        padding: 0.5rem 1.3rem;
        z-index: 10;
        gap: 1rem;
        background: ${theme.palette.background.paper};
        width: 40vw;
        @media (max-width: 600px) {
            width: 100vw;
        }
        @media (min-width: 600px) and (max-width: 1024px) {
            width: 50vw;
        }
        @media (min-width: 1500px) {
            width: 25vw;
        }
    `}
`

export const autoCompleteContainer: CSSObject = {
    display: 'flex',
    flexDirection: 'column',
    gap: '0.5rem',
}

export const typographyContainer: CSSObject = {
    borderBottom: '1px solid #cccccc',
}
