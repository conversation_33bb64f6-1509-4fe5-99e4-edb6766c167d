import { Box, useMediaQuery, useTheme } from '@mui/material'
import { ClnButtonProps, ActionIcon, MatIcon } from '@celanese/ui-lib'
import { useEffect, useMemo, useState } from 'react'
import { SiteSpecificCategory } from '../../common/models/site-specific-category'
import { useAuthGuard } from '../../common/hooks/useAuthGuard'
import DetailDeleteModal from '@/app/components/ModalComponent/DetailDeleteModal'
import { EntityType, GetSpace } from '../../common/utils/space-util'
import { generateNewExternalId, ROWS_PER_PAGE_OPTIONS_ADMIN_SCREEN } from '../../common/utils'
import { SiteSpecificCategoryForm } from './SiteSpecificCategoryForm'
import { useSiteSpecificCategoryMutations } from '@/app/common/hooks/mutations/useSiteSpecificCategoryMutations'
import MessageModal from '@/app/components/ModalComponent/Modal/MessageModal'
import { useRecurrenceAndCategoryConfigBySpecificCategory } from '@/app/common/hooks/action-item-management/useSpecificCategoryCheck'
import { GridColDef, GridRowsProp } from '@mui/x-data-grid-pro'
import { DataGridTable } from '@/app/components/PaginatedTable/DataGridTable'
import { useSnackbar } from '@/app/common/contexts/SnackbarContext'
import { translate } from '@/app/common/utils/generate-translate'
import { useLoading } from '@/app/common/contexts/LoadingContext'
import { getLocalUserSite } from '@celanese/celanese-ui'

type SpecificCategoryTabProps = {
    loading: boolean
    siteSpecificCategories: SiteSpecificCategory[]
    refetchSiteSpecificCategory: () => void
}

export const SiteSpecificCategoryTab = (props: SpecificCategoryTabProps) => {
    const { siteSpecificCategories, refetchSiteSpecificCategory } = props

    const { showSnackbar } = useSnackbar()
    const { showLoading } = useLoading()

    const theme = useTheme()
    const isMobile = useMediaQuery(theme.breakpoints.down('md'))

    const { checkPermissionsFromComponents } = useAuthGuard()
    const [saveSpecificCategories] = useSiteSpecificCategoryMutations()

    //Drawer
    const [siteSpecificCategoryToEdit, setSiteSpecificCategoryToEdit] = useState<SiteSpecificCategory | undefined>()
    const [isEdit, setIsEdit] = useState<boolean>(false)
    const [openSiteSpecificCategoryDrawer, setOpenSiteSpecificCategoryDrawer] = useState<boolean>(false)

    //Delete
    const [openModal, setOpenModal] = useState<boolean>(false)
    const [siteSpecificCategoryToDelete, setSiteSpecificCategoryToDelete] = useState<SiteSpecificCategory>()

    const { loading: loadingInfos, hasRecurrenceOrCategoryConfig } = useRecurrenceAndCategoryConfigBySpecificCategory(
        useMemo<string | undefined>(() => siteSpecificCategoryToDelete?.externalId, [siteSpecificCategoryToDelete])
    )

    const handleDeleteClick = (siteSpecificCategory?: SiteSpecificCategory) => {
        setSiteSpecificCategoryToDelete(siteSpecificCategory)
    }

    const handleCloseModal = () => {
        setSiteSpecificCategoryToDelete(undefined)
        setOpenModal(false)
    }

    const deleteSiteSpecificCategory = () => {
        handleSubmitForm(siteSpecificCategoryToDelete, true)
        refetchSiteSpecificCategory()
    }

    //Filter
    const [search, setSearch] = useState<string>('')

    //Filter data in frontend according to filter
    const filteredData: SiteSpecificCategory[] = useMemo(() => {
        const filteredData = siteSpecificCategories.filter((sc: SiteSpecificCategory) => {
            //Search
            if (search && search.length > 0) {
                const searchData = search.toLowerCase()
                const specificCategoryName = sc.name?.toLowerCase()
                const specificCategoryDescription = sc.description?.toLowerCase()

                if (!specificCategoryName?.includes(searchData) && !specificCategoryDescription?.includes(searchData)) {
                    return false
                }
            }
            return true
        })
        return filteredData
    }, [siteSpecificCategories, search])

    //Table
    const headCells: GridColDef[] = useMemo(
        () => [
            {
                field: 'subcategory2',
                headerName: translate('adminSettings.table.headers.subcategoryTwo'),
                filterable: false,
                flex: 1,
                renderHeader: () => (
                    <span data-test="admin-sub-category-2-tab-subcategoryTwo_table_sort" data-origin="aim">
                        {translate('adminSettings.table.headers.subcategoryTwo')}
                    </span>
                ),
            },
            {
                field: 'description',
                headerName: translate('adminSettings.table.headers.description'),
                filterable: false,
                flex: 1,
                renderHeader: () => (
                    <span data-test="admin-sub-category-2-tab-description_table_sort" data-origin="aim">
                        {translate('adminSettings.table.headers.description')}
                    </span>
                ),
            },
        ],
        []
    )

    const [currentPage, setCurrentPage] = useState(0)
    const [rowsPerPage, setRowsPerPage] = useState(ROWS_PER_PAGE_OPTIONS_ADMIN_SCREEN[0])

    const tableRows: GridRowsProp = useMemo(() => {
        function convertActionItemDataToRows(items: any[]) {
            return items.map((item) => ({
                id: `${item.externalId}`,
                subcategory2: `${item.name}`,
                description: `${item.description}`,
            }))
        }

        const actionsRows =
            filteredData != null && filteredData.length > 0 ? convertActionItemDataToRows(filteredData) : []

        return actionsRows
    }, [filteredData])

    const actions: ActionIcon[] = [
        {
            icon: <MatIcon icon="edit" />,
            onClick: (id) => {
                const scItem = siteSpecificCategories.find((cc) => cc.externalId == id)
                handleEditClick(scItem)
            },
        },
        {
            icon: <MatIcon icon="delete" color="error.main" />,
            onClick: (id) => {
                const ccItem = siteSpecificCategories.find((cc) => cc.externalId == id)
                handleDeleteClick(ccItem)
            },
        },
    ]

    const buttons: ClnButtonProps[] = [
        {
            label: isMobile ? '' : translate('adminSettings.table.newSubCategory2'),
            startIcon: isMobile ? <MatIcon icon="add" /> : undefined,
            variant: 'contained',
            sxProps: isMobile
                ? {
                      '& .MuiButton-startIcon': {
                          marginRight: 0,
                      },
                  }
                : undefined,
            onClick: () => setOpenSiteSpecificCategoryDrawer(true),
        },
    ]

    const handleEditClick = (specificCategory: SiteSpecificCategory | undefined) => {
        setSiteSpecificCategoryToEdit(specificCategory)
        setIsEdit(true)
        setOpenSiteSpecificCategoryDrawer(true)
    }

    //Form
    const generateSiteSpecificCategoryExternalId = () => {
        const systemCode = `SSC-${EntityType.STS}-${getLocalUserSite()?.siteCode}`
        const externalId = generateNewExternalId(systemCode, 0)
        return externalId
    }

    const handleCloseForm = () => {
        setIsEdit(false)
        setOpenSiteSpecificCategoryDrawer(false)
        setSiteSpecificCategoryToEdit(undefined)
    }

    const handleSubmitForm = async (siteSpecificCategory: any, isDeleted: boolean, onSuccess?: () => void) => {
        const { externalId, name, description } = siteSpecificCategory

        let specificCategories = siteSpecificCategories

        if (externalId) {
            specificCategories = specificCategories.filter((category) => category.externalId !== externalId)
        }

        const categoryNames = specificCategories.map((category) => category.name)
        const isDuplicateName = categoryNames.includes(name)

        if (isDuplicateName) {
            showAlert('alerts.errorOcurred', true)
            return
        }

        const dataToRequest: SiteSpecificCategory = {
            externalId: externalId || generateSiteSpecificCategoryExternalId(),
            name,
            description,
            reportingSite: {
                externalId: `${EntityType.STS}-${getLocalUserSite()?.siteCode}`,
                space: `${GetSpace(EntityType.REF)}`,
            },
            ...(isDeleted && { isDeleted: true }),
        }

        try {
            await saveSpecificCategories(dataToRequest)
            showAlert('alerts.dataSavedWithSuccess', false)
            refetchSiteSpecificCategory()
            handleCloseForm()
            onSuccess?.()
        } catch (error) {
            showAlert('alerts.unexpectedErrorOcurred', true)
        }
    }

    const showAlert = (messageKey: string, isError: boolean) => {
        showSnackbar(translate(messageKey), isError ? 'error' : 'success', 'admin-site-specific-category')
    }

    useEffect(() => {
        if (loadingInfos || !siteSpecificCategoryToDelete) return

        setOpenModal(true)
    }, [siteSpecificCategoryToDelete])

    enum TableTranslateKey {
        Search = 'Search',
        RowsPerPage = 'Rows per page',
        Of = 'of',
        Filters = 'Filters',
        Actions = 'Actions',
    }

    const translatedLabels = new Map<TableTranslateKey, string>()
    translatedLabels.set(TableTranslateKey.Search, translate('common.search'))
    translatedLabels.set(TableTranslateKey.RowsPerPage, translate('common.rowsPerPage'))
    translatedLabels.set(TableTranslateKey.Of, translate('common.of'))
    translatedLabels.set(TableTranslateKey.Filters, translate('common.filters'))
    translatedLabels.set(TableTranslateKey.Actions, translate('common.actions'))

    useEffect(() => {
        showLoading(loadingInfos)
    }, [loadingInfos])

    return (
        <Box
            sx={{
                margin: '1rem 0px',
                display: 'flex',
                flexGrow: 1,
                flexDirection: 'column',
            }}
        >
            <DataGridTable
                id="admin-site-specific-category"
                isLoading={props.loading}
                initialColumnDefs={headCells}
                showCustomFilter={false}
                onSearchSubmit={(value: string) => setSearch(value)}
                customButtons={buttons}
                rows={tableRows.slice(currentPage * rowsPerPage, currentPage * rowsPerPage + rowsPerPage)}
                rowsPerPageOptions={ROWS_PER_PAGE_OPTIONS_ADMIN_SCREEN}
                currentPage={currentPage}
                rowsPerPage={rowsPerPage}
                setRowsPerPage={setRowsPerPage}
                totalPages={Math.ceil(tableRows.length / rowsPerPage)}
                setCurrentPage={setCurrentPage}
                actions={checkPermissionsFromComponents(SiteSpecificCategoryTab.name) ? actions : undefined}
            />
            {loadingInfos
                ? null
                : openModal &&
                  siteSpecificCategoryToDelete && (
                      <>
                          <MessageModal
                              name={siteSpecificCategoryToDelete?.name ?? ''}
                              text={translate('adminSettings.specificCategories.alertDelete')}
                              open={hasRecurrenceOrCategoryConfig}
                              handleClose={handleCloseModal}
                              isCancelModal={false}
                          />
                          <DetailDeleteModal
                              name={siteSpecificCategoryToDelete?.name ?? ''}
                              activeAlertText={false}
                              alertText=""
                              open={!hasRecurrenceOrCategoryConfig}
                              handleClose={handleCloseModal}
                              deleteFunction={deleteSiteSpecificCategory}
                          />
                      </>
                  )}
            <SiteSpecificCategoryForm
                onClose={() => setOpenSiteSpecificCategoryDrawer(false)}
                openDrawer={openSiteSpecificCategoryDrawer}
                onSubmitCallback={handleSubmitForm}
                onCloseCallback={handleCloseForm}
                siteSpecificCategoryToEdit={siteSpecificCategoryToEdit}
                title={
                    isEdit
                        ? translate('adminSettings.specificCategories.drawerEditTitle')
                        : translate('adminSettings.specificCategories.drawerCreateTitle')
                }
                isEdit={isEdit}
            />
        </Box>
    )
}
