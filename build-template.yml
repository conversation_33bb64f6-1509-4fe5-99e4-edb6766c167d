parameters:
    - name: NodeEnv
      type: string

steps:
    - task: npmAuthenticate@0
      inputs:
          workingFile: '$(System.DefaultWorkingDirectory)/portal/.npmrc'

    - task: NodeTool@0
      inputs:
          versionSpec: '20.10.0'
      displayName: 'Install Node.js'

    - script: |
          cd $(System.DefaultWorkingDirectory)/portal
          echo "NEXT_PUBLIC_MUI_LICENSE_KEY=$(NEXT_PUBLIC_MUI_LICENSE_KEY)" >> .env
      displayName: 'Set env vars'

    - script: |
          cd $(System.DefaultWorkingDirectory)/portal
          npm install
          npm run build:${{ parameters.NodeEnv }} --verbose
      displayName: 'npm install and build the Portal'
