import { ExternalEntity } from '..'
import { ReportingLocation } from '../asset-hierarchy/reporting-location'
import { ReportingUnit } from '../asset-hierarchy/reporting-unit'
import { Role } from './role'
import { UserAzureAttribute } from './user-azure-attribute'

export interface UserComplement extends ExternalEntity {
    name?: string
    email: string
    active?: boolean
    admin?: boolean
    roles?: Role[]
    firstName: string
    lastName: string
    userAzureAttribute?: UserAzureAttribute
    reportingUnits?: ReportingUnit[]
    reportingLocations?: ReportingLocation[]
    reportingSites?: { itens: ExternalEntity[] }
    label?: string
    userRoleSite?: { items: { role: Role }[] }
}
