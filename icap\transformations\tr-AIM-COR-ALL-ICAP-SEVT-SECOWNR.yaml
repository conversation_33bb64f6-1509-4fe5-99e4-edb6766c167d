# Manifest file downloaded from fusion
externalId: tr-AIM-COR-ALL-ICAP-SEVT-SECOWNR
name: AIM-COR-ALL-ICAP-SEVT-SECOWNR
query: >-
  SELECT 
      source_event.space AS space,
      CONCAT('SourceEvent.secondaryOwnerUsers-', source_event.externalId, '-', utl_user_secondary_owner.user_azure_attribute.externalId) AS externalId,
      NODE_REFERENCE(source_event.space, source_event.externalId) AS startNode,
      utl_user_secondary_owner.user_azure_attribute AS endNode
  FROM (
      SELECT DISTINCT
          EventID,
          OwnerID
      FROM `ICAP-COR`.`EVNT-tblEventSecondaryOwners`
  ) AS icap_event_secondary_owner

  INNER JOIN 
      cdf_data_models("AIM-COR-ALL-DMD", "ActionItemManagementDOM", "6_0_0", "SourceEvent") AS source_event
  ON 
      source_event.iCAPEventID = cast(icap_event_secondary_owner.EventID AS STRING)
  INNER JOIN 
      `AIM-COR`.`ICAP-MAP-User` AS utl_user_secondary_owner
  ON 
      utl_user_secondary_owner.key = cast(icap_event_secondary_owner.OwnerID AS STRING)
  WHERE 1 = 0 -- freeze
destination:
  dataModel:
    space: AIM-COR-ALL-DMD
    externalId: ActionItemManagementDOM
    version: "6_0_0"
    destinationType: SourceEvent
    destinationRelationshipFromType: secondaryOwnerUsers
  type: instances
ignoreNullFields: true
shared: true
action: upsert
dataSetExternalId: AIM-COR-ALL-DAT
# Specify credentials separately like this:
# authentication:
#   read:
#     clientId: ${READ_CLIENT_ID}
#     clientSecret: ${READ_CLIENT_SECRET}
#     tokenUrl: ${READ_TOKEN_URL}
#     cdfProjectName: ${READ_CDF_PROJECT_NAME}
#     # Optional: If idP requires providing the scopes
#     scopes:
#       - ${READ_SCOPES}
#     # Optional: If idP requires providing the audience
#     audience: ${READ_CDF_AUDIENCE}
#   write:
#     clientId: ${WRITE_CLIENT_ID}
#     clientSecret: ${WRITE_CLIENT_SECRET}
#     tokenUrl: ${WRITE_TOKEN_URL}
#     cdfProjectName: ${WRITE_CDF_PROJECT_NAME}
#     # Optional: If idP requires providing the scopes
#     scopes:
#       - ${WRITE_SCOPES}
#     # Optional: If idP requires providing the audience
#     audience: ${WRITE_CDF_AUDIENCE}
# Or together like this:
# authentication:
#   clientId: ${CLIENT_ID}
#   clientSecret: ${CLIENT_SECRET}
#   tokenUrl: ${TOKEN_URL}
#   # Optional: If idP requires providing the scopes
#   cdfProjectName: ${CDF_PROJECT_NAME}
#   scopes:
#    - ${SCOPES}
#   # Optional: If idP requires providing the audience
#   audience: ${AUDIENCE}