# Manifest file downloaded from fusion
externalId: tr-AIM-COR-ALL-RAW-ICAP-VAL-MISSING-EVENT
name: AIM-COR-ALL-RAW-ICAP-VAL-MISSING-EVENT
query: >-
  SELECT
    	icap_event.key,
    	icap_event.key AS `1_EventID`,
    	icap_event.EventTitle AS `2_EventTitle`,
    	icap_event.EventDescription AS `3_EventDescription`,
      icap_user_owner.Email AS `4_EventPrimaryOwnerEmail`,
    	icap_grouped_secondary_owner.secondary_owners AS `5_EventSecondaryOwnerEmails`,
    	icap_event_category.EventCategoryName AS `6_EventCategory`,
    	icap_event_status.EventStatusName AS `7_EventStatus`,
    	DATE(icap_event.StartDate) AS `8_EventStartDate`,
    	DATE(icap_event.DueDate) AS `9_EventDueDate`,
    	icap_site.SiteName AS `10_SiteName`,
    	icap_unit.UnitName AS `11_AreaName`,
    	icap_event.Private AS `12_isPrivate`,
    	main.error_details AS `13_errorDetails`
  FROM
      (
          SELECT
              main.key,
  			concat(
  			  if(isnull(category), 'Missing Category; ', ''),
  			  if(isnull(subCategory), 'Missing Sub Category 1; ', ''),
  			  if(isnull(owner), 'Missing Owner; ', ''),
  			  if(isnull(createdBy), 'Missing Created By; ', ''),
  			  if(isnull(status), 'Missing Status; ', ''),
  			  if(isnull(reportingUnit), 'Missing Reporting Unit - Invalid Area Mapping; ', ''),
  			  if(
  				  isnotnull(reportingSite) 
  				  AND isnotnull(reportingUnit)
  				  AND NOT startswith(
  					reportingUnit.externalId,
  					replace(reportingSite.externalId, 'STS-', 'UNT-')
  				  ), 
  				  'Reporting Unit from another Site; ', 
  				  ''
  			  ),
  			  if(
  				  isnotnull(reportingSite) 
  				  AND isnotnull(reportingLocation)
  				  AND NOT startswith(
  					reportingLocation.externalId,
  					replace(reportingSite.externalId, 'STS-', 'LOC-')
  				  ), 
  				  'Reporting Location from another Site; ', 
  				  ''
  			  )
  			) AS error_details
          FROM
              `AIM-COR`.`ICAP-STG-Event` as main
      ) AS main
      INNER JOIN `ICAP-COR`.`EVNT-tblEvent` icap_event 
    		ON main.key = icap_event.key
      LEFT JOIN `ICAP-COR`.`USR-tblUser` icap_user_owner
      	ON icap_user_owner.key = cast(icap_event.EventAddedByOwner AS STRING)
      LEFT JOIN (
        SELECT 
        	icap_secondary_owner.EventID, 
        	array_join(array_distinct(array_agg(icap_user_secondary_owner.Email)), ", ") AS secondary_owners
        FROM `ICAP-COR`.`EVNT-tblEventSecondaryOwners` icap_secondary_owner
        INNER JOIN `ICAP-COR`.`USR-tblUser` icap_user_secondary_owner
        	ON cast(icap_secondary_owner.OwnerID AS STRING) = icap_user_secondary_owner.key
        GROUP BY icap_secondary_owner.EventID
      ) icap_grouped_secondary_owner
        	ON icap_event.key = cast(icap_grouped_secondary_owner.EventID AS STRING)
      LEFT JOIN `ICAP-COR`.`EVNT-tblEventCategory` icap_event_category
      	ON icap_event_category.key = cast(icap_event.EventCategoryID AS STRING)
      LEFT JOIN `ICAP-COR`.`EVNT-tblEventStatus` icap_event_status
        	ON icap_event_status.key = cast(icap_event.EventStatusID AS STRING)
      LEFT JOIN `ICAP-COR`.`STS-tblSite` icap_site
        	ON icap_site.key = cast(icap_event.SiteID AS STRING)
      LEFT JOIN `ICAP-COR`.`UNT-tblUnit` icap_unit
        	ON icap_unit.key = cast(icap_event.UnitID AS STRING)
  WHERE
      LENGTH(main.error_details) > 0
  	-- OR icap_event.Private
destination:
  database: AIM-COR
  table: ICAP-VAL-MissingEvent
  type: raw
ignoreNullFields: true
shared: true
action: upsert
dataSetExternalId: AIM-COR-ALL-DAT
# Specify credentials separately like this:
# authentication:
#   read:
#     clientId: ${READ_CLIENT_ID}
#     clientSecret: ${READ_CLIENT_SECRET}
#     tokenUrl: ${READ_TOKEN_URL}
#     cdfProjectName: ${READ_CDF_PROJECT_NAME}
#     # Optional: If idP requires providing the scopes
#     scopes:
#       - ${READ_SCOPES}
#     # Optional: If idP requires providing the audience
#     audience: ${READ_CDF_AUDIENCE}
#   write:
#     clientId: ${WRITE_CLIENT_ID}
#     clientSecret: ${WRITE_CLIENT_SECRET}
#     tokenUrl: ${WRITE_TOKEN_URL}
#     cdfProjectName: ${WRITE_CDF_PROJECT_NAME}
#     # Optional: If idP requires providing the scopes
#     scopes:
#       - ${WRITE_SCOPES}
#     # Optional: If idP requires providing the audience
#     audience: ${WRITE_CDF_AUDIENCE}
# Or together like this:
# authentication:
#   clientId: ${CLIENT_ID}
#   clientSecret: ${CLIENT_SECRET}
#   tokenUrl: ${TOKEN_URL}
#   # Optional: If idP requires providing the scopes
#   cdfProjectName: ${CDF_PROJECT_NAME}
#   scopes:
#    - ${SCOPES}
#   # Optional: If idP requires providing the audience
#   audience: ${AUDIENCE}