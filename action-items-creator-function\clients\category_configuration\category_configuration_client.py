from typing import Optional

from clients.category_configuration.cognite_filters import (
    get_categories_configuration_filters,
)
from clients.category_configuration.models import (
    CategoryConfigurationByFilterResult,
    CategoryResult,
    SiteSpecificCategoryResult,
    SubCategoryResult,
)
from clients.category_configuration.queries import (
    GET_CATEGORY_BY_FILTER,
    GET_CATEGORY_CONFIGURATION_BY_FILTER_QUERY,
    GET_SITE_SPECIFIC_CATEGORY_BY_FILTER,
    GET_SUB_CATEGORY_BY_FILTER,
)
from clients.category_configuration.requests import (
    GetCategoriesRequest,
    GetCategoryConfigurationByFilterRequest,
    GetSiteSpecificCategoriesBySitesRequest,
    GetSiteSpecificCategoriesRequest,
    GetSubCategoriesRequest,
)
from clients.core.constants import AGGREGATE_LIMIT, SEARCH_FILTER_LIMIT, DataSpaceEnum
from clients.core.models import PaginatedData, ServiceParams


class CategoryConfigurationClient:
    def __init__(
        self,
        params: ServiceParams,
    ):
        self._cognite_client = params.cognite_client
        self._data_model_id = params.data_model.as_id()

    def get_category_configurations(
        self,
        request: GetCategoryConfigurationByFilterRequest,
        filter_only_by_site: bool = False,
    ) -> PaginatedData[CategoryConfigurationByFilterResult]:
        result = self._cognite_client.data_modeling.graphql.query(
            id=self._data_model_id,
            query=GET_CATEGORY_CONFIGURATION_BY_FILTER_QUERY,
            variables=get_categories_configuration_filters(
                request,
                filter_only_by_site,
            ),
        )

        return PaginatedData[CategoryConfigurationByFilterResult].from_graphql_response(
            result,
            request.page_size,
        )

    def get_categories(
        self,
        request: GetCategoriesRequest,
    ) -> Optional[PaginatedData[CategoryResult]]:
        filters = [
            {"space": {"eq": DataSpaceEnum.AIM_REF_DATA_SPACE}},
        ]

        if request.name:
            filters.append({"name": {"eq": request.name}})

        variables = {"filter": {"and": filters}, "sort": {"name": "ASC"}}

        try:
            result = self._cognite_client.data_modeling.graphql.query(
                id=self._data_model_id,
                query=GET_CATEGORY_BY_FILTER,
                variables=variables,
            )
            return PaginatedData[CategoryResult].from_graphql_response(
                result,
                AGGREGATE_LIMIT,
            )
        except Exception:
            return None

    def get_sub_categories(
        self,
        request: GetSubCategoriesRequest,
    ) -> Optional[PaginatedData[SubCategoryResult]]:
        filters = [
            {"space": {"eq": DataSpaceEnum.AIM_REF_DATA_SPACE}},
        ]

        if request.name:
            filters.append({"name": {"eq": request.name}})

        variables = {"filter": {"and": filters}, "sort": {"name": "ASC"}}

        try:
            result = self._cognite_client.data_modeling.graphql.query(
                id=self._data_model_id,
                query=GET_SUB_CATEGORY_BY_FILTER,
                variables=variables,
            )
            return PaginatedData[SubCategoryResult].from_graphql_response(
                result,
                AGGREGATE_LIMIT,
            )
        except Exception:
            return None

    def get_site_specific_categories(
        self,
        request: GetSiteSpecificCategoriesRequest,
    ) -> Optional[PaginatedData[SiteSpecificCategoryResult]]:
        filters = [
            {"space": {"in": [request.space, DataSpaceEnum.COR_SPACE]}},
            {
                "reportingSite": {
                    "externalId": {"eq": request.reporting_site_external_id},
                },
            },
            {"isDeleted": {"isNull": True}},
        ]

        if request.name:
            filters.append({"name": {"eq": request.name}})

        variables = {"filter": {"and": filters}, "sort": {"name": "ASC"}}

        try:
            result = self._cognite_client.data_modeling.graphql.query(
                id=self._data_model_id,
                query=GET_SITE_SPECIFIC_CATEGORY_BY_FILTER,
                variables=variables,
            )
            return PaginatedData[SiteSpecificCategoryResult].from_graphql_response(
                result,
                AGGREGATE_LIMIT,
            )
        except Exception:
            return None

    def get_site_specific_categories_by_sites(
        self,
        request: GetSiteSpecificCategoriesBySitesRequest,
    ) -> Optional[PaginatedData[SiteSpecificCategoryResult]]:
        """
        Retrieve site specific categories based on site codes and optional search filters.

        Filters the site specific categories by space, site code prefix, active status, and optional search parameters.

        Args:
            request (GetSiteSpecificCategoriesBySitesRequest): The request containing the site codes
                                                    and optional search parameters.

        Returns:
            Optional[PaginatedData[SiteSpecificCategoryResult]]: A paginated list of site specific categories results,
                                                        or None if the query fails.

        """
        filters = [
            {"space": {"in": request.spaces}},
            {
                "reportingSite": {
                    "externalId": {"in": request.reporting_site_external_ids},
                },
            },
            {"isDeleted": {"isNull": True}},
        ]

        variables = {
            "query": request.search,
            "fields": request.search_properties,
            "filter": {"and": filters},
            "sort": {"description": "ASC"},
        }

        try:

            result = self._cognite_client.data_modeling.graphql.query(
                id=self._data_model_id,
                query=GET_SITE_SPECIFIC_CATEGORY_BY_FILTER,
                variables=variables,
            )

            return PaginatedData[SiteSpecificCategoryResult].from_graphql_response(
                result,
                SEARCH_FILTER_LIMIT,
            )

        except Exception:
            return None
