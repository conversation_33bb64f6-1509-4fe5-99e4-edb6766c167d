from os.path import abspath, dirname
from pathlib import Path

import pandas as pd
from cognite.client import CogniteClient
from ..constants import RAW_DB


class MigrationProgressReportService:
    EVENT_MIGRATION_PROGRESS_TABLE = "ICAP-VAL-EventMigrationProgress"
    ACTION_MIGRATION_PROGRESS_TABLE = "ICAP-VAL-ActionMigrationProgress"

    def __init__(self, client: CogniteClient):
        self._client = client

    def generate(self):
        event_df = self._client.raw.rows.retrieve_dataframe(
            db_name=RAW_DB,
            table_name=self.EVENT_MIGRATION_PROGRESS_TABLE,
            limit=-1,
            partitions=20,
        )

        action_df = self._client.raw.rows.retrieve_dataframe(
            db_name=RAW_DB,
            table_name=self.ACTION_MIGRATION_PROGRESS_TABLE,
            limit=-1,
            partitions=20,
        )

        for site in event_df.reporting_site.unique():
            site_event_df = event_df[event_df.reporting_site == site]
            site_event_df = self._handle_columns(site_event_df)

            site_action_df = action_df[action_df.reporting_site == site]
            site_action_df = self._handle_columns(site_action_df)

            site_code = site.split("-")[-1]
            with pd.ExcelWriter(
                self._get_current_dir_path().parent.parent.joinpath(
                    "outputs", f"iCap-AIM-{site_code}-Report.xlsx"
                )
            ) as f:
                site_event_df.to_excel(f, sheet_name="Events", index=False)
                site_action_df.to_excel(f, sheet_name="Actions", index=False)

    @staticmethod
    def _handle_columns(df: pd.DataFrame):
        df = df.drop("reporting_site", axis="columns")
        sorted_columns = sorted(df.columns, key=lambda col: int(col.split("_")[0]))
        df = df[sorted_columns]
        df = df.rename(
            {col: col[col.find("_") + 1 :] for col in df.columns}, axis="columns"
        )
        return df

    @staticmethod
    def _get_current_dir_path() -> Path:
        return Path(dirname(abspath(__file__)))
