import { type CogniteClient } from '@cognite/sdk'
import dayjs from 'dayjs'
import { getCurrentDatasetId } from './datasets'

export const buildUploadFilesRequest = async (
    cogniteClient: CogniteClient,
    filesToUpload: File[],
    user: string,
    siteId: string,
    isPrivate?: boolean,
    source?: string,
    prefix: string = 'ACTATT'
): Promise<FormData> => {
    const formData = new FormData()

    const timestamp = dayjs().format('YYYYMMDDHHmmssSSS')
    const datasetId = await getCurrentDatasetId(cogniteClient, siteId, isPrivate)

    filesToUpload.forEach((file, index) => {
        const metadata = {
            name: file.name,
            externalId: `${prefix}-${file.name}-${timestamp}-${index + 1}`,
            source: source || '',
            mimeType: file.type,
            fileSize: file.size.toString(),
            dataSetId: datasetId.toString(),
            isPrivate: isPrivate,
            user: user,
        }

        formData.append('files', file)
        formData.append('metadata', JSON.stringify(metadata))
    })

    return formData
}
