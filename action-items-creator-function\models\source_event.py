import os
import sys
from typing import List

from pydantic import Field

from models.node_reference import NodeReference, PossiblyNewNode

script_dir = os.path.dirname(os.path.abspath(__file__))
project_dir = os.path.dirname(script_dir)
sys.path.append(project_dir)


class SourceEventHistory(NodeReference):
    sourceEvent: NodeReference | None = None
    status: str
    comments: str = Field(default="")
    changedAt: str
    updateBy: NodeReference | None = None


class SourceEvent(PossiblyNewNode):
    externalId: str | None = None
    space: str | None = Field(min_length=1, default=None)
    title: str | None = None
    description: str | None = None
    status: NodeReference | None = None
    application: NodeReference | None = None
    reportingSite: NodeReference | None = None
    eventType: NodeReference | None = None
    actionsCount: int | None = None
    actions: list[NodeReference] | None = None
    owners: list[NodeReference] | None = None
    owner: NodeReference | None = None
    secondaryOwnerUsers: list[NodeReference] | None = None
    secondaryOwnerRoles: list[NodeReference] | None = None
    secondaryOwnerTeams: list[NodeReference] | None = None
    reportingUnit: NodeReference | None = None
    reportingUnits: list[NodeReference] | None = None
    reportingLocation: NodeReference | None = None
    reportingLine: NodeReference | None = None
    impactedReportingLocations: list[NodeReference] | None = None
    category: NodeReference | None = None
    subCategory: NodeReference | None = None
    siteSpecificCategory: NodeReference | None = None
    sourceInformation: str | None = None
    assignmentDate: str | None = None
    dueDate: str | None = None
    displayDueDate: str | None = None
    businessLine: NodeReference | None = None
    createdBy: NodeReference | None = None
    sourceEventHistory: list[SourceEventHistory] | None = None
    createdAt: str | None = None
    attachments: List[str] | None = None
    isPrivate: bool | None = None
    viewUsers: list[NodeReference] | None = None
    viewRoles: list[NodeReference] | None = None
    viewTeams: list[NodeReference] | None = None
    views: list[str] | None = None

    equipments: list[NodeReference] | None = None
    functionalLocations: list[NodeReference] | None = None
    searchTags: list[str] | None = None

    sortReportingUnit: str | None = None
    sortReportingLocation: str | None = None
    sortStatus: str | None = None
    sortCategory: str | None = None
    sortSubCategory: str | None = None
    sortSiteSpecificCategory: str | None = None
    sortTitle: str | None = None
