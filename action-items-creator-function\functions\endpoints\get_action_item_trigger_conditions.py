import azure.functions as func
from infra.action_item_trigger_retriever_factory import ActionItemTriggerRetrieverFactory

bp = func.Blueprint()

@bp.function_name(name="GetActionItemTriggerConditions")
@bp.route("get-condition-options", methods=["post"], auth_level=func.AuthLevel.ANONYMOUS)
async def main(req: func.HttpRequest) -> func.HttpResponse:
    import logging
    import json as json

    try:
        body = req.get_json()
        logging.info("Function GetActionItemTriggerConditions started")
        application_id= body["application_id"]
        conditions = await ActionItemTriggerRetrieverFactory.retriever().get_condition_options(application_id)
        logging.info(f"Finishing execution - Conditions Retrieved : {conditions.count}")
        return func.HttpResponse(json.dumps(conditions))
    except Exception as e:
        logging.info("Exception found!")
        return func.HttpResponse(f"Error: {e}", status_code=500)
    