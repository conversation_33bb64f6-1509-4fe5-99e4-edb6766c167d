import { Box } from '@mui/material'
import { ActionIcon, MatIcon } from '@celanese/ui-lib'
import { useContext, useEffect, useMemo, useState } from 'react'
import { ActionItemTemplateTabFilter } from './ActionItemTemplateTabFilter'
import { Action, Owner, TemplateConfiguration } from '../../common/models/action'
import { ActionDetailItem, StatusHistoryInstance } from '../../common/models/action-detail'
import { EntityType, GetSpace } from '../../common/utils/space-util'
import { useRouter } from 'next/navigation'
import { ActionItemCategory } from '../../common/models/category'
import { ReportingUnit } from '../../common/models/reporting-unit'
import {
    StatusHistoryQuery,
    useStatusHistoryInstances,
} from '../../common/hooks/action-item-management/useStatusHistoryInstances'
import { useAuthGuard } from '../../common/hooks/useAuthGuard'
import {
    FilterOptionsActionItemTemplate,
    FilterSelectedActionItemTemplate,
} from '@/app/common/models/admin-settings/filter-action-item-template'
import {
    useActionByTemplate,
    UseActionByTemplateQueryRequest,
} from '@/app/common/hooks/action-item-management/useActionByTemplate'
import { useTemplateQuery } from '@/app/common/hooks/pagination/useTemplateQuery'
import TemplateModal from '@/app/components/ModalComponent/TemplateModal'
import { useUpsertTemplateConfiguration } from '@/app/common/hooks/mutations/useUpsertTemplateConfiguration'
import { DataGridTable } from '@/app/components/PaginatedTable/DataGridTable'
import { GridColDef, GridRowsProp } from '@mui/x-data-grid-pro'
import { getLocalUserSite, UserManagementContext, UserManagementContextState } from '@celanese/celanese-ui'
import { TemplateConfigForm } from '@/app/common/models/template-configuration'
import { useFdmDeleter } from '@/app/common/hooks/cognite/useFdmDeleter'
import { useSnackbar } from '@/app/common/contexts/SnackbarContext'
import RemoveItemModal from '@/app/components/ButtonsComponents/RemoveItemModal'
import { translate } from '@/app/common/utils/generate-translate'
import { determineActiveFilterCount } from '@/app/common/utils/active-filter-count'
import { useLoading } from '@/app/common/contexts/LoadingContext'
import { ROWS_PER_PAGE_OPTIONS_ADMIN_SCREEN } from '@/app/common/utils'

type ActionItemTemplateTabProps = {
    loading: boolean
    categories: ActionItemCategory[]
    units: ReportingUnit[]
    owner: Owner[]
    handleAlert: (message: string, error?: boolean) => void
}

export const ActionItemTemplateTab = ({
    loading,
    categories,
    units,
    owner,
    handleAlert,
}: ActionItemTemplateTabProps) => {
    const router = useRouter()
    const { checkPermissionsFromComponents } = useAuthGuard()

    const { showSnackbar } = useSnackbar()
    const { showLoading } = useLoading()

    const { siteCode } = getLocalUserSite() || {}

    const { userInfo } = useContext<UserManagementContextState>(UserManagementContext)

    const [deleteModalOpen, setDeleteModalOpen] = useState(false)
    const [deleteActionItemTemplate, setDeleteActionItemTemplate] = useState<ActionDetailItem | undefined>()
    const [assigneeModalOpen, setAssigneeModalOpen] = useState(false)
    const [selectedTemplate, setSelectedTemplate] = useState<TemplateConfiguration | null>(null)

    const space = GetSpace(EntityType.Instance, siteCode)

    const [currentPage, setCurrentPage] = useState(0)
    const [rowsPerPage, setRowsPerPage] = useState(ROWS_PER_PAGE_OPTIONS_ADMIN_SCREEN[0])

    const storedFilterInfo = sessionStorage.getItem(`admin-template-filterInfo`)
    const parsedFilterInfo = storedFilterInfo ? JSON.parse(storedFilterInfo) : {}

    const [filterActions, setFilterActions] = useState<FilterSelectedActionItemTemplate>({ ...parsedFilterInfo })
    const {
        actions: actionsItens,
        refetchTemplate,
        loadingTemplate: loadingTemplateActions,
    } = useActionByTemplate(
        useMemo<UseActionByTemplateQueryRequest>(() => {
            const queryParams: UseActionByTemplateQueryRequest = {
                unit: filterActions?.unit,
                category: filterActions?.category,
                owner: filterActions?.owner,
            }
            setCurrentPage(0)
            return queryParams
        }, [filterActions])
    )

    const actionsIds = useMemo(() => actionsItens.map((x) => x.externalId), [actionsItens])

    const { historyInstance } = useStatusHistoryInstances(
        useMemo<StatusHistoryQuery>(
            () => ({
                externalId: actionsIds,
                statusId: null,
            }),
            [actionsIds]
        )
    )

    const historyInstanceArray = useMemo(() => {
        const historyIds: string[] = []
        const latestHistoryMap: StatusHistoryInstance[] = []

        historyInstance.forEach((x) => {
            if (!historyIds.includes(x?.action?.externalId)) {
                historyIds.push(x?.action?.externalId)
                latestHistoryMap.push(x)
            }
        })

        const latestHistoryArray = Array.from(latestHistoryMap.values())

        return latestHistoryArray
    }, [historyInstance])

    //Filter
    const [search, setSearch] = useState<string>('')
    const filteredData = useMemo(() => {
        const filteredData = actionsItens.filter((template) => {
            //Search
            if (search && search.length > 0) {
                const searchData = search.toLowerCase()
                const recurringTitle = template.templateConfiguration?.name?.toLowerCase() ?? ''
                const recurringUnit = template.reportingUnit?.description.toLowerCase()
                const recurringCategory = template.category?.name.toLowerCase()
                const recurringOwnerLast = template.owner?.user?.lastName.toLowerCase()
                const recurringOwnerFist = template.owner?.user?.firstName.toLowerCase()

                if (
                    !recurringTitle?.includes(searchData) &&
                    !recurringUnit?.includes(searchData) &&
                    !recurringCategory?.includes(searchData) &&
                    !recurringOwnerLast?.includes(searchData) &&
                    !recurringOwnerFist?.includes(searchData)
                ) {
                    return false
                }
            }
            return true
        })
        return filteredData
    }, [actionsItens, search])

    const [filterOptions, setFilterOptions] = useState<FilterOptionsActionItemTemplate>({
        unit: [],
        category: [],
        owner: [],
    })

    const applyFilters = async (filters: any) => {
        setFilterActions(filters)
        resetPageProps()
    }

    const buildFilterOptions = (): void => {
        const options: FilterOptionsActionItemTemplate = {
            unit: [],
            category: [],
            owner: [],
        }
        categories.forEach((cat) => {
            if (cat.name && !options.category.includes(cat.name)) {
                options.category.push(cat.name)
            }
        })
        units.forEach((unit) => {
            if (
                unit.externalId &&
                unit.description &&
                !options.unit.some(
                    (existingUnit) =>
                        existingUnit.externalId === unit.externalId &&
                        existingUnit.description === existingUnit.description
                )
            ) {
                options.unit.push({
                    externalId: unit.externalId,
                    description: unit.description,
                })
            }
        })
        owner.forEach((owner) => {
            if (
                owner.user.firstName &&
                owner.user.lastName &&
                !options.owner.some(
                    (existingOwner) =>
                        existingOwner.label === `${owner.user.lastName}, ${owner.user.firstName}` &&
                        existingOwner.externalId === owner.user.externalId
                )
            ) {
                options.owner.push({
                    externalId: owner.user.externalId,
                    label: `${owner.user.lastName}, ${owner.user.firstName}`,
                })
            }
        })

        setFilterOptions(options)
    }

    //Table
    const headCells: GridColDef[] = useMemo(
        () => [
            {
                field: 'title',
                headerName: translate('adminSettings.table.headers.title'),
                filterable: false,
                flex: 1,
                renderHeader: () => (
                    <span data-test="admin-template-tab-title_table_sort" data-origin="aim">
                        {translate('adminSettings.table.headers.title')}
                    </span>
                ),
            },
            {
                field: 'owner',
                headerName: translate('adminSettings.table.headers.owner'),
                filterable: false,
                flex: 1,
                renderHeader: () => (
                    <span data-test="admin-template-tab-owner_table_sort" data-origin="aim">
                        {translate('adminSettings.table.headers.owner')}
                    </span>
                ),
            },
            {
                field: 'unit',
                headerName: translate('adminSettings.table.headers.unit'),
                filterable: false,
                flex: 1,
                renderHeader: () => (
                    <span data-test="admin-template-tab-unit_table_sort" data-origin="aim">
                        {translate('adminSettings.table.headers.unit')}
                    </span>
                ),
            },
            {
                field: 'category',
                headerName: translate('adminSettings.table.headers.category'),
                filterable: false,
                flex: 1,
                renderHeader: () => (
                    <span data-test="admin-template-tab-category_table_sort" data-origin="aim">
                        {translate('adminSettings.table.headers.category')}
                    </span>
                ),
            },
        ],
        [translate]
    )

    const resetPageProps = () => {
        setCurrentPage(0)
    }

    const tableRows: GridRowsProp = useMemo(() => {
        function convertActionItemDataToRows(items: any[]) {
            return items.map((item) => ({
                id: `${item.externalId}`,
                title: `${item.templateConfiguration?.name ?? ''}`,
                owner: `${item.owner?.user?.lastName}, ${item.owner?.user?.firstName}`,
                unit: `${item.reportingUnit?.description ?? ''}`,
                category: `${item.category?.name ?? ''}`,
            }))
        }

        const actionsRows =
            filteredData != null && filteredData.length > 0 ? convertActionItemDataToRows(filteredData) : []

        return actionsRows
    }, [filteredData, historyInstanceArray])

    const handleSetTemplateAssigneeClick = (templateId: string) => {
        if (!templateId) {
            showSnackbar(translate('alerts.unexpectedErrorOcurred'), 'error', 'admin-template-tab')
            return
        }

        const foundTemplate = actionsItens.find((action) => action.externalId === templateId)
        if (foundTemplate && foundTemplate.templateConfiguration) {
            setSelectedTemplate(foundTemplate.templateConfiguration)
        } else {
            showSnackbar(translate('alerts.unexpectedErrorOcurred'), 'error', 'admin-template-tab')
            setSelectedTemplate(null)
        }
        setAssigneeModalOpen(true)
    }

    const handleDeleteClick = (id: string) => {
        function mapToActionDetailItem(action: Action): ActionDetailItem {
            return {
                externalId: action.externalId,
                space: action.space,
                title: action.title,
            }
        }
        const foundTemplate = actionsItens.find((action) => action.externalId === id)
        if (foundTemplate) {
            const actionDetailItem: ActionDetailItem = mapToActionDetailItem(foundTemplate)
            setDeleteActionItemTemplate(actionDetailItem)
        }
        setDeleteModalOpen(true)
    }

    const handleCloseDeleteModal = () => {
        setDeleteModalOpen(false)
        refetchTemplate()
    }

    const handleCloseAssigneeModal = () => {
        setAssigneeModalOpen(false)
    }

    const [deleteEdges] = useFdmDeleter()
    const [upsertTemplateConfiguration] = useUpsertTemplateConfiguration()

    const { queryResult: actionsTemplateName, loading: loadingTemplate } = useTemplateQuery({})

    const updateTemplateConfig = async (template: TemplateConfigForm) => {
        const templateId = selectedTemplate?.externalId

        if (!templateId) {
            showSnackbar(translate('alerts.unexpectedErrorOcurred'), 'error', 'admin-template-tab')
            return
        }

        const actionSpace = GetSpace(EntityType.Instance, siteCode)

        const foundTemplate = actionsItens.find((action) => action.templateConfiguration?.externalId === templateId)

        if (!foundTemplate) {
            showSnackbar(translate('alerts.unexpectedErrorOcurred'), 'error', 'admin-template-tab')
            return
        }

        const mappedTemplateUsers: any[] | undefined =
            template.users?.map((user) => ({
                node: {
                    externalId: user || '',
                    space: GetSpace(EntityType.UMG),
                },
                edge: {
                    externalId: `${templateId}-${user}`,
                    space: actionSpace,
                },
            })) ?? undefined

        const mappedTemplateRole: any[] | undefined =
            template.roles?.map((role) => ({
                node: {
                    externalId: role || '',
                    space: GetSpace(EntityType.UMG),
                },
                edge: {
                    externalId: `${templateId}-${role}`,
                    space: actionSpace,
                },
            })) ?? undefined

        const templateConfigUpdateRequest = {
            space: space,
            externalId: templateId,
            users: mappedTemplateUsers,
            roles: mappedTemplateRole,
        }

        const edgesIdsToDelete = [...(template.originalUsers ?? []), ...(template.originalRoles ?? [])].map(
            (item) => `${templateId}-${item}`
        )

        try {
            if (edgesIdsToDelete) {
                await deleteEdges({
                    externalIds: edgesIdsToDelete,
                    instanceType: 'edge',
                    space: actionSpace,
                })
            }
            await upsertTemplateConfiguration({
                variables: { templateConfigurations: [templateConfigUpdateRequest] },
            })
            showSnackbar(translate('alerts.templateSavedWithSuccess'), 'success', 'admin-template-tab')
            refetchTemplate()
        } catch (ex) {
            showSnackbar(translate('alerts.unexpectedErrorOcurred'), 'error', 'admin-template-tab')
        }
    }

    const actions: ActionIcon[] = [
        {
            icon: <MatIcon icon="edit" />,
            onClick: (id) => {
                showLoading(true)
                localStorage.setItem('isEditForm', 'true')
                router.push(`/action-item/edit/${siteCode}/${id}`)
            },
        },
        {
            icon: <MatIcon icon="group" />,
            onClick: (template) => {
                if (template) {
                    handleSetTemplateAssigneeClick(template)
                }
            },
        },
        {
            icon: <MatIcon icon="delete" color="error.main" />,
            onClick: (id) => {
                handleDeleteClick(id ?? '')
            },
        },
    ]

    useEffect(() => {
        buildFilterOptions()
    }, [categories, actionsItens, units, owner])

    enum TableTranslateKey {
        Search = 'Search',
        RowsPerPage = 'Rows per page',
        Of = 'of',
        Filters = 'Filters',
        Actions = 'Actions',
    }

    const translatedLabels = new Map<TableTranslateKey, string>()
    translatedLabels.set(TableTranslateKey.Search, translate('common.search'))
    translatedLabels.set(TableTranslateKey.RowsPerPage, translate('common.rowsPerPage'))
    translatedLabels.set(TableTranslateKey.Of, translate('common.of'))
    translatedLabels.set(TableTranslateKey.Filters, translate('common.filters'))
    translatedLabels.set(TableTranslateKey.Actions, translate('common.actions'))

    const customPopoverContent = useMemo(() => {
        return (
            <ActionItemTemplateTabFilter
                data={filterOptions}
                defaultFilter={filterActions}
                onSubmit={(filters) => {
                    applyFilters(filters)
                    sessionStorage.setItem(`admin-template-filterInfo`, JSON.stringify(filters))
                }}
            />
        )
    }, [translate, filterOptions, filterActions])

    return (
        <Box
            sx={{
                margin: '1rem 0px',
                display: 'flex',
                flexGrow: 1,
                flexDirection: 'column',
            }}
        >
            <DataGridTable
                id="admin-template"
                isLoading={loading || loadingTemplateActions}
                initialColumnDefs={headCells}
                onSearchSubmit={(value: string) => setSearch(value)}
                rows={tableRows.slice(currentPage * rowsPerPage, currentPage * rowsPerPage + rowsPerPage)}
                rowsPerPageOptions={ROWS_PER_PAGE_OPTIONS_ADMIN_SCREEN}
                currentPage={currentPage}
                rowsPerPage={rowsPerPage}
                setRowsPerPage={setRowsPerPage}
                customPopoverContent={customPopoverContent}
                totalPages={Math.ceil(tableRows.length / rowsPerPage)}
                setCurrentPage={setCurrentPage}
                actions={checkPermissionsFromComponents(ActionItemTemplateTab.name) ? actions : undefined}
                activeFiltersCount={determineActiveFilterCount(filterActions)}
            />
            <TemplateModal
                modalTitle={translate('adminSettings.template.updateView')}
                open={assigneeModalOpen}
                users={selectedTemplate?.users?.items}
                roles={selectedTemplate?.roles?.items}
                actionsTemplateName={actionsTemplateName}
                loadingTemplate={!!selectedTemplate && loadingTemplate}
                handleClose={handleCloseAssigneeModal}
                createFunction={(x) => updateTemplateConfig(x)}
                isEdit={true}
                templateToEdit={selectedTemplate}
            />
            <RemoveItemModal
                actionDetails={deleteActionItemTemplate}
                routerPush="/admin-settings"
                openModal={deleteModalOpen}
                handleClose={handleCloseDeleteModal}
                activeUserEmail={userInfo.email}
                handleAlert={handleAlert}
                titleLabel={translate('requestModal.deleteTemplate')}
                questionLabel={translate('requestModal.deleteTemplateQuestion')}
                isDelete
                comments
            />
        </Box>
    )
}
