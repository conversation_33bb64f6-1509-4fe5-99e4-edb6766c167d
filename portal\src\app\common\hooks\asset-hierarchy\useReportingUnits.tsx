import { gql } from '@apollo/client'
import { useEffect, useState } from 'react'
import { useGraphqlQuery } from '../cognite/useGraphqlQuery'
import { ReportingUnit } from '../../models/common/asset-hierarchy/reporting-unit'
import { ArrayEntity } from '../../models/common'
import { ReportingLocation } from '../../models/common/asset-hierarchy/reporting-location'
import { ReportingSite } from '../../models/common/asset-hierarchy/reporting-site'
import { EntityType, getCodeFromReportingSiteExternalId, GetSpace } from '../../utils/space-util'

export interface UnitQueryRequest {
    siteId: string
}

const buildUnitQuery = (request: UnitQueryRequest): string => {
    const filters: string[] = []
    const siteCode = getCodeFromReportingSiteExternalId(request.siteId)

    filters.push(`{ space: { eq: "${GetSpace(EntityType.REF)}" } }`, `{ name: { prefix: "${siteCode}" } }`)

    const queryFilter = `{ and: [ ${filters.join(', ')} ] }`

    return `
        query GetReportingUnit {
            listReportingUnit(
                filter: ${queryFilter}
                , first: 1000
                , sort: { name: ASC }
            ) {
                items {
                    externalId
                    name
                    description
                    aliases
                    createdTime
                    space
                    refersTo {
                        items {
                            name
                            externalId
                            space
                        }
                    }
                    reportingSites {
                        items {
                            externalId
                            siteCode
                            space
                        }
                    }
                }
            }
        }
    `
}

export const useReportingUnits = (request: UnitQueryRequest) => {
    const query = buildUnitQuery(request)
    const { data: fdmData } = useGraphqlQuery<ReportingUnit>(gql(query), 'listReportingUnit', {})

    const [resultData, setResultData] = useState<{ data: ReportingUnit[]; loading: boolean }>({
        data: [],
        loading: true,
    })

    useEffect(() => {
        if (fdmData.length == 0) {
            setResultData({ data: [], loading: false })
        } else {
            const fdmDataParsed = fdmData.map((d) => {
                const arrayEntity = d.refersTo as any as ArrayEntity<ReportingLocation>
                const arrayReportingSite = d.reportingSites as any as ArrayEntity<ReportingSite>

                if (arrayEntity.items.length || arrayReportingSite.items.length) {
                    return {
                        ...d,
                        refersTo: arrayEntity.items,
                        reportingSites: arrayReportingSite.items,
                    }
                }
                return d
            }) as ReportingUnit[]
            setResultData({ data: fdmDataParsed, loading: false })
        }
    }, [fdmData])

    return {
        loading: resultData.loading,
        units: resultData.data,
    }
}
