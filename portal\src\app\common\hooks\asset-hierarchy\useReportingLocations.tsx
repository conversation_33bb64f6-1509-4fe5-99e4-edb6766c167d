import { gql } from '@apollo/client'
import { useEffect, useState } from 'react'
import { useGraphqlQuery } from '../cognite/useGraphqlQuery'
import { ReportingLocation } from '../../models/common/asset-hierarchy/reporting-location'
import { getCodeFromReportingSiteExternalId } from '../../utils/space-util'

export interface LocationQueryRequest {
    siteId: string
    unitIds?: string[]
    onlyActive?: boolean
}

const buildLocationQuery = (request: LocationQueryRequest): string => {
    const filters: string[] = []
    const siteCode = getCodeFromReportingSiteExternalId(request.siteId)

    if (request?.unitIds?.length) {
        filters.push(`{ reportingUnit: { externalId:  { in: [${request.unitIds.map((e) => `"${e}"`).join(', ')}] } } }`)
    }

    filters.push(`{ name: { prefix: "${siteCode}" } }`)

    if (request.onlyActive) {
        filters.push(`{ isActive: { eq: true } }`)
    }

    const queryFilter = `{ and: [ ${filters.join(', ')} ] }`

    return `
        query GetReportingLocation {
            listReportingLocation(
                filter: ${queryFilter}
                , first: 1000
            ) {
                items {
                    externalId
                    name
                    description
                    aliases
                    createdTime
                    space
                    reportingUnit {
                        externalId
                        name
                        description
                        space
                    }
                }
            }
        }
    `
}

export const useReportingLocations = (request: LocationQueryRequest) => {
    const query = buildLocationQuery(request)
    const { data: fdmData } = useGraphqlQuery<ReportingLocation>(gql(query), 'listReportingLocation', {})

    const [resultData, setResultData] = useState<{ data: ReportingLocation[]; loading: boolean }>({
        data: [],
        loading: true,
    })

    useEffect(() => {
        if (fdmData.length == 0) {
            setResultData({ data: [], loading: false })
        } else {
            setResultData({ data: fdmData, loading: false })
        }
    }, [fdmData])

    return {
        loading: resultData.loading,
        locations: resultData.data,
    }
}
