from datetime import date
from typing import Annotated, Any, ClassVar, Literal, Optional

from cognite.client.data_classes.data_modeling import EdgeId
from industrial_model.queries import (
    NestedQueryParam,
    QueryParam,
)
from pydantic import (
    BaseModel,
    ConfigDict,
    Field,
    computed_field,
    model_validator,
)
from pydantic.alias_generators import to_camel

from clients.actions.requests import GetActionRequest
from clients.core.constants import DataSpaceEnum
from clients.core.models import BaseCamelCaseModel, GlobalModel, GlobalModelV2

from .constants import SourceEventStatus


def _create_node_as_dict(external_id: str, space: str) -> dict[str, str]:
    return {"externalId": external_id, "space": space}


class BaseSourceEventFields(GlobalModel):
    """Base request class for source events, defining common fields used for querying events."""

    external_ids: list[str] | None = None

    due_date_gte: date | None = None
    due_date_lt: date | None = None

    only_private: bool | None = None

    status_external_ids: list[str] | None = None
    reporting_unit_external_ids: list[str] | None = None
    reporting_line_external_ids: list[str] | None = None
    impacted_reporting_unit_external_ids: list[str] | None = None
    event_type_external_ids: list[str] | None = None
    category_external_ids: list[str] | None = None
    subcategory_external_ids: list[str] | None = None
    site_specific_category_external_ids: list[str] | None = None

    external_id_prefix: str | None = None
    title_prefix: str | None = None

    active_user_email: str | None = None
    active_user: str | None = None
    active_user_roles_ids: list[str] | None = None
    active_user_teams_ids: list[str] | None = None

    owner_external_id: list[str] | None = None

    equipment_external_ids: list[str] | None = None
    functional_location_external_ids: list[str] | None = None

    def get_filter_spaces(
        self,
        all_sites: bool = False,
        private_space: bool | None = None,
    ) -> list[str]:
        """Generate a list of spaces based on the given conditions."""
        if private_space:
            return [DataSpaceEnum.PRIVATE_SPACE]
        if private_space is not None:
            return self.spaces
        if all_sites:
            return [DataSpaceEnum.PRIVATE_SPACE, DataSpaceEnum.COR_SPACE, *self.spaces]
        return [DataSpaceEnum.PRIVATE_SPACE, *self.spaces]

    @computed_field
    @property
    def impacted_reporting_units(self) -> list[dict[str, str]]:
        """Compute the impacted reporting units from the external IDs."""
        if not self.impacted_reporting_unit_external_ids:
            return []
        return [
            _create_node_as_dict(external_id, DataSpaceEnum.REF_DATA_SPACE)
            for external_id in self.impacted_reporting_unit_external_ids
        ]

    @computed_field
    @property
    def views_private(self) -> list[str]:
        """Generate a list of users, roles, and teams for private view access."""
        if not self.active_user:
            return []
        return (
            [self.active_user]
            + (self.active_user_roles_ids or [])
            + (self.active_user_teams_ids or [])
        )


class BaseSourceEventRequest(BaseSourceEventFields, GlobalModel):
    """Base request para Source Events com tipos simples."""


class GetSourceEventRequest(BaseSourceEventRequest):
    """Request class for retrieving source events with search, sorting, and pagination."""

    search: str | None = None
    search_properties: ClassVar[list[str]] = ["title"]

    sort_by: str = "createdAt"
    direction: Literal["ASC", "DESC"] = "DESC"

    page_size: int = 10
    cursor: str | None = None


class GetSourceEventByIdRequest(GetSourceEventRequest):
    """Request class for retrieving a specific source event by its external ID."""

    external_id: str

    def model_post_init(self, _: Any) -> None:  # noqa: ANN401
        """Logic to ensure `external_id` is added to `external_ids`."""
        if self.external_id and (
            self.external_ids is None or self.external_id not in self.external_ids
        ):
            self.external_ids = (self.external_ids or []) + [self.external_id]


class UpsertSourceEventRequest(BaseModel):
    """Request class for creating or updating a source event with detailed attributes."""

    model_config = ConfigDict(
        alias_generator=to_camel,
        populate_by_name=True,
        from_attributes=True,
    )

    title: str = Field(..., min_length=10, max_length=200)
    description: str = Field(..., min_length=10, max_length=1000)
    due_date: Optional[date] = None
    assignment_date: Optional[date] = None

    category_id: str = Field(min_length=1, default="ACTC-General")
    sub_category_id: Optional[str] = Field(None, min_length=1)
    site_specific_category_id: Optional[str] = Field(None, min_length=1)
    reporting_unit_id: Optional[str] = Field(None, min_length=1)
    impacted_units_id: list[str] = Field(default_factory=list)
    reporting_location_id: Optional[str] = Field(None, min_length=1)
    impacted_locations_id: list[str] = Field(default_factory=list)

    application_id: str = Field(..., min_length=1)
    business_line_id: Optional[str] = Field(None, min_length=1)
    reporting_line_id: Optional[str] = Field(None, min_length=1)
    reporting_site_id: str = Field(..., min_length=1)
    attachment_ids: Optional[list[str]] = Field(None, min_items=1)

    created_by_id: str = Field(..., min_length=1)
    owner_id: Optional[str] = Field(None, min_length=1)
    secondary_owner_users_id: list[str] = Field(default_factory=list)
    secondary_owner_roles_id: list[str] = Field(default_factory=list)
    secondary_owner_teams_id: list[str] = Field(default_factory=list)
    view_users: list[str] = Field(default_factory=list)
    view_roles: list[str] = Field(default_factory=list)
    view_teams: list[str] = Field(default_factory=list)
    views: list[str] = Field(default_factory=list)

    private: bool = Field(default=False)

    target_external_id: Optional[str] = Field(None)

    equipments_id: list[str] = Field(default_factory=list)
    functional_locations_id: list[str] = Field(default_factory=list)

    @model_validator(mode="after")
    def validate_source_event_date(self) -> "UpsertSourceEventRequest":
        """Validate that the assignment date is before the due date."""
        if (
            self.assignment_date
            and self.due_date
            and self.assignment_date > self.due_date
        ):
            msg = "assignmentDate must be before dueDate."
            raise ValueError(msg)

        return self

    @computed_field
    @property
    def sap_space(self) -> str:
        """Computed SAP space identifier based on the reporting site ID."""
        site_code = self.reporting_site_id.split("-")[1]
        return f"SAP-{site_code}-ALL-DAT"


class CreateSourceEventRequest(UpsertSourceEventRequest):
    """Request class for creating a new source event."""


class UpdateSourceEventRequest(UpsertSourceEventRequest):
    """Request class for updating an existing source event, including deletion of edges and files."""

    external_id: str
    space: str

    edges_ids_to_delete: Optional[list[str]] = Field(default=None)
    files_ids_to_delete: Optional[list[str]] = Field(default=None)

    def build_edges_to_delete(self) -> list[EdgeId]:
        """Build a list of edges to be deleted based on the provided edge IDs."""
        if not self.edges_ids_to_delete:
            return []

        return [
            EdgeId(
                external_id=f"{self.external_id}-{node_external_id}",
                space=self.space,
            )
            for node_external_id in self.edges_ids_to_delete
            if node_external_id
        ]


class UpdateSourceEventStatusRequest(BaseModel):
    """Request class for updating the status of a source event."""

    model_config = ConfigDict(
        alias_generator=to_camel,
        populate_by_name=True,
        from_attributes=True,
    )

    external_id: str
    space: str
    status_id: SourceEventStatus

    active_user_email: str
    reporting_site_external_id: str

    actions_filters: GetActionRequest | None = None


class BaseSourceEventRequestForIndustrialModel(BaseSourceEventFields, GlobalModelV2):
    """Base request class for source events, defining common fields used for querying events."""

    external_ids: Annotated[
        list[str] | None,
        QueryParam(property="externalId", operator="in"),
    ] = None
    status_external_ids: Annotated[
        list[str] | None,
        NestedQueryParam(
            "status",
            QueryParam(property="externalId", operator="in"),
        ),
    ] = None
    reporting_unit_external_ids: Annotated[
        list[str] | None,
        NestedQueryParam(
            "reportingUnit",
            QueryParam(property="externalId", operator="in"),
        ),
    ] = None
    reporting_line_external_ids: Annotated[
        list[str] | None,
        NestedQueryParam(
            "reportingLine",
            QueryParam(property="externalId", operator="in"),
        ),
    ] = None
    event_type_external_ids: Annotated[
        list[str] | None,
        NestedQueryParam(
            "eventType",
            QueryParam(property="externalId", operator="in"),
        ),
    ] = None
    category_external_ids: Annotated[
        list[str] | None,
        NestedQueryParam("category", QueryParam(property="externalId", operator="in")),
    ] = None
    subcategory_external_ids: Annotated[
        list[str] | None,
        NestedQueryParam(
            "subCategory",
            QueryParam(property="externalId", operator="in"),
        ),
    ] = None
    owner_external_id: Annotated[
        list[str] | None,
        NestedQueryParam("owner", QueryParam(property="externalId", operator="in")),
    ] = None
    equipment_external_ids: Annotated[
        list[str] | None,
        QueryParam(property="searchTags", operator="containsAny"),
    ] = None
    functional_location_external_ids: Annotated[
        list[str] | None,
        QueryParam(property="searchTags", operator="containsAny"),
    ] = None


class GetSourceEventRequestForIndustrialModel(BaseSourceEventRequestForIndustrialModel):
    """Request model for retrieving actions with search and sorting options."""

    search: str | None = None
    search_properties: ClassVar[list[str]] = ["title"]

    sort_by: str = "createdAt"
    direction: Literal["ascending", "descending"] = "descending"

    page_size: int = 10
    cursor: str | None = None


class ExportSourceEventsRequest(BaseCamelCaseModel):
    """Request class for exporting source events with search, sorting, and pagination."""

    filters: GetSourceEventRequestForIndustrialModel
    columns: dict[str, str]
    translations: dict[str, str]
