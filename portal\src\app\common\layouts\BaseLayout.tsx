import { Wrapper } from '@celanese/ui-lib'
import Box from '@mui/material/Box'
import CssBaseline from '@mui/material/CssBaseline'
import useMediaQuery from '@mui/material/useMediaQuery'
import { Dispatch, ReactNode, SetStateAction, useContext, useEffect, useState } from 'react'
import { NoTranslate, UserManagementContext, UserManagementContextState } from '@celanese/celanese-ui'
import { HeaderNavBar } from '@/app/components/LayoutHeaderNavBar'
import LoadingScreen from '@/app/components/Loader/LoadingScreen'

interface BaseLayoutProps {
    children: ReactNode
    setLocaleCode: Dispatch<SetStateAction<string>>
    handleLangChanged: (localeCode: string) => void
    shouldTranslateDynamic: boolean | undefined
    setShouldTranslateDynamic: Dispatch<SetStateAction<boolean | undefined>>
    dynamicTranslationLoading: boolean
    setDynamicTranslationLoading: Dispatch<SetStateAction<boolean>>
}

const EXPANDED_WIDTH = 298
const COLLAPSED_WIDTH = 48

export default function BaseLayout({
    children,
    setLocaleCode,
    handleLangChanged,
    shouldTranslateDynamic,
    setShouldTranslateDynamic,
    dynamicTranslationLoading,
    setDynamicTranslationLoading,
}: BaseLayoutProps) {
    const { userInfo, loading: loadingUserInfo } = useContext<UserManagementContextState>(UserManagementContext)
    const isMobile = useMediaQuery('(max-width:599px)')
    const [sidebarWidth, setSidebarWidth] = useState(COLLAPSED_WIDTH)

    useEffect(() => {
        const checkSidebarState = () => {
            if (isMobile) {
                setSidebarWidth(0)
            } else {
                const flag = localStorage.getItem('cln_cln-header-navbar')
                setSidebarWidth(flag === 'true' ? EXPANDED_WIDTH : COLLAPSED_WIDTH)
            }
        }

        checkSidebarState()

        const interval = setInterval(checkSidebarState, 500)
        return () => clearInterval(interval)
    }, [isMobile])

    return (
        <Box>
            <CssBaseline />
            <NoTranslate>
                <HeaderNavBar
                    setLocaleCode={setLocaleCode}
                    onLangChanged={handleLangChanged}
                    shouldTranslateDynamicState={{
                        shouldTranslateDynamic,
                        setShouldTranslateDynamic,
                    }}
                    dynamicTranslationLoadingState={{
                        dynamicTranslationLoading,
                        setDynamicTranslationLoading,
                    }}
                />
            </NoTranslate>

            <Wrapper navBarOffset={-COLLAPSED_WIDTH} sideBarOffset={-sidebarWidth}>
                <div style={{ paddingTop: COLLAPSED_WIDTH, paddingLeft: sidebarWidth }}>
                    {loadingUserInfo || !userInfo?.email ? <LoadingScreen /> : children}
                </div>
            </Wrapper>
        </Box>
    )
}
