import { ChangeEvent, createContext, useContext } from 'react'

export type UploadError = {
    line: string
    field: string
    error: string
}

export type State = {
    modalType?: 'loading' | 'result'
    successMessage: string
    errorMessage: string
    errorsCount: number
    errors: UploadError[]
    isLoading: boolean
}

export type BulkUploadContextType = {
    state: State
    siteId: string
    userId: string
    eventId?: string
    setState: (value: State | ((prev: State) => State)) => void
    handleBulkUpload: (
        e: ChangeEvent<HTMLInputElement>,
        siteId: string,
        userId?: string,
        eventId?: string,
        debouncedFetchActions?: () => void
    ) => void
    generateErrorReportCsv: () => void
}

export const BulkUploadContext = createContext<BulkUploadContextType | undefined>(undefined)

export const useBulkUpload = () => {
    const context = useContext(BulkUploadContext)
    if (!context) {
        throw new Error('useBulkUpload must be used inside BulkUploadProvider')
    }
    return context
}
