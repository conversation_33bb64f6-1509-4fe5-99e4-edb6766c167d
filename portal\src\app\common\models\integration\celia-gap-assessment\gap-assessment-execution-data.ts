import { ExternalEntity } from "../../common"


interface Topic extends ExternalEntity {
    name?: string
    assessmentCategory?: GapAssessmentCategory
}

interface GapAssessmentExecution extends ExternalEntity {
    topic?: { items: Topic[] }
}

export interface GapAssessmentResponse extends GapAssessmentExecutionData {
    categoryName?: string
    topicName?: string[]
}

export interface GapAssessmentExecutionData extends ExternalEntity {
    requirement?: string
    verification?: string
    systemInPlace?: string
    gapDetails?: string
    gapAssessmentExecutions?: { items: GapAssessmentExecution[] }
}

export interface GapAssessmentCategory extends ExternalEntity {
    categoryName?: string
}
