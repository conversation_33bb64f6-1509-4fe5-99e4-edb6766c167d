import json as json
import logging
from http import HTTPStatus

import azure.functions as func

from clients.core.constants import APPLICATION_JSON
from clients.reporting_location.requests import GetReportingLocationsBySitesRequest
from infra.action_item_client_factory import ActionItemClientFactory

bp = func.Blueprint()


@bp.function_name(name="GetLocationsBySites")
@bp.route(
    "get-locations-by-sites",
    methods=["get"],
    auth_level=func.AuthLevel.ANONYMOUS,
)
def main(req: func.HttpRequest) -> func.HttpResponse:
    """Handle GET requests to retrieve reporting locations by site based on request parameters."""
    logging.info("Function GetLocationsBySites started")

    try:
        auth_header = req.headers.get("Authorization")
        if auth_header and auth_header.startswith("Bearer "):
            token_request = auth_header[len("Bearer ") :]
        else:
            token_request = auth_header

        user_complement_request_param = req.params.get(
            "reportingLocationsRequest",
            "{}",
        )
        user_complement_request = json.loads(user_complement_request_param)

        request = GetReportingLocationsBySitesRequest.model_validate(
            user_complement_request,
        )
        client = ActionItemClientFactory.retriever(override_token=token_request)

        response = client.reporting_location.get_reporting_locations_by_sites(
            request,
        )

        items = response.data if response is not None else []

        items_dict = [i.model_dump(mode="json", by_alias=True) for i in items]
        response_body = json.dumps(items_dict)

        logging.info("Finishing execution - GetLocationsBySites")

        return func.HttpResponse(
            response_body,
            status_code=HTTPStatus.OK,
            mimetype=APPLICATION_JSON,
        )
    except Exception as e:
        logging.error(f"Exception found: {e}", exc_info=True)
        return func.HttpResponse(
            f"Error: {e}",
            status_code=HTTPStatus.INTERNAL_SERVER_ERROR,
        )
