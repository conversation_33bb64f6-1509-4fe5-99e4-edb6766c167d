import { MatIcon } from '@celanese/ui-lib'
import { Box, DialogActions, DialogTitle, type SxProps, type Theme, IconButton, Modal, useTheme } from '@mui/material'
import React from 'react'

interface ModalPopoverProps {
    title?: string
    content: React.ReactNode
    openModal: boolean
    closeModal: () => void
    sxProps?: SxProps<Theme>
    sxPropsTitle?: SxProps<Theme>
    whiteBackground?: boolean
    dataTest?: string
    hideBackdrop?: boolean
}

export const ModalWrapper = ({
    title,
    content,
    openModal,
    closeModal,
    sxProps,
    sxPropsTitle,
    whiteBackground,
    dataTest,
    hideBackdrop = true,
}: ModalPopoverProps) => {
    const theme = useTheme()

    return (
        <Modal
            open={openModal}
            onClose={closeModal}
            sx={{ ...sxProps, alignItems: 'center', justifyContent: 'center', display: 'flex' }}
            hideBackdrop={hideBackdrop}
        >
            <Box
                sx={{
                    maxHeight: '90%',
                    bgcolor: 'background.paper',
                    borderRadius: '8px',
                    background: whiteBackground ? 'background.paper' : 'background.default',
                    overflow: 'auto',
                }}
            >
                <Box
                    sx={{
                        display: 'flex',
                        flexDirection: 'row',
                        alignItems: 'start',
                        justifyContent: 'space-between',
                        mb: 0,
                    }}
                >
                    {title && (
                        <DialogTitle
                            sx={
                                sxPropsTitle
                                    ? { ...sxPropsTitle }
                                    : {
                                          color: theme.palette.grey[600],
                                          fontSize: '19px',
                                          marginRight: 'auto',
                                          wordBreak: 'break-word',
                                          whiteSpace: 'normal',
                                          maxWidth: 'calc(100% - 60px)',
                                      }
                            }
                        >
                            {title}
                        </DialogTitle>
                    )}

                    <DialogActions sx={{ marginLeft: 'auto' }}>
                        <IconButton onClick={closeModal} sx={{ ml: 'auto' }} data-test={dataTest} data-origin="aim">
                            <MatIcon icon="close" />
                        </IconButton>
                    </DialogActions>
                </Box>

                {content}
            </Box>
        </Modal>
    )
}
