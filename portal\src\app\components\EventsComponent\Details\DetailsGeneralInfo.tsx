'use client'
import { Box, Grid, Typography, useMediaQuery, useTheme } from '@mui/material'
import { useMemo } from 'react'
import GenericFieldText from '../../FieldsComponent/GenericFieldText'
import { GenerateStatusChip } from '../../StatusComponet'
import { SourceEventStatusClearEnum } from '@/app/common/enums/SourceEventStatusEnum'
import { translate } from '@/app/common/utils/generate-translate'
import { SourceEvent } from '@/app/common/models/source-event'
import { ClnChip, MatIcon } from '@celanese/ui-lib'

type Props = { id: string; sourceEvent?: SourceEvent }

export function DetailsGeneralInfo(params: Props) {
    const { id, sourceEvent } = params
    const theme = useTheme()

    const isMobile = useMediaQuery(theme.breakpoints.down('sm'))
    const isTablet = useMediaQuery(theme.breakpoints.down('md'))

    const columnsPerRow = isMobile ? 1 : isTablet ? 2 : 5

    const unitsList = useMemo(() => {
        const units = sourceEvent?.reportingUnits
        return units?.map((x: any) => `${x?.description}`).join(' | ') || '-'
    }, [sourceEvent])

    const locationsList = useMemo(() => {
        const location = sourceEvent?.impactedReportingLocations
        return location?.map((x: any) => `${x?.description}`).join(' | ') || '-'
    }, [sourceEvent])

    const fields = [
        {
            label: translate('table.headers.id'),
            value: id,
        },
        {
            label: translate('details.fields.eventOwner'),
            value: sourceEvent?.owner
                ? `${sourceEvent.owner?.user?.lastName}, ${sourceEvent.owner?.user?.firstName}`
                : '-',
        },
        {
            label: translate('details.fields.category'),
            value: sourceEvent?.category?.name ?? '-',
        },
        {
            label: translate('details.fields.subcategory1'),
            value: sourceEvent?.subCategory?.name ?? '-',
        },
        {
            label: translate('details.fields.subcategory2'),
            value: sourceEvent?.siteSpecificCategory?.name ?? '-',
        },
        {
            label: translate('details.fields.unit'),
            value: sourceEvent?.reportingUnit?.description ?? '-',
        },
        {
            label: translate('details.fields.reportingLocation'),
            value: sourceEvent?.reportingLocation?.description ?? '-',
        },
        {
            label: translate('details.fields.businessLine'),
            value: sourceEvent?.businessLine?.description ?? '-',
        },
        {
            label: translate('stepper.form.reportingLine'),
            value: sourceEvent?.reportingLine?.description ?? '-',
        },
        {
            label: translate('details.fields.functionalLocation'),
            value: sourceEvent?.functionalLocations[0]?.name ?? '-',
        },
        {
            label: translate('details.fields.equipment'),
            value: sourceEvent?.equipments[0]
                ? `${sourceEvent?.equipments[0].number} | ${sourceEvent?.equipments[0].name} | ${sourceEvent?.equipments[0].description}`
                : '-',
            ellipsis: true,
        },
        {
            label: translate('details.fields.impactedUnit'),
            value: unitsList,
            ellipsis: true,
        },
        {
            label: translate('details.fields.impactedLocation'),
            value: locationsList,
            ellipsis: true,
        },
    ]

    return (
        <Box
            sx={{
                width: '100%',
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                '& .MuiFormControl-root': {
                    maxWidth: '100% !important',
                    width: '100% !important',
                },
                '& > *': { width: '100% !important' },
                gap: '1rem',
                padding: '10px',
            }}
        >
            <Box
                sx={{
                    display: 'flex',
                    flexGrow: 1,
                    flexDirection: 'row',
                    gap: '1rem',
                    overflowWrap: 'break-word',
                    whiteSpace: 'normal',
                    alignItems: 'center',
                }}
            >
                <Typography
                    variant="h3"
                    sx={{
                        fontWeight: 'bold',
                        wordBreak: 'break-word',
                        whiteSpace: 'normal',
                        overflowWrap: 'break-word',
                        flexShrink: 1,
                    }}
                >
                    {sourceEvent?.title}
                </Typography>
                {sourceEvent?.isPrivate && <GenerateStatusChip statusId={'private'} />}
                {sourceEvent && (
                    <GenerateStatusChip
                        statusId={sourceEvent?.status?.externalId ?? SourceEventStatusClearEnum.inProgress}
                    />
                )}
                {sourceEvent && (
                    <ClnChip
                        icon={<MatIcon icon="location_on" color="warning.main" />}
                        label={sourceEvent?.reportingSite?.name}
                        size="small"
                        sxProps={{
                            backgroundColor: 'warning.100',
                            color: 'warning.main',
                            '&:hover': {
                                backgroundColor: 'warning.100',
                                color: 'warning.main',
                            },
                        }}
                        variant="filled"
                    />
                )}
            </Box>
            <Grid container sx={{ rowGap: 1 }}>
                {fields.map((item, index) => {
                    const colIndex = index % columnsPerRow
                    const isFirstCol = colIndex === 0
                    const isLastCol = colIndex === columnsPerRow - 1

                    return (
                        <Grid
                            key={index}
                            item
                            xs={12}
                            sm={6}
                            md={2.4}
                            lg={2.4}
                            sx={{
                                pl: isFirstCol ? 0 : 1,
                                pr: isLastCol ? 0 : 1,
                            }}
                        >
                            <GenericFieldText fieldName={item.label} value={item.value} ellipsis={item.ellipsis} />
                        </Grid>
                    )
                })}
            </Grid>
        </Box>
    )
}

export default DetailsGeneralInfo
