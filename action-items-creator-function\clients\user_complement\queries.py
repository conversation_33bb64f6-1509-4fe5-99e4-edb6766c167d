GET_USER_COMPLEMENTS_QUERY = """
query GetUserComplements($userComplementFilter: _ListUserComplementFilter) {
  listUserComplement(filter: $userComplementFilter, first: 1000) {
    items {
      externalId
      space
      userAzureAttribute {
        externalId
        space
        user {
          active
          externalId
          email
          firstName
          lastName
          space
        }
      }
    }
  }
}
"""

GET_USER_COMPLEMENT_BY_EMAIL_QUERY = """
query GetUserComplementByEmail(
  $userFilter: _ListUserComplementFilter,
  $roleFilter: _ListUserRoleSiteFilter,
  $teamFilter: _ListTeamFilter,
) {
  listUserComplement(filter: $userFilter, first: 1000) {
    items {
      externalId
      space
      userRoleSite(filter: $roleFilter, first: 1000) {
        items {
          externalId
          space
          role {
            externalId
            space
            site {
              externalId
              space
            }
          }
        }
      }
      userAzureAttribute {
        externalId
        space
        user {
          externalId
          space
          firstName
          lastName
          email
          active
          teams(filter: $teamFilter, first: 1000) {
            items {
              externalId
              space
              reportingSite {
                externalId
                space
              }
            }
          }
        }
      }
    }
  }
}
"""
