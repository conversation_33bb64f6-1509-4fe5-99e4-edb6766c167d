'use client'
import '../../../common/utils/polyfills'
import { AzureFunctionClient } from '@/app/common/clients/azure-function-client'
import { UserManagementContext, UserManagementContextState } from '@celanese/celanese-ui'
import { useDebounceFunction } from '@/app/common/hooks/general-functions/useDebounce'
import { SourceEvent, SourceEventResponse } from '@/app/common/models/source-event'
import EventForms from '@/app/components/EventsComponent/EventForm/eventForms'
import LoaderCircular from '@/app/components/Loader'
import MessageModal from '@/app/components/ModalComponent/Modal/MessageModal'
import { useRouter } from 'next/navigation'
import { useCallback, useContext, useEffect, useMemo, useState } from 'react'
import AuthGuardWrapper from '@/app/common/wrapper/AuthGuardWrapper'
import { BackArrow } from '@/app/common/utils/backArrow'
import { ClnPage, ClnPanel } from '@celanese/ui-lib'
import { useSnackbar } from '@/app/common/contexts/SnackbarContext'
import { translate } from '@/app/common/utils/generate-translate'
import { UserRolesPermission } from '@celanese/celanese-sdk'
import { UserExternalContext, UserExternalContextState } from '@/app/common/contexts/UserExternalContext'
import { AllActionStatusExternalIdEnum } from '@/app/common/enums/ActionItemStatusEnum'

type SourceEventEditPageProps = {
    params: {
        slug: string[]
    }
}

export default function SourceEventEditPage({ params }: SourceEventEditPageProps) {
    const { id, siteId } = useMemo(() => {
        return params.slug?.length === 2
            ? { id: params.slug[1], siteId: `STS-${params.slug[0]}` }
            : { id: params.slug[0], siteId: undefined }
    }, [params])

    const { showSnackbar } = useSnackbar()
    const router = useRouter()

    const { userInfo } = useContext<UserManagementContextState>(UserManagementContext)
    const { userExternalInfo } = useContext<UserExternalContextState>(UserExternalContext)

    const [activeUser, setActiveUser] = useState<UserRolesPermission>()
    const [isLoadingDetails, setIsLoadingDetails] = useState<boolean>(true)
    const [isAuthorized, setIsAuthorized] = useState<boolean>(false)

    const [sourceEvent, setSourceEvent] = useState<SourceEvent>()

    const [cancelModalOpen, setCancelModalOpen] = useState(false)

    const fetchEvent = useCallback(async () => {
        try {
            if (sourceEvent) return

            setIsLoadingDetails(true)
            const client = new AzureFunctionClient()

            if (activeUser?.email) {
                const result: SourceEventResponse = await client.getSourceEventById({
                    externalId: id,
                    activeUserEmail: activeUser?.email,
                    reportingSiteExternalId: siteId
                        ? [siteId]
                        : activeUser.applications[0].userSites.map((item) => item.siteId),
                })

                const sourceEventDetail =
                    Array.isArray(result.data) && result.data.length > 0
                        ? result.data[0]
                        : (result.data as SourceEvent | undefined)

                setSourceEvent(sourceEventDetail)
                const userCanEditEvent =
                    (sourceEventDetail?.secondaryOwnerUsers?.some(
                        (item: any) => item?.user?.email === userInfo?.email
                    ) ||
                        sourceEventDetail?.owner?.user?.email === userInfo?.email) &&
                    sourceEventDetail?.status?.externalId !== AllActionStatusExternalIdEnum.Cancelled
                setIsAuthorized((result.access && userCanEditEvent) ?? false)
            } else {
                setIsAuthorized(false)
            }
        } catch {
            showAlert('alerts.unexpectedErrorOcurred', true)
        } finally {
            setIsLoadingDetails(false)
        }
    }, [activeUser])

    const debouncedFetchEvent = useCallback(useDebounceFunction(fetchEvent, 300), [fetchEvent])

    useEffect(() => {
        if (activeUser?.email !== undefined) debouncedFetchEvent()
    }, [fetchEvent])

    useEffect(() => {
        if (
            userExternalInfo.externalId &&
            userExternalInfo.externalId !== '' &&
            (activeUser?.externalId !== userExternalInfo.externalId ||
                activeUser?.applications !== userInfo.applications)
        ) {
            setActiveUser({
                ...userInfo,
                externalId: userExternalInfo.externalId,
                roles: userExternalInfo.roles,
                teams: userExternalInfo.teams,
            })
        }
    }, [userInfo, userExternalInfo])

    const showAlert = (messageKey: string, isError: boolean) => {
        showSnackbar(translate(messageKey), isError ? 'error' : 'success', 'edit-event')
    }

    const handleLeaveCancelModal = () => {
        localStorage.removeItem('isEditForm')
        setCancelModalOpen(false)
        router.push(`/event-source/details/${sourceEvent?.reportingSite?.siteCode}/${id}`)
    }

    return (
        <>
            {isLoadingDetails ? (
                LoaderCircular()
            ) : (
                <AuthGuardWrapper
                    componentName={
                        [1, 2].includes(params.slug.length) && isAuthorized
                            ? SourceEventEditPage.name
                            : 'UnknownComponent'
                    }
                    siteId={siteId ?? sourceEvent?.reportingSite?.externalId!}
                >
                    <ClnPage>
                        <ClnPanel
                            id="event-edit-panel"
                            sx={{
                                display: 'flex',
                                flexDirection: 'column',
                                padding: '20px',
                                gap: '0.5rem',
                            }}
                        >
                            {BackArrow(translate('source-event.create'), () => setCancelModalOpen(true))}
                            <EventForms
                                onClose={setCancelModalOpen}
                                sourceEvent={sourceEvent}
                                fromEdit
                                siteId={siteId ?? sourceEvent?.reportingSite?.externalId!}
                            />
                        </ClnPanel>
                        <MessageModal
                            name=""
                            text={translate('requestModal.closeQuestion')}
                            open={cancelModalOpen}
                            isCancelModal
                            handleClose={() => setCancelModalOpen(false)}
                            handleLeave={handleLeaveCancelModal}
                        />
                    </ClnPage>
                </AuthGuardWrapper>
            )}
        </>
    )
}
