import { Box, styled, Typography } from '@mui/material'

export const ModalContentBox = styled(Box)(({ isMobile }: { isMobile: boolean }) => ({
    display: 'flex',
    flexDirection: 'column',
    width: isMobile ? 'auto' : '80vw',
    maxWidth: 1200,
    padding: '0 24px 24px 24px',
}))

export const ButtonsContainer = styled(Box)({
    display: 'flex',
    justifyContent: 'flex-end',
    gap: '1rem',
    marginTop: '24px',
})

export const modalTitleSxProps = {
    color: 'text/primary',
    fontSize: '24px',
    marginRight: 'auto',
    wordBreak: 'break-word',
    whiteSpace: 'normal',
    maxWidth: 'calc(100% - 60px)',
    paddingBottom: '0px !important',
}

export const modalBackgroundSxProps = {
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
}

export const TabContainer = styled(Box)(({ theme }) => ({
    paddingTop: '1rem',
    fontFamily: 'Roboto, sans-serif',
    background: theme.palette.background.paper,
}))

export const LoadingBox = styled(Box)({
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    height: '100%',
    minHeight: '200px',
})

export const ContentBox = styled(Box)({
    display: 'flex',
    flexDirection: 'column',
    gap: '16px',
    width: '100%',
})

export const StyledTitle = styled(Typography)(({ theme }) => ({
    fontSize: '20px',
    fontWeight: 'bold',
    color: theme.palette.text.primary,
    whiteSpace: 'pre-wrap',
    wordBreak: 'break-word',
    overflowWrap: 'break-word',
}))

export const SubtitleContainer = styled(Box)(({ theme }) => ({
    display: 'flex',
    justifyContent: 'flex-start',
    alignItems: 'center',
    gap: '6px',

    [theme.breakpoints.down('sm')]: {
        flexDirection: 'column',
        alignItems: 'flex-start',
    },
}))

export const SectionContainer = styled(Box)(({ theme }) => ({
    display: 'flex',
    flexDirection: 'column',
    border: '1px solid #C4C4C4',
    padding: '24px',
    borderRadius: '8px',
    background: theme.palette.background.paper,
    width: '100%',
    gap: '30px',
}))

export const SubsectionContainer = styled(Box)({
    display: 'flex',
    flexDirection: 'column',
    gap: '16px',
    width: '100%',
})

export const SubsectionTitle = styled(Typography)(({ theme }) => ({
    fontSize: '18px',
    fontWeight: 'bold',
    color: theme.palette.text.primary,
    whiteSpace: 'pre-wrap',
    wordBreak: 'break-word',
    overflowWrap: 'break-word',
}))

export const ColumnTitle = styled(Typography)(({ theme }) => ({
    color: theme.palette.text.primary,
    fontWeight: 'bold',
    fontSize: '18px',
    marginBottom: '16px',
    whiteSpace: 'pre-wrap',
    wordBreak: 'break-word',
    overflowWrap: 'break-word',
}))

export const FieldsWrapper = styled(Box)({
    display: 'flex',
    flexDirection: 'column',
    gap: '10px',
})

export const FieldContainer = styled(Box)(({ theme }) => ({
    fontWeight: 600,
    color: theme.palette.text.secondary,
    fontSize: '18px',
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
    gap: '4px',
}))

export const FieldLabel = styled(Typography)(({ theme }) => ({
    fontSize: '16px',
    fontHeight: '18px',
    fontWeight: 600,
    color: theme.palette.text.secondary,
    whiteSpace: 'pre-wrap',
    wordBreak: 'break-word',
    overflowWrap: 'break-word',
}))

export const FieldValue = styled(Typography)(({ theme }) => ({
    fontSize: '16px',
    fontHeight: '18px',
    fontWeight: 500,
    color: theme.palette.text.primary,
    whiteSpace: 'pre-wrap',
    wordBreak: 'break-word',
    overflowWrap: 'break-word',
}))
