import React from 'react'
import { Controller } from 'react-hook-form'
import { InputAdornment, InputBaseProps, TextField } from '@mui/material'
import { MatIcon } from '@celanese/ui-lib'
import { translate } from '@/app/common/utils/generate-translate'

type GenericTextFieldProps = {
    name: string
    control?: any
    label?: string
    placeholder?: string
    helperText?: string
    error?: boolean
    valueController?: string
    rows?: number | string
    inputProps?: InputBaseProps['inputProps']
    isSearchIcon?: boolean
    required?: boolean
    disabled?: boolean
    shrink?: boolean
    onChange?: (newValue: string) => void
    type?: string
}

const GenericTextField = ({
    name,
    control,
    label,
    placeholder,
    helperText,
    error,
    valueController,
    rows,
    isSearchIcon,
    inputProps,
    required,
    disabled = false,
    shrink = true,
    onChange,
    type,
}: GenericTextFieldProps) => (
    <Controller
        name={name}
        control={control}
        render={({ field: { value, onChange: onChangeField } }) => (
            <TextField
                disabled={disabled}
                value={value ?? ''}
                defaultValue={valueController}
                onChange={(e) => {
                    onChangeField(e.target.value)
                    onChange && onChange(e.target.value)
                }}
                required={required}
                size="small"
                variant="outlined"
                label={label}
                rows={rows}
                multiline={Boolean(rows)}
                placeholder={placeholder ?? translate('table.filter.writeHere')}
                sx={{
                    width: '100%',
                    color: 'primary',
                }}
                InputLabelProps={{
                    ...(shrink && { shrink }),
                }}
                InputProps={
                    isSearchIcon
                        ? {
                              startAdornment: (
                                  <InputAdornment position="start">
                                      <MatIcon icon="search" />
                                  </InputAdornment>
                              ),
                          }
                        : {}
                }
                helperText={helperText}
                error={error}
                type={type ?? 'text'}
                {...(inputProps && { inputProps: inputProps })}
            />
        )}
    />
)

export default GenericTextField
