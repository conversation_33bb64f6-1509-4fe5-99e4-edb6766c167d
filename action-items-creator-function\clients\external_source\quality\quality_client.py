from typing import Optional

from clients.core.models import ExternalSourceServiceParams, PaginatedData
from clients.external_source.quality.models import QualityNotification
from clients.external_source.quality.queries import (
    GET_QUALITY_NOTIFICATION,
)
from clients.external_source.quality.responses import QualityNotificationResponse


class QualityClient:
    """Client for retrieving quality event data from the external data model."""

    def __init__(self, params: ExternalSourceServiceParams) -> None:
        """
        Initialize the QualityClient with external source service parameters.

        Args:
            params (ExternalSourceServiceParams): Configuration parameters including Cognite client,
                                                  logger, and data model reference.

        """
        self._cognite_client = params.cognite_client
        self._data_model_id = params.data_model.as_id()

    def get_notification(
        self,
        external_id: str,
    ) -> Optional[QualityNotificationResponse]:
        """
        Retrieve quality notification for a given external ID.

        Args:
            external_id (str): The external identifier of the event.

        Returns:
            Optional[QualityNotificationResponse]: The quality notification if found, otherwise None.

        """
        result = self._cognite_client.data_modeling.graphql.query(
            id=self._data_model_id,
            query=GET_QUALITY_NOTIFICATION,
            variables={"filter": {"externalId": {"eq": external_id}}},
        )

        notification = (
            PaginatedData[QualityNotification]
            .from_graphql_response(result, 1)
            .first_or_default()
        )

        return notification.to_response() if notification is not None else None
