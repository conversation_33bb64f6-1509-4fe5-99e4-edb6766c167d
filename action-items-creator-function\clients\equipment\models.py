from typing import Optional

from pydantic import computed_field

from clients.core.models import Node


class EquipmentResult(Node):
    """Data model representing a equipment node result."""

    name: Optional[str]
    description: Optional[str]
    number: Optional[str]
    functional_location_parent: Optional[Node]

    @computed_field
    @property
    def label(self) -> str:
        """Formatted label combining number, name, and description."""
        return f"{self.number or ''} | {self.name or ''} | {self.description or ''}"
