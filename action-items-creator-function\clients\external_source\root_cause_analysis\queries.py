GET_EVENT_ANALYSIS = """
query ListEventAnalysis(
  $filter: _ListEventAnalysisFilter
) {
  listEventAnalysis(filter: $filter) {
    items {
      externalId
      space
      externalEventId
      eventTitle
      eventType {
        externalId
        space
        name
      }
      startDate
      dueDate
      assignedTo {
        externalId
        space
        userAzureAttribute {
          externalId
          space
          user {
            externalId
            space
            displayName
          }
        }
      }
      team {
        items {
          externalId
          space
          userAzureAttribute {
            externalId
            space
            user {
              externalId
              space
              displayName
            }
          }
        }
      }
      investigationType {
        externalId
        space
        name
      }
      rootCauses
      approver {
        items {
          externalId
          space
          approver {
            externalId
            space
            userAzureAttribute {
              externalId
              space
              user {
                externalId
                space
                displayName
              }
            }
          }
        }
      }
      reportingLocation {
        externalId
        space
        name
        description
      }
    }
  }
}
"""

GET_EVENT_INVESTIGATION = """
query ListEventInvestigation(
  $filter: _ListEventInvestigationFilter
) {
  listEventInvestigation(filter: $filter) {
    items {
      externalId
      space
      reportingUnit {
        externalId
        space
        name
        description
      }
      businessLine {
        externalId
        space
        name
        description
      }
      functionalLocation {
        externalId
        space
        name
        description
      }
      equipment {
        externalId
        space
        name
        description
      }
    }
  }
}
"""

GET_WORK_PROCESS_INVESTIGATION = """
query ListWorkProcessInvestigation(
  $filter: _ListWorkProcessInvestigationFilter
) {
  listWorkProcessInvestigation(filter: $filter) {
    items {
      externalId
      space
      reportingUnit {
        externalId
        space
        name
        description
      }
      functionalLocation {
        externalId
        space
        name
        description
      }
      equipment {
        externalId
        space
        name
        description
      }
      problemDescription
    }
  }
}
"""
