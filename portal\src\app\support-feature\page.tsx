'use client'
import '../common/utils/polyfills'
import { useContext, useState } from 'react'
import { useReportingSites } from '../common/hooks/asset-hierarchy/useReportingSites'
import AuthGuardWrapper from '../common/wrapper/AuthGuardWrapper'
import { AZURE_ACTION_PATH, AZURE_APP_CODE, APP_NAME } from '../common/utils'
import { Box } from '@mui/material'
import { SuccessScreen } from '../components/SupportFeature/success-screen'
import { SupportFeaturePanel } from '../components/SupportFeature'
import { translate, UserManagementContext, UserManagementContextState } from '@celanese/celanese-ui'
import { styles } from './style'
import { ClnPage } from '@celanese/ui-lib'
import PageHeader from '../components/PageHeader'

export default function Support() {
    const { userInfo } = useContext<UserManagementContextState>(UserManagementContext)

    const { sites: reportingSites, loading: loadingSites } = useReportingSites({})
    const sitesData = reportingSites?.map((site) => ({
        externalId: site.externalId,
        value: site.name!,
    }))
    const [ticketNumber, setTicketNumber] = useState('')
    const [isSubmitted, setIsSubmitted] = useState(false)
    const [formKey, setFormKey] = useState(0)
    const handleSuccess = (ticket: string) => {
        setTicketNumber(ticket)
        setIsSubmitted(true)
    }
    const resetForm = () => {
        setIsSubmitted(false)
        setFormKey((prevKey) => prevKey + 1)
    }
    const actionItemAreaPath = AZURE_ACTION_PATH
    const useCase = APP_NAME
    const appCode = AZURE_APP_CODE

    document.title = translate('supportFeature.support') + ' ' + '|' + ' ' + APP_NAME
    return (
        <AuthGuardWrapper componentName={Support.name}>
            <ClnPage>
                <Box sx={{ display: 'flex', flexDirection: 'column' }}>
                    <PageHeader title={translate('supportFeature.support')} />
                    <Box sx={styles.formWrapper}>
                        {isSubmitted ? (
                            <SuccessScreen onBack={resetForm} ticketNumber={ticketNumber} />
                        ) : (
                            <SupportFeaturePanel
                                appCode={appCode}
                                areaPath={actionItemAreaPath}
                                key={formKey}
                                userInfo={userInfo}
                                onSuccess={handleSuccess}
                                sites={sitesData}
                                loadingSites={loadingSites}
                                useCase={useCase}
                            />
                        )}
                    </Box>
                </Box>
            </ClnPage>
        </AuthGuardWrapper>
    )
}
