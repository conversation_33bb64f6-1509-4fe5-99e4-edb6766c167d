# NOTE: Copied from api/ because pylance only looks for pyproject.toml in root dir

[tool.ruff]
# Exclude a variety of commonly ignored directories.
exclude = [
    ".bzr",
    ".direnv",
    ".eggs",
    ".git",
    ".git-rewrite",
    ".hg",
    ".ipynb_checkpoints",
    ".mypy_cache",
    ".nox",
    ".pants.d",
    ".pyenv",
    ".pytest_cache",
    ".pytype",
    ".ruff_cache",
    ".svn",
    ".tox",
    ".venv",
    ".vscode",
    "__pypackages__",
    "_build",
    "buck-out",
    "build",
    "dist",
    "node_modules",
    "site-packages",
    "venv",
]

# Same as Black.
line-length = 88
indent-width = 4

[tool.ruff.lint]
select = ["ALL"]
# TODO: copy this ignore to the function
ignore = [
    "D212",
    "D100",
    "D104",
    "FIX002",
    "TD002",
    "TD003",
    "E501",
    "D400",
    "D415",
    "D204",
    "FA102",
    "FA100",
    "B008",
    "D107",
    "TID252",
    "D203",
]

# Allow fix for all enabled rules (when `--fix`) is provided.
fixable = ["ALL"]
unfixable = []

# Allow unused variables when underscore-prefixed.
dummy-variable-rgx = "^(_+|(_+[a-zA-Z0-9_]*[a-zA-Z0-9]+?))$"

[tool.pyright]
typeCheckingMode = "strict"
venvPath = "."
venv = ".venv"

reportUnknownMemberType = "information"
reportUnknownArgumentType = "information"
reportUnusedImport = false # handled by ruff
