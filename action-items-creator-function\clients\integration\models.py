from pydantic import BaseModel, ConfigDict
from clients.core.models import BaseEntity

from ._models import UserApplication, UserSite, UserUnit

from pydantic.alias_generators import to_camel


class UserIntegrationResponse(BaseModel):
    model_config = ConfigDict(
        alias_generator=to_camel,
        populate_by_name=True,
        from_attributes=True,
    )
    display_name: str
    first_name: str
    last_name: str
    email: str
    lan_id: str
    company_name: str
    job_title: str
    department: str
    avatar: str
    favorite_reporting_site: UserSite | None = None
    favorite_language: str | None = None
    sites: list[UserSite]
    units: list[UserUnit]
    applications: list[UserApplication]

class UserByNodeAccessResponse(BaseEntity):
    model_config = ConfigDict(
        alias_generator=to_camel,
        populate_by_name=True,
        from_attributes=True,
    )
    external_id: str
    space: str
    first_name: str
    last_name: str
    email: str