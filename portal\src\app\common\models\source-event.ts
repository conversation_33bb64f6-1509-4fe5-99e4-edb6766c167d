import { ExternalEntity } from './common'
import { BusinessLine } from './common/asset-hierarchy/business-line'
import { ReportingLocation } from './common/asset-hierarchy/reporting-location'
import { ActionItemCategory } from './category'
import { ActionItemSubCategory } from './sub-category'
import { ReportingUnit } from './reporting-unit'
import { ReportingLine } from './common/asset-hierarchy/reporting-line'
import { ReportingSite } from './common/asset-hierarchy/reporting-site'
import { Application } from './application'
import { Action, Owner } from './action'
import { PageInfo } from '../hooks'
import { Attachment } from './action-detail'
import { Role } from './common/user-management/role'
import { Team } from './common/user-management/team'
import { DescribableEntity } from '@celanese/celanese-sdk'
import { SimpleEquipment } from './common/asset-hierarchy/equipment'

export interface SourceEvent extends ExternalEntity {
    eventType?: SourceEventType
    title?: string
    description?: string
    sourceInformation?: string
    actionsCount?: number
    actionsCompletedCount?: number
    owner?: Owner
    secondaryOwnerUsers?: Owner[]
    secondaryOwnerRoles?: Role[]
    secondaryOwnerTeams?: Team[]
    status: SourceEventStatus
    application: Application
    reportingUnit?: ReportingUnit
    reportingUnits?: ReportingUnit[]
    reportingLocation?: ReportingLocation
    impactedReportingLocations?: ReportingLocation[]
    actions?: Action[]
    category?: ActionItemCategory
    subCategory?: ActionItemSubCategory
    siteSpecificCategory?: ActionItemSubCategory
    assignmentDate?: Date
    dueDate: Date
    displayDueDate?: string
    reportingSite?: ReportingSite
    businessLine?: BusinessLine
    reportingLine?: ReportingLine
    attachments?: Attachment[]
    isPrivate?: boolean
    viewUsers?: Owner[]
    viewRoles?: Role[]
    viewTeams?: Team[]
    equipments: SimpleEquipment[]
    functionalLocations: DescribableEntity[]
    pageInfo?: PageInfo
}

export interface SourceEventResponse {
    hasNextPage: boolean
    data: SourceEvent[] | SourceEvent
    totalItems: number
    totalSourceEvent: number
    cursor?: string
    access?: boolean
}

export interface SourceEventType extends ExternalEntity {
    sourceEventTypeName: string
    sourceEventTypeCode: string
    application: Application
}

export interface SourceEventStatus extends ExternalEntity {
    name: string
    description: string
}

export interface SourceEventTitlesRequest {
    search: string
    activeUserEmail: string
    reportingSiteExternalId: string
}

export interface SourceEventTitlesResponse {
    items: string[]
}
