trigger: none

variables:
  - name: pythonVersion
    value: '3.11'
  - name: runLint
    value: 'True'
  - name: environment
    value: 'dev'
  - name: buildPath
    value: $(System.DefaultWorkingDirectory)/api
  - name: runTests # tests are run in "runLint" because of how they're executed (python -m pytest instead of pytest)
    value: 'False'
  - name: runIntegrationTests
    value: 'False'

pool:
  vmImage: ubuntu-latest

resources:
  repositories:
    - repository: templates
      type: git
      name: Templates
      ref: dev
      clean: true

stages:
  - stage: Build
    jobs:
      - job: Build
        displayName: Build
        steps:
          - template: build/template-build-python.yml@templates
