# Manifest file downloaded from fusion
externalId: tr-AIM-COR-ALL-RAW-ICAP-MAP-USER
name: AIM-COR-ALL-RAW-ICAP-MAP-USER
query: >-
  WITH icap_user AS (
      SELECT
          key,
          lower(`Email`) AS email,
          CASE
              WHEN lower(`Email`) LIKE '%@celanese.%' THEN lower(
                  concat(
                      substr(`Email`, 1, instr(`Email`, '@') - 1),
                      '@celanese.com'
                  )
              )
              ELSE NULL
          END AS email_transformed
      FROM `ICAP-COR`.`USR-tblUser`
  ),

  icap_mapping AS (
      SELECT
          lower(`email`) AS email,
          lower(`user`) AS user_email
      FROM `AIM-COR`.`ICAP-MAP-EmailSanitization`
  ),

  icap_user_final AS (
      SELECT
          icap_user.key,
          icap_user.email AS email,
          lower(icap_mapping.user_email) AS email_mapped,
          icap_user.email_transformed
      FROM icap_user
      LEFT JOIN icap_mapping ON icap_user.email = icap_mapping.email
  ),

  user_management_ranked AS (
      SELECT
          externalId,
          space,
          lower(externalId) AS external_id_lower,
    		ROW_NUMBER() OVER (
    			PARTITION BY lower(externalId) 
    			ORDER BY coalesce(active, false) DESC, `node.createdTime` DESC
    		) rn
      FROM cdf_data_models(
          'UMG-COR-ALL-DMD',
          'UserManagementDOM',
          '8_7_2',
          'User'
      )
  ),

  user_management AS (
    SELECT
    	externalId,
    	space,
    	external_id_lower
    FROM user_management_ranked
    WHERE rn = 1
  )

  SELECT
      main.key AS key,
      node_reference(main.space, main.externalId) AS user,
      node_reference(
          usr_azure_attribute.space,
          usr_azure_attribute.externalId
      ) AS user_azure_attribute
  FROM (
      SELECT
          icap_user_final.key,
          coalesce(match.externalId, match0.externalId, match1.externalId, match2.externalId) AS externalId,
          coalesce(match.space, match0.space, match1.space, match2.space) AS space
      FROM icap_user_final
    	LEFT JOIN user_management AS match ON match.external_id_lower = icap_user_final.email_mapped AND icap_user_final.email_mapped = '<EMAIL>'
      LEFT JOIN user_management AS match0 ON match0.external_id_lower = icap_user_final.email
      LEFT JOIN user_management AS match1 ON match1.external_id_lower = icap_user_final.email_mapped
      LEFT JOIN user_management AS match2 ON match2.external_id_lower = icap_user_final.email_transformed
  ) AS main

  JOIN cdf_data_models(
      'UMG-COR-ALL-DMD',
      'UserManagementDOM',
      '8_7_2',
      'UserAzureAttribute'
  ) usr_azure_attribute on node_reference(main.space, main.externalId) = usr_azure_attribute.user
destination:
  database: AIM-COR
  table: ICAP-MAP-User
  type: raw
ignoreNullFields: true
shared: true
action: upsert
schedule:
  interval: 0 * * * *
  isPaused: false
dataSetExternalId: AIM-COR-ALL-DAT
# Specify credentials separately like this:
# authentication:
#   read:
#     clientId: ${READ_CLIENT_ID}
#     clientSecret: ${READ_CLIENT_SECRET}
#     tokenUrl: ${READ_TOKEN_URL}
#     cdfProjectName: ${READ_CDF_PROJECT_NAME}
#     # Optional: If idP requires providing the scopes
#     scopes:
#       - ${READ_SCOPES}
#     # Optional: If idP requires providing the audience
#     audience: ${READ_CDF_AUDIENCE}
#   write:
#     clientId: ${WRITE_CLIENT_ID}
#     clientSecret: ${WRITE_CLIENT_SECRET}
#     tokenUrl: ${WRITE_TOKEN_URL}
#     cdfProjectName: ${WRITE_CDF_PROJECT_NAME}
#     # Optional: If idP requires providing the scopes
#     scopes:
#       - ${WRITE_SCOPES}
#     # Optional: If idP requires providing the audience
#     audience: ${WRITE_CDF_AUDIENCE}
# Or together like this:
# authentication:
#   clientId: ${CLIENT_ID}
#   clientSecret: ${CLIENT_SECRET}
#   tokenUrl: ${TOKEN_URL}
#   # Optional: If idP requires providing the scopes
#   cdfProjectName: ${CDF_PROJECT_NAME}
#   scopes:
#    - ${SCOPES}
#   # Optional: If idP requires providing the audience
#   audience: ${AUDIENCE}