import { useCallback, useState } from 'react'
import { useCognite } from './useCognite'
import { CogniteViewsResponseItem } from '../../models/common'

export type RetrieveDatamodelHookResult = [RetrieveDatamodelFunction, RetrieveDatamodelState]

export type RetrieveDatamodelFunction = () => Promise<CogniteViewsResponseItem[]>
export type RetrieveDatamodelState = {
    loading: boolean
    called: boolean
    error: any
}

export function useFdmRetrieveDatamodel(): RetrieveDatamodelHookResult {
    const { fdmClient: client } = useCognite()
    const [error, setError] = useState<any | undefined>()
    const [loading, setLoading] = useState<boolean>(false)
    const [called, setCalled] = useState<boolean>(false)

    const retrieveFunction = useCallback(() => {
        setLoading(true)
        setCalled(true)
        return client
            .getAllVersions()
            .catch(() => [])
            .finally(() => setLoading(false))
    }, [client])

    return [retrieveFunction, { error, loading, called }]
}
