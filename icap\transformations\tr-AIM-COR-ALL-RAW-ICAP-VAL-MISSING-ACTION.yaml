# Manifest file downloaded from fusion
externalId: tr-AIM-COR-ALL-RAW-ICAP-VAL-MISSING-ACTION
name: AIM-COR-ALL-RAW-ICAP-VAL-MISSING-ACTION
query: >-
  SELECT
    	icap_action.key,
    	icap_action.key AS `1_ActionItemID`,
    	icap_action.ActionDescription AS `2_ActionTitle`,
    	icap_action.Cause AS `3_ActionDescription`,
    	icap_action_category.ActionCategoryName AS `4_ActionCategory`,
    	icap_user_assignee.Email AS `5_PPREmail`,
    	icap_user_secondary_assignee.Email AS `6_SecondaryPPREmail`,
    	icap_action_status.ActionStatusName AS `7_ActionStatus`,
    	DATE(icap_action.ActionStartDate) AS `8_ActionStartDate`,
    	DATE(icap_action.ActionDueDate) AS `9_ActionDueDate`,
    	DATE(icap_action.ActionActualCompletionDate) AS `10_ActionCompletedDate`,
    	icap_action_priority.ActionPriority AS `11_Priority`,
    	icap_action.EvidenceRequired AS `12_EvidenceRequired`,
    	icap_site.SiteName AS `13_SiteName`,
    	icap_unit.UnitName AS `14_AreaName`,
    	icap_event.key AS `15_EventID`,
    	icap_moc.key AS `16_MOCID`,
    	icap_mooc.key AS `17_MOOCID`,
    	icap_event.EventTitle AS `18_EventTitle`,
    	icap_user_owner.Email AS `19_EventPrimaryOwnerEmail`,
    	icap_event_category.EventCategoryName AS `20_EventCategory`,
    	coalesce(icap_event.Private, icap_action.Private) AS `21_isPrivate`,
    	main.error_details AS `22_errorDetails`
  FROM
      (
          SELECT
              main.key,
              concat(
                if(isnull(category), 'Missing Category; ', ''),
                if(isnull(subCategory), 'Missing Sub Category 1; ', ''),
                if(isnull(dueDate), 'Missing Due Date; ', ''),
                if(isnull(assignmentDate), 'Missing Assignment Date; ', ''),
                if(isnull(assignedTo), 'Missing Assignee; ', ''),
                if(isnull(owner), 'Missing Owner; ', ''),
                if(isnull(createdBy), 'Missing Created By; ', ''),
                if(isnull(currentStatus), 'Missing Status; ', ''),
                if(isnull(reportingUnit), 'Missing Reporting Unit - Invalid Area Mapping; ', ''),
                if(
                    isnotnull(reportingSite) 
                    AND isnotnull(reportingUnit)
                    AND NOT startswith(
                      reportingUnit.externalId,
                      replace(reportingSite.externalId, 'STS-', 'UNT-')
                    ), 
                    'Reporting Unit from another Site; ', 
                    ''
                ),
                if(
                    isnotnull(reportingSite) 
                    AND isnotnull(reportingLocation)
                    AND NOT startswith(
                      reportingLocation.externalId,
                      replace(reportingSite.externalId, 'STS-', 'LOC-')
                    ), 
                    'Reporting Location from another Site; ', 
                    ''
                )
              ) AS error_details
          FROM
              `AIM-COR`.`ICAP-STG-Action` as main
      ) AS main
      INNER JOIN `ICAP-COR`.`AXN-tblActionItem` icap_action ON main.key = icap_action.key
      LEFT JOIN `ICAP-COR`.`AXN-tblActionCategory` icap_action_category
      	ON icap_action_category.key = cast(icap_action.ActionCategoryID AS STRING)
      LEFT JOIN `ICAP-COR`.`USR-tblUser` icap_user_assignee
      	ON icap_user_assignee.key = cast(icap_action.PPRID AS STRING)
      LEFT JOIN `ICAP-COR`.`USR-tblUser` icap_user_secondary_assignee
      	ON icap_user_secondary_assignee.key = cast(icap_action.SecondaryPPRID AS STRING)
      LEFT JOIN `ICAP-COR`.`AXN-tblActionStatus` icap_action_status
      	ON icap_action_status.key = cast(icap_action.ActionStatusID AS STRING)
      LEFT JOIN `ICAP-COR`.`AXN-tblActionPriority` icap_action_priority
      	ON icap_action_priority.key = cast(icap_action.ActionPriorityID AS STRING)
      LEFT JOIN `ICAP-COR`.`EVNT-tblEvent` icap_event
      	ON icap_event.key = cast(icap_action.EventID AS STRING)
    	LEFT JOIN `ICAP-COR`.`STS-tblSite` icap_site
    		ON icap_site.key = cast(icap_event.SiteID AS STRING)
      LEFT JOIN `ICAP-COR`.`UNT-tblUnit` icap_unit
        	ON icap_unit.key = cast(icap_event.UnitID AS STRING)
      LEFT JOIN `ICAP-COR`.`MOC-tblMOC` icap_moc
      	ON cast(icap_moc.EventID AS STRING) = icap_event.key
        	AND icap_event.EventCategoryID = 15
      LEFT JOIN `ICAP-COR`.`MOOC-tblMOOC` icap_mooc
      	ON cast(icap_mooc.EventID AS STRING) = icap_event.key
      	AND icap_event.EventCategoryID = 21
      LEFT JOIN `ICAP-COR`.`USR-tblUser` icap_user_owner
      	ON icap_user_owner.key = cast(icap_event.EventAddedByOwner AS STRING)
      LEFT JOIN `ICAP-COR`.`EVNT-tblEventCategory` icap_event_category
      	ON icap_event_category.key = cast(icap_event.EventCategoryID AS STRING)
  WHERE
      LENGTH(main.error_details) > 0
      OR coalesce(icap_event.Private, icap_action.Private)
destination:
  database: AIM-COR
  table: ICAP-VAL-MissingAction
  type: raw
ignoreNullFields: true
shared: true
action: upsert
dataSetExternalId: AIM-COR-ALL-DAT
# Specify credentials separately like this:
# authentication:
#   read:
#     clientId: ${READ_CLIENT_ID}
#     clientSecret: ${READ_CLIENT_SECRET}
#     tokenUrl: ${READ_TOKEN_URL}
#     cdfProjectName: ${READ_CDF_PROJECT_NAME}
#     # Optional: If idP requires providing the scopes
#     scopes:
#       - ${READ_SCOPES}
#     # Optional: If idP requires providing the audience
#     audience: ${READ_CDF_AUDIENCE}
#   write:
#     clientId: ${WRITE_CLIENT_ID}
#     clientSecret: ${WRITE_CLIENT_SECRET}
#     tokenUrl: ${WRITE_TOKEN_URL}
#     cdfProjectName: ${WRITE_CDF_PROJECT_NAME}
#     # Optional: If idP requires providing the scopes
#     scopes:
#       - ${WRITE_SCOPES}
#     # Optional: If idP requires providing the audience
#     audience: ${WRITE_CDF_AUDIENCE}
# Or together like this:
# authentication:
#   clientId: ${CLIENT_ID}
#   clientSecret: ${CLIENT_SECRET}
#   tokenUrl: ${TOKEN_URL}
#   # Optional: If idP requires providing the scopes
#   cdfProjectName: ${CDF_PROJECT_NAME}
#   scopes:
#    - ${SCOPES}
#   # Optional: If idP requires providing the audience
#   audience: ${AUDIENCE}