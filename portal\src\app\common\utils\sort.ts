import {
    GridSortDirection,
    GridComparatorFn,
    gridStringOrNumberComparator,
    gridDateComparator,
} from '@mui/x-data-grid-pro'

export const getNullsLastStringOrNumberComparator = (sortDirection: GridSortDirection): GridComparatorFn => {
    const modifier = sortDirection === 'desc' ? -1 : 1

    return (value1, value2, cellParams1, cellParams2) => {
        if (!value1 && !value2) return 0
        if (!value1) return 1
        if (!value2) return -1
        return modifier * gridStringOrNumberComparator(value1, value2, cellParams1, cellParams2)
    }
}

export const getNullsLastDateComparator = (direction: GridSortDirection): GridComparatorFn => {
    const modifier = direction === 'desc' ? -1 : 1

    return (value1, value2, params1, params2) => {
        const date1 = value1 ? new Date(value1) : null
        const date2 = value2 ? new Date(value2) : null

        const isValid = (d: Date | null) => d instanceof Date && !isNaN(d.getTime())

        if (!isValid(date1) && !isValid(date2)) return 0
        if (!isValid(date1)) return 1
        if (!isValid(date2)) return -1

        return modifier * gridDateComparator(date1, date2, params1, params2)
    }
}
