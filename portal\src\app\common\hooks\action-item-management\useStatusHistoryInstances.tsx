import { useEffect, useState } from 'react'
import { StatusHistoryInstance } from '../../models/action-detail'
import { useGetAllResultsFunction } from '../general-functions/useGetAllResultFunction'
import { Filter } from '../../models/base-hook-request'

const historyInstanceQuery = `
    externalId
    space
    action {
        externalId
        space
    }
    status {
        externalId
        space
    }
    friendlyName
    changedAt
    statusSubject {
        user {
            lastName
            firstName
            email
        }
    }
`

export interface StatusHistoryQuery {
    externalId: string[] | null
    statusId: string | null
}

export const useStatusHistoryInstances = (params: StatusHistoryQuery) => {
    const [resultData, setResultData] = useState<{ data: StatusHistoryInstance[]; loading: boolean }>({
        data: [],
        loading: true,
    })
    const { getAllResults: getAllData } = useGetAllResultsFunction<StatusHistoryInstance>(
        historyInstanceQuery,
        'listStatusHistoryInstance',
        'changedAt',
        'DESC'
    )

    const filter: Filter<StatusHistoryInstance> = {
        and: [
            { action: { externalId: { in: params.externalId ? params.externalId : ['NULL_PARAM'] } } },
            ...(params.statusId ? [{ status: { externalId: { eq: params.statusId } } }] : []),
        ],
    }

    useEffect(() => {
        getAllData(filter).then((res) => {
            if (res.length == 0) {
                setResultData({ data: [], loading: false })
            } else {
                setResultData({ data: res, loading: true })
            }
        })
    }, [params.externalId])

    return {
        loading: resultData.loading,
        historyInstance: resultData.data,
    }
}
