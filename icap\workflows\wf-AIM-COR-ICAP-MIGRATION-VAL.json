{"externalId": "wf-AIM-COR-ICAP-MIGRATION-VAL", "version": "1_0_0", "description": "Generate iCAP migration validation report", "schedules": [{"externalId": "wfs-AIM-COR-ICAP-MIGRATION-VAL-01", "cron": "0 0 * * *", "data": null}], "tasks": [{"externalId": "task-transformation-653", "type": "transformation", "name": "Fill actions validation in raw", "parameters": {"transformation": {"externalId": "tr-AIM-COR-ALL-RAW-ICAP-VAL-ACTION", "concurrencyPolicy": "fail"}}, "timeout": 10000, "onFailure": "abortWorkflow", "dependsOn": []}, {"externalId": "task-transformation-845", "type": "transformation", "name": "Fill events validation in raw", "parameters": {"transformation": {"externalId": "tr-AIM-COR-ALL-RAW-ICAP-VAL-EVENT", "concurrencyPolicy": "fail"}}, "timeout": 10000, "onFailure": "abortWorkflow", "dependsOn": []}, {"externalId": "task-transformation-369", "type": "transformation", "name": "Fill missing actions in raw", "parameters": {"transformation": {"externalId": "tr-AIM-COR-ALL-RAW-ICAP-VAL-MISSING-ACTION", "concurrencyPolicy": "fail"}}, "timeout": 10000, "onFailure": "abortWorkflow", "dependsOn": []}, {"externalId": "task-transformation-654", "type": "transformation", "name": "Fill missing events in raw", "parameters": {"transformation": {"externalId": "tr-AIM-COR-ALL-RAW-ICAP-VAL-MISSING-EVENT", "concurrencyPolicy": "fail"}}, "timeout": 10000, "onFailure": "abortWorkflow", "dependsOn": []}, {"externalId": "task-transformation-275", "type": "transformation", "name": "Fill missing assets in raw", "parameters": {"transformation": {"externalId": "tr-AIM-COR-ALL-RAW-ICAP-VAL-MISSING-ASSET", "concurrencyPolicy": "fail"}}, "timeout": 10000, "onFailure": "abortWorkflow", "dependsOn": []}, {"externalId": "task-transformation-404", "type": "transformation", "name": "Fill missing users in raw", "parameters": {"transformation": {"externalId": "tr-AIM-COR-ALL-RAW-ICAP-VAL-MISSING-USER", "concurrencyPolicy": "fail"}}, "timeout": 10000, "onFailure": "abortWorkflow", "dependsOn": []}, {"externalId": "task-function-614", "type": "function", "name": "Generate report and clear raw table", "parameters": {"function": {"externalId": "FUNC-AIM-COR-ALL-ICAP-VAL", "data": {}}}, "timeout": 10000, "onFailure": "abortWorkflow", "dependsOn": [{"externalId": "task-transformation-404"}, {"externalId": "task-transformation-275"}, {"externalId": "task-transformation-654"}, {"externalId": "task-transformation-369"}, {"externalId": "task-transformation-845"}, {"externalId": "task-transformation-653"}]}, {"externalId": "task-transformation-518", "type": "transformation", "name": "Update action migration progress in raw", "parameters": {"transformation": {"externalId": "tr-AIM-COR-ALL-RAW-ICAP-VAL-ACTION-MIGRATION-PROGRESS", "concurrencyPolicy": "fail"}}, "timeout": 10000, "onFailure": "skipTask", "dependsOn": []}, {"externalId": "task-transformation-606", "type": "transformation", "name": "Update event migration progress in raw", "parameters": {"transformation": {"externalId": "tr-AIM-COR-ALL-RAW-ICAP-VAL-EVENT-MIGRATION-PROGRESS", "concurrencyPolicy": "fail"}}, "timeout": 10000, "onFailure": "skipTask", "dependsOn": []}]}