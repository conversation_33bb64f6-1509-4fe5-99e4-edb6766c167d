import { ExternalEntity } from '..'
import { ReportingUnit } from './reporting-unit'
import { Location } from './functional-location'
import { ReportingLocation } from './reporting-location'

export interface ReportingSite extends ExternalEntity {
    name?: string
    description?: string
    functionalLocations?: Location[]
    reportingLocations?: ReportingLocation[]
    reportingUnits?: ReportingUnit[]
    aliases?: string[]
    city?: string
    latitude?: number
    longitude?: number
    physicalAddress?: string
    postalCode?: string
    siteCode?: string
    stateOrProvince?: string
}
