GET_REPORTING_LOCATION_BY_FILTER = """
query GetReportingLocation($filter: _ListReportingLocationFilter, $sort: _ReportingLocationSort!) {
    listReportingLocation(
        filter: $filter, first: 1000, sort: [$sort]) {
        items {
            externalId
            space
            description
            reportingUnit {
                externalId
                space
            }
        }
    }
}
"""


GET_SEARCH_REPORTING_LOCATION_BY_FILTER = """
query SearchReportingLocation($query: String!, $fields: [_SearchReportingLocationFields!], $filter: _SearchReportingLocationFilter, $sort: _ReportingLocationSort!) {
  searchReportingLocation(
    query: $query
    fields: $fields
    filter: $filter
    first: 1000
    sort: [$sort]
  ) {
    items {
      externalId
      space
      description
      reportingUnit {
        externalId
        space
      }
      reportingSites {
        items {
          externalId
          space
          siteCode
        }
      }
    }
  }
}
"""
