import os
import sys
from typing import Optional
from uuid import uuid4

script_dir = os.path.dirname(os.path.abspath(__file__))
project_dir = os.path.dirname(script_dir)
sys.path.append(project_dir)
from infra.action_item_client_factory import ActionItemClientFactory
from infra.cognite_client_factory import CogniteClientFactory
from infra.graphql_client_factory import GraphqlClientFactory
from models.settings import Settings
from services.cognite_service import CogniteService
from services.graphql_service import GraphqlService
from services.logging_service import LoggingService
from services.notification_service import NotificationService
from services.recurrent_action_items_creator import RecurrentActionItemsCreator


class RecurrentActionItemsCreatorFactory:
    @staticmethod
    def create(
        settings: Settings = Settings.from_env(),
        call_id: str = str(uuid4()),
        override_token: Optional[str] = None,
    ) -> RecurrentActionItemsCreator:
        log = LoggingService(call_id)
        cognite_client = CogniteClientFactory.create(settings, override_token)
        graphl_client = GraphqlClientFactory.create(cognite_client, settings)
        notification_service = NotificationService(cognite_client, settings)

        action_item_client = ActionItemClientFactory.retriever(
            settings, call_id, override_token
        )

        return RecurrentActionItemsCreator(
            CogniteService(cognite_client, settings, log),
            GraphqlService(graphl_client, log),
            settings,
            log,
            notification_service,
            action_item_client,
        )


if __name__ == "__main__":
    import asyncio
    import logging

    logging.basicConfig(
        filename="local_recurring_processor_log.log", level=logging.INFO
    )

    asyncio.run(RecurrentActionItemsCreatorFactory.create().execute())
