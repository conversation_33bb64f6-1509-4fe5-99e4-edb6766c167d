import { ExternalEntity } from './common'

export interface ExternalSourceRequest {
    sourceType: string
    sourceId: string
}

export interface QualityReliabilityEvent extends ExternalEntity {
    eventDate?: Date
    reportingUnit?: string
    reportTitle?: string
    reportDate?: Date
    problemCategory?: string
    initiator?: string
    proceduresInvolved?: string
    immediateActionTaken?: string
    batchNumber?: string
    equipment?: string
    eventId?: string
    problemDescription?: string
}

export interface EventInvestigationResponse extends ExternalEntity {
    reportingUnit?: string
    businessLine?: string
    functionalLocation?: string
    equipment?: string
}

export interface WorkProcessInvestigation extends ExternalEntity {
    reportingUnit?: string
    functionalLocation?: string
    equipment?: string
    problemDescription?: string
}

export interface QualityNotificationResponse extends ExternalEntity {
    notificationNumber?: string
    notificationDescription?: string
    notificationDate?: Date
    batchNumber?: string
    defectDescription?: string
    severity?: string
    material?: string
    returnedQuantity?: number
    subjectCode?: string
    systemStatus?: string
}

export interface EventAnalysis extends ExternalEntity {
    externalEventId?: string
    eventTitle?: string
    eventType?: string
    investigationStartDate?: Date
    dueDate?: Date
    leadInvestigator?: string
    investigationTeam?: string[]
    rcaType?: string
    rootCauses?: string[]
    approvers?: string[]
    reportingLocation?: string
}

export interface ExternalSourceDetails {
    general?: EventAnalysis
    specific?: QualityReliabilityEvent &
        EventInvestigationResponse &
        WorkProcessInvestigation &
        QualityNotificationResponse
}
