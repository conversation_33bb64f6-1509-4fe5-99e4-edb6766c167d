from pydantic import BaseModel, computed_field

from clients.core.constants import DataSpaceEnum, EmployeeStatusEnum
from clients.core.models import BaseCamelCaseModel, GlobalModel


def _create_node_as_dict(external_id: str, space: str) -> dict[str, str]:
    """Create a dictionary representing a node with externalId and space."""
    return {"externalId": external_id, "space": space}


class GetUserComplementByUnitsRequest(BaseCamelCaseModel):
    """Request model to get user complements filtered by reporting units."""

    reporting_units_ids: list[str]
    users_external_ids: list[str] | None = None

    @computed_field
    @property
    def reporting_units(self) -> list[dict[str, str]]:
        """
        Generate a list of reporting unit nodes.

        Returns:
            list[dict[str, str]]: List of node dicts for each reporting unit.

        """
        if not self.reporting_units_ids:
            return []
        return [
            _create_node_as_dict(external_id, DataSpaceEnum.REF_DATA_SPACE)
            for external_id in self.reporting_units_ids
        ]

    @computed_field
    @property
    def employee_status_node(self) -> dict[str, str]:
        """
        Generate a node for the inactive employee status.

        Returns:
            dict[str, str]: Node dict for the inactive employee status.

        """
        return _create_node_as_dict(
            EmployeeStatusEnum.INACTIVE,
            DataSpaceEnum.UMG_DATA_SPACE,
        )


class GetUserComplementByLocationsRequest(BaseCamelCaseModel):
    """Request model to get user complements filtered by reporting locations."""

    reporting_locations_ids: list[str]

    @computed_field
    @property
    def reporting_locations(self) -> list[dict[str, str]]:
        """
        Generate a list of reporting location nodes.

        Returns:
            list[dict[str, str]]: List of node dicts for each reporting location.

        """
        if not self.reporting_locations_ids:
            return []
        return [
            _create_node_as_dict(external_id, DataSpaceEnum.REF_DATA_SPACE)
            for external_id in self.reporting_locations_ids
        ]

    @computed_field
    @property
    def employee_status_node(self) -> dict[str, str]:
        """
        Generate a node for the inactive employee status.

        Returns:
            dict[str, str]: Node dict for the inactive employee status.

        """
        return _create_node_as_dict(
            EmployeeStatusEnum.INACTIVE,
            DataSpaceEnum.UMG_DATA_SPACE,
        )


class GetUserRolesAndTeamsRequest(GlobalModel):
    """Request model for retrieving user roles and teams."""

    email: str


class GetUsersRequest(BaseModel):
    """Request model for retrieving users based on site external ID."""

    reporting_site_external_id: str
