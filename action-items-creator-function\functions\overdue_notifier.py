import logging
import os
import sys

import azure.functions as func

bp = func.Blueprint()

script_dir = os.path.dirname(os.path.abspath(__file__))
project_dir = os.path.dirname(script_dir)
sys.path.append(project_dir)

from infra.action_item_client_factory import ActionItemClientFactory
from services.overdue_notifier import OverdueNotifierService

TIMER_PAST_DUE = "Timer is past due."


@bp.function_name(name="OverdueNDaysNotifier")
@bp.schedule(arg_name="timer", schedule="%OVERDUE_N_DAYS_NOTIFIER_CRON%")
async def overdue_n_days_notifier(timer: func.TimerRequest):
    try:
        if timer.past_due:
            logging.info(TIMER_PAST_DUE)

        service = OverdueNotifierService(ActionItemClientFactory.retriever())
        logging.info(await service.notify_users_of_upcoming_due_actions())
    except Exception as e:
        logging.error(f"Error in OverdueNDaysNotifier: {e}", exc_info=True)


@bp.function_name(name="OverdueAlertNotifier")
@bp.schedule(arg_name="timer", schedule="%OVERDUE_ALERT_NOTIFIER_CRON%")
async def immediate_overdue_notifier(timer: func.TimerRequest):
    try:
        if timer.past_due:
            logging.info(TIMER_PAST_DUE)

        service = OverdueNotifierService(ActionItemClientFactory.retriever())
        logging.info(await service.notify_users_of_overdue_actions())
    except Exception as e:
        logging.error(f"Error in OverdueAlertNotifier: {e}", exc_info=True)
