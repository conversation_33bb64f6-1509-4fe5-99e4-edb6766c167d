import { ExternalEntity, Location } from '..'
import { BusinessLine } from './business-line'
import { BusinessLineLegacy } from './business-line-legacy'
import { ReportingSite } from './reporting-site'

export interface ReportingUnit extends ExternalEntity {
    name: string
    description: string
    newBusinessLine?: BusinessLine
    legacyBusinessLine?: BusinessLineLegacy
    refersTo: Location[]
    physicalAreas?: any[]
    aliases?: string[]
    reportingSites: ReportingSite[]
    isActive?: boolean
}

export interface ReportingUnitRequest {
    search?: string
    reportingSiteExternalIds: string[]
}
