from clients.action_item_client import ActionItemClient
from clients.functional_location.models import FunctionalLocationResult
from clients.functional_location.requests import GetFunctionalLocationRequest


class FunctionalLocationService:
    """Service responsible for retrieving and managing functional location data."""

    def __init__(
        self,
        action_item_client: ActionItemClient,
    ) -> None:
        """
        Initialize the FunctionalLocationService with the provided ActionItemClient.

        Args:
            action_item_client (ActionItemClient): Instance providing access to the
                FunctionalLocationClient and associated logging utilities.

        """
        self._action_item_client = action_item_client
        self._log = action_item_client.functional_location.log

    def get_functional_locations(
        self,
        request: GetFunctionalLocationRequest,
    ) -> list[FunctionalLocationResult]:
        """
        Retrieve a list of functional locations based on the specified request.

        Args:
            request (GetFunctionalLocationRequest): Object containing filtering criteria,
                such as a list of reporting unit external IDs.

        Returns:
            list[FunctionalLocationResult]: List of functional location results
                that match the provided request parameters.

        Raises:
            Exception: If an error occurs while retrieving the data, it is logged
                and re-raised to the caller.

        """
        try:
            return (
                self._action_item_client.functional_location.get_functional_locations(
                    request,
                )
            )
        except Exception as e:
            error_msg = f"Failed to retrieve functional locations: {e!s}"
            self._log.error(error_msg)
            raise
