import os
import sys
from typing import Optional

from clients.action_item_client import ActionItemClient
from clients.user_complement.models import UserByEmailResult
from clients.user_complement.requests import GetUserRolesAndTeamsRequest

script_dir = os.path.dirname(os.path.abspath(__file__))
project_dir = os.path.dirname(script_dir)
sys.path.append(project_dir)


class UserNotFoundError(Exception):
    """Exception raised when the user is not found."""

    def __init__(self, email):
        """Initialize the UserNotFoundError exception with the provided email."""
        super().__init__(f"User with email '{email}' not found.")
        self.email = email


class UserComplementsService:
    """
    Service responsible for retrieving and managing user information, specifically roles and teams, based on user email and site ID. Provides methods to access and log user data for specified requests.

    Attributes:
        _action_item_client (ActionItemClient): Client to manage and access action items and user data.
        _log (Logger): Used to log operations and errors.

    """

    def __init__(
        self,
        action_item_client: ActionItemClient,
    ) -> None:
        """
        Initialize the user complements service with the provided action item client.

        Args:
            action_item_client (ActionItemClient): An instance of ActionItemClient to access the AIM data model.

        """
        self._action_item_client = action_item_client
        self._log = action_item_client.actions._logging

    async def get_user_by_email(
        self,
        request: GetUserRolesAndTeamsRequest,
    ) -> Optional[UserByEmailResult]:
        """
        Retrieve user roles and teams by email and site, returning user details if found.

        Args:
            request (GetUserRolesAndTeamsRequest): The request containing the email and site ID to retrieve user data.

        Returns:
            Optional[UserByEmailResult]: A `UserByEmailResult` object with user details (external ID, space, active roles, and teams)
                                        if the user is found, otherwise `None`.

        Raises:
            UserNotFoundError: If no user data is found for the given email.
            Exception: For other errors encountered during data retrieval.

        """
        self._log.info(
            f"Starting to get user with email: {request.email} and site: {request.reporting_site_external_ids}",
        )

        try:
            user_complement = (
                self._action_item_client.user_complement.get_user_roles_and_teams(
                    request,
                )
            )

            if len(user_complement.data) < 1:
                raise UserNotFoundError(request.email)

        except UserNotFoundError as e:
            self._log.error(f"User not found. Error: {e}")
            return None

        except Exception as err:
            self._log.error(f"Could not get user by email. Error: {err}")
            return None

        user = user_complement.data[0]
        return UserByEmailResult(
            external_id=user.user_azure_attribute.external_id,
            space=user.user_azure_attribute.space,
            active_user_roles_ids=(
                [role_site.role.external_id for role_site in user.user_role_site]
                if user.user_role_site
                else None
            ),
            active_user_teams_ids=(
                [team.external_id for team in user.user_azure_attribute.user.teams]
                if user.user_azure_attribute.user.teams
                else None
            ),
            user_external_id=(
                user.user_azure_attribute.user.external_id
                if user.user_azure_attribute and user.user_azure_attribute.user
                else None
            ),
        )
