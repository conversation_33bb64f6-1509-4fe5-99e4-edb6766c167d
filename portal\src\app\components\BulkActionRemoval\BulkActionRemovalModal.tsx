import React, { useEffect, useState } from 'react'
import { Box, Typography, useMediaQuery, useTheme } from '@mui/material'
import { ModalWrapper } from '../ModalComponent/Modal/ModalWrapper'
import { ClnButton, ClnCircularProgress } from '@celanese/ui-lib'
import { translate } from '@/app/common/utils/generate-translate'
import { FilterInfoProps } from '../ActionTable/HomeFilter'
import { ActionTable, allActionColumns } from '../ActionTable'
import { AzureFunctionClient } from '@/app/common/clients/azure-function-client'
import { useSnackbar } from '@/app/common/contexts/SnackbarContext'
import { UserRolesPermission } from '@celanese/celanese-sdk'
import { getLocalUserSite } from '@celanese/celanese-ui'
import { PageType } from '@/app/common/enums/PageTypeEnum'

interface BulkActionRemovalModalProps {
    onClose: (showConfirmationModal: boolean) => void
    isCancel: boolean
    defaultFilterInfo: FilterInfoProps
    activeUser: UserRolesPermission
}

export function BulkActionRemovalModal({
    onClose,
    isCancel,
    defaultFilterInfo,
    activeUser,
}: BulkActionRemovalModalProps) {
    const client = new AzureFunctionClient()
    const { siteId } = getLocalUserSite() || {}

    const theme = useTheme()
    const isMobile = useMediaQuery(theme.breakpoints.down('sm'))
    const { showSnackbar } = useSnackbar()

    const [filterInfo, setFilterInfo] = useState<FilterInfoProps>({
        ...defaultFilterInfo,
        pageSize: 10,
    })

    const [page, setPage] = useState<number>(0)
    const [memoryPage, setMemoryPage] = useState<number>(0)
    const [isLoading, setIsLoading] = useState<boolean>(false)

    useEffect(() => {
        const visibleColumns = ['externalId', 'sortTitle', 'sortCurrentStatus']

        const defaultVisibilityModel = allActionColumns.reduce(
            (acc, column) => {
                acc[column] = visibleColumns.includes(column)
                return acc
            },
            {} as Record<string, boolean>
        )

        const visibilityKey = 'visibilityModelChange-bulk-delete-actions-modal-filterInfo-tab'
        localStorage.setItem(visibilityKey, JSON.stringify(defaultVisibilityModel))
    }, [])

    const handleAlert = (message: string, error: boolean = false) => {
        showSnackbar(message, error ? 'error' : 'success', 'action-details')
    }

    const handleRemoveItem = async () => {
        setIsLoading(true)
        try {
            const requestPayload = {
                externalIds: filterInfo.externalIds,
                activeUserEmail: activeUser.email,
                reportingSiteExternalId: siteId ?? '-',
            }

            isCancel
                ? await client?.cancelActionRequest(requestPayload)
                : await client?.deleteActionRequest(requestPayload)

            onClose(true)
        } catch (ex) {
            handleAlert(translate('alerts.unexpectedErrorOcurred'), true)
        } finally {
            setIsLoading(false)
        }
    }

    return (
        <ModalWrapper
            title={translate(
                isCancel ? 'requestModal.cancelActionsInBulkQuestion' : 'requestModal.deleteActionsInBulkQuestion'
            ).replace('{}', filterInfo.externalIds?.length.toString())}
            openModal
            closeModal={() => onClose(false)}
            sxProps={{ backgroundColor: 'rgba(0, 0, 0, 0.5)' }}
            sxPropsTitle={{
                color: 'text.primary',
                fontSize: '22px',
                fontWeight: 400,
                marginRight: 'auto',
                wordBreak: 'break-word',
                whiteSpace: 'normal',
                maxWidth: 'calc(100% - 60px)',
            }}
            content={
                <Box
                    sx={{
                        display: 'flex',
                        flexDirection: 'column',
                        padding: '0 24px 24px 24px',
                        width: isMobile ? 'auto' : '50vw',
                        minWidth: isMobile ? 'auto' : '600px',
                        overflowY: 'auto',
                        height: isMobile ? 'auto' : '50vh',
                        minHeight: '400px',
                    }}
                >
                    {isLoading ? (
                        <Box
                            sx={{
                                display: 'flex',
                                justifyContent: 'center',
                                alignItems: 'center',
                                flex: 1,
                            }}
                        >
                            <ClnCircularProgress size={40} value={0} />
                        </Box>
                    ) : (
                        <>
                            <Typography
                                variant="subtitle1"
                                sx={{
                                    wordBreak: 'break-word',
                                    whiteSpace: 'normal',
                                    fontSize: '16px',
                                    fontWeight: 400,
                                    color: 'text.primary',
                                    mb: 2,
                                }}
                            >
                                {translate(
                                    isCancel
                                        ? 'requestModal.cancelActionsInBulkSubtitle'
                                        : 'requestModal.deleteActionsInBulkSubtitle'
                                ).replace('{}', filterInfo.externalIds?.length?.toString())}
                            </Typography>
                            <Typography
                                variant="subtitle1"
                                sx={{
                                    wordBreak: 'break-word',
                                    whiteSpace: 'normal',
                                    fontSize: '16px',
                                    fontWeight: 400,
                                    color: 'text.primary',
                                }}
                            >
                                {`${translate('requestModal.selectedActions')}:`}
                            </Typography>
                            <Box sx={{ flex: 1, overflow: 'auto', maxHeight: '400px' }}>
                                {filterInfo && (
                                    <ActionTable
                                        id="bulk-delete-actions-modal"
                                        filterInfo={filterInfo}
                                        setFilterInfo={setFilterInfo}
                                        currentPage={page}
                                        setCurrentPage={setPage}
                                        memoryPage={memoryPage}
                                        setMemoryPage={setMemoryPage}
                                        isToDoTab
                                        client={client}
                                        siteIds={[siteId!]}
                                        activeUser={activeUser}
                                        showBulkUpload={false}
                                        showLink={false}
                                        onActionDetailsClick={() => {}}
                                        pageType={PageType.BulkDeleteActions}
                                        showOptionsBar={false}
                                    />
                                )}
                            </Box>
                            <Box
                                sx={{
                                    display: 'flex',
                                    flexDirection: isMobile ? 'column' : 'row',
                                    gap: 1,
                                    justifyContent: isMobile ? 'stretch' : 'flex-end',
                                    alignItems: 'center',
                                    mt: '20px',
                                    width: '100%',
                                }}
                            >
                                <ClnButton
                                    variant="text"
                                    label={translate('requestModal.cancel')}
                                    onClick={() => onClose(false)}
                                    color="primary"
                                    sx={{
                                        width: isMobile ? '100%' : 'auto',
                                        order: isMobile ? 2 : 1,
                                    }}
                                />
                                <ClnButton
                                    variant="contained"
                                    label={translate(
                                        isCancel
                                            ? 'requestModal.cancelSelectedActions'
                                            : 'requestModal.deleteSelectedActions'
                                    )}
                                    color="error"
                                    onClick={handleRemoveItem}
                                    sx={{ width: isMobile ? '100%' : 'auto', order: isMobile ? 1 : 2 }}
                                />
                            </Box>
                        </>
                    )}
                </Box>
            }
        />
    )
}
