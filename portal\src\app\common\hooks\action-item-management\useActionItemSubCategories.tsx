import { gql } from '@apollo/client'
import { useEffect, useState } from 'react'
import { useGraphqlQuery } from '../cognite/useGraphqlQuery'
import { ActionItemSubCategory } from '../../models/sub-category'
import { EntityType, GetSpace } from '../../utils/space-util'

const buildSubCategoryQuery = (): string => {
    const filters: string[] = []

    filters.push(`{ space: { eq: "${GetSpace(EntityType.Static)}" } }`)

    const queryFilter = `{ and: [ ${filters.join(', ')} ] }`

    return `
        query GetActionItemSubCategory {
            listActionItemSubCategory(
                filter: ${queryFilter}
                , first: 1000
                , sort: { description: ASC }
            ) {
                items {
                    externalId
                    space
                    name
                    description
                }
            }
        }
    `
}

export const useActionItemSubCategories = () => {
    const query = buildSubCategoryQuery()
    const { data: fdmData } = useGraphqlQuery<ActionItemSubCategory>(gql(query), 'listActionItemSubCategory', {})

    const [resultData, setResultData] = useState<{ data: ActionItemSubCategory[]; loading: boolean }>({
        data: [],
        loading: true,
    })

    useEffect(() => {
        if (fdmData.length == 0) {
            setResultData({ data: [], loading: false })
        } else {
            setResultData({ data: fdmData, loading: false })
        }
    }, [fdmData])

    return {
        loading: resultData.loading,
        subCategories: resultData.data,
    }
}
