import { gql } from '@apollo/client'
import { useEffect, useState } from 'react'
import { useGraphqlQuery } from '../cognite/useGraphqlQuery'
import { ReportingLine } from '../../models/common/asset-hierarchy/reporting-line'
import { EntityType, getCodeFromReportingSiteExternalId, GetSpace } from '../../utils/space-util'

export interface LineQueryRequest {
    siteIds: string[]
    unitId?: string
}

const buildLineQuery = ({ siteIds, unitId }: LineQueryRequest): string => {
    const siteCodes = siteIds.map(getCodeFromReportingSiteExternalId)

    const filters: string[] = []

    if (unitId) {
        filters.push(`{ reportingUnit: { externalId: { eq: ${JSON.stringify(unitId)} } } }`)
    }

    filters.push(`{ space: { eq: "${GetSpace(EntityType.REF)}" } }`)

    const orClauses = siteCodes.map((code) => `{ externalId: { prefix: "RLN-${code}" } }`).join(', ')
    filters.push(`{ or: [ ${orClauses} ] }`)

    filters.push(`{ isActive: { eq: true } }`)

    const queryFilter = `{ and: [ ${filters.join(', ')} ] }`

    return `
        query GetReportingLine {
            listReportingLine(
                filter: ${queryFilter}
                , first: 1000
            ) {
                items {
                    externalId
                    name
                    description
                    aliases
                    createdTime
                    space
                    reportingUnit {
                        externalId
                        name
                        description
                        space
                    }
                }
            }
        }
    `
}

export const useReportingLines = (request: LineQueryRequest) => {
    const query = buildLineQuery(request)
    const { data: fdmData } = useGraphqlQuery<ReportingLine>(gql(query), 'listReportingLine', {})

    const [resultData, setResultData] = useState<{ data: ReportingLine[]; loading: boolean }>({
        data: [],
        loading: true,
    })

    useEffect(() => {
        if (fdmData.length == 0) {
            setResultData({ data: [], loading: false })
        } else {
            setResultData({ data: fdmData, loading: false })
        }
    }, [fdmData])

    return {
        loading: resultData.loading,
        lines: resultData.data,
    }
}
