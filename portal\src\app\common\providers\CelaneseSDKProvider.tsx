import dynamic from 'next/dynamic'
import { PropsWithChildren } from 'react'
import { getIdTokenFromMsal } from '../utils'
import { createPublicClientApplication } from '../factories/msal-factory'
import { environment } from '../configurations/environment'
import { useColorMode } from '@celanese/ui-lib'

const CelaneseClientProvider = dynamic(
    () => import('@celanese/celanese-ui').then((mod) => mod.CelaneseClientProvider),
    { ssr: false }
)

const msalInstance = createPublicClientApplication()

const accounts = msalInstance.getAllAccounts()
if (accounts.length > 0) {
    msalInstance.setActiveAccount(accounts[0])
}

const getIdToken = (): Promise<string> => getIdTokenFromMsal(msalInstance)
const getAuthToken = (): Promise<string> => getIdTokenFromMsal(msalInstance, true)

export const CelaneseSDKProvider = ({ children }: PropsWithChildren) => {
    return (
        <CelaneseClientProvider
            authToken={getAuthToken}
            idToken={getIdToken}
            appId={environment.userManagementAppCode!}
            activeColorMode={useColorMode()}
        >
            {children}
        </CelaneseClientProvider>
    )
}
