'use client'

import { Box } from '@mui/material'
import { ClnPage } from '@celanese/ui-lib'
import { ClnShowingSites } from '@celanese/celanese-ui'
import { UserRolesPermission } from '@celanese/celanese-sdk'
import GenericFieldTitle from '../FieldsComponent/GenericFieldTitle'
import styles from './styles'
import { useMultiSiteEnabled } from '@/app/common/hooks/useMultiSiteEnabled'

type Props = {
    title: string
    activeUser?: UserRolesPermission
}

export default function PageHeader({ title, activeUser }: Props) {
    const isGlobal = useMultiSiteEnabled()
    const hasMultipleSites = (activeUser?.selectedSites?.length ?? 0) > 1

    return (
        <ClnPage.Header id="cln-page-header">
            <ClnPage.Title id="cln-page-title" multSite={!!hasMultipleSites} sx={styles.title}>
                {title}
            </ClnPage.Title>

            {isGlobal && (
                <ClnPage.InlineButtons>
                    {hasMultipleSites ? (
                        <ClnShowingSites />
                    ) : (
                        <Box id="title-location" sx={styles.locationBox}>
                            <GenericFieldTitle fieldName="Local:" isParagraphText />
                            <GenericFieldTitle fieldName={activeUser?.selectedSites?.[0]?.siteName ?? ''} isLocation />
                        </Box>
                    )}
                </ClnPage.InlineButtons>
            )}
        </ClnPage.Header>
    )
}
