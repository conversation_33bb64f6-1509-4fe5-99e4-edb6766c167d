import { gql } from '@apollo/client'
import { useEffect, useState } from 'react'
import { useGraphqlQuery } from '../cognite/useGraphqlQuery'
import { Status } from '../../models/action'
import { EntityType, GetSpace } from '../../utils/space-util'
import { ActionStatusQuery } from '../../enums/ActionItemStatusEnum'

export interface StatusFilterRequest {
    statusNameList?: string[]
    action?: ActionStatusQuery
}

const buildStatusQuery = (request: StatusFilterRequest): string => {
    const filters: string[] = []

    if (request?.action && request.statusNameList?.length) {
        const values = request.statusNameList.map((name) => `"${name}"`).join(', ')
        const nameFilter = `{ name: { in: [${values}] } }`

        filters.push(request.action === ActionStatusQuery.INCLUDE ? nameFilter : `{ not: ${nameFilter} }`)
    }

    filters.push(`{ space: { eq: "${GetSpace(EntityType.Static)}" } }`)

    const queryFilter = `{ and: [ ${filters.join(', ')} ] }`

    return `
        query GetAllStatus {
            listActionItemStatus(
                filter: ${queryFilter}
                , first: 1000
                , sort: { name: ASC }
            ) {
                items {
                    name
                    externalId
                    space
                }
            }
        }
    `
}

export const useActionItemStatuses = (request: StatusFilterRequest) => {
    const query = buildStatusQuery(request)
    const { data: fdmData } = useGraphqlQuery<Status>(gql(query), 'listActionItemStatus', {})

    const [resultData, setResultData] = useState<{ data: Status[]; loading: boolean }>({
        data: [],
        loading: true,
    })

    useEffect(() => {
        if (fdmData.length == 0) {
            setResultData({ data: [], loading: false })
        } else {
            setResultData({ data: fdmData, loading: false })
        }
    }, [fdmData])

    return {
        loading: resultData.loading,
        allStatus: resultData.data,
    }
}
