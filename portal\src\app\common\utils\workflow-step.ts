import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
import { generateNewExternalId } from '.'
import { WorkflowRequest } from '../models/action-detail'

dayjs.extend(utc)

export const generateWorkflowStep = (
    user: string,
    space: string,
    isApproved?: boolean,
    workflowId?: string
): WorkflowRequest => {
    const workflowStepRequest: WorkflowRequest = {
        externalId: generateNewExternalId('APWSP', isApproved ? 1 : 2),
        space: space,
    }

    workflowStepRequest.users = {
        node: {
            externalId: user,
            space: 'UMG-COR-ALL-DAT',
        },
        edge: {
            externalId: `${workflowStepRequest.externalId}-${user}`,
            space: space ?? '',
        },
    }

    workflowStepRequest.approvalWorkflow = {
        node: { externalId: workflowId ?? '', space: space ?? '' },
    }
    workflowStepRequest.approvalCondition = {
        node: { externalId: 'APWCO-AND', space: 'APW-COR-ALL-REF' },
    }
    workflowStepRequest.approvalWorkflowConsentType = {
        node: { externalId: 'APWCT-User', space: 'APW-COR-ALL-REF' },
    }
    workflowStepRequest.step = isApproved ? 1 : 2
    workflowStepRequest.status = { node: { externalId: 'APWST-Pending', space: 'APW-COR-ALL-REF' } }
    workflowStepRequest.description = isApproved ? 'AIM-approval' : 'AIM-verification'
    workflowStepRequest.startDate = dayjs().format('YYYY-MM-DD')

    return workflowStepRequest
}
