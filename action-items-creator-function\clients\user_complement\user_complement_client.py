from typing import Any

from cognite.client.data_classes import data_modeling

from clients.core.constants import (
    <PERSON><PERSON><PERSON>,
    REPORTING_LOCATIONS_TO_USER_COMPLEMENTS,
    REPORTING_UNITS_TO_USER_COMPLEMENTS,
    DataModelSpaceEnum,
    ViewEnum,
)
from clients.core.filters import equals_sdk_filter
from clients.core.models import Node, PaginatedData, ServiceParams
from clients.user_complement.requests import (
    GetUserComplementByLocationsRequest,
    GetUserComplementByUnitsRequest,
    GetUserRolesAndTeamsRequest,
    GetUsersRequest,
)

from .cognite_filter import (
    get_nested_in_filter,
    get_not_equals_filter,
    get_user_complement_filters,
    get_user_complements_by_units_filters,
    nested_sdk_filter,
)
from .models import UserComplementResult, UserRolesAndTeamsResult, UsersBySiteResult
from .queries import (
    GET_USER_COMPLEMENT_BY_EMAIL_QUERY,
    GET_USER_COMPLEMENTS_QUERY,
)


class UserComplementClient:
    """
    Initialize the UserComplementClient with the provided service parameters.

    Args:
        params (ServiceParams): The service parameters that include various client configurations.

    """

    def __init__(self, params: ServiceParams) -> None:
        """
        Initialize the UserComplementClient with the provided service parameters.

        Args:
            params (ServiceParams): The service parameters that include various client configurations.

        """
        self._cognite_client = params.cognite_client
        self._graphql_client = params.graphql_client
        self._data_model_id = params.data_model.as_id()
        self._settings = params.settings
        self._logging = params.logging
        self._user_complement_view = params.get_views()[ViewEnum.USER_COMPLEMENT]
        self._user_azure_attribute_view = params.get_views()[
            ViewEnum.USER_AZURE_ATTRIBUTE
        ]
        self._user_view = params.get_views()[ViewEnum.USER]
        self._reporting_site_view = params.get_views()[ViewEnum.REPORTING_SITE]

    def get_user_complements_by_units(
        self,
        request: GetUserComplementByUnitsRequest,
    ) -> list[UserComplementResult]:
        """
        Fetch user complements based on the provided locations request.

        Args:
            request (GetUserComplementByLocationsRequest): The request object containing the user's filters based on locations.

        Returns:
            list[UserComplementResult]: A list of user complement results.

        """
        with_clause = self._build_get_by_units_with_clause(request)

        select_clause_dict = {
            "userComplements": self._build_user_complements_select_clause(),
        }

        user_complements_non_filtered = (
            self._cognite_client.data_modeling.instances.query(
                data_modeling.query.Query(
                    with_=with_clause,
                    select=select_clause_dict,
                ),
            )
        )

        user_complements_ids = [
            node.external_id
            for node in Node.from_node_list(
                user_complements_non_filtered.get_nodes("userComplements"),
            )
        ]

        result = self._cognite_client.data_modeling.graphql.query(
            id=self._data_model_id,
            query=GET_USER_COMPLEMENTS_QUERY,
            variables={
                "userComplementFilter": get_user_complements_by_units_filters(
                    user_complements_ids,
                ),
            },
        )

        return self._map_user_complements_result(result)

    def get_user_complements_by_locations(
        self,
        request: GetUserComplementByLocationsRequest,
    ) -> list[UserComplementResult]:
        """
        Fetch user complements based on the provided locations request.

        Args:
            request (GetUserComplementByLocationsRequest): The request object containing the user's filters based on locations.

        Returns:
            list[UserComplementResult]: A list of user complement results.

        """
        with_clause = self._build_get_by_locations_with_clause(request)

        select_clause_dict = {
            "userComplements": self._build_user_complements_select_clause(),
        }

        user_complements_non_filtered = (
            self._cognite_client.data_modeling.instances.query(
                data_modeling.query.Query(
                    with_=with_clause,
                    select=select_clause_dict,
                ),
            )
        )

        user_complements_ids = [
            node.external_id
            for node in Node.from_node_list(
                user_complements_non_filtered.get_nodes("userComplements"),
            )
        ]

        result = self._cognite_client.data_modeling.graphql.query(
            id=self._data_model_id,
            query=GET_USER_COMPLEMENTS_QUERY,
            variables={
                "userComplementFilter": get_user_complements_by_units_filters(
                    user_complements_ids,
                ),
            },
        )

        return self._map_user_complements_result(result)

    def get_user_roles_and_teams(
        self,
        request: GetUserRolesAndTeamsRequest,
    ) -> PaginatedData[UserRolesAndTeamsResult]:
        """
        Retrieve user roles and teams based on the provided request.

        Args:
            request (GetUserRolesAndTeamsRequest): The request object containing the user's roles and teams filter.

        Returns:
            PaginatedData[UserRolesAndTeamsResult]: A paginated list of user roles and teams.

        """
        result = self._cognite_client.data_modeling.graphql.query(
            id=self._data_model_id,
            query=GET_USER_COMPLEMENT_BY_EMAIL_QUERY,
            variables=get_user_complement_filters(request),
        )

        return PaginatedData[UserRolesAndTeamsResult].from_graphql_response(result, 10)

    def get_users_by_site(self, request: GetUsersRequest) -> list[UsersBySiteResult]:
        """
        Retrieve users by site based on the provided request.

        Args:
            request (GetUsersRequest): The request object containing the site's user filter.

        Returns:
            list[UsersBySiteResult]: A list of users filtered by site.

        """
        with_clause = self._build_with_clause(request)
        select_clause_dict = self._build_select_clause_dict()

        all_data: data_modeling.NodeListWithCursor = []
        cursors = None

        while True:
            res = self._cognite_client.data_modeling.instances.query(
                data_modeling.query.Query(
                    with_=with_clause,
                    select=select_clause_dict,
                    cursors=cursors,
                ),
            )

            users = res.get_nodes("users")
            all_data.extend(users)

            if (
                not users
                or len(users) < LIMIT
                or not res.cursors.get("user_complements")
            ):
                break

            cursors = res.cursors

        return list(UsersBySiteResult.from_node_list(all_data))

    def _build_with_clause(self, request: GetUsersRequest) -> dict:
        """
        Build the 'with' clause for querying users by site.

        Args:
            request (GetUsersRequest): The request object containing the site's filter.

        Returns:
            dict: A dictionary containing the 'with' clause for the query.

        """
        return {
            "reporting_sites": data_modeling.query.NodeResultSetExpression(
                filter=data_modeling.filters.And(
                    equals_sdk_filter(
                        "externalId",
                        request.reporting_site_external_id,
                        self._reporting_site_view,
                    ),
                ),
                limit=LIMIT,
            ),
            "user_complements_edges": data_modeling.query.EdgeResultSetExpression(
                from_="reporting_sites",
                filter=data_modeling.filters.And(
                    data_modeling.filters.Equals(
                        ["edge", "type"],
                        {
                            "space": DataModelSpaceEnum.UMG_DATA_MODEL_SPACE,
                            "externalId": "UserComplement.reportingSites",
                        },
                    ),
                ),
                limit=LIMIT,
                direction="inwards",
            ),
            "user_complements": data_modeling.query.NodeResultSetExpression(
                from_="user_complements_edges",
                limit=LIMIT,
            ),
            "user_azure_attributes": data_modeling.query.NodeResultSetExpression(
                from_="user_complements",
                through=self._user_complement_view.as_property_ref(
                    "userAzureAttribute",
                ),
                limit=LIMIT,
            ),
            "users": data_modeling.query.NodeResultSetExpression(
                from_="user_azure_attributes",
                filter=data_modeling.filters.And(
                    equals_sdk_filter("active", value=True, view=self._user_view),
                    equals_sdk_filter(
                        "deleted",
                        value=False,
                        view=self._user_view,
                    ),
                ),
                through=self._user_azure_attribute_view.as_property_ref("user"),
                limit=LIMIT,
            ),
        }

    def _build_select_clause_dict(self) -> dict:
        """
        Build the select clause dictionary for querying users and user complements.

        Returns:
            dict: A dictionary containing the select clause for the query.

        """
        return {
            "users": self._get_select_user_clause(),
            "user_complements": self._get_select_user_complement_clause(),
        }

    def _build_user_complements_select_clause(self) -> data_modeling.query.Select:
        """
        Build the select clause for querying user complements.

        Returns:
            data_modeling.query.Select: The select clause for user complements.

        """
        return data_modeling.query.Select(
            sources=[
                data_modeling.query.SourceSelector(
                    source=self._user_complement_view,
                    properties=["userAzureAttribute"],
                ),
            ],
        )

    def _build_get_by_units_with_clause(
        self,
        request: GetUserComplementByUnitsRequest,
    ) -> dict:
        """
        Build the 'with' clause for querying user complements based on units.

        Args:
            request (GetUserComplementByUnitsRequest): The request object containing the units filter.

        Returns:
            dict: A dictionary containing the 'with' clause for the query.

        """
        filters = [
            get_not_equals_filter(
                "employeeStatus",
                request.employee_status_node,
                self._user_complement_view,
            ),
        ]

        if request.users_external_ids:
            filters.append(
                nested_sdk_filter(
                    property_name="userAzureAttribute",
                    sdk_filter=get_nested_in_filter(
                        property_table="user",
                        property_filter="externalId",
                        external_ids=request.users_external_ids,
                        view=self._user_azure_attribute_view,
                    ),
                    view=self._user_complement_view,
                ),
            )

        return {
            "reportingUnits": data_modeling.query.EdgeResultSetExpression(
                filter=data_modeling.filters.And(
                    data_modeling.filters.Equals(
                        ["edge", "type"],
                        REPORTING_UNITS_TO_USER_COMPLEMENTS,
                    ),
                    data_modeling.filters.In(
                        ["edge", "endNode"],
                        request.reporting_units,
                    ),
                ),
                direction="inwards",
                max_distance=1,
                limit=LIMIT,
            ),
            "userComplements": data_modeling.query.NodeResultSetExpression(
                from_="reportingUnits",
                filter=data_modeling.filters.And(*filters),
                limit=LIMIT,
            ),
        }

    def _build_get_by_locations_with_clause(
        self,
        request: GetUserComplementByLocationsRequest,
    ) -> dict:
        """
        Build the 'with' clause for querying user complements based on locations.

        Args:
            request (GetUserComplementByLocationsRequest): The request object containing the locations filter.

        Returns:
            dict: A dictionary containing the 'with' clause for the query.

        """
        return {
            "reportingLocations": data_modeling.query.EdgeResultSetExpression(
                filter=data_modeling.filters.And(
                    data_modeling.filters.Equals(
                        ["edge", "type"],
                        REPORTING_LOCATIONS_TO_USER_COMPLEMENTS,
                    ),
                    data_modeling.filters.In(
                        ["edge", "endNode"],
                        request.reporting_locations,
                    ),
                ),
                direction="inwards",
                max_distance=1,
                limit=LIMIT,
            ),
            "userComplements": data_modeling.query.NodeResultSetExpression(
                from_="reportingLocations",
                filter=get_not_equals_filter(
                    "employeeStatus",
                    request.employee_status_node,
                    self._user_complement_view,
                ),
                limit=LIMIT,
            ),
        }

    def _map_user_complements_result(
        self,
        response: dict[str, Any],
    ) -> list[UserComplementResult]:
        """
        Map the response from the Cognite GraphQL query to a list of user complements.

        Args:
            response (dict[str, Any]): The response from the GraphQL query.

        Returns:
            list[UserComplementResult]: A list of mapped user complement results.

        """
        keys = list(response.keys())
        entry = response[keys[0]]
        raw_items: list = entry.get("items", [])

        user_complements = []
        for item in raw_items:
            user_azure_attribute = item.get("userAzureAttribute", {})
            user = user_azure_attribute.get("user", {})
            user_complement = {
                "externalId": user_azure_attribute.get("externalId"),
                "space": user_azure_attribute.get("space"),
                "email": user.get("email"),
                "firstName": user.get("firstName"),
                "lastName": user.get("lastName"),
                "name": f"{user.get('lastName')}, {user.get('firstName')}",
                "label": f"{user.get('lastName')}, {user.get('firstName')} ({user.get('email')})",
                "userAzureAttribute": {
                    "externalId": user_azure_attribute.get("externalId"),
                    "space": user_azure_attribute.get("space"),
                    "user": {
                        "externalId": user.get("externalId"),
                        "space": user.get("space"),
                        "email": user.get("email"),
                        "firstName": user.get("firstName"),
                        "lastName": user.get("lastName"),
                        "active": user.get("active"),
                    },
                },
            }

            user_complements.append(user_complement)

        return user_complements

    def _get_select_user_clause(self) -> data_modeling.query.Select:
        """
        Build the select clause for querying users.

        Returns:
            data_modeling.query.Select: The select clause for users.

        """
        return data_modeling.query.Select(
            sources=[
                data_modeling.query.SourceSelector(
                    source=self._user_view,
                    properties=["email"],
                ),
            ],
            limit=LIMIT,
        )

    def _get_select_user_complement_clause(self) -> data_modeling.query.Select:
        """
        Build the select clause for querying user complements.

        Returns:
            data_modeling.query.Select: The select clause for user complements.

        """
        return data_modeling.query.Select(
            sources=[
                data_modeling.query.SourceSelector(
                    source=self._user_view,
                    properties=[],
                ),
            ],
            limit=LIMIT,
        )
