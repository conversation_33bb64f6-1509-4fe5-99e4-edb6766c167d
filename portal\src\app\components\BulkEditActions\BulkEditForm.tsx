import React, { useState, useMemo } from 'react'
import {
    Grid,
    TextField,
    Checkbox,
    FormControlLabel,
    Box,
    Typography,
    Autocomplete,
} from '@mui/material'
import { DatePicker } from '@mui/x-date-pickers/DatePicker'
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider'
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs'
import { translate } from '@celanese/celanese-ui'
import { UserRolesPermission } from '@celanese/celanese-sdk'

import { AutocompleteOption } from '../FieldsComponent/GenericAutocomplete'
import { AzureFunctionClient } from '../../common/clients/azure-function-client'
import { BulkEditFormData } from './types'
import { useUsersSearch } from '@/app/common/hooks/user-management/useUsers'
import { transformOptionsForUser } from '@/app/common/utils/transform-options-for-filter'
import { useReportingUnits } from '@/app/common/hooks/asset-hierarchy/useReportingUnits'
import { LocationQueryRequest, useReportingLocations } from '@/app/common/hooks/asset-hierarchy/useReportingLocations'
import { LineQueryRequest,useReportingLines } from '@/app/common/hooks/asset-hierarchy/useReportingLines'
import { useActionItemCategories } from '@/app/common/hooks/action-item-management/useActionItemCategories'
import { useActionItemSubCategories } from '@/app/common/hooks/action-item-management/useActionItemSubCategories'
import { useSiteSpecificCategories } from '@/app/common/hooks/action-item-management/useSiteSpecificCategories'
import { PriorityEnum } from '@/app/common/enums/PriorityEnum'

interface BulkEditFormProps {
    formData: BulkEditFormData
    setFormData: (data: BulkEditFormData) => void
    activeUser: UserRolesPermission
    siteId: string
}

interface FieldState {
    enabled: boolean
    value: any
}

export const BulkEditForm: React.FC<BulkEditFormProps> = ({
    formData,
    setFormData,
    activeUser,
    siteId,
}) => {
    const client = new AzureFunctionClient()

    // Field states to track which fields are enabled for editing
    const [fieldStates, setFieldStates] = useState<Record<string, FieldState>>({
        title: { enabled: false, value: '' },
        description: { enabled: false, value: '' },
        dueDate: { enabled: false, value: null },
        assignedToId: { enabled: false, value: null },
        approverId: { enabled: false, value: null },
        verifierId: { enabled: false, value: null },
        priorityId: { enabled: false, value: null },
        categoryId: { enabled: false, value: null },
        subCategoryId: { enabled: false, value: null },
        siteSpecificCategoryId: { enabled: false, value: null },
        reportingUnitId: { enabled: false, value: null },
        reportingLocationId: { enabled: false, value: null },
        reportingLineId: { enabled: false, value: null },
    })

    // Autocomplete options
    const [isLoadingOptions, setIsLoadingOptions] = useState(false)
    const [selectedOwnerOption, setSelectedOwnerOption] = useState<AutocompleteOption>()
    const [selectedAssigneeOption, setSelectedAssigneeOption] = useState<AutocompleteOption>()
    const [selectedApproverOption, setSelectedApproverOption] = useState<AutocompleteOption>()
    const [selectedVerificationOption, setSelectedVerificationOption] = useState<AutocompleteOption>()
    const [selectedCategoryOption, setSelectedCategoryOption] = useState<AutocompleteOption>()
    const [selectedSubCategoryOption, setSelectedSubCategoryOption] = useState<AutocompleteOption>()
    const [selectedReportingUnitOption, setSelectedReportingUnitOption] = useState<any>()
    const [selectedReportingLocationOption, setSelectedReportingLocationOption] = useState<any>()
    const [selectedReportingLineOption, setSelectedReportingLineOption] = useState<any>()
    const [selectedSiteSpecificCategoryOption, setSelectedSiteSpecificCategoryOption] = useState<any>()
    const [selectedPriorityOption, setSelectedPriorityOption] = useState<any>()
    const [priorityOptions, setPriorityOptions] = useState<AutocompleteOption[]>([])
    const [categoryOptions, setCategoryOptions] = useState<AutocompleteOption[]>([])
    const [subCategoryOptions, setSubCategoryOptions] = useState<AutocompleteOption[]>([])
    const [reportingUnitOptions, setReportingUnitOptions] = useState<AutocompleteOption[]>([])
    const [reportingLocationOptions, setReportingLocationOptions] = useState<AutocompleteOption[]>([])
    const [usersParam, setUsersParam] = useState<string>('NULL_PARAM')

    const { units } = useReportingUnits({ siteId })
    const { locations } = useReportingLocations(
        useMemo(() => {
            const request: LocationQueryRequest = { siteId, unitIds: [selectedReportingUnitOption || '-'], onlyActive: true }
            return request
        }, [selectedReportingUnitOption])
    )
    const { lines } = useReportingLines(
        useMemo(() => {
            const request: LineQueryRequest = { siteIds: [siteId], unitId: selectedReportingUnitOption || '-' }
            return request
        }, [selectedReportingUnitOption])
    )
    const { categories } = useActionItemCategories()
    const { subCategories } = useActionItemSubCategories()
    const { siteSpecificCategories } = useSiteSpecificCategories({ siteIds: [siteId] })
    const selectedPriorityOptions = Object.values(PriorityEnum).map((priority) => ({
        value: priority,
        label: translate(`stepper.form.assignment.priorityOptions.${priority}`),
        name: priority,
        externalId: priority,
    }))

    const { users: filtredUsers } = useUsersSearch(
        useMemo(() => {
            console.log('Users param:', usersParam)
            return usersParam
        }, [usersParam])
    )

    const { users: filtredActiveUser } = useUsersSearch(
        useMemo(() => {
            return activeUser.email.split('@')[0]
        }, [activeUser.email])
    )

    const sortedFiltredUsers = transformOptionsForUser([...filtredUsers], true)
    const sortedUsersOptions = Array.from(
        new Map([...sortedFiltredUsers].map((item) => [item.value, item])).values()
    ).sort((a, b) => a.label.localeCompare(b.label))

    const selectedOptionsUser = (user?: AutocompleteOption) => {
        let selectedOptionsUser: AutocompleteOption[] = [...sortedUsersOptions]

        if (user) {
            selectedOptionsUser = Array.from(new Set([...sortedUsersOptions, user]))
        }

        return selectedOptionsUser
    }

    const handleFieldToggle = (fieldKey: string, enabled: boolean) => {
        setFieldStates(prev => ({
            ...prev,
            [fieldKey]: { ...prev[fieldKey], enabled }
        }))

        if (enabled) {
            // Add field to form data with current value
            setFormData({
                ...formData,
                [fieldKey]: fieldStates[fieldKey].value
            })
        } else {
            // Remove field from form data
            const newFormData = { ...formData }
            delete newFormData[fieldKey as keyof BulkEditFormData]
            setFormData(newFormData)
        }
    }

    const handleFieldValueChange = (fieldKey: string, value: any) => {
        console.log('handleFieldValueChange', fieldKey, value)
        setFieldStates(prev => ({
            ...prev,
            [fieldKey]: { ...prev[fieldKey], value }
        }))

        if (fieldStates[fieldKey].enabled) {
            setFormData({
                ...formData,
                [fieldKey]: value
            })
        }
    }

    const renderTextField = (fieldKey: string, label: string, multiline = false, rows = 1) => (
        <Grid item xs={12} md={6} key={fieldKey}>
            <Box>
                <FormControlLabel
                    control={
                        <Checkbox
                            checked={fieldStates[fieldKey].enabled}
                            onChange={(e) => handleFieldToggle(fieldKey, e.target.checked)}
                        />
                    }
                    label={label}
                />
                <TextField
                    fullWidth
                    label={label}
                    value={fieldStates[fieldKey].value}
                    onChange={(e) => handleFieldValueChange(fieldKey, e.target.value)}
                    disabled={!fieldStates[fieldKey].enabled}
                    multiline={multiline}
                    rows={rows}
                    size="small"
                    sx={{ mt: 1 }}
                />
            </Box>
        </Grid>
    )

    const renderDateField = (fieldKey: string, label: string) => (
        <Grid item xs={12} md={6} key={fieldKey}>
            <Box>
                <FormControlLabel
                    control={
                        <Checkbox
                            checked={fieldStates[fieldKey].enabled}
                            onChange={(e) => handleFieldToggle(fieldKey, e.target.checked)}
                        />
                    }
                    label={label}
                />
                <LocalizationProvider dateAdapter={AdapterDayjs}>
                    <DatePicker
                        label={label}
                        value={fieldStates[fieldKey].value}
                        onChange={(newValue) => handleFieldValueChange(fieldKey, newValue?.format('YYYY-MM-DD'))}
                        disabled={!fieldStates[fieldKey].enabled}
                        slotProps={{
                            textField: {
                                fullWidth: true,
                                size: 'small',
                                sx: { mt: 1 }
                            }
                        }}
                    />
                </LocalizationProvider>
            </Box>
        </Grid>
    )

    const renderAutocompleteField = (
        fieldKey: string,
        label: string,
        value: any
    ) => {


        return (
            <Grid item xs={12} md={6} key={fieldKey}>
                <Box>
                    <FormControlLabel
                        control={
                            <Checkbox
                                checked={fieldStates[fieldKey].enabled}
                                onChange={(e) => handleFieldToggle(fieldKey, e.target.checked)}
                            />
                        }
                        label={label}
                    />
                    <Autocomplete
                        options={selectedOptionsUser(value)}
                        multiple={false}
                        getOptionLabel={(option) => option.label}
                        value={value}
                        onChange={(_, newValue) => handleFieldValueChange(fieldKey, newValue?.value || null)}
                        disabled={!fieldStates[fieldKey].enabled}
                        size="small"
                        isOptionEqualToValue={(option, value) => option.value === value?.value}
                        renderInput={(params) => (
                            <TextField
                                {...params}
                                label={label}
                                fullWidth
                                sx={{ mt: 1 }}
                            />
                        )}
                        onInputChange={(event, newInputValue) => {
                            if (newInputValue.length >= 3) {
                                setUsersParam(newInputValue)
                            }
                        }}
                    />
                </Box>
            </Grid>
        )
    }

    const renderAutocompleteUnitsField = (
        fieldKey: string,
        label: string,
        values: any,
        value:any,
        setValue:any
    ) => {


        return (
            <Grid item xs={12} md={6} key={fieldKey}>
                <Box>
                    <FormControlLabel
                        control={
                            <Checkbox
                                checked={fieldStates[fieldKey].enabled}
                                onChange={(e) => handleFieldToggle(fieldKey, e.target.checked)}
                            />
                        }
                        label={label}
                    />
                    <Autocomplete
                        options={values}
                        multiple={false}
                        getOptionLabel={(option) => option.name}
                        value={value}
                        onChange={(e, newValue) => {
                            console.log(newValue)
                            setValue(newValue.externalId || null)
                            handleFieldValueChange(fieldKey, newValue?.externalId || null)
                        }}
                        disabled={!fieldStates[fieldKey].enabled}
                        size="small"
                        isOptionEqualToValue={(option, value) => option.value === value?.value}
                        renderInput={(params) => (
                            <TextField
                                {...params}
                                label={label}
                                fullWidth
                                sx={{ mt: 1 }}
                            />
                        )}
                    />
                </Box>
            </Grid>
        )
    }


    return (
        <Box>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                {translate('bulkEditActionItems.selectFieldsInstruction')}
            </Typography>

            {isLoadingOptions && (
                <Typography variant="body2" color="primary" sx={{ mb: 2 }}>
                    Loading dropdown options...
                </Typography>
            )}

            <Grid container spacing={2}>
                {renderTextField('title', translate('actionItem.title'))}
                {renderTextField('description', translate('actionItem.description'), true, 3)}
                {renderDateField('dueDate', translate('actionItem.dueDate'))}
                {renderAutocompleteField('assignedToId', translate('actionItem.assignee'), selectedAssigneeOption)}
                {renderAutocompleteField('approverId', translate('actionItem.approver'), selectedApproverOption)}
                {renderAutocompleteField('verifierId', translate('actionItem.verifier'), selectedVerificationOption)}
                {renderAutocompleteUnitsField('priorityId', translate('actionItem.priority'), selectedPriorityOptions, selectedPriorityOption, setSelectedPriorityOption)}
                {renderAutocompleteUnitsField('categoryId', translate('actionItem.category'), categories, selectedCategoryOption, setSelectedCategoryOption)}
                {renderAutocompleteUnitsField('subCategoryId', translate('actionItem.subCategory'), subCategories, selectedSubCategoryOption, setSelectedSubCategoryOption)}
                {renderAutocompleteUnitsField('siteSpecificCategoryId', translate('actionItem.siteSpecificCategory'), siteSpecificCategories, selectedSiteSpecificCategoryOption, setSelectedSiteSpecificCategoryOption)}
                {renderAutocompleteUnitsField('reportingUnitId', translate('actionItem.reportingUnit'), units, selectedReportingUnitOption, setSelectedReportingUnitOption)}
                {renderAutocompleteUnitsField('reportingLocationId', translate('actionItem.reportingLocation'), locations, selectedReportingLocationOption, setSelectedReportingLocationOption)}
                {renderAutocompleteUnitsField('reportingLineId', translate('actionItem.reportingLines'), lines, selectedReportingLineOption, setSelectedReportingLineOption)}
            </Grid>
        </Box>
    )
}
