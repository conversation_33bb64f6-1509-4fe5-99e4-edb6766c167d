from typing import Optional


class SortMapper:
    """Map external IDs to sort values using predefined dictionaries for various attributes."""

    def __init__(
        self,
        action_status_map: Optional[dict[str, str]] = None,
        source_event_status_map: Optional[dict[str, str]] = None,
        user_map: Optional[dict[str, str]] = None,
        application_map: Optional[dict[str, str]] = None,
        reporting_unit_map: Optional[dict[str, str]] = None,
        reporting_location_map: Optional[dict[str, str]] = None,
        category_map: Optional[dict[str, str]] = None,
        sub_category_map: Optional[dict[str, str]] = None,
        site_specific_category_map: Optional[dict[str, str]] = None,
    ) -> None:
        """Initialize the SortMapper with optional mappings for various attributes."""
        self._action_status_map = action_status_map or {}
        self._source_event_status_map = source_event_status_map or {}
        self._user_map = user_map or {}
        self._application_map = application_map or {}
        self._reporting_unit_map = reporting_unit_map or {}
        self._reporting_location_map = reporting_location_map or {}
        self._category_map = category_map or {}
        self._sub_category_map = sub_category_map or {}
        self._site_specific_category_map = site_specific_category_map or {}

    def get_action_status_sort_value(self, external_id: Optional[str]) -> Optional[str]:
        """Return the sort value for action status based on the given external ID."""
        if external_id is None:
            return None
        return self._action_status_map.get(external_id)

    def get_source_event_status_sort_value(
        self,
        external_id: Optional[str],
    ) -> Optional[str]:
        """Return the sort value for source event status based on the given external ID."""
        if external_id is None:
            return None
        return self._source_event_status_map.get(external_id)

    def get_user_sort_value(self, external_id: Optional[str]) -> Optional[str]:
        """Return the sort value for a user based on the given external ID."""
        if external_id is None:
            return None
        return self._user_map.get(external_id)

    def get_user_azure_attribute_sort_value(
        self,
        external_id: Optional[str],
    ) -> Optional[str]:
        """Return the sort value for a user based on the Azure attribute extracted from the external ID."""
        if external_id is None:
            return None
        return self._user_map.get(external_id.replace("UserAzureAttribute_", ""))

    def get_application_sort_value(self, external_id: Optional[str]) -> Optional[str]:
        """Return the sort value for application based on the given external ID."""
        if external_id is None:
            return None
        return self._application_map.get(external_id)

    def get_reporting_unit_sort_value(
        self,
        external_id: Optional[str],
    ) -> Optional[str]:
        """Return the sort value for reporting unit based on the given external ID."""
        if external_id is None:
            return None
        return self._reporting_unit_map.get(external_id)

    def get_reporting_location_sort_value(
        self,
        external_id: Optional[str],
    ) -> Optional[str]:
        """Return the sort value for reporting location based on the given external ID."""
        if external_id is None:
            return None
        return self._reporting_location_map.get(external_id)

    def get_category_sort_value(self, external_id: Optional[str]) -> Optional[str]:
        """Return the sort value for category based on the given external ID."""
        if external_id is None:
            return None
        return self._category_map.get(external_id)

    def get_sub_category_sort_value(self, external_id: Optional[str]) -> Optional[str]:
        """Return the sort value for sub-category based on the given external ID."""
        if external_id is None:
            return None
        return self._sub_category_map.get(external_id)

    def get_site_specific_category_sort_value(
        self,
        external_id: Optional[str],
        space: Optional[str],
    ) -> Optional[str]:
        """Return the sort value for site-specific category based on the given external ID and space."""
        if external_id is None or space is None:
            return None
        return self._site_specific_category_map.get(external_id + space)

    def get_normalized_sort_value(self, text: Optional[str]) -> Optional[str]:
        """Return the normalized (lowercase) sort value for the given text."""
        if text is None:
            return None
        return text.lower()
