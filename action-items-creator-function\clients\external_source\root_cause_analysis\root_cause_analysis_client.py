from typing import Optional

from clients.core.models import ExternalSourceServiceParams, PaginatedData
from clients.external_source.root_cause_analysis.models import (
    EventAnalysis,
    EventInvestigation,
    WorkProcessInvestigation,
)
from clients.external_source.root_cause_analysis.queries import (
    GET_EVENT_ANALYSIS,
    GET_EVENT_INVESTIGATION,
    GET_WORK_PROCESS_INVESTIGATION,
)
from clients.external_source.root_cause_analysis.responses import (
    EventAnalysisResponse,
    EventInvestigationResponse,
    WorkProcessInvestigationResponse,
)


class RootCauseAnalysisClient:
    """Client for retrieving event analysis data from the external data model."""

    def __init__(self, params: ExternalSourceServiceParams) -> None:
        """
        Initialize the RootCauseAnalysisClient with external source service parameters.

        Args:
            params (ExternalSourceServiceParams): Configuration parameters including Cognite client,
                                                  logger, and data model reference.

        """
        self._cognite_client = params.cognite_client
        self._data_model_id = params.data_model.as_id()
        self.log = params.logging

    def get_event_analysis(
        self,
        external_event_id: str,
    ) -> Optional[EventAnalysisResponse]:
        """
        Retrieve event analysis details for a given external ID.

        Args:
            external_event_id (str): The external identifier of the event.

        Returns:
            Optional[EventAnalysisResponse]: The event analysis details if found, otherwise None.

        """
        result = self._cognite_client.data_modeling.graphql.query(
            id=self._data_model_id,
            query=GET_EVENT_ANALYSIS,
            variables={"filter": {"externalEventId": {"eq": external_event_id}}},
        )

        event_analysis = (
            PaginatedData[EventAnalysis]
            .from_graphql_response(result, 1)
            .first_or_default()
        )

        return event_analysis.to_response() if event_analysis is not None else None

    def get_event_investigation(
        self,
        external_id: str,
    ) -> Optional[EventInvestigationResponse]:
        """
        Retrieve event investigation for a given external ID.

        Args:
            external_id (str): The external identifier of the event investigation.

        Returns:
            Optional[EventInvestigationResponse]: The event investigation if found, otherwise None.

        """
        result = self._cognite_client.data_modeling.graphql.query(
            id=self._data_model_id,
            query=GET_EVENT_INVESTIGATION,
            variables={"filter": {"externalId": {"eq": external_id}}},
        )

        investigation = (
            PaginatedData[EventInvestigation]
            .from_graphql_response(result, 1)
            .first_or_default()
        )

        return investigation.to_response() if investigation is not None else None

    def get_work_process_investigation(
        self,
        external_id: str,
    ) -> Optional[WorkProcessInvestigationResponse]:
        """
        Retrieve work process investigation for a given external ID.

        Args:
            external_id (str): The external identifier of the work process investigation.

        Returns:
            Optional[WorkProcessInvestigationResponse]: The work process investigation if found, otherwise None.

        """
        result = self._cognite_client.data_modeling.graphql.query(
            id=self._data_model_id,
            query=GET_WORK_PROCESS_INVESTIGATION,
            variables={"filter": {"externalId": {"eq": external_id}}},
        )

        investigation = (
            PaginatedData[WorkProcessInvestigation]
            .from_graphql_response(result, 1)
            .first_or_default()
        )

        return investigation.to_response() if investigation is not None else None
