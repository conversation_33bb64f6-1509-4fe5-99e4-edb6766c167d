from datetime import date
from typing import Annotated

from industrial_model import ViewInstance, ViewInstanceConfig
from pydantic import Field

from clients.core.models import (
    AttachmentsEntity,
    BaseEntity,
    DescribableEntity,
    Node,
    ReportingSiteEntity,
)
from clients.core.validators import edge_unwraper_validator

from ._models import Action, Equipment, _UserAzureAttribute


class SourceEventResult(Node):
    """Represents a source event with basic attributes like title, dates, and status. It also tracks the count of actions related to the event."""

    description: str | None

    title: str | None

    due_date: date | None
    display_due_date: date | None
    assignment_date: date | None
    status: DescribableEntity | None
    category: DescribableEntity | None
    sub_category: DescribableEntity | None
    site_specific_category: DescribableEntity | None
    reporting_site: ReportingSiteEntity | None
    reporting_unit: DescribableEntity | None
    reporting_line: DescribableEntity | None
    owner: _UserAzureAttribute | None
    is_private: bool | None

    reporting_units: Annotated[list[DescribableEntity], edge_unwraper_validator]

    actions_count: int | None = None
    actions_completed_count: int | None = None

    equipments: Annotated[
        list[Equipment],
        edge_unwraper_validator,
    ]
    functional_locations: Annotated[
        list[DescribableEntity],
        edge_unwraper_validator,
    ]

    def set_aggregate_value(
        self,
        aggregate_total_actions_value: int | None,
        aggregate_completed_actions_value: int | None,
    ) -> None:
        """
        Set the total and completed actions count for the source event.

        This method updates the `actions_count` and `actions_completed_count` attributes
        with the provided aggregate values, setting them to `None` if no value is provided.

        Args:
            aggregate_total_actions_value (int or None):
                The total count of actions related to the event, or None.

            aggregate_completed_actions_value (int or None):
                The count of completed actions related to the event, or None.

        """
        self.actions_count = (
            aggregate_total_actions_value
            if aggregate_total_actions_value is not None
            else None
        )
        self.actions_completed_count = (
            aggregate_completed_actions_value
            if aggregate_completed_actions_value is not None
            else None
        )


class SourceEventByIdResult(SourceEventResult):
    """Represents a source event with more detailed information, including description, attachments, and secondary owners."""

    reporting_site: ReportingSiteEntity | None
    reporting_location: DescribableEntity | None
    business_line: DescribableEntity | None
    owner: _UserAzureAttribute | None
    attachments: list[AttachmentsEntity] | None

    impacted_reporting_locations: Annotated[
        list[DescribableEntity],
        edge_unwraper_validator,
    ]

    secondary_owner_users: Annotated[list[_UserAzureAttribute], edge_unwraper_validator]
    secondary_owner_roles: Annotated[list[DescribableEntity], edge_unwraper_validator]
    secondary_owner_teams: Annotated[list[DescribableEntity], edge_unwraper_validator]

    view_users: Annotated[list[_UserAzureAttribute], edge_unwraper_validator]
    view_roles: Annotated[list[DescribableEntity], edge_unwraper_validator]
    view_teams: Annotated[list[DescribableEntity], edge_unwraper_validator]

    views: list[str] | None


class SourceEventHistoryUpdate(BaseEntity):
    """Represents an update to the history of a source event, including status changes and comments."""

    source_event: Node
    status: str
    comments: str | None = None
    changed_at: str
    update_by: Node | None = None

    def get_properties_to_include(self) -> list[str]:
        """Return a list of properties that should be included in serialization, excluding certain fields."""
        properties = self.model_dump(
            by_alias=True,
            exclude_none=True,
        )
        return list(properties.keys())


class SourceEventStatusUpdate(BaseEntity):
    """Represents an update to the status of a source event, including an optional sorting status."""

    status: Node
    sort_status: str | None = Field(None)

    def get_properties_to_include(self) -> list[str]:
        """Return a list of properties that should be included in serialization, excluding certain fields."""
        properties = self.model_dump(
            by_alias=True,
            exclude={"external_id", "space"},
            exclude_none=True,
        )
        return list(properties.keys())


class SourceEventWithActionsResult(SourceEventResult):
    """Represents a source event that includes related actions."""

    actions: Annotated[list[Action], edge_unwraper_validator]
    views: list[str] | None = Field(None)
    attachments: list[AttachmentsEntity] | None = Field(None)


class SourceEventExportResponse(Node):
    """Represents the response for exporting a source event."""

    title: str | None = None
    description: str | None = None
    status: str | None = None
    reporting_site: str | None = None
    reporting_unit: str | None = None
    reporting_line: str | None = None
    category: str | None = None
    sub_category: str | None = None
    site_specific_category: str | None = None
    owner: str | None = None
    assignment_date: str | None = None
    display_due_date: str | None = None
    is_private: str | None = None
    actions_count: str | None = "0"
    actions_completed_count: str | None = "0"


class SourceEventExportView(ViewInstance):
    """Represents the result of a source event within the system."""

    view_config = ViewInstanceConfig(
        instance_spaces_prefix="SEVT-",
        view_external_id="SourceEvent",
    )

    external_id: str
    space: str
    title: str | None = None
    description: str | None = None
    status: DescribableEntity | None = None
    reporting_site: ReportingSiteEntity | None = None
    reporting_unit: DescribableEntity | None = None
    reporting_line: DescribableEntity | None = None
    category: DescribableEntity | None = None
    sub_category: DescribableEntity | None = None
    site_specific_category: DescribableEntity | None = None
    owner: _UserAzureAttribute | None = None
    assignment_date: date | None = None
    display_due_date: date | None = None
    is_private: bool | None = None
    actions_count: int | None = 0
    actions_completed_count: int | None = 0

    def set_aggregate_value(
        self,
        aggregate_total_actions_value: int | None = None,
        aggregate_completed_actions_value: int | None = None,
    ) -> None:
        """Set the total and completed actions count for the source event."""
        self.actions_count = aggregate_total_actions_value or 0
        self.actions_completed_count = aggregate_completed_actions_value or 0

    def _get_translation(
        self,
        key: str | None,
        translations: dict[str, str],
    ) -> str | None:
        if key is None:
            return None
        return translations.get(key, key)

    def _format_owner(self) -> str | None:
        if self.owner and self.owner.user:
            return f"{self.owner.user.last_name}, {self.owner.user.first_name}"
        return None

    def _format_reporting_unit(self) -> str | None:
        if self.reporting_unit and self.reporting_unit.name:
            return f"{self.reporting_unit.name[:3]} - {self.reporting_unit.description}"
        return None

    def to_response(self, translations: dict[str, str]) -> SourceEventExportResponse:
        """Cast the entity to its response type."""
        return SourceEventExportResponse(
            external_id=self.external_id,
            space=self.space,
            title=self.title,
            description=self.description,
            status=(
                self._get_translation(
                    getattr(self.status, "external_id", None),
                    translations,
                )
                if self.status
                else None
            ),
            reporting_site=getattr(self.reporting_site, "name", None),
            reporting_unit=self._format_reporting_unit(),
            reporting_line=getattr(self.reporting_line, "description", None),
            category=(
                self._get_translation(
                    getattr(self.category, "external_id", None),
                    translations,
                )
                if self.category
                else None
            ),
            sub_category=(
                self._get_translation(
                    getattr(self.sub_category, "external_id", None),
                    translations,
                )
                if self.sub_category
                else None
            ),
            site_specific_category=getattr(self.site_specific_category, "name", None),
            owner=self._format_owner(),
            assignment_date=str(self.assignment_date) if self.assignment_date else None,
            display_due_date=(
                str(self.display_due_date) if self.display_due_date else None
            ),
            is_private=self._get_translation(
                (
                    str(self.is_private).lower()
                    if self.is_private is not None
                    else "false"
                ),
                translations,
            ),
            actions_count=str(self.actions_count or 0),
            actions_completed_count=str(self.actions_completed_count or 0),
        )
