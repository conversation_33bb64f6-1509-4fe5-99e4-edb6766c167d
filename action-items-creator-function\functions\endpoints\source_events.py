import json
import logging
from http import HTT<PERSON>tatus

import azure.functions as func

from clients.core.constants import APPLICATION_JSON
from clients.source_event.requests import (
    ExportSourceEventsRequest,
    GetSourceEventByIdRequest,
    GetSourceEventRequest,
    UpdateSourceEventStatusRequest,
)
from infra.action_item_client_factory import ActionItemClientFactory
from services.source_event_service import SourceEventService

bp = func.Blueprint()


@bp.function_name(name="GetSourceEventsHome")
@bp.route("get-home-source-event", methods=["get"], auth_level=func.AuthLevel.ANONYMOUS)
async def main(req: func.HttpRequest) -> func.HttpResponse:
    """Retrieve source events for the event home view."""
    try:
        auth_header = req.headers.get("Authorization")

        if auth_header and auth_header.startswith("Bearer "):
            token_request = auth_header[len("Bearer ") :]
        else:
            token_request = auth_header

        source_event_request_param = req.params.get("sourceEventRequest", "{}")
        source_event_request = json.loads(source_event_request_param)

        logging.info("Function GetSourceEvents started")

        request = GetSourceEventRequest.model_validate(source_event_request)

        service = SourceEventService(
            ActionItemClientFactory.retriever(override_token=token_request),
        )
        events, total = await service.get_source_event_by_site(request)

        logging.info("Finishing execution - sourceEventRequest")

        items_dict = events.model_dump(mode="json", by_alias=True) if events else {}
        items_dict["totalSourceEvent"] = total
        response_body = json.dumps(items_dict)

        return func.HttpResponse(
            response_body,
            mimetype=APPLICATION_JSON,
            status_code=200,
        )
    except Exception as e:
        logging.exception("Exception found")
        return func.HttpResponse(f"Error: {e}", status_code=500)


@bp.function_name(name="GetSourceEventsById")
@bp.route(
    "get-source-event-by-id",
    methods=["get"],
    auth_level=func.AuthLevel.ANONYMOUS,
)
async def get_source_event_by_id_request(req: func.HttpRequest) -> func.HttpResponse:
    """Retrieve a source event by its ID."""
    try:
        auth_header = req.headers.get("Authorization")

        if auth_header and auth_header.startswith("Bearer "):
            token_request = auth_header[len("Bearer ") :]
        else:
            token_request = auth_header

        source_event_request_param = req.params.get("sourceEventRequest", "{}")
        source_event_request = json.loads(source_event_request_param)

        logging.info("Function GetSourceEventsById started")

        request = GetSourceEventByIdRequest.model_validate(source_event_request)

        service = SourceEventService(
            ActionItemClientFactory.retriever(override_token=token_request),
        )

        items = await service.get_source_event_by_id(request)

        logging.info("Finishing execution")
        logging.info("Access permission: %s", items is not None)

        response_body = json.dumps(
            {
                "data": items.model_dump(mode="json", by_alias=True) if items else {},
                "access": items is not None,
            },
        )

        return func.HttpResponse(
            response_body,
            mimetype=APPLICATION_JSON,
            status_code=200,
        )
    except Exception as e:
        logging.exception("Exception found")
        return func.HttpResponse(f"Error: {e}", status_code=500)


@bp.function_name(name="UpdateSourceEventStatus")
@bp.route(
    "update-source-event-status",
    methods=["patch"],
    auth_level=func.AuthLevel.ANONYMOUS,
)
async def update_source_event(req: func.HttpRequest) -> func.HttpResponse:
    """Update the status of a source event."""
    logging.info("Function UpdateSourceEventStatus started")
    try:
        token_request = req.headers.get("Authorization")

        body = req.get_json()

        request = UpdateSourceEventStatusRequest.model_validate(body)

        logging.info("Updating source event status in Cognite")
        service = SourceEventService(
            ActionItemClientFactory.retriever(override_token=token_request),
        )
        external_ids, errors = await service.update_source_event_status(request=request)

        logging.info("UpdateSourceEventStatus executed successfully")

        return func.HttpResponse(
            json.dumps({"externalIds": external_ids}),
            status_code=(
                HTTPStatus.OK if not errors else HTTPStatus.INTERNAL_SERVER_ERROR
            ),
            mimetype=APPLICATION_JSON,
        )
    except Exception as e:
        logging.exception("Error in UpdateSourceEventStatus")
        return func.HttpResponse(
            f"Error: {e}",
            status_code=HTTPStatus.INTERNAL_SERVER_ERROR,
            mimetype=APPLICATION_JSON,
        )


@bp.function_name(name="GetSourceEventsTitle")
@bp.route(
    "get-source-events-title",
    methods=["get"],
    auth_level=func.AuthLevel.ANONYMOUS,
)
async def get_source_events_title(req: func.HttpRequest) -> func.HttpResponse:
    """Retrieve titles of source events."""
    try:
        auth_header = req.headers.get("Authorization")

        if auth_header and auth_header.startswith("Bearer "):
            token_request = auth_header[len("Bearer ") :]
        else:
            token_request = auth_header

        request_param = req.params.get("sourceEventTitleRequest", "{}")
        request = json.loads(request_param)

        request = GetSourceEventRequest.model_validate(request)

        logging.info("Getting source events title in Cognite")

        service = SourceEventService(
            ActionItemClientFactory.retriever(override_token=token_request),
        )

        items = await service.get_source_events_title(request)

        logging.info("GetSourceEventsTitle executed successfully")

        return func.HttpResponse(
            json.dumps({"items": items}),
            status_code=(HTTPStatus.OK),
            mimetype=APPLICATION_JSON,
        )
    except Exception as e:
        logging.exception("Error in GetSourceEventsTitle")
        return func.HttpResponse(
            f"Error: {e}",
            status_code=HTTPStatus.INTERNAL_SERVER_ERROR,
            mimetype=APPLICATION_JSON,
        )


@bp.function_name(name="ExportSourceEvents")
@bp.route(
    "source-event/export",
    methods=["POST"],
    auth_level=func.AuthLevel.ANONYMOUS,
)
async def export_source_events(req: func.HttpRequest) -> func.HttpResponse:
    """Retrieve source events to be exported (e.g., to Excel)."""
    try:
        auth_header = req.headers.get("Authorization", "")
        token_request = (
            auth_header.replace("Bearer ", "").strip()
            if auth_header.startswith("Bearer ")
            else auth_header
        )

        try:
            request = req.get_json()
        except ValueError:
            return func.HttpResponse(
                "Invalid JSON in request body",
                status_code=HTTPStatus.BAD_REQUEST,
            )

        logging.info("Function ExportSourceEvents started")

        request = ExportSourceEventsRequest.model_validate(request)

        service = SourceEventService(
            ActionItemClientFactory.retriever(override_token=token_request),
        )
        return await service.export_source_events_to_excel(request)

    except Exception as e:
        logging.exception("Exception found in ExportSourceEvents")
        return func.HttpResponse(
            f"Error: {e!s}",
            status_code=HTTPStatus.INTERNAL_SERVER_ERROR,
        )
