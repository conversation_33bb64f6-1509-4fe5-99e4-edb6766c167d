from enum import StrEnum

NOTIFICATIONS_FUNCTION_EXTERNAL_ID = "ntf-event-receiver"
ACTION_ITEM_CREATION = "Action Item Creation"
OVERDUE_TYPE = "Overdue"
OVERDUE_N_DAYS_TYPE = "Action Due in [n] Days"

PRIMITIVE_TYPES = (str, int, float, bool)
NUMERIC_TYPES = (int, float)

NOTIFICATIONS_FUNCTION_EXTERNAL_ID = "ntf-event-receiver"


class NotificationTypes(StrEnum):
    DUE_DATE_EXTENSION = "Due Date Extension"
    REASSIGNMENT = "Reassignment"
    CHALLENGE = "Challenge"
    PENDING_APPROVAL = "Pending Approval"
    PENDING_VERIFICATION = "Pending Verification"
    APPROVAL_REJECTED = "Approval Rejected"
    VERIFICATION_REJECTED = "Verification Rejected"
    CANCELLED = "Cancelled"
    COMPLETED = "Completed"
    ACTION_ITEM_CREATION = "Action Item Creation"
    OVERDUE = "Overdue"
    DUE_IN_N_DAYS = "Action Due in [n] Days"
    OVERDUE_ALERT = "Overdue Alert"
    
class SendToTypes(StrEnum):
    ASSIGNEE = "STTYPE-Assignee"
    OWNER = "STTYPE-Owner"
    APPROVER = "STTYPE-Approver"
    VERIFIER = "STTYPE-Verifier"
    ROLE = "STTYPE-Role"