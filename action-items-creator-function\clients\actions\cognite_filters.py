from datetime import UTC, datetime
from typing import Any

from cognite.client.data_classes import data_modeling
from industrial_model import and_, col, not_, or_

from clients.category_configuration.models import CategoryConfigurationByFilterResult
from clients.core.constants import (
    WAS_SITE_EXTERNAL_ID,
    ActionItemKind,
    ActionStatusClearEnum,
    ActionStatusEnum,
    ApprovalWorkflowStatusEnum,
    ApprovalWorkflowStepDescriptionEnum,
    ChangeRequestTypeEnum,
    DataSpaceEnum,
    KpiNameEnum,
)
from clients.core.filters import (
    contains_any_filter,
    contains_sdk_filter,
    eq_filter,
    equals_sdk_filter,
    exists_sdk_filter,
    gte_filter,
    in_filter,
    in_sdk_filter,
    is_null_filter,
    lt_filter,
    node_id_filter,
    not_exists_sdk_filter,
    not_in_filter,
    not_in_sdk_filter,
    prefix_filter,
    prefix_sdk_filter,
    range_sdk_filter,
)
from utils.date_utils import (
    map_property_date_as_objects,
    map_property_date_as_objects_for_industrial_model,
    map_property_date_ranges,
)
from utils.list_utils import all_casings

from .models import ActionExportResult
from .requests import (
    BaseActionRequest,
    GetActionRequest,
    GetActionRequestForIndustrialModel,
)

PRIVATE_SPACE = "AIM-COR-ALL-PROT"


def _create_node_as_dict(external_id: str, space: str) -> dict[str, str]:
    return {"externalId": external_id, "space": space}


def _get_reporting_site_filter(request: GetActionRequest) -> dict[str, Any]:
    external_ids = request.reporting_site_external_ids
    if not external_ids:
        return {}

    return node_id_filter(external_ids, DataSpaceEnum.REF_DATA_SPACE)


def _get_user_space_filter() -> dict[str, dict[str, Any]]:
    return eq_filter("space", DataSpaceEnum.UMG_DATA_SPACE)


def _get_action_space_filter(
    request: GetActionRequest,
    no_views_filter: bool | None = None,
) -> dict[str, Any]:
    filter_private = {
        "and": [
            in_filter("space", request.get_filter_spaces(private_space=True)),
            contains_any_filter("views", request.views_private),
        ],
    }
    filter_site = in_filter("space", request.get_filter_spaces(private_space=False))
    if no_views_filter:
        return in_filter("space", request.get_filter_spaces())
    if request.only_private:
        return filter_private
    if request.only_private is not None:
        return filter_site
    return {"or": [filter_site, filter_private]}


def _get_action_item_filter(request: GetActionRequest) -> dict[str, Any]:
    filters: list[Any] = []

    if request.reporting_site_external_ids:
        filters.extend(
            [
                _get_action_space_filter(request),
                {"reportingSite": _get_reporting_site_filter(request)},
            ],
        )

    if request.external_ids:
        filter_func = not_in_filter if request.not_in_external_ids else in_filter
        filters.append(filter_func("externalId", request.external_ids))

    if request.status_external_ids:
        filters.append(
            {
                "currentStatus": node_id_filter(
                    request.status_external_ids,
                    DataSpaceEnum.AIM_REF_DATA_SPACE,
                ),
            },
        )

    if request.reporting_unit_external_ids:
        filters.append(
            {
                "reportingUnit": node_id_filter(
                    request.reporting_unit_external_ids,
                    DataSpaceEnum.REF_DATA_SPACE,
                ),
            },
        )

    if request.reporting_location_external_ids:
        filters.append(
            {
                "reportingLocation": node_id_filter(
                    request.reporting_location_external_ids,
                    DataSpaceEnum.REF_DATA_SPACE,
                ),
            },
        )

    if request.owner_external_id:
        filters.append(
            {
                "owner": node_id_filter(
                    request.owner_external_id,
                    DataSpaceEnum.UMG_DATA_SPACE,
                ),
            },
        )

    if request.assigned_to_external_id:
        filters.append(
            {
                "assignedTo": node_id_filter(
                    request.assigned_to_external_id,
                    DataSpaceEnum.UMG_DATA_SPACE,
                ),
            },
        )

    if request.category_external_id:
        filters.append(
            {
                "category": node_id_filter(
                    request.category_external_id,
                    DataSpaceEnum.AIM_REF_DATA_SPACE,
                ),
            },
        )

    if request.subcategory_external_id:
        filters.append(
            {
                "subCategory": node_id_filter(
                    request.subcategory_external_id,
                    DataSpaceEnum.AIM_REF_DATA_SPACE,
                ),
            },
        )

    if request.site_specific_category_external_id:
        filters.append(
            {
                "siteSpecificCategory": node_id_filter(
                    request.site_specific_category_external_id,
                    request.get_filter_spaces(all_sites=True),
                ),
            },
        )

    if request.application_external_id:
        filters.append(
            {
                "application": node_id_filter(
                    request.application_external_id,
                    DataSpaceEnum.UMG_DATA_SPACE,
                ),
            },
        )

    if request.approval_workflow_ids and request.kpi_filter in [
        KpiNameEnum.MY_APPROVALS,
        KpiNameEnum.MY_VERIFICATIONS,
    ]:
        filters.append(
            {
                "approvalWorkflow": node_id_filter(
                    request.approval_workflow_ids,
                    request.get_filter_spaces(),
                ),
            },
        )

    if request.source_type_external_ids:
        filters.append(
            {
                "sourceType": node_id_filter(
                    request.source_type_external_ids,
                    DataSpaceEnum.AIM_REF_DATA_SPACE,
                ),
            },
        )

    if request.external_id_prefix:
        filters.append(prefix_filter("externalId", request.external_id_prefix.upper()))

    if request.title_prefix:
        title_prefix_list = all_casings(request.title_prefix)

        title_prefix_filter = [
            prefix_filter("title", title) for title in title_prefix_list
        ]

        filters.append({"or": [*title_prefix_filter]})

    if request.source_info_prefix:
        source_info_prefix_list = all_casings(request.source_info_prefix)

        source_info_prefix_filter = [
            prefix_filter("sourceInformation", source_info)
            for source_info in source_info_prefix_list
        ]

        filters.append({"or": [*source_info_prefix_filter]})

    if request.source_ids:
        filters.append(in_filter("sourceId", request.source_ids))

    if request.due_date_gte:
        filters.append(gte_filter("displayDueDate", request.due_date_gte))

    if request.due_date_lt:
        filters.append(lt_filter("displayDueDate", request.due_date_lt))

    if request.due_date_eq:
        filters.append(eq_filter("displayDueDate", request.due_date_eq.isoformat()))

    if request.update_status_date:
        update_status_date = map_property_date_as_objects(
            request.update_status_date,
            "displayDueDate",
        )
        filters.append({"or": [*update_status_date]})
        filters.append(
            {
                "currentStatus": node_id_filter(
                    list(ActionStatusClearEnum.__members__.values()),
                    DataSpaceEnum.AIM_REF_DATA_SPACE,
                ),
            },
        )

    if request.icap_action_id_prefix:
        filters.append(prefix_filter("objectType", request.icap_action_id_prefix))

    if request.source_event_title_eq:
        filters.append(
            eq_filter("sortSourceEventTitle", request.source_event_title_eq.lower()),
        )

    if request.source_event_title_prefix:
        filters.append(
            prefix_filter(
                "sortSourceEventTitle",
                request.source_event_title_prefix.lower(),
            ),
        )

    def create_status_filter(
        statuses: list[str],
        space: DataSpaceEnum,
    ) -> dict[str, Any]:
        return {"currentStatus": node_id_filter(statuses, space)}

    if request.kpi_filter in [
        KpiNameEnum.OVERDUE,
        KpiNameEnum.RELATED_TO_ME,
        KpiNameEnum.MY_EXTENDS,
        KpiNameEnum.MY_ACTIONS,
        KpiNameEnum.MY_REASSIGNMENT,
    ]:
        related_filter: list[Any] = []

        status_filter_assigned = None
        status_filter_owner = None
        status_filter_workflow = None
        if request.kpi_filter == KpiNameEnum.OVERDUE:
            status_filter_assigned = create_status_filter(
                [
                    ActionStatusEnum.ASSIGNED,
                    ActionStatusEnum.APPROVAL_REJECTED,
                    ActionStatusEnum.VERIFICATION_REJECTED,
                ],
                DataSpaceEnum.AIM_REF_DATA_SPACE,
            )

            status_filter_owner = create_status_filter(
                [ActionStatusEnum.CHALLENGE_PERIOD],
                DataSpaceEnum.AIM_REF_DATA_SPACE,
            )

            status_filter_workflow = create_status_filter(
                [
                    ActionStatusEnum.PENDING_APPROVAL,
                    ActionStatusEnum.PENDING_VERIFICATION,
                ],
                DataSpaceEnum.AIM_REF_DATA_SPACE,
            )

            filters.append(lt_filter("displayDueDate", datetime.now(tz=UTC).date()))

        owner_filter = {
            "owner": node_id_filter(
                [request.active_user or "-"],
                DataSpaceEnum.UMG_DATA_SPACE,
            ),
        }

        assigned_filter = {
            "assignedTo": node_id_filter(
                [request.active_user or "-"],
                DataSpaceEnum.UMG_DATA_SPACE,
            ),
        }

        if request.kpi_filter == KpiNameEnum.MY_ACTIONS and request.active_user:
            filters.append({"or": [owner_filter, assigned_filter]})
            return {"and": filters}

        if (
            request.kpi_filter in [KpiNameEnum.OVERDUE, KpiNameEnum.RELATED_TO_ME]
            and request.active_user
        ):
            related_filter.append(
                {
                    "or": [
                        (
                            {
                                "and": [
                                    status_filter_owner,
                                    owner_filter,
                                ],
                            }
                            if status_filter_owner
                            else owner_filter
                        ),
                        (
                            {
                                "and": [
                                    status_filter_assigned,
                                    assigned_filter,
                                ],
                            }
                            if status_filter_assigned
                            else assigned_filter
                        ),
                    ],
                },
            )

        permissions_reporting_unit = None
        if request.permissions_reporting_unit_external_ids:
            permissions_reporting_unit = {
                "reportingUnit": node_id_filter(
                    request.permissions_reporting_unit_external_ids,
                    DataSpaceEnum.REF_DATA_SPACE,
                ),
            }

        extend_status = create_status_filter(
            [ActionStatusEnum.DUE_DATE_EXTENSION_PERIOD],
            DataSpaceEnum.AIM_REF_DATA_SPACE,
        )

        extension_approver_configs = (
            [
                config
                for config in request.category_configurations
                if config.default_extension_approver_role
                and request.active_user_roles_ids
                and config.default_extension_approver_role.external_id
                in request.active_user_roles_ids
            ]
            if request.category_configurations
            else []
        )

        non_extension_approver_configs = (
            [
                config
                for config in request.category_configurations
                if config.default_extension_approver_role
                and (
                    (
                        request.active_user_roles_ids
                        and config.default_extension_approver_role.external_id
                        not in request.active_user_roles_ids
                    )
                    or not request.active_user_roles_ids
                )
            ]
            if request.category_configurations
            else []
        )

        def create_category_filter(
            config: CategoryConfigurationByFilterResult,
        ) -> dict[str, Any]:
            return {
                "and": [
                    (
                        {
                            "and": [
                                eq_filter(
                                    "sortCategory",
                                    config.category.name,
                                ),
                                is_null_filter("sortCategory", is_null=False),
                            ],
                        }
                        if config.category
                        else is_null_filter("sortCategory", is_null=True)
                    ),
                    (
                        {
                            "and": [
                                eq_filter(
                                    "sortSubCategory",
                                    config.action_item_sub_category.name,
                                ),
                                is_null_filter("sortSubCategory", is_null=False),
                            ],
                        }
                        if config.action_item_sub_category
                        else is_null_filter("sortSubCategory", is_null=True)
                    ),
                    (
                        {
                            "and": [
                                eq_filter(
                                    "sortSiteSpecificCategory",
                                    config.site_specific_category.name,
                                ),
                                is_null_filter(
                                    "sortSiteSpecificCategory",
                                    is_null=False,
                                ),
                            ],
                        }
                        if config.site_specific_category
                        else is_null_filter("sortSiteSpecificCategory", is_null=True)
                    ),
                ],
            }

        in_category_filters = [
            create_category_filter(config) for config in extension_approver_configs
        ]
        not_in_category_filters = [
            {"not": create_category_filter(config)}
            for config in non_extension_approver_configs
        ]

        if request.permissions_extend:

            permissions_extend_sites = None
            if request.extension_approval_site_external_ids:
                sites = [
                    site
                    for site in request.extension_approval_site_external_ids
                    if site != WAS_SITE_EXTERNAL_ID
                ]
                if sites:
                    permissions_extend_sites = {
                        "reportingSite": node_id_filter(
                            sites,
                            DataSpaceEnum.REF_DATA_SPACE,
                        ),
                    }

            or_conditions = [
                *in_category_filters,
                {
                    "and": [
                        *not_in_category_filters,
                        permissions_reporting_unit,
                    ],
                },
            ]

            and_conditions = [
                extend_status,
                *not_in_category_filters,
            ]

            if permissions_extend_sites:
                or_conditions.append(permissions_extend_sites)
                and_conditions.append(permissions_extend_sites)

            related_filter.append(
                (
                    {
                        "and": [
                            extend_status,
                            {
                                "or": or_conditions,
                            },
                        ],
                    }
                    if permissions_reporting_unit
                    else {
                        "and": and_conditions,
                    }
                ),
            )
        else:
            (
                related_filter.append(
                    (
                        {"and": [extend_status, {"or": in_category_filters}]}
                        if in_category_filters
                        else {"and": [extend_status, prefix_filter("externalId", "-")]}
                    ),
                )
            )

        if (
            request.permissions_reassing
            and request.reassignment_approval_site_external_ids
        ):
            valid_sites = [
                site
                for site in request.reassignment_approval_site_external_ids
                if site != WAS_SITE_EXTERNAL_ID
            ]

            permissions_by_site = None
            if valid_sites:
                permissions_by_site = {
                    "reportingSite": node_id_filter(
                        valid_sites,
                        DataSpaceEnum.REF_DATA_SPACE,
                    ),
                }
            reassing_status = create_status_filter(
                [ActionStatusEnum.REASSIGNMENT_PERIOD],
                DataSpaceEnum.AIM_REF_DATA_SPACE,
            )
            or_filters = []

            if permissions_reporting_unit:
                or_filters.append(permissions_reporting_unit)

                if permissions_by_site:
                    or_filters.append(permissions_by_site)

            and_filters = []
            and_filters.append(reassing_status)

            if or_filters:
                and_filters.append({"or": or_filters})
            elif permissions_by_site:
                and_filters.append(permissions_by_site)

            related_filter.append({"and": and_filters})

        if request.approval_workflow_ids:
            workflow_filter = {
                "approvalWorkflow": node_id_filter(
                    request.approval_workflow_ids,
                    request.get_filter_spaces(),
                ),
            }
            related_filter.append(
                (
                    {"and": [status_filter_workflow, workflow_filter]}
                    if status_filter_workflow
                    else workflow_filter
                ),
            )

        filters.append({"or": related_filter})

    return {"and": filters}


def _get_user_site_configurations_filter(
    request: GetActionRequest,
) -> dict[str, list[Any]]:
    filters: list[Any] = [_get_user_space_filter()]

    if request.reporting_site_external_ids:
        filters.append({"reportingSite": _get_reporting_site_filter(request)})

    return {"and": filters}


def _get_approval_workflow_step_filter(
    request: GetActionRequest,
) -> dict[str, list[Any]]:
    descriptions = [
        ApprovalWorkflowStepDescriptionEnum.APPROVAL.value,
        ApprovalWorkflowStepDescriptionEnum.VERIFICATION.value,
    ]

    filters: list[Any] = [{"description": {"in": descriptions}}]

    if request.reporting_site_external_ids:
        filters.append(_get_action_space_filter(request, no_views_filter=True))

    return {"and": filters}


def _get_change_request_filter(request: GetActionRequest) -> dict[str, list[Any]]:
    change_request_type = node_id_filter(
        ChangeRequestTypeEnum.DUE_DATE,
        DataSpaceEnum.AIM_REF_DATA_SPACE,
    )

    approval_workflow = {
        "status": node_id_filter(
            [ApprovalWorkflowStatusEnum.APPROVED, ApprovalWorkflowStatusEnum.PROGRESS],
            DataSpaceEnum.APW_REF_DATA_SPACE,
        ),
    }

    change_request_reassigment_type = node_id_filter(
        ChangeRequestTypeEnum.REASSIGNMENT,
        DataSpaceEnum.AIM_REF_DATA_SPACE,
    )

    change_request_challenge_type = node_id_filter(
        ChangeRequestTypeEnum.CHALLENGE,
        DataSpaceEnum.AIM_REF_DATA_SPACE,
    )

    approval_workflow_reassigment_or_challenge = {
        "status": node_id_filter(
            [ApprovalWorkflowStatusEnum.PROGRESS],
            DataSpaceEnum.APW_REF_DATA_SPACE,
        ),
    }

    filters: list[Any] = [
        {
            "or": [
                {
                    "and": [
                        {"changeRequestType": change_request_type},
                        {"approvalWorkflow": approval_workflow},
                    ],
                },
                {
                    "and": [
                        {"changeRequestType": change_request_reassigment_type},
                        {
                            "approvalWorkflow": approval_workflow_reassigment_or_challenge,
                        },
                    ],
                },
                {
                    "and": [
                        {"changeRequestType": change_request_challenge_type},
                        {
                            "approvalWorkflow": approval_workflow_reassigment_or_challenge,
                        },
                    ],
                },
            ],
        },
    ]

    if request.reporting_site_external_ids:
        filters.append(_get_action_space_filter(request, no_views_filter=True))

    return {"and": filters}


def get_action_filters(request: GetActionRequest) -> dict[str, Any]:
    """
    Generate a dictionary of filters based on the given request.

    Args:
        request (GetActionRequest): The request object containing data to generate filters.

    Returns:
        dict[str, Any]: A dictionary with filters for pagination, action items, user configurations, approval workflow, and other parameters.

    Filters include:
        - "pageSize": The page size for the query.
        - "cursor": The cursor for pagination.
        - "actionItemFilter": Filter for action items.
        - "userSiteConfigurationsFilter": Filter for user site configurations.
        - "approvalWorkflowStepFilter": Filter for the approval workflow step.
        - "userOnApprovalWorkflowStepFilter": Filter for the user on the approval workflow step.
        - "sourceEventFilter": Filter for source events, if `reporting_site_external_ids` is provided.
        - "changeRequestFilter": Filter for change requests.
        - "sorting": Dictionary containing the sorting field and direction.

    """
    return {
        "pageSize": request.page_size,
        "cursor": request.cursor,
        "actionItemFilter": _get_action_item_filter(request),
        "userSiteConfigurationsFilter": _get_user_site_configurations_filter(request),
        "approvalWorkflowStepFilter": _get_approval_workflow_step_filter(request),
        "userOnApprovalWorkflowStepFilter": _get_user_space_filter(),
        "sourceEventFilter": (
            _get_action_space_filter(request)
            if request.reporting_site_external_id
            else None
        ),
        "changeRequestFilter": _get_change_request_filter(request),
        "sorting": {request.sort_by: request.direction},
    }


def _get_space_filter_for_industrial_model(
    request: GetActionRequestForIndustrialModel,
    no_views_filter: bool | None = None,
):
    filter_private = and_(
        col(ActionExportResult.space).in_(
            request.get_filter_spaces(private_space=True),
        ),
        col(ActionExportResult.views).contains_any_(request.views_private),
    )
    filter_site = col(ActionExportResult.space).in_(
        request.get_filter_spaces(private_space=False),
    )
    if no_views_filter:
        return col(ActionExportResult.space).in_(request.get_filter_spaces())
    if request.only_private:
        return filter_private
    if request.only_private is not None:
        return filter_site
    return or_(filter_site, filter_private)


def _get_action_filter_for_industrial_model(
    request: GetActionRequestForIndustrialModel,
):
    filters: list[bool] = []

    if request.reporting_site_external_ids:
        filters.extend(
            [
                _get_space_filter_for_industrial_model(request),
                col(ActionExportResult.reporting_site).nested_(
                    col("externalId").in_(request.reporting_site_external_ids),
                ),
            ],
        )

    if request.external_ids:
        filters.append(
            (
                col(ActionExportResult.external_id).not_("in", request.external_ids)
                if request.not_in_external_ids
                else col(ActionExportResult.external_id).in_(request.external_ids)
            ),
        )

    if request.site_specific_category_external_id:
        filters.append(
            col(ActionExportResult.site_specific_category).nested_(
                and_(
                    col("externalId").in_(request.site_specific_category_external_id),
                    col("space").in_(request.get_filter_spaces(all_sites=True)),
                ),
            ),
        )

    if request.approval_workflow_ids and request.kpi_filter in [
        KpiNameEnum.MY_APPROVALS,
        KpiNameEnum.MY_VERIFICATIONS,
    ]:

        filters.append(
            col("approvalWorkflow").nested_(
                and_(
                    col("externalId").in_(request.approval_workflow_ids),
                    col("space").in_(request.get_filter_spaces()),
                ),
            ),
        )

    if request.external_id_prefix:
        filters.append(
            col(ActionExportResult.external_id).prefix(
                request.external_id_prefix.upper(),
            ),
        )

    if request.title_prefix:
        title_prefix_list = all_casings(request.title_prefix)

        title_prefix_filter = [
            col(ActionExportResult.title).prefix(title) for title in title_prefix_list
        ]

        filters.append(or_(*title_prefix_filter))

    if request.source_info_prefix:
        source_info_prefix_list = all_casings(request.source_info_prefix)

        source_info_prefix_filter = [
            col(ActionExportResult.source_information).prefix(source_info)
            for source_info in source_info_prefix_list
        ]

        filters.append(or_(*source_info_prefix_filter))

    if request.due_date_gte:
        filters.append(
            col(ActionExportResult.display_due_date).gte_(
                request.due_date_gte.isoformat(),
            ),
        )

    if request.due_date_lt:
        filters.append(
            col(ActionExportResult.display_due_date).lt_(
                request.due_date_lt.isoformat(),
            ),
        )

    if request.due_date_eq:
        filters.append(
            col(ActionExportResult.display_due_date).equals_(
                request.due_date_eq.isoformat(),
            ),
        )

    if request.update_status_date:
        update_status_date = map_property_date_as_objects_for_industrial_model(
            request.update_status_date,
            ActionExportResult.display_due_date,
        )
        filters.append(or_(*update_status_date))
        filters.append(
            col(ActionExportResult.current_status).nested_(
                col("externalId").in_(list(ActionStatusClearEnum.__members__.values())),
            ),
        )

    if request.source_event_title_eq:
        filters.append(
            col("sortSourceEventTitle").equals_(request.source_event_title_eq.lower()),
        )

    if request.source_event_title_prefix:
        filters.append(
            col("sortSourceEventTitle").prefix(
                request.source_event_title_prefix.lower(),
            ),
        )

    def create_status_filter(
        statuses: list[str],
    ):
        return col(ActionExportResult.current_status).nested_(
            and_(
                col("externalId").in_(statuses),
                col("space").equals_(DataSpaceEnum.AIM_REF_DATA_SPACE),
            ),
        )

    if request.kpi_filter in [
        KpiNameEnum.OVERDUE,
        KpiNameEnum.RELATED_TO_ME,
        KpiNameEnum.MY_EXTENDS,
        KpiNameEnum.MY_ACTIONS,
        KpiNameEnum.MY_REASSIGNMENT,
    ]:
        related_filter: list[Any] = []

        status_filter_assigned = None
        status_filter_owner = None
        status_filter_workflow = None
        if request.kpi_filter == KpiNameEnum.OVERDUE:
            status_filter_assigned = create_status_filter(
                [
                    ActionStatusEnum.ASSIGNED,
                    ActionStatusEnum.APPROVAL_REJECTED,
                    ActionStatusEnum.VERIFICATION_REJECTED,
                ],
            )

            status_filter_owner = create_status_filter(
                [ActionStatusEnum.CHALLENGE_PERIOD],
            )

            status_filter_workflow = create_status_filter(
                [
                    ActionStatusEnum.PENDING_APPROVAL,
                    ActionStatusEnum.PENDING_VERIFICATION,
                ],
            )

            filters.append(
                col(ActionExportResult.display_due_date).lt_(
                    datetime.now(tz=UTC).date().isoformat(),
                ),
            )

        owner_filter = col(ActionExportResult.owner).nested_(
            col("externalId").in_([request.active_user or "-"]),
        )

        assigned_filter = col(ActionExportResult.assigned_to).nested_(
            col("externalId").in_([request.active_user or "-"]),
        )

        if request.kpi_filter == KpiNameEnum.MY_ACTIONS and request.active_user:
            filters.append(or_(owner_filter, assigned_filter))
            return and_(*filters)

        if (
            request.kpi_filter in [KpiNameEnum.OVERDUE, KpiNameEnum.RELATED_TO_ME]
            and request.active_user
        ):
            related_filter.append(
                or_(
                    (
                        and_(status_filter_owner, owner_filter)
                        if status_filter_owner
                        else owner_filter
                    ),
                    (
                        and_(status_filter_assigned, assigned_filter)
                        if status_filter_assigned
                        else assigned_filter
                    ),
                ),
            )

        permissions_reporting_unit = None
        if request.permissions_reporting_unit_external_ids:
            permissions_reporting_unit = col(ActionExportResult.reporting_unit).nested_(
                col("externalId").in_(request.permissions_reporting_unit_external_ids),
            )

        extend_status = create_status_filter(
            [ActionStatusEnum.DUE_DATE_EXTENSION_PERIOD],
        )

        extension_approver_configs = (
            [
                config
                for config in request.category_configurations
                if config.default_extension_approver_role
                and request.active_user_roles_ids
                and config.default_extension_approver_role.external_id
                in request.active_user_roles_ids
            ]
            if request.category_configurations
            else []
        )

        non_extension_approver_configs = (
            [
                config
                for config in request.category_configurations
                if config.default_extension_approver_role
                and (
                    (
                        request.active_user_roles_ids
                        and config.default_extension_approver_role.external_id
                        not in request.active_user_roles_ids
                    )
                    or not request.active_user_roles_ids
                )
            ]
            if request.category_configurations
            else []
        )

        def create_category_filter(
            config: CategoryConfigurationByFilterResult,
        ):
            return and_(
                (
                    col("sortCategory").equals_(config.category.name)
                    if config.category and config.category.name
                    else col("sortCategory").not_exists_()
                ),
                (
                    col("sortSubCategory").equals_(config.action_item_sub_category.name)
                    if config.action_item_sub_category
                    and config.action_item_sub_category.name
                    else col("sortSubCategory").not_exists_()
                ),
                (
                    col("sortSiteSpecificCategory").equals_(
                        config.site_specific_category.name,
                    )
                    if config.site_specific_category
                    and config.site_specific_category.name
                    else col("sortSiteSpecificCategory").not_exists_()
                ),
            )

        in_category_filters = [
            create_category_filter(config) for config in extension_approver_configs
        ]

        not_in_category_filters = [
            not_(create_category_filter(config))
            for config in non_extension_approver_configs
        ]

        if request.permissions_extend:

            permissions_extend_sites = None
            if request.extension_approval_site_external_ids:
                sites = [
                    site
                    for site in request.extension_approval_site_external_ids
                    if site != WAS_SITE_EXTERNAL_ID
                ]
                if sites:
                    permissions_extend_sites = col(
                        ActionExportResult.reporting_site,
                    ).nested_(
                        col("externalId").in_(sites),
                    )

            or_conditions = []
            if permissions_reporting_unit:
                or_conditions = [
                    *in_category_filters,
                    and_(*not_in_category_filters, permissions_reporting_unit),
                ]

            and_conditions = [extend_status, *not_in_category_filters]

            if permissions_extend_sites:
                or_conditions.append(permissions_extend_sites)
                and_conditions.append(permissions_extend_sites)

            related_filter.append(
                (
                    and_(
                        extend_status,
                        or_(*or_conditions),
                    )
                    if permissions_reporting_unit
                    else and_(*and_conditions)
                ),
            )
        else:
            (
                related_filter.append(
                    (
                        and_(extend_status, or_(*in_category_filters))
                        if in_category_filters
                        else and_(
                            extend_status,
                            col(ActionExportResult.external_id).prefix("-"),
                        )
                    ),
                )
            )

        if (
            request.permissions_reassing
            and request.reassignment_approval_site_external_ids
        ):
            valid_sites = [
                site
                for site in request.reassignment_approval_site_external_ids
                if site != WAS_SITE_EXTERNAL_ID
            ]

            permissions_by_site = None
            if valid_sites:
                permissions_by_site = col(ActionExportResult.reporting_site).nested_(
                    col("externalId").in_(valid_sites),
                )

            reassing_status = create_status_filter(
                [ActionStatusEnum.REASSIGNMENT_PERIOD],
            )

            or_filters = []

            if permissions_reporting_unit:
                or_filters.append(permissions_reporting_unit)

                if permissions_by_site:
                    or_filters.append(permissions_by_site)

            and_filters = []
            and_filters.append(reassing_status)

            if or_filters:
                and_filters.append(or_(*or_filters))
            elif permissions_by_site:
                and_filters.append(permissions_by_site)

            related_filter.append(and_(*and_filters))

        if request.approval_workflow_ids:
            workflow_filter = col("approvalWorkflow").nested_(
                and_(
                    col("externalId").in_(request.approval_workflow_ids),
                    col("space").in_(request.get_filter_spaces()),
                ),
            )

            related_filter.append(
                (
                    and_(status_filter_workflow, workflow_filter)
                    if status_filter_workflow
                    else workflow_filter
                ),
            )

        filters.append(or_(*related_filter))

    return and_(*filters)


def get_action_filters_for_industrial_model(
    request: GetActionRequestForIndustrialModel,
):
    """
    Generate a dictionary of filters based on the given request.

    Args:
        request (GetActionRequestForIndustrialModel): The request object containing data to generate filters.

    """
    return _get_action_filter_for_industrial_model(request)


def get_aggregate_filters(
    request: BaseActionRequest,
    action_view: data_modeling.ViewId,
) -> list[data_modeling.Filter]:
    """
    Generate a list of filters for aggregating action data based on the provided request and action view.

    This function creates a list of filters used to query and filter action items, taking into account various
    request parameters such as external IDs, statuses, due dates, and user roles. It supports filtering by different
    attributes, including action items' status, category, due dates, and specific roles assigned to the action.

    Args:
        request (BaseActionRequest):
            The request object containing filter criteria such as external IDs, statuses, due dates, and user-related parameters.

        action_view (data_modeling.ViewId):
            The action view used to filter the data based on specific views.

    Returns:
        list[data_modeling.Filter]:
            A list of filters that apply to action items, combining multiple conditions based on the input request.

    """

    def _equals_filter(
        attribute: str,
        external_id: str,
        space: str,
    ) -> data_modeling.filters.Equals:
        external_id_mapping = _create_node_as_dict(external_id, space)

        return equals_sdk_filter(
            attribute,
            external_id_mapping,
            action_view,
        )

    def _in_filter(
        attribute: str,
        external_ids: list[str],
        space: str | None = None,
    ) -> data_modeling.filters.In:
        external_ids_mapping = (
            external_ids
            if space is None
            else [
                _create_node_as_dict(external_id, space) for external_id in external_ids
            ]
        )

        return in_sdk_filter(
            attribute,
            external_ids_mapping,
            action_view,
        )

    def _contains_filter(
        attribute: str,
        external_ids: list[str],
    ) -> data_modeling.filters.ContainsAny:
        return contains_sdk_filter(
            attribute,
            external_ids,
            action_view,
        )

    def _not_exists_filter(attribute: str) -> data_modeling.filters.Not:
        return not_exists_sdk_filter(attribute, action_view)

    def _exists_filter(attribute: str) -> data_modeling.filters.Exists:
        return exists_sdk_filter(attribute, action_view)

    def _get_action_space_aggregate_filter(
        request: BaseActionRequest,
    ) -> (
        data_modeling.filters.Or
        | data_modeling.filters.And
        | data_modeling.filters.SpaceFilter
    ):
        filter_private = data_modeling.filters.And(
            data_modeling.filters.SpaceFilter(
                request.get_filter_spaces(private_space=True),
            ),
            _contains_filter("views", request.views_private),
        )

        filter_site = data_modeling.filters.SpaceFilter(
            request.get_filter_spaces(private_space=False),
        )

        if request.only_private:
            return filter_private
        if request.only_private is not None:
            return filter_site
        return data_modeling.filters.Or(filter_site, filter_private)

    action_filters: list[data_modeling.Filter] = [
        _equals_filter(
            "actionItemKind",
            ActionItemKind.ONE_TIME,
            DataSpaceEnum.AIM_REF_DATA_SPACE,
        ),
    ]

    if request.reporting_site_external_ids:
        action_filters.extend(
            [
                _get_action_space_aggregate_filter(request),
                _in_filter(
                    "reportingSite",
                    request.reporting_site_external_ids,
                    DataSpaceEnum.REF_DATA_SPACE,
                ),
            ],
        )

    if request.external_ids:
        action_filters.append(
            (
                not_in_sdk_filter("externalId", request.external_ids, action_view)
                if request.not_in_external_ids
                else _in_filter(
                    "externalId",
                    request.external_ids,
                )
            ),
        )

    if request.source_ids:
        action_filters.append(
            _in_filter(
                "sourceId",
                request.source_ids,
            ),
        )

    if request.source_type_external_ids:
        action_filters.append(
            _in_filter(
                "sourceType",
                request.source_type_external_ids,
                DataSpaceEnum.AIM_REF_DATA_SPACE,
            ),
        )

    if request.status_external_ids:
        action_filters.append(
            _in_filter(
                "currentStatus",
                request.status_external_ids,
                DataSpaceEnum.AIM_REF_DATA_SPACE,
            ),
        )

    if request.reporting_unit_external_ids:
        action_filters.append(
            _in_filter(
                "reportingUnit",
                request.reporting_unit_external_ids,
                DataSpaceEnum.REF_DATA_SPACE,
            ),
        )

    if request.reporting_location_external_ids:
        action_filters.append(
            _in_filter(
                "reportingLocation",
                request.reporting_location_external_ids,
                DataSpaceEnum.REF_DATA_SPACE,
            ),
        )

    if request.owner_external_id:
        action_filters.append(
            _in_filter(
                "owner",
                request.owner_external_id,
                DataSpaceEnum.UMG_DATA_SPACE,
            ),
        )

    if request.assigned_to_external_id:
        action_filters.append(
            _in_filter(
                "assignedTo",
                request.assigned_to_external_id,
                DataSpaceEnum.UMG_DATA_SPACE,
            ),
        )

    if request.category_external_id:
        action_filters.append(
            _in_filter(
                "category",
                request.category_external_id,
                DataSpaceEnum.AIM_REF_DATA_SPACE,
            ),
        )

    if request.subcategory_external_id:
        action_filters.append(
            _in_filter(
                "subCategory",
                request.subcategory_external_id,
                DataSpaceEnum.AIM_REF_DATA_SPACE,
            ),
        )

    if request.site_specific_category_external_id:
        all_spaces = request.get_filter_spaces(all_sites=True)
        external_ids_mapping = [
            _create_node_as_dict(external_id, space)
            for external_id in request.site_specific_category_external_id
            for space in all_spaces
        ]
        action_filters.append(
            in_sdk_filter(
                "siteSpecificCategory",
                external_ids_mapping,
                action_view,
            ),
        )

    if request.application_external_id:
        action_filters.append(
            _in_filter(
                "application",
                request.application_external_id,
                DataSpaceEnum.UMG_DATA_SPACE,
            ),
        )

    if request.approval_workflow_ids and request.kpi_filter in [
        KpiNameEnum.MY_APPROVALS,
        KpiNameEnum.MY_VERIFICATIONS,
    ]:
        nodes = [
            _create_node_as_dict(workflow.external_id, workflow.space)
            for workflow in request.approval_workflow_nodes or []
        ]
        action_filters.append(in_sdk_filter("approvalWorkflow", nodes, action_view))

    if request.external_id_prefix:
        action_filters.append(
            prefix_sdk_filter(
                "externalId",
                request.external_id_prefix.upper(),
                action_view,
            ),
        )

    if request.title_prefix:
        tile_prefix_list = all_casings(request.title_prefix)

        title_prefix_filter = [
            prefix_sdk_filter("title", title, action_view) for title in tile_prefix_list
        ]

        action_filters.append(data_modeling.filters.Or(*title_prefix_filter))

    if request.source_info_prefix:
        source_info_prefix_list = all_casings(request.source_info_prefix)

        source_info_prefix_filter = [
            prefix_sdk_filter("sourceInformation", source_info, action_view)
            for source_info in source_info_prefix_list
        ]

        action_filters.append(data_modeling.filters.Or(*source_info_prefix_filter))

    if request.due_date_gte:
        action_filters.append(
            range_sdk_filter(
                "displayDueDate",
                action_view,
                gte=request.due_date_gte.isoformat(),
            ),
        )

    if request.due_date_lt:
        action_filters.append(
            range_sdk_filter(
                "displayDueDate",
                action_view,
                lt=request.due_date_lt.isoformat(),
            ),
        )

    if request.due_date_eq:
        action_filters.append(
            prefix_sdk_filter(
                "displayDueDate",
                request.due_date_eq.isoformat(),
                action_view,
            ),
        )

    if request.update_status_date:
        update_status_date = map_property_date_ranges(
            request.update_status_date,
            "displayDueDate",
        )
        action_filters.append(data_modeling.filters.Or(*update_status_date))
        action_filters.append(
            _in_filter(
                "currentStatus",
                list(ActionStatusClearEnum.__members__.values()),
                DataSpaceEnum.AIM_REF_DATA_SPACE,
            ),
        )

    if request.icap_action_id_prefix:
        action_filters.append(
            prefix_sdk_filter("objectType", request.icap_action_id_prefix, action_view),
        )

    if request.source_event_title_eq:
        action_filters.append(
            equals_sdk_filter(
                "sortSourceEventTitle",
                request.source_event_title_eq.lower(),
                action_view,
            ),
        )

    if request.source_event_title_prefix:
        action_filters.append(
            prefix_sdk_filter(
                "sortSourceEventTitle",
                request.source_event_title_prefix.lower(),
                action_view,
            ),
        )

    def create_status_filter(
        statuses: list[str],
        space: DataSpaceEnum,
    ) -> data_modeling.filters.In:
        return _in_filter("currentStatus", statuses, space)

    if request.kpi_filter in [
        KpiNameEnum.OVERDUE,
        KpiNameEnum.RELATED_TO_ME,
        KpiNameEnum.MY_EXTENDS,
        KpiNameEnum.MY_ACTIONS,
        KpiNameEnum.MY_REASSIGNMENT,
    ]:
        related_filter = []

        status_filter_assigned = None
        status_filter_owner = None
        status_filter_workflow = None
        if request.kpi_filter == KpiNameEnum.OVERDUE:
            status_filter_assigned = create_status_filter(
                [
                    ActionStatusEnum.ASSIGNED,
                    ActionStatusEnum.APPROVAL_REJECTED,
                    ActionStatusEnum.VERIFICATION_REJECTED,
                ],
                DataSpaceEnum.AIM_REF_DATA_SPACE,
            )

            status_filter_owner = create_status_filter(
                [ActionStatusEnum.CHALLENGE_PERIOD],
                DataSpaceEnum.AIM_REF_DATA_SPACE,
            )

            status_filter_workflow = create_status_filter(
                [
                    ActionStatusEnum.PENDING_APPROVAL,
                    ActionStatusEnum.PENDING_VERIFICATION,
                ],
                DataSpaceEnum.AIM_REF_DATA_SPACE,
            )

            action_filters.append(
                range_sdk_filter(
                    "displayDueDate",
                    action_view,
                    lt=datetime.now(tz=UTC).date().isoformat(),
                ),
            )

        owner_filter = _in_filter(
            "owner",
            [request.active_user or "-"],
            DataSpaceEnum.UMG_DATA_SPACE,
        )

        assigned_filter = _in_filter(
            "assignedTo",
            [request.active_user or "-"],
            DataSpaceEnum.UMG_DATA_SPACE,
        )

        if request.kpi_filter == KpiNameEnum.MY_ACTIONS and request.active_user:
            action_filters.append(
                data_modeling.filters.Or(owner_filter, assigned_filter),
            )
            return action_filters

        if (
            request.kpi_filter in [KpiNameEnum.OVERDUE, KpiNameEnum.RELATED_TO_ME]
            and request.active_user
        ):
            related_filter.append(
                data_modeling.filters.Or(
                    (
                        data_modeling.filters.And(
                            owner_filter,
                            status_filter_owner,
                        )
                        if status_filter_owner
                        else owner_filter
                    ),
                    (
                        data_modeling.filters.And(
                            assigned_filter,
                            status_filter_assigned,
                        )
                        if status_filter_assigned
                        else assigned_filter
                    ),
                ),
            )

        permissions_reporting_unit = None
        if request.permissions_reporting_unit_external_ids:
            permissions_reporting_unit = _in_filter(
                "reportingUnit",
                request.permissions_reporting_unit_external_ids,
                DataSpaceEnum.REF_DATA_SPACE,
            )

        extend_status = create_status_filter(
            [ActionStatusEnum.DUE_DATE_EXTENSION_PERIOD],
            DataSpaceEnum.AIM_REF_DATA_SPACE,
        )

        extension_approver_configs = (
            [
                config
                for config in request.category_configurations
                if config.default_extension_approver_role
                and request.active_user_roles_ids
                and config.default_extension_approver_role.external_id
                in request.active_user_roles_ids
            ]
            if request.category_configurations
            else []
        )

        non_extension_approver_configs = (
            [
                config
                for config in request.category_configurations
                if config.default_extension_approver_role
                and (
                    (
                        request.active_user_roles_ids
                        and config.default_extension_approver_role.external_id
                        not in request.active_user_roles_ids
                    )
                    or not request.active_user_roles_ids
                )
            ]
            if request.category_configurations
            else []
        )

        def create_category_filter(
            config: CategoryConfigurationByFilterResult,
        ) -> data_modeling.filters.And:
            return data_modeling.filters.And(
                (
                    data_modeling.filters.And(
                        prefix_sdk_filter(
                            "sortCategory",
                            config.category.name,
                            action_view,
                        ),
                        _exists_filter("category"),
                    )
                    if config.category and config.category.name
                    else _not_exists_filter("category")
                ),
                (
                    data_modeling.filters.And(
                        prefix_sdk_filter(
                            "sortSubCategory",
                            config.action_item_sub_category.name,
                            action_view,
                        ),
                        _exists_filter("subCategory"),
                    )
                    if config.action_item_sub_category
                    and config.action_item_sub_category.name
                    else _not_exists_filter("subCategory")
                ),
                (
                    data_modeling.filters.And(
                        prefix_sdk_filter(
                            "sortSiteSpecificCategory",
                            config.site_specific_category.name,
                            action_view,
                        ),
                        _exists_filter("siteSpecificCategory"),
                    )
                    if config.site_specific_category
                    and config.site_specific_category.name
                    else _not_exists_filter("siteSpecificCategory")
                ),
            )

        in_category_filters = [
            create_category_filter(config) for config in extension_approver_configs
        ]
        not_in_category_filters = [
            data_modeling.filters.Not(create_category_filter(config))
            for config in non_extension_approver_configs
        ]

        if request.permissions_extend:

            permissions_extend_sites = None
            if request.extension_approval_site_external_ids:
                sites = [
                    site
                    for site in request.extension_approval_site_external_ids
                    if site != WAS_SITE_EXTERNAL_ID
                ]
                if sites:
                    permissions_extend_sites = _in_filter(
                        "reportingSite",
                        sites,
                        DataSpaceEnum.REF_DATA_SPACE,
                    )

            or_conditions: list[Any] = []
            if permissions_reporting_unit:
                or_conditions = [
                    *in_category_filters,
                    data_modeling.filters.And(
                        *not_in_category_filters,
                        permissions_reporting_unit,
                    ),
                ]

            and_conditions = [
                extend_status,
                *not_in_category_filters,
            ]

            if permissions_extend_sites:
                or_conditions.append(permissions_extend_sites)
                and_conditions.append(permissions_extend_sites)

            related_filter.append(
                (
                    data_modeling.filters.And(
                        extend_status,
                        data_modeling.filters.Or(*or_conditions),
                    )
                    if permissions_reporting_unit
                    else data_modeling.filters.And(*and_conditions)
                ),
            )
        else:
            related_filter.append(
                (
                    data_modeling.filters.And(
                        extend_status,
                        data_modeling.filters.Or(*in_category_filters),
                    )
                    if in_category_filters
                    else data_modeling.filters.And(
                        extend_status,
                        prefix_sdk_filter("externalId", "-", action_view),
                    )
                ),
            )

        if (
            request.permissions_reassing
            and request.reassignment_approval_site_external_ids
        ):
            valid_sites = [
                site
                for site in request.reassignment_approval_site_external_ids
                if site != WAS_SITE_EXTERNAL_ID
            ]

            permissions_by_site = None
            if valid_sites:
                permissions_by_site = _in_filter(
                    "reportingSite",
                    valid_sites,
                    DataSpaceEnum.REF_DATA_SPACE,
                )

            reassing_status = create_status_filter(
                [ActionStatusEnum.REASSIGNMENT_PERIOD],
                DataSpaceEnum.AIM_REF_DATA_SPACE,
            )

            or_filters = []

            if permissions_reporting_unit:
                or_filters.append(permissions_reporting_unit)

                if permissions_by_site:
                    or_filters.append(permissions_by_site)

            and_filters = []
            and_filters.append(reassing_status)

            if or_filters:
                and_filters.append(data_modeling.filters.Or(*or_filters))
            elif permissions_by_site:
                and_filters.append(permissions_by_site)

            related_filter.append(data_modeling.filters.And(*and_filters))

        if request.approval_workflow_ids:
            nodes = [
                _create_node_as_dict(workflow.external_id, workflow.space)
                for workflow in request.approval_workflow_nodes or []
            ]
            workflow_filter = in_sdk_filter("approvalWorkflow", nodes, action_view)

            related_filter.append(
                (
                    data_modeling.filters.And(
                        workflow_filter,
                        status_filter_workflow,
                    )
                    if status_filter_workflow
                    else workflow_filter
                ),
            )

        action_filters.append(data_modeling.filters.Or(*related_filter))

    return action_filters
