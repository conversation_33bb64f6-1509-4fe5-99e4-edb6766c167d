import { ClnButton, MatIcon } from '@celanese/ui-lib'
import { ActionDetailItem } from '@/app/common/models/action-detail'
import { useState } from 'react'
import RemoveItemModal from './RemoveItemModal'
import { translate } from '@/app/common/utils/generate-translate'

type RemoveItemIconButtonProps = {
    actionDetails?: ActionDetailItem
    activeUserEmail: string
    light?: boolean
    size?: 'small' | 'medium' | 'large'
    hidden?: boolean
    challengeRequest?: boolean
    handleAlert: (message: string, error?: boolean) => void
    label?: string
    isOutline?: boolean
    isDelete?: boolean
    fullWidth?: boolean
    iconSize?: string

    redirectToPage?: () => void
}

export default function RemoveItemIconButton({
    actionDetails,
    activeUserEmail,
    light,
    size = 'medium',
    hidden,
    challengeRequest,
    handleAlert,
    label = translate('requestModal.delete'),
    isOutline = false,
    isDelete = false,
    fullWidth = false,
    iconSize,
    redirectToPage,
}: RemoveItemIconButtonProps) {
    const [openModal, setOpenModal] = useState<boolean>(false)

    const handleOpen = () => {
        setOpenModal(true)
    }

    const handleClose = () => {
        setOpenModal(false)
    }

    return (
        <>
            {!hidden &&
                (light ? (
                    <ClnButton
                        variant={isOutline ? 'outlined' : 'contained'}
                        size={size}
                        label={label}
                        onClick={handleOpen}
                        sx={
                            isOutline
                                ? {
                                      '@media (max-width:1024px)': {
                                          width: fullWidth ? '100%' : '5rem',
                                      },
                                  }
                                : {
                                      flex: 1,
                                  }
                        }
                        type="button"
                        color="error"
                    />
                ) : (
                    <MatIcon
                        sx={{
                            cursor: 'pointer',
                        }}
                        icon="delete"
                        color={'error.main'}
                        fontSize={iconSize ?? '30px'}
                        onClick={handleOpen}
                    />
                ))}
            {openModal && (
                <RemoveItemModal
                    actionDetails={actionDetails}
                    activeUserEmail={activeUserEmail}
                    routerPush="/"
                    openModal={openModal}
                    handleClose={handleClose}
                    handleAlert={handleAlert}
                    challengeRequest={challengeRequest}
                    isDelete={isDelete}
                    redirectToPage={redirectToPage}
                />
            )}
        </>
    )
}
