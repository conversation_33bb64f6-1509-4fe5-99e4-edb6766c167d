from datetime import date

from pydantic import Field

from clients.core.models import Node


class QualityReliabilityEventResponse(Node):
    """Represents a quality or reliability event with parsed metadata."""

    event_date: date | None = Field(default=None)
    reporting_unit: str | None = Field(default=None)
    report_title: str | None = Field(default=None)
    report_date: date | None = Field(default=None)
    problem_category: str | None = Field(default=None)
    initiator: str | None = Field(default=None)
    procedures_involved: str | None = Field(default=None)
    immediate_action_taken: str | None = Field(default=None)
    batch_number: str | None = Field(default=None)
    equipment: str | None = Field(default=None)
    event_id: str | None = Field(default=None)
    problem_description: str | None = Field(default=None)
