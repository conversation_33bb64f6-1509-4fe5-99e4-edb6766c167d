from typing import Optional

from .queries import GET_REPORTING_SITES

from clients.core.constants import AGGREGATE_LIMIT
from clients.core.filters import eq_filter
from clients.core.models import PaginatedData, ServiceParams
from clients.reporting_site.models import ReportingSiteResult


class ReportingSiteClient:
    def __init__(
        self,
        params: ServiceParams,
    ):
        self._cognite_client = params.cognite_client
        self._data_model_id = params.data_model.as_id()

    def get_reporting_sites(self) -> Optional[PaginatedData[ReportingSiteResult]]:
        variables = {"filter": eq_filter("isActive", True)}

        try:
            result = self._cognite_client.data_modeling.graphql.query(
                id=self._data_model_id,
                query=GET_REPORTING_SITES,
                variables=variables,
            )

            return PaginatedData[ReportingSiteResult].from_graphql_response(
                result, AGGREGATE_LIMIT
            )
        except Exception:
            return None
