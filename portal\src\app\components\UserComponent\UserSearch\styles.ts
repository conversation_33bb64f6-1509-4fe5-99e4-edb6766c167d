import { Theme } from '@mui/material'
import { CSSObject } from 'styled-components'
export const suggestionsContainter = (theme: Theme): CSSObject => ({
    width: 'calc(43vw - 84px)',
    position: 'absolute',
    backgroundColor: theme.palette.background.paper,
    border: '1px solid',
    borderColor: theme.palette.divider,
    borderRadius: '4px',
    zIndex: 2000,
    maxHeight: 'calc(35vh - 84px)',
    overflowY: 'auto',
    '@media (max-width: 600px)': {
        width: '60vw',
    },
})

export const subHeader: CSSObject = {
    fontSize: '16px',
    fontWeight: 'bold',
    color: 'grey[400]',
    textTransform: 'uppercase',
    justifyContent: 'flex-start',
    justifyItems: 'center',
    alignItems: 'center',
    display: 'flex',
    gap: '1rem',
}

export const subHeaderBox: CSSObject = {
    marginBottom: '10px',
}
