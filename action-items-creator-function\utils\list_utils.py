from collections.abc import Generator, Iterator
from typing import TypeVar

T = TypeVar("T")


def to_list(value: str | list[str]) -> list[str]:
    """
    Convert a string or list of strings to a list of strings.

    Args:
        value (str | list[str]): A single string or a list of strings.

    Returns:
        list[str]: A list containing the input string(s).

    """
    return value if isinstance(value, list) else [value]


def divide_in_chunks(lst: list[T], n: int) -> Iterator[list[T]]:
    """
    Split a list into successive chunks of size `n`.

    Args:
        lst (list[T]): The list to be divided.
        n (int): The size of each chunk.

    Yields:
        Iterator[list[T]]: An iterator over the list chunks, each of size `n`.
            The last chunk may be smaller if the list length is not divisible by `n`.

    """
    for i in range(0, len(lst), n):
        yield lst[i : i + n]


def all_casings(input_string: str, limit: int = 15) -> Iterator[str]:
    """
    Generate up to `limit` distinct casing variations of the input string.

    The original string is always the first variation yielded.
    Variations include lowercase, uppercase, title case, capitalized,
    and all possible combinations of upper and lower case for each character,
    stopping when the total number of unique variations reaches `limit`.

    Args:
        input_string (str): The original string to generate casings from.
        limit (int, optional): The maximum number of unique variations to yield. Defaults to 15.

    Yields:
        Iterator[str]: An iterator over distinct casing variations of the input string.

    """

    def basic_variations(input_string: str) -> Generator:
        yield input_string
        yield input_string.lower()
        yield input_string.upper()
        yield input_string.title()
        yield input_string.capitalize()

    seen = set()

    for variation in basic_variations(input_string):
        if len(seen) < limit and variation not in seen:
            seen.add(variation)
            yield variation

    def generate_casings(input_string: str, current_version: str) -> Generator:
        if len(seen) >= limit:
            return

        if not input_string:
            if current_version not in seen:
                seen.add(current_version)
                yield current_version
        else:
            first = input_string[:1]
            if first.lower() == first.upper():
                yield from generate_casings(input_string[1:], current_version + first)
            else:
                yield from generate_casings(
                    input_string[1:],
                    current_version + first.lower(),
                )
                if len(seen) < limit:
                    yield from generate_casings(
                        input_string[1:],
                        current_version + first.upper(),
                    )

    yield from generate_casings(input_string, "")
