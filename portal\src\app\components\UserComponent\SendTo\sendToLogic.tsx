import { NotificationRole } from '@/app/common/models/notification-role'
import { NotificationSubscribers } from '@/app/common/models/notification-subscribers'
import { UserComplement } from '@/app/common/models/common/user-management/user-complement'
import { Dispatch, SetStateAction, useCallback, useEffect, useMemo, useState } from 'react'
import { NotificationTeam } from '@/app/common/models/notification-team'
import { NotificationUnit } from '@/app/common/models/notification-unit'
import { SubmitHandler, useForm } from 'react-hook-form'
import { z } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'
import { ReportingLocation } from '@/app/common/models/common/asset-hierarchy/reporting-location'
import { LocationQueryRequest, useReportingLocations } from '@/app/common/hooks/asset-hierarchy/useReportingLocations'
import { UserRoleSiteQueryRequest, useUserRoleSites } from '@/app/common/hooks/user-management/useUserRoleSites'
import { TeamQueryRequest, useTeams, useTeamSearch } from '@/app/common/hooks/user-management/useTeams'
import { UserSearchRequest, useUserSearch } from '@/app/common/hooks/user-management/useUsers'
import { UserComplementQueryRequest, useUserComplement } from '@/app/common/hooks/user-management/useUserComplements'
import { RoleSearchRequest, useRoleSearch } from '@/app/common/hooks/user-management/useRoles'
import { SiteQueryRequest, useReportingSites } from '@/app/common/hooks/asset-hierarchy/useReportingSites'
import useDebounce from '@/app/common/hooks/general-functions/useDebounce'
import { AzureFunctionClient } from '@/app/common/clients/azure-function-client'

type FormSchema = z.infer<typeof formSchema>

const formSchema = z.object({
    team: z.array(z.string()),
    role: z.array(z.string()),
    unit: z.array(z.string()),
    reportingLocation: z.array(z.string()),
})

export function useSendToDrawerLogic(
    siteId: string,
    selectedSubscribers: NotificationSubscribers,
    setSelectedSubscribers: Dispatch<SetStateAction<NotificationSubscribers>>
) {
    const [searchValue, setSearchValue] = useState('')
    const [roles, setRoles] = useState([] as NotificationRole[])
    const [users, setUsers] = useState([] as UserComplement[])
    const [teams, setTeams] = useState([] as NotificationTeam[])

    const [usersAdvancedSearch, setUsersAdvancedSearch] = useState([] as UserComplement[])
    const [usersByUnit, setUsersByUnit] = useState([] as UserComplement[])
    const [usersByLocation, setUsersByLocation] = useState([] as UserComplement[])
    const [usersByRole, setUsersByRole] = useState([] as UserComplement[])
    const [usersByTeam, setUsersByTeam] = useState([] as UserComplement[])
    const [locationsByUnit, setLocationsByUnit] = useState([] as ReportingLocation[])

    const [selectedRoles, setSelectedRoles] = useState(selectedSubscribers?.roles ?? [])
    const [selectedTeams, setSelectedTeams] = useState(selectedSubscribers?.teams ?? [])
    const [selectedUsers, setSelectedUsers] = useState(selectedSubscribers?.users ?? [])

    const [openPopper, setOpenPopper] = useState(false)
    const [isLoader, setIsLoader] = useState(false)
    const [anchorEl, setAnchorEl] = useState(null)

    const handleFocus = (event: any) => {
        setAnchorEl(event.currentTarget)
        setOpenPopper(true)
    }

    const handleChange = (event: any) => {
        setSearchValue(event.target.value)
    }

    const handleBlur = () => {
        setTimeout(() => {
            setOpenPopper(false)
        }, 500)
    }

    const { control, handleSubmit, reset } = useForm<FormSchema>({
        resolver: zodResolver(formSchema),
        defaultValues: {
            team: [],
            role: [],
            unit: [],
            reportingLocation: [],
        },
    })

    const handleResetStates = () => {
        setRoles([] as NotificationRole[])
        setTeams([] as NotificationTeam[])
        setUsers([] as UserComplement[])
    }

    //SEARCH
    const debouncedSearchValue = useDebounce(searchValue, 500)

    //Users
    const { searchResult: userSearchResult, loading: isUserSearchLoading } = useUserSearch(
        useMemo<UserSearchRequest>(() => {
            const searchParams: UserSearchRequest = {
                search: debouncedSearchValue,
            }

            return searchParams
        }, [debouncedSearchValue])
    )

    const { users: searchUsers, loading: isUsersComplementLoading } = useUserComplement(
        useMemo<UserComplementQueryRequest>(() => {
            const queryParams: UserComplementQueryRequest = {
                userIds: userSearchResult,
            }

            return queryParams
        }, [userSearchResult])
    )

    useEffect(() => {
        setUsers(searchUsers)
    }, [searchUsers])

    //Roles
    const { searchResult: roleSearchResult, loading: isRoleSearchLoading } = useRoleSearch(
        useMemo<RoleSearchRequest>(() => {
            const searchParams: RoleSearchRequest = {
                search: debouncedSearchValue,
            }

            return searchParams
        }, [debouncedSearchValue])
    )

    const { userRoleSites: searchUserRoleSites, loading: isRolesLoading } = useUserRoleSites(
        useMemo<UserRoleSiteQueryRequest>(() => {
            const queryParams: UserRoleSiteQueryRequest = {
                siteId,
                roleIds: roleSearchResult,
            }

            return queryParams
        }, [roleSearchResult])
    )

    const notificationRolesFromSearch = useMemo(() => {
        const groupedUsers = groupBy(searchUserRoleSites, 'roleId')
        const notificationRoles = Object.keys(groupedUsers).map((key) => {
            let roleUsers: UserComplement[] = []
            groupedUsers[key].map((item: any) => {
                const usersByRole = item?.usersComplements?.map((userComplement: any) => {
                    const user: UserComplement = {
                        externalId: userComplement.externalId,
                        space: userComplement.space,
                        email: userComplement.email,
                        firstName: userComplement.firstName,
                        lastName: userComplement.lastName,
                        name: `${userComplement.lastName}, ${userComplement.firstName}`,
                    }
                    return user
                })
                roleUsers = [...roleUsers, ...usersByRole]
            })

            const role: NotificationRole = {
                externalId: groupedUsers[key][0].roleId,
                name: groupedUsers[key][0].name,
                users: roleUsers,
            }

            return role
        })

        return notificationRoles.slice().sort((a, b) => a.name?.localeCompare(b.name))
    }, [searchUserRoleSites])

    useEffect(() => {
        setRoles(notificationRolesFromSearch)
    }, [notificationRolesFromSearch])

    //Teams
    const { searchResult: teamSearchResult, loading: isTeamSearchLoading } = useTeamSearch(
        useMemo<UserSearchRequest>(() => {
            const searchParams: UserSearchRequest = {
                search: debouncedSearchValue,
            }

            return searchParams
        }, [debouncedSearchValue])
    )

    const { teams: searchTeams, loading: isTeamsLoading } = useTeams(
        useMemo<TeamQueryRequest>(() => {
            const queryParams: TeamQueryRequest = {
                siteId,
                teamIds: teamSearchResult,
            }

            return queryParams
        }, [teamSearchResult])
    )

    const notificationTeamsFromSearch = useMemo(() => {
        const teams = searchTeams.map((team) => {
            const notificationTeam: NotificationTeam = {
                externalId: team.externalId,
                space: team.space,
                name: team.name,
                users: team.members
                    ? team.members.map((member: any) => {
                          const user: UserComplement = {
                              externalId: member.externalId,
                              space: member.space,
                              email: member.email,
                              firstName: member.firstName,
                              lastName: member.lastName,
                              name: `${member.lastName}, ${member.firstName}`,
                          }
                          return user
                      })
                    : [],
            }
            return notificationTeam
        })
        return teams.slice().sort((a, b) => a.name.localeCompare(b.name))
    }, [searchTeams])

    useEffect(() => {
        setTeams(notificationTeamsFromSearch)
    }, [notificationTeamsFromSearch])

    const [selectedTeam, setSelectedTeam] = useState<NotificationTeam>()
    const { users: teamUserComplements } = useUserComplement(
        useMemo<UserComplementQueryRequest>(() => {
            const userIds = selectedTeam?.users?.map((user) => user.externalId) || []
            return { userIds }
        }, [selectedTeam])
    )

    useEffect(() => {
        if (!selectedTeam) return

        const updatedTeam = {
            ...selectedTeam,
            users: teamUserComplements,
        }
        setSelectedTeams([...selectedTeams.filter((team) => team.externalId !== selectedTeam.externalId), updatedTeam])
        setSelectedSubscribers((prev: NotificationSubscribers) => ({
            ...prev,
            teams: [...prev.teams.filter((team) => team.externalId !== selectedTeam.externalId), updatedTeam],
        }))
    }, [teamUserComplements])

    useEffect(() => {
        setIsLoader(
            isUserSearchLoading ||
                isUsersComplementLoading ||
                isRoleSearchLoading ||
                isRolesLoading ||
                isTeamSearchLoading ||
                isTeamsLoading
        )
    }, [
        isUserSearchLoading,
        isUsersComplementLoading,
        isRoleSearchLoading,
        isRolesLoading,
        isTeamSearchLoading,
        isTeamsLoading,
    ])

    //ADVANCED SEARCH

    //Roles
    const { userRoleSites: userRoleSitesData } = useUserRoleSites({ siteId })

    const notificationRolesData = useMemo(() => {
        const groupedUsers = groupBy(userRoleSitesData, 'roleId')
        const roles = Object.keys(groupedUsers).map((key) => {
            let roleUsers: UserComplement[] = []
            groupedUsers[key].map((item: any) => {
                const usersByRole = item?.usersComplements?.map((userComplement: any) => {
                    const user: UserComplement = {
                        externalId: userComplement.externalId,
                        space: userComplement.space,
                        email: userComplement.email,
                        firstName: userComplement.firstName,
                        lastName: userComplement.lastName,
                        name: `${userComplement.lastName}, ${userComplement.firstName}`,
                    }

                    return user
                })

                roleUsers = [...roleUsers, ...usersByRole]
            })

            const notificationRole: NotificationRole = {
                externalId: groupedUsers[key][0].roleId,
                name: groupedUsers[key][0].name,
                users: roleUsers,
            }

            return notificationRole
        })

        return roles.slice().sort((a, b) => a.name?.localeCompare(b.name))
    }, [userRoleSitesData])

    const [selectedRolesInAdvancedSearch, setSelectedRolesInAdvancedSearch] = useState<NotificationRole[]>([])

    useEffect(() => {
        const rolesUserComplementsInAdvancedSearch = selectedRolesInAdvancedSearch.flatMap(
            (role) => role.users?.map((user) => user)
        )

        const uniqueUserMap = new Map()
        rolesUserComplementsInAdvancedSearch.forEach((user) => {
            if (user?.externalId) {
                uniqueUserMap.set(user.externalId, user)
            }
        })

        const uniqueUsers = Array.from(uniqueUserMap.values())

        setUsersByRole(uniqueUsers)
        filteredUsersByAdvancedSearch(usersByTeam, uniqueUsers, usersByUnit, usersByLocation)
    }, [selectedRolesInAdvancedSearch])

    //Teams
    const { teams: teamsData } = useTeams({ siteId })

    const notificationTeamsData = useMemo(() => {
        const notificationTeams = teamsData.map((team) => {
            const notificationTeam: NotificationTeam = {
                externalId: team.externalId,
                space: team.space,
                name: team.name,
                users: team.members
                    ? team.members.map((member: any) => {
                          const user: UserComplement = {
                              externalId: member.externalId,
                              space: member.space,
                              email: member.email,
                              firstName: member.firstName,
                              lastName: member.lastName,
                              name: `${member.lastName}, ${member.firstName}`,
                          }
                          return user
                      })
                    : [],
            }
            return notificationTeam
        })
        return notificationTeams.slice().sort((a, b) => a.name.localeCompare(b.name))
    }, [teamsData])

    const [selectedTeamsInAdvancedSearch, setSelectedTeamsInAdvancedSearch] = useState<NotificationTeam[]>([])

    const { users: teamsUserComplementsInAdvancedSearch } = useUserComplement(
        useMemo<UserComplementQueryRequest>(() => {
            const userIds = selectedTeamsInAdvancedSearch.flatMap(
                (team) => team.users?.map((user) => user.externalId) || ['-']
            )
            const uniqueUserIds = Array.from(new Set(userIds))
            return { userIds: uniqueUserIds }
        }, [selectedTeamsInAdvancedSearch])
    )

    useEffect(() => {
        setUsersByTeam(teamsUserComplementsInAdvancedSearch)
        filteredUsersByAdvancedSearch(teamsUserComplementsInAdvancedSearch, usersByRole, usersByUnit, usersByLocation)
    }, [teamsUserComplementsInAdvancedSearch])

    //Units
    const { sites } = useReportingSites(
        useMemo<SiteQueryRequest>(() => {
            return { siteId }
        }, [siteId])
    )

    const unitsData = useMemo(() => {
        if (sites?.length > 0) {
            return sites[0].reportingUnits
        }

        return []
    }, [sites])

    const { locations } = useReportingLocations(
        useMemo(() => {
            const request: LocationQueryRequest = {
                siteId: siteId,
                unitIds: unitsData?.map((unit) => unit.externalId),
                onlyActive: true,
            }
            return request
        }, [unitsData])
    )

    const locationsData: ReportingLocation[] = useMemo(() => {
        if (unitsData?.length === 0) {
            return []
        }

        return locations || []
    }, [locations])

    const notificationUnitsData = useMemo(() => {
        const notificationUnits = unitsData?.map((unit) => {
            const notificationUnit: NotificationUnit = {
                externalId: unit.externalId,
                space: unit.space,
                name: unit.name,
                description: unit.description,
                locations: locationsData?.filter((location) => location?.reportingUnit?.externalId == unit.externalId),
            }

            return notificationUnit
        })

        return notificationUnits?.slice().sort((a, b) => a.name.localeCompare(b.name))
    }, [unitsData, locationsData])

    const [selectedUnitsInAdvancedSearch, setSelectedUnitsInAdvancedSearch] = useState<NotificationUnit[]>([])
    const [unitsUserComplementsInAdvancedSearch, setUnitsUserComplementsInAdvancedSearch] = useState<UserComplement[]>(
        []
    )

    const fecthUserComplementsByUnits = useCallback(async () => {
        if (!selectedUnitsInAdvancedSearch?.length) {
            setUnitsUserComplementsInAdvancedSearch([])
            return
        }

        try {
            const client = new AzureFunctionClient()

            const result = await client.getUserComplementsByUnits({
                reportingUnitsIds: selectedUnitsInAdvancedSearch.map((unit) => unit.externalId),
            })

            setUnitsUserComplementsInAdvancedSearch(result)
        } catch (err) {}
    }, [selectedUnitsInAdvancedSearch])

    useEffect(() => {
        fecthUserComplementsByUnits()
    }, [fecthUserComplementsByUnits])

    useEffect(() => {
        setUsersByUnit(unitsUserComplementsInAdvancedSearch)
        const locations = selectedUnitsInAdvancedSearch.flatMap((unit) => unit.locations?.map((location) => location))

        const uniqueLocationMap = new Map()
        locations.forEach((location) => {
            if (location?.externalId) {
                uniqueLocationMap.set(location.externalId, location)
            }
        })

        const uniqueLocations = Array.from(uniqueLocationMap.values())
        setLocationsByUnit(uniqueLocations)

        const unitIds = selectedUnitsInAdvancedSearch.map((unit) => unit.externalId)
        setSelectedLocationsInAdvancedSearch((prevValue) =>
            prevValue.filter((location) => unitIds.includes(location.reportingUnit.externalId))
        )

        filteredUsersByAdvancedSearch(usersByTeam, usersByRole, unitsUserComplementsInAdvancedSearch, usersByLocation)
    }, [unitsUserComplementsInAdvancedSearch])

    //Locations
    const [selectedLocationsInAdvancedSearch, setSelectedLocationsInAdvancedSearch] = useState<ReportingLocation[]>([])
    const [locationsUserComplementsInAdvancedSearch, setLocationsUserComplementsInAdvancedSearch] = useState<
        UserComplement[]
    >([])

    const fecthUserComplementsByLocations = useCallback(async () => {
        if (!selectedLocationsInAdvancedSearch?.length) {
            setLocationsUserComplementsInAdvancedSearch([])
            return
        }

        try {
            const client = new AzureFunctionClient()

            const result = await client.getUserComplementsByLocations({
                reportingLocationsIds: selectedLocationsInAdvancedSearch.map((location) => location.externalId),
            })

            setLocationsUserComplementsInAdvancedSearch(result)
        } catch (err) {}
    }, [selectedLocationsInAdvancedSearch])

    useEffect(() => {
        fecthUserComplementsByLocations()
    }, [fecthUserComplementsByLocations])

    useEffect(() => {
        setUsersByLocation(locationsUserComplementsInAdvancedSearch)
        filteredUsersByAdvancedSearch(usersByTeam, usersByRole, usersByUnit, locationsUserComplementsInAdvancedSearch)
    }, [locationsUserComplementsInAdvancedSearch])

    const [openDrawer, setOpenDrawer] = useState<boolean>(false)
    const handleOpenDrawer = (open: boolean) => {
        setOpenDrawer(open)
    }

    const handleRoleAction = (externalId: string, isExpand: boolean, roleUsers?: UserComplement[]) => {
        const rolesFiltered = selectedRoles.filter((role) => role.externalId !== externalId)
        setSelectedRoles(rolesFiltered)

        if (roleUsers && isExpand) {
            const displayedExternalIds = new Set()
            const newSelectedUsers = roleUsers.filter((user) => {
                if (!displayedExternalIds.has(user.externalId)) {
                    displayedExternalIds.add(user.externalId)
                    const existsInSelectedUsers = selectedUsers.some(
                        (selectedUser) => selectedUser.externalId === user.externalId
                    )
                    return !existsInSelectedUsers
                }
                return false
            })
            setSelectedUsers((prevSelectedUsers) => [...prevSelectedUsers, ...newSelectedUsers])
        }
    }

    const handleTeamAction = (externalId: string, isExpand: boolean, teamUsers?: UserComplement[]) => {
        const teamsFiltered = selectedTeams.filter((role) => role.externalId != externalId)
        setSelectedTeams(teamsFiltered)

        if (teamUsers && isExpand) {
            const displayedExternalIds = new Set()
            const newSelectedUsers = teamUsers.filter((user) => {
                if (!displayedExternalIds.has(user.externalId)) {
                    displayedExternalIds.add(user.externalId)
                    const existsInSelectedUsers = selectedUsers.some(
                        (selectedUser) => selectedUser.externalId === user.externalId
                    )
                    return !existsInSelectedUsers
                }
                return false
            })
            setSelectedUsers((prevSelectedUsers) => [...prevSelectedUsers, ...newSelectedUsers])
        }
    }

    const handleRemoveUser = (externalId: string) => {
        const filteredUsers = selectedUsers.filter((role) => role.externalId != externalId)
        setSelectedUsers(filteredUsers)
    }

    const handleRemoveUserSearch = (externalId: string) => {
        const filteredUsers = usersAdvancedSearch.filter((role) => role.externalId != externalId)
        setUsersAdvancedSearch(filteredUsers)
    }

    const clearAllAdvancedSearch = () => {
        setUsersAdvancedSearch([])
        setUsersByUnit([])
        setUsersByRole([])
        setUsersByTeam([])
        setUsersByLocation([])
        reset({
            team: [],
            role: [],
            unit: [],
            reportingLocation: [],
        })
    }

    const filteredUsersByAdvancedSearch = (
        usersTeam: UserComplement[],
        usersRole: UserComplement[],
        usersUnit: UserComplement[],
        usersLocation: UserComplement[]
    ) => {
        const filters = [
            { users: usersTeam, validate: selectedTeamsInAdvancedSearch.length > 0 },
            { users: usersRole, validate: selectedRolesInAdvancedSearch.length > 0 },
            { users: usersUnit, validate: selectedUnitsInAdvancedSearch.length > 0 },
            { users: usersLocation, validate: selectedLocationsInAdvancedSearch.length > 0 },
        ]

        const userIdArrays = filters.map((filter) => filter.users.map((user) => user.externalId))

        if (filters.some((filter, i) => filter.validate && userIdArrays[i].length === 0)) {
            setUsersAdvancedSearch([])
            return
        }

        const intersectedUserIds = filters.reduce(
            (acc, filter, i) => {
                if (filter.validate) {
                    return acc.filter((id) => userIdArrays[i].includes(id))
                }
                return acc
            },
            Array.from(new Set(userIdArrays.flat()))
        )

        if (intersectedUserIds.length === 0) {
            setUsersAdvancedSearch([])
            return
        }

        const allUsers = [...usersTeam, ...usersRole, ...usersUnit, ...usersLocation]
        const intersectedUsers = allUsers.filter((user) => intersectedUserIds.includes(user.externalId))

        const uniqueUsers = Array.from(new Map(intersectedUsers.map((user) => [user.externalId, user])).values())

        setUsersAdvancedSearch(uniqueUsers)
    }

    const submitFn: SubmitHandler<FormSchema> = async (data) => {
        const existingExternalIds = new Set(selectedUsers.map((user) => user.externalId))

        const newUsers = usersAdvancedSearch.filter((user) => !existingExternalIds.has(user.externalId))

        setSelectedUsers((prevSelectedUsers) => [...prevSelectedUsers, ...newUsers])
        setOpenDrawer(false)
        setSelectedSubscribers((prev: NotificationSubscribers) => ({
            ...prev,
            users: [...selectedUsers, ...newUsers],
        }))
    }

    return {
        handleChange,
        searchValue,
        handleResetStates,
        setSearchValue,
        roles,
        setSelectedRoles,
        selectedRoles,
        teams,
        setSelectedTeams,
        selectedTeams,
        users,
        setSelectedUsers,
        selectedUsers,
        locationsByUnit,
        handleRoleAction,
        handleTeamAction,
        handleRemoveUser,
        submitFn,
        openDrawer,
        handleOpenDrawer,
        control,
        handleSubmit,
        notificationTeamsData,
        notificationRolesData,
        notificationUnitsData,
        usersAdvancedSearch,
        handleRemoveUserSearch,
        clearAllAdvancedSearch,
        handleFocus,
        handleBlur,
        setAnchorEl,
        anchorEl,
        openPopper,
        isLoader,
        setSelectedTeam,
        setSelectedTeamsInAdvancedSearch,
        setSelectedRolesInAdvancedSearch,
        setSelectedUnitsInAdvancedSearch,
        setSelectedLocationsInAdvancedSearch,
    }
}

function groupBy(xs: any, key: any) {
    return xs.reduce(function (rv: any, x: any) {
        ;(rv[x[key]] = rv[x[key]] || []).push(x)
        return rv
    }, {})
}
