import { ExternalEntity } from '../../common'

export interface ICAPMOOCReport extends ExternalEntity {
    event: Event
    newEmployee?: User
    positionImpacted?: string
}

interface Event extends ExternalEntity {
    owner?: User
    createdTime?: string
    reportingUnit?: ReportingUnit
    description: string
    name?: string
    status?: string
    number: string
    businessLine?: string
}

interface User extends ExternalEntity {
    firstName: string
    lastName: string
}

interface ReportingUnit extends ExternalEntity {
    description: string
}
