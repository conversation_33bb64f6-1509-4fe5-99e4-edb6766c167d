# Manifest file downloaded from fusion
externalId: tr-AIM-COR-ALL-RAW-ICAP-VAL-EVENT-MIGRATION-PROGRESS
name: AIM-COR-ALL-RAW-ICAP-VAL-EVENT-MIGRATION-PROGRESS
query: >
  SELECT
    	icap_event.key,
    	icap_event.key AS `1_EventID`,
    	icap_event.EventTitle AS `2_EventTitle`,
    	icap_event.EventDescription AS `3_EventDescription`,
      icap_user_owner.Email AS `4_EventPrimaryOwnerEmail`,
    	icap_grouped_secondary_owner.secondary_owners AS `5_EventSecondaryOwnerEmails`,
    	icap_event_category.EventCategoryName AS `6_EventCategory`,
    	icap_event_status.EventStatusName AS `7_EventStatus`,
    	DATE(icap_event.StartDate) AS `8_EventStartDate`,
    	DATE(icap_event.DueDate) AS `9_EventDueDate`,
    	icap_unit.UnitName AS `10_AreaName`,
    	isnotnull(aim_event.externalId) AS `11_isMigrated`,
    	icap_event.Private AS `12_isPrivate`,
    	icap_event.EventCategoryID IN (5, 9, 15, 21, 27) AS `13_isFromAnotherModule`,
    	if(
    		icap_event.EventCategoryID IN (5, 9, 15, 21, 27),
    		NULL,
    		concat(
            if(isnull(stg_event.category), 'Missing Category; ', ''),
            if(isnull(stg_event.subCategory), 'Missing Sub Category 1; ', ''),
            if(isnull(stg_event.owner), 'Missing Owner; ', ''),
            if(isnull(stg_event.createdBy), 'Missing Created By; ', ''),
            if(isnull(stg_event.status), 'Missing Status; ', ''),
            if(isnull(stg_event.reportingUnit), 'Missing Reporting Unit - Invalid Area Mapping; ', ''),
            if(
                isnotnull(stg_event.reportingSite) 
                AND isnotnull(stg_event.reportingUnit)
                AND NOT startswith(
                  stg_event.reportingUnit.externalId,
                  replace(stg_event.reportingSite.externalId, 'STS-', 'UNT-')
                ), 
                'Reporting Unit from another Site; ', 
                ''
            ),
            if(
                isnotnull(stg_event.reportingSite) 
                AND isnotnull(stg_event.reportingLocation)
                AND NOT startswith(
                  stg_event.reportingLocation.externalId,
                  replace(stg_event.reportingSite.externalId, 'STS-', 'LOC-')
                ), 
                'Reporting Location from another Site; ', 
                ''
            )
  		)
      ) AS `14_errorDetails`,
      aim_event.externalId AS `15_[AIM] Event ExternalId`,
    	aim_event.space AS `16_[AIM] Event Space`,
    	aim_event.title AS `17_[AIM] Event Title`,
    	aim_event.description AS `18_[AIM] Event Description`,
    	replace(aim_event.owner.externalId, 'UserAzureAttribute_', '') AS `19_[AIM] Event Primary Owner`,
    	aim_grouped_secondary_owner.secondary_owners AS `20_[AIM] Event Secondary Owners`,
    	aim_event_status.name AS `21_[AIM] Event Status`,
      aim_event_category.description AS `22_[AIM] Event Category`,
    	aim_event_sub_category.description AS `23_[AIM] Event Sub Category 1`,
    	aim_event_site_category.description AS `24_[AIM] Event Sub Category 2`,
    	aim_event.assignmentDate AS `25_[AIM] Event Start Date`,
    	aim_event.dueDate AS `26_[AIM] Due Date`,
      if(
    		isnotnull(ah_reporting_unit.externalId),
    		concat(ah_reporting_unit.description, ' (', ah_reporting_unit.name, ')'),
    		''
    	) AS `27_[AIM] Reporting Unit`,
      if(
    		isnotnull(ah_reporting_location.externalId),
    		concat(ah_reporting_location.description, ' (', ah_reporting_location.name, ')'),
    		''
    	) AS `28_[AIM] Reporting Location`,
    	utl_reporting_site.ah_reporting_site.externalId AS reporting_site
  FROM `ICAP-COR`.`EVNT-tblEvent` icap_event

  INNER JOIN `AIM-COR`.`ICAP-MAP-ReportingSite` utl_reporting_site
    	ON utl_reporting_site.key = cast(icap_event.SiteID AS STRING)
  LEFT JOIN `ICAP-COR`.`USR-tblUser` icap_user_owner
  	ON icap_user_owner.key = cast(icap_event.EventAddedByOwner AS STRING)
  LEFT JOIN (
    SELECT 
    	icap_secondary_owner.EventID, 
    	array_join(array_distinct(array_agg(icap_user_secondary_owner.Email)), ", ") AS secondary_owners
    FROM `ICAP-COR`.`EVNT-tblEventSecondaryOwners` icap_secondary_owner
    INNER JOIN `ICAP-COR`.`USR-tblUser` icap_user_secondary_owner
    	ON cast(icap_secondary_owner.OwnerID AS STRING) = icap_user_secondary_owner.key
    GROUP BY icap_secondary_owner.EventID
  ) icap_grouped_secondary_owner
    	ON icap_event.key = cast(icap_grouped_secondary_owner.EventID AS STRING)
  LEFT JOIN `ICAP-COR`.`EVNT-tblEventCategory` icap_event_category
  	ON icap_event_category.key = cast(icap_event.EventCategoryID AS STRING)
  LEFT JOIN `ICAP-COR`.`EVNT-tblEventStatus` icap_event_status
    	ON icap_event_status.key = cast(icap_event.EventStatusID AS STRING)
  LEFT JOIN `ICAP-COR`.`UNT-tblUnit` icap_unit
    	ON icap_unit.key = cast(icap_event.UnitID AS STRING)
  LEFT JOIN `AIM-COR`.`ICAP-STG-Event` stg_event
    	ON stg_event.key = icap_event.key
  LEFT JOIN cdf_data_models("AIM-COR-ALL-DMD", "ActionItemManagementDOM", "6_0_0", "SourceEvent") aim_event
  	ON stg_event.externalId = aim_event.externalId
  	AND stg_event.space = aim_event.space
  LEFT JOIN (
    SELECT 
    	aim_event_to_secondary_owners.startNode.externalId, 
    	aim_event_to_secondary_owners.startNode.space,
    	array_join(array_distinct(array_agg(replace(aim_event_to_secondary_owners.endNode.externalId, 'UserAzureAttribute_', ''))), ", ") AS secondary_owners
    FROM cdf_data_models("AIM-COR-ALL-DMD", "ActionItemManagementDOM", "6_0_0", "SourceEvent", "secondaryOwnerUsers") aim_event_to_secondary_owners
    GROUP BY aim_event_to_secondary_owners.startNode.externalId, aim_event_to_secondary_owners.startNode.space
  ) aim_grouped_secondary_owner
    	ON aim_grouped_secondary_owner.externalId = aim_event.externalId
    	AND aim_grouped_secondary_owner.space = aim_event.space
  LEFT JOIN cdf_data_models("AIM-COR-ALL-DMD", "ActionItemManagementDOM", "6_0_0", "SourceEventStatus") aim_event_status
  	ON aim_event.status.externalId = aim_event_status.externalId
  	AND aim_event.status.space = aim_event_status.space
  LEFT JOIN cdf_data_models("AIM-COR-ALL-DMD", "ActionItemManagementDOM", "6_0_0", "ActionItemCategory") aim_event_category
  	ON aim_event.category.externalId = aim_event_category.externalId
  	AND aim_event.category.space = aim_event_category.space
  LEFT JOIN cdf_data_models("AIM-COR-ALL-DMD", "ActionItemManagementDOM", "6_0_0", "ActionItemSubCategory") aim_event_sub_category
  	ON aim_event.subCategory.externalId = aim_event_sub_category.externalId
  	AND aim_event.subCategory.space = aim_event_sub_category.space
  LEFT JOIN cdf_data_models("AIM-COR-ALL-DMD", "ActionItemManagementDOM", "6_0_0", "SiteSpecificCategory") aim_event_site_category
  	ON aim_event.siteSpecificCategory.externalId = aim_event_site_category.externalId
  	AND aim_event.siteSpecificCategory.space = aim_event_site_category.space
  LEFT JOIN cdf_data_models("AIM-COR-ALL-DMD", "ActionItemManagementDOM", "6_0_0", "ReportingUnit") ah_reporting_unit
  	ON aim_event.reportingUnit.externalId = ah_reporting_unit.externalId
  	AND aim_event.reportingUnit.space = ah_reporting_unit.space
  LEFT JOIN cdf_data_models("AIM-COR-ALL-DMD", "ActionItemManagementDOM", "6_0_0", "ReportingLocation") ah_reporting_location
  	ON aim_event.reportingLocation.externalId = ah_reporting_location.externalId
  	AND aim_event.reportingLocation.space = ah_reporting_location.space
destination:
  database: AIM-COR
  table: ICAP-VAL-EventMigrationProgress
  type: raw
ignoreNullFields: true
shared: true
action: upsert
dataSetExternalId: AIM-COR-ALL-DAT
# Specify credentials separately like this:
# authentication:
#   read:
#     clientId: ${READ_CLIENT_ID}
#     clientSecret: ${READ_CLIENT_SECRET}
#     tokenUrl: ${READ_TOKEN_URL}
#     cdfProjectName: ${READ_CDF_PROJECT_NAME}
#     # Optional: If idP requires providing the scopes
#     scopes:
#       - ${READ_SCOPES}
#     # Optional: If idP requires providing the audience
#     audience: ${READ_CDF_AUDIENCE}
#   write:
#     clientId: ${WRITE_CLIENT_ID}
#     clientSecret: ${WRITE_CLIENT_SECRET}
#     tokenUrl: ${WRITE_TOKEN_URL}
#     cdfProjectName: ${WRITE_CDF_PROJECT_NAME}
#     # Optional: If idP requires providing the scopes
#     scopes:
#       - ${WRITE_SCOPES}
#     # Optional: If idP requires providing the audience
#     audience: ${WRITE_CDF_AUDIENCE}
# Or together like this:
# authentication:
#   clientId: ${CLIENT_ID}
#   clientSecret: ${CLIENT_SECRET}
#   tokenUrl: ${TOKEN_URL}
#   # Optional: If idP requires providing the scopes
#   cdfProjectName: ${CDF_PROJECT_NAME}
#   scopes:
#    - ${SCOPES}
#   # Optional: If idP requires providing the audience
#   audience: ${AUDIENCE}