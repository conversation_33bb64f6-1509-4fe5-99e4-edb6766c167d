import { Dispatch, SetStateAction, useState } from 'react'
import { useRouter } from 'next/navigation'
import MessageModal from '../ModalComponent/Modal/MessageModal'
import { getLocalUserSite } from '@celanese/celanese-ui'
import { MenuItems } from './MenuItems'
import { translate } from '@/app/common/utils/generate-translate'
import { useLoading } from '@/app/common/contexts/LoadingContext'
import dynamic from 'next/dynamic'
import { UserSite } from '@celanese/celanese-sdk'
import { useMultiSiteEnabled } from '@/app/common/hooks/useMultiSiteEnabled'

interface BaseLayoutProps {
    setLocaleCode: Dispatch<SetStateAction<string>>
    onLangChanged: (localeCode: string) => void
    shouldTranslateDynamicState: {
        shouldTranslateDynamic: boolean | undefined
        setShouldTranslateDynamic: Dispatch<SetStateAction<boolean | undefined>>
    }
    dynamicTranslationLoadingState: {
        dynamicTranslationLoading: boolean
        setDynamicTranslationLoading: Dispatch<SetStateAction<boolean>>
    }
}

const ClnHeaderNavBar = dynamic(() => import('@celanese/celanese-ui').then((mod) => mod.ClnHeaderNavBar), {
    ssr: false,
})

export const HeaderNavBar = ({
    setLocaleCode,
    onLangChanged,
    shouldTranslateDynamicState,
    dynamicTranslationLoadingState,
}: BaseLayoutProps) => {
    const router = useRouter()

    const { showLoading } = useLoading()

    const [isCancelModalOpen, setIsCancelModalOpen] = useState(false)
    const [routeString, setRouteString] = useState('')

    const authorizedMenuItems = MenuItems({ setIsCancelModalOpen, setRouteString, showLoading })

    function clearSpecificSessionStorage(newSiteIds: string[]) {
        const newSiteId = newSiteIds[0]
        const { siteId } = (getLocalUserSite() as UserSite) || {}
        if (newSiteId === siteId) return
        const keysToRemove = [
            'home-filterInfo-toDo-tab',
            'home-filterInfo-closed-tab',
            'dashboard-site-filterInfo-tab',
            'dashboard-filterInfo-supervisor-tab',
            'dashboard-supervisor-filterInfo-tab',
            'event-home-filterInfo',
            'event-details-filterInfo-action-tab',
            'event-details-filterInfo-recurring-tab',
            'admin-recurring-filterInfo',
            'admin-template-filterInfo',
            'admin-category-configuration-filterInfo',
            'admin-site-specific-category-filterInfo',
            'admin-reference-filterInfo',
        ]

        keysToRemove.forEach((key) => sessionStorage.removeItem(key))
    }

    const handleCloseCancelModal = () => {
        setIsCancelModalOpen(false)
    }

    const handleLeaveCancelModal = () => {
        localStorage.removeItem('isEditForm')
        localStorage.removeItem('eventDetails')
        localStorage.removeItem('isCreateNewActions')
        setIsCancelModalOpen(false)
        if (routeString === '/') {
            location.replace('/')
        } else {
            router.push(routeString)
        }
    }

    return (
        <>
            <ClnHeaderNavBar
                iconName={'task'}
                nameBold={'ACTION ITEM'}
                nameLight={'MANAGEMENT'}
                menuItems={authorizedMenuItems}
                shouldTranslateDynamicState={shouldTranslateDynamicState}
                dynamicTranslationLoadingState={dynamicTranslationLoadingState}
                setLocaleCode={setLocaleCode}
                onLangChanged={onLangChanged}
                onSiteChanged={clearSpecificSessionStorage}
                isRouteMultiSite={useMultiSiteEnabled()}
            />
            <MessageModal
                name={''}
                text={translate('requestModal.closeQuestion')}
                open={isCancelModalOpen}
                isCancelModal={true}
                handleClose={handleCloseCancelModal}
                handleLeave={handleLeaveCancelModal}
            />
        </>
    )
}
