from enum import StrEnum


class DataModelSpace(StrEnum):
    """Lists relevant data model spaces."""

    ACTION_ITEM_MANAGEMENT = "AIM-COR-ALL-DMD"
    APPROVAL_WORKFLOW = "APW-COR-ALL-DMD"


class InstanceSpace(StrEnum):
    """Lists relevant instance spaces."""

    ACTION_ITEM_MANAGEMENT_REF = "AIM-COR-ALL-REF"
    USER_MANAGEMENT = "UMG-COR-ALL-DAT"
    ASSET_HIERARCHY = "REF-COR-ALL-DAT"
    APPROVAL_WORKFLOW_REF = "APW-COR-ALL-REF"
