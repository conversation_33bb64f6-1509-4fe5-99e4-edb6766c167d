from cognite.client.data_classes import data_modeling

from clients.core.constants import (
    <PERSON><PERSON><PERSON>,
    USER_COMPLEMENT_PREFIX,
    DataModelSpaceEnum,
    DataSpaceEnum,
    ViewEnum,
)
from clients.core.models import ServiceParams

from .cognite_filters import (
    get_space_filters_for_templates_query,
    get_user_id_filters_for_templates_query,
)
from .requests import GetTemplatesByUserIdRequest


class TemplateClient:
    """
    A client for interacting with action item templates.

    This class provides methods to retrieve templates based on user information and
    filter them using various criteria. It interacts with Cognite's data model for
    retrieving and processing template data.
    """

    def __init__(
        self,
        params: ServiceParams,
    ) -> None:
        """
        Initialize the TemplateClient with service parameters.

        Args:
            params (ServiceParams): The service parameters that provide access
            to the necessary views and services.

        """
        self._cognite_service = params.cognite_service
        self._log = params.logging

        self._template_configuration_view = params.get_views()[
            ViewEnum.TEMPLATE_CONFIGURATION
        ]
        self._user_role_site_view = params.get_views()[ViewEnum.USER_ROLE_SITE]
        self._user_complement_view = params.get_views()[ViewEnum.USER_COMPLEMENT]
        self._user_azure_attribute_view = params.get_views()[
            ViewEnum.USER_AZURE_ATTRIBUTE
        ]

    async def get_templates_by_user_id(
        self,
        request: GetTemplatesByUserIdRequest,
    ) -> list[dict[str, str]]:
        """
        Retrieve templates associated with a user based on their ID.

        Args:
            request (GetTemplatesByUserIdRequest): The request object containing
            the active user's external ID and the space for querying templates.

        Returns:
            list[dict[str, str]]: A list of templates, sorted by name, associated
            with the user.

        """
        user_filters = get_user_id_filters_for_templates_query(request)
        space_filters = get_space_filters_for_templates_query(request.space)
        select_template_clause = self._get_select_template_clause()

        with_clause = {
            "user": data_modeling.query.NodeResultSetExpression(
                filter=data_modeling.filters.And(*user_filters),
            ),
            "user_edge_templateConfiguration": data_modeling.query.EdgeResultSetExpression(
                from_="user",
                filter=data_modeling.filters.Equals(
                    ["edge", "type"],
                    {
                        "space": DataModelSpaceEnum.ACTION_DATA_MODEL_SPACE,
                        "externalId": "TemplateConfiguration.users",
                    },
                ),
                limit=LIMIT,
                direction="inwards",
            ),
            "templateConfiguration_users": data_modeling.query.NodeResultSetExpression(
                from_="user_edge_templateConfiguration",
                filter=space_filters,
                limit=LIMIT,
            ),
            "user_complement_edge_userRoleSite": data_modeling.query.EdgeResultSetExpression(
                filter=data_modeling.filters.And(
                    data_modeling.filters.Equals(
                        ["edge", "type"],
                        {
                            "space": DataModelSpaceEnum.UMG_DATA_MODEL_SPACE,
                            "externalId": "UserComplement.userRoleSite",
                        },
                    ),
                    data_modeling.filters.Equals(
                        ["edge", "startNode"],
                        {
                            "externalId": USER_COMPLEMENT_PREFIX
                            + request.active_user_external_id,
                            "space": DataSpaceEnum.UMG_DATA_SPACE,
                        },
                    ),
                ),
                limit=LIMIT,
            ),
            "userRoleSite": data_modeling.query.NodeResultSetExpression(
                from_="user_complement_edge_userRoleSite",
                limit=LIMIT,
            ),
            "role_in_userRoleSite": data_modeling.query.NodeResultSetExpression(
                from_="userRoleSite",
                through=self._user_role_site_view.as_property_ref("role"),
                limit=LIMIT,
            ),
            "role_edge_templateConfiguration": data_modeling.query.EdgeResultSetExpression(
                from_="role_in_userRoleSite",
                filter=data_modeling.filters.And(
                    data_modeling.filters.Equals(
                        ["edge", "type"],
                        {
                            "space": DataModelSpaceEnum.ACTION_DATA_MODEL_SPACE,
                            "externalId": "TemplateConfiguration.roles",
                        },
                    ),
                ),
                limit=LIMIT,
                direction="inwards",
            ),
            "templateConfiguration_roles": data_modeling.query.NodeResultSetExpression(
                from_="role_edge_templateConfiguration",
                filter=space_filters,
                limit=LIMIT,
            ),
        }

        select_clause_dict = {
            "templateConfiguration_users": select_template_clause,
            "templateConfiguration_roles": select_template_clause,
        }

        query = data_modeling.query.Query(with_=with_clause, select=select_clause_dict)

        try:
            res = await self._cognite_service.get_query_results_list(
                query,
                ViewEnum.TEMPLATE_CONFIGURATION,
            )

        except Exception as e:
            return self.handle_exception("query result", e)

        template_configuration_users = res["templateConfiguration_users"].data
        template_configuration_roles = res["templateConfiguration_roles"].data

        combined_template_configurations = (
            template_configuration_users + template_configuration_roles
        )

        unique_template_configurations = list(
            {v.external_id: v for v in combined_template_configurations}.values(),
        )

        templates = [
            {
                "externalId": config.external_id,
                "name": config.properties[self._template_configuration_view]["name"],
            }
            for config in unique_template_configurations
            if config.properties.get(self._template_configuration_view, {}).get("name")
            is not None
        ]

        return sorted(templates, key=lambda x: x["name"].lower())

    def _get_select_template_clause(self) -> data_modeling.query.Select:
        """
        Construct the SELECT clause for retrieving template properties.

        Returns:
            data_modeling.query.Select: The SELECT clause specifying the desired
            properties for the templates.

        """
        return data_modeling.query.Select(
            sources=[
                data_modeling.query.SourceSelector(
                    source=self._template_configuration_view,
                    properties=["name"],
                ),
            ],
        )

    def handle_exception(self, operation_type: str, e: Exception) -> list:
        """
        Handle exceptions by logging the error and returning an empty list.

        Args:
            operation_type (str): The type of operation that failed.
            e (Exception): The exception raised during the operation.
            logger: The logger instance used to log the error.

        Returns:
            list: An empty list, indicating failure.

        """
        msg = f"The {operation_type} operation failed with error: {e}"
        self._log.error(msg)
        return []
