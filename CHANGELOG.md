# Changelog - Action Item
This project follows the **MAJOR.MINOR.PATCH** versioning scheme:

- 🔺 **MAJOR (1.x.x)**: Changes that break backward compatibility.
- 🔹 **MINOR (x.2.x)**: New features or improvements that do not introduce breaking changes.
- 🔧 **PATCH (x.x.3)**: Bug fixes and minor improvements that do not affect compatibility.

This changelog records all significant changes, bug fixes, and feature updates to track the progress and ensure transparency in the development of the project.

## [4.6.1] - 2025-06-05
### Patch Updates

#### Back-End Changes
- Fix reassignment process for private actions.

## [4.6.0] - 2025-06-05
### Patch Updates

#### Back-End Changes
- Added a new `getSiteSpecificCategoryBySearch` endpoint for dynamic search of site specific category.

#### Front-End Changes
- **SDK-UI version update**: Updated `@celanese/celanese-ui` to version 5.13.2.
- Implemented new data-fetching hooks: `useFetchSearchSiteSpecificCategory`.

## [4.5.4] - 2025-05-30
### Patch Updates

#### Front-End Changes
- Resolved an issue where form fields were incorrectly pre-filled with previous or existing data when creating a new Category Configuration or Subcategory.

## [4.5.3] - 2025-05-30
### Patch Updates

#### Front-End Changes
- Fix back button inconsistency redirection on ActionDetails

## [4.5.2] - 2025-05-30
### Patch Updates

#### Back-End Changes
- Implemented security validation for querying templates by site

#### Front-End Changes
- Fixed duplicate users bug in template

## [4.5.1] - 2025-05-28
### Patch Updates

#### Back-End Changes
- Resolved an issue encountered when editing old events containing files.
- Implemented selective update of newly added files within the dataset during event edits.

## [4.5.0] - 2025-05-28
### Minor Updates

#### Front-End Changes
- `ActionTable`: Added due date validation.
- **SDK-JS version update**: Updated `@celanese/celanese-sdk` to version 6.11.0.
- **SDK-UI version update**: Updated `@celanese/celanese-ui` to version 5.11.0.

## [4.4.1] - 2025-05-27
### Patch Updates

#### Front-End Changes
- Fix `MessageModal` component to prevent text wrapping

## [4.4.0] - 2025-05-27
### Minor Updates

#### Back-End Changes
- Adapted the `event_id` field to accept either a `string` or a `list of strings`.
- Standardized internal handling of `event_id` as `list[str]` for consistency.
- Improved event filter handling to support the new structure and enhance accuracy.
- Created a new `Export Events` endpoint that generates and returns a downloadable Excel file.

#### Front-End Changes
- Updated Events page to support users associated with multiple sites.
- Enhanced event filtering to handle multiple site assignments per user.
- Created and updated the subheader component to support global context and integration with `showing` state.
- Implemented new data-fetching hooks: `useAsyncAutocomplete` and `useDebouncedFetch`.
- Refactored filter value extraction logic into a table abstraction layer.
- Updated edit and details URLs to support both `/SITE_CODE/ID` and `/ID` navigation patterns.

## [4.3.2] - 2025-05-27
### Patch Updates

[Bugfix #183921](https://dev.azure.com/CelaneseCorporation/Digital%20Plant/_boards/board/t/25.%20Action%20Item%20Management%20_%20Thiago%20Wigg%20(APP)/Stories?text=caio&workitem=183921)
[Bugfix #184079](https://dev.azure.com/CelaneseCorporation/Digital%20Plant/_boards/board/t/25.%20Action%20Item%20Management%20_%20Thiago%20Wigg%20(APP)/Stories?text=caio&workitem=184079)
[Style update #164449](https://dev.azure.com/CelaneseCorporation/Digital%20Plant/_boards/board/t/25.%20Action%20Item%20Management%20_%20Thiago%20Wigg%20(APP)/Stories?text=caio&workitem=164449)

#### Front-End Changes
- Responsiveness adjustments on TableOptionsBar and userSearch. (#164449)
- Fixed column order change method for Master Admin. (#184079)
- Fixed bug where template viewers were being removed when deleting another user. (#183921)

## [4.3.1] - 2025-05-26
### Patch Updates

[Bugfix card link](https://dev.azure.com/CelaneseCorporation/Digital%20Plant/_workitems/edit/195595)

#### Back-End Changes
- Associate recurrings to the source event and increment the action counter

## [4.3.0] - 2025-05-22
### Minor Updates
#### Back-End Changes
- Added new library: `industrial-model`
- Adjusted the actions export query to use the abstract format from `industrial-model`

#### Front-End Changes
- Adjusted workflow validations


## [4.2.2] - 2025-05-21
### Patch Updates

#### Front-End Changes
- Add icap in translation for uploadFilesSteps

## [4.2.1] - 2025-05-19
### Patch Updates

[Bugfix card link](https://dev.azure.com/CelaneseCorporation/Digital%20Plant/_workitems/edit/193927/)

#### Back-End Changes
- Fixed an issue where due date updates made by the Master Admin were not reflected in datamodel.

## [4.2.0] - 2025-05-16
### Minor Updates

#### Back-End Changes
- Adapted the `reporting_site_external_id` field to accept either a `string` or a `list of strings`.
- Standardized internal handling of `reporting_site_external_id` as `list[str]` for consistency.
- Updated approval workflow logic to receive a `node` object containing both `externalId` and `space`.
- Improved action filter handling to support the new structure and enhance accuracy.
- Created a new `Export Actions` endpoint that generates and returns a downloadable Excel file.
- Added a new `getUnitsBySearch` endpoint for dynamic search of reporting units.
- Added a new `getLocationsBySearch` endpoint for dynamic search of reporting locations.

#### Front-End Changes
- Updated Home page to support users associated with multiple sites.
- Enhanced site filtering to handle multiple site assignments per user.
- Adjusted KPIs to correctly aggregate data across multiple sites.
- Created and updated the subheader component to support global context and integration with `showing` state.
- Added new translation keys to improve language coverage.
- Implemented new data-fetching hooks: `useFetchSearchLocation` and `useFetchSearchUnit`.
- Replaced all usage of `siteCodes` with `siteId` across the application.
- Renamed constant `SITE_REQUIRED_FIELD` to `SITE_EXTERNAL_ID_REQUIRED_FIELD` for clarity.
- Refactored filter value extraction logic into a table abstraction layer.
- Updated edit and details URLs to support both `/SITECODE/ID` and `/ID` navigation patterns.
- **SDK-JS version update**: Updated `@celanese/celanese-sdk` to version 6.4.1.
- **SDK-UI version update**: Updated `@celanese/celanese-ui` to version 5.5.11.
- **CONTEXTUALIZATION-UI version update**: Updated `@celanese/contextualization-lib` to version 1.22.11.
- **UI-LIB version update**: Updated `@celanese/ui-lib` to version 3.0.0.

## [4.1.3] - 2025-05-16
### Patch Updates

[Bugfix card link](https://dev.azure.com/CelaneseCorporation/Digital%20Plant/_boards/board/t/25.%20Action%20Item%20Management%20_%20Thiago%20Wigg%20(APP)/Stories?System.AssignedTo=%40me&workitem=193789)

#### Front-End Changes
- Added validation to handle cases where `eventAnalysis` is undefined in modal data handling.

#### Back-End Changes
- Removed `EVEN-` prefix from `source_id` if present to prevent errors in RCA Events.

## [4.1.2] - 2025-05-16
### Patch Updates

[Bugfix card link](https://dev.azure.com/CelaneseCorporation/Digital%20Plant/_boards/board/t/25.%20Action%20Item%20Management%20_%20Thiago%20Wigg%20(APP)/Stories?System.AssignedTo=%40me&workitem=192562)

#### Front-End Changes
- Fixed sorting behavior when switching between tabs or KPIs to ensure consistency and accuracy in the displayed data.

## [4.1.1] - 2025-05-16
### Patch Updates

#### Front-End Changes
- Fixed inconsistency in `AuthGuardWrapper`.
- Resolved visual issue where the `HeaderNavBar` overlapped page content after cache was cleared.
- Adjusted `webpack.config` settings.


## [4.1.0] - 2025-05-14
### Minor Updates

#### Front-End Changes
- Improved variable names for the DataGridTable and ActionTable components
- Removed unnecessary parameters
- Adjusted logic for selecting actions to be canceled
- Improved file names and associated components for the tables

#### Back-End Changes
- Removal of the actions by source event id endpoint
- Addition of the not_in_external_ids filter in the actions request
- All logic for canceling associated events and actions is now done on the backend
- Removal of unused code

## [4.0.9] - 2025-05-13
### Patch Updates

[Bugfix card link](https://dev.azure.com/CelaneseCorporation/Digital%20Plant/_boards/board/t/25.%20Action%20Item%20Management%20_%20Thiago%20Wigg%20(APP)/Stories?text=caio&workitem=184079)

#### Front-End Changes
- Fixed inconsistent column reorganization in the `DataGridTable.tsx` component by correcting the `handleColumnOrderChange` logic.
- Improved reliability of table column order persistence and user experience.

## [4.0.8] - 2025-05-13
### Patch Updates

[Bugfix card link](https://dev.azure.com/CelaneseCorporation/Digital%20Plant/_workitems/edit/192498/)

#### Back-End Changes
- Fixed the site-specific category filter for actions.

## [4.0.7] - 2025-05-12
### Patch Updates

[Bugfix card link](https://dev.azure.com/CelaneseCorporation/Digital%20Plant/_workitems/edit/187896)

#### Front-End Changes
- Fixed an issue where uploading a file smaller than 1KB caused its display to overlap with previously added links.
- Updated the `maxWidth` property in `uploadFile.tsx` to properly support files up to 1023 bytes and ensure alignment with the rest of the file name on the same line.

## [4.0.6] - 2025-05-08
### Patch Updates

[Bugfix card link](https://dev.azure.com/CelaneseCorporation/Digital%20Plant/_boards/board/t/25.%20Action%20Item%20Management%20_%20Thiago%20Wigg%20(APP)/Stories?text=caio&workitem=187905)

#### Front-End Changes
- Fixed an issue where during the challenge workflow, selecting the 'Delete' button actually cancels the action instead of deleting it.
- Updated the label of the `RemoveItemButton` in `AssigneeRequestModal.tsx` for clarity.

## [4.0.5] - 2025-05-08
### Patch Updates

[Bugfix card link](https://dev.azure.com/CelaneseCorporation/Digital%20Plant/_boards/board/t/25.%20Action%20Item%20Management%20_%20Thiago%20Wigg%20(APP)/Stories?text=caio&workitem=175137)

#### Front-End Changes
- Fixed an issue where the search tag remained visible in the other tab's table (To Do/Closed) on the homepage even though it was not actually applied.
- Add useEffect to update chipValue on `DataGridTable.tsx`

## [4.0.4] - 2025-05-06
### Patch Updates

#### Front-End Changes
- Unified KPIs and table into a single panel on the home page.
- Default KPI filter fields are now disabled in the home filter.
- An informational message about the selected KPI is now displayed directly below the KPIs.

#### Back-End Changes
- Fixed an issue with the "My Reassignment" KPI calculation.

## [4.0.3] - 2025-05-07
### Patch Updates

#### Front-End Changes
- Fix break MessageModal when file.name is too long in `details\[id]\page.tsx`.

## [4.0.2] - 2025-05-06
### Patch Updates

#### Front-End Changes
- Standardized drawer components to use `CustomDrawer`.
- Set a fixed width for the History drawer to avoid pagination issues in `DataGridPro`.

## [4.0.1] - 2025-06-05
### Patch Updates

#### Front-End Changes
- Fix [letting upload files when an action is completed/cancelled (Fault)](https://dev.azure.com/CelaneseCorporation/Digital%20Plant/_boards/board/t/25.%20Action%20Item%20Management%20_%20Thiago%20Wigg%20(APP)/Stories?text=caio&workitem=190289).
- Fix [when uploading multiple files, only one is shown in the confirmation modal (Fault)](https://dev.azure.com/CelaneseCorporation/Digital%20Plant/_boards/board/t/25.%20Action%20Item%20Management%20_%20Thiago%20Wigg%20(APP)/Stories?text=caio&workitem=190290).
- Update `MessageModal.tsx`.
- Update `en.json - 546 ln`.
- Update `action-item\details\[id]\page.tsx`.

## [4.0.0] - 2025-04-30
### Major Updates

#### Back-End Changes
- Refactored and updated various back-end components.
- `CreateActionCommentRequest` now includes logic to fetch `user_email` and resolve the corresponding `external_id` from the back-end.
- Refactored `template_by_user_id` structure into separate client, server, and controller layers.
- Adjusted `aggregate_service` KPIs to use `_get_user_by_email` for resolving `external_id` from the back-end.
- Updated `event_service` to also use `_get_user_by_email` for external ID resolution.

#### Front-End Changes
- **SDK-JS version update**: Updated `@celanese/celanese-sdk` to version 6.0.0 in both `package.json` and `package-lock.json`.
- **SDK-UI version update**: Updated `@celanese/celanese-ui` to version 5.1.0.
- **UI-LIB version update**: Updated `@celanese/celanese-ui` to version 2.4.1.
- Replaced `AuthGuardContext` with `UserManagementContext` from the SDK and removed the `AuthGuardContext` file.
- Removed internal `getLocalUserSite` and updated calls to use `getLocalUserSite` from the SDK.
- Modified API endpoint calls (`getSourceEventById`, `getHomeKpis`, `getTemplatesByUserId`, `getSourceEvent`, `getActionsGroupedBy`) to send `activeUserEmail` instead of `id`, `roles`, and `teams`.
- Removed components `UserPopover`, `NotificationsBadge`, `NotificationsDrawer`, and `LogOutButton` – these are now handled by the SDK.
- Updated `LayoutHeaderNavBar` to use the corresponding SDK implementation.
## [3.10.0] - 2025-05-05
### Minor Updates

### Back-End Changes
- Add api initial structure and all related pipeline (.yml) files

## [3.9.5] - 2025-04-30
### Patch Updates

#### Back-End Changes
- Returned `isPrivate` field when fetching action by template.
- Fields `sortTitle`, `sortSourceInformation`, and `sortSourceEventTitle` are now saved in lowercase.
- Filters for `sortTitle`, `sortSourceInformation`, and `sortSourceEventTitle` now use `lower`.

#### Front-End Changes
- Created a new comparator for date-type fields in `DataGridPro`.
- Removed `capitalize` from the `title` and `sourceInformation` fields in the event and actions form.

## [3.9.4] - 2025-04-30
### Patch Updates

#### Front-End Changes
- Adjusted the cancel action in the New Action Item form to properly reset the form.

## [3.9.3] - 2025-04-30
### Patch Updates

#### Back-End Changes
  - Adding FRP source type on ActionSourceTypeEnum

## [3.9.2] - 2025-04-29
### Patch Updates

#### Front-End Changes
- Refactored loading state to provide better feedback when users upload files.
- Adjusted spacing between files in the upload file component for improved visual clarity.
- Updated styles for the `MessageModal.tsx` component.

## [3.9.1] - 2025-04-28
### Patch Updates

#### Back-End Changes
  - Approval workflows associated with the user are now retrieved from the backend when necessary.
  - Space PROT and SITE filters are now applied to approval workflows and approval workflow steps.

#### Front-End Changes
- Remove function that previously fetched the user's `approvalWorkflowIds`.

## [3.9.0] - 2025-04-25
### Minor Updates

##### [Task link](https://dev.azure.com/CelaneseCorporation/Digital%20Plant/_boards/board/t/25.%20Action%20Item%20Management%20_%20Thiago%20Wigg%20(APP)/Stories?text=caio&workitem=142214)
- **Upload Files On Details Screen**: assignees, owners, and master admins (feature ID) should be able to upload files to the Uploads section while the action is in progress (not completed or cancelled)

#### Front-End Changes
  - Update `MessageModal.tsx` component.
  - Update `FilesAndLinksTab.tsx` component.
  - Update `details/[id]/page.tsx` component.
  - Create loading state

#### Back-End Changes
  - Fix action creation bug on `action_item_creator.py`

## [3.8.2] - 2025-04-25
### Patch Updates

#### Back-End Changes
- **Improved Casing Variation Logic**: Included `input_string` as one of the basic casing variations in the `all_casings` utility function, ensuring the original input is always considered in the output.

## [3.8.1] - 2025-04-25
### Patch Updates

#### Front-End Changes
- **Private Modal and Data Submission**:
  - Only users, teams, and roles manually added in the private modal will be sent to the backend in `viewUsers`, `viewTeams`, and `viewRoles` respectively for events and actions.
- **Action Creation Changes**:
  - The `views` field will no longer be sent from the frontend during action creation; it will be constructed in the backend.
- **Date Validation**:
  - There is no longer a date range limitation for `dueDate` and `startDate`. The only requirement is that `dueDate` must be greater than `startDate` when both exist.
  - It is no longer possible to save an event form with a `dueDate` earlier than the `startDate`.
- **Private Actions in Public Events**:
  - It is no longer possible to create private actions within a public event.

#### Back-End Changes
- **Action Creation and Editing Logic**:
  - Updated creation and editing logic for actions to save manually added values in `views` and required values for actions and events, when applicable.
- **Event Editing Logic**:
  - Updated event editing logic to modify the `views` field of related actions when an event change occurs.

## [3.8.0] - 2025-04-17
### Minor Updates

#### Front-End Changes
##### [Task link](https://dev.azure.com/CelaneseCorporation/Digital%20Plant/_boards/board/t/25.%20Action%20Item%20Management%20_%20Thiago%20Wigg%20(APP)/Stories?System.AssignedTo=pedro.rimes_contractor%40celanese.com&workitem=143044)
- **Creation of Support Screen**: Creation of Support Screen forms and the feature to create cards from the aplication

## [3.7.0] - 2025-04-15
### Patch Updates

#### Front-End Changes
- **Upload Flow Enhancements**:
  - Fixed incorrect tags on file upload methods.
  - Updated `buildUploadFilesRequest` to use correct `activeUser` typing.
  - Adjusted `buildUploadFilesRequest` usage for action creation.
  - Fixed UI state of upload files during action creation and addition.
  - Displayed `source`, `user`, and `uploadedTime` in frontend components.
  - Corrected regex used in upload logic.
  - Applied conditional rendering rules for file-related text.
  - Updated and standardized text styling.
  - Organized related components for clarity.
  - Fixed user info rendering for source-event uploads.

- **Translation and UI Improvements**:
  - Added step-based rules and translations for upload process.
  - Created logic for upload step translation and text rendering.
  - Committed translation updates: `[142214]: [DEV] Adding translations for upload steps`.

#### Back-End Changes
- **File Upload Enhancements**:
  - Ensured correct storage handling for `DueDateExtension` file uploads.
  - Extended queries to include `uploadedTime` across:
    - Completed Evidence
    - Extension Request
    - Action Creation
    - Action In Progress
    - Event Creation
    - Event In Progress
  - Ensured correct `uploadedTime` is sent in `due-date-extension` request.
  - Verified `source` integrity across all file-related back-end logic.

## [3.6.0] - 2025-04-14
### Minor Updates

#### Front-End Changes
- **Event Form Enhancements**:
  - Added **equipment** and **functional location** fields to the event form.
- **Event Table Improvements**:
  - Added **equipment** and **functional location** columns to the event table.
  - Added **filter options by equipment** and **functional location** in the event table.

#### Back-End Changes
- Implemented the `containsAny` filter for **equipments** and **functional locations**.
- Created endpoints to retrieve the list of:
  - **Functional Locations**
  - **Equipments**

## [3.5.4] - 2025-04-11
### Patch Updates

#### Front-End Changes
- **Save new template**: Fixed issue with saving assignees that was not working properly.
- **Recurrence date**: Visual adjustment for small screens.

## [3.5.3] - 2025-04-08
### Patch Updates

#### Front-End Changes
- **Update SDK-UI version**: Update @celanese/celanese-ui to 4.21.4 on package.json and package-lock.json.

## [3.5.2] - 2025-04-08
### Minor Updates

#### Front-End Changes
##### [Task link](https://dev.azure.com/CelaneseCorporation/Digital%20Plant/_boards/board/t/25.%20Action%20Item%20Management%20_%20Thiago%20Wigg%20(APP)/Stories?System.AssignedTo=pedro.rimes_contractor%40celanese.com&workitem=181871)
- **Update Events Complete and Cancel Buttons**: Complete event button and Cancel Event button only render when the event status is in progress

## [3.5.1] - 2025-04-08
### Patch Updates

#### Back-End Changes
- **Safe JSON parsing for related actions**: Prevent `KeyError` and `JSONDecodeError` when loading `related_actions` from file metadata by adding default values and error handling.

## [3.5.0] - 2025-04-04
### Patch Updates

#### Front-End Changes
- **Dependencies**:
  - Removed `storybook` from `package.json` dependencies.
  - Forced `glob` dependency to version `^10.0.0` or higher to resolve memory lock issues.
- **Update SDK-JS version**: Update @celanese/celanese-sdk to 5.5.0 on package.json and package-lock.json.
- **Update SDK-UI version**: Update @celanese/celanese-ui to 4.21.2 on package.json and package-lock.json.

## [3.4.1] - 2025-04-03
### Patch Updates

#### Front-End Changes
##### [Fault Link](https://dev.azure.com/CelaneseCorporation/Digital%20Plant/_workitems/edit/184803)
- **Update Events Table Id**: Fox description should be as default off.
- **Update Events Table Filter**: Fix onChange method to status field on event table filter.

## [3.4.0] - 2025-04-03
### Minor Updates

#### Front-End Changes
##### [Task link](https://dev.azure.com/CelaneseCorporation/Digital%20Plant/_boards/board/t/25.%20Action%20Item%20Management%20_%20Thiago%20Wigg%20(APP)/Stories?text=caio&workitem=184700)
- **Update Events Table Filter**: The "Clear," "Default," and "Apply" buttons in the `EventFilter\index.tsx` are now always fixed, and the filter has adopted the same standard used in the Home filter and Recurring filter.

## [3.3.0] - 2025-04-02
### Minor Updates

#### Front-End Changes
##### [Feature card link: 180720 - [AIM] Improvements to the events table exported](https://dev.azure.com/CelaneseCorporation/Digital%20Plant/_boards/board/t/25.%20Action%20Item%20Management%20_%20Thiago%20Wigg%20(APP)/Stories?text=caio&workitem=180720).
- **Update Events Table Export**: The exported events table now includes the total number of action items, the completed action items, and event descriptions.
- **Add Description Column**: The events table now has a new description column with default unselected.

## [3.2.3] - 2025-04-02
### Patch Updates

#### Back-End Changes
- **Prevent event file loss**: Ensure that event files are retained when a new action is created within the event.

## [3.2.2] - 2025-04-02
### Patch Updates

#### Front-End Changes
- **Challenge text label fix**: Changed the label text for the challenge modal

## [3.2.1] - 2025-04-01
### Patch Updates

#### Back-End Changes
- **Modify action query**: Update the `action by id` query to return `owner` and `secondary owners` of the event.
- **Ownership validation**: Implement logic to check if the active user is the `owner` or `secondary owner` of the event.

#### Front-End Changes
- **Adjust action pages**: Update the edit and detail pages to validate whether the edit/delete buttons should be displayed for the logged-in user.

## [3.2.0] - 2025-03-31
### Patch Updates

#### Front-End Changes
- **Update SDK-JS version**: Update @celanese/celanese-sdk to 5.3.0 on package.json and package-lock.json.
- **Update SDK-UI version**: Update @celanese/celanese-ui to 4.16.0 on package.json and package-lock.json.

## [3.1.0] - 2025-03-27
### Minor Updates

#### Front-End Changes
- **Adds Prefix Filter by Source Event Title**: Added the option to filter items in the home table by the prefix of the `SourceEvent` title.
- **Adds Exact Match Filter by Source Event Title in Dashboard**: Added the option to filter by exact `SourceEvent` title in the "Site" and "Supervisor" tabs on the dashboard.
- **Updates Filter Model in Dashboard**: The filter model in the dashboard has been updated to resemble the one in the home, providing a more consistent user experience.

#### Back-End Changes
- **Filters Actions by Source Event Title**: Implemented filtering for actions by `sortSourceEventTitle` (exact match and prefix) for KPIs and tables.
- **Populates `sortSourceEventTitle` When Creating Action**: When creating an action related to a `SourceEvent`, the `sortSourceEventTitle` field is populated with the corresponding `SourceEvent` title.
- **Updates Actions When `SourceEvent` Title Changes**: If the `SourceEvent` title is updated, all associated actions are automatically updated with the new title.

## [3.0.3] - 2025-03-28
### Patch Updates

#### Front-End Changes
- **New Action Form**: Adjusted private action creation process.

## [3.0.2] - 2025-03-27
### Patch Updates

#### Front-End Changes
##### [Bugfix card link: 183209 - It is not possible to cancel an action](https://dev.azure.com/CelaneseCorporation/Digital%20Plant/_workitems/edit/183209).
- **Bugfix**: Fix RemoveItemModal component loghic.

## [3.0.1] - 2025-03-27
### Patch Updates

#### Front-End Changes
- **Adding New Translations**: Included additional translations.

## [3.0.0] - 2025-03-27
### Major Updates

#### Back-End Changes
- **Refactored Update Action Endpoint and update-related methods**:
  - Enabled field erasing.
  - Absorbed business rules related to privates, files, assignees, links, etc (removing from front).
  - Now only updates what was changed.
  - Contract changes (breaking)
- **Overall improvements**: Overall improvements on typing, performance, etc, on existing code.

#### Front-End Changes
- **Adjust Action Form Permissions**: Updated permissions for the action creation and editing forms to ensure only authorized users have access.
- **Adding New Translations**: Included additional translations.
- **Refactored New Action Form**:
  - Implemented `zod` for form validation.
  - Improved responsiveness.
  - Created a `zod` schema for the recurring action inside the new action form.
- **Enum Folder Refactoring**: Created a dedicated `enum` folder and moved all enums into it for better organization.
- **Action Tab Updates**:
  - The Action Tab no longer shows actions in draft. Creation is now done directly.
  - The Action Tab now uses the general `ActionTable` component.
- **Recurring Tab Updates**:
  - The Recurring Tab no longer shows actions in draft. Creation is now done directly.

## [2.8.7] - 2025-03-26
### Patch Updates

#### Front-End Changes
##### [Bugfix card link: Adjust Action Details screen on mobile](https://dev.azure.com/CelaneseCorporation/Digital%20Plant/_boards/board/t/25.%20Action%20Item%20Management%20_%20Thiago%20Wigg%20(APP)/Stories?text=caio&workitem=142636).
- **Updated**: `ActionItemDetailsPage` to use `isMobile` prop for responsive layout adjustments.
- **Refactored**: `DetailEditIconButton` and `RemoveItemIconButton` to support dynamic font sizes and widths based on screen size.
- **Enhanced**: `DetailsMenu` to allow flexible button arrangements and full-width buttons on smaller screens.
- **Modified**: `GeneralInfo` to hide the title on smaller screens and adjust layout dynamically.

## [2.8.6] - 2025-03-24
### Patch Updates

#### Front-End Changes
- **Adding Translation**: Added missing translations for UI components to improve localization.
- **Fix Filter to Exclude Deleted Items**: Updated filters to ensure deleted items are no longer displayed in the UI.
- **Fix Message Modal**: Corrected the text content issues in the message modal.
- **Fix Recurring Tab ID Column**: Resolved an issue where the ID column in the recurring tab was not displaying correctly.

## [2.8.5] - 2025-03-20
### Patch Updates

#### Front-End Changes
- **Updated URL for JO.IA API Requests**: The URL for JO.IA requests has been updated.

## [2.8.4] - 2025-03-19
### Patch Updates

#### Front-End Changes
- **Charts Responsiveness Improvements**:
  - Enhanced responsiveness for charts, ensuring better readability and usability on mobile devices.
  - Added the `responsive` property to both bar and pie charts to dynamically adjust layout, font sizes, and alignment based on screen size.
  - Adjusted legend and tooltip behavior for smaller screens to prevent overlapping elements.
- **Supervisor Tab Filter Fix**:
  - Fixed the behavior of the "Clear" button in the Supervisor tab filter. Now, all fields, including the "Employee Name" field, are properly cleared when the button is clicked.
  - Improved the reset logic to ensure default values are correctly applied when resetting filters.
- **Dashboard Page Responsiveness**:
  - Adjusted the `margin` of tabs in the `AimTabs` component to be responsive. On mobile devices, the `margin` is now `0`, while on larger screens, it is `0 30px`.
  - Updated the padding of the `ClnPanel` in the Dashboard page to dynamically adapt based on screen size.
- **Supervisor Tab Refactor**:
  - Introduced the `isChartFragment` variable to encapsulate the conditional rendering logic for charts and tables, reducing code duplication.
  - Adjusted the layout to switch between `ClnPanel` and `Box` based on screen size, ensuring a consistent user experience across mobile and desktop devices.
- **BarChart Responsiveness**:
  - Added the `responsive` property to the bar chart to switch between horizontal and vertical layouts depending on screen size.
  - Adjusted font sizes and legend positioning to improve usability on mobile devices.
- **ReferenceTab Responsiveness**:
  - Updated the "New Reference" button to display only the icon on mobile devices, while showing the full text on larger screens.
  - Improved the filtering and search experience with visual adjustments for smaller screens.

## [2.8.5] - 2025-03-20
### Patch Updates

#### Back-End Changes
- **Updated aggregate query filter for events table**: Refined the filter for `title`, `id`, and `due date` in the `aggregate` call to match the model accepted by the SDK.

#### Front-End Changes
- **Re-enabled due date display in filters**: Ensured that the `due date` appears again when reopening a filter in which it was applied.

## [2.8.4] - 2025-03-20
### Patch Updates

#### Front-End Changes
- **Fix message in the cancellation confirmation modal**: Corrected the text displayed in the cancellation confirmation modal.

## [2.8.3] - 2025-03-20
### Patch Updates

#### Front-End Changes
- **Create confirmation modal**: Added a confirmation modal for bulk deletion/cancellation of actions.
- **Fix action selection behavior**: Corrected action selection behavior when switching pages.
- **Fix search field behavior**: Resolved issue where the search field was disabled on the bulk delete screen.
- **Fix visible columns**: Adjusted the visible columns in the selected actions modal.

## [2.8.2] - 2025-03-19
### Patch Updates

#### Back-End Changes
- **Set default value for isPrivate**: Defined `isPrivate` as `False` by default when creating a new action item.
- **Normalize priority field**: Ensured that the `priority` field is always stored in lowercase when saved.

## [2.8.1] - 2025-03-19
### Patch Updates

#### Front-End Changes
- **Update UI-LIB version**: Update @celanese/ui-lib to 2.1.1 on package.json and package-lock.json.
- **Update SDK-JS version**: Update @celanese/celanese-sdk to 4.7.0 on package.json and package-lock.json.
- **Update SDK-UI version**: Update @celanese/celanese-ui to 4.11.2 on package.json and package-lock.json.
- **Fix favorite sites in Header Navbar**: Adjust inconsistencies in favorite sites handling in the header navigation bar.
- **Add `updateFavoriteSiteInfo` function in AuthGuardContext**: Allows updating the favorite site without needing to call UM again.

## [2.8.0] - 2025-03-17
### Minor Updates

#### Front-End Changes
- **Disable active filter fields and remove "Clear" button on Bulk Delete page**: Deactivated the filter fields that were active on the home page and removed the "Clear" button for filters on the Bulk Delete page.
- **Display KPI information in Snackbar on Bulk Delete Actions page**: Added a Snackbar to show the active KPI on the Bulk Delete Actions page.
- **Update Snackbar style to match correct design**: Adjusted the Snackbar style to align with the correct design and added a new parameter for Snackbar anchoring, allowing customization of its position.
- **Lock search field on Bulk Delete page**: Locked the search field on the Bulk Delete page if a filter was applied from the home page.
- **Preserve selected KPI and filters when returning to Home**: Ensured that when returning to the Home page, the previously selected KPI and all applied filters are retained.

#### Back-End Changes
- **Add new fields to kpiFilter enum**: Added new fields to the `kpiFilter` enum to expand available filter options.

## [2.7.1] - 2025-03-17
### Patch Updates

#### Front-End Changes
- **Add sort functionality for Action Tables**: Added sorting for the columns **externalId, assignee, owner, approver, verifier, is Private**, and **reporting Location** in all action tables.
- **Add sort for Event Table**: Added sorting for the columns **externalId** and **is Private** in the Event table.
- **Remove sort from specific Event Table columns**: Removed sorting functionality from the columns **reporting Line** and **impacted Units** in the Event table.

#### Back-End Changes
- **Fix sorting behavior by externalId in Action Table**: Corrected the sorting behavior by **externalId** for the Action table within an event.

## [2.7.0] - 2025-03-17
### Minor Updates

#### Front-End Changes
- **Add update script for User Management**: Added `"update-um": "cmd /c updateUm.cmd"` script to update the User Management repository and activate the virtual environment.
- **Add bulk delete/cancel button**: Created a new button in the Home table for bulk deletion and cancellation of actions.
- **Require authentication for bulk delete/cancel route**: Added authentication to the bulk deletion/cancellation route.
- **Enhance ActionTable/DataGridTable component**: Adapted the table component to allow hiding specific buttons and adding a selection checkbox.
- **Improve HomeFilter component**: Updated HomeFilter to display an informational message when necessary.
- **Fix IconWithTooltip component**: Ensured the tooltip displays correctly.
- **Add confirmation modal for bulk delete/cancel actions**: Created a confirmation modal for bulk deletion and cancellation of actions.
- **Adjust action details modal**: Made improvements to the modal that displays action details.
- **Enhance ModalWrapper component**: Added automatic title line breaking and ensured the close button is always present, even without a title.
- **Add informative message to search field**: Included an informational message in the search input field.

#### Back-End Changes
- **Make comments optional when canceling actions**: Comments are now optional when canceling actions.

## [2.6.1] - 2025-03-13
### Patch Updates
#### Back-End Changes
- **Metadata query fix**: Adjusted the metadata call to use the correct query.

## [2.6.0] - 2025-03-13
### Minor Updates

#### Front-End Changes
- **Update UI-LIB version**: Update @celanese/ui-lib to 2.0.0 on package.json and package-lock.json.

## [2.5.0] - 2025-03-13
### Minor Updates
#### Back-End Changes
- **Add new field in GetActionsTable response**: Now returning the metadata field in the response of the `GetActionsTable` endpoint.

#### Front-End Changes
- **Update SDK JS version**: Update @celanese/celanese-sdk to 4.6.0 on package.json and package-lock.json.
- **Update SDK UI version**: Update @celanese/celanese-ui to 4.9.0 on package.json and package-lock.json.

## [2.4.2] - 2025-03-13
### Patch Updates
#### Front-End Changes
- **Bug fix for translation**: Change `ViewOnly` to `viewOnly`.

## [2.4.1] - 2025-03-10
### Patch Updates
#### Back-End Changes
- **Fix sort for recurring action items**: Fix sort for actions being created based on a recurrence.


## [2.4.0] - 2025-03-10
### Minor Updates
#### Back-End Changes
- **Add create action endpoint for external applications**: Added a create action endpoint to be used by external applications.


## [2.3.1] - 2025-03-10
### Patch Updates
#### Front-End Changes
- **Fix bug in action form link rendering**: Fixed a bug where links entered in the action form were not being found during local editing in the same render cycle.


## [2.3.0] - 2025-03-07
### Minor Updates
#### Back-End Changes
- **Add MetadataField to create action item flows**: Added support to receive metadatas in all flows that create actions.

### Patch Updates
#### Back-End Changes
- **Fixed typing errors**: Fixed some properties or variables not being properly typed.
- **Fixed lint warnings**: Fixed some warning pointed by ruff.

## [2.2.15] - 2025-03-06
### Patch Updates
#### Back-End Changes
- **Date validation fix for new actions**: Now considers the original time zone when provided.

#### Front-End Changes
- **Visual adjustments on Home Action KPIs**: For the "Closed" tab, non-clickable KPIs no longer show a pointer cursor.
- **Header NavBar icon hover fix**: Adjusted hover effect for the Dynamic Translation and Notifications icons.
- **Action Home Page filter cache fix**: When switching between tabs (e.g., "Closed" to another tab and vice versa), filters now reset to their original state.
- **Translation fixes**: Improved translation for "Category" and "Subcategory 1" in the Action Tab on the Home/Dashboard, Export, and New Action form options.
- **Export bug fix**: Resolved issue where "Private" and "Location" fields were not appearing in the export.
- **User time zone handling**: The user's time zone is now sent when creating a new action.


## [2.2.14] - 2025-02-28
### Patch Updates
#### Front-End Changes
- **Fix approval answer**: Fix approval error message when answering.

## [2.2.13] - 2025-02-28
### Patch Updates
#### Front-End Changes
- **Fix action edit**: Fix action edit when subcategory 2 is missing.

## [2.2.12] - 2025-02-28
### Patch Updates
#### Package.json Changes
- **Update SDK JS version**: Update @celanese/celanese-sdk to 4.3.0 on package.json and package-lock.json.
- **Update SDK UI version**: Update @celanese/celanese-ui to 4.5.1 on package.json and package-lock.json.

## [2.2.11] - 2025-02-28
### Patch Updates
#### Front-End Changes
- **Adjusted Reporting Line Filter**: Modified the reporting line filter to fetch only items related to the selected site.
- **Fixed Default and Clear Filters on Event Page**: Resolved an issue where the default and clear filters were not properly resetting the reporting line filter on the event page.

## [2.2.10] - 2025-02-28
### Patch Updates
#### Front-End Changes
- **Filter Logic Adjustments for Action Home**: Fixed the logic when switching between the "Todo" and "Closed" tabs, where the status filters were previously being incorrectly cleared.
- **Added sort as false for the sourceEventTitle column in the Action Table**: Disabled sorting for the `sourceEventTitle` column in the Action Table.

## [2.2.9] - 2025-02-27
### Patch Updates
#### Front-End Changes
- **Validation Adjustments in determineActiveFilterCount**: Improved validation in `determineActiveFilterCount` to handle `null` or `undefined` values properly, ensuring robustness in filter count calculations.
- **Fix for onlyPrivate Filter Validation**: Adjusted the validation logic for `onlyPrivate` in `determineActiveFilterCount` to correctly account for its presence in filter calculations.

## [2.2.8] - 2025-02-27
### Patch Updates
#### Front-End Changes
- **Removal of Search from Session Storage Filter**: Removed the `search` parameter from the session storage filter to improve performance and consistency.

## [2.2.7] - 2025-02-27
### Patch Updates
#### Front-End Changes
- **Visual Adjustment for Action Filter in Event Details Action Tab**: Fixed an issue where "Owner" and "Assigned" were not appearing as default values in the action filter.
- **Fix for Recurring Status Change in Admin Recurring Screen**: Resolved an issue where the recurring status change was not being applied correctly in the admin recurring screen.
- **Visual Adjustment for Supervisor Filter in Dashboard for UpdateStatus**: Improved the visual presentation of the supervisor filter in the dashboard to enhance the updateStatus functionality.
- **Translation Updates for Columns in Recurring Tab**: Added new translations for the "Frequency" and "Category" columns to improve localization in the recurring tab.
- **Change from AutocompleteOptionNew to AutocompleteOption**: Updated the reference from `AutocompleteOptionNew` to `AutocompleteOption` to standardize the naming convention.

## [2.2.6] - 2025-02-27
### Patch Updates
#### Back-End Changes
- **Event creation/edition**: Fixed event request parsing.

## [2.2.5] - 2025-02-27
### Patch Updates
#### Front-End Changes
- **Addition of New Translations for Frequency**: Added new translations for frequency options to improve localization.
- **Visual Adjustment for Dashboard Filters**: Improved the visual presentation of filters in the dashboard for better user experience.
- **Bug Fix for Start Date Cache in Recurring Tab**: Fixed an issue where the start date cache was not being properly handled in the recurring tab.
- **Code Improvement for Recurring Filters**: Refactored the recurring filter logic for better maintainability and performance.

## [2.2.4] - 2025-02-26
### Patch Updates
#### Back-End Changes
- **Event creation/edition**: Fixed event creation and edition methods.

## [2.2.3] - 2025-02-26
### Patch Updates
#### Back-End Changes
- **Action edit**: Fixed update action method.

## [2.2.2] - 2025-02-26
### Patch Updates
#### Front-End Changes
- **Default Filters Logic Update for Local Storage**: Adjusted the logic for default filters in local storage to include the visual names of users for owners and assignees.
- **Internal Filter Fix for Action to Event Details**: Fixed an issue with the internal filter where the wrong event ID was being passed to the event details.
- **Filter Logic Refinement for Tables**: Refined the filter logic in tables to call a `setValueFilterInfo` function, which modifies the filter and resets the data in `sessionStorage`.

## [2.2.1] - 2025-02-26
### Patch Updates
#### Front-End Changes
- **ActionTable sort**: Added getNullsLastComparator to all sortable columns in order to replicate cognite's behavior.

#### Back-End Changes (Function)
- **Sort Fields**: Added `SortMapperClient` that should be used to get a `SortMapper`, the SortMapper can then be used to get sort values based on externalIds. All Actions and Events creation/edition flows were updated to save the right sort values (usually the name or description).

## [2.2.0] - 2025-02-25

### Minor Updates
#### Front-End Changes
- **Standardized General Buttons Component**: Created a standard file for general buttons (`Edit`, `Delete`, `Cancel`) inside the `ButtonsComponents` folder.
- **Session Storage Enhancement**: Added a new `sessionStorage` property to store the last accessed tab when a return button is available. Example: `Home Page > Details > Home Page`.
- **Active Filters Count in Data Grid**: Implemented an `activeFiltersCount` in the Data Grid table to track selected filters.
- **Persistent Column State in Data Grid Pro**: Now, column positions and hidden columns are saved per table. An ID is required when invoking `Data Grid Pro`.
- **Loading Indicator on Click**: Added a loading indicator for all button clicks across screens.
- **Translation Abstraction Layer**: Added an abstraction layer for the `translate` function, now handled through the `generate-translate` utility layer for better maintainability and flexibility
- **Unified Action Table Component**: Created a higher-level abstraction for the `ActionTable`, now used across `Action Home` and `Dashboard`.
- **Admin Page Filter UI Fix**: Adjusted filters in the Admin Page to visually indicate active filters when reopening the filter modal.
- **Due Date Filter Fix in Action Home**: Fixed an issue where selecting a date filter did not display the selected date in the filter UI.
- **Loading Provider**: Introduced a new loading provider to manage loading states across the application, improving consistency and control over loading indicators.

#### Back-End Changes
- **UpsertSourceEventRequest Model Update**: Removed the validation that required `due_date` to be equal to or greater than today's date.

## [2.1.3] - 2025-02-24

### Patch Updates
#### Back-End Changes
- **Adjust Number of Action Items Column in Event Table**:
Actions with deleted status should not be counted in the source event table.
- **Modification:** Update source_event_service.py in _get_aggregate_actions_by_event_id method, adding filter to get every status except DELETED, ACTIVE and INACTIVE.

## [2.1.2] - 2025-02-20

### Patch Updates
#### Front-End Changes
- **Alignment of the comment box and buttons**: Fixing responsiveness width of cancel action item modal.
- **UI-lib version update**: Updated the version of `ui-lib` to 1.7.2.
- **Action Home KPIs visual update**: Fixed responsiveness for the KPIs component in Action Home.
- **Notification component fix**: Improved the Notification component to correctly clear the count of unread notifications.

#### Back-End Changes
- **Get Actions Endpoint update**: Renamed parameters `sourceId` and `sourceType` to `sourceIds` and `sourceTypeExternalIds` in the `getActions` endpoint.
- **Edit Action validation fix**: Adjusted the validation in the Edit Action feature to allow actions with the status `inactive`.

## [2.1.1] - 2025-02-20

### Patch Updates
#### Front-End Changes
- **Alignment of the comment box and buttons**: Fixing responsiviness width of cancel action item modal.

## [2.1.0] - 2025-02-20

### Minor Updates
#### Back-End Changes
- **Get Actions Endpoint**: The **get actions** endpoint has been updated to allow searching by a list of `sourceId` or a list of `sourceType`.

#### Front-End Changes
- **Recurring Edit Validation**: Fixed an issue on the front-end by adding a validation to ensure that, when editing a recurring action, the `displayDueDate` or `dueDate` fields are not sent.
- **Add Feature Flag SDK Page**: Added a feature flag to hide the SDK page when the flag is set to `false`. Only the production environment will not have access to this page.


## [2.0.0] - 2025-02-12

### Major Updates
#### Front-End Changes
- **UI-Lib**: Updated from version 0.144 to 1.7
  - The following fields became mandatory:
    - `ClnPage` must be used on all pages.
    - To add a box with a border, use `ClnPanel`.
    - All colors must follow the library's model, e.g., `primary.main` (for dark blue).
    - Translations must now use the format `translation(TAG, 'APP-AIM')`.
- **Celanese SDK**: Updated to the latest version, improving integration and performance with the application.

### New Features
#### Front-End Changes
- **Celanese SDK Components**: Now available on a dedicated screen. By adding `/aim-sdk` at the end of the URL, users can access the AIM components directly.
- **Notifications**: The notifications icon, which previously redirected to a screen, now opens a drawer with all user notifications.
- **Dark Mode**: The dark mode option has been enabled.
- **Favorites**:
  - You can now favorite a language.
  - You can now favorite a site.
- **Translations**: Static translations have been revalidated.
- **New Functionalities**:
  - `Contextualization` and `jo.ia` are now enabled.
