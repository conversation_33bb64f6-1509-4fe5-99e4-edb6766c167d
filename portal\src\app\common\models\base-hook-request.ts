export type Filter<T> =
    | {
          [K in keyof T]?: ComparisonOperators<T[K]> | SpecialOperators<T> | Filter<T[K]>
      }
    | SpecialOperators<T>

type ComparisonOperators<T> =
    | { eq: T }
    | { lt: T }
    | { lte: T }
    | { gt: T }
    | { gte: T }
    | { isNull: boolean }
    | { in: T[] }
    | { prefix: T }

type SpecialOperators<T> = { and: Filter<T>[] } | { or: Filter<T>[] } | { not: Filter<T> }
