import React from 'react'
import { Controller } from 'react-hook-form'
import { ClnDatePicker, ClnDateRangePicker, MatIcon } from '@celanese/ui-lib'
import { Dayjs } from 'dayjs'
import { Popper } from '@mui/material'
import { translate } from '@/app/common/utils/generate-translate'

type GenericDateRangePickerProps = {
    name: string
    control: any
    valueController?: Dayjs | null
    onChange?: (newValue: [Dayjs | null, Dayjs | null] | Dayjs | null) => void
    label?: string
    limitTags?: number
    disableCloseOnSelect?: boolean
    size?: 'small' | 'medium'
    isRange?: boolean
    minDate?: Dayjs
    maxDate?: Dayjs
    disabled?: boolean
    [key: string]: any
}

const GenericDateRangePicker = ({
    name,
    control,
    options,
    getOptionLabel,
    valueController,
    onChange,
    label,
    limitTags = 1,
    renderOption,
    disableCloseOnSelect = true,
    customTagRender,
    size,
    isRange = true,
    minDate,
    maxDate,
    disabled,
    ...rest
}: GenericDateRangePickerProps) => (
    <Controller
        name={name}
        control={control}
        render={({ field: { value, onChange: onChangeField } }) => (
            <>
                {isRange ? (
                    <ClnDateRangePicker
                        disabled={disabled}
                        label={label ?? ''}
                        sxProps={{
                            width: '100%',
                            '.MuiInputBase-input': {
                                minWidth: 'auto',
                            },
                        }}
                        defaultValue={value}
                        value={value}
                        minDate={minDate}
                        maxDate={maxDate}
                        onChange={(e) => {
                            onChangeField(e)
                            onChange && onChange(e)
                        }}
                        slotProps={{
                            textField: {
                                size: size ?? 'medium',
                                InputProps: {
                                    startAdornment: <MatIcon icon="calendar_today" />,
                                },
                            },
                        }}
                        localeLanguage={'en'}
                    />
                ) : (
                    <ClnDatePicker
                        disabled={disabled}
                        value={value}
                        defaultValue={valueController}
                        onChange={(newValue) => {
                            onChangeField(newValue)
                            onChange && onChange(newValue)
                        }}
                        size={size ?? 'small'}
                        label={label ?? translate('stepper.form.assignment.chooseDate')}
                        minDate={minDate}
                        maxDate={maxDate}
                        toolbarHidden
                        closeOnSelect
                        slots={{
                            popper: Popper,
                        }}
                        slotProps={{
                            popper: {
                                sx: {
                                    zIndex: 1300,
                                },
                                placement: 'top-start',
                                modifiers: [
                                    {
                                        name: 'offset',
                                        options: {
                                            offset: [0, 8],
                                        },
                                    },
                                ],
                            },
                        }}
                        fullWidth
                        {...rest}
                    />
                )}
            </>
        )}
    />
)

export default GenericDateRangePicker
