import React from 'react'
import { Box, Tooltip, Typography } from '@mui/material'

type GenericFieldTextProps = {
    fieldName: string
    value: string
    icon?: React.ReactNode
    color?: string
    ellipsis?: boolean
}

const GenericFieldText = ({ fieldName, value, icon, color, ellipsis }: GenericFieldTextProps) => {
    const textContent = (
        <Typography
            sx={{
                fontWeight: 500,
                lineHeight: '24px',
                letterSpacing: '0.5px',
                color: color ?? 'default',
                ...(ellipsis && {
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap',
                    display: 'block',
                }),
            }}
        >
            {value}
        </Typography>
    )

    const wrappedContent = ellipsis && value !== '-' ? <Tooltip title={value}>{textContent}</Tooltip> : textContent

    return (
        <Box
            sx={{
                display: 'flex',
                flexDirection: 'column',
                wordWrap: 'break-word',
                whiteSpace: 'normal',
            }}
        >
            <Typography
                sx={{
                    color: 'text.secondary',
                    fontSize: '12px',
                    lineHeight: '16px',
                }}
            >
                {fieldName}
            </Typography>
            {icon ? (
                <Box
                    sx={{
                        display: 'flex',
                        flexDirection: 'row',
                        justifyContent: 'flex-start',
                        alignItems: 'center',
                        gap: '0.25rem',
                    }}
                >
                    {icon}
                    {wrappedContent}
                </Box>
            ) : (
                wrappedContent
            )}
        </Box>
    )
}

export default GenericFieldText
