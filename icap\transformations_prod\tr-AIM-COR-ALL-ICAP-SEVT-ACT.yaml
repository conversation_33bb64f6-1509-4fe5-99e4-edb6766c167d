# Manifest file downloaded from fusion
externalId: tr-AIM-COR-ALL-ICAP-SEVT-ACT
name: AIM-COR-ALL-ICAP-SEVT-ACT
query: >-
  SELECT 
      event.space AS space,
      CONCAT('SourceEvent.actions-', event.externalId, '-', action.externalId) AS externalId,
      node_reference(event.space, event.externalId) AS startNode,
    	node_reference(action.space, action.externalId) AS endNode
  FROM cdf_data_models('AIM-COR-ALL-DMD', 'ActionItemManagementDOM', '6_0_0', 'Action') AS action

  INNER JOIN cdf_data_models('AIM-COR-ALL-DMD', 'ActionItemManagementDOM', '6_0_0', 'SourceEvent') AS event on split(action.objectType, '-')[3] = event.iCAPEventID

  WHERE 
    	event.iCAPEventID IS NOT NULL
  	AND startswith(action.objectType, 'ICAP')
  	AND 1 = 0 -- freeze
destination:
  dataModel:
    space: AIM-COR-ALL-DMD
    externalId: ActionItemManagementDOM
    version: "6_0_0"
    destinationType: SourceEvent
    destinationRelationshipFromType: actions
  type: instances
ignoreNullFields: true
shared: true
action: upsert
dataSetExternalId: AIM-COR-ALL-DAT
# Specify credentials separately like this:
# authentication:
#   read:
#     clientId: ${READ_CLIENT_ID}
#     clientSecret: ${READ_CLIENT_SECRET}
#     tokenUrl: ${READ_TOKEN_URL}
#     cdfProjectName: ${READ_CDF_PROJECT_NAME}
#     # Optional: If idP requires providing the scopes
#     scopes:
#       - ${READ_SCOPES}
#     # Optional: If idP requires providing the audience
#     audience: ${READ_CDF_AUDIENCE}
#   write:
#     clientId: ${WRITE_CLIENT_ID}
#     clientSecret: ${WRITE_CLIENT_SECRET}
#     tokenUrl: ${WRITE_TOKEN_URL}
#     cdfProjectName: ${WRITE_CDF_PROJECT_NAME}
#     # Optional: If idP requires providing the scopes
#     scopes:
#       - ${WRITE_SCOPES}
#     # Optional: If idP requires providing the audience
#     audience: ${WRITE_CDF_AUDIENCE}
# Or together like this:
# authentication:
#   clientId: ${CLIENT_ID}
#   clientSecret: ${CLIENT_SECRET}
#   tokenUrl: ${TOKEN_URL}
#   # Optional: If idP requires providing the scopes
#   cdfProjectName: ${CDF_PROJECT_NAME}
#   scopes:
#    - ${SCOPES}
#   # Optional: If idP requires providing the audience
#   audience: ${AUDIENCE}