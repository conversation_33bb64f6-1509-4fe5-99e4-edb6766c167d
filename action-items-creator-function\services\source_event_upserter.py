import json
from datetime import UTC, datetime
from typing import Optional, Union

from pydantic import ValidationError

from clients.action_item_client import ActionItemClient
from clients.actions.models import ActionItemUpdate
from clients.core.constants import FileMetadataKey
from clients.core.models import Node
from clients.core.utils import to_user_azure_attribute
from clients.sort_mapper import GetSortMapperRequest, SortMapper
from clients.source_event._models import Action
from clients.source_event.constants import SourceEventStatus
from clients.source_event.models import SourceEventWithActionsResult
from clients.source_event.requests import (
    CreateSourceEventRequest,
    GetSourceEventRequest,
    UpdateSourceEventRequest,
)
from models.action_item import NodeReference
from models.entities_enum import EntitiesEnum
from models.settings import Settings
from models.source_event import SourceEvent, SourceEventHistory
from models.upsert_source_event_request import Attachment
from services.cognite_service import CogniteService
from services.graphql_service import GraphqlService
from services.logging_service import LoggingService
from services.source_event_service import SourceEventService
from utils.general_utils import flat_map
from utils.id_generation_utils import IdGenerator
from utils.space_utils import get_transactional_space_from_site_id


class SourceEventUpserter:
    """A class responsible for upserting source events into the system."""

    def __init__(
        self,
        cognite_service: CogniteService,
        graphql_service: GraphqlService,
        settings: Settings,
        logging_service: LoggingService,
        action_item_client: ActionItemClient,
    ) -> None:
        """Initialize the SourceEventUpserter with the required services and settings."""
        self._cognite_service = cognite_service
        self._graphql_service = graphql_service
        self._log = logging_service
        self._settings = settings
        self._action_item_client = action_item_client
        self._now_datetime = datetime.now(tz=UTC)
        self._now_date = self._now_datetime.date()
        self._id_generator = IdGenerator[EntitiesEnum](self._now_datetime)

    async def execute(
        self,
        source_event_request: list[dict],
        is_update: bool = False,
    ) -> tuple[list[str], dict]:
        """Upsert source events into the system."""
        if len(source_event_request) == 0:
            self._log.info("No source event to upsert.")
            return [], {}

        now_str = self._now_datetime.replace(microsecond=0).isoformat()
        errors_by_event_id = {}
        attachments_by_event_id: dict[str, list[Attachment]] = {}
        source_event_to_create_by_event_id: dict[str, SourceEvent] = {}
        source_event_external_ids: list[str] = []

        parsed_requests, sort_mapper = self._get_parsed_requests(
            source_event_request,
            is_update,
            errors_by_event_id,
        )

        source_event_dict: dict[str, SourceEventWithActionsResult] = {}
        if is_update:
            source_event_service = SourceEventService(self._action_item_client)
            request = GetSourceEventRequest(
                external_ids=[req.external_id for req in parsed_requests],
            )
            source_events = await source_event_service.get_source_events_with_actions(
                request,
            )

            if source_events:
                source_event_dict.update(
                    {event.external_id: event for event in source_events},
                )

        actions_to_update: list[ActionItemUpdate] = []
        for item_request in parsed_requests:
            external_id = item_request.target_external_id
            assert external_id is not None

            errors_by_event_id[external_id] = errors_by_event_id.get(external_id) or []

            self._log.info(f"Processing source event request from event {external_id}.")

            aim_space = get_transactional_space_from_site_id(
                item_request.reporting_site_id,
                item_request.private,
            )

            aim_site_space = get_transactional_space_from_site_id(
                item_request.reporting_site_id,
            )

            aim_dataset_id = self._cognite_service.get_dataset_id(aim_space)

            aim_ref_space = self._settings.aim_ref_instances_space
            um_space = self._settings.user_management_instances_space
            assets_space = self._settings.asset_hierarchy_instances_space

            try:
                source_event = SourceEvent(
                    externalId=external_id,
                    space=(item_request.space if is_update else aim_space),
                    createdAt=(
                        self._id_generator.get_timestamp() if not is_update else None
                    ),
                    status=(
                        NodeReference(
                            externalId=SourceEventStatus.IN_PROGRESS.value,
                            space=aim_ref_space,
                        )
                    ),
                    actionsCount=0 if not is_update else None,
                    title=item_request.title,
                    description=item_request.description,
                    sourceInformation=external_id,
                    eventType=(
                        NodeReference(
                            externalId="SEVTY-Internal",
                            space=aim_ref_space,
                        )
                        if not is_update
                        else None
                    ),
                    owner=NodeReference(
                        externalId=item_request.owner_id or item_request.created_by_id,
                        space=um_space,
                    ),
                    secondaryOwnerUsers=(
                        [
                            NodeReference(externalId=owner_id, space=um_space)
                            for owner_id in item_request.secondary_owner_users_id
                        ]
                        if item_request.secondary_owner_users_id
                        else None
                    ),
                    secondaryOwnerRoles=(
                        [
                            NodeReference(externalId=owner_id, space=um_space)
                            for owner_id in item_request.secondary_owner_roles_id
                        ]
                        if item_request.secondary_owner_roles_id
                        else None
                    ),
                    secondaryOwnerTeams=(
                        [
                            NodeReference(externalId=owner_id, space=um_space)
                            for owner_id in item_request.secondary_owner_teams_id
                        ]
                        if item_request.secondary_owner_teams_id
                        else None
                    ),
                    application=(
                        NodeReference(
                            externalId=item_request.application_id,
                            space=um_space,
                        )
                        if not is_update
                        else None
                    ),
                    reportingSite=(
                        NodeReference(
                            externalId=item_request.reporting_site_id,
                            space=assets_space,
                        )
                        if not is_update
                        else None
                    ),
                    category=NodeReference(
                        externalId=item_request.category_id,
                        space=aim_ref_space,
                    ),
                    subCategory=(
                        NodeReference(
                            externalId=item_request.sub_category_id,
                            space=aim_ref_space,
                        )
                        if item_request.sub_category_id is not None
                        else None
                    ),
                    siteSpecificCategory=(
                        NodeReference(
                            externalId=item_request.site_specific_category_id,
                            space=aim_site_space,
                        )
                        if item_request.site_specific_category_id is not None
                        else None
                    ),
                    createdBy=(
                        NodeReference(
                            externalId=item_request.created_by_id,
                            space=um_space,
                        )
                        if not is_update
                        else None
                    ),
                    dueDate=(
                        item_request.due_date.isoformat()
                        if item_request.due_date is not None
                        else None
                    ),
                    displayDueDate=(
                        item_request.due_date.isoformat()
                        if item_request.due_date is not None
                        else None
                    ),
                    assignmentDate=(
                        item_request.assignment_date.isoformat()
                        if item_request.assignment_date is not None
                        else None
                    ),
                    reportingUnit=(
                        NodeReference(
                            externalId=item_request.reporting_unit_id,
                            space=assets_space,
                        )
                        if item_request.reporting_unit_id is not None
                        else None
                    ),
                    reportingUnits=(
                        [
                            NodeReference(externalId=unit_id, space=assets_space)
                            for unit_id in item_request.impacted_units_id
                        ]
                        if item_request.impacted_units_id
                        else None
                    ),
                    reportingLocation=(
                        NodeReference(
                            externalId=item_request.reporting_location_id,
                            space=assets_space,
                        )
                        if item_request.reporting_location_id is not None
                        else None
                    ),
                    impactedReportingLocations=(
                        [
                            NodeReference(externalId=location_id, space=assets_space)
                            for location_id in item_request.impacted_locations_id
                        ]
                        if item_request.impacted_locations_id
                        else None
                    ),
                    reportingLine=(
                        NodeReference(
                            externalId=item_request.reporting_line_id,
                            space=assets_space,
                        )
                        if item_request.reporting_line_id is not None
                        else None
                    ),
                    businessLine=(
                        NodeReference(
                            externalId=item_request.business_line_id,
                            space=assets_space,
                        )
                        if item_request.business_line_id is not None
                        else None
                    ),
                    isPrivate=item_request.private,
                    viewUsers=(
                        [
                            NodeReference(externalId=owner_id, space=um_space)
                            for owner_id in item_request.view_users
                        ]
                        if item_request.view_users and item_request.private
                        else None
                    ),
                    viewRoles=(
                        [
                            NodeReference(externalId=owner_id, space=um_space)
                            for owner_id in item_request.view_roles
                        ]
                        if item_request.view_roles and item_request.private
                        else None
                    ),
                    viewTeams=(
                        [
                            NodeReference(externalId=owner_id, space=um_space)
                            for owner_id in item_request.view_teams
                        ]
                        if item_request.view_teams and item_request.private
                        else None
                    ),
                    views=(item_request.views if item_request.views else None),
                    sourceEventHistory=[
                        SourceEventHistory(
                            externalId=self._id_generator.next_id(
                                EntitiesEnum.SourceEventHistory,
                            ),
                            space=aim_space,
                            sourceEvent=NodeReference(
                                externalId=external_id,
                                space=aim_space,
                            ),
                            status="created" if not is_update else "editedBy",
                            comments="",
                            updateBy=NodeReference(
                                externalId=item_request.created_by_id,
                                space=um_space,
                            ),
                            changedAt=str(now_str),
                        ),
                    ],
                    equipments=(
                        [
                            NodeReference(
                                externalId=equipment_id,
                                space=item_request.sap_space,
                            )
                            for equipment_id in item_request.equipments_id
                        ]
                        if item_request.equipments_id
                        else None
                    ),
                    functionalLocations=(
                        [
                            NodeReference(
                                externalId=functional_location_id,
                                space=item_request.sap_space,
                            )
                            for functional_location_id in item_request.functional_locations_id
                        ]
                        if item_request.functional_locations_id
                        else None
                    ),
                    sortReportingUnit=sort_mapper.get_reporting_unit_sort_value(
                        item_request.reporting_unit_id,
                    ),
                    sortReportingLocation=sort_mapper.get_reporting_location_sort_value(
                        item_request.reporting_location_id,
                    ),
                    sortStatus=sort_mapper.get_source_event_status_sort_value(
                        SourceEventStatus.IN_PROGRESS.value,
                    ),
                    sortCategory=sort_mapper.get_category_sort_value(
                        item_request.category_id,
                    ),
                    sortSubCategory=sort_mapper.get_sub_category_sort_value(
                        item_request.sub_category_id,
                    ),
                    sortSiteSpecificCategory=sort_mapper.get_site_specific_category_sort_value(
                        item_request.site_specific_category_id,
                        get_transactional_space_from_site_id(
                            item_request.reporting_site_id,
                        ),
                    ),
                    sortTitle=sort_mapper.get_normalized_sort_value(item_request.title),
                    searchTags=(
                        [
                            *item_request.equipments_id,
                            *item_request.functional_locations_id,
                        ]
                    ),
                )
            except ValidationError as err:
                msg = f"Invalid source event request from Title: {item_request.title}. Error: {err}. Skipping."
                self._log.error(msg)
                errors_by_event_id[external_id].append(msg)
                continue

            attachments = []
            if item_request.attachment_ids:
                received_files_metadata = self._cognite_service.get_files_metadata(
                    item_request.attachment_ids,
                )
                for file_id in item_request.attachment_ids or []:
                    file_metadata = received_files_metadata.get(file_id)
                    if file_metadata is None:
                        errors_by_event_id[external_id].append(
                            f"File {file_id} given as attachment was not found.",
                        )
                        continue

                    attachments.append(
                        Attachment(
                            received_external_id=file_id,
                            name=file_metadata.name,
                            mime_type=file_metadata.mime_type,
                            target_external_id=file_id,
                            target_dataset_id=aim_dataset_id,
                            file_size=(
                                file_metadata.metadata.get("fileSize")
                                if file_metadata.metadata
                                else None
                            ),
                            user=(
                                file_metadata.metadata.get("user")
                                if file_metadata.metadata
                                else None
                            ),
                        ),
                    )
            attachments_by_event_id[external_id] = attachments

            self._process_attachments(attachments, source_event)
            source_event_to_create_by_event_id[external_id] = source_event

            if len(source_event_to_create_by_event_id) == 0:
                self._log.info("No valid source event to create.")
                return [], errors_by_event_id

            matching_event = source_event_dict.get(external_id)
            current_attachments = matching_event.attachments if matching_event else None

            for external_id, attachments_list in attachments_by_event_id.items():
                if errors_by_event_id.get(external_id):
                    continue

                try:
                    for attachment in attachments_list:
                        attachment_already_exists = current_attachments and any(
                            attachment.target_external_id == a.external_id
                            for a in current_attachments
                        )
                        if attachment_already_exists:
                            continue

                        metadata = {
                            FileMetadataKey.RELATED_EVENTS.value: json.dumps(
                                attachment.source_event_external_ids,
                            ),
                            FileMetadataKey.FILE_SIZE.value: json.dumps(
                                attachment.file_size,
                            ),
                            FileMetadataKey.USER.value: json.dumps(attachment.user),
                        }

                        self._cognite_service.set_file_metadata(
                            attachment.received_external_id,
                            metadata,
                        )

                except Exception as err:
                    self._log.error(
                        f"Could not upload attachments for event {external_id}. Error: {err}",
                    )
                    errors_by_event_id[external_id].append(str(err))

            source_event_to_create: list[SourceEvent] = []
            for external_id, source_event in source_event_to_create_by_event_id.items():
                if len(errors_by_event_id[external_id]) > 0:
                    continue

                source_event_to_create.append(source_event)
                source_event_external_ids.append(source_event.externalId or "")

            if is_update:
                if isinstance(item_request, UpdateSourceEventRequest):
                    edges_to_delete = item_request.build_edges_to_delete()
                    self._cognite_service.delete_edges(edges_to_delete)
                    if item_request.files_ids_to_delete:
                        self._cognite_service.delete_files(
                            item_request.files_ids_to_delete,
                        )

                if matching_event:
                    actions_to_update = [
                        ActionItemUpdate(
                            external_id=action.external_id,
                            space=action.space,
                            sort_source_event_title=sort_mapper.get_normalized_sort_value(
                                item_request.title,
                            ),
                            views=(
                                self.generate_views(
                                    action,
                                    item_request.views or [],
                                )
                                if action.is_private
                                else None
                            ),
                        )
                        for action in matching_event.actions
                    ]

            try:
                self._log.info(
                    f"Upserting {len(source_event_to_create)} source events.",
                )
                _, err = self._cognite_service.upsert_source_events(
                    source_event_to_create,
                    update_attachments=True,
                )

                if len(err) > 0:
                    await self._cleanup()
                    self._log.error(f"Could not upsert source events. Errors: {err}.")
                    self._cognite_service.delete_files(
                        flat_map(
                            lambda atts: [file.target_external_id for file in atts],
                            [
                                attachments_by_event_id[key]
                                for key in attachments_by_event_id
                            ],
                        ),
                    )
                    return [], {"internal": err}

                if is_update and actions_to_update:
                    self._log.info(f"Upserting {len(actions_to_update)} action items.")
                    self._action_item_client.actions.upsert_action_item(
                        actions_to_update,
                    )

            except Exception as err:
                self._log.error(f"Could not upsert source events. Error: {err}")
                return [], {"internal": [str(err)]}

            await self._cleanup()

        return source_event_external_ids, errors_by_event_id

    def generate_views(
        self,
        action: Action,
        source_event_views: list[str],
    ) -> Optional[list[str]]:
        """Generate the `views` field by aggregating unique user, role, and team externalIds from the action and the source event."""

        def extract_ids(
            nodes: Optional[list[Node]],
            is_user: bool = False,
        ) -> list[str]:
            """Extract IDs from nodes, applying `to_user_azure_attribute` if `is_user` is True."""
            if is_user:
                return [to_user_azure_attribute(ref.external_id) for ref in nodes or []]
            return [ref.external_id for ref in nodes or []]

        def extract_approval_user_ids() -> list[str]:
            if not action.approval_workflow or not action.approval_workflow.steps:
                return []
            return [
                to_user_azure_attribute(user.external_id)
                for step in action.approval_workflow.steps or []
                for user in step.users or []
            ]

        user_ids = (
            extract_ids(action.view_users, is_user=True)
            + extract_approval_user_ids()
            + [
                ref.external_id
                for ref in [action.owner, action.assigned_to]
                if ref and ref.external_id
            ]
        )

        role_ids = extract_ids(action.view_roles)
        team_ids = extract_ids(action.view_teams)

        combined_views = list(
            set(user_ids + role_ids + team_ids + (source_event_views or [])),
        )
        return combined_views or None

    async def _cleanup(self) -> None:
        """Clean up resources by calling the cleanup method on the GraphQL service."""
        await self._graphql_service.cleanup()

    def _get_parsed_requests(
        self,
        raw_requests: list[dict],
        is_update: bool,
        errors_by_event_id: dict,
    ) -> tuple[
        list[Union[CreateSourceEventRequest, UpdateSourceEventRequest]],
        SortMapper,
    ]:
        parsed_requests: list[
            Union[CreateSourceEventRequest, UpdateSourceEventRequest]
        ] = []

        reporting_unit_external_ids: list[str] = []
        reporting_location_external_ids: list[str] = []
        category_external_ids: list[str] = []
        sub_category_external_ids: list[str] = []
        site_specific_category_instances: list[tuple[str, str]] = []

        for raw_request in raw_requests:
            external_id = (
                raw_request.get("externalId")
                if is_update
                else self._id_generator.next_id(EntitiesEnum.SourceEvent)
            )

            errors_by_event_id[external_id] = []
            self._log.info(f"Processing source event request from event {external_id}.")

            try:
                self._log.info(f"Validating request for event {external_id}.")
                parsed_request = (
                    UpdateSourceEventRequest(**raw_request)
                    if is_update
                    else CreateSourceEventRequest(**raw_request)
                )

                parsed_request.target_external_id = external_id

                if parsed_request.reporting_unit_id is not None:
                    reporting_unit_external_ids.append(parsed_request.reporting_unit_id)

                if parsed_request.reporting_location_id is not None:
                    reporting_location_external_ids.append(
                        parsed_request.reporting_location_id,
                    )

                if parsed_request.category_id is not None:
                    category_external_ids.append(parsed_request.category_id)

                if parsed_request.sub_category_id is not None:
                    sub_category_external_ids.append(parsed_request.sub_category_id)

                if parsed_request.site_specific_category_id is not None:
                    site_specific_category_instances.append(
                        (
                            get_transactional_space_from_site_id(
                                parsed_request.reporting_site_id,
                            ),
                            parsed_request.site_specific_category_id,
                        ),
                    )

                parsed_request.impacted_units_id = list(
                    set(parsed_request.impacted_units_id),
                )
                parsed_request.impacted_locations_id = list(
                    set(parsed_request.impacted_locations_id),
                )
                parsed_request.secondary_owner_users_id = list(
                    set(parsed_request.secondary_owner_users_id),
                )
                parsed_request.secondary_owner_roles_id = list(
                    set(parsed_request.secondary_owner_roles_id),
                )
                parsed_request.secondary_owner_teams_id = list(
                    set(parsed_request.secondary_owner_teams_id),
                )

                parsed_requests.append(parsed_request)

            except ValidationError as err:
                msg = f"Invalid request from EventId: {external_id}. Error: {err}. Skipping."
                self._log.error(msg)
                errors_by_event_id[external_id].append(msg)
                continue

        return parsed_requests, self._action_item_client.sort_mapper.get_sort_mapper(
            GetSortMapperRequest(
                reporting_unit_external_ids=reporting_unit_external_ids,
                reporting_location_external_ids=reporting_location_external_ids,
                category_external_ids=category_external_ids,
                sub_category_external_ids=sub_category_external_ids,
                site_specific_category_instances=site_specific_category_instances,
            ),
        )

    @staticmethod
    def _process_attachments(
        attachments: list[Attachment],
        source_event: SourceEvent,
    ) -> None:
        """Process a list of attachments and associates them with a source event (SourceEvent)."""
        if not attachments:
            source_event.attachments = None
            return

        source_event.attachments = []
        for attachment in attachments:
            if source_event.externalId:
                attachment.source_event_external_ids.append(source_event.externalId)

            if attachment.target_external_id:
                source_event.attachments.append(attachment.target_external_id)
