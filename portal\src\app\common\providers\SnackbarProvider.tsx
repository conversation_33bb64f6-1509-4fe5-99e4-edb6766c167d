import { useState, useCallback, ReactNode } from 'react'
import Snackbar, { SnackbarOrigin } from '@mui/material/Snackbar'
import Alert from '@mui/material/Alert'
import { Severity, SnackbarContext } from '../contexts/SnackbarContext'

const defaultPosition: SnackbarOrigin = { vertical: 'bottom', horizontal: 'left' }
const defaultAutoHideDuration = 6000

const severityStyles = {
    info: { backgroundColor: 'info.background', color: 'info.content', iconColor: 'info.main' },
    success: { backgroundColor: 'success.background', color: 'success.content', iconColor: 'success.main' },
    warning: { backgroundColor: 'warning.background', color: 'warning.content', iconColor: 'warning.main' },
    error: { backgroundColor: 'error.background', color: 'error.content', iconColor: 'error.main' },
}

export const SnackbarProvider = ({ children }: { children: ReactNode }) => {
    const [snackbarState, setSnackbarState] = useState({
        open: false,
        message: '',
        severity: 'info' as Severity,
        customId: 'default',
        autoHideDuration: defaultAutoHideDuration,
        position: defaultPosition,
    })

    const showSnackbar = useCallback(
        (
            msg: string,
            sev: Severity = 'info',
            id: string | null = 'default',
            duration: number | null = defaultAutoHideDuration,
            pos: SnackbarOrigin = defaultPosition
        ) => {
            setSnackbarState({
                open: true,
                message: msg,
                severity: sev,
                customId: id || 'default',
                autoHideDuration: duration || defaultAutoHideDuration,
                position: pos || defaultPosition,
            })
        },
        []
    )

    const handleCloseAlert = (_event?: React.SyntheticEvent | Event, reason?: string) => {
        if (reason === 'clickaway') return
        setSnackbarState((prevState) => ({ ...prevState, open: false }))
    }

    return (
        <SnackbarContext.Provider value={{ showSnackbar, handleCloseAlert }}>
            {children}
            <Snackbar
                open={snackbarState.open}
                onClose={handleCloseAlert}
                autoHideDuration={snackbarState.autoHideDuration}
                id={`alert-${snackbarState.severity}-${snackbarState.customId}`}
                anchorOrigin={snackbarState.position}
            >
                <Alert
                    onClose={handleCloseAlert}
                    severity={snackbarState.severity}
                    sx={{
                        width: '100%',
                        ...severityStyles[snackbarState.severity],
                        '& .MuiAlert-icon': {
                            color: severityStyles[snackbarState.severity].iconColor,
                        },
                    }}
                >
                    {snackbarState.message}
                </Alert>
            </Snackbar>
        </SnackbarContext.Provider>
    )
}
