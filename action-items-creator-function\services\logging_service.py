import logging
from datetime import UTC, datetime


class LoggingService:
    """Service for structured logging with execution ID and timestamp."""

    def __init__(self, call_id: str) -> None:
        """Initialize the logger with a unique execution ID."""
        self.execution_id = call_id

    def info(self, message: str) -> None:
        """Log an info-level message."""
        logging.info(self._format_message(message))

    def error(self, message: str) -> None:
        """Log an error-level message."""
        logging.error(self._format_message(message))

    def warning(self, message: str) -> None:
        """Log a warning-level message."""
        logging.warning(self._format_message(message))

    def exception(self, message: str) -> None:
        """Log an exception with traceback."""
        logging.exception(self._format_message(message))

    def _format_message(self, message: str) -> str:
        """Format the message with execution ID and UTC timestamp."""
        return f"[ExecutionId: {self.execution_id} - Timestamp: {datetime.now(tz=UTC).isoformat()}] - {message}"

    def disable_logs_temporarily(self) -> None:
        """Temporarily disable all logging."""
        logging.disable(logging.CRITICAL)

    def enable_logs(self) -> None:
        """Re-enable logging after being disabled."""
        logging.disable(logging.NOTSET)
