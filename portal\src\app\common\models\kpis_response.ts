export interface KpisResponseData {
    totalActionItems: string
    totalActionClosed: string
    assignedToMe: string
    assignedToMeClosed: string
    relatedToMe: string
    pendingApprovals: string
    pendingVerifications: string
    overdue: string
    myApprovals: string
    myVerifications: string
    myChallenges: string
    myExtends: string
    myReassignment: string
}

export interface KpisFilter {
    reportingSiteExternalId: string[]
    activeUserEmail: string
    reportingUnitExternalIds?: string[]
    permissionsExtend: boolean
    permissionsReassing: boolean
    extensionApprovalSiteExternalIds?: string[]
    reassignmentApprovalSiteExternalIds?: string[]
    activeUserRolesIds?: string[]
    activeUserTeamsIds?: string[]
}

export interface SiteTabKpisResponseData {
    totalEntered: number
    overdue: number
    open: number
    pending: number
    overduePercentage: number
}

export interface SupervisorTabKpisResponseData {
    employees: number
    overdue: number
    tasksWithin7Days: number
    tasksWithinThisMonth: number
    dueDateOver30Days: number
    pendingApproval: number
    pendingVerification: number
}
