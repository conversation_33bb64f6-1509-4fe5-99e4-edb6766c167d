from typing import Callable, Optional, TypeVar


def get_user_id_from_user_azure_attribute_id(
    user_azure_attribute_id: Optional[str],
) -> Optional[str]:
    """Extract user ID by removing the prefix 'UserAzureAttribute_'."""
    return (
        user_azure_attribute_id.replace("UserAzureAttribute_", "")
        if user_azure_attribute_id
        else None
    )


def generate_query_only_with_node_reference(
    list_name: str,
    extra_selection: str = "",
) -> str:
    """Generate a query with only externalId, space, and additional selected fields."""
    items = f"""
        externalId
        space
        {extra_selection}
    """
    return generate_query(list_name, items)


def generate_query(list_name: str, selected_items: str) -> str:
    """Generate a GraphQL query for a given list name and selected fields."""
    return f"""
        query Query($first: Int, $after: String, $filter: _{list_name[0].upper() + list_name[1:]}Filter) {{
            {list_name}(first: $first, after: $after, filter: $filter) {{
                items {{
                    {selected_items}
                }}
                pageInfo {{
                    hasNextPage
                    endCursor
                }}
            }}
        }}
    """


T = TypeVar("T")
O = TypeVar("O")


def flat_map(f: Callable[[list[T]], list[O]], xs: list[list[T]]) -> list[O]:
    """Apply a function to a nested list and flattens the result."""
    ys: list[O] = []
    for x in xs:
        ys.extend(f(x))
    return ys


if __name__ == "__main__":
    print(
        get_user_id_from_user_azure_attribute_id(
            "<EMAIL>",
        ),
    )

    print(
        flat_map(
            lambda xs: [x["item"] for x in xs],
            [[{"item": 1}, {"item": 2}], [{"item": 3}, {"item": 4}]],
        ),
    )
