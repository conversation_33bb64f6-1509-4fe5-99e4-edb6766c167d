import { ClnButton } from '@celanese/ui-lib'
import { Checkbox, FormControlLabel, Grid, Typography, Box } from '@mui/material'
import { useState } from 'react'
import { IconWithModal } from '@/app/common/utils/icons-helper'
import { PrivateSettingsModal, Viewers } from '../UserComponent/privateSettingsModal'
import { Team } from '@/app/common/models/common/user-management/team'
import { Role } from '@/app/common/models/common/user-management/role'
import { User } from '@/app/common/models/common/user-management/user'
import * as styles from './styles'
import { translate } from '@/app/common/utils/generate-translate'

type PrivateComponentProps = {
    siteId: string
    disabled?: boolean
    isFromActionForm?: boolean
    isChecked: boolean
    setIsChecked: (value: boolean) => void
    selectedUsersViewers: User[]
    selectedRolesViewers: Role[]
    selectedTeamsViewers: Team[]
    onSave: (value: Viewers) => void
}

export default function PrivateComponent({
    siteId,
    disabled = false,
    isFromActionForm = false,
    isChecked,
    setIsChecked,
    selectedUsersViewers,
    selectedRolesViewers,
    selectedTeamsViewers,
    onSave,
}: PrivateComponentProps) {
    const [privateSettingsModalOpen, setPrivateSettingsModalOpen] = useState(false)

    const labelPrivate = isFromActionForm ? 'Action' : 'Event'

    const labelButton = translate('privateComponent.privateSettingsButton').replace(
        /\{\}/,
        translate(`privateComponent.${labelPrivate}`)
    )

    const handleToggleModal = () => setPrivateSettingsModalOpen((prev) => !prev)

    const handleSavePrivateSettings = (viewers: Viewers) => {
        onSave(viewers)
        setPrivateSettingsModalOpen(false)
    }

    const renderPrivateInfoModalContent = () => (
        <Box marginRight={2}>
            <ul>
                {Array.from({ length: isFromActionForm ? 5 : 4 }).map((value, i) => (
                    <li key={i}>
                        <Typography key={i} sx={{ fontWeight: 500, fontSize: '16px' }}>
                            {translate(`privateComponent.tooltipPrivate${labelPrivate}List${i + 1}`)}
                        </Typography>
                    </li>
                ))}
            </ul>
            {isFromActionForm && <p>{translate(`privateComponent.tooltipPrivate${labelPrivate}List6`)}</p>}
        </Box>
    )

    return (
        <Box sx={{ width: '100% !important', marginTop: '0px !important' }}>
            <Grid item xs={12} id="private-forms-new-events">
                <Box sx={styles.boxCenterItems}>
                    <FormControlLabel
                        control={
                            <Checkbox
                                checked={isChecked}
                                disabled={disabled}
                                onChange={(e) => setIsChecked(e.target.checked)}
                            />
                        }
                        label={translate('source-event.checkBoxPrivate')}
                        data-test="new_action_item_flow_3-mark_as_private_checkbox"
                        data-origin="aim"
                    />
                    <IconWithModal
                        title={translate('source-event.checkBoxPrivate')}
                        fontSizeIcon="24px"
                        secondaryTitle={translate(`privateComponent.tooltipPrivate${labelPrivate}Text`)}
                        content={renderPrivateInfoModalContent()}
                        id="new_action_item_flow_3-mark_as_private_info_button"
                    />
                </Box>
                <ClnButton
                    disabled={!isChecked}
                    label={labelButton}
                    variant="contained"
                    style={{ width: '100%' }}
                    size="small"
                    onClick={handleToggleModal}
                    data-test="new_action_item_flow_3-private_event_settings_button"
                    data-origin="ui-lib"
                />
            </Grid>
            {privateSettingsModalOpen && (
                <PrivateSettingsModal
                    siteId={siteId}
                    selectedUsersViewers={selectedUsersViewers}
                    selectedRolesViewers={selectedRolesViewers}
                    selectedTeamsViewers={selectedTeamsViewers}
                    onClose={handleToggleModal}
                    onSave={handleSavePrivateSettings}
                    labelPrivate={labelPrivate}
                />
            )}
        </Box>
    )
}
