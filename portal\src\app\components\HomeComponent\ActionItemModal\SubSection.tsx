import { Grid } from '@mui/material'
import { ModalDataSubsection } from './modalData'
import { Column } from './Column'
import { SubsectionContainer, SubsectionTitle, SubtitleContainer } from './styles'
import GenericFieldTitle from '../../FieldsComponent/GenericFieldTitle'
import { FieldItem } from './FieldItem'

export function SubSection({ title, subtitle, externalId, columns, rows }: ModalDataSubsection) {
    return (
        <SubsectionContainer>
            {title && <SubsectionTitle>{title}</SubsectionTitle>}
            {subtitle && (
                <SubtitleContainer>
                    <GenericFieldTitle isDetailsExternalId fieldName={subtitle} />
                    <GenericFieldTitle isDetailsExternalId isBold fieldName={externalId ?? 'N/A'} />
                </SubtitleContainer>
            )}
            <>
                <Grid container spacing={2}>
                    {columns.map((col, index) => (
                        <Column
                            key={`action-modal-column-${index.toString()}`}
                            title={col.title}
                            fields={col.fields}
                            columnCount={columns.length}
                        />
                    ))}
                </Grid>
                <Grid container spacing={2}>
                    {rows?.map((row, index) => (
                        <Grid
                            item
                            xs={12}
                            key={`action-modal-row-${index.toString()}`}
                            sx={{ paddingTop: '10px !important' }}
                        >
                            <FieldItem label={row.label} value={row.value} />
                        </Grid>
                    ))}
                </Grid>
            </>
        </SubsectionContainer>
    )
}
