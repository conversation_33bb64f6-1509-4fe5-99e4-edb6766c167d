import { ClnButton } from '@celanese/ui-lib'
import { Grid, Box } from '@mui/material'
import { useState } from 'react'
import { SecondaryOwnersModal, SecondaryOwners } from '../UserComponent/secondaryOwnersModal'
import { Team } from '@/app/common/models/common/user-management/team'
import { Role } from '@/app/common/models/common/user-management/role'
import { User } from '@/app/common/models/common/user-management/user'
import { translate } from '@/app/common/utils/generate-translate'

type SecondaryOwnersProps = {
    siteId: string
    selectedUsers: User[]
    selectedRoles: Role[]
    selectedTeams: Team[]
    onSave: (value: SecondaryOwners) => void
}

export default function ManageSecondaryOwners({
    siteId,
    selectedUsers,
    selectedRoles,
    selectedTeams,
    onSave,
}: SecondaryOwnersProps) {
    const [secondaryOwnersModalOpen, setSecondaryOwnersModalOpen] = useState(false)

    const handleToggleModal = () => setSecondaryOwnersModalOpen((prev) => !prev)

    const handleSaveSecondaryOwners = (secondaryOwners: SecondaryOwners) => {
        onSave(secondaryOwners)
        setSecondaryOwnersModalOpen(false)
    }

    return (
        <Box sx={{ width: '100% !important', marginTop: '0px !important' }}>
            {
                <Grid item xs={12} id="private-forms-new-events">
                    <ClnButton
                        label={translate('source-event.addAndManageSecondaryOwners')}
                        variant="outlined"
                        size="small"
                        onClick={handleToggleModal}
                        data-test="new_action_item_flow_3-secodary_owners_settings_button"
                        data-origin="ui-lib"
                        style={{ width: '100%' }}
                    />
                </Grid>
            }
            {secondaryOwnersModalOpen && (
                <SecondaryOwnersModal
                    siteId={siteId}
                    selectedUsers={selectedUsers}
                    selectedRoles={selectedRoles}
                    selectedTeams={selectedTeams}
                    onClose={handleToggleModal}
                    onSave={handleSaveSecondaryOwners}
                />
            )}
        </Box>
    )
}
