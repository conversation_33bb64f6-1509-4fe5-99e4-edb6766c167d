import { PlantItem } from '@celanese/ui-lib'
import { WorkItemTypeEnum } from '../enums/WorkItemTypeEnum'

export interface WorkItemBodyInformation {
    title: string
    description?: string
    application: string
    itemType: WorkItemTypeEnum
    attachments?: File[]
    useCase: string
    siteAccessed?: string
    priority?: string
    incident?: string
    jobTitle?: string
    stepByStep?: string
    previouslyFunctioning?: string
    expectedFunctionality?: string
    typeOfRequest?: string
    action?: string
    objective?: string
    userEmail?: string
    sites: PlantItem[]
    impactedSites?: string[]
}
