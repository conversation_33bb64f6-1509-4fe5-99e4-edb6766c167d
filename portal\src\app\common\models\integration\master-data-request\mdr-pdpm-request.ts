import { User } from '../../action'
import { Equipment } from '../../common/asset-hierarchy/equipment'
import { ReportingUnit } from '../../reporting-unit'
import { ApprovalWorkflow, MDRCriticalityType, FunctionalLocation } from './mdr-equipment-request'

export interface MDRPdpmRequestHeader {
    requestor: User | null
    plantArea: ReportingUnit | null
    requestDate: Date | null
    miqaReliabilityLead: User | null
    isDraft: boolean
    createdBy: User | null
    approvalWorkflow: ApprovalWorkflow | null
}

export interface MDRPdpmRequest {
    header: MDRPdpmRequestHeader | null
    basicInfo: MDRPdpmBasicInfo | null
    isDraft: boolean | null
    createdBy: User | null
    requestType: string | null
    itemsObjects: MDRPdpmItemObject[]
}

export interface MDRPdpmBasicInfo {
    pdpmType: MDRPdpmType | null
    activityReason: string | null
    newDueDate: Date | null
    newFrequency: number | null
    leadTime: number | null
    sapActivityType: MDRActivityType | null
    activityDescription: string | null
    activitySkill: MDRActivitySkill | null
    equipShutdown: MDREquipShutdown | null
    justification: string | null
    mp: number | null
    mpDescription: string | null
    criticatily: MDRCriticalityType | null
    toWhatRegulation: string | null
    isthisUrgent: boolean | null
}

export interface MDRActivityType {
    name: string
    description: string
}

export interface MDRActivitySkill {
    name: string
    description: string
}

export interface MDREquipShutdown {
    name: string
    description: string
}

export interface MDRPdpmItemObject {
    functionalLocation: FunctionalLocation | null
    area: ReportingUnit | null
    equipment: Equipment | null
    isNewEquipment: boolean | null
    technicalId: string | null
    objectsToAdd: string[] | null
    itemsToAdd: string[] | null
}

export interface MDRPdpmType {
    name: string
    description: string
}
