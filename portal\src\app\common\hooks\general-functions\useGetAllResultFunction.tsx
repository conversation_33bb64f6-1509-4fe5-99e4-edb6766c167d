import { LazyQueryHookOptions, gql, useLazyQuery } from '@apollo/client'
import { useCallback, useMemo } from 'react'
import { Filter } from '../../models/base-hook-request'
import { PaginatedQueryResult } from '../../models/paginated-query-result'
import { MAX_COGNITE_GRAPHQL_BATCH_SIZE, toFirstLetterUpperCased } from '../../utils'
import { createApolloClient } from '../../factories/apollo-client-factory'
import { useAuthToken } from '../cognite/useAuthToken'

function getPaginatedQuery(itemSelection: string, listName: string, sortName?: string, sortType?: string) {
    const listNameWithFirstLetterUpperCased = toFirstLetterUpperCased(listName)
    let sortClause = ''

    if (sortName) {
        sortClause = `, sort: {${sortName}: ${sortType}}`
    }
    return gql`
        query ${listNameWithFirstLetterUpperCased}($filter: _${listNameWithFirstLetterUpperCased}Filter, $first: Int, $after: String) {
            ${listName}(filter: $filter, first: $first, after: $after${sortClause}) {
                items {
                    ${itemSelection}
                }
                pageInfo{
                    hasNextPage
                    endCursor
                }
            }
        }
    `
}

export function useGetAllResultsFunction<T>(
    itemSelection: string,
    listName: string,
    sortName?: string,
    sortType?: string
) {
    const [getResults] = useLazyQuery<PaginatedQueryResult<T>>(
        getPaginatedQuery(itemSelection, listName, sortName, sortType)
    )
    return {
        getAllResults: useCallback(
            async (filter?: Filter<T>, options?: LazyQueryHookOptions): Promise<T[]> => {
                let allItems: T[] = []
                let hasNextPage = true
                let after: string | undefined
                while (hasNextPage) {
                    const res = await getResults({
                        ...options,
                        fetchPolicy: 'no-cache',
                        variables: { filter, first: MAX_COGNITE_GRAPHQL_BATCH_SIZE, after },
                    })
                    const data = !!res?.data
                        ? res.data[listName]
                        : { items: [], pageInfo: { hasNextPage: false, endCursor: '' } }
                    allItems = allItems.concat(data?.items)
                    hasNextPage = data?.pageInfo.hasNextPage
                    after = data?.pageInfo?.endCursor
                }
                return allItems
            },
            [getResults, listName]
        ),
    }
}

export function useGetAllResultsFunctionFromCustomClient<T>(
    itemSelection: string,
    listName: string,
    useCaseIdentifier: string,
    sortName?: string,
    sortType?: string
) {
    const { getAuthToken } = useAuthToken()
    const client = useMemo(() => createApolloClient(getAuthToken, useCaseIdentifier), [getAuthToken])
    const query = getPaginatedQuery(itemSelection, listName, sortName, sortType)

    return {
        getAllItems: useCallback(
            async (filter?: Filter<T>, options?: LazyQueryHookOptions): Promise<T[]> => {
                let hasNextPage = true
                let after: string | undefined
                let allItems: T[] = []

                while (hasNextPage) {
                    const res = await client.query({
                        query: query,
                        fetchPolicy: 'no-cache',
                        variables: {
                            filter,
                            first: MAX_COGNITE_GRAPHQL_BATCH_SIZE,
                            after,
                        },
                    })

                    const data = !!res?.data
                        ? res.data[listName]
                        : { items: [], edges: [], pageInfo: { hasNextPage: false, endCursor: '' } }

                    allItems = allItems.concat(data?.items)
                    hasNextPage = data?.pageInfo.hasNextPage
                    after = data?.pageInfo?.endCursor
                }
                return allItems
            },
            [client, listName, query]
        ),
    }
}
