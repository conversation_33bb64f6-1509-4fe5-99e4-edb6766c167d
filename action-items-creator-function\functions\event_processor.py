import azure.functions as func


bp = func.Blueprint()


@bp.function_name(name="EventProcessor")
@bp.schedule(arg_name="timer", schedule="%EVENT_PROCESSOR_CRON%")
async def main(timer: func.TimerRequest):
    import logging
    import os
    import sys

    script_dir = os.path.dirname(os.path.abspath(__file__))
    project_dir = os.path.dirname(script_dir)
    sys.path.append(project_dir)
    from infra.action_item_event_processor_factory import (
        ActionItemEventProcessorFactory,
    )

    if timer.past_due:
        logging.info("The timer is past due.")

    logging.info(await ActionItemEventProcessorFactory.create().execute())
