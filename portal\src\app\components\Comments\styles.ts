import { CSSObject } from '@emotion/react'

export const Container: CSSObject = {
    display: 'flex',
    flex: 1,
    flexDirection: 'column',
    justifyContent: 'space-between',
    height: '100%',
}

export const Header: CSSObject = {
    gap: 1,
    display: 'flex',
    flexDirection: 'column',
    flex: 1,
    minHeight: 0,
}

export const CommentsContainer: CSSObject = {
    flex: 1,
    minHeight: 0,
    overflowY: 'auto',
    gap: '1rem',
    display: 'flex',
    flexDirection: 'column',
    pr: 1.5,
    mr: -1.5,
    '&::-webkit-scrollbar': {
        width: '8px',
    },
    '&::-webkit-scrollbar-thumb': {
        backgroundColor: 'grey.300',
        borderRadius: '4px',
    },
    '@media (max-width: 600px)': {
        maxHeight: 'none',
        height: '100%',
        marginTop: '8px',
        pr: 0,
        mr: 0,
    },
}

export const CommentsContainerModal: CSSObject = {
    height: '490px',
    width: '872px',
    overflowY: 'auto',
    gap: '1rem',
    display: 'flex',
    flexDirection: 'column',
    '&::-webkit-scrollbar': {
        width: '8px',
    },
    '&::-webkit-scrollbar-thumb': {
        backgroundColor: 'grey.300',
        borderRadius: '4px',
    },
    '@media (max-width: 600px)': {
        maxHeight: 'none',
        height: '100%',
        marginTop: '8px',
        width: 'auto',
    },
}

export const CommentsContainerModalEmpty: CSSObject = {
    height: '490px',
    width: '872px',
    overflowY: 'auto',
    gap: '1rem',
    display: 'flex',
    flexDirection: 'column',
    backgroundColor: 'background.default',
    justifyContent: 'center',
    '&::-webkit-scrollbar': {
        width: '8px',
    },
    '&::-webkit-scrollbar-thumb': {
        backgroundColor: 'grey.300',
        borderRadius: '4px',
    },
    '@media (max-width: 600px)': {
        maxHeight: 'none',
        height: '100%',
        marginTop: '8px',
        width: 'auto',
    },
}

export const CommentBox: CSSObject = {
    padding: '1.1rem',
    display: 'flex',
    gap: '10px',
    flexDirection: 'column',
    backgroundColor: 'background.default',
    width: '100%',
}

export const CommentFooter: CSSObject = {
    display: 'flex',
    justifyContent: 'space-between',
    flexWrap: 'wrap',
    gap: '8px',
}

export const CommentSourceDesktop: CSSObject = {
    textAlign: 'right',
    marginLeft: 'auto',
    wordBreak: 'break-word',
}

export const CommentPosted: CSSObject = {
    wordWrap: 'break-word',
    overflowWrap: 'break-word',
    maxWidth: '100%',
}

export const WriteComment: CSSObject = {
    borderRadius: '20px 20px 20px 20px',
    '& .MuiOutlinedInput-root': {
        borderRadius: '20px 0 0 20px',
        '& fieldset': {
            borderColor: '#C4C4C4',
            borderRightWidth: '0px',
        },
        '&:hover fieldset': {
            borderColor: '#C4C4C4',
            borderRightWidth: '0px',
        },
        '&.Mui-focused fieldset': {
            borderColor: '#C4C4C4',
            borderWidth: '1px',
            borderRightWidth: '0px',
        },
    },
    display: 'flex',
    flexGrow: 1,
}

export const SendButton: CSSObject = {
    height: 'auto',
    width: 'auto',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: '0 20px 20px 0',
    border: '1px solid #C4C4C4',
    borderLeftWidth: '0px',
    minWidth: 'auto',
    padding: '15px',
    '&:focus': {
        outline: 'none',
    },
    '& .MuiButton-root': {
        borderWidth: '1px',
    },
    flexGrow: 0,
    alignSelf: 'stretch',
    '& .MuiButton-startIcon': {
        marginRight: 0,
    },
}

export const CommentInDetails: CSSObject = {
    display: 'block',
    width: '100%',
    overflow: 'hidden',
    whiteSpace: 'nowrap',
    textOverflow: 'ellipsis',
    overflowWrap: 'break-word',
    wordWrap: 'break-word',
    maxWidth: '100%',
}
