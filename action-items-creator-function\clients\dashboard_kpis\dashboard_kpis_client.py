from typing import Any

from cognite.client.data_classes import data_modeling

from clients.core.constants import (
    AGGREGATE_LIMIT,
    LIMIT,
    ViewEnum,
)
from clients.core.models import ServiceParams

from .cognite_filters import get_common_kpis_filters, get_kpis_current_status_filter
from .requests import Get<PERSON>hartsRequest, GetDashboardKpisRequest


class DashboardKpisClient:
    """
    Client class for interacting with dashboard KPIs in a Cognite environment.

    This class provides methods for managing and retrieving dashboard KPIs
    and interacting with the views within a specific data model.
    """

    def __init__(self, params: ServiceParams) -> None:
        """
        Initialize an instance of DashboardKpisClient with the given parameters.

        Args:
            params (ServiceParams): The parameters containing the Cognite client and data model views.

        """
        self._cognite_client = params.cognite_client
        view_as_dict = {view.external_id: view for view in params.data_model.views}
        self._action_view = view_as_dict[ViewEnum.ACTION]
        self._user_azure_attribute_view = view_as_dict[ViewEnum.USER_AZURE_ATTRIBUTE]

    def get_actions(self, request: GetDashboardKpisRequest) -> dict[str, list]:
        """
        Retrieve actions based on the specified filters and criteria.

        This method fetches actions from the Cognite data model, applying common filters and KPI status filters,
        and returns them grouped under the key "generalActions". It supports pagination to ensure all actions are
        retrieved, even when they exceed the limit set for a single query.

        Args:
            request (GetDashboardKpisRequest): The request object containing the filtering criteria. This includes
                KPIs, status filters, and any additional parameters needed to fetch the appropriate actions.

        Returns:
            dict[str, list]: A dictionary with a key "generalActions" containing a list of actions retrieved from
                the Cognite data model. The list will include all actions that match the filtering criteria, and
                if pagination is required, additional actions will be fetched and added to the list until all actions
                are retrieved.

        """
        common_kpis_filters = get_common_kpis_filters(request, self._action_view)
        kpis_current_status_filter = get_kpis_current_status_filter(
            request,
            self._action_view,
        )

        select_action_clause = self._get_select_action_clause()

        with_clause = {
            "generalActions": data_modeling.query.NodeResultSetExpression(
                filter=data_modeling.filters.And(
                    *common_kpis_filters,
                    kpis_current_status_filter,
                ),
                limit=LIMIT,
            ),
        }

        select_clause_dict = {
            "generalActions": select_action_clause,
        }

        cursors = None
        all_data = {
            "generalActions": [],
        }

        while True:
            res = self._cognite_client.data_modeling.instances.query(
                data_modeling.query.Query(
                    with_=with_clause,
                    select=select_clause_dict,
                    cursors=cursors,
                ),
            )

            general_actions = res.get("generalActions", [])
            cursors = res.cursors

            all_data["generalActions"].extend(general_actions)
            general_actions_count = len(general_actions)

            if general_actions_count == 0 or general_actions_count < LIMIT:
                break

        return all_data

    def count_total_entered(self, request: GetDashboardKpisRequest) -> int:
        """
        Count the total number of actions that meet the specified criteria.

        This method aggregates actions based on the filters and criteria provided in the request. It uses the
        `externalId` field to count the actions that match the specified filters, which can include KPIs and
        other relevant parameters.

        Args:
            request (GetDashboardKpisRequest): The request object containing the filters and criteria used to
                narrow down the KPIs to be counted. This includes common KPI filters as well as any additional
                parameters necessary for the count.

        Returns:
            int: The total number of actions that match the provided filters and criteria. This count is based
                on the `externalId` field of the actions.

        """
        return int(
            self._cognite_client.data_modeling.instances.aggregate(
                view=self._action_view,
                aggregates=data_modeling.aggregations.Count("externalId"),
                filter=data_modeling.filters.And(
                    *get_common_kpis_filters(request, self._action_view),
                ),
            ).value,
        )

    def get_grouped_by_actions(self, request: GetChartsRequest) -> list[dict[str, Any]]:
        """
        Aggregate and groups actions by a specified criterion.

        This method aggregates actions based on the filters provided in the request and groups them by the
        specified criterion, such as a KPI or status. It supports pagination with a limit on the number of
        grouped results returned.

        Args:
            request (GetChartsRequest): The request object containing the grouping criteria and the number
                of items per page. This includes the grouping field and the filters to apply when grouping
                the actions.

        Returns:
            dict: A dictionary containing the aggregated data grouped by the specified criterion. The data
                is returned in a format that can be further processed or displayed in a chart.

        """
        limit = request.items_per_page or AGGREGATE_LIMIT
        data = self._cognite_client.data_modeling.instances.aggregate(
            view=self._action_view,
            aggregates=data_modeling.aggregations.Count("externalId"),
            filter=data_modeling.filters.And(
                *get_common_kpis_filters(request, self._action_view),
            ),
            group_by=request.group_by,
            limit=limit,
        )

        return data.dump()

    def _get_select_action_clause(self) -> data_modeling.query.Select:
        """
        Construct the SELECT clause for retrieving action properties.

        This method creates a `SELECT` clause that specifies the properties of actions to be retrieved,
        including the current status, display due date, reporting unit, and category. This clause is used
        when querying the data model to retrieve action details.

        Returns:
            Select: A `SELECT` clause specifying the desired properties for actions, such as `currentStatus`,
                    `displayDueDate`, `reportingUnit`, and `category`.

        """
        return data_modeling.query.Select(
            sources=[
                data_modeling.query.SourceSelector(
                    source=self._action_view,
                    properties=[
                        "currentStatus",
                        "displayDueDate",
                        "reportingUnit",
                        "category",
                    ],
                ),
            ],
        )
