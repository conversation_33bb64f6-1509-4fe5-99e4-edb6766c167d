import { Dayjs } from 'dayjs'
import { ExternalEntity } from '../../common'
import { ReportingLocation } from '../../common/asset-hierarchy/reporting-location'
import { ReportingUnit } from '../../reporting-unit'
import { User } from '../../common/user-management/user'

export interface MDREquipmentRequest extends ExternalEntity {
    header?: MDREquipmentRequestHeader
    basicInfo?: MDREquipmentRequestBasicInfo
    generalInfo?: MDREquipmentRequestGeneralInfo
    locationData?: MDREquipmentRequestLocationData
    itemsObjects?: MDRPdpmItemObject[]
}

export interface MDREquipmentRequestHeader extends ExternalEntity {
    requestor: User | null
    miqaReliabilityLead: User | null
    plantArea: ReportingUnit | null
    projectNumber: string
    fcrOriginator: User | null
    installDate: Date
    requestDate: Date | null
    safetyPpe: boolean | null
    isDraft: boolean
    createdBy: User | null
    approvalWorkflow: ApprovalWorkflow | null
}

export interface ApprovalWorkflowStatus extends ExternalEntity {
    name: string
    description: string | null
}

export interface ApprovalWorkflow extends ExternalEntity {
    currentStep: number
    startDate: Date
    endDate: Date | null
    createdBy: User | null
    description: string | null
    status: ApprovalWorkflowStatus | null
}

export interface MDREquipmentCriticality extends ExternalEntity {
    name: string
    description: string
    permit: string | null
}

export interface MDRCriticalityType extends ExternalEntity {
    name: string
    description: string
}

export interface MDREquipmentKeyNoun extends ExternalEntity {
    name: string
    description: string | null
    classes: MDREquipmentKeyNounClass[]
}

export interface MDREquipmentKeyNounClass extends ExternalEntity {
    name: string
    description: string | null
}

export interface MDREquipmentRequestBasicInfo extends ExternalEntity {
    newKeyNounNeeded: boolean | null
    newKeyNoun: string | null
    keyNoun: MDREquipmentKeyNoun | null
    description: string | null
    class: MDREquipmentKeyNounClass | null
    sapNumberEquipment: number | null
    additionalInto: string | null
    equipmanetCriticality: MDREquipmentCriticality | null
    typeOfCriticality: MDRCriticalityType | null
    permit: string | null
    replacingExistingEquipment: boolean | null
    replacedEquipment: string | null
    sparePartsRequired: boolean | null
    systemDescription: string | null
    sapOfSuperiorEquipment: string | null
    outOfService: boolean | null
    pdpmType: MDRPdpmType | null
    activityReason: string | null
    newDueDate: Date | null
    newFrequency: number | null
    sapActivityType: MDRActivityType | null
    activityDescription: string | null
    activitySkill: MDRActivitySkill | null
    equipShutdown: MDREquipShutdown
    justification: string | null
    leadTime: number | null
    criticatily: MDRCriticalityType | null
    isthisUrgent: boolean | null
    mpDescription: string | null
    toWhatRegulation: string | null
}

export interface MDREquipmentRequestGeneralInfo extends ExternalEntity {
    startupDate: Dayjs
    newManufacturerNeeded: boolean
}

export interface MDRPdpmType extends ExternalEntity {
    name: string | null
    description: string | null
}

export interface MDRActivityType extends ExternalEntity {
    name: string | null
    description: string | null
}

export interface MDRActivitySkill extends ExternalEntity {
    name: string | null
    description: string | null
}

export interface MDREquipShutdown extends ExternalEntity {
    name: string | null
    description: string | null
}

export interface FunctionalLocation extends ExternalEntity {
    name: string | null
}

export interface MDREquipmentRequestLocationData extends ExternalEntity {
    functionalLocation: FunctionalLocation | null
    newFunctionalLocationNeeded: boolean
    newFunctionalLocationDetails: string | null
    suggestedTechnicalId: string | null
    reportingLocation: ReportingLocation | null
}

export interface MDRPdpmItemObject extends ExternalEntity {
    functionalLocation: FunctionalLocation | null
    isNewEquipment: boolean | null
    objectsToAdd: string[] | null
    itemsToAdd: string[] | null
    equipment: Equipment | null
    technicalId: string | null
}

export interface FunctionalLocation extends ExternalEntity {
    name: string | null
}

export interface Equipment extends ExternalEntity {
    name: string | null
}
