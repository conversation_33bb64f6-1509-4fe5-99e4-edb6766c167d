import { useState } from 'react'
import { ModalWrapper } from '../ModalComponent/Modal/ModalWrapper'
import { Box, TextField, Typography } from '@mui/material'
import { Comment, Application } from '@/app/common/models/action-detail'
import * as S from './styles'
import dayjs from 'dayjs'
import { ClnButton, ClnPanel, MatIcon } from '@celanese/ui-lib'
import { translate } from '@/app/common/utils/generate-translate'

interface CommentsModalProps {
    onClose: () => void
    comments: Comment[]
    application?: Application
    onAddComment: (comment: string) => void
}

export function CommentsModal({ onClose, comments, application, onAddComment }: CommentsModalProps) {
    const [newComment, setNewComment] = useState('')
    const noComments = comments.length === 0

    const handleCancel = () => {
        onClose()
    }

    const handleAddComment = () => {
        if (newComment.trim()) {
            onAddComment(newComment)
            setNewComment('')
        }
    }

    return (
        <ModalWrapper
            title={translate('requestModal.comments')}
            openModal={true}
            closeModal={handleCancel}
            sxProps={{ backgroundColor: 'rgba(0, 0, 0, 0.5)', minHeight: '200px' }}
            sxPropsTitle={{
                fontSize: '24px',
                marginRight: 'auto',
                paddingLeft: '20px',
            }}
            content={
                <Box
                    id="comments-modal"
                    sx={{
                        px: '1.5rem',
                        pb: '1.5rem',
                    }}
                >
                    {noComments ? (
                        <Box sx={S.CommentsContainerModalEmpty}>
                            <Typography textAlign="center" color="text.secondary">
                                {translate('details.components.comments.noCommentsAvailable')}
                            </Typography>
                        </Box>
                    ) : (
                        <Box sx={S.CommentsContainerModal}>
                            {comments.map((comment, index) => {
                                const fullName = comment.user
                                    ? `${comment.user.firstName} ${comment.user.lastName}`
                                    : ''

                                return (
                                    <ClnPanel key={index} sx={S.CommentBox}>
                                        <Typography variant="body2" sx={S.CommentPosted}>
                                            {comment.comment}
                                        </Typography>

                                        <Box sx={S.CommentFooter}>
                                            {(comment.timestamp || comment.user) && (
                                                <Typography variant="caption">
                                                    {translate('details.components.comments.commentSent')}
                                                    {comment.timestamp &&
                                                        dayjs(comment.timestamp).format(' MM/DD/YYYY HH:mm')}
                                                    {comment.user && (
                                                        <>
                                                            {' '}
                                                            {translate('details.components.comments.by')}{' '}
                                                            <strong>{fullName}</strong>
                                                        </>
                                                    )}
                                                </Typography>
                                            )}

                                            {application && (
                                                <Typography variant="caption" textAlign={'right'} ml={'auto'}>
                                                    {comment.isLegacy ? (
                                                        <>
                                                            {translate(
                                                                'details.components.comments.historicalDataFrom'
                                                            )}{' '}
                                                            <strong>{application.name}</strong>
                                                        </>
                                                    ) : (
                                                        <>
                                                            {translate(
                                                                'details.components.comments.createdVia'
                                                            )}{' '}
                                                            <strong>{translate('app.boldTitle')}</strong>
                                                        </>
                                                    )}
                                                </Typography>
                                            )}
                                        </Box>
                                    </ClnPanel>
                                )
                            })}
                        </Box>
                    )}
                    <Box sx={{ display: 'flex', alignItems: 'flex-start', marginTop: '18px' }}>
                        <TextField
                            fullWidth
                            multiline
                            maxRows={3}
                            value={newComment}
                            onChange={(e) => setNewComment(e.target.value)}
                            placeholder={translate('details.components.comments.addAComment')}
                            sx={S.WriteComment}
                        />
                        <ClnButton
                            variant="text"
                            size="large"
                            startIcon={<MatIcon icon="send" />}
                            onClick={handleAddComment}
                            disabled={!newComment.trim()}
                            sx={S.SendButton}
                        />
                    </Box>
                </Box>
            }
        />
    )
}
