from clients.action_item_client import ActionItemClient
from clients.equipment.models import EquipmentResult
from clients.equipment.requests import GetEquipmentRequest


class EquipmentService:
    """Service responsible for retrieving and managing equipment data."""

    def __init__(
        self,
        action_item_client: ActionItemClient,
    ) -> None:
        """
        Initialize the EquipmentService with the provided ActionItemClient.

        Args:
            action_item_client (ActionItemClient): Instance providing access to the
                EquipmentClient and associated logging utilities.

        """
        self._action_item_client = action_item_client
        self._log = action_item_client.functional_location.log

    def get_equipments(
        self,
        request: GetEquipmentRequest,
    ) -> list[EquipmentResult]:
        """
        Retrieve a list of equipments based on the specified request.

        Args:
            request (GetEquipmentRequest): Object containing filtering criteria,
                such as a list of reporting unit external IDs.

        Returns:
            list[FunctionalLocationResult]: List of equipment results
                that match the provided request parameters.

        Raises:
            Exception: If an error occurs while retrieving the data, it is logged
                and re-raised to the caller.

        """
        try:
            return self._action_item_client.equipment.get_equipments(
                request,
            )
        except Exception as e:
            error_msg = f"Failed to retrieve equipments: {e!s}"
            self._log.error(error_msg)
            raise
