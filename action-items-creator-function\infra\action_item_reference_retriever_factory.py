from typing import Optional
import os
import sys
from uuid import uuid4


script_dir = os.path.dirname(os.path.abspath(__file__))
project_dir = os.path.dirname(script_dir)
sys.path.append(project_dir)
from infra.graphql_client_factory import GraphqlClientFactory
from infra.cognite_client_factory import CogniteClientFactory
from models.settings import Settings
from services.action_item_reference_retriever import ActionItemReferenceRetriever
from services.cognite_service import CogniteService
from services.graphql_service import GraphqlService
from services.logging_service import LoggingService


class ActionItemReferenceRetrieverFactory:
    @staticmethod
    def retriever(
        settings: Settings = Settings.from_env(),
        call_id: str = str(uuid4()),
        override_token: Optional[str] = None,
    ) -> ActionItemReferenceRetriever:
        log = LoggingService(call_id)
        cognite_client = CogniteClientFactory.create(settings, override_token)
        graphl_client = GraphqlClientFactory.create(cognite_client, settings)

        return ActionItemReferenceRetriever(
            CogniteService(cognite_client, settings, log),
            GraphqlService(graphl_client, log),
            settings,
            log,
        )