import { Timestamp } from '@cognite/sdk/dist/src'
import { User } from '../../action'
import { ExternalEntity } from '../../common'
import { ReportingUnit } from '../../reporting-unit'
import { ApprovalWorkflow, MDRCriticalityType } from './mdr-equipment-request'

export interface MDRMdmRequestHeader extends ExternalEntity {
    requestor: User | null
    plantArea: ReportingUnit | null
    requestDate: Date
    safetyPpe: boolean
    isDraft: boolean
    createdBy: User | null
    approvalWorkflow: ApprovalWorkflow | null
}

export interface MDRMdmRequest extends ExternalEntity {
    header: MDRMdmRequestHeader | null
    mdmGeneralInfo: MDRMdmGeneralInfo | null
    noun: MDRMdmRequestNoun | null
    modifier: MDRMdmRequestModifier | null
    isDraft: boolean | null
    createdBy: User | null
    requestType: string | null
    description: string | null
}

export interface MDRMdmGeneralInfo extends ExternalEntity {
    parentEquipmentCriticality: MDRActivityCriticality | null
    componentMaterialCriticality: MDRActivityCriticality | null
    criticalityType: MDRCriticalityType | null
    sapEquipmentFunctionalLocationBom: string | null
    bomQuantity: number | null
    mrpController: MDRMRPController | null
    safetyStockQty: number | null
    unitOfMeasure: string | null
    pricePerUnit: number | null
    leadTime: number | null
    meanTimeBetweenFailures: number | null
    numberOfAreasUsedOnSite: number | null
    justification: string | null
    vendorCode: MDRMdmVendorCode | null
    sapMaterialNumber: string | null
}

export interface MDRMdmVendorCode extends ExternalEntity {
    title: string | null
    vendorName: string | null
    city: string | null
    district: string | null
    postalCode: string | null
    streetAddress: string | null
    telephone: string | null
    modified: Timestamp | null
    modifiedBy: string | null
    sourceId: number | null
    itemType: string | null
    path: string | null
}

export interface MDRMdmNounCharacteristics extends ExternalEntity {
    noun: MDRMdmRequestNoun | null
    modifier: MDRMdmRequestModifier | null
    description: string | null
}

export interface MDRActivityCriticality extends ExternalEntity {
    name: string
    description: string
}

export interface MDRMdmRequestNoun extends ExternalEntity {
    name: string
    description: string | null
    modifiers: MDRMdmRequestModifier[]
}

export interface MDRMdmRequestModifier extends ExternalEntity {
    name: string
    description: string | null
    mdmRequestNoun: MDRMdmRequestNoun | null
}

export interface MDRMRPController extends ExternalEntity {
    name: string
    description: string
}
