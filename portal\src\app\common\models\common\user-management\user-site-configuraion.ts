import { ExternalEntity } from '..'
import { UserComplement } from './user-complement'
import { ReportingSite } from '../asset-hierarchy/reporting-site'
import { UserAzureAttribute } from './user-azure-attribute'

export interface UserSiteConfiguration extends ExternalEntity {
    user: UserComplement
    onSiteManagement?: OnSiteManagement
    reportingSite?: ReportingSite
}

export interface OnSiteManagement extends ExternalEntity {
    name?: string
    email: string
    firstName: string
    lastName: string
}

export interface UsersEmployees extends ExternalEntity {
    name?: string
    email: string
    firstName: string
    lastName: string
    userAzureAttribute?: UserAzureAttribute
}

export interface UserByManagement extends ExternalEntity {
    name?: string
    email: string
    firstName: string
    lastName: string
    userAzureAttribute?: UserAzureAttribute
    onSiteManagement?: OnSiteManagement
    employees?: UsersEmployees[]
}
