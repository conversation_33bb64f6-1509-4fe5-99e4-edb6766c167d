from typing import Optional

from clients.core.models import ExternalSourceServiceParams, PaginatedData
from clients.external_source.quality_reliability.models import QualityReliabilityEvent
from clients.external_source.quality_reliability.queries import (
    GET_QUALITY_REALIABILITY_EVENT,
)
from clients.external_source.quality_reliability.responses import (
    QualityReliabilityEventResponse,
)


class QualityReliabilityClient:
    """Client for retrieving quality and reliability event data from the external data model."""

    def __init__(self, params: ExternalSourceServiceParams) -> None:
        """
        Initialize the QualityReliabilityClient with external source service parameters.

        Args:
            params (ExternalSourceServiceParams): Configuration parameters including Cognite client,
                                                  logger, and data model reference.

        """
        self._cognite_client = params.cognite_client
        self._data_model_id = params.data_model.as_id()

    def get_event(
        self,
        external_id: str,
    ) -> Optional[QualityReliabilityEventResponse]:
        """
        Retrieve quality reliability event details for a given external ID.

        Args:
            external_id (str): The external identifier of the event.

        Returns:
            Optional[QualityReliabilityEventResponse]: The event details if found, otherwise None.

        """
        result = self._cognite_client.data_modeling.graphql.query(
            id=self._data_model_id,
            query=GET_QUALITY_REALIABILITY_EVENT,
            variables={"filter": {"externalId": {"eq": external_id}}},
        )

        event = (
            PaginatedData[QualityReliabilityEvent]
            .from_graphql_response(result, 1)
            .first_or_default()
        )

        return event.to_response() if event is not None else None
