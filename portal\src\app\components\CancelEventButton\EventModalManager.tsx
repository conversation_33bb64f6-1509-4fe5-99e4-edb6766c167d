import React, { useState } from 'react'
import { Box } from '@mui/material'
import AuthGuardWrapper from '@/app/common/wrapper/AuthGuardWrapper'
import { ConfirmCancelModal } from './ConfirmCancelModal'
import { ActionsByEventModal } from './ActionsByEventModal'
import { ActionDetailsModal } from './ActionDetailsModal'
import { useRouter } from 'next/navigation'
import { ActionStatusExternalIdHomeDefaultEnum } from '@/app/common/enums/ActionItemStatusEnum'
import { AzureFunctionClient } from '@/app/common/clients/azure-function-client'
import { useSnackbar } from '@/app/common/contexts/SnackbarContext'
import { SourceEventStatusClearEnum } from '@/app/common/enums/SourceEventStatusEnum'
import { translate } from '@/app/common/utils/generate-translate'
import { UserRolesPermission } from '@celanese/celanese-sdk'
import { FilterInfoProps } from '../ActionTable/HomeFilter'

type EventModalProps = {
    eventId: string
    siteId?: string
    eventSpace?: string
    activeUser: UserRolesPermission
    isOpen: boolean
    onClose: () => void
}

export type SelectionState = {
    isCheckboxChecked: boolean
    checkedActionIds: string[]
    uncheckedActionIds: string[]
}

export function EventModalManager({ eventId, siteId, eventSpace, activeUser, isOpen, onClose }: EventModalProps) {
    const { showSnackbar } = useSnackbar()

    const router = useRouter()

    const [selectActionsModalOpen, setSelectActionsModalOpen] = useState<boolean>(false)
    const [viewActionModalOpen, setViewActionModalOpen] = useState<boolean>(false)
    const [selectedActionsModalOpen, setSelectedActionsModalOpen] = useState<boolean>(false)

    const [selectionState, setSelectionState] = useState<SelectionState>({
        isCheckboxChecked: true,
        checkedActionIds: [],
        uncheckedActionIds: [],
    })

    const [filterInfo, setFilterInfo] = useState<FilterInfoProps>({
        reportingSiteExternalId: siteId ?? '-',
        externalIdPrefix: '',
        titlePrefix: '',
        statusExternalIds: Object.values(ActionStatusExternalIdHomeDefaultEnum),
        pageSize: 10,
        search: '',
        searchProperties: ['title'],
    })

    const [viewActionId, setViewActionId] = useState<string>('')

    const [totalActionItemsTable, setTotalActionItemsTable] = useState<number>(0)

    const [isCancellationInProgress, setIsCancellationInProgress] = useState<boolean>(false)

    const handleActionSelection = (actionId: string) => {
        setViewActionId(actionId)
        setViewActionModalOpen(true)
    }

    const handleModalClose = () => {
        setSelectActionsModalOpen(false)
        setSelectionState({
            isCheckboxChecked: true,
            checkedActionIds: [],
            uncheckedActionIds: [],
        })
        onClose()
    }

    const handleCancel = async (shouldCancelActions: boolean = false): Promise<void> => {
        setIsCancellationInProgress(true)

        const client = new AzureFunctionClient()

        const { isCheckboxChecked, uncheckedActionIds, checkedActionIds } = selectionState

        try {
            const request = {
                activeUserEmail: activeUser?.email ?? '-',
                reportingSiteExternalId: siteId ?? '-',
                externalId: eventId,
                space: eventSpace ?? '-',
                statusId: SourceEventStatusClearEnum.cancelled,
                actionsFilters: shouldCancelActions
                    ? {
                          ...filterInfo,
                          externalIds: isCheckboxChecked ? uncheckedActionIds : checkedActionIds,
                          notInExternalIds: isCheckboxChecked,
                          pageSize: 1000,
                      }
                    : undefined,
            }

            await client?.updateSourceEventStatusRequest(request)
            showSnackbar(translate('alerts.dataSavedWithSuccess'), 'success', 'event-modal-manager')
            router.push('/event-source')
        } catch {
            showSnackbar(translate('alerts.unexpectedErrorOcurred'), 'error', 'event-modal-manager')
        } finally {
            setIsCancellationInProgress(false)
        }
    }

    return (
        <AuthGuardWrapper componentName={EventModalManager.name}>
            {isOpen && (
                <Box>
                    <ConfirmCancelModal onClose={handleModalClose} onConfirm={() => setSelectActionsModalOpen(true)} />
                    {selectActionsModalOpen && (
                        <ActionsByEventModal
                            eventId={eventId}
                            onClose={handleModalClose}
                            onActionsCancel={() => {
                                setFilterInfo({ ...filterInfo, cursor: undefined })
                                setSelectedActionsModalOpen(true)
                            }}
                            onEventCancel={() => handleCancel()}
                            onActionView={handleActionSelection}
                            selectionState={selectionState}
                            setSelectionState={setSelectionState}
                            isSelectionModal={true}
                            filterInfo={filterInfo}
                            setFilterInfo={setFilterInfo}
                            totalActionItemsTable={totalActionItemsTable}
                            setTotalActionItemsTable={setTotalActionItemsTable}
                        />
                    )}
                    {viewActionModalOpen && (
                        <ActionDetailsModal
                            onClose={() => setViewActionModalOpen(false)}
                            actionId={viewActionId}
                            hideBackdrop={true}
                            activeUser={activeUser}
                        />
                    )}
                    {selectedActionsModalOpen && (
                        <ActionsByEventModal
                            eventId={eventId}
                            onClose={() => {
                                setFilterInfo({ ...filterInfo, cursor: undefined })
                                setSelectedActionsModalOpen(false)
                            }}
                            onActionsCancel={() => handleCancel(true)}
                            onEventCancel={() => handleCancel()}
                            onActionView={handleActionSelection}
                            selectionState={selectionState}
                            setSelectionState={setSelectionState}
                            isSelectionModal={false}
                            filterInfo={filterInfo}
                            setFilterInfo={setFilterInfo}
                            totalActionItemsTable={totalActionItemsTable}
                            setTotalActionItemsTable={setTotalActionItemsTable}
                            isCancellationInProgress={isCancellationInProgress}
                        />
                    )}
                </Box>
            )}
        </AuthGuardWrapper>
    )
}
