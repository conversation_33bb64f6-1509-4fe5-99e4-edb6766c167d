import React, { PropsWithChildren, useEffect, useState } from 'react'
import { CogniteContext } from '../contexts/CogniteContext'
import { createCogniteClient } from '../factories/cognite-client-factory'
import { createFdmClient } from '../factories/fdm-client-factory'
import { useAuthToken } from '../hooks'
import { getLocalUserSite } from '@celanese/celanese-ui'

export const CogniteClientProvider = ({ children }: PropsWithChildren) => {
    const { getAuthToken } = useAuthToken()
    const [profile, setProfile] = useState(getLocalUserSite())
    const cogniteClient = React.useMemo(() => createCogniteClient(getAuthToken), [getAuthToken])
    const fdmClient = React.useMemo(() => createFdmClient(getAuthToken), [getAuthToken, profile])

    useEffect(() => {
        function handleChangeStorage() {
            setProfile(getLocalUserSite())
        }

        window.addEventListener('storage', handleChangeStorage)
        return () => window.removeEventListener('storage', handleChangeStorage)
    }, [])

    return (
        <CogniteContext.Provider
            value={{
                cogniteClient,
                fdmClient,
            }}
        >
            {children}
        </CogniteContext.Provider>
    )
}
