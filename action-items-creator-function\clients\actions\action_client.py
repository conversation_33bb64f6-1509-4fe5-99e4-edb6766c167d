import json
import re
from collections.abc import Sequence
from datetime import UTC, datetime
from typing import Any, Literal, overload

from cognite.client.data_classes import data_modeling
from cognite.client.data_classes.files import FileMetadata, FileMetadataWrite
from industrial_model import PaginatedResult

from clients.core.cognite_mappers import CogniteDependencyMapper
from clients.core.constants import (
    AGGREGATE_LIMIT,
    LIMIT,
    SEARCH_LIMIT,
    DataSpaceEnum,
    EdgeType,
    EntitiesEnum,
    FileMetadataKey,
    ViewEnum,
)
from clients.core.models import BaseEntity, Node, PaginatedData, ServiceParams
from utils.id_generation_utils import IdGenerator

from .cognite_filters import (
    get_action_filters,
    get_action_filters_for_industrial_model,
    get_aggregate_filters,
)
from .models import (
    ActionByIdForEditingResult,
    ActionByIdResult,
    ActionExportResult,
    ActionItemChangeRequest,
    ActionItemForAssigneeRequestProcess,
    ActionItemForUpdateComparisonResult,
    ActionItemUpdate,
    ActionResult,
    CommentItem,
    ViewFieldsUpdater,
)
from .queries import (
    GET_ACTION_BY_ID_FOR_EDITING_QUERY,
    GET_ACTION_BY_ID_QUERY,
    GET_ACTION_ITEM_FOR_ASSIGNEE_REQUEST_PROCESS_QUERY,
    GET_ACTION_ITEMS_FOR_UPDATE_COMPARISON_QUERY,
    GET_ACTIONS_QUERY,
)
from .requests import (
    BaseActionRequest,
    GetActionByIdRequest,
    GetActionRequest,
    GetActionRequestForIndustrialModel,
)


class ActionClient:
    """Client responsible for handling actions and related views in the data model."""

    def __init__(
        self,
        params: ServiceParams,
    ) -> None:
        """Initialize the ActionClient with the required services and configuration."""
        self._cognite_client = params.cognite_client
        self._settings = params.settings
        self._engine = params.engine
        self._logging = params.logging
        self._cognite_service = params.cognite_service
        self._graphql_service = params.graphql_service
        self._notification_service = params.notification_service
        self._data_model_id = params.data_model.as_id()
        self._action_view = params.get_views()[ViewEnum.ACTION]
        self._recurrence_instance_view = params.get_views()[ViewEnum.RECURRENCE]
        self._status_history_instance_view = params.get_views()[
            ViewEnum.STATUS_HISTORY_INSTANCE
        ]
        self._action_item_link_view = params.get_views()[ViewEnum.ACTION_ITEM_LINK]
        self._approval_workflow_view = params.get_views()[ViewEnum.APPROVAL_WORKFLOW]
        self._approval_workflow_step_view = params.get_views()[
            ViewEnum.APPROVAL_WORKFLOW_STEP
        ]
        self._now_datetime = datetime.now(tz=UTC)
        self._id_generator = IdGenerator[EntitiesEnum](self._now_datetime)
        self._source_event_view = params.get_views()[ViewEnum.SOURCE_EVENT]
        self._change_request_view = params.get_views()[ViewEnum.CHANGE_REQUEST]

    def count_actions(self, request: BaseActionRequest) -> int:
        """Return the total number of actions matching the given filters."""
        return int(
            self._cognite_client.data_modeling.instances.aggregate(
                view=self._action_view,
                aggregates=data_modeling.aggregations.Count("externalId"),
                filter=data_modeling.filters.And(
                    *get_aggregate_filters(request, self._action_view),
                ),
            ).value,
        )

    async def count_actions_by_source_id(
        self,
        request: BaseActionRequest,
    ) -> list[dict[str, Any]]:
        """Return the count of actions grouped by sourceId based on the given filters."""
        return self._cognite_client.data_modeling.instances.aggregate(
            view=self._action_view,
            aggregates=data_modeling.aggregations.Count("externalId"),
            filter=data_modeling.filters.And(
                *get_aggregate_filters(request, self._action_view),
            ),
            group_by=["sourceId"],
            limit=AGGREGATE_LIMIT,
        ).dump()

    def get_actions(
        self,
        request: GetActionRequest,
    ) -> PaginatedData[ActionResult]:
        """Retrieve paginated actions based on filters and optional search."""
        if request.search is not None and len(request.search) > 0:
            self._handle_search(request)

        result = self._cognite_client.data_modeling.graphql.query(
            id=self._data_model_id,
            query=GET_ACTIONS_QUERY,
            variables=get_action_filters(request),
        )

        return PaginatedData[ActionResult].from_graphql_response(
            result,
            request.page_size,
        )

    @overload
    async def get_actions_for_industrial_model(
        self,
        request: GetActionRequestForIndustrialModel,
        all_pages: Literal[True],
    ) -> list[ActionExportResult]: ...

    @overload
    async def get_actions_for_industrial_model(
        self,
        request: GetActionRequestForIndustrialModel,
        all_pages: Literal[False] = False,
    ) -> PaginatedResult[ActionExportResult]: ...

    async def get_actions_for_industrial_model(
        self,
        request: GetActionRequestForIndustrialModel,
        all_pages: bool = False,
    ) -> PaginatedResult[ActionExportResult] | list[ActionExportResult]:
        """Retrieve paginated actions or all actions based on filters using industrial-model."""
        if request.search is not None and len(request.search) > 0:
            self._handle_search(request)

        query = request.to_statement(ActionExportResult).sort(
            request.sort_by,
            request.direction_industrial_model,
        )

        query.where(get_action_filters_for_industrial_model(request))

        if all_pages:
            query.limit(LIMIT)
            # For Debugger
            # self._engine._engine._cognite_adapter._cognite_client.config.debug = True
            return await self._engine.query_all_pages_async(
                query,
                validation_mode="ignoreOnError",
            )

        query.limit(request.page_size).cursor(request.cursor)
        return await self._engine.query_async(query)

    def map_and_format_comments(
        self,
        raw_comments: list[CommentItem] | None,
    ) -> list[CommentItem]:
        """Format and split legacy comments into individual CommentItem instances."""
        # TODO: refactor this, very unintuitive
        if not isinstance(raw_comments, list):
            return []

        formatted_comments: list[CommentItem] = []

        for comment in raw_comments:
            if not hasattr(comment, "comment"):
                continue

            comment_data = comment.__dict__.copy()

            if comment.is_legacy and isinstance(comment.comment, str):
                parts = re.split(r"_{5,}", comment.comment)

                for part in parts:
                    stripped_part = part.strip()
                    if stripped_part:
                        comment_data["comment"] = stripped_part
                        formatted_comments.append(CommentItem(**comment_data))
            else:
                comment_data["comment"] = (
                    comment.comment.strip() if isinstance(comment.comment, str) else ""
                )
                formatted_comments.append(CommentItem(**comment_data))

        return formatted_comments

    def get_action_by_id(
        self,
        request: GetActionByIdRequest,
    ) -> (
        PaginatedData[ActionByIdResult]
        | PaginatedData[ActionByIdForEditingResult]
        | PaginatedData[ActionItemForAssigneeRequestProcess]
    ):
        """
        Retrieve an action item by ID, depending on the request context.

        Returns the appropriate view of the action item based on the request:
        - If editing, returns the editable version.
        - If part of an assignee request process, returns the view for the assignee.
        - Otherwise, returns the default version with formatted comments.

        Args:
            request (GetActionByIdRequest): The request parameters, including context flags
                (`is_edition`, `is_assignee_request_process`) and pagination size.

        Returns:
            PaginatedData[ActionByIdResult] or
            PaginatedData[ActionByIdForEditingResult] or
            PaginatedData[ActionItemForAssigneeRequestProcess]:
                The paginated result based on the request context.

        """
        if request.is_edition:
            query = GET_ACTION_BY_ID_FOR_EDITING_QUERY
            result_type = PaginatedData[ActionByIdForEditingResult]
        elif request.is_assignee_request_process:
            query = GET_ACTION_ITEM_FOR_ASSIGNEE_REQUEST_PROCESS_QUERY
            result_type = PaginatedData[ActionItemForAssigneeRequestProcess]
        else:
            query = GET_ACTION_BY_ID_QUERY
            result_type = PaginatedData[ActionByIdResult]

        result = self._cognite_client.data_modeling.graphql.query(
            id=self._data_model_id,
            query=query,
            variables=get_action_filters(request),
        )

        paginated_result = result_type.from_graphql_response(result, request.page_size)

        for action in paginated_result.data:
            if isinstance(action, ActionByIdResult):
                action.comments = self.map_and_format_comments(action.comments)

        return paginated_result

    def upsert_action_item(
        self,
        action_items_update: list[ActionItemUpdate],
    ) -> None:
        """
        Update or inserts action items into the database, including approval workflows, status history, and attachments.

        Args:
            action_items_update (list[ActionItemUpdate]): list of action items to be processed.

        """
        current_states = self._get_action_items_for_update_comparison(
            nodes=action_items_update,
        )

        current_states_map = {cs.as_base_entity(): cs for cs in current_states}

        all_nodes_to_upsert: list[data_modeling.NodeApply] = []
        all_edges_to_upsert: list[data_modeling.EdgeApply] = []

        all_nodes_to_delete: list[data_modeling.NodeId] = []
        all_edges_to_delete: list[data_modeling.EdgeId] = []

        all_files_to_update: list[FileMetadataWrite] = []
        all_files_to_delete_external_ids: list[str] = []

        for update in action_items_update:
            current = current_states_map[update.as_base_entity()]
            edges_to_delete_external_ids: list[str] = []

            if current.is_private:
                _ = ViewFieldsUpdater(current, update).execute(
                    all_edges_to_upsert,
                    edges_to_delete_external_ids,
                )

            self._process_recurrence_instance_update(update, all_nodes_to_upsert)

            self._process_approval_workflow_update(
                update,
                all_nodes_to_upsert,
                all_edges_to_upsert,
            )

            self._process_status_history_update(update, all_nodes_to_upsert)

            self._process_action_item_link_update(
                current,
                update,
                all_nodes_to_upsert,
                all_nodes_to_delete,
            )

            self._process_assignees_update(
                current,
                update,
                all_edges_to_upsert,
                edges_to_delete_external_ids,
            )

            files_to_update, files_to_delete_external_ids = (
                self._process_attachments_update(
                    action_external_id=current.external_id,
                    action_space=current.space,
                    current_attachments_state=[
                        att.external_id for att in current.attachments or []
                    ],
                    new_attachments_state=update.attachments,
                )
            )
            all_files_to_update.extend(files_to_update)
            all_files_to_delete_external_ids.extend(files_to_delete_external_ids)

            self._process_action_update(update, all_nodes_to_upsert)

            all_nodes_to_delete.extend(update.nodes_to_delete)
            all_edges_to_delete.extend(update.edges_to_delete)
            all_edges_to_delete.extend(
                data_modeling.EdgeId(external_id=external_id, space=current.space)
                for external_id in edges_to_delete_external_ids
            )

        # raise Exception("Should not be reached")  # TODO: remove

        if all_files_to_update:
            self._cognite_client.files.update(item=all_files_to_update)

        if all_files_to_delete_external_ids:
            self._cognite_client.files.delete(
                external_id=all_files_to_delete_external_ids,
            )

        if all_nodes_to_delete or all_edges_to_delete:
            self._cognite_client.data_modeling.instances.delete(
                nodes=all_nodes_to_delete or None,
                edges=all_edges_to_delete or None,
            )

        self._cognite_client.data_modeling.instances.apply(
            nodes=all_nodes_to_upsert,
            edges=all_edges_to_upsert or None,
        )

    def upsert_change_request(
        self,
        change_requests_update: list[ActionItemChangeRequest],
    ) -> None:
        """
        Handle the upsertion of multiple change requests, including processing approval workflows, status history, and attachments, before applying the updates to the data model.

        This method processes each `ActionItemChangeRequest` in the provided `change_requests_update` list.
        For each change request, it updates the corresponding nodes, edges, and attachments,
        ensuring that the action items, approval workflows, and other related data are correctly upserted.
        The function then applies the changes to the data model, using the Cognite client to persist the updates.

        Args:
            change_requests_update (list[ActionItemChangeRequest]):
                A list of `ActionItemChangeRequest` instances that need to be upserted.
                Each request contains the details of the change, such as the action item, approval workflow,
                attachments, and associated history.

        Process Flow:
            1. **Approval Workflow Processing**: If an approval workflow is associated with the change request,
               it processes the workflow and adds it to the nodes and edges for the data model.
            2. **Status History Processing**: If the action item has a history instance,
               it processes the status history and adds it to the nodes for the data model.
            3. **Attachments Processing**: If attachments are included in the change request,
               it processes the attachments, including any necessary metadata.
            4. **Finalization**: After processing the change request, it finalizes both the action item and the
               change request, preparing them for upsertion into the data model.
            5. **Apply Updates**: Once all change requests are processed, it applies the changes to the data model
               by interacting with the Cognite client.

        Returns:
            None:
                This function does not return any value. Instead, it applies the changes directly to the data model
                through the Cognite client.

        Example:
            change_requests = [
                ActionItemChangeRequest(external_id="CR-001", action=ActionByIdResult(...)),
                ActionItemChangeRequest(external_id="CR-002", action=ActionByIdResult(...)),
            ]
            upsert_change_request(change_requests)

        """
        nodes: list[data_modeling.NodeApply] = []
        edges: list[data_modeling.EdgeApply] = []
        all_files_to_update: list[FileMetadataWrite] = []

        for update in change_requests_update:
            self._process_approval_workflow_update(update, nodes, edges)

            self._process_status_history_update(update.action, nodes)

            files_to_update, _ = self._process_attachments_update(
                action_external_id=update.action.external_id,
                action_space=update.action.space,
                current_attachments_state=[],  # NOTE: assumes it's always a one-time addition
                new_attachments_state=update.attatchments,
            )
            all_files_to_update.extend(files_to_update)

            self._process_action_update(update.action, nodes)

            nodes.append(
                update.to_node_apply(
                    self._change_request_view,
                    update.get_properties_to_include(),
                ),
            )

        if all_files_to_update:
            self._cognite_client.files.update(item=all_files_to_update)

        self._cognite_client.data_modeling.instances.apply(
            nodes=nodes,
            edges=edges if len(edges) > 0 else None,
        )

    def _process_action_update(
        self,
        update: ActionItemUpdate,
        nodes_to_upsert: list[data_modeling.NodeApply],
    ) -> None:
        properties_to_include = update.get_properties_to_include()
        if not properties_to_include:
            return None
        return nodes_to_upsert.append(
            update.to_node_apply(self._action_view, properties_to_include),
        )

    def _process_approval_workflow_update(
        self,
        update: ActionItemUpdate | ActionItemChangeRequest,
        nodes_to_upsert: list[data_modeling.NodeApply],
        edges_to_upsert: list[data_modeling.EdgeApply],
    ) -> None:
        """Process approval workflow update."""
        if not update.approval_workflow:
            return

        nodes_to_upsert.append(
            update.approval_workflow.to_node_apply(
                self._approval_workflow_view,
                update.approval_workflow.get_properties_to_include(),
            ),
        )

        for step in update.approval_workflow.steps or []:
            if step.users:
                edges_to_upsert.extend(
                    data_modeling.EdgeApply(
                        external_id=f"{step.external_id}-{user.external_id}",
                        space=step.space,
                        type=EdgeType.APPROVAL_WORKFLOW_STEP_TO_USERS.value,
                        start_node=(step.space, step.external_id),
                        end_node=(DataSpaceEnum.UMG_DATA_SPACE, user.external_id),
                    )
                    for user in step.users
                )
                step.approval_workflow = Node(
                    external_id=update.approval_workflow.external_id,
                    space=update.approval_workflow.space,
                )

            nodes_to_upsert.append(
                step.to_node_apply(
                    self._approval_workflow_step_view,
                    step.get_properties_to_include(),
                ),
            )

    def _process_recurrence_instance_update(
        self,
        update: ActionItemUpdate,
        nodes_to_upsert: list[data_modeling.NodeApply],
    ) -> None:
        """Process recurrence instance update."""
        if not update.recurrence_instance:
            return

        nodes_to_upsert.append(
            update.recurrence_instance.to_node_apply(
                self._recurrence_instance_view,
                update.recurrence_instance.get_properties_to_include(),
            ),
        )

    def _process_status_history_update(
        self,
        update: ActionItemUpdate,
        nodes_to_upsert: list[data_modeling.NodeApply],
    ) -> None:
        """Process status history instances update."""
        if not update.history_instance:
            return

        nodes_to_upsert.extend(
            [
                history.to_node_apply(
                    self._status_history_instance_view,
                    history.get_properties_to_include(),
                )
                for history in update.history_instance
            ],
        )

    def _process_action_item_link_update(
        self,
        current: ActionItemForUpdateComparisonResult,
        update: ActionItemUpdate,
        nodes_to_upsert: list[data_modeling.NodeApply],
        nodes_to_delete: list[data_modeling.NodeId],
    ) -> None:
        """Process action item links update."""
        if update.action_item_link is None:
            return

        nodes_to_upsert.extend(
            [
                link_update.to_node_apply(
                    self._action_item_link_view,
                    link_update.get_properties_to_include(),
                )
                for link_update in update.action_item_link
            ],
        )

        new_links_state_external_ids = {
            uail.external_id for uail in update.action_item_link
        }

        for current_link in current.action_item_link:
            if current_link.external_id in new_links_state_external_ids:
                continue

            nodes_to_delete.append(current_link.to_node_id())

    def _process_assignees_update(
        self,
        current: ActionItemForUpdateComparisonResult,
        update: ActionItemUpdate,
        edges_to_upsert: list[data_modeling.EdgeApply],
        edges_to_delete_external_ids: list[str],
    ) -> None:
        """Process assignees update."""
        if update.assignees is None:
            return

        current_assignees_state_external_ids = {
            ca.external_id for ca in current.assignees
        }
        new_assignees_state_external_ids = set(update.assignees)

        assignee_external_ids_to_add = (
            new_assignees_state_external_ids - current_assignees_state_external_ids
        )
        edges_to_upsert.extend(
            [
                data_modeling.EdgeApply(
                    external_id=f"{current.external_id}-{assignee_external_id}",
                    space=current.space,
                    type=EdgeType.ACTION_TO_ASSIGNEES.value,
                    start_node=(current.space, current.external_id),
                    end_node=(DataSpaceEnum.UMG_DATA_SPACE.value, assignee_external_id),
                )
                for assignee_external_id in assignee_external_ids_to_add
            ],
        )

        assignees_external_ids_to_delete_edges = (
            current_assignees_state_external_ids - new_assignees_state_external_ids
        )
        edges_to_delete_external_ids.extend(
            ca.edge_external_id
            for ca in current.assignees
            if ca.external_id in assignees_external_ids_to_delete_edges
        )

    def _process_attachments_update(
        self,
        action_external_id: str,
        action_space: str,
        current_attachments_state: list[str] | None,
        new_attachments_state: list[str] | None,
    ) -> tuple[list[FileMetadataWrite], list[str]]:  # files to update / files to delete
        """Process attachments update for an action item."""
        if new_attachments_state is None:
            return [], []

        current_attachments_state = current_attachments_state or []

        # TODO: improve the two calls below to not be inside a loop
        external_id_to_metadata = self._cognite_service.get_files_metadata(
            file_external_ids=list(
                set(new_attachments_state + current_attachments_state),
            ),
        )

        aim_data_set_id = self._cognite_service.get_dataset_id(action_space)
        assert aim_data_set_id is not None

        files_to_remove_link_external_ids: list[str] = list(
            set(current_attachments_state) - set(new_attachments_state),
        )
        files_to_add_link_external_ids: list[str] = list(
            set(new_attachments_state) - set(current_attachments_state),
        )

        files_to_add_link_update = self._process_attachments_to_add_link(
            files_to_add_link_external_ids,
            external_id_to_metadata,
            aim_data_set_id,
            action_external_id,
        )

        files_to_remove_link_update, files_to_delete_external_ids = (
            self._process_attachments_to_remove_link(
                files_to_remove_link_external_ids,
                external_id_to_metadata,
                aim_data_set_id,
                action_external_id,
            )
        )

        files_to_update = files_to_add_link_update + files_to_remove_link_update

        return files_to_update, files_to_delete_external_ids

    @staticmethod
    def _process_attachments_to_add_link(
        files_to_add_link_external_ids: list[str],
        external_id_to_metadata: dict[str, FileMetadata],
        aim_data_set_id: int,
        action_external_id: str,
    ) -> list[FileMetadataWrite]:
        files_to_update: list[FileMetadataWrite] = []
        for ftal_external_id in files_to_add_link_external_ids:
            file_to_add_link = external_id_to_metadata.get(ftal_external_id)
            if file_to_add_link is None:
                continue

            if file_to_add_link.data_set_id != aim_data_set_id:
                msg = "Only files in AIM datasets are accepted."
                raise ValueError(msg)

            related_actions_raw: str | None = (
                file_to_add_link.metadata.get(FileMetadataKey.RELATED_ACTIONS.value)
                if file_to_add_link.metadata is not None
                else None
            )

            related_actions: list[str] = (
                [] if related_actions_raw is None else json.loads(related_actions_raw)
            )

            related_actions = list({*related_actions, action_external_id})

            file_to_add_link.metadata = {
                **(file_to_add_link.metadata or {}),
                FileMetadataKey.RELATED_ACTIONS.value: json.dumps(related_actions),
            }

            files_to_update.append(file_to_add_link.as_write())

        return files_to_update

    @staticmethod
    def _process_attachments_to_remove_link(
        files_to_remove_link_external_ids: list[str],
        external_id_to_metadata: dict[str, FileMetadata],
        aim_data_set_id: int,
        action_external_id: str,
    ) -> tuple[list[FileMetadataWrite], list[str]]:
        files_to_delete_external_ids: list[str] = []
        files_to_update: list[FileMetadataWrite] = []
        for ftrl_external_id in files_to_remove_link_external_ids:
            file_to_delete_link = external_id_to_metadata.get(ftrl_external_id)
            if (
                file_to_delete_link is None
                or file_to_delete_link.data_set_id != aim_data_set_id
            ):
                continue

            related_actions_raw: str | None = (
                file_to_delete_link.metadata.get(FileMetadataKey.RELATED_ACTIONS.value)
                if file_to_delete_link.metadata is not None
                else None
            )

            related_actions: list[str] = (
                [] if related_actions_raw is None else json.loads(related_actions_raw)
            )

            related_actions = list(
                filter(
                    lambda ra, external_id=action_external_id: ra != external_id,
                    related_actions,
                ),
            )

            if len(related_actions) == 0:
                files_to_delete_external_ids.append(ftrl_external_id)
                continue

            file_to_delete_link.metadata = {
                **(file_to_delete_link.metadata or {}),
                FileMetadataKey.RELATED_ACTIONS.value: json.dumps(related_actions),
            }

            files_to_update.append(file_to_delete_link.as_write())

        return files_to_update, files_to_delete_external_ids

    def _handle_search(
        self,
        request: GetActionRequest | GetActionRequestForIndustrialModel,
    ) -> None:
        """
        Handle the search functionality for actions based on the search term and properties specified in the request.

        This method searches for actions in the data model by performing a full-text query on the specified
        search properties. It filters results using the search term provided in the request and updates the
        `request.external_ids` attribute with the external IDs of matching actions.

        Args:
            request (GetActionRequest):
                The request object containing the search term, search properties, and any additional filters
                to be applied during the search.

        Returns:
            None: The method directly updates the `request.external_ids` attribute.

        """
        if not request.search_properties:
            msg = "No search properties specified for the search operation."
            raise ValueError(msg)

        actions = self._cognite_client.data_modeling.instances.search(
            view=self._action_view,
            query=request.search or "",
            properties=request.search_properties,
            filter=data_modeling.filters.And(
                *get_aggregate_filters(request, self._action_view),
            ),
            limit=SEARCH_LIMIT,
        )

        normalized_search_term = (
            request.search.lower() if request.search is not None else ""
        )

        matching_actions_ids: list[str] = [
            action["externalId"]
            for action in CogniteDependencyMapper.map(actions)
            if any(
                str(action[prop]).lower()
                for prop in request.search_properties
                if prop in action
                and isinstance(action[prop], str)
                and normalized_search_term in str(action[prop]).lower()
            )
        ]

        request.external_ids = matching_actions_ids or ["-"]
        request.not_in_external_ids = False

    def _get_action_items_for_update_comparison(
        self,
        nodes: Sequence[BaseEntity],
    ) -> list[ActionItemForUpdateComparisonResult]:
        variables = {
            "filter": {
                "or": [
                    {
                        "and": [
                            {"externalId": {"eq": n.external_id}},
                            {"space": {"eq": n.space}},
                        ],
                    }
                    for n in nodes
                ],
            },
        }
        result = self._cognite_client.data_modeling.graphql.query(
            id=self._data_model_id,
            query=GET_ACTION_ITEMS_FOR_UPDATE_COMPARISON_QUERY,
            variables=variables,
        )

        return ActionItemForUpdateComparisonResult.from_single_query_result(result)
