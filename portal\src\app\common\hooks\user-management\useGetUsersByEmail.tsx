import { useEffect, useState } from 'react'
import { User } from '../../models/common/user-management/user'
import { useGraphqlQuery } from '../cognite/useGraphqlQuery'
import { gql } from '@apollo/client'

const buildReferenceQuery = (email: string): string => {
    const filters: string[] = []

    let queryFilter = '{ }'

    if (email) {
        filters.push(`{ email: { eq: "${email}" } }`)
    }

    if (filters.length > 0) {
        queryFilter = `{ and: [ ${filters.join(', ')} ] }`
    }

    return `
        query GetUserByEmail {
            listUser(
                filter: ${queryFilter}
                , first: 1
            ) {
                items {
                    externalId
                    space
                    firstName
                    lastName
                    email
                    teams {
                        items {
                            externalId
                            name
                            reportingSite {
                                externalId
                            }
                            reportingUnits {
                                items {
                                    externalId
                                }
                            }
                        }
                    }
                }
            }
        }
    `
}

export function useGetUsersByEmail(email: string) {
    const query = buildReferenceQuery(email)
    const [resultData, setResultData] = useState<{ data: User[]; loading: boolean }>({
        data: [],
        loading: true,
    })

    const { data: fdmData } = useGraphqlQuery<User>(gql(query), 'listUser', {})

    useEffect(() => {
        if (fdmData.length == 0) {
            setResultData({ data: [], loading: false })
        } else {
            setResultData({ data: fdmData, loading: false })
        }
    }, [fdmData])

    return {
        loading: resultData.loading,
        users: resultData.data,
    }
}
