@echo off
setlocal

:: Dynamically define the directory path
set "UM_DIR=%USERPROFILE%\source\UserManagement"

:: Check if the directory exists
if not exist "%UM_DIR%" (
    echo ❌ ERROR: Directory "%UM_DIR%" not found!
    exit /b
)

cd /d "%UM_DIR%"

echo 🔄 Updating repository...
git pull
if %errorlevel% neq 0 (
    echo ❌ Error executing git pull
    exit /b
)

cd UserManagement-API

echo 🐍 Activating virtual environment...
call .\.venv\Scripts\activate
if %errorlevel% neq 0 (
    echo ❌ Error activating virtual environment
    exit /b
)

echo 📦 Installing dependencies...
pip install --no-cache-dir -r requirements.txt
if %errorlevel% neq 0 (
    echo ❌ Error installing dependencies
    exit /b
)

:: Display a warning in a new terminal with a pink background
start cmd /k color d0 ^& echo CHECK UM VERSION IN COGNITE BEFORE RUNNING

echo ✅ Update completed!
endlocal
