from functools import lru_cache
from clients.core.models import ServiceParams
from clients.core.constants import DataSpaceEnum
from .requests import GetFilesDownloadUrlsRequest, UploadFileRequest


class FileClient:
    def __init__(self, params: ServiceParams):
        self._cognite_client = params.cognite_client

    @lru_cache(maxsize=1)
    def _get_security_category(self, category_name: str) -> int:
        """
        Fetches the security category ID by name, using a cache for optimization.

        Parameters:
        - category_name (str): The name of the security category.

        Returns:
        - int: The ID of the security category, or None if not found.
        """
        security_categories = self._cognite_client.iam.security_categories.list()
        security_id = next(
            (
                item.get("id")
                for item in security_categories.dump()
                if item.get("name") == DataSpaceEnum.PRIVATE_SPACE
            ),
            None,
        )

        if security_id is None:
            raise ValueError(f"Security category '{category_name}' not found.")

        return security_id

    def upload_bytes(self, file: UploadFileRequest) -> str:
        """
        Uploads a file to the Cognite platform in byte format.

        Parameters:
        - file (UploadFileRequest): The file object.

        Returns:
        - str: The external ID of the uploaded file.
        """
        security_categories = (
            [self._get_security_category(DataSpaceEnum.PRIVATE_SPACE)]
            if file.is_private
            else None
        )

        return self._cognite_client.files.upload_bytes(
            content=file.content,
            name=file.name,
            external_id=file.external_id,
            source=file.source,
            mime_type=file.mime_type,
            metadata=file.metadata,
            data_set_id=file.data_set_id,
            security_categories=security_categories,
        ).external_id

    def get_files_download_urls(
        self, request: GetFilesDownloadUrlsRequest
    ) -> dict[str | int, str]:
        """
        Retrieves download URLs for specified files from the Cognite platform.

        Parameters:
        - request (GetFilesDownloadUrlsRequest): Object containing a list of external IDs for the files to retrieve URLs for.

        Returns:
        - dict[str | int, str]: A dictionary mapping file identifiers to their respective download URLs.
        """
        return self._cognite_client.files.retrieve_download_urls(
            external_id=request.external_ids
        )
