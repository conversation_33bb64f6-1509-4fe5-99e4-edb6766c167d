from datetime import date, datetime
from typing import Annotated

from cognite.client.data_classes.data_modeling import (
    EdgeApply,
    EdgeId,
    NodeId,
)
from industrial_model import ViewInstance, ViewInstanceConfig
from pydantic import BaseModel, Field, PlainSerializer, computed_field

from clients.actions._models import (
    ApprovalWorkflow,
    ChangeRequest,
    MetadataField,
    Recurrence,
    SourceEventForUpdateComparisonResult,
    ViewEntityForUpdateComparisonResult,
    _ActionItemLink,
    _SourceEvent,
    _StatusHistoryInstance,
    _User,
    _UserAzureAttribute,
)
from clients.approval_workflow.models import ApprovalWorkflowUpdate
from clients.core.constants import (
    ActionStatusClosedEnum,
    ApprovalWorkflowStatusEnum,
    ChangeRequestTypeEnum,
    DataSpaceEnum,
    EdgeType,
)
from clients.core.models import (
    ApplicationEntity,
    AttachmentsEntity,
    BaseEntity,
    DescribableEntity,
    Node,
    ReportingSiteEntity,
)
from clients.core.utils import to_user_azure_attribute
from clients.core.validators import (
    edge_unwraper_validator,
    edge_unwraper_validator_with_edge_id,
)


class ActionResult(Node):
    """
    Represents the result of an action within the system.

    This class extends `Node` and encapsulates various attributes related to an
    action item, including its status, category, assigned users, and associated metadata.
    """

    title: str

    current_status: DescribableEntity
    due_date: date | None
    display_due_date: date | None
    approval_date: date | None
    verification_date: date | None
    conclusion_date: date | None
    assignment_date: date | None

    category: DescribableEntity
    source_information: str | None
    source_id: str | None
    source_type: DescribableEntity | None
    sub_category: DescribableEntity | None

    is_private: bool | None

    site_specific_category: DescribableEntity | None
    action_item_kind: DescribableEntity | None
    reporting_site: ReportingSiteEntity | None
    reporting_unit: DescribableEntity | None
    reporting_location: DescribableEntity | None
    application: ApplicationEntity | None
    assigned_to: _UserAzureAttribute | None
    owner: _UserAzureAttribute | None
    approval_workflow: ApprovalWorkflow | None
    source_events: Annotated[list[_SourceEvent], edge_unwraper_validator]

    change_requests: Annotated[list[ChangeRequest], edge_unwraper_validator] | None = (
        None
    )
    metadatas: Annotated[list[MetadataField], edge_unwraper_validator] | None = None

    priority: str | None = None
    object_type: str | None = None

    @computed_field
    @property
    def approver(self) -> _User | None:
        """Retrieves the approver from the approval workflow if available."""
        return (
            None
            if not self.approval_workflow
            else self.approval_workflow.get_approver()
        )

    @computed_field
    @property
    def verifier(self) -> _User | None:
        """Retrieves the verifier from the approval workflow if available."""
        return (
            None
            if not self.approval_workflow
            else self.approval_workflow.get_verifier()
        )

    @computed_field
    @property
    def source_event_title(self) -> str | None:
        """Retrieves the title of the first associated source event, if any exist."""
        return next((event.title for event in self.source_events), None)

    @computed_field
    @property
    def ext(self) -> int:
        """Counts the number of approved due date change requests."""
        return sum(
            1
            for request in self.change_requests or []
            if (
                request.approval_workflow
                and request.approval_workflow.status
                and request.approval_workflow.status.external_id
                == ApprovalWorkflowStatusEnum.APPROVED
                and request.change_request_type
                and request.change_request_type.external_id
                == ChangeRequestTypeEnum.DUE_DATE
            )
        )

    @computed_field
    @property
    def manager(self) -> _User | None:
        """Retrieves the on-site manager for the assigned user if the user has site configurations."""
        if not self.assigned_to or not self.assigned_to.user_complement:
            return None
        user_site_configurations = (
            self.assigned_to.user_complement.user_site_configurations
        )
        return next(
            (
                configuration.on_site_manager
                for configuration in user_site_configurations
            ),
            None,
        )

    def to_external_ref(self) -> dict[str, str]:
        """Convert the ActionResult object to an external reference dictionary."""
        return {"external_id": self.external_id, "space": self.space}


class ActionExportResponse(Node):
    """Represents the response for exporting a actions."""

    title: str | None = None
    current_status: str | None = None
    assignee: str | None = None
    application: str | None = None
    reporting_site: str | None = None
    reporting_unit: str | None = None
    reporting_location: str | None = None
    category: str | None = None
    sub_category: str | None = None
    site_specific_category: str | None = None
    due_date: str | None = None
    conclusion_date: str | None = None
    approver: str | None = None
    approval_date: str | None = None
    verifier: str | None = None
    verification_date: str | None = None
    source_information: str | None = None
    source_event_title: str | None = None
    owner: str | None = None
    assignment_date: str | None = None
    is_private: str | None = None
    icap_action_id: str | None = None
    priority: str | None = None
    ext: str | None = None
    manager: str | None = None
    overdue: str | None = None


class ActionExportResult(ViewInstance):
    """
    Represents the result of an action within the system.

    This class extends `Node` and encapsulates various attributes related to an
    action item, including its status, category, assigned users, and associated metadata.
    """

    view_config = ViewInstanceConfig(
        instance_spaces_prefix="ACT-",
        view_external_id="Action",
    )
    space: str
    title: str

    current_status: DescribableEntity
    due_date: date | None = None
    display_due_date: date | None = None
    approval_date: date | None = None
    verification_date: date | None = None
    conclusion_date: date | None = None
    assignment_date: date | None = None

    object_type: str | None = None

    category: DescribableEntity
    source_information: str | None = None
    source_id: str | None = None
    source_type: DescribableEntity | None = None
    sub_category: DescribableEntity | None = None

    priority: str | None = None

    is_private: bool | None = None

    site_specific_category: DescribableEntity | None = None
    action_item_kind: DescribableEntity | None = None
    reporting_site: ReportingSiteEntity | None = None
    reporting_unit: DescribableEntity | None = None
    reporting_location: DescribableEntity | None = None
    application: ApplicationEntity | None = None
    assigned_to: _UserAzureAttribute | None = None
    owner: _UserAzureAttribute | None = None
    views: list[str] | None = None
    approval_workflow: ApprovalWorkflow | None = None

    sort_source_event_title: str | None = None

    source_events: list[_SourceEvent] | None = None
    change_requests: list[ChangeRequest] | None = None

    @computed_field
    @property
    def approver(self) -> _User | None:
        """Retrieves the approver from the approval workflow if available."""
        return (
            None
            if not self.approval_workflow
            else self.approval_workflow.get_approver()
        )

    @computed_field
    @property
    def verifier(self) -> _User | None:
        """Retrieves the verifier from the approval workflow if available."""
        return (
            None
            if not self.approval_workflow
            else self.approval_workflow.get_verifier()
        )

    @computed_field
    @property
    def source_event_title(self) -> str | None:
        """Retrieves the title of the first associated source event, if any exist."""
        if self.source_events:
            return next((event.title for event in self.source_events), None)
        return None

    @computed_field
    @property
    def ext(self) -> int:
        """Counts the number of approved due date change requests."""
        return sum(
            1
            for request in self.change_requests or []
            if (
                request.approval_workflow
                and request.approval_workflow.status
                and request.approval_workflow.status.external_id
                == ApprovalWorkflowStatusEnum.APPROVED
                and request.change_request_type
                and request.change_request_type.external_id
                == ChangeRequestTypeEnum.DUE_DATE
            )
        )

    @computed_field
    @property
    def manager(self) -> _User | None:
        """Retrieves the on-site manager for the assigned user if the user has site configurations."""
        if not self.assigned_to or not self.assigned_to.user_complement:
            return None
        user_site_configurations = (
            self.assigned_to.user_complement.user_site_configurations
        )
        return next(
            (
                configuration.on_site_manager
                for configuration in user_site_configurations
                if configuration.reporting_site
                and self.reporting_site
                and configuration.reporting_site.external_id
                == self.reporting_site.external_id
            ),
            None,
        )

    def _get_translation(
        self,
        key: str | None,
        translations: dict[str, str],
    ) -> str | None:
        if key is None:
            return None
        return translations.get(key, key)

    def _format_reporting_unit(self) -> str | None:
        if self.reporting_unit and self.reporting_unit.name:
            return f"{self.reporting_unit.name[:3]} - {self.reporting_unit.description}"
        return None

    def _extract_icap_action_id_from_object_type(self, input_string: str | None) -> str:
        """Extract the ICAP Action ID from an objectType string (e.g., 'ICAP-123-XYZ')."""
        if input_string is None:
            return ""
        parts = input_string.split("-")

        if len(parts) >= 3 and parts[0] == "ICAPContinuousMigration":
            return parts[1]
        return ""

    def _format_overdue(self, translations: dict[str, str]) -> str | None:
        if not self.display_due_date or not isinstance(self.display_due_date, date):
            return None

        closed_statuses = {status.value for status in ActionStatusClosedEnum}
        is_closed = self.current_status.external_id in closed_statuses
        due_date = self.display_due_date
        today = datetime.now().date()

        is_overdue_key = "true" if not is_closed and due_date < today else "false"
        return self._get_translation(is_overdue_key, translations)

    def to_response(self, translations: dict[str, str]) -> ActionExportResponse:
        """Cast the entity to its response type."""
        return ActionExportResponse(
            external_id=self.external_id,
            space=self.space,
            title=self.title,
            current_status=(
                self._get_translation(
                    getattr(self.current_status, "external_id", None),
                    translations,
                )
                if self.current_status
                else None
            ),
            assignee=(
                self.assigned_to.user._format_user()
                if self.assigned_to and self.assigned_to.user
                else None
            ),
            application=getattr(self.application, "alias", None)
            or getattr(self.application, "name", None),
            assignment_date=str(self.assignment_date) if self.assignment_date else None,
            due_date=str(self.due_date) if self.due_date else None,
            conclusion_date=str(self.conclusion_date) if self.conclusion_date else None,
            reporting_site=getattr(self.reporting_site, "name", None),
            reporting_unit=self._format_reporting_unit(),
            reporting_location=getattr(self.reporting_location, "description", None),
            category=(
                self._get_translation(
                    getattr(self.category, "external_id", None),
                    translations,
                )
                if self.category
                else None
            ),
            sub_category=(
                self._get_translation(
                    getattr(self.sub_category, "external_id", None),
                    translations,
                )
                if self.sub_category
                else None
            ),
            site_specific_category=getattr(self.site_specific_category, "name", None),
            owner=(
                self.owner.user._format_user()
                if self.owner and self.owner.user
                else None
            ),
            approver=self.approver._format_user() if self.approver else None,
            approval_date=str(self.approval_date) if self.approval_date else None,
            verifier=self.verifier._format_user() if self.verifier else None,
            verification_date=(
                str(self.verification_date) if self.verification_date else None
            ),
            source_information=self.source_information,
            source_event_title=self.source_event_title,
            is_private=self._get_translation(
                (
                    str(self.is_private).lower()
                    if self.is_private is not None
                    else "false"
                ),
                translations,
            ),
            icap_action_id=self._extract_icap_action_id_from_object_type(
                self.object_type,
            ),
            priority=self.priority,
            ext=str(self.ext),
            manager=self.manager._format_user() if self.manager else None,
            overdue=self._format_overdue(translations),
        )


class CommentItem(Node):
    """Represents a comment item with metadata such as timestamp and user information."""

    is_legacy: bool = Field(default=False)
    comment: str | None = Field(default=None)
    timestamp: str | None = Field(default=None)
    user: _User | None = Field(default=None)


class ActionByIdResult(ActionResult):
    """Stores details about an action identified by its ID, including comments, priority, and attachments."""

    description: str
    voe_action_item: str | None = Field(default=None)
    assignee_comment: str | None = Field(default=None)

    evidence_required: bool | None = Field(default=None)
    view_only: bool | None = Field(default=None)

    attachments: list[AttachmentsEntity] | None = Field(default=None)

    action_item_link: Annotated[list[_ActionItemLink], edge_unwraper_validator]
    history_instance: Annotated[list[_StatusHistoryInstance], edge_unwraper_validator]

    comments: Annotated[list[CommentItem], edge_unwraper_validator]

    recurrence_instance: Node | None = Field(default=None)
    is_template: bool | None


class ActionByIdForEditingResult(ActionResult):
    """Stores editable details about an action item, including assignment, recurrence, and viewing permissions."""

    description: str
    reporting_line: DescribableEntity | None
    action_item_link: Annotated[list[_ActionItemLink], edge_unwraper_validator]
    voe_action_item: str | None = Field(default=None)

    evidence_required: bool | None

    action_item_kind: DescribableEntity | None
    recurrence_instance: Recurrence | None
    is_template: bool | None
    assignees: Annotated[list[_User], edge_unwraper_validator_with_edge_id] | None
    attachments: list[AttachmentsEntity] | None

    view_users: Annotated[list[_UserAzureAttribute], edge_unwraper_validator] | None
    view_roles: Annotated[list[DescribableEntity], edge_unwraper_validator] | None
    view_teams: Annotated[list[DescribableEntity], edge_unwraper_validator] | None


class ApprovalWorkflowStepUpdate(BaseEntity):
    """Represents an approval workflow step, tracking status, assigned users, and timestamps."""

    approval_condition: Node | None = None
    approval_workflow_consent_type: Node | None = None
    approval_workflow: Node | None = None
    status: Node | None = None
    users: list[Node] | None = None

    step: int | None = None

    start_date: str | None = None
    end_date: str | None = None
    approval_date: str | None = None
    description: str | None = None

    def get_properties_to_include(self) -> list[str]:
        """Return a list of properties that should be included in serialization, excluding certain fields."""
        properties = self.model_dump(
            by_alias=True,
            exclude={"external_id", "space"},
            exclude_none=True,
        )
        return list(properties.keys())


class ActionItemLink(BaseEntity):
    """Represents a link to an action item, including a reference to the action and a description."""

    action: Node | None = None
    link: str | None = None
    description: str | None = None

    def get_properties_to_include(self) -> list[str]:
        """Return a list of properties that should be included in serialization, excluding certain fields."""
        properties = self.model_dump(
            by_alias=True,
            exclude={"external_id", "space"},
            exclude_none=True,
        )
        return list(properties.keys())


class RecurrenceInstance(BaseEntity):
    """Represents a recurrence pattern, including frequency, specific days, and date ranges."""

    description: str | None = None
    recurrence_type: Node | None = None
    week_days: list[int] | None = None
    months: list[int] | None = None
    day_of_the_month: int | None = None
    quarters: list[int] | None = None
    month_of_the_year: int | None = None
    next_dates: list[str] | None = None
    start_date: str | None = None
    end_date: str | None = None

    def get_properties_to_include(self) -> list[str]:
        """Return a list of properties that should be included in serialization, excluding certain fields."""
        properties = self.model_dump(
            by_alias=True,
            exclude={"external_id", "space"},
        )
        return list(properties.keys())


class StatusHistoryInstance(BaseEntity):
    """Represents the history of status changes for an action, tracking changes and timestamps."""

    action: Node
    status: Node
    friendly_name: str | None = None
    changed_at: str
    status_subject: Node | None = None

    def get_properties_to_include(self) -> list[str]:
        """Return a list of properties that should be included in serialization, excluding certain fields."""
        properties = self.model_dump(
            by_alias=True,
            exclude={"external_id", "space"},
            exclude_none=True,
        )
        return list(properties.keys())


class AttachmentRequest(BaseModel):
    """Represents a request to attach a file, including metadata and validation properties."""

    received_external_id: str | None = None
    target_external_id: str | None = None
    action_item_external_ids: list[str] | None = None
    file_size: str | None = None
    name: str | None = None
    mime_type: str | None = None
    target_dataset_id: int | None = None

    @property
    def is_file_from_aim(self) -> bool:
        """Determines if the file originates from AIM based on matching external IDs."""
        return self.received_external_id == self.target_external_id


class ActionItemUpdate(BaseEntity):
    """Represents an update to an action item, including details such as status, assignee, description, priority, and associated files, along with methods for managing related nodes and edges."""

    history_instance: list[StatusHistoryInstance] | None = Field(
        default=None,
        exclude=True,
    )
    current_status: Node | None = Field(default=None)
    assignee_comment: str | None = Field(default=None)
    assigned_to: Node | None = Field(default=None)

    title: str | None = Field(default=None)
    description: str | None = Field(default=None)
    owner: Node | None = Field(default=None)
    reporting_unit: Node | None = Field(default=None)
    reporting_location: Node | None = Field(default=None)
    reporting_line: Node | None = Field(default=None)

    source_information: str | None = Field(default=None)
    category: Node | None = Field(default=None)
    sub_category: Node | None = Field(default=None)
    site_specific_category: Node | None = Field(default=None)

    priority: str | None = Field(default=None)

    assignment_date: str | None = Field(default=None)

    evidence_required: bool | None = Field(default=None)
    views: list[str] | None = Field(default=None)

    # None = not changed, empty list = erased
    attachments: list[str] | None = Field(default=None)
    assignees: list[str] | None = Field(default=None, exclude=True)
    action_item_link: list[ActionItemLink] | None = Field(default=None, exclude=True)
    view_users: list[Node] | None = Field(default=None, exclude=True)
    view_roles: list[Node] | None = Field(default=None, exclude=True)
    view_teams: list[Node] | None = Field(default=None, exclude=True)

    sort_reporting_unit: str | None = Field(default=None)
    sort_reporting_location: str | None = Field(default=None)
    sort_current_status: str | None = Field(default=None)
    sort_assignee: str | None = Field(default=None)
    sort_owner: str | None = Field(default=None)
    sort_category: str | None = Field(default=None)
    sort_sub_category: str | None = Field(default=None)
    sort_site_specific_category: str | None = Field(default=None)
    sort_approver: str | None = Field(default=None)
    sort_verifier: str | None = Field(default=None)
    sort_source_event_title: str | None = Field(default=None)

    approval_workflow: (
        Annotated[
            ApprovalWorkflowUpdate,
            PlainSerializer(ApprovalWorkflowUpdate.serialize_as_base_entity),
        ]
        | None
    ) = Field(default=None)
    due_date: str | None = Field(default=None)
    display_due_date: str | None = Field(default=None)
    conclusion_date: str | None = Field(default=None)
    approval_date: str | None = Field(default=None)
    verification_date: str | None = Field(default=None)

    action_item_kind: Node | None = Field(default=None)
    recurrence_instance: (
        Annotated[
            RecurrenceInstance,
            PlainSerializer(RecurrenceInstance.serialize_as_base_entity),
        ]
        | None
    ) = Field(default=None)

    nodes_to_delete: list[NodeId] = Field(default_factory=list, exclude=True)
    edges_to_delete: list[EdgeId] = Field(default_factory=list, exclude=True)

    def get_properties_to_include(self) -> list[str]:
        """Return a list of properties that should be included in serialization, excluding certain fields."""
        properties = self.model_dump(
            by_alias=True,
            exclude={"external_id", "space"},
            exclude_unset=True,
        )
        return list(properties.keys())


class ActionItemChangeRequest(BaseEntity):
    """Represents a request to change an action item."""

    action: Annotated[
        ActionItemUpdate,
        PlainSerializer(ActionItemUpdate.serialize_as_base_entity),
    ]
    change_request_type: Node | None = Field(default=None)
    comments: list[str] | None = Field(default=None)
    properties_to_change: dict | None = Field(default=None)
    approval_workflow: (
        Annotated[
            ApprovalWorkflowUpdate,
            PlainSerializer(ApprovalWorkflowUpdate.serialize_as_base_entity),
        ]
        | None
    ) = Field(default=None)
    attatchments: list[str] | None = Field(default=None)

    def get_properties_to_include(self) -> list[str]:
        """Return a list of properties that should be included in serialization, excluding certain fields."""
        properties = self.model_dump(
            by_alias=True,
            exclude={"external_id", "space"},
            exclude_none=True,
        )
        return list(properties.keys())


class ActionItemForUpdateComparisonResult(BaseEntity):
    """Stores the result of comparing an action item before and after an update."""

    attachments: list[AttachmentsEntity] | None = Field(default=None)
    owner: Node | None = Field(default=None)
    assigned_to: Node | None = Field(default=None)
    approval_workflow: ApprovalWorkflow | None = Field(default=None)
    is_private: bool | None = Field(default=None)
    action_item_link: Annotated[list[BaseEntity], edge_unwraper_validator]
    assignees: Annotated[
        list[ViewEntityForUpdateComparisonResult],
        edge_unwraper_validator_with_edge_id,
    ]
    view_users: Annotated[
        list[ViewEntityForUpdateComparisonResult],
        edge_unwraper_validator_with_edge_id,
    ]
    view_roles: Annotated[
        list[ViewEntityForUpdateComparisonResult],
        edge_unwraper_validator_with_edge_id,
    ]
    view_teams: Annotated[
        list[ViewEntityForUpdateComparisonResult],
        edge_unwraper_validator_with_edge_id,
    ]
    source_events: Annotated[
        list[SourceEventForUpdateComparisonResult],
        edge_unwraper_validator,
    ]

    @staticmethod
    def _get_all_view_entities_map(
        items: list[ViewEntityForUpdateComparisonResult],
    ) -> dict[str, ViewEntityForUpdateComparisonResult]:
        return {v.external_id: v for v in items}

    def get_mandatory_view_users_from_source_event(self) -> set[str]:
        """
        Retrieve mandatory view users from the first source event.

        Returns:
            A set of UserAzureAttribute external IDs of mandatory view users.

        """
        mandatory_view_users: set[str] = set()
        if len(self.source_events) == 0:
            return mandatory_view_users

        source_event = self.source_events[0]
        if source_event.owner is not None:
            mandatory_view_users.add(source_event.owner.external_id)

        mandatory_view_users.update(
            sou.external_id for sou in source_event.secondary_owner_users
        )

        mandatory_view_users.update(vu.external_id for vu in source_event.view_users)
        return mandatory_view_users

    def get_all_view_users_map(self) -> dict[str, ViewEntityForUpdateComparisonResult]:
        """
        Retrieve a mapping of all view users.

        Returns:
            A dictionary mapping UserAzureAttribute external IDs to their view entity objects.

        """
        return self._get_all_view_entities_map(self.view_users)

    def get_all_view_users_external_ids(self) -> set[str]:
        """
        Retrieve all view user external IDs.

        Returns:
            A set of UserAzureAttribute external IDs.

        """
        return set(self.get_all_view_users_map().keys())

    def get_all_view_users(self) -> list[ViewEntityForUpdateComparisonResult]:
        """
        Retrieve view users.

        Returns:
            A list of view entities.

        """
        all_view_users_external_ids = self.get_all_view_users_external_ids()

        all_view_users_map = self._get_all_view_entities_map(self.view_users)

        return [all_view_users_map[vu] for vu in all_view_users_external_ids]

    def get_mandatory_view_roles_from_source_event(self) -> set[str]:
        """
        Retrieve mandatory view roles from the first source event.

        Returns:
            A set of external IDs of mandatory view roles.

        """
        mandatory_view_roles: set[str] = set()
        if len(self.source_events) == 0:
            return mandatory_view_roles

        source_event = self.source_events[0]
        mandatory_view_roles.update(
            (sor.external_id for sor in source_event.secondary_owner_roles),
        )
        mandatory_view_roles.update(vr.external_id for vr in source_event.view_roles)
        return mandatory_view_roles

    def get_all_view_roles_external_ids(self) -> set[str]:
        """
        Retrieve all view role external IDs.

        Returns:
            A set of role external IDs.

        """
        return set(self._get_all_view_entities_map(self.view_roles).keys())

    def get_all_view_roles(self) -> list[ViewEntityForUpdateComparisonResult]:
        """
        Retrieve view roles.

        Returns:
            A list of view entities.

        """
        all_view_roles_external_ids = self.get_all_view_roles_external_ids()

        all_view_roles_map = self._get_all_view_entities_map(self.view_roles)

        return [all_view_roles_map[vr] for vr in all_view_roles_external_ids]

    def get_mandatory_view_teams_from_source_event(self) -> set[str]:
        """
        Retrieve mandatory view teams from the first source event.

        Returns:
            A set of external IDs of mandatory view teams.

        """
        mandatory_view_teams: set[str] = set()
        if len(self.source_events) == 0:
            return mandatory_view_teams

        source_event = self.source_events[0]
        mandatory_view_teams.update(
            (sot.external_id for sot in source_event.secondary_owner_teams),
        )
        mandatory_view_teams.update(vt.external_id for vt in source_event.view_teams)
        return mandatory_view_teams

    def get_all_view_teams_external_ids(self) -> set[str]:
        """
        Retrieve all view team external IDs.

        Returns:
            A set of team external IDs.

        """
        return set(self._get_all_view_entities_map(self.view_teams).keys())

    def get_all_view_teams(self) -> list[ViewEntityForUpdateComparisonResult]:
        """
        Retrieve view teams.

        Returns:
            A list of view entities.

        """
        all_view_teams_external_ids = self.get_all_view_teams_external_ids()

        all_view_teams_map = self._get_all_view_entities_map(self.view_teams)

        return [all_view_teams_map[vt] for vt in all_view_teams_external_ids]


class ViewFieldsUpdater:
    """Updates view fields based on changes."""

    def __init__(
        self,
        current: ActionItemForUpdateComparisonResult,
        update: ActionItemUpdate,
    ) -> None:
        """Initialize the instance with current data and updates."""
        self._c = current
        self._u = update

    def execute(
        self,
        edges_to_upsert: list[EdgeApply],
        edges_to_delete: list[str],
    ) -> None:
        """Update user, role, and team views."""
        view_users, view_users_was_changed = self._process_users(
            edges_to_upsert,
            edges_to_delete,
        )
        view_roles, view_roles_was_changed = self._process_roles(
            edges_to_upsert,
            edges_to_delete,
        )
        view_teams, view_teams_was_changed = self._process_teams(
            edges_to_upsert,
            edges_to_delete,
        )

        if (
            not view_users_was_changed
            and not view_roles_was_changed
            and not view_teams_was_changed
        ):
            return

        self._u.views = list(view_users | view_roles | view_teams)

    def _process_users(
        self,
        edges_to_upsert: list[EdgeApply],
        edges_to_delete: list[str],
    ) -> tuple[set[str], bool]:
        changed_properties = self._u.model_fields_set

        approval_workflow_was_changed = "approval_workflow" in changed_properties
        assigned_to_was_changed = "assigned_to" in changed_properties
        owner_was_changed = "owner" in changed_properties
        view_users_was_changed = self._u.view_users is not None

        if (
            not approval_workflow_was_changed
            and not assigned_to_was_changed
            and not owner_was_changed
            and not view_users_was_changed
        ):
            return self._c.get_all_view_users_external_ids(), False

        approver_external_id = self._get_current_approver_external_id()
        verifier_external_id = self._get_current_verifier_external_id()
        new_approver_external_id = None
        new_verifier_external_id = None
        if approval_workflow_was_changed:
            new_approver_external_id = self._get_new_approver_external_id()
            new_verifier_external_id = self._get_new_verifier_external_id()

        if (  # needed because approval workflow is the only entity here that we can change internally without changing user-related fields
            approval_workflow_was_changed
            and new_approver_external_id == approver_external_id
            and new_verifier_external_id == verifier_external_id
            and not assigned_to_was_changed
            and not owner_was_changed
            and not view_users_was_changed
        ):
            return self._c.get_all_view_users_external_ids(), False

        approver_external_id = (
            new_approver_external_id
            if approval_workflow_was_changed
            else approver_external_id
        )
        verifier_external_id = (
            new_verifier_external_id
            if approval_workflow_was_changed
            else verifier_external_id
        )

        assigned_to_external_id = self._get_current_assigned_to_external_id()
        if assigned_to_was_changed:
            assigned_to_external_id = self._get_new_assigned_to_external_id()

        owner_external_id = self._get_current_owner_external_id()
        if owner_was_changed:
            owner_external_id = self._get_new_owner_external_id()

        current_all_views_users = self._c.get_all_view_users()
        all_view_users = [csu.external_id for csu in current_all_views_users]
        if view_users_was_changed:
            assert self._u.view_users is not None
            all_view_users = [vu.external_id for vu in self._u.view_users]

        current_all_view_users_external_ids = self._c.get_all_view_users_external_ids()
        users_to_add_edges = set(all_view_users) - current_all_view_users_external_ids
        edges_to_upsert.extend(
            [
                EdgeApply(
                    external_id=f"{self._c.external_id}-viewUser-{external_id}",
                    space=self._c.space,
                    type=EdgeType.ACTION_TO_VIEW_USERS.value,
                    start_node=(self._c.space, self._c.external_id),
                    end_node=(DataSpaceEnum.UMG_DATA_SPACE.value, external_id),
                )
                for external_id in users_to_add_edges
            ],
        )

        users_to_delete_edges = current_all_view_users_external_ids - set(
            all_view_users,
        )
        current_all_view_users_map = self._c.get_all_view_users_map()
        for external_id in users_to_delete_edges:
            user = current_all_view_users_map.get(external_id)
            if user is None:
                continue
            edges_to_delete.append(user.edge_external_id)

        new_view_users: set[str] = self._c.get_mandatory_view_users_from_source_event()

        if approver_external_id is not None:
            new_view_users.add(to_user_azure_attribute(approver_external_id))
        if verifier_external_id is not None:
            new_view_users.add(to_user_azure_attribute(verifier_external_id))
        if assigned_to_external_id is not None:
            new_view_users.add(assigned_to_external_id)
        if owner_external_id is not None:
            new_view_users.add(owner_external_id)

        new_view_users.update(all_view_users)

        return new_view_users, True

    def _process_roles(
        self,
        edges_to_upsert: list[EdgeApply],
        edges_to_delete: list[str],
    ) -> tuple[set[str], bool]:
        """
        Process the view roles by determining changes and updating the necessary edges.

        Args:
            edges_to_upsert (list[EdgeApply]): List of edges to be added or updated.
            edges_to_delete (list[str]): List of edges to be removed.

        Returns:
            tuple[set[str], bool]:
                - A set of updated view roles.
                - A boolean indicating whether changes were made.

        """
        if self._u.view_roles is None:
            return self._c.get_all_view_roles_external_ids(), False

        current_all_view_roles = self._c.get_all_view_roles()
        all_view_roles = [vu.external_id for vu in self._u.view_roles]
        edges_to_delete.extend(
            csr.edge_external_id
            for csr in current_all_view_roles
            if csr.external_id not in all_view_roles
        )

        current_all_view_roles_external_ids = self._c.get_all_view_roles_external_ids()
        for nvr in all_view_roles:
            if nvr in current_all_view_roles_external_ids:
                continue

            edges_to_upsert.append(
                EdgeApply(
                    external_id=f"{self._c.external_id}-viewRole-{nvr}",
                    space=self._c.space,
                    type=EdgeType.ACTION_TO_VIEW_ROLES.value,
                    start_node=(self._c.space, self._c.external_id),
                    end_node=(DataSpaceEnum.UMG_DATA_SPACE.value, nvr),
                ),
            )

        new_view_roles: set[str] = self._c.get_mandatory_view_roles_from_source_event()
        new_view_roles.update(all_view_roles)

        return new_view_roles, True

    def _process_teams(
        self,
        edges_to_upsert: list[EdgeApply],
        edges_to_delete: list[str],
    ) -> tuple[set[str], bool]:
        if self._u.view_teams is None:
            return self._c.get_all_view_teams_external_ids(), False

        current_all_view_teams = self._c.get_all_view_teams()
        all_view_teams = [vu.external_id for vu in self._u.view_teams or []]
        edges_to_delete.extend(
            cst.edge_external_id
            for cst in current_all_view_teams
            if cst.external_id not in all_view_teams
        )

        current_all_view_teams_external_ids = self._c.get_all_view_teams_external_ids()
        for nvt in all_view_teams:
            if nvt in current_all_view_teams_external_ids:
                continue

            edges_to_upsert.append(
                EdgeApply(
                    external_id=f"{self._c.external_id}-viewTeam-{nvt}",
                    space=self._c.space,
                    type=EdgeType.ACTION_TO_VIEW_TEAMS.value,
                    start_node=(self._c.space, self._c.external_id),
                    end_node=(DataSpaceEnum.UMG_DATA_SPACE.value, nvt),
                ),
            )

        new_view_teams: set[str] = self._c.get_mandatory_view_teams_from_source_event()
        new_view_teams.update(all_view_teams)

        return new_view_teams, True

    def _get_current_approver_external_id(self) -> str | None:
        return (
            approver.external_id
            if self._c.approval_workflow is not None
            and (approver := self._c.approval_workflow.get_approver()) is not None
            else None
        )

    def _get_current_verifier_external_id(self) -> str | None:
        return (
            verifier.external_id
            if self._c.approval_workflow is not None
            and (verifier := self._c.approval_workflow.get_verifier()) is not None
            else None
        )

    def _get_new_approver_external_id(self) -> str | None:
        return (
            approver.external_id
            if self._u.approval_workflow is not None
            and (approver := self._u.approval_workflow.get_approver()) is not None
            else None
        )

    def _get_new_verifier_external_id(self) -> str | None:
        return (
            verifier.external_id
            if self._u.approval_workflow is not None
            and (verifier := self._u.approval_workflow.get_verifier()) is not None
            else None
        )

    def _get_current_assigned_to_external_id(self) -> str | None:
        return (
            self._c.assigned_to.external_id if self._c.assigned_to is not None else None
        )

    def _get_new_assigned_to_external_id(self) -> str | None:
        return (
            self._u.assigned_to.external_id if self._u.assigned_to is not None else None
        )

    def _get_current_owner_external_id(self) -> str | None:
        return self._c.owner.external_id if self._c.owner is not None else None

    def _get_new_owner_external_id(self) -> str | None:
        return self._u.owner.external_id if self._u.owner is not None else None


class ActionItemForAssigneeRequestProcess(Node):
    """Stores the result of comparing an action item before and after an update."""

    due_date: date | None = Field(default=None)
    is_private: bool | None = Field(default=None)
    owner: _UserAzureAttribute | None = Field(default=None)
    assigned_to: _UserAzureAttribute | None = Field(default=None)
    site_specific_category: DescribableEntity | None = Field(default=None)
    approval_workflow: ApprovalWorkflow | None = Field(default=None)
    source_events: Annotated[list[_SourceEvent], edge_unwraper_validator]
    change_requests: Annotated[list[ChangeRequest], edge_unwraper_validator] | None = (
        None
    )
    view_users: Annotated[list[_UserAzureAttribute], edge_unwraper_validator] | None
    view_roles: Annotated[list[DescribableEntity], edge_unwraper_validator] | None
    view_teams: Annotated[list[DescribableEntity], edge_unwraper_validator] | None

    @computed_field
    @property
    def approver(self) -> _User | None:
        """Retrieves the approver from the approval workflow if available."""
        return (
            None
            if not self.approval_workflow
            else self.approval_workflow.get_approver()
        )

    @computed_field
    @property
    def verifier(self) -> _User | None:
        """Retrieves the verifier from the approval workflow if available."""
        return (
            None
            if not self.approval_workflow
            else self.approval_workflow.get_verifier()
        )
