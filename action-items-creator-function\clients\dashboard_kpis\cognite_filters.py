from cognite.client.data_classes import data_modeling

from clients.core.constants import DataSpaceEnum
from clients.core.filters import (
    contains_sdk_filter,
    equals_sdk_filter,
    in_sdk_filter,
)
from utils.date_utils import map_property_date_ranges

from .requests import GetDashboardKpisRequest, create_node_dict


def get_common_kpis_filters(
    request: GetDashboardKpisRequest,
    action_view: data_modeling.ViewId,
) -> list[data_modeling.Filter]:
    """
    Construct a list of filters to apply to KPI-related queries.

    Args:
        request (GetDashboardKpisRequest): Object containing all possible filters such as dates, status,
            locations, units, assignees, and categories.
        action_view (ViewId): View identifier referencing the data model structure.

    Returns:
        list[Filter]: List of filter objects to apply in data modeling queries.

    """
    common_filters = [
        _get_kpis_space_filter(request, action_view),
        equals_sdk_filter("reportingSite", request.filters.reporting_site, action_view),
    ]

    date_filters = {
        "gte": request.filters.due_date_gte,
        "lt": request.filters.due_date_lt,
    }
    if any(date_filters.values()):
        common_filters.append(
            data_modeling.filters.Range(
                action_view.as_property_ref("displayDueDate"),
                **{key: date.isoformat() for key, date in date_filters.items() if date},
            ),
        )

    if request.filters.reporting_units:
        common_filters.append(
            in_sdk_filter(
                "reportingUnit",
                request.filters.reporting_units,
                action_view,
            ),
        )

    if request.filters.reporting_locations:
        common_filters.append(
            in_sdk_filter(
                "reportingLocation",
                request.filters.reporting_locations,
                action_view,
            ),
        )

    if request.filters.categories:
        common_filters.append(
            in_sdk_filter("category", request.filters.categories, action_view),
        )

    if request.filters.subcategories:
        common_filters.append(
            in_sdk_filter("subCategory", request.filters.subcategories, action_view),
        )

    if request.filters.site_specific_categories:
        common_filters.append(
            in_sdk_filter(
                "siteSpecificCategory",
                request.filters.site_specific_categories,
                action_view,
            ),
        )

    if request.filters.status:
        common_filters.append(
            in_sdk_filter(
                "currentStatus",
                request.filters.status,
                action_view,
            ),
        )

    if request.filters.update_status:
        update_status_date = map_property_date_ranges(
            request.filters.update_status,
            action_view.as_property_ref("displayDueDate"),
        )
        common_filters.append(data_modeling.filters.Or(*update_status_date))
        common_filters.append(
            in_sdk_filter(
                "currentStatus",
                request.filters.default_overdue_statuses,
                action_view,
            ),
        )

    if request.filters.assignees:
        common_filters.append(
            in_sdk_filter(
                "assignedTo",
                request.filters.assignees,
                action_view,
            ),
        )

    if request.filters.not_in_assignee_external_ids:
        assignees = [
            create_node_dict(external_id, DataSpaceEnum.UMG_DATA_SPACE)
            for external_id in request.filters.not_in_assignee_external_ids
        ]
        common_filters.append(
            data_modeling.filters.Not(
                in_sdk_filter(
                    "assignedTo",
                    assignees,
                    action_view,
                ),
            ),
        )

    if request.filters.source_event_title_eq:
        common_filters.append(
            equals_sdk_filter(
                "sortSourceEventTitle",
                request.filters.source_event_title_eq.lower(),
                action_view,
            ),
        )

    return common_filters


def get_kpis_current_status_filter(
    request: GetDashboardKpisRequest,
    action_view: data_modeling.ViewId,
) -> data_modeling.Filter:
    """
    Construct a filter to exclude KPIs with closed statuses.

    Args:
        request (GetDashboardKpisRequest): Object containing the list of statuses to exclude.
        action_view (ViewId): View identifier referencing the data model structure.

    Returns:
        Filter: A filter excluding KPIs with specified closed statuses.

    """
    return data_modeling.filters.Not(
        in_sdk_filter(
            "currentStatus",
            request.filters.closed_statuses,
            action_view,
        ),
    )


def _get_kpis_space_filter(
    request: GetDashboardKpisRequest,
    action_view: data_modeling.ViewId,
) -> data_modeling.Filter:
    """
    Construct a space filter to distinguish between private and public KPI entries.

    Args:
        request (GetDashboardKpisRequest): Object containing flags and parameters for space filtering.
        action_view (ViewId): View identifier referencing the data model structure.

    Returns:
        Filter: A filter determining whether to query private, public, or both KPI spaces.

    """
    filter_private = data_modeling.filters.And(
        data_modeling.filters.SpaceFilter(
            request.get_filter_spaces(private_space=True),
        ),
        contains_sdk_filter("views", request.views_private, action_view),
    )

    filter_site = data_modeling.filters.SpaceFilter(
        request.get_filter_spaces(private_space=False),
    )

    if request.filters.only_private:
        return filter_private
    if request.filters.only_private is not None:
        return filter_site
    return data_modeling.filters.Or(filter_site, filter_private)
