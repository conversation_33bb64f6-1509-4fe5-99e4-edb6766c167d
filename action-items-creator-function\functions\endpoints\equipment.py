import json
import logging
from http import HTT<PERSON>tatus

import azure.functions as func
from pydantic import ValidationError

from clients.equipment.requests import GetEquipmentRequest
from infra.action_item_client_factory import ActionItemClientFactory
from services.equipment_service import EquipmentService

bp = func.Blueprint()
logging.basicConfig(format="%(message)s", level=logging.INFO)


@bp.function_name(name="GetEquipments")
@bp.route(
    "get-equipments",
    methods=["get"],
    auth_level=func.AuthLevel.ANONYMOUS,
)
def main(req: func.HttpRequest) -> func.HttpResponse:
    """Handle GET requests to retrieve equipment details based on the provided request parameters."""
    try:
        request_param = req.params.get("equipmentRequest", "{}")
        request_data = json.loads(request_param)

        request = GetEquipmentRequest.model_validate(request_data)
        service = EquipmentService(ActionItemClientFactory.retriever())

        equipments = service.get_equipments(request)

        result = [
            equipment.model_dump(mode="json", by_alias=True) for equipment in equipments
        ]

        return func.HttpResponse(
            json.dumps(result, default=str),
            status_code=HTTPStatus.OK,
            mimetype="application/json",
        )

    except ValidationError as ve:
        logging.exception("Validation error")
        return func.HttpResponse(
            json.dumps({"error": "Invalid request data", "details": ve.errors()}),
            status_code=HTTPStatus.BAD_REQUEST,
            mimetype="application/json",
        )

    except Exception as e:
        logging.exception("Unhandled exception occurred")
        return func.HttpResponse(
            json.dumps({"error": "Internal server error", "details": str(e)}),
            status_code=HTTPStatus.INTERNAL_SERVER_ERROR,
            mimetype="application/json",
        )
