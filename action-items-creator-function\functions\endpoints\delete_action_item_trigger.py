import azure.functions as func
from infra.action_item_trigger_remover_factory import ActionItemTriggerRemoverFactory

bp = func.Blueprint()

@bp.function_name(name="DeleteActionItemTriggers")
@bp.route("delete-action-item-trigger", methods=["delete"], auth_level=func.AuthLevel.ANONYMOUS)
async def main(req: func.HttpRequest) -> func.HttpResponse:
    import logging
    import json as json

    try:
        body = req.get_json()
        logging.info("Function DeleteActionItemTriggers started")
        result = await ActionItemTriggerRemoverFactory.remover().delete_action_item_trigger(body)
        logging.info(f"Finishing execution - Status : {result}")
        return func.HttpResponse(json.dumps(result))
    except Exception as e:
        logging.info("Exception found!")
        return func.HttpResponse(f"Error: {e}", status_code=500)
    