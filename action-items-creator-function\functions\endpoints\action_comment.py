import json as json
import logging
from http import HTTPStatus

import azure.functions as func

from clients.action_comment.requests import (
    CreateActionCommentRequest,
)
from clients.core.constants import APPLICATION_JSON
from infra.action_item_client_factory import ActionItemClientFactory
from services.action_comment_service import ActionCommentService

bp = func.Blueprint()


@bp.function_name(name="CreateActionComment")
@bp.route(
    "create-action-comment",
    methods=["post"],
    auth_level=func.AuthLevel.ANONYMOUS,
)
async def create_action_comment(req: func.HttpRequest) -> func.HttpResponse:
    """
    Azure Function endpoint to create an action comment.

    This function receives a POST request containing data to create a comment
    on an action item. It parses the request, validates the input, and delegates
    the creation process to the ActionCommentService.

    Parameters
    ----------
    req : func.HttpRequest
        The HTTP request object containing headers and JSON body.

    Returns
    -------
    func.HttpResponse
        A JSON response with the external IDs of created comments or an error message.

    """
    logging.info("Function CreateActionComment started")
    try:
        token_request = req.headers.get("Authorization")

        body = req.get_json()

        request = CreateActionCommentRequest.model_validate(body)

        logging.info("Creating Action Comment")

        service = ActionCommentService(
            ActionItemClientFactory.retriever(override_token=token_request),
        )
        external_ids = await service.create_action_comment(request=request)

        logging.info("CreateActionComment executed successfully")
        return func.HttpResponse(
            json.dumps({"externalIds": external_ids}),
            status_code=(HTTPStatus.OK),
            mimetype=APPLICATION_JSON,
        )
    except Exception as e:
        logging.error(f"Error in CreateActionComment: {e}", exc_info=True)
        return func.HttpResponse(
            f"Error: {e}",
            status_code=HTTPStatus.INTERNAL_SERVER_ERROR,
            mimetype=APPLICATION_JSON,
        )
