from enum import Enum


class EntitiesEnum(Enum):
    Action = "ACT"
    ActionItemLink = "ACTL"
    StatusHistoryInstance = "SHINS"
    RecurrenceInstance = "RCI"
    ChangeRequest = "ACIR"
    Attachment = "ACTATT"
    ApprovalWorkflow = "APWE"
    ApprovalWorkflowStep = "APWSP"
    ProcessedActionItemEvent = "PACTEVT"
    ProcessedActionItemEventStatus = "PACTEVTS"
    SourceEvent = "SEVT"
    SourceEventHistory = "SEVTH"
    ActionComment = "ACTCMNT"
    MetadataField = "MTDTF"
