from pydantic import (
    BaseModel,
    ConfigDict,
    computed_field,
)
from pydantic.alias_generators import to_camel

from clients.core.constants import DataSpaceEnum


class CreateActionCommentRequest(BaseModel):
    """Request model for creating a comment on an action item."""

    model_config = ConfigDict(
        alias_generator=to_camel,
        populate_by_name=True,
        from_attributes=True,
        arbitrary_types_allowed=True,
    )

    comment: str
    action_external_id: str
    is_private: bool
    reporting_site_external_id: str
    timestamp: str

    user_email: str
    user_external_id: str | None = None

    @computed_field
    @property
    def space(self) -> str:
        """Compute the data space based on the site code and privacy flag."""
        site_code = self.reporting_site_external_id.split("-")[1]
        return (
            DataSpaceEnum.PRIVATE_SPACE
            if self.is_private
            else f"AIM-{site_code}-ALL-DAT"
        )
