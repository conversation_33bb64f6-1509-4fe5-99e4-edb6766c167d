import { CSSObject } from '@mui/material'

export const WarningStyle: CSSObject = {
    backgroundColor: 'warning.100',
    color: 'warning.main',
}

export const PrimaryMainStyle: CSSObject = {
    backgroundColor: 'primary.100',
    color: 'primary.main',
}

export const SuccessStyle: CSSObject = {
    backgroundColor: 'success.100',
    color: 'success.dark',
}

export const ErrorStyle: CSSObject = {
    backgroundColor: 'error.100',
    color: 'error.dark',
}

export const NeutralStyle: CSSObject = {
    backgroundColor: 'text.disabled',
    color: 'text.primary',
}

export const InfoStyle: CSSObject = {
    backgroundColor: 'info.100',
    color: 'info.main',
}

export const Box: CSSObject = {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    padding: '0 0.5rem',
    height: '20px',
    borderRadius: '50px',
}
