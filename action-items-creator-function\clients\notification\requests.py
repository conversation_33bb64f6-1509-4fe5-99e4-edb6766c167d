from typing import Any
from pydantic import BaseModel, ConfigDict

from pydantic.alias_generators import to_camel

from clients.notification.constants import ACTION_ITEM_CREATION, PRIMITIVE_TYPES


class NotificationProperty(BaseModel):
    name: str
    value: str
    type: str


class NotificationRequest(BaseModel):
    model_config = ConfigDict(
        alias_generator=to_camel,
        populate_by_name=True,
        from_attributes=True,
    )
    notification_type: str
    description: str
    users: list[str]
    properties: list[NotificationProperty]

    @classmethod
    def from_action_item_creation(
        cls, action_item_dict: dict[str, Any], assignedTo_email: str, owner_email: str
    ) -> "NotificationRequest":
        properties = create_notification_properties(action_item_dict)
        user_ids_for_notification = [assignedTo_email, owner_email]

        return cls(
            notification_type=ACTION_ITEM_CREATION,
            description=ACTION_ITEM_CREATION,
            properties=properties,
            users=list(set(user_ids_for_notification)),
        )

    @classmethod
    def create_notification_request(
        cls,
        action_item_dict: dict[str, Any],
        users: list[str],
        notification_type: str,
        description: str | None = None,
    ) -> "NotificationRequest":
        properties = create_notification_properties(action_item_dict)

        return cls(
            notification_type=notification_type,
            description=description if description is not None else notification_type,
            properties=properties,
            users=list(set(users)),
        )


def create_properties(key: str, value: Any) -> list[NotificationProperty]:
    if not value:
        return []

    if type(value) in PRIMITIVE_TYPES:
        prop_type = "text" if type(value) is str else "number"
        return [NotificationProperty(name=key, value=str(value), type=prop_type)]

    properties: list[NotificationProperty] = []

    keys_to_search = {
        "externalId": "ExternalId",
        "name": "Name",
        "description": "Description",
        "space": "Space",
    }

    for key_to_search, suffix in keys_to_search.items():
        if key_to_search in value:
            properties.append(
                NotificationProperty(
                    name=f"{key}{suffix}",
                    value=str(value[key_to_search]),
                    type="text",
                )
            )
    return properties


def create_notification_properties(
    object: dict[str, Any]
) -> list[NotificationProperty]:
    properties: list[NotificationProperty] = []
    for key, value in object.items():
        if value is None:
            continue
        if type(value) is list:
            index = -1

            for value_item in value:
                index += 1
                result_properties = create_properties(key, value_item)
                for item in result_properties:
                    item.name = f"{item.name}_{index}"
                properties.extend(result_properties)
        else:
            properties.extend(create_properties(key, value))
    return properties
