'use client'
import { ClnPanel } from '@celanese/ui-lib'
import { Box } from '@mui/material'
import { useState } from 'react'
import { Category, Status } from '../common/models/action'
import { styles } from './styles'
import AuthGuardWrapper from '../common/wrapper/AuthGuardWrapper'
import SiteCharts from '../components/Dashboard/SiteTab/SiteCharts'
import SiteKpis from '../components/Dashboard/SiteTab/SiteKpis'
import { FilterInfoProps } from '../components/ActionTable/HomeFilter'
import { SiteTable } from '../components/Dashboard/SiteTab/SiteTable'
import { AzureFunctionClient } from '../common/clients/azure-function-client'
import GenericFieldTitle from '../components/FieldsComponent/GenericFieldTitle'
import { useSnackbar } from '../common/contexts/SnackbarContext'
import { translate } from '../common/utils/generate-translate'
import { UserRolesPermission } from '@celanese/celanese-sdk'

interface SiteTabProps {
    siteId: string
    categories: Category[]
    allStatus: Status[]
    activeUser?: UserRolesPermission
    isSiteTab: boolean
    filterInfo: FilterInfoProps
    currentPage: number
    setCurrentPage: (value: number) => void
}

export const SiteTab = ({
    siteId,
    categories,
    allStatus,
    activeUser,
    isSiteTab,
    filterInfo,
    currentPage,
    setCurrentPage,
}: SiteTabProps) => {
    const client = new AzureFunctionClient()

    const { showSnackbar } = useSnackbar()

    const [isChart, setIsChart] = useState(true)

    const handleError = (err: any) => {
        const errorMessage = err instanceof Error ? err.message : `${translate('alerts.unexpectedErrorOcurred')}`
        showSnackbar(`${translate('alerts.unexpectedErrorOcurred')}: ${errorMessage}`, 'error', 'dashboard-site-tab')
    }

    return (
        <AuthGuardWrapper componentName={SiteTab.name}>
            {isSiteTab && (
                <Box sx={styles.container} id={'dashboard-site-tab'}>
                    <SiteKpis
                        activeUser={activeUser}
                        filterInfo={filterInfo}
                        client={client}
                        handleError={handleError}
                    />

                    <ClnPanel
                        sx={{
                            padding: '1rem',
                            display: 'flex',
                            flexDirection: 'column',
                        }}
                        id={'dashboard-site-tab-graphc-and-table-panel'}
                    >
                        <GenericFieldTitle fieldName={translate('dashboards.tabs.site')} isSubHeader />
                        {isChart ? (
                            <SiteCharts
                                categories={categories}
                                allStatus={allStatus}
                                handleError={handleError}
                                setIsChart={setIsChart}
                                activeUser={activeUser}
                                filterInfo={filterInfo}
                                client={client}
                            />
                        ) : (
                            <Box id={'table-dashboard-site'}>
                                <SiteTable
                                    activeUser={activeUser}
                                    siteId={siteId}
                                    filterInfo={filterInfo}
                                    client={client}
                                    handleError={handleError}
                                    setIsChart={(value: boolean) => setIsChart(value)}
                                    currentPage={currentPage}
                                    setCurrentPage={setCurrentPage}
                                />
                            </Box>
                        )}
                    </ClnPanel>
                </Box>
            )}
        </AuthGuardWrapper>
    )
}
export default SiteTab
