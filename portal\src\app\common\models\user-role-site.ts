import { Role, Team } from '@celanese/celanese-sdk'
import { ExternalEntity } from './common'
import { UserAzureAttribute } from './common/user-management/user-azure-attribute'

export interface UserRoleSite extends ExternalEntity {
    role: ExternalEntity
    reportingSite: ExternalEntity
    usersComplements: { items: UserComplements[] }
}

export interface UserComplements extends ExternalEntity {
    reportingUnits: { items: ExternalEntity[] }
    userAzureAttribute: UserAzureAttribute
}

export interface UserRoleSiteById extends ExternalEntity {
    externalId: string
    usersComplements: { items: UserComplementsById[] }
}

export interface UserComplementsById extends ExternalEntity {
    reportingUnits: { items: ExternalEntity[] }
    userAzureAttribute: UserAzureAttribute
}

export interface UserComplementByEmail extends ExternalEntity {
    userAzureAttribute: UserAzureAttribute
    userRoleSite: UserRoleSite[]
    reportingSites: ExternalEntity[]
}

export interface UserRolesExternalIds {
    externalId: string
    roles: Role[]
    teams: Team[]
}
