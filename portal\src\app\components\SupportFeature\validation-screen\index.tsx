import { Box, Typography } from '@mui/material'
import React from 'react'
import { ClnButton } from '@celanese/ui-lib'
import { blueGrey, grey } from '@mui/material/colors'
import { translate } from '@celanese/celanese-ui'
import { UserRolesPermission } from '@celanese/celanese-sdk'

interface SupportFeatureValidationProps {
    subjectTitle: string
    typeOfRequest: string
    description?: string
    onFinalSubmit: () => Promise<void>
    onBack: () => void
    sitesSelected?: string[]
    action?: string
    objective?: string
    natureOfIncident?: string
    expectedFunctionality?: string
    previouslyFunctioning?: string
    incidentStepDecision?: string
    impactOnWork?: string
    uploadedFiles?: File[]
    isButtonDisabled: boolean
    userInfo: UserRolesPermission
}

export const ValidationScreen = ({
    subjectTitle,
    typeOfRequest,
    description,
    onFinalSubmit,
    sitesSelected,
    action,
    impactOnWork,
    onBack,
    expectedFunctionality,
    natureOfIncident,
    objective,
    previouslyFunctioning,
    isButtonDisabled,
    incidentStepDecision,
    uploadedFiles,
    userInfo,
}: SupportFeatureValidationProps) => {
    const displayUserName =
        userInfo.firstName && userInfo.lastName ? `${userInfo.firstName} ${userInfo.lastName}` : userInfo.displayName

    return (
        <Box
            sx={{
                display: 'flex',
                flexDirection: 'column',
                padding: '1.5rem',
                gap: '1.5rem',
            }}
        >
            <Box
                sx={{
                    backgroundColor: 'background.default',
                    borderRadius: '0.5rem',
                    padding: '1rem',
                }}
            >
                <Typography
                    sx={{
                        color: (theme) => theme.palette.text.primary,
                    }}
                >
                    {translate('supportFeature.reviewMessage')}{' '}
                </Typography>
            </Box>
            <Box
                sx={{
                    backgroundColor: 'background.default',
                    borderRadius: '0.5rem',
                    padding: '1rem',
                    display: 'flex',
                    justifyContent: 'space-between',
                    flexWrap: 'wrap',
                }}
            >
                <Box display="flex" flexDirection="column">
                    <Typography
                        sx={{
                            color: 'text.secondary',
                            fontSize: '0.75rem',
                        }}
                    >
                        {translate('supportFeature.name')}
                    </Typography>
                    <Typography>{displayUserName}</Typography>
                </Box>
                <Box display="flex" flexDirection="column">
                    <Typography
                        sx={{
                            color: 'text.secondary',
                            fontSize: '0.75rem',
                        }}
                    >
                        {translate('supportFeature.role')}
                    </Typography>
                    <Typography>{userInfo.jobTitle}</Typography>
                </Box>
                <Box display="flex" flexDirection="column">
                    <Typography
                        sx={{
                            color: 'text.secondary',
                            fontSize: '0.75rem',
                        }}
                    >
                        {translate('supportFeature.site')}
                    </Typography>
                    <Typography>{userInfo.selectedSite?.siteName}</Typography>
                </Box>
                <Box display="flex" flexDirection="column">
                    <Typography
                        sx={{
                            color: 'text.secondary',
                            fontSize: '0.75rem',
                        }}
                    >
                        {translate('supportFeature.email')}
                    </Typography>
                    <Typography>{userInfo.email}</Typography>
                </Box>
            </Box>

            <Box
                display="flex"
                flexDirection="column"
                bgcolor="background.default"
                gap="1rem"
                padding="1rem"
                borderRadius="0.5rem"
            >
                <Box>
                    <Typography
                        sx={{
                            color: 'text.secondary',
                            fontSize: '0.75rem',
                        }}
                    >
                        {translate('supportFeature.subject')}
                    </Typography>
                    <Typography>{subjectTitle}</Typography>
                </Box>
                <Box>
                    <Typography
                        sx={{
                            color: 'text.secondary',
                            fontSize: '0.75rem',
                        }}
                    >
                        {translate('supportFeature.typeOfRequest')}
                    </Typography>
                    <Typography>{typeOfRequest}</Typography>
                </Box>
                {typeOfRequest === 'incident' ? (
                    <React.Fragment>
                        <Box>
                            <Typography
                                sx={{
                                    color: 'text.secondary',
                                    fontSize: '0.75rem',
                                }}
                            >
                                {translate('supportFeature.natureOfIncident')}
                            </Typography>
                            <Typography>{natureOfIncident}</Typography>
                        </Box>
                        <Box>
                            <Typography
                                sx={{
                                    color: 'text.secondary',
                                    fontSize: '0.75rem',
                                }}
                            >
                                {translate('supportFeature.previouslyFunctioning')}
                            </Typography>
                            <Typography>{previouslyFunctioning}</Typography>
                        </Box>
                        <Box>
                            <Typography
                                sx={{
                                    color: 'text.secondary',
                                    fontSize: '0.75rem',
                                }}
                            >
                                {translate('supportFeature.incidentStepByStepDescription')}
                            </Typography>
                            <Typography>{incidentStepDecision}</Typography>
                        </Box>
                        <Box>
                            <Typography
                                sx={{
                                    color: 'text.secondary',
                                    fontSize: '0.75rem',
                                }}
                            >
                                {translate('supportFeature.expectedFunctionality')}
                            </Typography>
                            <Typography>{expectedFunctionality}</Typography>
                        </Box>
                        <Box>
                            <Typography
                                sx={{
                                    color: 'text.secondary',
                                    fontSize: '0.75rem',
                                }}
                            >
                                {translate('supportFeature.impactOnWork')}
                            </Typography>
                            <Typography>{impactOnWork}</Typography>
                        </Box>
                        <Box>
                            <Typography
                                sx={{
                                    color: 'text.secondary',
                                    fontSize: '0.75rem',
                                }}
                            >
                                {translate('supportFeature.impactedSites')}
                            </Typography>
                            {sitesSelected?.map((item) => <Typography key={item}>{item}</Typography>)}
                        </Box>
                    </React.Fragment>
                ) : (
                    <React.Fragment>
                        <Box>
                            <Typography
                                sx={{
                                    color: 'text.secondary',
                                    fontSize: '0.75rem',
                                }}
                            >
                                {translate('supportFeature.action')}
                            </Typography>
                            <Typography>{action}</Typography>
                        </Box>
                        <Box>
                            <Typography
                                sx={{
                                    color: 'text.secondary',
                                    fontSize: '0.75rem',
                                }}
                            >
                                {translate('supportFeature.objective')}
                            </Typography>
                            <Typography>{objective}</Typography>
                        </Box>
                    </React.Fragment>
                )}
                <Box>
                    <Typography
                        sx={{
                            color: 'text.secondary',
                            fontSize: '0.75rem',
                        }}
                    >
                        {translate('supportFeature.description')}
                    </Typography>
                    <Typography>{description}</Typography>
                </Box>
                <Box
                    sx={{
                        alignSelf: 'start',
                        display: 'flex',
                        flexDirection: 'column',
                        gap: '0.5rem',
                    }}
                >
                    {uploadedFiles?.map((file, index) => (
                        <Box
                            key={index}
                            sx={{
                                marginTop: '10px',
                                padding: '0.5rem 0.75rem',
                                borderRadius: '0.5rem',
                                display: 'flex',
                                backgroundColor: grey[300],
                                alignItems: 'center',
                                gap: '0.5rem',
                            }}
                        >
                            <Typography
                                sx={{
                                    fontWeight: 500,
                                    marginRight: '0.5rem',
                                }}
                            >
                                {file.name}
                            </Typography>
                            <Typography
                                sx={{
                                    color: blueGrey[600],
                                }}
                            >
                                {(file.size / 1048576).toFixed(2)}mb
                            </Typography>
                        </Box>
                    ))}
                </Box>
            </Box>

            <Box sx={{ display: 'flex', gap: '1rem', gridColumn: '2 /  2', justifyContent: 'end' }}>
                <ClnButton label={translate('stepper.back')} variant="text" onClick={onBack} />
                <ClnButton
                    disabled={isButtonDisabled}
                    label={translate('common.submit')}
                    onClick={async () => {
                        await onFinalSubmit()
                    }}
                    variant="contained"
                />
            </Box>
        </Box>
    )
}
