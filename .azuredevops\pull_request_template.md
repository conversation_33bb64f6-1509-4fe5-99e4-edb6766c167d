Please include a summary of the changes made and explain why this change is required.

- **What is changed?**
  - [ ] Bug Fix
  - [ ] New Feature
  - [ ] Documentation Update
  - [ ] Library Update
  - [ ] Translation Update
  - [ ] Code Refactor
  - [ ] Other

- **Changes Location**
  - [ ] Backend
  - [ ] Frontend
  - [ ] Database
  - [ ] CI/CD
  - [ ] Documentation

## Code Validation

- [ ] I have reviewed my own code
- [ ] I have added appropriate documentation or comments where necessary
- [ ] My code follows the project style guide and linting rules
- [ ] I have updated the changelog, explaining what was changed, and validated it according to the versioning rules:
  - 🔺 MAJOR (1.x.x): Changes that break compatibility.
  - 🔹 MINOR (x.2.x): New features without breaking changes.
  - 🔧 PATCH (x.x.3): Bug fixes and small improvements without breaking changes.

## Screenshots (if applicable)

(Provide screenshots if applicable to show before and after changes in the UI or other visual elements.)

## Additional Notes

(Include any additional information or context for the reviewer. If there are special deployment considerations, please list them here.)
