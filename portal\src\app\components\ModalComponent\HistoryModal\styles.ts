export const historyDrawerStyles = {
    verticalCenterCell: {
        display: 'flex',
        alignItems: 'center',
        height: '100%',
    },
    verticalCenterColumnWrap: {
        display: 'flex',
        flexDirection: 'column' as const,
        justifyContent: 'center',
        height: '100%',
        whiteSpace: 'pre-wrap' as const,
        wordBreak: 'break-word' as const,
    },
    cancelButtonContainer: {
        display: 'flex',
        justifyContent: 'right',
        margin: '1.5rem 0 0 0',
    },
    datagridTableContainer: {
        display: 'flex',
        flexDirection: 'column',
        height: '100%',
        overflowY: 'auto',
        overflowX: 'hidden',
    },
}
