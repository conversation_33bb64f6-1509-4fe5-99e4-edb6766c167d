from typing import Annotated

from fastapi import APIRouter, Depends

from ..validations import is_running_locally
from .client import ListReportingUnitClient
from .models import ListReportingUnitResponse, ReportingUnitResponse

router = APIRouter(
    prefix="/example/list-reporting-unit",
    dependencies=[Depends(is_running_locally)],
    include_in_schema=False,
)


@router.get("", response_model=list[ReportingUnitResponse])
async def list_reporting_unit(
    client: Annotated[ListReportingUnitClient, Depends()],
) -> ListReportingUnitResponse:
    """Mimmick a simplified create action endpoint."""
    return await client.list()
