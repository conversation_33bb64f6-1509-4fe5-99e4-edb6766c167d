from datetime import datetime, timezone
from http import HTT<PERSON>tatus
from http.client import <PERSON><PERSON><PERSON><PERSON>xception
from typing import Optional

from cognite.client.data_classes.data_modeling.ids import NodeId

from clients.action_comment.models import ActionCommentCreate
from clients.action_comment.requests import (
    CreateActionCommentRequest,
)
from clients.action_item_client import ActionItemClient
from clients.core.constants import USER_AZURE_ATTRIBUTE_PREFIX, DataSpaceEnum
from clients.core.models import Node
from clients.user_complement.requests import (
    GetUserRolesAndTeamsRequest,
)
from models.entities_enum import EntitiesEnum
from services.user_complements_service import UserByEmailResult, UserComplementsService
from utils.id_generation_utils import IdGenerator


class ActionCommentService:
    """Service layer for managing action comment creation and processing."""

    def __init__(
        self,
        action_item_client: ActionItemClient,
    ) -> None:
        """
        Initialize the ActionCommentService.

        Args:
            action_item_client (ActionItemClient): Client used to interact with action items.

        """
        self._action_item_client = action_item_client
        self._now_datetime = datetime.now(timezone.utc)
        self._id_generator = IdGenerator[EntitiesEnum](self._now_datetime)

    def _build_action_comment(
        self,
        request: CreateActionCommentRequest,
    ) -> ActionCommentCreate:
        """
        Build an ActionCommentCreate object from the request data.

        Args:
            request (CreateActionCommentRequest): Request data for creating an action comment.

        Returns:
            ActionCommentCreate: A fully populated action comment model ready for persistence.

        """
        return ActionCommentCreate(
            external_id=self._id_generator.next_id(EntitiesEnum.ActionComment),
            space=request.space,
            comment=request.comment,
            action=Node(
                external_id=request.action_external_id,
                space=request.space,
            ),
            user=Node(
                external_id=request.user_external_id,
                space=DataSpaceEnum.UMG_DATA_SPACE,
            ),
            is_legacy=False,
            timestamp=request.timestamp,
        )

    async def create_action_comment(
        self,
        request: CreateActionCommentRequest,
    ) -> list[NodeId] | None:
        """
        Create a new action comment in Cognite Data Fusion.

        Args:
            request (CreateActionCommentRequest): Data required to create the comment.

        Returns:
            list[NodeId] | None: A list containing the created node ID(s), or None if creation failed.

        Raises:
            HTTPException: If the user cannot be found (unauthorized).

        """
        user = await self._get_user_by_email(
            request.user_email,
            request.reporting_site_external_id,
        )

        if user is None:
            self._raise_unauthorized(request.user_email)

        request.user_external_id = user.external_id.removeprefix(
            USER_AZURE_ATTRIBUTE_PREFIX,
        )

        action_comment = self._build_action_comment(request)
        return self._action_item_client.action_comment.create_action_comment(
            action_comment,
        )

    async def _get_user_by_email(
        self,
        email: str,
        site_external_id: str,
    ) -> Optional[UserByEmailResult]:
        """
        Retrieve a user by email and site.

        This function searches for a user based on their email address and the external ID of the reporting site.
        If the user is found, it returns the user's details; otherwise, it returns None.

        Args:
            email (str): The email address of the user to be retrieved.
            site_external_id (str): The external ID of the reporting site associated with the user.

        Returns:
            Optional[UserByEmailResult]:
                - The user details if the user is found.
                - None if no matching user is found.

        """
        user_by_email_request = GetUserRolesAndTeamsRequest(
            email=email,
            reporting_site_external_id=site_external_id,
        )

        try:
            user_service = UserComplementsService(self._action_item_client)
            user: Optional[UserByEmailResult] = await user_service.get_user_by_email(
                user_by_email_request,
            )

            if user is None:
                self._raise_unauthorized(email)

        except Exception as err:
            self._log.error(f"Error retrieving user by email {email}. Error: {err}")
            self._raise_unauthorized(email)
        else:
            return user

    def _raise_unauthorized(self, email: str) -> None:
        self._log.error(f"No user found with email: {email}")
        raise HTTPException(status_code=HTTPStatus.UNAUTHORIZED)
