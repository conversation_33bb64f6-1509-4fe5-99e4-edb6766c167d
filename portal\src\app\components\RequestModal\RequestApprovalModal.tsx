import { NoTranslate } from '@celanese/celanese-ui'
import { ActionDetailItem } from '@/app/common/models/action-detail'
import { MIN_DEFAULT_TEXT_FIELD } from '@/app/common/utils'
import AuthGuardWrapper from '@/app/common/wrapper/AuthGuardWrapper'
import { ClnButton, ClnCircularProgress, ClnTextField } from '@celanese/ui-lib'
import { zodResolver } from '@hookform/resolvers/zod'
import { Box, List, ListItemText, Typography, useMediaQuery, useTheme } from '@mui/material'
import dayjs from 'dayjs'
import React, { useMemo, useState } from 'react'
import { Controller, SubmitHandler, useForm } from 'react-hook-form'
import { z } from 'zod'
import { ActionStatusExternalIdClearEnum } from '@/app/common/enums/ActionItemStatusEnum'
import { AzureFunctionClient } from '@/app/common/clients/azure-function-client'
import { translate } from '@/app/common/utils/generate-translate'
import { CustomDrawer } from '../ModalComponent/Drawer/Drawer'
import { drawerStyles } from '../ModalComponent/Drawer/styles'
import * as S from './styles'
import { SITE_EXTERNAL_ID_REQUIRED_FIELD } from '@/app/common/utils/validate-codes'

interface RequestModalProps {
    onClose: () => void
    client: AzureFunctionClient | null
    openDrawer: boolean
    action: ActionDetailItem
    activeUserEmail: string
    handleAlert: (message: string, error?: boolean) => void
    redirectToLastPage: () => void
    redirectToPage?: () => void
}

const formSchema = z.object({
    comments: z.string().min(MIN_DEFAULT_TEXT_FIELD),
})

type RequestSchema = z.infer<typeof formSchema>

export function RequestApprovalModal({
    client,
    openDrawer,
    action,
    activeUserEmail,
    onClose,
    handleAlert,
    redirectToPage,
    redirectToLastPage,
}: RequestModalProps) {
    const theme = useTheme()
    const isBelowSm = useMediaQuery(theme.breakpoints.down('sm'))

    const [loading, setLoading] = useState<boolean>(false)

    const {
        control,
        handleSubmit,
        getValues,
        reset,
        formState: { errors },
    } = useForm<RequestSchema>({
        resolver: zodResolver(formSchema),
        defaultValues: {
            comments: '',
        },
    })

    const viewApproval =
        (activeUserEmail === action?.assignedTo?.user?.email &&
            action?.currentStatus?.externalId === ActionStatusExternalIdClearEnum.Assigned) ||
        action?.currentStatus?.externalId === ActionStatusExternalIdClearEnum.PendingApproval ||
        action?.currentStatus?.externalId === ActionStatusExternalIdClearEnum.PendingVerification

    const parseFriendlyName = (jsonString: string | undefined): string => {
        try {
            if (jsonString) {
                const formattedJsonString = jsonString.replace(/\n/g, '\\n')
                const parsedJson = JSON.parse(formattedJsonString)

                return parsedJson.comments || ''
            }
            return ''
        } catch (error) {
            handleAlert(translate('alerts.unexpectedErrorOcurred'), true)
            return ''
        }
    }

    const approvalComments = useMemo(() => {
        const targetHistoryInstance = action?.historyInstance?.find(
            (history) => history.friendlyName && history.friendlyName.includes('"actionUser": "approvedBy"')
        )

        return targetHistoryInstance ? parseFriendlyName(targetHistoryInstance?.friendlyName ?? '') : undefined
    }, [action?.historyInstance])

    const submitFn: (data: RequestSchema, isApproved: boolean) => Promise<void> = async (data, isApproved) => {
        try {
            setLoading(true)
            const statusSubmit = isApproved ? 'Approved' : 'Rejected'
            const isPendingApproval =
                action?.currentStatus?.externalId === ActionStatusExternalIdClearEnum.PendingApproval

            const requestData = {
                externalId: action?.externalId,
                space: action?.space,
                activeUserEmail: activeUserEmail,
                reportingSiteExternalId: action.reportingSite?.externalId ?? SITE_EXTERNAL_ID_REQUIRED_FIELD,
                ...(isPendingApproval
                    ? {
                          approverComment: data.comments,
                          approvalStatus: statusSubmit,
                      }
                    : {
                          verifierComment: data.comments,
                          verificationStatus: statusSubmit,
                      }),
            }
            const result = await client?.upsertActionApprovalWorkflow(action?.externalId, requestData)
            handleAlert(`${translate('alerts.dataSavedWithSuccess')}: ${result.externalId}`)
            onClose()
            reset({ comments: '' })
            if (redirectToPage) {
                redirectToPage()
            } else {
                redirectToLastPage()
            }
        } catch (error) {
            handleAlert(translate('alerts.unexpectedErrorOcurred'), true)
        } finally {
            setLoading(false)
        }
    }

    const onSubmitApprove: SubmitHandler<RequestSchema> = (data) => {
        submitFn(data, true)
    }

    const onSubmitReject: SubmitHandler<RequestSchema> = (data) => {
        submitFn(data, false)
    }

    return (
        <AuthGuardWrapper componentName={RequestApprovalModal.name}>
            <CustomDrawer
                overlineMeta={translate('requestModal.actionItemManagement')}
                title={translate(
                    action?.currentStatus?.externalId === ActionStatusExternalIdClearEnum.PendingApproval
                        ? 'requestModal.pendingApproval'
                        : 'requestModal.pendingVerification'
                )}
                openDrawer={openDrawer}
                closeDrawer={onClose}
                content={
                    loading ? (
                        <Box sx={S.loaderContainerStyle}>
                            <ClnCircularProgress size={40} value={0} />
                        </Box>
                    ) : (
                        <Box sx={drawerStyles.container}>
                            <Box sx={S.boxContainerStyle}>
                                <List
                                    sx={{
                                        display: 'grid',
                                        gridTemplateColumns: 'repeat(2, 1fr)',
                                        gap: '1rem',
                                        columnGap: isBelowSm ? '1rem' : '6rem',
                                        placeContent: 'start flex-start',
                                        padding: 0,
                                    }}
                                >
                                    <ListItemText sx={{ whiteSpace: 'nowrap !important' }}>
                                        <Typography sx={{ fontWeight: 'bold' }}>
                                            {translate('requestModal.action')}
                                        </Typography>
                                        {action?.externalId}
                                    </ListItemText>
                                    <ListItemText />
                                    <ListItemText sx={{ whiteSpace: 'nowrap !important' }}>
                                        <Typography sx={{ fontWeight: 'bold' }}>
                                            {translate('requestModal.title')}
                                        </Typography>
                                        {action?.title}
                                    </ListItemText>
                                    <ListItemText />
                                    <ListItemText>
                                        <Typography sx={{ fontWeight: 'bold' }}>
                                            {translate('requestModal.fromAssignee')}
                                        </Typography>
                                        <NoTranslate>
                                            {action?.assignedTo?.user?.lastName} , {action?.assignedTo?.user?.firstName}
                                        </NoTranslate>
                                    </ListItemText>
                                    <ListItemText>
                                        <Typography sx={{ fontWeight: 'bold' }}>
                                            {translate('requestModal.fromDueDate')}
                                        </Typography>
                                        {action?.dueDate ? <>{dayjs(action?.dueDate).format('MM/DD/YYYY')}</> : ''}
                                    </ListItemText>
                                    <ListItemText
                                        sx={{
                                            gridColumn: 'span 2',
                                        }}
                                        hidden={
                                            approvalComments === undefined ||
                                            action?.currentStatus?.externalId !==
                                                ActionStatusExternalIdClearEnum.PendingVerification
                                        }
                                    >
                                        <Typography sx={{ fontWeight: 'bold' }}>
                                            {translate('requestModal.approverComments')}
                                        </Typography>
                                        <ClnTextField
                                            value={approvalComments}
                                            label=""
                                            variant="outlined"
                                            required
                                            helperText=""
                                            disabled={true}
                                            multiline
                                            rows={6}
                                            sx={{ width: '100%' }}
                                        />
                                    </ListItemText>
                                </List>
                                <form style={S.formContainerStyle}>
                                    <Controller
                                        control={control}
                                        name="comments"
                                        render={({ field: { value, onChange } }) => (
                                            <ClnTextField
                                                onChange={(e) => onChange(e.target.value)}
                                                value={getValues('comments')}
                                                label={translate('requestModal.approvalComments')}
                                                variant="outlined"
                                                required
                                                multiline
                                                minRows={5}
                                                maxRows={6}
                                                helperText={translate('stepper.form.helperTextApprovalModal')}
                                                error={Boolean(errors.comments)}
                                            />
                                        )}
                                    />
                                </form>
                            </Box>
                            {viewApproval && (
                                <Box component={S.ButtonContainerStyle}>
                                    <ClnButton
                                        variant={'contained'}
                                        label={translate('requestModal.reject')}
                                        onClick={handleSubmit(onSubmitReject)}
                                        color="error"
                                        type="button"
                                        sx={{ flex: 1 }}
                                    />

                                    <ClnButton
                                        label={translate('requestModal.approve')}
                                        onClick={handleSubmit(onSubmitApprove)}
                                        sx={{ flex: 1 }}
                                    />
                                </Box>
                            )}
                        </Box>
                    )
                }
            />
        </AuthGuardWrapper>
    )
}

export default RequestApprovalModal
