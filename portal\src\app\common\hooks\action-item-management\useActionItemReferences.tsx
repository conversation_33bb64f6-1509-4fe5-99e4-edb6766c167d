import { gql } from '@apollo/client'
import { useEffect, useState } from 'react'
import { useGraphqlQuery } from '../'
import { EntityType, GetSpace } from '../../utils/space-util'
import { Reference } from '../../models/reference'
import { getLocalUserSite } from '@celanese/celanese-ui'

const buildReferenceQuery = (): string => {
    const filters: string[] = []

    filters.push(
        `{ space: { in: [ "${GetSpace(EntityType.Instance, getLocalUserSite()?.siteCode)}", "${GetSpace(
            EntityType.Instance
        )}" ] } }`
    )

    const queryFilter = `{ and: [ ${filters.join(', ')} ] }`

    return `
        query GetReference {
            listActionItemReference(
                filter: ${queryFilter}
                , first: 1000
                , sort: { name: ASC }
            ) {
                items {
                    externalId
                    space
                    name
                    description
                    externalId
                    application {
                        name
                        description
                        url
                        externalId
                    }
                }
            }
        }
    `
}

export const useActionItemReferences = () => {
    const query = buildReferenceQuery()
    const { data: fdmData, refetch } = useGraphqlQuery<Reference>(gql(query), 'listActionItemReference', {})

    const [resultData, setResultData] = useState<{ data: Reference[]; loading: boolean }>({
        data: [],
        loading: true,
    })

    useEffect(() => {
        if (fdmData.length == 0) {
            setResultData({ data: [], loading: false })
        } else {
            setResultData({ data: fdmData, loading: false })
        }
    }, [fdmData])

    return {
        loading: resultData.loading,
        references: resultData.data,
        refetchReferences: refetch,
    }
}
