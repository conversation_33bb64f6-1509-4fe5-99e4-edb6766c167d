import { useCallback } from 'react'
import { useFdmDeleter, useFdmMutation } from '..'
import { Reference } from '../../models/reference'

export const useActionItemReferenceMutations = () => {
    const [referenceMutation, referenceMutationStatus] = useFdmMutation<Reference>('ActionItemReference', false)
    const [referenceDeleter] = useFdmDeleter()

    const createOrUpdateReference = useCallback(
        async (reference: Reference) => {
            const referenceMutationResult = await referenceMutation([reference], [])

            return {
                ok: referenceMutationResult.ok,
                data: referenceMutationResult.data,
                error: referenceMutationResult.error,
            }
        },
        [referenceMutation]
    )

    const deleteReferences = useCallback(
        async (data: Reference[]) => {
            const dataToRequest = data.map((x) => x.externalId)

            return await referenceDeleter({ externalIds: dataToRequest })
        },
        [referenceDeleter]
    )

    return [createOrUpdateReference, deleteReferences, referenceMutationStatus] as const
}
