import { AuthGuardPermission, authGuardRules } from 'auth/auth-guard-rules'
import { environment } from '../configurations/environment'
import { useContext } from 'react'
import { UserManagementContext, UserManagementContextState } from '@celanese/celanese-ui'
import { UserRolesPermission } from '@celanese/celanese-sdk'

export interface AuthGuardResult {
    isAuthorized: boolean
    sitesIds?: string[]
    message: string
}

export const useAuthGuard = () => {
    const { userInfo } = useContext<UserManagementContextState>(UserManagementContext)

    function checkPermissionsFromRoutes(path: string): boolean {
        const routePermission = authGuardRules.routes.find((c) => c.path === path)
        return CheckPermissions(routePermission).isAuthorized
    }

    function CheckPermissions(
        permission: AuthGuardPermission | undefined,
        siteId?: string,
        siteList?: boolean
    ): AuthGuardResult {
        const userPermission: UserRolesPermission = userInfo

        if (permission) {
            let siteIdsList: string[] = []
            const roles =
                userPermission?.applications?.find((a) => a.applicationCode == environment.userManagementAppCode)
                    ?.roles ?? []
            const rolesBySiteId = siteId
                ? roles.filter((role) => role.siteCodes.some((siteCode) => siteId === siteCode))
                : roles
            const hasRoles = permission.roleCodes.some((r) => roles.some((x) => x.roleCode === r))

            const hasFeature = permission.features.some((f) =>
                rolesBySiteId.some((x) =>
                    x.features.some(
                        (y) => y.featureCode === f.featureCode && y.featureAccessLevelCode === f.featureAccessLevelCode
                    )
                )
            )

            if (siteList && hasFeature) {
                const validRoles = rolesBySiteId.filter((role) =>
                    role.features.some((feature) =>
                        permission.features.some(
                            (pf) =>
                                pf.featureCode === feature.featureCode &&
                                pf.featureAccessLevelCode === feature.featureAccessLevelCode
                        )
                    )
                )
                const siteIdsSet = new Set<string>(siteIdsList)

                validRoles.forEach((role) => {
                    role.siteCodes.forEach((code) => {
                        siteIdsSet.add(code)
                    })
                })
                siteIdsList = Array.from(siteIdsSet)
            }

            return {
                isAuthorized: hasRoles || hasFeature,
                sitesIds: siteIdsList,
                message: permission.notAuthorizedMessage,
            }
        } else {
            return {
                isAuthorized: true,
                message: '',
            }
        }
    }

    function checkPermissionsFromComponents(
        componentName: string,
        siteId?: string,
        siteList?: boolean
    ): AuthGuardResult {
        if (!userInfo) {
            return {
                isAuthorized: false,
                sitesIds: [],
                message: '',
            }
        }

        const componentPermission = authGuardRules.components.find((c) => c.name === componentName)
        return CheckPermissions(componentPermission, siteId, siteList)
    }

    return {
        checkPermissionsFromRoutes,
        checkPermissionsFromComponents,
    }
}
