from datetime import UTC, datetime, timedelta
from typing import Any

import pytest
from fastapi import status
from fastapi.testclient import TestClient

from app.infra.setup.tests import get_test_client
from app.main import app
from app.modules.example.create_action.errors import ErrorMessage
from app.modules.example.list_reporting_unit.client import ListReportingUnitClient
from app.modules.example.list_reporting_unit.models import (
    ListReportingUnitResponse,
    ReportingUnitResponse,
)

PATH = "/example/create-action"


@pytest.fixture
def test_client():
    app.dependency_overrides[ListReportingUnitClient] = ListReportingUnitClientMock
    return get_test_client(app)


class ListReportingUnitClientMock(ListReportingUnitClient):
    @pytest.mark.anyio
    async def list(self):  # type: ignore[override-type]
        return ListReportingUnitResponse(
            (
                ReportingUnitResponse(
                    external_id="UNT-WASFIL",
                    space="REF-COR-ALL-DAT",
                ),
                ReportingUnitResponse(
                    external_id="UNT-WASADM",
                    space="REF-COR-ALL-DAT",
                ),
            ),
        )


@pytest.fixture
def valid_body():
    return {
        "request": {
            "reportingSiteExternalId": "STS-WAS",
            "reportingUnitExternalId": "UNT-WASFIL",
            "dueDate": (datetime.now(UTC) + timedelta(days=1))
            .date()
            .strftime("%Y-%m-%d"),
        },
    }


def test_create_action_overall_validation(
    test_client: TestClient,
    valid_body: dict[str, Any],
):
    response = test_client.post(PATH, json=valid_body)

    assert response.status_code == status.HTTP_200_OK


@pytest.fixture
def invalid_reporting_unit_body(valid_body: dict[str, Any]):
    request = valid_body["request"]
    request = {**request, "reportingUnitExternalId": "INEXISTENT-ID"}
    return {"request": request}


def test_create_action_reporting_unit_validation(
    test_client: TestClient,
    invalid_reporting_unit_body: dict[str, Any],
):

    response = test_client.post(
        PATH,
        json=invalid_reporting_unit_body,
    )

    assert response.status_code == status.HTTP_400_BAD_REQUEST
    assert response.json().get("detail") == ErrorMessage.REPORTING_UNIT_DOES_NOT_EXIST


@pytest.fixture
def invalid_due_date_body(valid_body: dict[str, Any]):
    request = valid_body["request"]
    request = {
        **request,
        "dueDate": (datetime.now(UTC) + timedelta(days=-1)).date().strftime("%Y-%m-%d"),
    }
    return {"request": request}


def test_create_action_due_date_validation(
    test_client: TestClient,
    invalid_due_date_body: dict[str, Any],
):

    response = test_client.post(
        PATH,
        json=invalid_due_date_body,
    )

    assert response.status_code == status.HTTP_400_BAD_REQUEST
    assert response.json().get("detail") == ErrorMessage.DUE_DATE_IN_THE_PAST
