import { useCallback } from 'react'
import { translate } from '@celanese/celanese-ui'
import { environment } from '@/app/common/configurations/environment'
import { aimAreaPath, AZURE_APP_NAME_DP, AZURE_APP_ORGANIZATION } from '@/app/common/utils'
import { AppInformationProps } from '@/app/components/SupportFeature/consts'
import { RequestTypeEnum } from '../enums/RequestTypeEnum'
import { WorkItemTypeEnum } from '../enums/WorkItemTypeEnum'
import { WorkItemBodyInformation } from '../models/work-item-body-information'

interface UzeAzureServiceProps {
    application?: string
    appInformation: AppInformationProps
    typeOfRequest: RequestTypeEnum
}
export const encodeBase64 = (data: string): string => {
    return Buffer.from(data, 'utf-8').toString('base64')
}

export const useAzureService = ({ typeOfRequest, appInformation }: UzeAzureServiceProps) => {
    const pat = environment.supportFeature
    const project = AZURE_APP_NAME_DP
    const organization = AZURE_APP_ORGANIZATION

    const getDescriptionTemplate = ({
        incident,
        previouslyFunctioning,
        description,
        expectedFunctionality,
        typeOfRequest,
        stepByStep,
        action,
        objective,
        impactedSites,
        siteAccessed,
        userEmail,
        jobTitle,
    }: Partial<WorkItemBodyInformation>): string => {
        switch (typeOfRequest) {
            case RequestTypeEnum.INCIDENT:
                return `
                    <h3>${translate('supportFeature.email')}</h3>
                    <p>${userEmail}</p>
                    <h3>${translate('supportFeature.siteAccessed')}</h3>
                    <p>${siteAccessed}</p>
                    ${jobTitle ? `<h3>${translate('supportFeature.jobTitle')}</h3><p>${jobTitle}</p>` : ''}
                    <h3>${translate('supportFeature.natureOfIncident')}</h3>
                    <p>${incident}</p>
                    <h3>${translate('supportFeature.previouslyFunctioning')}</h3>
                    <p>${previouslyFunctioning}</p>
                    <h3>${translate('supportFeature.incidentStepByStepDescription')}</h3>
                    <p>${stepByStep}</p>
                    <h3>${translate('supportFeature.expectedFunctionality')}</h3>
                    <p>${expectedFunctionality}</p>
                    ${description ? `<h3>${translate('supportFeature.description')}</h3><p>${description}</p>` : ''}
                    <h3>${translate('supportFeature.impactedSites')}</h3>
                    ${impactedSites?.map((site) => `<p>${site}</p>`).join('')}
    `

            case RequestTypeEnum.IMPROVEMENT:
                return `
                    <h3>${translate('supportFeature.email')}</h3>
                    <p>${userEmail}</p>
                    ${jobTitle ? `<h3>${translate('supportFeature.jobTitle')}</h3><p>${jobTitle}</p>` : ''}
                    <h3>${translate('supportFeature.siteAccessed')}</h3>
                    <p>${siteAccessed}</p>
                    <h3>${translate('supportFeature.action')}</h3>
                    <p>${action}</p>
                    <h3>${translate('supportFeature.objective')}</h3>
                    <p>${objective}</p>
                    ${description ? `<h3>${translate('supportFeature.description')}</h3><p>${description}</p>` : ''}
                `
            default:
                return ''
        }
    }

    const postAttachments = async (attachments: File[]): Promise<string[]> => {
        const postAttachmentUrl = `https://dev.azure.com/${organization}/${project}/_apis/wit/attachments?api-version=7.1`

        const uploadFile = async (file: File): Promise<string> => {
            const response = await fetch(postAttachmentUrl, {
                method: 'POST',
                headers: {
                    Authorization: `Basic ${encodeBase64(`:${pat}`)}`,
                    'Content-Type': 'application/octet-stream',
                },
                body: file,
            })

            if (!response.ok) {
                const errorDetails = await response.json()
                throw new Error(`Failed to upload file: ${response.statusText}`)
            }

            const data = await response.json()
            return data.url
        }

        return Promise.all(attachments.map((file) => uploadFile(file)))
    }

    const fetchAvailableDeploymentAndScaling = async (workItemType: WorkItemTypeEnum) => {
        const fieldName = 'Custom.DeploymentandScaling'
        const url = `https://dev.azure.com/${organization}/${project}/_apis/wit/workitemtypes/${workItemType}/fields/${fieldName}?api-version=7.1&$expand=All`

        const response = await fetch(url, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                Authorization: `Basic ${encodeBase64(`:${pat}`)}`,
            },
        })
        if (!response.ok) {
            const errorDetails = await response.json()
            throw new Error(`Failed to fetch projects: ${response.statusText}`)
        }

        const data = await response.json()
        return data.allowedValues
    }

    const fetchProjects = async () => {
        const organization = 'CelaneseCorporation'
        const project = 'Digital Plant'
        const areaPathUrl = `https://dev.azure.com/${organization}/${project}/_apis/wit/classificationnodes/areas?$depth=5&api-version=7.1`
        const response = await fetch(areaPathUrl, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                Authorization: `Basic ${encodeBase64(`:${pat}`)}`,
            },
        })
        if (!response.ok) {
            const errorDetails = await response.json()
            throw new Error(`Failed to fetch projects: ${response.statusText}`)
        }

        const data = await response.json()
        return data
    }

    const handlePostWorkItem = useCallback(
        async (workItemProps: Partial<WorkItemBodyInformation>) => {
            const attachmentRelations =
                workItemProps.attachments && workItemProps.attachments.length > 0
                    ? await postAttachments(workItemProps.attachments).then((attachmentUrls) =>
                          attachmentUrls.map((url, index) => {
                              const originalFile = workItemProps.attachments?.[index]
                              return {
                                  op: 'add',
                                  path: '/relations/-',
                                  value: {
                                      rel: 'AttachedFile',
                                      url,
                                      attributes: {
                                          comment: `Attached file: ${originalFile?.name ?? 'unknown file'}`,
                                          name: originalFile?.name ?? 'Unnamed',
                                      },
                                  },
                              }
                          })
                      )
                    : []

            const body = [
                {
                    op: 'add',
                    path: '/fields/System.Title',
                    from: null,
                    value: `${appInformation.titlePrefix} ${workItemProps.title}`,
                },
                {
                    op: 'add',
                    path: '/fields/System.Description',
                    from: null,
                    value: workItemProps.description,
                },
                {
                    op: 'add',
                    path: '/fields/System.TeamProject',
                    from: null,
                    value: workItemProps.application,
                },
                {
                    op: 'add',
                    path: '/fields/System.AreaPath',
                    from: null,
                    value: aimAreaPath,
                },
                {
                    op: 'add',
                    path: '/fields/Custom.UseCase',
                    from: null,
                    value: workItemProps.useCase,
                },
                {
                    op: 'add',
                    path: '/fields/Microsoft.VSTS.Common.Priority',
                    from: null,
                    value: workItemProps.priority,
                },
                {
                    op: 'add',
                    path: '/fields/Custom.DeploymentandScaling',
                    from: null,
                    value: workItemProps.siteAccessed,
                },
                {
                    op: 'add',
                    path: '/fields/System.State',
                    from: null,
                    value: 'New',
                },
                ...attachmentRelations,
            ]

            const response = await fetch(
                `https://dev.azure.com/${organization}/${project}/_apis/wit/workitems/$${workItemProps.itemType}?api-version=7.1`,
                {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json-patch+json',
                        Authorization: `Basic ${encodeBase64(`:${pat}`)}`,
                    },
                    body: JSON.stringify(body),
                }
            )
            if (response.status === 200) {
                const data = await response.json()
                return { success: true, data }
            } else {
                const errorDetails = await response.json()
                return { success: false, error: errorDetails }
            }
        },
        [appInformation]
    )

    return { handlePostWorkItem, getDescriptionTemplate, fetchAvailableDeploymentAndScaling, fetchProjects }
}
