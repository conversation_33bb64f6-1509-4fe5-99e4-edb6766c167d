from datetime import date
from typing import Optional, Union

from cognite.client.data_classes.filters import (
    And,
    Range,
)
from industrial_model import and_, col

from clients.core.filters import gte_filter, lt_filter

MAX_MONTH = 12
QUARTER_MIN = 1
QUARTER_MAX = 4


def get_quarter(month: int) -> int:
    """
    Return the quarter corresponding to the given month.

    Args:
        month (int): The month for which to determine the quarter.

    Returns:
        int: The quarter corresponding to the given month.

    Raises:
        ValueError: If the month is not between 1 and 12.

    """
    if month < 1 or month > MAX_MONTH:
        msg = "Month must be between 1 and 12"
        raise ValueError(msg)
    return int((month - 1) / 3) + 1


def get_first_month_of_quarter(quarter: int) -> int:
    """
    Get the first month of a given quarter.

    Args:
        quarter (int): The quarter number.

    Returns:
        int: The first month of the quarter.

    Raises:
        ValueError: If the quarter is not between 1 and 4.

    """
    if quarter < QUARTER_MIN or quarter > QUARTER_MAX:
        msg = "Quarter must be between 1 and 4"
        raise ValueError(msg)
    return (quarter - 1) * 3 + 1


def map_property_date_ranges(
    items: list[tuple[Optional[date], Optional[date]]],
    action_ref: Union[tuple, str],
) -> list:
    """
    Map a list of date ranges to a list of property ranges.

    Args:
        items (list[tuple[Optional[date], Optional[date]]]): A list of date ranges, where each range is represented as a tuple of start and end dates.
        action_ref (Union[tuple, str]): The reference for the action property.

    Returns:
        list: A list of property ranges, where each range is represented as an instance of the `Range` class.

    """
    if items is None:
        return []

    result = []
    for item in items:
        ranges = []
        start_date = item[0].isoformat() if isinstance(item[0], date) else item[0]
        end_date = item[1].isoformat() if isinstance(item[1], date) else item[1]

        if start_date:
            ranges.append(Range(property=action_ref, gte=start_date))

        if end_date:
            ranges.append(Range(property=action_ref, lt=end_date))

        if ranges:
            result.append(And(*ranges))

    return result


def map_property_date_as_objects(
    items: list[tuple[Optional[date], Optional[date]]],
    key: str,
) -> list[dict[str, dict[str, str]]]:
    """
    Map a list of date tuples to a list of filter objects based on the given key.

    Args:
        items (list[tuple[Optional[date], Optional[date]]]): A list of date tuples, where each tuple contains a start date and an end date.
        key (str): The key to be used for filtering.

    Returns:
        list[dict[str, dict[str, str]]]: A list of filter objects, where each object represents a date range filter.

    """
    result = []

    for start_date, end_date in items:
        filters = []

        if start_date:
            filters.append(gte_filter(key, start_date))

        if end_date:
            filters.append(lt_filter(key, end_date))

        if filters:
            result.append({"and": filters})

    return result


def map_property_date_as_objects_for_industrial_model(
    items: list[tuple[Optional[date], Optional[date]]],
    key: str,
):
    """
    Map a list of date tuples to a list of filter objects based on the given key.

    Args:
        items (list[tuple[Optional[date], Optional[date]]]): A list of date tuples, where each tuple contains a start date and an end date.
        key (str): The key to be used for filtering.

    """
    result = []

    for start_date, end_date in items:
        filters = []

        if start_date:
            filters.append(col(key).gte_(start_date.isoformat()))

        if end_date:
            filters.append(col(key).lt_(end_date.isoformat()))

        if filters:
            result.append(and_(*filters))

    return result
