import { ActionItemLink } from '@/app/common/models/link'
import { Box, Typography, SxProps, Theme, Tooltip, styled, useTheme, useMediaQuery } from '@mui/material'
import GenericFieldTitle from '../FieldsComponent/GenericFieldTitle'
import { GridColDef, GridRowsProp } from '@mui/x-data-grid-pro'
import { useEffect, useMemo, useState } from 'react'
import { DataGridTable } from '../PaginatedTable/DataGridTable'
import { z } from 'zod'
import { SubmitHandler, useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { ModalWrapper } from '../ModalComponent/Modal/ModalWrapper'
import GenericTextField from '../FieldsComponent/GenericTextField'
import { ClnButton, MatIcon } from '@celanese/ui-lib'

import * as S from './styles'
import { translate } from '@/app/common/utils/generate-translate'

const actionButtonsStyle = {
    display: 'flex',
    justifyContent: 'space-between',
    gap: 1,
    '@media (max-width:600px)': {
        flexDirection: 'column',
        gap: '0.5rem',
        alignItems: 'center',
    },
}

type linksTableDetailProps = {
    links: ActionItemLink[]
    sxProps?: SxProps<Theme>
    title?: string
    setLinks?: (value: ActionItemLink[]) => void
    handleAlert?: (message: string, error?: boolean) => void
}

type GenerateLinkTableProps = {
    links: ActionItemLink[]
    setLinks?: (value: ActionItemLink[]) => void
    handleAlert?: (message: string, error?: boolean) => void
}

type ModalLinkFormProps = {
    link?: ActionItemLink
    links?: ActionItemLink[]
    setLinks?: (value: ActionItemLink[]) => void
    closeModal: () => void
    handleAlert?: (message: string, error?: boolean) => void
}

const createFormSchema = () =>
    z.object({
        id: z.string().min(1),
        url: z.string().min(1),
        description: z.string().min(1),
    })

type RequestSchema = z.infer<ReturnType<typeof createFormSchema>>

const Form = styled('form')({
    width: '100% !important',
    '@media (max-width:600px)': {
        padding: '1.5rem',
    },
})

const ModalLinkForm = ({ link, links, setLinks, closeModal, handleAlert }: ModalLinkFormProps) => {
    const theme = useTheme()
    const isMobile = useMediaQuery(theme.breakpoints.down('md'))

    const {
        getValues,
        handleSubmit,
        reset,
        control,
        formState: { errors },
    } = useForm<RequestSchema>({
        defaultValues: {
            id: link?.externalId ?? `local${links?.length ?? 0}`,
            url: link?.link ?? '',
            description: link?.description ?? '',
        },
        resolver: zodResolver(createFormSchema()),
        mode: 'all',
    })

    const submitFn: SubmitHandler<RequestSchema> = async (data) => {
        const existingLink = links?.find((link) => link.link === data.url && link.externalId !== data.id)
        if (existingLink) {
            handleAlert && handleAlert(translate('stepper.form.addLinkSameUrlError'), true)
            return
        }

        if (setLinks && links) {
            const existingLink = links.find((link) => link.externalId === data.id)

            let updatedLinks: ActionItemLink[]

            if (existingLink) {
                updatedLinks = links.map((link) =>
                    link.externalId === data.id ? { ...link, link: data.url, description: data.description } : link
                )
            } else {
                updatedLinks = [
                    ...links,
                    {
                        externalId: data.id,
                        space: '',
                        link: data.url,
                        description: data.description,
                    },
                ]
            }

            setLinks(updatedLinks)
        }
        closeModal()
    }

    const handleCancel = () => {
        reset({ url: '', description: '' })
        closeModal()
    }

    return (
        <ModalWrapper
            title={translate('stepper.form.addLinkModalTitle')}
            openModal
            closeModal={handleCancel}
            sxProps={{ backgroundColor: 'rgba(0, 0, 0, 0.5)', minHeight: '200px' }}
            sxPropsTitle={{
                fontSize: '24px',
                marginRight: 'auto',
                paddingLeft: '20px',
            }}
            content={
                <Box
                    sx={{
                        width: isMobile ? '90vw' : '80vw',
                        maxWidth: '827px',
                        padding: isMobile ? '10px' : '20px',
                        fontFamily: 'Roboto',
                        paddingTop: 0,
                    }}
                >
                    <Form onSubmit={handleSubmit(submitFn)} id="template-selector-form">
                        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                            <GenericTextField
                                name={'url'}
                                control={control}
                                error={Boolean(errors.url)}
                                valueController={getValues('url')}
                                label={'Url'}
                                required
                            />
                            <GenericTextField
                                name={'description'}
                                control={control}
                                error={Boolean(errors.description)}
                                valueController={getValues('description')}
                                label={translate('stepper.form.description')}
                                required
                            />
                        </Box>
                        <Box sx={{ ...actionButtonsStyle, marginTop: '1rem' }}>
                            <ClnButton variant="text" label={translate('stepper.form.cancel')} onClick={handleCancel} />
                            <ClnButton type="submit" variant="contained" label={translate('stepper.form.save')} />
                        </Box>
                    </Form>
                </Box>
            }
        />
    )
}

const GenerateLinkTable = ({ links, setLinks, handleAlert }: GenerateLinkTableProps) => {
    const [openLinkModal, setOpenLinkModal] = useState<boolean>(false)
    const [idToEdit, setIdToEdit] = useState<string | undefined>()
    const [idToDelete, setIdToDelete] = useState<string | undefined>()

    const formatExternalLink = (url: string) => {
        if (!url.startsWith('http://') && !url.startsWith('https://')) {
            return `https://${url}`
        }
        return url
    }

    const headCells: GridColDef[] = useMemo(() => {
        const columns: GridColDef[] = [
            {
                field: 'link',
                headerName: translate('details.components.link'),
                flex: 1,
                renderCell: (params) =>
                    params.row.link.length > 30 ? (
                        <Tooltip title={params.row.link} arrow>
                            <a
                                href={params.row.link}
                                target="_blank"
                                rel="noopener noreferrer"
                                style={{ color: 'info.main', cursor: 'pointer' }}
                                className="visited-link"
                            >
                                {`${params.row.link.slice(0, 30 - 1)}...`}
                            </a>
                        </Tooltip>
                    ) : (
                        <a
                            href={params.row.link}
                            target="_blank"
                            rel="noopener noreferrer"
                            style={{ color: 'info.main', cursor: 'pointer' }}
                        >
                            {params.row.link}
                        </a>
                    ),
                renderHeader: () => (
                    <span data-test="link-link_table_sort" data-origin="aim">
                        {translate('details.components.link')}
                    </span>
                ),
            },
            {
                field: 'description',
                headerName: translate('details.components.description'),
                flex: 1,
                renderCell: (params) =>
                    params.row.description.length > 30 ? (
                        <Tooltip title={params.row.description}>
                            <span>{`${params.row.description.slice(0, 30 - 1)}...`}</span>
                        </Tooltip>
                    ) : (
                        <span>{params.row.description}</span>
                    ),
                renderHeader: () => (
                    <span data-test="link-description_table_sort" data-origin="aim">
                        {translate('details.components.description')}
                    </span>
                ),
            },
        ]

        if (setLinks) {
            columns.push({
                field: 'action',
                headerName: translate('edit.components.action'),
                flex: 0.4,
                renderCell: (params) => {
                    return (
                        <Box>
                            <MatIcon
                                icon="delete"
                                onClick={() => setIdToDelete(params.row.id)}
                                sx={{
                                    cursor: 'pointer',
                                }}
                            />
                            <MatIcon
                                icon="edit"
                                onClick={() => {
                                    setIdToEdit(params.row.id)
                                    setOpenLinkModal(true)
                                }}
                                sx={{
                                    cursor: 'pointer',
                                }}
                            />
                        </Box>
                    )
                },
                renderHeader: () => (
                    <span data-test="link-action_table_sort" data-origin="aim">
                        {translate('edit.components.action')}
                    </span>
                ),
            })
        }

        return columns
    }, [translate, links])

    const tableRows: GridRowsProp = useMemo(() => {
        function convertActionItemDataToRows(items: any[]) {
            return items.map((item) => ({
                id: `${item.externalId}`,
                link: `${formatExternalLink(item.link)}`,
                description: `${item.description}`,
            }))
        }

        const linksRows = links != null && links.length > 0 ? convertActionItemDataToRows(links) : []

        return linksRows
    }, [links])

    useEffect(() => {
        if (!idToDelete || !links || !setLinks) return
        setLinks(links.filter((l) => l.externalId !== idToDelete))
        setIdToDelete(undefined)
    }, [idToDelete, links, setLinks])

    return (
        <>
            {openLinkModal && (
                <ModalLinkForm
                    link={links.find((l) => l.externalId === idToEdit)}
                    links={links}
                    setLinks={setLinks}
                    closeModal={() => {
                        setIdToEdit(undefined)
                        setOpenLinkModal(false)
                    }}
                    handleAlert={handleAlert}
                />
            )}
            <Box sx={{ display: 'flex', flexDirection: 'column', width: '100%' }}>
                <DataGridTable
                    isNoDataFilter={false}
                    initialColumnDefs={headCells}
                    rows={tableRows}
                    infinitePagination
                    removeOptionsColumnsManager
                    maxHeight={setLinks ? 200 : undefined}
                    showOptionsBar={false}
                />
                {setLinks && (
                    <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
                        <ClnButton
                            onClick={() => setOpenLinkModal(true)}
                            variant="text"
                            size="small"
                            label={translate('stepper.form.addLinkModalTitle') + '+'}
                        />
                    </Box>
                )}
            </Box>
        </>
    )
}

export default function LinksComponent({ links, sxProps, title, setLinks, handleAlert }: linksTableDetailProps) {
    return (
        <Box sx={{ gap: 1, display: 'flex', flexDirection: 'column', height: '100%' }}>
            <GenericFieldTitle fieldName={title ?? translate('details.components.link')} isSubHeader />
            {setLinks || (links && links.length > 0) ? (
                <Box sx={sxProps}>
                    <GenerateLinkTable links={links} setLinks={setLinks} handleAlert={handleAlert} />
                </Box>
            ) : (
                <S.NoData>
                    <Typography
                        sx={{
                            textAlign: 'center',
                            padding: '10px',
                            color: 'text.secondary',
                        }}
                    >
                        {translate('details.components.noLinks')}
                    </Typography>
                </S.NoData>
            )}
        </Box>
    )
}
