import { getLocalUserSite } from '@celanese/celanese-ui'
import { cognite } from '../configurations/cognite'
import { EntityType } from '../enums/EntityTypeEnum'

export function GetSpace(type: EntityType, siteCode?: string, unitCode?: string) {
    let prefix = cognite.cogniteFdmProjectCode
    let suffix = cognite.cogniteFdmSuffixInstancesSpace

    const siteFragment = siteCode ?? cognite.cogniteFdmGlobalSiteSpace
    const unitFragment = unitCode ?? cognite.cogniteFdmGlobalUnitSpace

    if (type == EntityType.Static) {
        suffix = cognite.cogniteFdmSuffixStaticSpace
    } else if (type == EntityType.Model) {
        suffix = cognite.cogniteFdmSuffixModelSpace
    } else if (type == EntityType.Protected) {
        suffix = cognite.cogniteFdmSuffixProtectedSpace
    } else if (type == EntityType.APMATEMP) {
        prefix = 'APMA'
        suffix = 'DAT-TEMP'
    } else if (type == EntityType.APW) {
        prefix = 'APW'
        suffix = 'DMD'
    } else if (type == EntityType.APWREF) {
        prefix = 'APW'
        suffix = 'REF'
    } else if (type != EntityType.Instance) {
        prefix = type
    }

    return [prefix, siteFragment, unitFragment, suffix].join('-')
}

export function getCurrentInstancesSpace() {
    return GetSpace(EntityType.Instance, getLocalUserSite()?.siteCode)
}

export { EntityType }

export function getCodeFromReportingSiteExternalId(siteId: string): string {
    return siteId.replace(/^STS-/, '')
}
