import { CSSObject } from '@emotion/react'
import { useTheme } from '@mui/material'

export const chartTitle: CSSObject = {
    fontSize: '20px',
    lineHeight: '23px',
    letterSpacing: '0em',
    textAlign: 'left',
    color: 'text.secondary',
}

export const chartContainer: CSSObject = {
    position: 'relative',
    width: '50%',
}

export const legendContainer: CSSObject = {
    display: 'flex',
    flexDirection: 'column',
    width: '50%',
    height: '200px',
    overflowY: 'auto',
}

export const chartLegendBox: CSSObject = {
    display: 'flex',
    justifyContent: 'space-between',
}

export const chartLegendColumnNumber: CSSObject = {
    margin: '0 5px',
}

export const chartLegendColumn: CSSObject = {
    margin: '0 5px',
}

export const chartLegend: CSSObject = {
    fontSize: '14px',
    lineHeight: '16px',
    letterSpacing: '0em',
    textAlign: 'left',
    color: 'text.primary',
}

export const chartLegendNumber: CSSObject = {
    fontSize: '23px',
    lineHeight: '27px',
    letterSpacing: '0em',
    textAlign: 'right',
}

export const useChartDefaultColors = () => {
    const theme = useTheme()

    return [
        theme.palette.success.main,
        theme.palette.error.main,
        theme.palette.warning.main,
        theme.palette.primary.main,
        theme.palette.primary.light,
        theme.palette.secondary.contrastText,
        theme.palette.secondary.dark,
        theme.palette.secondary.light,
        theme.palette.info.main,
        theme.palette.info.dark,
    ]
}
