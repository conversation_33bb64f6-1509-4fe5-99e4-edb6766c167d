export const environment = {
    graphqlUri: process.env.NEXT_PUBLIC_GRAPHQL_URI,
    msalClientId: process.env.NEXT_PUBLIC_MSAL_CLIENT_ID,
    msalAuthority: process.env.NEXT_PUBLIC_MSAL_AUTHORITY,
    msalScopes: process.env.NEXT_PUBLIC_MSAL_SCOPES?.split(',') ?? [],
    msalGraphScopes: process.env.NEXT_PUBLIC_MSAL_GRAPH_SCOPES?.split(',') ?? [],
    cogniteAppId: process.env.NEXT_PUBLIC_COGNITE_APP_ID,
    cogniteProject: process.env.NEXT_PUBLIC_COGNITE_PROJECT,
    cogniteBaseUrl: process.env.NEXT_PUBLIC_COGNITE_BASE_URL,
    cogniteApiVersion: process.env.NEXT_PUBLIC_COGNITE_API_VERSION,
    cogniteFdmModel: process.env.NEXT_PUBLIC_COGNITE_MODEL,
    cogniteFdmSuffixModelSpace: process.env.NEXT_PUBLIC_COGNITE_FDM_SUFFIX_MODEL_SPACE,
    cogniteFdmSuffixInstancesSpace: process.env.NEXT_PUBLIC_COGNITE_FDM_SUFFIX_INSTANCES_SPACE,
    cogniteFdmSuffixProtectedSpace: process.env.NEXT_PUBLIC_COGNITE_FDM_SUFFIX_PROTECTED_SPACE,
    cogniteFdmSuffixStaticSpace: process.env.NEXT_PUBLIC_COGNITE_FDM_SUFFIX_STATIC_SPACE,
    cogniteFdmGlobalSiteSpace: process.env.NEXT_PUBLIC_COGNITE_FDM_GLOBAL_SITE_SPACE,
    cogniteFdmGlobalUnitSpace: process.env.NEXT_PUBLIC_COGNITE_FDM_GLOBAL_UNIT_SPACE,
    cogniteFdmProjectCode: process.env.NEXT_PUBLIC_COGNITE_FDM_PROJECT_CODE,
    cogniteFusionUrl: process.env.NEXT_PUBLIC_COGNITE_FUSION_URL,
    cogniteXCdpApp: process.env.NEXT_PUBLIC_COGNITE_X_CDP_APP,
    muiLicenseKey: process.env.NEXT_PUBLIC_MUI_LICENSE_KEY,
    cogniteDefaultGraphQlListLimit: process.env.NEXT_PUBLIC_COGNITE_DEFAULT_GRAPHQL_LIST_LIMIT,
    cogniteDataSetId: process.env.NEXT_PUBLIC_COGNITE_DATA_SET_ID,
    themeSelectionEnabled: process.env.NEXT_PUBLIC_THEME_SELECTION_ENABLED == 'true',
    apiBaseURL: process.env.NEXT_PUBLIC_API_URL,
    userSearchLimit: process.env.NEXT_PUBLIC_USER_SEARCH_LIMIT,
    appsTranslationUrl: process.env.NEXT_PUBLIC_APPS_TRANSLATION_URL,
    appsTranslationAppId: process.env.NEXT_PUBLIC_APPS_TRANSLATION_APP_ID,
    userManagementUrl: process.env.NEXT_PUBLIC_USER_MANAGEMENT_URL,
    userManagementAppCode: process.env.NEXT_PUBLIC_USER_MANAGEMENT_APP_CODE,
    supportFeature: process.env.NEXT_PUBLIC_SUPPORT_FEATURE_CODE,
    notificationPortalUrl: process.env.NEXT_PUBLIC_NOTIFICATION_PORTAL_URL,
    notificationApiUrl: process.env.NEXT_PUBLIC_NOTIFICATION_API_URL,
    supportedSitesToTargetDatasets: JSON.parse(process.env.NEXT_PUBLIC_SUPPORTED_SITES_TO_TARGET_DATASETS || '{}'),
    featureEnableContextualization:
        process.env.NEXT_PUBLIC_FEATURE_ENABLE_CONTEXTUALIZATION?.toLocaleLowerCase() == 'true',
    featureEnableSDKPage:
        process.env.NEXT_PUBLIC_FEATURE_ENABLE_SDK_PAGE?.toLocaleLowerCase() == 'true',
    azureFunctionUrl: process.env.NEXT_PUBLIC_AZURE_FUNCTION_URL,
    azureFunctionSDKUrl: process.env.NEXT_PUBLIC_AIM_AZURE_FUNCTION_URL,
    mocGraphUrl: process.env.NEXT_PUBLIC_MOC_GRAPH_URL ?? '',
    icapGraphUrl: process.env.NEXT_PUBLIC_ICAP_GRAPH_URL ?? '',
    gapAssessmentGraphUrl: process.env.NEXT_PUBLIC_GAP_ASSESSMENT_GRAPH_URL ?? '',
    ofwaGraphUrl: process.env.NEXT_PUBLIC_OFWA_GRAPH_URL ?? '',
}
