
## Setup Pre-Commit Hooks
In the root folder of the repository run the script:

```bash
python setup_pre_commit.py
```

***Note***: For ESLINT to run properly it's necessary too run `npm i` in the portal folder

The pre-commit hook will validate only staged files using:
- **Ruff** for python files (linting)
- **Black** for python files (formatting)
- **Pyright** for python files (static type checking)
- **ESLINT** for typescript and tsx files (linting)
- **Prettier** for typescript and tsx files (formatting)

After the hook is executed if any files were auto-fixed they'll not be added automatically to git's staging

It's recommended to install the Extensions for these linters on you editor:
- Sonar Qube (Formerly Sonar Lint)
- Ruff (From Astral Software)
- Pyright (Pylance on VSCODE) (Offical MS extension)
- ESLINT (Official MS extension)
- Prettier
