import { ExternalEntity } from '..'
import { Dayjs } from 'dayjs'

export interface Location extends ExternalEntity {
    aliases: string[]
    name: string
    changedBy: string
    changedDate: Dayjs
    companyCode: string
    companyCodeDescription: string
    costCenter: string
    costCenterDescription: string
    createdBy: string
    createdDate: Dayjs
    description: string
    functionalLocation: string
    parent: Location
    planningPlant: string
    planningPlantDescription: string
    maintenancePlant: string
    maintenancePlantDescription: string
    plannerGroup: string
    plannerGroupDescription: string
    plantSection: string
    plantSectionDescription: string
    functionalLocationStatus: string
}

export interface FunctionalLocationRequest {
    search?: string
    reportingSiteExternalIds: string[]
    reportingUnitExternalIds?: string[]
}
