# Manifest file downloaded from fusion
externalId: tr-AIM-COR-ALL-RAW-ICAP-STG-EVENT
name: AIM-COR-ALL-RAW-ICAP-STG-EVENT
query: >-
  WITH cte AS (
    SELECT
    	icap_event.key,
    	aim_existing_event.externalId as existing_external_id,
    	if(isnull(aim_existing_event.externalId), ROW_NUMBER() OVER(ORDER BY icap_event.`EventID` ASC), 0) as tmp_idx,
      if(
      	icap_event.Private,
      	'AIM-COR-ALL-PROT',
      	utl_reporting_site.aim_space
      ) AS space,
    	utl_reporting_site.ah_reporting_site AS reportingSite,
      node_reference('UMG-COR-ALL-DAT', 'APP-ICAP') AS application,
      node_reference('AIM-COR-ALL-REF', 'SEVTY-ICAP-Event') AS eventType,
      cast(icap_event.`EventID` AS STRING) AS iCAPEventID,
      coalesce(icap_action_count.value, 0) AS actionsCount,
      cast(icap_event.`StartDate` AS DATE) AS assignmentDate,
      cast(icap_event.`EventDescription` AS STRING) AS description,
      utl_user_owner.user_azure_attribute AS createdBy,
      utl_user_owner.user_azure_attribute AS owner,
      cast(icap_event.`DueDate` AS DATE) AS dueDate,
      cast(icap_event.`DueDate` AS DATE) AS displayDueDate,
      category_map.aim_category AS category,
      category_map.aim_subcategory AS subCategory,
    	if(
    		isnotnull(category_map.aim_site_category_external_id_pattern), 
    		node_reference(
    			utl_reporting_site.aim_space, 
    			replace(
    				category_map.aim_site_category_external_id_pattern, 
    				'<SITE>', 
    				utl_reporting_site.ah_site_code
    			)
    		), 
    		NULL
      ) AS siteSpecificCategory,
      icap_event.`EventTitle` AS title,
    	utl_reporting_unit.ah_reporting_location AS reportingLocation,
  	utl_reporting_unit.ah_reporting_unit AS reportingUnit,
    	utl_reporting_unit.ah_reporting_line AS reportingLine,
    	utl_business_line.ah_business_line AS businessLine,
      if(
        NOT icap_event.EventActive, 
        named_struct("externalId", 'SEVS-Deleted', "space", 'AIM-COR-ALL-REF'),
        utl_status.status
      ) AS status,
    	icap_event.Private AS isPrivate,
      if(
      	icap_event.Private,
      	array_except(
      		concat(
      			ifnull(grouped_event_viewer.viewer_external_ids, array()),
      			ifnull(grouped_event_secondary_owner.secondary_owner_external_ids, array()),
      			array(utl_user_owner.user_azure_attribute.externalId)
        	),
      		array(NULL)
    	  ),
    	  NULL
      ) AS views,
    	ROW_NUMBER() OVER (PARTITION BY icap_event.key ORDER BY aim_existing_event.`node.createdTime` DESC) AS row_num
    FROM `ICAP-COR`.`EVNT-tblEvent` AS icap_event
    INNER JOIN `ICAP-COR`.`EVNT-tblEventCategory` AS icap_event_category ON icap_event.`EventCategoryID` = icap_event_category.`EventCategoryID`
    LEFT JOIN `AIM-COR`.`ICAP-MAP-ReportingSite` AS utl_reporting_site ON utl_reporting_site.key = cast(icap_event.`SiteID` AS STRING)
    LEFT JOIN `AIM-COR`.`ICAP-MAP-EventStatus` AS utl_status ON utl_status.key = cast(icap_event.`EventStatusID` AS STRING)
    LEFT JOIN `AIM-COR`.`ICAP-MAP-Category` category_map ON category_map.icap_event_category_id = icap_event.`EventCategoryID`
    LEFT JOIN `AIM-COR`.`ICAP-MAP-User` AS utl_user_owner ON utl_user_owner.key = cast(icap_event.`EventAddedByOwner` AS STRING)
    LEFT JOIN `ICAP-COR`.`UNT-tblUnit` AS icap_unit ON icap_unit.key = cast(icap_event.`UnitID` AS STRING) AND icap_event.`SiteID` = icap_unit.`SiteID`
    LEFT JOIN `AIM-COR`.`ICAP-MAP-ReportingUnit&Location` AS utl_reporting_unit ON utl_reporting_unit.key = icap_unit.key
    LEFT JOIN `AIM-COR`.`ICAP-MAP-BusinessLine` AS utl_business_line ON utl_business_line.key = cast(icap_event.BusinessLineID AS STRING)
    LEFT JOIN cdf_data_models("AIM-COR-ALL-DMD", "ActionItemManagementDOM", "6_0_0", "SourceEvent") AS aim_existing_event ON aim_existing_event.iCAPEventID = cast(icap_event.`EventID` AS STRING)
    LEFT JOIN (
    	SELECT
    		icap_action.EventID AS EventID,
      	cast(count(*) AS INTEGER) AS value
        FROM `ICAP-COR`.`AXN-tblActionItem` AS icap_action
        GROUP BY icap_action.EventID
    ) AS icap_action_count ON icap_event.EventID = icap_action_count.EventID
    LEFT JOIN (
      SELECT 
      	icap_event_viewer.EventID, 
      	array_agg(utl_user_viewer.user_azure_attribute.externalId) AS viewer_external_ids
      FROM `ICAP-COR`.`EVNT-tblEventViewer` icap_event_viewer
      INNER JOIN `AIM-COR`.`ICAP-MAP-User` utl_user_viewer ON utl_user_viewer.key = cast(icap_event_viewer.ViewerID AS STRING)
      GROUP BY icap_event_viewer.EventID
    ) AS grouped_event_viewer ON grouped_event_viewer.EventID = icap_event.EventID
    LEFT JOIN (
      SELECT
      	icap_secondary_owner.EventID,
      	array_agg(utl_user_secondary_owner.user_azure_attribute.externalId) AS secondary_owner_external_ids
      FROM `ICAP-COR`.`EVNT-tblEventSecondaryOwners` icap_secondary_owner
      INNER JOIN `AIM-COR`.`ICAP-MAP-User` utl_user_secondary_owner ON utl_user_secondary_owner.key = cast(icap_secondary_owner.OwnerID AS STRING)
      GROUP BY icap_secondary_owner.EventID
    ) AS grouped_event_secondary_owner ON grouped_event_secondary_owner.EventID = icap_event.EventID
    WHERE NOT icap_event.EventCategoryID IN (5, 9, 15, 21, 27)
  )
   
  SELECT 
    coalesce(
        existing_external_id,
          concat(
          'SEVT-',
          split(split(current_timestamp(),' ')[0],'-')[0],
          split(split(current_timestamp(),' ')[0],'-')[1],
          split(split(current_timestamp(),' ')[0],'-')[2],
          split(split(current_timestamp(),' ')[1],':')[0],
          split(split(current_timestamp(),' ')[1],':')[1],
          '-',
          lpad(
            tmp_idx,
              array_max(array(4, length(cast(tmp_idx AS STRING)))),
            '0'
            )
        )
    ) as externalId,
    *
  FROM cte

  WHERE row_num = 1
destination:
  database: AIM-COR
  table: ICAP-STG-Event
  type: raw
ignoreNullFields: true
shared: true
action: upsert
dataSetExternalId: AIM-COR-ALL-DAT
# Specify credentials separately like this:
# authentication:
#   read:
#     clientId: ${READ_CLIENT_ID}
#     clientSecret: ${READ_CLIENT_SECRET}
#     tokenUrl: ${READ_TOKEN_URL}
#     cdfProjectName: ${READ_CDF_PROJECT_NAME}
#     # Optional: If idP requires providing the scopes
#     scopes:
#       - ${READ_SCOPES}
#     # Optional: If idP requires providing the audience
#     audience: ${READ_CDF_AUDIENCE}
#   write:
#     clientId: ${WRITE_CLIENT_ID}
#     clientSecret: ${WRITE_CLIENT_SECRET}
#     tokenUrl: ${WRITE_TOKEN_URL}
#     cdfProjectName: ${WRITE_CDF_PROJECT_NAME}
#     # Optional: If idP requires providing the scopes
#     scopes:
#       - ${WRITE_SCOPES}
#     # Optional: If idP requires providing the audience
#     audience: ${WRITE_CDF_AUDIENCE}
# Or together like this:
# authentication:
#   clientId: ${CLIENT_ID}
#   clientSecret: ${CLIENT_SECRET}
#   tokenUrl: ${TOKEN_URL}
#   # Optional: If idP requires providing the scopes
#   cdfProjectName: ${CDF_PROJECT_NAME}
#   scopes:
#    - ${SCOPES}
#   # Optional: If idP requires providing the audience
#   audience: ${AUDIENCE}