import * as React from 'react'
import { type SxProps, type Theme, useTheme, useMediaQuery, Box } from '@mui/material'
import * as S from './styles'
import { ClnButton, ClnButtonProps } from '@celanese/ui-lib'

type CustomTabPanelProps = {
    children?: React.ReactNode
    value?: number
    index?: number
}

type AimTabsProps = {
    children?: React.ReactNode
    value?: number
    index?: number
    sxProps?: SxProps<Theme>
    tabs?: any
    buttons?: ClnButtonProps[]
    onChange?: (e: any, value: number) => void
    filterComponent?: JSX.Element
}

function CustomTabPanel(props: CustomTabPanelProps) {
    const { children, value, index } = props
    return value === index ? <>{children}</> : null
}

function AimTabs({ children, buttons, value, tabs, onChange, sxProps, filterComponent }: AimTabsProps) {
    const theme = useTheme()
    const isMobile = useMediaQuery(theme.breakpoints.down('md'))

    return (
        <>
            <S.BorderTab sx={{ ...sxProps }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', width: '100%' }}>
                    <S.Tabs
                        value={value ?? 0}
                        onChange={onChange}
                        variant={isMobile ? 'fullWidth' : 'standard'}
                        sx={{
                            width: isMobile ? '100%' : 'auto',
                        }}
                    >
                        {tabs.map((tab: any, index: number, dataTest: string) => (
                            <S.Tab
                                className="tab-single"
                                label={tab.label}
                                icon={tab.icon}
                                iconPosition="start"
                                key={index}
                                value={index}
                                disabled={tab.disabled}
                                data-test={dataTest}
                            />
                        ))}
                    </S.Tabs>
                    {!isMobile && (
                        <S.ButtonsBox>
                            {filterComponent}
                            {buttons &&
                                buttons.map((button, index) => (
                                    <ClnButton
                                        key={index}
                                        variant={button.variant}
                                        label={button.label}
                                        startIcon={button.startIcon}
                                        onClick={button.onClick}
                                        disabled={button.disabled}
                                        size="small"
                                    />
                                ))}
                        </S.ButtonsBox>
                    )}
                </Box>
            </S.BorderTab>
            {isMobile && (
                <S.ButtonsBoxMobile>
                    {filterComponent}
                    {buttons &&
                        buttons.map((button, index) => (
                            <ClnButton
                                key={index}
                                variant={button.variant}
                                label={button.label}
                                startIcon={button.startIcon}
                                onClick={button.onClick}
                                disabled={button.disabled}
                                sx={{
                                    width: '100%',
                                }}
                                size="small"
                            />
                        ))}
                </S.ButtonsBoxMobile>
            )}
            {tabs.map((tab: any, index: number) => (
                <CustomTabPanel value={value} index={index} key={index}>
                    {tab.content}
                </CustomTabPanel>
            ))}
        </>
    )
}

export { AimTabs }
