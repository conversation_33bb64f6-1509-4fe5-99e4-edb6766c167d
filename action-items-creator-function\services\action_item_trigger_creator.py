from services.action_item_event_processor import ActionItemTrigger

from models.settings import Settings
from services.cognite_service import CogniteService
from services.graphql_service import GraphqlService
from services.logging_service import LoggingService


class ActionItemTriggerRemover:
    def __init__(
        self,
        cognite_service: CogniteService,
        graphql_service: GraphqlService,
        settings: Settings,
        logging_service: LoggingService,
    ):
        self._cognite_service = cognite_service
        self._graphql_service = graphql_service
        self._log = logging_service
        self._settings = settings

    async def delete_action_item_trigger(self, item: ActionItemTrigger) -> bool:
        nodes = (item["space"], item["externalId"])
        response = self._cognite_service._cognite_client.data_modeling.instances.delete(
            nodes=nodes
        )
        return len(response.nodes) > 0
