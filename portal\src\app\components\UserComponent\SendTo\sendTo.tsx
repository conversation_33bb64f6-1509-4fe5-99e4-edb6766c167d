import { NotificationSubscribers } from '@/app/common/models/notification-subscribers'
import { ClnButton, ClnPanel, ClnTextField, MatIcon } from '@celanese/ui-lib'
import {
    Autocomplete,
    Box,
    Grid,
    List,
    ListItem,
    ListItemText,
    Popper,
    TextField,
    Typography,
    useMediaQuery,
    useTheme,
} from '@mui/material'
import SelectedChip from './selectedChip'
import * as styles from './sendTo.styles'
import { useSendToDrawerLogic } from './sendToLogic'
import { Controller } from 'react-hook-form'
import { UserComplement } from '@/app/common/models/common/user-management/user-complement'
import { LoaderCircular } from '../../Loader'
import { translate } from '@/app/common/utils/generate-translate'
import { Dispatch, SetStateAction, useState } from 'react'
import { CustomDrawer } from '../../ModalComponent/Drawer/Drawer'
import { drawerStyles } from '@/app/components/ModalComponent/Drawer/styles'
import { PREFIX_USER_AZURE_ATTRIBUTE } from '@/app/common/utils'

interface SendToDrawerProps {
    siteId: string
    selectedSubscribers: NotificationSubscribers
    setSelectedSubscribers: Dispatch<SetStateAction<NotificationSubscribers>>
    stepWithError: boolean
}

export default function SendToComponent({
    siteId,
    selectedSubscribers,
    setSelectedSubscribers,
    stepWithError,
}: SendToDrawerProps) {
    const theme = useTheme()
    const isMobile = useMediaQuery(theme.breakpoints.down('sm'))

    const ContentComponent = isMobile ? ClnPanel : Box
    const [addAssigneeMobile, setAddAssigneeMobile] = useState<boolean>(false)

    const {
        handleChange,
        searchValue,
        handleResetStates,
        setSearchValue,
        roles,
        setSelectedRoles,
        selectedRoles,
        teams,
        setSelectedTeams,
        selectedTeams,
        users,
        setSelectedUsers,
        selectedUsers,
        locationsByUnit,
        handleRoleAction,
        handleTeamAction,
        handleRemoveUser,
        submitFn,
        openDrawer,
        handleOpenDrawer,
        control,
        handleSubmit,
        notificationTeamsData,
        notificationRolesData,
        notificationUnitsData,
        usersAdvancedSearch,
        handleRemoveUserSearch,
        clearAllAdvancedSearch,
        handleFocus,
        handleBlur,
        setAnchorEl,
        anchorEl,
        openPopper,
        isLoader,
        setSelectedTeam,
        setSelectedTeamsInAdvancedSearch,
        setSelectedRolesInAdvancedSearch,
        setSelectedUnitsInAdvancedSearch,
        setSelectedLocationsInAdvancedSearch,
    } = useSendToDrawerLogic(siteId, selectedSubscribers, setSelectedSubscribers)

    const renderSelectedAssignees = () => {
        return (
            <Grid
                item
                xs={12}
                id={'selected-select-assignees'}
                sx={{ ...styles.itemsForm, marginTop: isMobile ? 0 : '1rem', padding: isMobile ? '0 1rem' : 0 }}
            >
                <Box sx={styles.selectedUsersContainer}>
                    {selectedRoles?.map((role) => (
                        <SelectedChip
                            key={role.externalId}
                            label={role.name}
                            isGroup
                            onExpandClick={() => {
                                handleRoleAction(role.externalId, true, role.users)

                                if (role.users) {
                                    setSelectedSubscribers((prev: NotificationSubscribers) => ({
                                        ...prev,
                                        users: [...selectedUsers, ...(role.users ?? [])],
                                        roles: selectedRoles.filter((r) => r.externalId !== role.externalId),
                                    }))
                                }
                            }}
                            onRemoveClick={() => {
                                handleRoleAction(role.externalId, false)
                                setSelectedSubscribers((prev: NotificationSubscribers) => ({
                                    ...prev,
                                    roles: selectedRoles.filter((r) => r.externalId != role.externalId),
                                }))
                            }}
                        />
                    ))}
                    {selectedTeams?.map((team) => (
                        <SelectedChip
                            key={team.externalId}
                            label={team.name}
                            isGroup
                            onExpandClick={() => {
                                handleTeamAction(team.externalId, true, team.users)
                                if (team.users) {
                                    setSelectedSubscribers((prev: NotificationSubscribers) => ({
                                        ...prev,
                                        users: [...selectedUsers, ...(team.users ?? [])],
                                        teams: selectedTeams.filter((r) => r.externalId != team.externalId),
                                    }))
                                }
                            }}
                            onRemoveClick={() => {
                                handleTeamAction(team.externalId, false)
                                setSelectedSubscribers((prev: NotificationSubscribers) => ({
                                    ...prev,
                                    teams: selectedTeams.filter((r) => r.externalId != team.externalId),
                                }))
                            }}
                        />
                    ))}
                    {selectedUsers?.map((user: { externalId: string; email: string }) => (
                        <SelectedChip
                            key={user.externalId}
                            label={user.email}
                            onRemoveClick={() => {
                                handleRemoveUser(user.externalId)
                                setSelectedSubscribers((prev: NotificationSubscribers) => ({
                                    ...prev,
                                    users: selectedUsers.filter((u) => u.externalId != user.externalId),
                                }))
                            }}
                        />
                    ))}
                </Box>
            </Grid>
        )
    }

    return (
        <ContentComponent
            id="select-assignees-panel"
            sx={isMobile ? { display: 'flex', flexDirection: 'column', marginBottom: '1rem' } : undefined}
        >
            <Grid id={'select-assignees'} container>
                <Grid
                    item
                    xs={12}
                    id={'title-select-assignees'}
                    sx={{ ...styles.itemsForm, padding: isMobile ? '1rem 1rem 0' : 0 }}
                >
                    <Typography sx={styles.subHeader}>{translate('stepper.form.assignment.selectUsers')}</Typography>
                </Grid>
                <Grid
                    item
                    xs={12}
                    id={'description-select-assignees'}
                    sx={{ ...styles.itemsForm, padding: isMobile ? '0 1rem' : 0 }}
                >
                    <Typography>{translate('stepper.form.assignment.selectUsersDesc')}</Typography>
                </Grid>
                {isMobile && renderSelectedAssignees()}
                <Grid
                    item
                    container
                    spacing={isMobile ? 0 : 1}
                    md={12}
                    id={'options-select-assignees'}
                    sx={styles.itemsOptions}
                >
                    <Grid item xs={12} sm={6} id={'field-options-select-assignees'}>
                        {isMobile && !addAssigneeMobile && (
                            <ClnButton
                                size="large"
                                label={translate('stepper.form.assignment.usersSearch.addAssigness')}
                                variant="text"
                                onClick={() => {
                                    setAddAssigneeMobile(true)
                                }}
                                startIcon={
                                    <MatIcon
                                        icon="add"
                                        sx={{
                                            borderRadius: '50%',
                                            border: '2px solid',
                                            borderColor: 'primary.main',
                                            padding: '1px',
                                        }}
                                    />
                                }
                                fullWidth={isMobile}
                                data-test="new_action_item_flow_3-advanced_search_button"
                                data-origin="ui-lib"
                            />
                        )}
                        {(isMobile === false || (isMobile === true && addAssigneeMobile === true)) && (
                            <ClnTextField
                                error={
                                    stepWithError &&
                                    !selectedRoles.length &&
                                    !selectedTeams.length &&
                                    !selectedUsers.length
                                }
                                required
                                size="small"
                                helperText=""
                                label={translate('stepper.form.assignment.usersSearch.name')}
                                variant="outlined"
                                onFocus={handleFocus}
                                onBlur={handleBlur}
                                onChange={handleChange}
                                value={searchValue}
                                fullWidth
                                inputRef={(node) => {
                                    setAnchorEl(node)
                                }}
                                sx={isMobile ? { paddingRight: '2rem', marginLeft: '1rem' } : {}}
                                data-test="new_action_item_flow_3-user_search_field"
                                data-origin="ui-lib"
                            />
                        )}
                        <Popper
                            open={openPopper}
                            sx={styles.suggestionsContainter}
                            anchorEl={anchorEl}
                            placement="bottom-start"
                        >
                            {isLoader ? (
                                LoaderCircular()
                            ) : (
                                <List>
                                    {roles.length === 0 && teams.length === 0 && users.length === 0 ? (
                                        <ListItem>
                                            <ListItemText primary={translate('stepper.form.typeAtLeast1')} />
                                        </ListItem>
                                    ) : (
                                        <>
                                            {roles.map((role, index) => (
                                                <ListItem button key={`${role.externalId}${index}`}>
                                                    <ListItemText
                                                        primary={role.name}
                                                        onClick={() => {
                                                            setSelectedRoles([...selectedRoles, role])
                                                            setSelectedSubscribers((prev: NotificationSubscribers) => ({
                                                                ...prev,
                                                                roles: [...selectedRoles, role],
                                                            }))
                                                            handleResetStates()
                                                            setSearchValue('')
                                                        }}
                                                    />
                                                </ListItem>
                                            ))}
                                            {teams.map((team, index) => (
                                                <ListItem button key={`${team.externalId}${index}`}>
                                                    <ListItemText
                                                        primary={team.name}
                                                        onClick={() => {
                                                            setSelectedTeam(team)
                                                            handleResetStates()
                                                            setSearchValue('')
                                                        }}
                                                    />
                                                </ListItem>
                                            ))}
                                            {users.map((user, index) => (
                                                <ListItem button key={`${user.externalId}${index}`}>
                                                    <ListItemText
                                                        primary={user.label}
                                                        onClick={() => {
                                                            const normalizeExternalId = (id: string) =>
                                                                id.startsWith(PREFIX_USER_AZURE_ATTRIBUTE)
                                                                    ? id.slice(PREFIX_USER_AZURE_ATTRIBUTE.length)
                                                                    : id

                                                            const normalizedUserId = normalizeExternalId(
                                                                user.externalId
                                                            )

                                                            if (
                                                                !selectedUsers.some(
                                                                    (selectedUser) =>
                                                                        normalizeExternalId(selectedUser.externalId) ===
                                                                        normalizedUserId
                                                                )
                                                            ) {
                                                                setSelectedUsers([...selectedUsers, user])
                                                                setSelectedSubscribers(
                                                                    (prev: NotificationSubscribers) => ({
                                                                        ...prev,
                                                                        users: [...selectedUsers, user],
                                                                    })
                                                                )
                                                            }
                                                            handleResetStates()
                                                            setSearchValue('')
                                                        }}
                                                    />
                                                </ListItem>
                                            ))}
                                        </>
                                    )}
                                </List>
                            )}
                        </Popper>
                        {isMobile && <hr />}
                    </Grid>
                    <Grid
                        item
                        xs={12}
                        sm={6}
                        container
                        spacing={1}
                        id={'buttons-options-select-assignees'}
                        sx={{ ...styles.itemsOptions, padding: isMobile ? '0 1rem' : 0 }}
                    >
                        <Grid
                            item
                            xs={12}
                            sm={6}
                            id={'advanced-search-button-select-assignees'}
                            sx={styles.itemsOptions}
                        >
                            <ClnButton
                                size="medium"
                                label={translate('stepper.form.assignment.usersSearch.advancedSearch')}
                                variant="outlined"
                                onClick={() => {
                                    handleOpenDrawer(true)
                                }}
                                sx={{ ...styles.buttons, width: { xs: '100%', sm: 'auto' } }}
                                fullWidth={isMobile}
                                data-test="new_action_item_flow_3-advanced_search_button"
                                data-origin="ui-lib"
                            />
                        </Grid>
                        <Grid item xs={12} sm={6} id={'clear-all-button-select-assignees'} sx={styles.itemsOptions}>
                            <ClnButton
                                label={translate('stepper.form.assignment.clearAll')}
                                variant={isMobile ? 'text' : 'outlined'}
                                size="medium"
                                onClick={() => {
                                    setSelectedRoles([])
                                    setSelectedTeams([])
                                    setSelectedUsers([])
                                    setSelectedSubscribers(new NotificationSubscribers())
                                }}
                                color={isMobile ? 'error' : 'primary'}
                                sx={{ ...styles.buttons, width: { xs: '100%', sm: 'auto' } }}
                                fullWidth={isMobile}
                                data-test="new_action_item_flow_3-clear_all_button"
                                data-origin="ui-lib"
                            />
                        </Grid>
                    </Grid>
                </Grid>
                {!isMobile && renderSelectedAssignees()}
            </Grid>
            <CustomDrawer
                title={translate('stepper.form.assignment.drawerOverLineMeta')}
                overlineMeta={translate('stepper.form.assignment.usersSearch.advancedSearch')}
                header={translate('stepper.form.assignment.usersSearch.advancedSearch')}
                openDrawer={openDrawer}
                closeDrawer={() => handleOpenDrawer(false)}
                content={
                    <Box sx={drawerStyles.container}>
                        <form style={drawerStyles.formContainer}>
                            <Box sx={styles.autoCompleteContainer}>
                                <Box sx={styles.typographyContainer}>
                                    <Typography>{translate('stepper.form.assignment.usersSearch.teams')}</Typography>
                                </Box>
                                <Controller
                                    name="team"
                                    control={control}
                                    render={({ field: { value, onChange } }) => (
                                        <Autocomplete
                                            multiple
                                            id="team"
                                            limitTags={5}
                                            value={
                                                notificationTeamsData.filter((team) =>
                                                    value.includes(team.externalId)
                                                ) || []
                                            }
                                            onChange={(e, values) => {
                                                setSelectedTeamsInAdvancedSearch(values)
                                                onChange(values.map((team) => team.externalId))
                                            }}
                                            options={notificationTeamsData}
                                            getOptionLabel={(option) => option.name || ''}
                                            renderInput={(params) => (
                                                <TextField
                                                    {...params}
                                                    size="small"
                                                    sx={{
                                                        flexGrow: '1',
                                                        color: 'primary',
                                                        width: '100%',
                                                    }}
                                                    label={translate('stepper.form.assignment.usersSearch.teams')}
                                                />
                                            )}
                                            renderOption={(props, option) => (
                                                <li {...props} key={option.externalId} data-value={option.externalId}>
                                                    <span>{option.name}</span>
                                                </li>
                                            )}
                                            data-test="new_action_item_flow_3_advanced_search-teams_field"
                                            data-origin="aim"
                                        />
                                    )}
                                />
                            </Box>
                            <Box sx={styles.autoCompleteContainer}>
                                <Box sx={styles.typographyContainer}>
                                    <Typography>{translate('stepper.form.assignment.usersSearch.roles')}</Typography>
                                </Box>
                                <Controller
                                    name="role"
                                    control={control}
                                    render={({ field: { value, onChange } }) => (
                                        <Autocomplete
                                            multiple
                                            id="role"
                                            limitTags={5}
                                            value={
                                                notificationRolesData.filter((role) =>
                                                    value.includes(role.externalId)
                                                ) || []
                                            }
                                            onChange={(e, values) => {
                                                setSelectedRolesInAdvancedSearch(values)
                                                onChange(values.map((role) => role.externalId))
                                            }}
                                            options={notificationRolesData}
                                            getOptionLabel={(option) => option.name || ''}
                                            renderInput={(params) => (
                                                <TextField
                                                    {...params}
                                                    size="small"
                                                    sx={{
                                                        flexGrow: '1',
                                                        color: 'primary',
                                                        width: '100%',
                                                    }}
                                                    label={translate('stepper.form.assignment.usersSearch.roles')}
                                                />
                                            )}
                                            renderOption={(props, option) => (
                                                <li {...props} key={option.externalId} data-value={option.externalId}>
                                                    <span>{option.name}</span>
                                                </li>
                                            )}
                                            data-test="new_action_item_flow_3_advanced_search-roles_field"
                                            data-origin="aim"
                                        />
                                    )}
                                />
                            </Box>
                            <Box sx={styles.autoCompleteContainer}>
                                <Box sx={styles.typographyContainer}>
                                    <Typography>{translate('stepper.form.assignment.usersSearch.units')}</Typography>
                                </Box>
                                <Controller
                                    name="unit"
                                    control={control}
                                    render={({ field: { value, onChange } }) => (
                                        <Autocomplete
                                            multiple
                                            id="unit"
                                            limitTags={5}
                                            value={
                                                notificationUnitsData?.filter((unit) =>
                                                    value.includes(unit.externalId)
                                                ) || []
                                            }
                                            onChange={(e, values) => {
                                                setSelectedUnitsInAdvancedSearch(values)
                                                onChange(values.map((unit) => unit.externalId))
                                            }}
                                            options={notificationUnitsData ? notificationUnitsData : []}
                                            getOptionLabel={(option) => option.name.slice(3) || ''}
                                            renderInput={(params) => (
                                                <TextField
                                                    {...params}
                                                    size="small"
                                                    sx={{
                                                        flexGrow: '1',
                                                        color: 'primary',
                                                        width: '100%',
                                                    }}
                                                    label={translate('stepper.form.assignment.usersSearch.units')}
                                                />
                                            )}
                                            renderOption={(props, option) => (
                                                <li {...props} key={option.externalId} data-value={option.externalId}>
                                                    <span>{option.name.slice(3) + ' - ' + option.description}</span>
                                                </li>
                                            )}
                                            data-test="new_action_item_flow_3_advanced_search-units_field"
                                            data-origin="aim"
                                        />
                                    )}
                                />
                            </Box>
                            <Box sx={styles.autoCompleteContainer}>
                                <Box sx={styles.typographyContainer}>
                                    <Typography>
                                        {translate('stepper.form.assignment.usersSearch.reportingLocation')}
                                    </Typography>
                                </Box>
                                <Controller
                                    name="reportingLocation"
                                    control={control}
                                    render={({ field: { value, onChange } }) => (
                                        <Autocomplete
                                            multiple
                                            id="reportingLocation"
                                            limitTags={5}
                                            value={
                                                locationsByUnit?.filter((location) =>
                                                    value.includes(location.externalId)
                                                ) || []
                                            }
                                            onChange={(e, values) => {
                                                setSelectedLocationsInAdvancedSearch(values)
                                                onChange(values.map((location) => location.externalId))
                                            }}
                                            options={locationsByUnit.length > 0 ? locationsByUnit : []}
                                            getOptionLabel={(option) => option.description || ''}
                                            renderInput={(params) => (
                                                <TextField
                                                    {...params}
                                                    size="small"
                                                    sx={{
                                                        flexGrow: '1',
                                                        color: 'primary',
                                                        width: '100%',
                                                    }}
                                                    label={translate(
                                                        'stepper.form.assignment.usersSearch.reportingLocation'
                                                    )}
                                                />
                                            )}
                                            renderOption={(props, option) => (
                                                <li {...props} key={option.externalId} data-value={option.externalId}>
                                                    <span>{option.description}</span>
                                                </li>
                                            )}
                                            data-test="new_action_item_flow_3_advanced_search-reporting_location_field"
                                            data-origin="aim"
                                        />
                                    )}
                                />
                            </Box>
                            <Box sx={styles.autoCompleteContainer}>
                                <Box sx={styles.typographyContainer}>
                                    <Typography>
                                        {translate('stepper.form.assignment.usersSearch.selectedUsers')}
                                    </Typography>
                                </Box>
                                <Box
                                    sx={
                                        usersAdvancedSearch.length
                                            ? styles.selectedUsersAdvancedContainer
                                            : { textAlign: 'center', marginTop: '1rem' }
                                    }
                                >
                                    {usersAdvancedSearch.length ? (
                                        <Box sx={styles.selectedUsersAdvancedSetings}>
                                            {usersAdvancedSearch.map((user: UserComplement) => (
                                                <SelectedChip
                                                    key={user.externalId}
                                                    label={`${user.name} (${user.email})`}
                                                    onRemoveClick={() => {
                                                        handleRemoveUserSearch(user.externalId)
                                                        setSelectedSubscribers((prev: NotificationSubscribers) => ({
                                                            ...prev,
                                                            users: selectedUsers.filter(
                                                                (u) => u.externalId !== user.externalId
                                                            ),
                                                        }))
                                                    }}
                                                />
                                            ))}
                                        </Box>
                                    ) : (
                                        <Typography>
                                            {translate('stepper.form.assignment.usersSearch.noUsers')}
                                        </Typography>
                                    )}
                                </Box>
                            </Box>
                        </form>
                        <Box id={'form-buttons'} sx={drawerStyles.buttonsContainer}>
                            <ClnButton
                                label={translate('stepper.form.assignment.clearAll')}
                                variant="text"
                                size="medium"
                                onClick={() => clearAllAdvancedSearch()}
                                data-test="new_action_item_flow_3_advanced_search-clear_all_button"
                                data-origin="ui-lib"
                                sx={{ flex: 1 }}
                            />
                            <ClnButton
                                size="medium"
                                variant="contained"
                                label={translate('stepper.form.assignment.usersSearch.addUsers')}
                                onClick={handleSubmit(submitFn)}
                                data-test="new_action_item_flow_3_advanced_search-add_users_button"
                                data-origin="ui-lib"
                                sx={{ flex: 1 }}
                            />
                        </Box>
                    </Box>
                }
            />
        </ContentComponent>
    )
}
