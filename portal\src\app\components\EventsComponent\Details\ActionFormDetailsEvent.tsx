'use client'
import { Box } from '@mui/material'
import { SourceEvent } from '@/app/common/models/source-event'
import { ActionItemKindExternalIdEnum } from '@/app/common/enums/ActionItemKindEnum'
import { ActionItemForms } from '../../ActionItemForm'
import { UserRolesPermission } from '@celanese/celanese-sdk'

type ActionFormDetailsEventProps = {
    id: string
    sourceEvent?: SourceEvent
    activeUser: UserRolesPermission
    sourceInformation?: string
    isRecurring?: boolean
    closeForm: (value: boolean) => void
    setRefatchActions: () => void
}
export default function ActionFormDetailsEvent({
    id,
    sourceEvent,
    activeUser,
    sourceInformation,
    isRecurring = false,
    closeForm,
    setRefatchActions,
}: ActionFormDetailsEventProps) {
    const cancelNewAction = () => {
        closeForm(false)
    }

    return (
        <Box>
            <ActionItemForms
                activeUser={activeUser}
                eventId={id}
                defaultValues={{
                    sourceInformation: sourceInformation ?? sourceEvent?.sourceInformation ?? id,
                    category: sourceEvent?.category?.externalId ?? '',
                    subCategory1: sourceEvent?.subCategory?.externalId,
                    subCategory2: sourceEvent?.siteSpecificCategory?.externalId,
                    taskType: isRecurring
                        ? ActionItemKindExternalIdEnum.Recurring
                        : ActionItemKindExternalIdEnum.OneTime,
                    isPrivate: sourceEvent?.isPrivate ?? false,
                }}
                finishSteppedFunction={(value) => setRefatchActions()}
                clickLeaveCancelModal={cancelNewAction}
                siteId={sourceEvent?.reportingSite?.externalId!}
            />
        </Box>
    )
}
