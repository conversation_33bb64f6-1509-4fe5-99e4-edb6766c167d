import { useEffect, useState } from 'react'
import { ActionRecurringFilter } from '../../models/action'
import { getLocalUserSite } from '@celanese/celanese-ui'
import { EntityType, GetSpace } from '../../utils/space-util'
import { useGetAllResultsFunction } from '../general-functions/useGetAllResultFunction'
import { Filter } from '../../models/base-hook-request'
import { AllActionStatusExternalIdEnum } from '../../enums/ActionItemStatusEnum'

export interface ActionByRecurrenceQueryRequest {
    unit?: string[]
    category?: string[]
    specificCategory?: string
    status?: string[]
    recurrenceType?: string[]
    start?: string | null
    noEndDate?: boolean
    sourceEventId?: string
}

const queryListAction = `
    title
    space
    externalId
    owner {
        user {
            lastName
            firstName
        }
    }
    reportingUnit {
        externalId
        name
        description
    }
    recurrenceInstance {
        externalId
        space
        description
        recurrenceType {
        name
        externalId
        space
        description
        }
        weekDays
        months
        dayOfTheMonth
        quarters
        monthOfTheYear
        nextDates
        startDate
        endDate
    }
    currentStatus {
        externalId
        name
        space
        description
    }
    category {
        name
    }
`

export const useActionByRecurrence = (request: ActionByRecurrenceQueryRequest) => {
    const [resultData, setResultData] = useState<{ data: ActionRecurringFilter[]; loading: boolean }>({
        data: [],
        loading: true,
    })

    const { getAllResults: getAllData } = useGetAllResultsFunction<ActionRecurringFilter>(queryListAction, 'listAction')

    const filter: Filter<ActionRecurringFilter> = {
        and: [
            ...(!request.sourceEventId
                ? [
                      {
                          space: {
                              eq: GetSpace(EntityType.Instance, getLocalUserSite()?.siteCode),
                          },
                      },
                  ]
                : []),
            {
                not: {
                    or: [{ recurrenceInstance: { externalId: { isNull: true } } }, { isTemplate: { eq: true } }],
                },
            },
            {
                currentStatus: {
                    externalId: { in: [AllActionStatusExternalIdEnum.Active, AllActionStatusExternalIdEnum.Inactive] },
                },
            },
            ...(request.unit && request.unit.length > 0
                ? [{ reportingUnit: { externalId: { in: request.unit } } }]
                : []),
            ...(request.category && request.category.length > 0
                ? [{ category: { externalId: { in: request.category } } }]
                : []),
            ...(request.status && request.status.length > 0
                ? [{ currentStatus: { externalId: { in: request.status } } }]
                : []),
            ...(request.recurrenceType && request.recurrenceType.length > 0
                ? [{ recurrenceInstance: { recurrenceType: { externalId: { in: request.recurrenceType } } } }]
                : []),
            ...(request.start ? [{ recurrenceInstance: { startDate: { eq: request.start } } }] : []),
            ...(request.noEndDate !== undefined
                ? [{ recurrenceInstance: { endDate: { isNull: request.noEndDate } } }]
                : []),
            ...(request.sourceEventId
                ? [{ recurrenceInstance: { sourceEvent: { externalId: { eq: request.sourceEventId } } } }]
                : []),
        ],
    }

    const fetchData = async () => {
        const res = await getAllData(filter)
        if (res.length === 0) {
            setResultData({ data: [], loading: false })
        } else {
            setResultData({ data: sortActionByCreatedTimeDesc(res), loading: false })
        }
    }

    useEffect(() => {
        fetchData()
    }, [request])

    const refetch = () => {
        fetchData()
    }

    return {
        loading: resultData.loading,
        actions: resultData.data,
        refetchActions: refetch,
    }
}

const sortActionByCreatedTimeDesc = (actionItems: ActionRecurringFilter[]): ActionRecurringFilter[] => {
    const sortedActions = [...actionItems]
    return sortedActions?.sort((a, b) => new Date(b.createdTime).getTime() - new Date(a.createdTime).getTime())
}
