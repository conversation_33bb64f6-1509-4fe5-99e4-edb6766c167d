import dayjs, { Dayjs } from 'dayjs'
import { RecurrenceSubTypeEnum } from '../enums/RecurrenceSubTypeEnum'
import { RecurrenceTypeEnum } from '../enums/RecurrenceTypeEnum'
import { RecurrenceForm } from '../models/forms/recurrence-form'
import { formatDate } from './date'
import { EntityType, GetSpace } from './space-util'
import { RecurrenceInstance } from '../models/action-detail'
import { getUpdatedValue } from './transform-input'
import { compareObjects } from './utils'

export const formatRecurrence = (
    siteId: string,
    recurrenceForm: RecurrenceForm,
    recurrenceExternalId?: string,
    recurrenceInstance?: RecurrenceInstance
) => {
    const generalProps = {
        endDate: recurrenceForm?.noEndDate ? null : formatDate(recurrenceForm.endDate!),
        startDate: formatDate(recurrenceForm?.startDate),
    }

    const differences = recurrenceInstance
        ? compareObjects(
              { ...recurrenceInstance, recurrenceType: recurrenceInstance.recurrenceType.externalId },
              {
                  ...recurrenceForm,
                  ...generalProps,
                  recurrenceType: `RCT-${recurrenceForm.recurrenceSubType ?? recurrenceForm.recurrenceType}`,
              }
          )
        : {}

    const hasDifferences = Object.keys(differences).length > 0

    let recurrenceType: RecurrenceTypeEnum | RecurrenceSubTypeEnum = recurrenceForm.recurrenceType
    const defaultProps = {
        weekDays: getUpdatedValue(recurrenceInstance?.weekDays, null),
        months: getUpdatedValue(recurrenceInstance?.months, null),
        dayOfTheMonth: getUpdatedValue(recurrenceInstance?.dayOfTheMonth, null),
        quarters: getUpdatedValue(recurrenceInstance?.quarters, null),
        monthOfTheYear: getUpdatedValue(recurrenceInstance?.monthOfTheYear, null),
        nextDates: hasDifferences ? null : recurrenceInstance?.nextDates?.map((date) => formatDate(dayjs(date))!),
    }

    let typeSpecificProps = { ...defaultProps }

    switch (recurrenceForm.recurrenceType) {
        case RecurrenceTypeEnum.Weekly:
            typeSpecificProps = {
                ...defaultProps,
                weekDays: recurrenceForm?.weekDays,
            }
            break

        case RecurrenceTypeEnum.Monthly:
            typeSpecificProps = {
                ...defaultProps,
                months: recurrenceForm?.months,
                dayOfTheMonth: recurrenceForm?.dayOfTheMonth,
            }
            break

        case RecurrenceTypeEnum.Quarterly:
            typeSpecificProps = {
                ...defaultProps,
                quarters: recurrenceForm.quarters,
            }
            break

        case RecurrenceTypeEnum.Yearly:
            recurrenceType = recurrenceForm.recurrenceSubType!
            typeSpecificProps = {
                ...defaultProps,
                dayOfTheMonth: recurrenceForm.dayOfTheMonth,
                monthOfTheYear: recurrenceForm.monthOfTheYear,
            }
            break

        case RecurrenceTypeEnum.Custom:
            typeSpecificProps = {
                ...defaultProps,
                nextDates: recurrenceForm?.customDates?.map((cd: Dayjs) => formatDate(cd)!),
            }
            break

        default:
            break
    }

    if (!recurrenceExternalId) {
        return {
            recurrenceType: `RCT-${recurrenceType}`,
            ...generalProps,
            ...typeSpecificProps,
        }
    }

    const isRecurrence = {
        externalId: recurrenceExternalId,
        space: GetSpace(EntityType.Instance, siteId.slice(4)),
    }

    const recurrenceTypeModel = {
        recurrenceType: `RCT-${recurrenceType}`,
    }

    return {
        ...recurrenceTypeModel,
        ...generalProps,
        ...typeSpecificProps,
        ...isRecurrence,
    }
}
