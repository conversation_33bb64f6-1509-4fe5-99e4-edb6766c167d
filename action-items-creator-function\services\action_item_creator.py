import asyncio
import json
import os
import sys
import uuid
from copy import deepcopy
from datetime import UTC, datetime, timedelta
from typing import Any, Optional

from pydantic import ValidationError

script_dir = os.path.dirname(os.path.abspath(__file__))
project_dir = os.path.dirname(script_dir)
sys.path.append(project_dir)
from clients.action_item_client import ActionItemClient
from clients.approval_workflow.constants import (
    ApprovalWorkflowCondition,
    ApprovalWorkflowConsentType,
)
from clients.core.constants import (
    ActionItemKind,
    ActionSourceTypeEnum,
    ActionStatusEnum,
    ApplicationEnum,
    ApprovalWorkflowStepDescriptionEnum,
    ApprovalWorkflowStepStatusEnum,
    FileMetadataKey,
    SourceEventTypeEnum,
)
from clients.sort_mapper import GetSortMapperRequest, SortMapper
from clients.source_event.constants import SourceEventStatus
from models.action_item import (
    ActionItem,
    ActionItemLink,
    ApprovalWorkflow,
    ApprovalWorkflowStep,
    MetadataField,
    NodeReference,
    <PERSON>cu<PERSON>Instance,
    RecurrenceInstanceType,
    SourceEvent,
    SourceEventHistory,
    StatusHistoryInstance,
)
from models.entities_enum import EntitiesEnum
from models.notification import Notification
from models.settings import Settings
from models.upsert_action_item_request import Attachment, UpsertActionItemRequest
from services.cognite_service import CogniteService
from services.graphql_service import GraphqlService
from services.logging_service import LoggingService
from services.notification_service import NotificationService
from utils.general_utils import (
    flat_map,
    generate_query,
    get_user_id_from_user_azure_attribute_id,
)
from utils.id_generation_utils import IdGenerator
from utils.space_utils import get_transactional_space_from_site_id


class ActionItemCreator:
    """
    A service class responsible for creating and managing action items.

    This class handles the processing, validation, and creation of action items,
    including managing related entities such as attachments, notifications, and
    source events. It interacts with various services and clients to perform its
    operations.
    """

    def __init__(
        self,
        cognite_service: CogniteService,
        graphql_service: GraphqlService,
        settings: Settings,
        logging_service: LoggingService,
        notification_service: NotificationService,
        action_item_client: ActionItemClient,
    ) -> None:
        self._cognite_service = cognite_service
        self._graphql_service = graphql_service
        self._log = logging_service
        self._settings = settings
        self._now_datetime = datetime.now(tz=UTC)
        self._now_date = self._now_datetime.date()
        self._id_generator = IdGenerator[EntitiesEnum](self._now_datetime)
        self._notification_service = notification_service
        self.errors_by_event_id = {}
        self.action_items: list[ActionItem] = []
        self.source_event_list: list[SourceEvent] = []
        self.attachments_by_event_id: dict[str, list[Attachment]] = {}
        self.users_in_request = set()
        self._action_item_client = action_item_client

    async def execute(
        self,
        action_items_request: list[dict],
        run_validations_only: bool = False,
        bypass_validations: bool = False,
        send_notifications: bool = True,
    ) -> tuple:
        if len(action_items_request) == 0:
            self._log.info("No action items to create.")
            return [], {}, []

        parsed_requests, sort_mapper = self._get_parsed_requests(action_items_request)

        batch_size = 50
        batches = [
            parsed_requests[i : i + batch_size]
            for i in range(0, len(parsed_requests), batch_size)
        ]

        for batch_number, batch in enumerate(batches):
            # print(f"Processing batch {batch_number + 1}/{len(batches)}") # for debugger
            await asyncio.gather(
                *[self.process_item(item, sort_mapper) for item in batch],
            )

        # region: Validators
        action_items_to_create_by_event_id = {}
        if not bypass_validations:
            (
                categories,
                sub_categories,
                site_specific_categories,
                applications,
                reporting_units,
                reporting_locations,
                reporting_sites,
                business_lines,
                reporting_lines,
                source_types,
                metadata_field_types,
                users,
            ) = await self._graphql_service.get_existing_entities(
                list(self.users_in_request),
            )
            for action_item in self.action_items:
                event_id = action_item._event_id
                self._log.info(f"Validating action item from event {event_id}.")
                direct_relation_errors: list[str] = []

                source_event_has_errors = len(self.errors_by_event_id[event_id]) > 0
                if source_event_has_errors:
                    continue

                reporting_site_exists = (
                    action_item.reportingSite.externalId in reporting_sites
                )
                if not reporting_site_exists:
                    direct_relation_errors.append(
                        f"Reporting site {action_item.reportingSite.externalId} not found.",
                    )

                if (
                    action_item.assignedTo is not None
                    and action_item.assignedTo.externalId not in users
                ):
                    direct_relation_errors.append(
                        f"Assigned to user {action_item.assignedTo.externalId} not found.",
                    )

                if (
                    action_item.owner is not None
                    and action_item.owner.externalId not in users
                ):
                    direct_relation_errors.append(
                        f"Owner user {action_item.owner.externalId} not found.",
                    )

                if action_item.createdBy.externalId not in users:
                    direct_relation_errors.append(
                        f"Created by user {action_item.createdBy.externalId} not found.",
                    )

                if action_item.application.externalId not in applications:
                    direct_relation_errors.append(
                        f"Application {action_item.application.externalId} not found.",
                    )

                if action_item.category.externalId not in categories:
                    direct_relation_errors.append(
                        f"Category {action_item.category.externalId} not found.",
                    )

                if (
                    action_item.subCategory is not None
                    and action_item.subCategory.externalId not in sub_categories
                ):
                    direct_relation_errors.append(
                        f"Sub category {action_item.subCategory.externalId} not found.",
                    )

                if (
                    action_item.siteSpecificCategory is not None
                    and action_item.siteSpecificCategory.externalId
                    not in site_specific_categories
                ):
                    direct_relation_errors.append(
                        f"Site specific category {action_item.siteSpecificCategory.externalId} not found.",
                    )

                if action_item.reportingUnit is not None and (
                    action_item.reportingUnit.externalId
                    not in reporting_units
                    # NOTE: commented because the relationship is not consistent (there are WAS units that are not linked to STS-WAS via ReportingSite.reportingUnits)
                    # or (
                    #     reporting_site_exists
                    #     and action_item.reportingUnit.externalId
                    #     not in units_per_site[action_item.reportingSite.externalId]
                    # )
                ):
                    direct_relation_errors.append(
                        f"Reporting unit {action_item.reportingUnit.externalId} not found or is not linked to the given site.",
                    )

                if action_item.reportingLocation is not None and (
                    action_item.reportingLocation.externalId
                    not in reporting_locations
                    # NOTE: commented because the relationship is not consistent (there are WAS locations that are not linked to STS-WAS via ReportingSite.reportingLocations)
                    # or (
                    #     reporting_site_exists
                    #     and action_item.reportingLocation.externalId
                    #     in locations_per_site[action_item.reportingSite.externalId]
                    # )
                ):
                    direct_relation_errors.append(
                        f"Reporting location {action_item.reportingLocation.externalId} not found or is not linked to the given site.",
                    )

                if (
                    action_item.businessLine is not None
                    and action_item.businessLine.externalId not in business_lines
                ):
                    direct_relation_errors.append(
                        f"Business line {action_item.businessLine.externalId} not found.",
                    )

                if (
                    action_item.reportingLine is not None
                    and action_item.reportingLine.externalId not in reporting_lines
                ):
                    direct_relation_errors.append(
                        f"Reporting line {action_item.reportingLine.externalId} not found.",
                    )

                if (
                    action_item.sourceType is not None
                    and action_item.sourceType.externalId not in source_types
                ):
                    direct_relation_errors.append(
                        f"Source type {action_item.sourceType.externalId} not found.",
                    )

                if action_item.metadatas is not None:
                    metadata_errors: list[str] = []
                    for metadata in action_item.metadatas:
                        if (
                            metadata_field_type := metadata_field_types.get(
                                metadata.metadataType.externalId,
                            )
                        ) is None:
                            metadata_errors.append(
                                f"Metadata Field Type {metadata.metadataType.externalId} not found.",
                            )
                            continue

                        if (
                            possible_values := metadata_field_type.get("possibleValues")
                        ) is not None and metadata.value not in possible_values:
                            metadata_errors.append(
                                f"Invalid value for Metadata Field Type {metadata.metadataType.externalId}. Received: {metadata.value}. Available values: {possible_values}.",
                            )

                    direct_relation_errors.extend(metadata_errors)

                event_id = action_item._event_id
                action_item_has_errors = len(direct_relation_errors) > 0
                if action_item_has_errors:
                    self.errors_by_event_id[event_id].extend(direct_relation_errors)
                    continue

                if action_items_to_create_by_event_id.get(event_id) is None:
                    action_items_to_create_by_event_id[event_id] = []

                action_items_to_create_by_event_id[event_id].append(action_item)

        else:
            for action_item in self.action_items:
                event_id = action_item._event_id
                self._log.info(f"Validating action item from event {event_id}.")

                source_event_has_errors = len(self.errors_by_event_id[event_id]) > 0
                if source_event_has_errors:
                    continue

                event_id = action_item._event_id

                if action_items_to_create_by_event_id.get(event_id) is None:
                    action_items_to_create_by_event_id[event_id] = []

                action_items_to_create_by_event_id[event_id].append(action_item)

        if run_validations_only:
            return [], self.errors_by_event_id, []

        if len(action_items_to_create_by_event_id) == 0:
            self._log.info("No valid action items to create.")
            return [], self.errors_by_event_id, []

        # endregion

        # region: Update Cognite
        for event_id, attachments_list in self.attachments_by_event_id.items():
            if len(self.errors_by_event_id[event_id]) > 0:
                continue

            try:
                for attachment in attachments_list:
                    if attachment.is_file_from_aim:
                        self._cognite_service.set_file_metadata(
                            attachment.received_external_id,
                            {
                                FileMetadataKey.RELATED_ACTIONS.value: json.dumps(
                                    attachment.action_item_external_ids,
                                ),
                                FileMetadataKey.FILE_SIZE.value: json.dumps(
                                    attachment.file_size,
                                ),
                                FileMetadataKey.USER.value: json.dumps(
                                    attachment.user,
                                ),
                            },
                        )
                        continue

                    self._log.info(
                        f"Duplicating file with external id {attachment.received_external_id} to AIM dataset under {attachment.target_external_id} external id.",
                    )

                    self._cognite_service.duplicate_file_to_target_dataset(attachment)

            except Exception as err:
                self._log.error(
                    f"Could not upload attachments for event {event_id}. Error: {err}",
                )
                self.errors_by_event_id[event_id].append(str(err))
                continue

        action_items_to_create = []
        for event_id, action_item_list in action_items_to_create_by_event_id.items():
            if len(self.errors_by_event_id[event_id]) > 0:
                continue

            action_items_to_create.extend(action_item_list)

        try:
            self._log.info(f"Upserting {len(action_items_to_create)} action items.")
            upserted_actions, errs = self._cognite_service.upsert_action_items(
                action_items_to_create,
            )
            if len(errs) > 0:
                self._log.error(f"Could not upsert action items. Errors: {errs}.")
                self._cognite_service.delete_files(
                    flat_map(
                        lambda atts: [file.target_external_id for file in atts],
                        [
                            self.attachments_by_event_id[key]
                            for key in self.attachments_by_event_id
                        ],
                    ),
                )
                return [], {"internal": errs}, []

        except Exception as err:
            self._log.error(f"Could not upsert action items. Error: {err}")
            self._cognite_service.delete_files(
                flat_map(
                    lambda atts: [file.target_external_id for file in atts],
                    [
                        self.attachments_by_event_id[key]
                        for key in self.attachments_by_event_id
                    ],
                ),
            )
            return [], {"internal": [str(err)]}, []

        if len(self.source_event_list) > 0:
            try:
                self._log.info(
                    f"Upserting {len(action_items_to_create)} source events.",
                )
                _, err = self._cognite_service.upsert_source_events(
                    self.source_event_list,
                )

                if len(err) > 0:
                    await self._cleanup()
                    self._log.error(f"Could not upsert source events. Errors: {err}.")
                    self._cognite_service.delete_files(
                        flat_map(
                            lambda atts: [file.target_external_id for file in atts],
                            [
                                self.attachments_by_event_id[key]
                                for key in self.attachments_by_event_id
                            ],
                        ),
                    )
                    return [], {"internal": err}, []

            except Exception as err:
                self._log.error(f"Could not upsert source events. Error: {err}")
                return [], {"internal": [str(err)]}, []

        actions_for_notifications = list(
            filter(
                lambda ai: ai.currentStatus.externalId
                == ActionStatusEnum.ASSIGNED.value,
                action_items_to_create,
            ),
        )
        actions_for_notifications_ids = [
            action.externalId for action in actions_for_notifications
        ]
        if send_notifications:
            try:
                notifications = []

                async def _get_user_if_exists(
                    user_id: str | None,
                ) -> dict[str, Any] | None:
                    if not user_id:
                        return None
                    return await self._get_user_by_user_azure_attribute_id(user_id)

                for action_item in list(
                    filter(
                        lambda ai: ai.currentStatus.externalId
                        == ActionStatusEnum.ASSIGNED.value,
                        action_items_to_create,
                    ),
                ):
                    action_item_dict = action_item.model_dump()
                    action_item_dict.update(
                        {
                            "currentStatus": {"name": "Assigned"},
                            "actionItemKind": {"name": "One-Time"},
                            "site": action_item.reportingSite.externalId or "-",
                            "subCategory2": {
                                "description": (
                                    action_item.siteSpecificCategory.description
                                    if action_item.siteSpecificCategory
                                    else "-"
                                ),
                            },
                        },
                    )

                    (
                        approver,
                        verifier,
                        assignee,
                        owner,
                        created_by,
                    ) = await asyncio.gather(
                        _get_user_if_exists(action_item._approver_id),
                        _get_user_if_exists(action_item._verifier_id),
                        _get_user_if_exists(action_item.assignedTo.externalId),
                        _get_user_if_exists(action_item.owner.externalId),
                        _get_user_if_exists(action_item.createdBy.externalId),
                    )

                    def _extract_user_info(
                        user: dict[str, Any] | None,
                        default_info: dict | None = None,
                    ) -> dict:
                        default_info = default_info or {}
                        return {
                            **default_info,
                            "name": user.get("displayName", "-") if user else "-",
                            "firstName": user.get("firstName", "-") if user else "-",
                            "lastName": user.get("lastName", "-") if user else "-",
                            "userMail": user.get("email", "-") if user else "-",
                        }

                    action_item_dict.update(
                        {
                            "approver": _extract_user_info(approver),
                            "verifier": _extract_user_info(verifier),
                            "assignedTo": _extract_user_info(
                                assignee,
                                action_item_dict.get("assignedTo"),
                            ),
                            "owner": _extract_user_info(
                                owner,
                                action_item_dict.get("owner"),
                            ),
                            "createdBy": _extract_user_info(
                                created_by,
                                action_item_dict.get("createdBy"),
                            ),
                        },
                    )

                    fields_to_add_props = [
                        ("application", applications, "name"),
                        ("category", categories, "name"),
                        ("subCategory", sub_categories, "name"),
                        (
                            "siteSpecificCategory",
                            site_specific_categories,
                            "description",
                        ),
                        ("reportingUnit", reporting_units, "description"),
                        ("reportingLocation", reporting_locations, "description"),
                        ("reportingSite", reporting_sites, "description"),
                        ("reportingLine", reporting_lines, "description"),
                        ("businessLine", business_lines, "description"),
                        ("sourceType", source_types, "name"),
                    ]

                    for (
                        action_field,
                        entities_dict,
                        prop_to_add_name,
                    ) in fields_to_add_props:
                        prop = action_item_dict.get(action_field)
                        if prop is None:
                            continue

                        external_id = prop.get("externalId")
                        if external_id is None:
                            continue

                        entity = entities_dict.get(external_id)
                        if entity is None:
                            continue

                        entity_prop_to_add = entity.get(prop_to_add_name)
                        if entity_prop_to_add is None:
                            continue

                        action_item_dict[action_field][
                            prop_to_add_name
                        ] = entity_prop_to_add

                    action_item_dict["siteSpecificCategory"] = {
                        "description": "-",
                        "space": "-",
                        "externalId": "-",
                        **(action_item_dict.get("siteSpecificCategory") or {}),
                    }

                    assigned_to_email = action_item_dict["assignedTo"]["userMail"]
                    owner_email = action_item_dict["owner"]["userMail"]

                    notifications.append(
                        Notification.from_action_item_creation(
                            action_item_dict,
                            assigned_to_email,
                            owner_email,
                        ),
                    )

                if len(notifications) > 0:
                    self._notification_service.send_notifications(notifications)

            except Exception as err:
                self._log.error(f"Could not send notifications. Error: {err}")
        # endregion

        await self._cleanup()

        return upserted_actions, self.errors_by_event_id, actions_for_notifications_ids

    async def _cleanup(self) -> None:
        await self._graphql_service.cleanup()

    async def process_item(
        self,
        item_request: UpsertActionItemRequest,
        sort_mapper: SortMapper,
    ) -> None:
        event_id = item_request._event_id
        self.errors_by_event_id[event_id] = self.errors_by_event_id.get(event_id) or []
        actions_by_event = []
        self._log.info(f"Processing action item request from event {event_id}.")

        aim_ref_space = self._settings.aim_ref_instances_space
        um_space = self._settings.user_management_instances_space
        assets_space = self._settings.asset_hierarchy_instances_space

        now_str = self._now_datetime.replace(microsecond=0).isoformat()

        status = (
            ActionStatusEnum.ASSIGNED.value
            if item_request.action_item_kind_id == ActionItemKind.ONE_TIME
            else ActionStatusEnum.ACTIVE.value
        )

        aim_space = get_transactional_space_from_site_id(
            item_request.reporting_site_id,
            item_request.private,
        )

        aim_site_space = get_transactional_space_from_site_id(
            item_request.reporting_site_id,
        )

        aim_space_private = get_transactional_space_from_site_id(
            item_request.reporting_site_id,
            item_request.private,
        )

        aim_dataset_id = (
            item_request.target_dataset_id
            or self._cognite_service.get_dataset_id(aim_space)
        )

        # this shouldn't happen since we are validating it in the event processor
        if aim_dataset_id is None:
            self.errors_by_event_id[event_id].append(
                f"Site {item_request.reporting_site_id} is not supported or does not exist.",
            )
            return

        action_item: ActionItem
        try:
            source_event_external_id = None
            source_information = None

            source_information = self._get_process_source_information(
                self._get_source_information(item_request),
            )

            existing_source_event = await self._get_source_event(
                source_information,
                [aim_site_space, aim_space_private],
                item_request.source_event_id,
            )

            source_info_exists = next(
                (
                    event
                    for event in self.source_event_list
                    if event.sourceInformation == source_information
                    and (
                        event.space == aim_site_space
                        or event.space == aim_space_private
                    )
                ),
                None,
            )

            source_event_external_id = (
                source_info_exists.externalId
                if source_info_exists
                else (
                    existing_source_event.externalId
                    if existing_source_event
                    else (
                        self._id_generator.next_id(EntitiesEnum.SourceEvent)
                        if (
                            item_request.source_information
                            or item_request.source_id
                            or item_request.action_item_kind_id
                            == ActionItemKind.RECURRING
                        )
                        else None
                    )
                )
            )

            external_id = (
                self._id_generator.next_id(EntitiesEnum.Action)
                if item_request.source_type_id == ActionSourceTypeEnum.AIM_SCRATCH.value
                else self._id_generator.next_id(
                    EntitiesEnum.Action,
                    prefix_id=(
                        source_info_exists.externalId
                        if source_info_exists
                        else source_event_external_id
                    ),
                    prefix_id_existing_count=(
                        source_info_exists.actionsCount
                        if source_info_exists
                        else (
                            existing_source_event.actionsCount
                            if existing_source_event
                            else 0
                        )
                    ),
                )
            )

            sort_source_event_title = (
                (
                    source_info_exists.title
                    if source_info_exists
                    else existing_source_event.title if existing_source_event else None
                )
                if item_request.source_type_id != ActionSourceTypeEnum.AIM_SCRATCH.value
                else None
            )

            action_item = ActionItem(
                externalId=external_id,
                space=aim_space,
                createdAt=self._id_generator.get_timestamp(),
                title=item_request.title,
                description=item_request.description,
                statusHistory=[
                    StatusHistoryInstance(
                        externalId=self._id_generator.next_id(
                            EntitiesEnum.StatusHistoryInstance,
                        ),
                        space=aim_space,
                        action=NodeReference(
                            externalId=external_id,
                            space=aim_space,
                        ),
                        status=NodeReference(externalId=status, space=aim_ref_space),
                        friendlyName='{"actionUser":"createdBy","comments":""}',
                        statusSubject=NodeReference(
                            externalId=item_request.created_by_id,
                            space=um_space,
                        ),
                        changedAt=str(now_str),
                    ),
                ],
                currentStatus=NodeReference(externalId=status, space=aim_ref_space),
                approvalWorkflow=self._get_approval_workflow(
                    item_request.approver_id,
                    item_request.verifier_id,
                    item_request.created_by_id,
                    aim_space,
                ),
                recurrenceInstance=(
                    RecurrenceInstance(
                        **{
                            **item_request.recurrence_instance.model_dump(),
                            "externalId": self._id_generator.next_id(
                                EntitiesEnum.RecurrenceInstance,
                            ),
                            "space": aim_space,
                            "recurrenceType": RecurrenceInstanceType(
                                externalId=item_request.recurrence_instance.recurrenceType,
                                space=aim_ref_space,
                            ),
                            "sourceEvent": (
                                SourceEvent(
                                    externalId=source_event_external_id,
                                    space=aim_space,
                                )
                                if source_event_external_id
                                else None
                            ),
                        },
                    )
                    if item_request.action_item_kind_id == ActionItemKind.RECURRING
                    and item_request.recurrence_instance is not None
                    else None
                ),
                actionItemKind=NodeReference(
                    externalId=item_request.action_item_kind_id,
                    space=aim_ref_space,
                ),
                application=NodeReference(
                    externalId=item_request.application_id,
                    space=um_space,
                ),
                sourceInformation=item_request.source_information,
                category=NodeReference(
                    externalId=item_request.category_id,
                    space=aim_ref_space,
                ),
                subCategory=(
                    NodeReference(
                        externalId=item_request.sub_category_id,
                        space=aim_ref_space,
                    )
                    if item_request.sub_category_id is not None
                    else None
                ),
                siteSpecificCategory=(
                    NodeReference(
                        externalId=item_request.site_specific_category_id,
                        space=aim_site_space,
                    )
                    if item_request.site_specific_category_id is not None
                    else None
                ),
                owner=NodeReference(
                    externalId=item_request.owner_id or item_request.created_by_id,
                    space=um_space,
                ),
                createdBy=NodeReference(
                    externalId=item_request.created_by_id,
                    space=um_space,
                ),
                dueDate=(
                    item_request.due_date.isoformat()
                    if item_request.action_item_kind_id == ActionItemKind.ONE_TIME
                    and item_request.due_date is not None
                    else None
                ),
                displayDueDate=(
                    item_request.due_date.isoformat()
                    if item_request.action_item_kind_id == ActionItemKind.ONE_TIME
                    and item_request.due_date is not None
                    else None
                ),
                assignmentDate=(
                    item_request.assignment_date.isoformat()
                    if item_request.action_item_kind_id == ActionItemKind.ONE_TIME
                    and item_request.assignment_date is not None
                    else None
                ),
                approvalDate=(
                    item_request.approval_date.isoformat()
                    if item_request.action_item_kind_id == ActionItemKind.ONE_TIME
                    and item_request.approval_date is not None
                    else None
                ),
                verificationDate=(
                    item_request.verification_date.isoformat()
                    if item_request.action_item_kind_id == ActionItemKind.ONE_TIME
                    and item_request.verification_date is not None
                    else None
                ),
                conclusionDate=(
                    item_request.conclusion_date.isoformat()
                    if item_request.action_item_kind_id == ActionItemKind.ONE_TIME
                    and item_request.conclusion_date is not None
                    else None
                ),
                reportingUnit=(
                    NodeReference(
                        externalId=item_request.reporting_unit_id,
                        space=assets_space,
                    )
                    if item_request.reporting_unit_id is not None
                    else None
                ),
                reportingLocation=(
                    NodeReference(
                        externalId=item_request.reporting_location_id,
                        space=assets_space,
                    )
                    if item_request.reporting_location_id is not None
                    else None
                ),
                links=(
                    [
                        ActionItemLink(
                            externalId=self._id_generator.next_id(
                                EntitiesEnum.ActionItemLink,
                            ),
                            space=aim_space,
                            action=NodeReference(
                                externalId=external_id,
                                space=aim_space,
                            ),
                            link=link.link,
                            description=link.description,
                        )
                        for link in item_request.links
                    ]
                    if item_request.links is not None
                    else None
                ),
                evidenceRequired=item_request.evidence_required,
                priority=(
                    item_request.priority.strip().lower()
                    if item_request.priority
                    else None
                ),
                businessLine=(
                    NodeReference(
                        externalId=item_request.business_line_id,
                        space=assets_space,
                    )
                    if item_request.business_line_id is not None
                    else None
                ),
                isPlantShutdownRequired=item_request.is_plant_shutdown_required,
                voeActionItem=item_request.voe_action_item,
                estimatedCost=item_request.estimated_cost,
                price=item_request.price,
                priceCurrencyKey=item_request.price_currency_key,
                estimatedGrade=item_request.estimated_grade,
                actionTaken=item_request.action_taken,
                objectType=item_request.object_type,
                objectExternalId=item_request.object_id,
                reportingSite=NodeReference(
                    externalId=item_request.reporting_site_id,
                    space=assets_space,
                ),
                reportingLine=(
                    NodeReference(
                        externalId=item_request.reporting_line_id,
                        space=assets_space,
                    )
                    if item_request.reporting_line_id is not None
                    else None
                ),
                sourceId=(
                    item_request.source_id
                    if item_request.source_id is not None
                    else (
                        source_event_external_id if source_event_external_id else None
                    )
                ),
                sourceType=(
                    NodeReference(
                        externalId=item_request.source_type_id,
                        space=aim_ref_space,
                    )
                    if item_request.source_type_id is not None
                    else None
                ),
                isPrivate=item_request.private,
                viewUsers=(
                    [
                        NodeReference(externalId=owner_id, space=um_space)
                        for owner_id in item_request.view_users
                    ]
                    if item_request.view_users and item_request.private
                    else None
                ),
                viewRoles=(
                    [
                        NodeReference(externalId=owner_id, space=um_space)
                        for owner_id in item_request.view_roles
                    ]
                    if item_request.view_roles and item_request.private
                    else None
                ),
                viewTeams=(
                    [
                        NodeReference(externalId=owner_id, space=um_space)
                        for owner_id in item_request.view_teams
                    ]
                    if item_request.view_teams and item_request.private
                    else None
                ),
                views=None,
                metadatas=(
                    [
                        MetadataField(
                            externalId=self._id_generator.next_id(
                                EntitiesEnum.MetadataField,
                            ),
                            space=aim_space,
                            action=NodeReference(
                                externalId=external_id,
                                space=aim_space,
                            ),
                            metadataType=NodeReference(
                                externalId=m.metadata_field_type_external_id,
                                space=aim_ref_space,
                            ),
                            value=m.value,
                        )
                        for m in item_request.metadatas
                    ]
                    if item_request.metadatas is not None
                    else None
                ),
                sortCategory=sort_mapper.get_category_sort_value(
                    item_request.category_id,
                ),
                sortSubCategory=sort_mapper.get_sub_category_sort_value(
                    item_request.sub_category_id,
                ),
                sortSiteSpecificCategory=sort_mapper.get_site_specific_category_sort_value(
                    item_request.site_specific_category_id,
                    aim_site_space,
                ),
                sortOwner=sort_mapper.get_user_azure_attribute_sort_value(
                    item_request.owner_id,
                ),
                sortReportingUnit=sort_mapper.get_reporting_unit_sort_value(
                    item_request.reporting_unit_id,
                ),
                sortReportingLocation=sort_mapper.get_reporting_location_sort_value(
                    item_request.reporting_location_id,
                ),
                sortApplication=sort_mapper.get_application_sort_value(
                    item_request.application_id,
                ),
                sortCurrentStatus=sort_mapper.get_action_status_sort_value(status),
                sortApprover=sort_mapper.get_user_azure_attribute_sort_value(
                    item_request.approver_id,
                ),
                sortVerifier=sort_mapper.get_user_azure_attribute_sort_value(
                    item_request.verifier_id,
                ),
                sortSourceEventTitle=sort_mapper.get_normalized_sort_value(
                    sort_source_event_title,
                ),
                sortTitle=sort_mapper.get_normalized_sort_value(
                    item_request.title,
                ),
                sortSourceInformation=sort_mapper.get_normalized_sort_value(
                    item_request.source_information,
                ),
            )

            action_item._event_id = event_id
            action_item._approver_id = item_request.approver_id
            action_item._verifier_id = item_request.verifier_id
        except ValidationError as err:
            msg = f"Invalid action item request from EventId: {event_id}. Error: {err}. Skipping."
            self._log.error(msg)
            self.errors_by_event_id[event_id].append(msg)
            return

        # NOTE: comment user-related lines below if users filter is not being applied in graphql_service.py (usually when running locally)
        self.users_in_request.update(item_request.assigned_to_ids)
        self.users_in_request.add(item_request.created_by_id)
        if item_request.owner_id is not None:
            self.users_in_request.add(item_request.owner_id)
        if item_request.approver_id is not None:
            self.users_in_request.add(item_request.approver_id)
        if item_request.verifier_id is not None:
            self.users_in_request.add(item_request.verifier_id)

        attachments = []
        received_files_metadata = self._cognite_service.get_files_metadata(
            item_request.attachment_ids,
        )
        for file_id in item_request.attachment_ids or []:
            file_metadata = received_files_metadata.get(file_id)
            if file_metadata is None:
                self.errors_by_event_id[event_id].append(
                    f"File {file_id} given as attachment was not found.",
                )
                continue

            attachments.append(
                Attachment(
                    received_external_id=file_id,
                    name=file_metadata.name,
                    mime_type=file_metadata.mime_type,
                    target_external_id=(
                        file_id
                        if file_metadata.data_set_id == aim_dataset_id
                        else self._id_generator.next_id(EntitiesEnum.Attachment)
                    ),
                    target_dataset_id=aim_dataset_id,
                    file_size=(
                        file_metadata.metadata.get("fileSize") or None
                        if file_metadata.metadata
                        else None
                    ),
                    user=(
                        file_metadata.metadata.get("user") or None
                        if file_metadata.metadata
                        else None
                    ),
                ),
            )

        self.attachments_by_event_id[event_id] = attachments

        if source_event_external_id is not None and source_info_exists is None:
            source_event = None
            if existing_source_event:
                source_event = SourceEvent(
                    externalId=source_event_external_id,
                    space=existing_source_event.space,
                    actionsCount=existing_source_event.actionsCount,
                    sourceInformation=existing_source_event.sourceInformation,
                )
            elif not item_request.private:
                non_aim_event_title = f"{item_request.source_id or item_request.source_information} - {(item_request.source_type_id or item_request.application_id).rsplit('-', 1)[-1]}"

                event_title = (
                    item_request.source_information or ""
                    if item_request.application_id == ApplicationEnum.AIM.value
                    else non_aim_event_title
                )

                source_event = SourceEvent(
                    externalId=source_event_external_id,
                    space=aim_space,
                    createdAt=self._id_generator.get_timestamp(),
                    status=NodeReference(
                        externalId=SourceEventStatus.IN_PROGRESS.value,
                        space=aim_ref_space,
                    ),
                    eventType=NodeReference(
                        externalId=(
                            SourceEventTypeEnum.AUTOMATIC.value
                            if item_request.application_id == ApplicationEnum.AIM.value
                            else SourceEventTypeEnum.EXTERNAL.value
                        ),
                        space=aim_ref_space,
                    ),
                    actionsCount=0,
                    title=event_title,
                    description=item_request.description,
                    sourceInformation=source_information,
                    owner=NodeReference(
                        externalId=item_request.owner_id or item_request.created_by_id,
                        space=um_space,
                    ),
                    application=NodeReference(
                        externalId=item_request.application_id,
                        space=um_space,
                    ),
                    reportingSite=NodeReference(
                        externalId=item_request.reporting_site_id,
                        space=assets_space,
                    ),
                    category=NodeReference(
                        externalId=item_request.category_id,
                        space=aim_ref_space,
                    ),
                    subCategory=(
                        NodeReference(
                            externalId=item_request.sub_category_id,
                            space=aim_ref_space,
                        )
                        if item_request.sub_category_id is not None
                        else None
                    ),
                    siteSpecificCategory=(
                        NodeReference(
                            externalId=item_request.site_specific_category_id,
                            space=aim_site_space,
                        )
                        if item_request.site_specific_category_id is not None
                        else None
                    ),
                    createdBy=NodeReference(
                        externalId=item_request.created_by_id,
                        space=um_space,
                    ),
                    dueDate=(
                        item_request.due_date.isoformat()
                        if item_request.action_item_kind_id == ActionItemKind.ONE_TIME
                        and item_request.due_date is not None
                        else None
                    ),
                    displayDueDate=(
                        item_request.due_date.isoformat()
                        if item_request.action_item_kind_id == ActionItemKind.ONE_TIME
                        and item_request.due_date is not None
                        else None
                    ),
                    assignmentDate=(
                        item_request.assignment_date.isoformat()
                        if item_request.action_item_kind_id == ActionItemKind.ONE_TIME
                        and item_request.assignment_date is not None
                        else None
                    ),
                    reportingUnit=(
                        NodeReference(
                            externalId=item_request.reporting_unit_id,
                            space=assets_space,
                        )
                        if item_request.reporting_unit_id is not None
                        else None
                    ),
                    reportingLocation=(
                        NodeReference(
                            externalId=item_request.reporting_location_id,
                            space=assets_space,
                        )
                        if item_request.reporting_location_id is not None
                        else None
                    ),
                    businessLine=(
                        NodeReference(
                            externalId=item_request.business_line_id,
                            space=assets_space,
                        )
                        if item_request.business_line_id is not None
                        else None
                    ),
                    isPrivate=False,
                    viewUsers=(
                        [
                            NodeReference(externalId=owner_id, space=um_space)
                            for owner_id in item_request.view_users
                        ]
                        if item_request.view_users and item_request.private
                        else None
                    ),
                    viewRoles=(
                        [
                            NodeReference(externalId=owner_id, space=um_space)
                            for owner_id in item_request.view_roles
                        ]
                        if item_request.view_roles and item_request.private
                        else None
                    ),
                    viewTeams=(
                        [
                            NodeReference(externalId=owner_id, space=um_space)
                            for owner_id in item_request.view_teams
                        ]
                        if item_request.view_teams and item_request.private
                        else None
                    ),
                    views=None,
                    sourceEventHistory=[
                        SourceEventHistory(
                            externalId=self._id_generator.next_id(
                                EntitiesEnum.SourceEventHistory,
                            ),
                            space=aim_space,
                            sourceEvent=NodeReference(
                                externalId=source_event_external_id,
                                space=aim_space,
                            ),
                            status="created",
                            comments="",
                            updateBy=NodeReference(
                                externalId=item_request.created_by_id,
                                space=um_space,
                            ),
                            changedAt=str(now_str),
                        ),
                    ],
                    sortReportingUnit=sort_mapper.get_reporting_unit_sort_value(
                        item_request.reporting_unit_id,
                    ),
                    sortReportingLocation=sort_mapper.get_reporting_location_sort_value(
                        item_request.reporting_location_id,
                    ),
                    sortStatus=sort_mapper.get_source_event_status_sort_value(
                        SourceEventStatus.IN_PROGRESS.value,
                    ),
                    sortCategory=sort_mapper.get_category_sort_value(
                        item_request.category_id,
                    ),
                    sortSubCategory=sort_mapper.get_sub_category_sort_value(
                        item_request.sub_category_id,
                    ),
                    sortSiteSpecificCategory=sort_mapper.get_site_specific_category_sort_value(
                        item_request.site_specific_category_id,
                        aim_site_space,
                    ),
                    sortTitle=sort_mapper.get_normalized_sort_value(event_title),
                )

            if source_event and not self._get_source_event_exists(
                source_event,
                self.source_event_list,
            ):
                self.source_event_list.append(source_event)

        if item_request.action_item_kind_id == ActionItemKind.RECURRING:
            action_item.assignees = [
                NodeReference(externalId=assignee_id, space=um_space)
                for assignee_id in item_request.assigned_to_ids
            ]

            self._proccess_attachments(attachments, action_item)
            self.action_items.append(action_item)
            actions_by_event.append(
                NodeReference(
                    externalId=action_item.externalId or "",
                    space=action_item.space or "",
                ),
            )
        else:
            for assignee_idx, assignee_id in enumerate(item_request.assigned_to_ids):
                per_assignee_action_item = deepcopy(action_item)
                assert per_assignee_action_item.externalId is not None
                assert per_assignee_action_item.space is not None
                per_assignee_external_id = per_assignee_action_item.externalId
                per_assignee_action_item.assignedTo = NodeReference(
                    externalId=assignee_id,
                    space=um_space,
                )
                per_assignee_action_item.statusHistory.append(
                    StatusHistoryInstance(
                        externalId=self._id_generator.next_id(
                            EntitiesEnum.StatusHistoryInstance,
                        ),
                        space=per_assignee_action_item.space,
                        action=NodeReference(
                            externalId=per_assignee_external_id,
                            space=per_assignee_action_item.space,
                        ),
                        status=NodeReference(
                            externalId=ActionStatusEnum.ASSIGNED.value,
                            space=per_assignee_action_item.currentStatus.space,
                        ),
                        friendlyName='{"actionUser":"assignedTo","comments":""}',
                        statusSubject=per_assignee_action_item.assignedTo,
                        changedAt=(self._now_datetime + timedelta(seconds=1))
                        .replace(microsecond=0)
                        .isoformat(),
                    ),
                )

                per_assignee_action_item.sortAssignee = (
                    sort_mapper.get_user_azure_attribute_sort_value(assignee_id)
                )

                if item_request.private:
                    source_event = source_info_exists or existing_source_event

                    per_assignee_action_item.generate_views(source_event)

                external_ids_are_already_filled = assignee_idx == 0
                if external_ids_are_already_filled:
                    self._proccess_attachments(attachments, per_assignee_action_item)
                    self.action_items.append(per_assignee_action_item)
                    actions_by_event.append(
                        NodeReference(
                            externalId=per_assignee_action_item.externalId,
                            space=per_assignee_action_item.space,
                        ),
                    )
                    continue

                per_assignee_external_id = (
                    self._id_generator.next_id(EntitiesEnum.Action)
                    if item_request.source_type_id
                    == ActionSourceTypeEnum.AIM_SCRATCH.value
                    else self._id_generator.next_id(
                        EntitiesEnum.Action,
                        prefix_id=(
                            source_info_exists.externalId
                            if source_info_exists
                            else source_event_external_id
                        ),
                        prefix_id_existing_count=(
                            source_info_exists.actionsCount + assignee_idx
                            if source_info_exists
                            else (
                                existing_source_event.actionsCount + assignee_idx
                                if existing_source_event
                                and existing_source_event.actionsCount
                                else assignee_idx
                            )
                        ),
                    )
                )

                per_assignee_action_item.createdAt = self._id_generator.get_timestamp()
                per_assignee_action_item.externalId = per_assignee_external_id
                for status_history in per_assignee_action_item.statusHistory:
                    status_history.externalId = self._id_generator.next_id(
                        EntitiesEnum.StatusHistoryInstance,
                    )
                    status_history.action.externalId = per_assignee_external_id

                if per_assignee_action_item.recurrenceInstance is not None:
                    per_assignee_action_item.recurrenceInstance.externalId = (
                        self._id_generator.next_id(EntitiesEnum.RecurrenceInstance)
                    )
                if per_assignee_action_item.approvalWorkflow is not None:
                    per_assignee_action_item.approvalWorkflow.externalId = (
                        self._id_generator.next_id(EntitiesEnum.ApprovalWorkflow)
                    )
                    for step in per_assignee_action_item.approvalWorkflow.steps:
                        step.externalId = self._id_generator.next_id(
                            EntitiesEnum.ApprovalWorkflowStep,
                        )
                        step.approvalWorkflow.externalId = (
                            per_assignee_action_item.approvalWorkflow.externalId
                        )
                if per_assignee_action_item.links is not None:
                    for link in per_assignee_action_item.links:
                        link.externalId = self._id_generator.next_id(
                            EntitiesEnum.ActionItemLink,
                        )
                        link.action.externalId = per_assignee_external_id

                if per_assignee_action_item.metadatas is not None:
                    for metadata in per_assignee_action_item.metadatas:
                        metadata.externalId = self._id_generator.next_id(
                            EntitiesEnum.MetadataField,
                        )
                        metadata.action.externalId = per_assignee_external_id

                self._proccess_attachments(attachments, per_assignee_action_item)
                actions_by_event.append(
                    NodeReference(
                        externalId=per_assignee_action_item.externalId,
                        space=per_assignee_action_item.space,
                    ),
                )

                self.action_items.append(per_assignee_action_item)

        for source_event in self.source_event_list:
            if source_event.externalId == source_event_external_id:
                if source_event.actions is None:
                    source_event.actions = []
                if actions_by_event:
                    source_event.actions.extend(actions_by_event)
                    source_event.actionsCount += len(actions_by_event)
                break

    @staticmethod
    def _proccess_attachments(
        attachments: list[Attachment],
        action_item: ActionItem,
    ) -> None:
        if attachments is None or len(attachments) == 0:
            return

        action_item.attachments = []
        for attachment in attachments or []:
            attachment.action_item_external_ids.append(action_item.externalId)
            action_item.attachments.append(attachment.target_external_id)

    def _get_approval_workflow(
        self,
        approver_id: str | None,
        verifier_id: str | None,
        created_by_id: str,
        aim_space: str,
    ) -> ApprovalWorkflow | None:
        if approver_id is None and verifier_id is None:
            return None

        apw_ref_space = self._settings.approval_workflow_ref_instances_space
        uh_space = self._settings.user_management_instances_space

        created_by_id, approver_id, verifier_id = map(
            get_user_id_from_user_azure_attribute_id,
            [created_by_id, approver_id, verifier_id],
        )

        approval_id = self._id_generator.next_id(EntitiesEnum.ApprovalWorkflow)

        approval_step = (
            ApprovalWorkflowStep(
                externalId=self._id_generator.next_id(
                    EntitiesEnum.ApprovalWorkflowStep,
                ),
                space=aim_space,
                approvalWorkflow=NodeReference(
                    externalId=approval_id,
                    space=aim_space,
                ),
                step=1,
                startDate=self._now_date,
                description=ApprovalWorkflowStepDescriptionEnum.APPROVAL.value,
                status=NodeReference(
                    externalId=ApprovalWorkflowStepStatusEnum.PENDING.value,
                    space=apw_ref_space,
                ),
                approvalCondition=NodeReference(
                    externalId=ApprovalWorkflowCondition.AND.value,
                    space=apw_ref_space,
                ),
                users=[NodeReference(externalId=approver_id, space=uh_space)],
                approvalWorkflowConsentType=NodeReference(
                    externalId=ApprovalWorkflowConsentType.USER.value,
                    space=apw_ref_space,
                ),
            )
            if approver_id is not None
            else None
        )

        verifier_step = (
            ApprovalWorkflowStep(
                externalId=self._id_generator.next_id(
                    EntitiesEnum.ApprovalWorkflowStep,
                ),
                space=aim_space,
                approvalWorkflow=NodeReference(
                    externalId=approval_id,
                    space=aim_space,
                ),
                step=2 if approver_id is not None else 1,
                startDate=self._now_date,
                description=ApprovalWorkflowStepDescriptionEnum.VERIFICATION.value,
                status=NodeReference(
                    externalId=ApprovalWorkflowStepStatusEnum.PENDING.value,
                    space=apw_ref_space,
                ),
                approvalCondition=NodeReference(
                    externalId=ApprovalWorkflowCondition.AND.value,
                    space=apw_ref_space,
                ),
                users=[
                    NodeReference(
                        externalId=verifier_id,
                        space=uh_space,
                    ),
                ],
                approvalWorkflowConsentType=NodeReference(
                    externalId=ApprovalWorkflowConsentType.USER.value,
                    space=apw_ref_space,
                ),
            )
            if verifier_id is not None
            else None
        )

        return ApprovalWorkflow(
            externalId=approval_id,
            space=aim_space,
            currentStep=1,
            startDate=self._now_date,
            createdBy=NodeReference(
                externalId=created_by_id,
                space=uh_space,
            ),
            status=NodeReference(
                externalId=ApprovalWorkflowStepStatusEnum.PENDING.value,
                space=apw_ref_space,
            ),
            steps=[
                approval_step or verifier_step,
            ]
            + (
                [verifier_step]
                if approval_step is not None and verifier_step is not None
                else []
            ),
        )

    def _get_source_information(
        self,
        item_request: UpsertActionItemRequest,
    ) -> str | None:
        if item_request.application_id == ApplicationEnum.AIM.value:
            if item_request.source_information:
                return item_request.source_information
            if item_request.action_item_kind_id == ActionItemKind.RECURRING:
                return item_request.title
            return None
        if item_request.source_id:
            return item_request.source_id
        return item_request.source_information

    def _get_process_source_information(
        self,
        source_information: str | None,
    ) -> str | None:
        if source_information is None:
            return None
        return source_information.lower().replace(" ", "")

    def _get_dict_to_source_event(self, data: dict[str, Any]) -> SourceEvent:
        return SourceEvent(
            externalId=data.get("externalId"),
            space=data.get("space"),
            actionsCount=data.get("actionsCount"),
            sourceInformation=data.get("sourceInformation"),
            title=data.get("title"),
            views=data.get("views"),
        )

    async def _get_source_event(
        self,
        source_information: str | None,
        spaces: list[str],
        external_id: str | None,
    ) -> SourceEvent | None:
        if source_information is None:
            return None

        source_event_filter = {
            "and": [
                (
                    {
                        "externalId": {
                            "eq": external_id,
                        },
                    }
                    if external_id
                    else {
                        "sourceInformation": {
                            "eq": source_information,
                        },
                    }
                ),
                {
                    "space": {
                        "in": spaces,
                    },
                },
            ],
        }
        source_events = await self._graphql_service.get_all_results_list(
            generate_query(
                get_scheduled_source_events_query_list_name,
                get_scheduled_source_events_query_selection,
            ),
            get_scheduled_source_events_query_list_name,
            source_event_filter,
        )

        if len(source_events) == 0:
            return None

        return self._get_dict_to_source_event(source_events[0])

    def _get_source_event_exists(
        self,
        event: SourceEvent,
        event_list: list[SourceEvent],
    ) -> bool:
        for e in event_list:
            if e.externalId == event.externalId and e.space == event.space:
                return True
        return False

    async def _get_user_by_user_azure_attribute_id(
        self,
        external_id: str,
    ) -> Optional[dict[str, Any]]:
        query_filter = {"externalId": {"eq": external_id}}

        user_azure_attributes = await self._graphql_service.get_all_results_list(
            generate_query(
                get_user_azure_attribute_query_list_name,
                get_user_azure_attribute_query_selection,
            ),
            get_user_azure_attribute_query_list_name,
            query_filter,
        )

        if not user_azure_attributes:
            return {}

        return user_azure_attributes[0].get("user")

    def _get_parsed_requests(
        self,
        raw_requests: list[dict],
    ) -> tuple[list[UpsertActionItemRequest], SortMapper]:
        parsed_requests: list[UpsertActionItemRequest] = []

        user_azure_attribute_external_ids: list[str] = []
        application_external_ids: list[str] = []
        reporting_unit_external_ids: list[str] = []
        reporting_location_external_ids: list[str] = []
        category_external_ids: list[str] = []
        sub_category_external_ids: list[str] = []
        site_specific_category_instances: list[tuple[str, str]] = []

        for raw_request in raw_requests:
            event_id = raw_request.get("eventId") or str(uuid.uuid4())
            self.errors_by_event_id[event_id] = []
            self._log.info(f"Processing action item request from event {event_id}.")

            parsed_request: UpsertActionItemRequest
            try:
                self._log.info(f"Parsing action item request from event {event_id}.")
                parsed_request = UpsertActionItemRequest(
                    **raw_request,
                    _event_id=event_id,
                )
                parsed_request.assigned_to_ids = list(
                    set(parsed_request.assigned_to_ids),
                )
                parsed_request._event_id = event_id
                parsed_requests.append(parsed_request)

                user_azure_attribute_external_ids.extend(
                    externalId
                    for externalId in [
                        *parsed_request.assigned_to_ids,
                        parsed_request.owner_id,
                        parsed_request.created_by_id,
                        parsed_request.approver_id,
                        parsed_request.verifier_id,
                    ]
                    if externalId is not None
                )
                application_external_ids.append(parsed_request.application_id)

                if parsed_request.reporting_unit_id is not None:
                    reporting_unit_external_ids.append(parsed_request.reporting_unit_id)

                if parsed_request.reporting_location_id is not None:
                    reporting_location_external_ids.append(
                        parsed_request.reporting_location_id,
                    )

                category_external_ids.append(parsed_request.category_id)
                if parsed_request.sub_category_id is not None:
                    sub_category_external_ids.append(parsed_request.sub_category_id)

                if (
                    parsed_request.site_specific_category_id is not None
                    and parsed_request.site_specific_category_id != ""
                ):
                    site_specific_category_instances.append(
                        (
                            get_transactional_space_from_site_id(
                                parsed_request.reporting_site_id,
                            ),
                            parsed_request.site_specific_category_id,
                        ),
                    )

            except ValidationError as err:
                msg = f"Invalid action item request from EventId: {event_id}. Error: {err}. Skipping."
                self._log.error(msg)
                self.errors_by_event_id[event_id].append(msg)
                continue

        return parsed_requests, self._action_item_client.sort_mapper.get_sort_mapper(
            GetSortMapperRequest(
                user_azure_attribute_external_ids=user_azure_attribute_external_ids,
                application_external_ids=application_external_ids,
                reporting_unit_external_ids=reporting_unit_external_ids,
                reporting_location_external_ids=reporting_location_external_ids,
                category_external_ids=category_external_ids,
                sub_category_external_ids=sub_category_external_ids,
                site_specific_category_instances=site_specific_category_instances,
            ),
        )


get_scheduled_source_events_query_list_name = "listSourceEvent"

get_scheduled_source_events_query_selection = """
    externalId
    space
    actionsCount
    sourceInformation
    title
    views
"""

get_user_azure_attribute_query_list_name = "listUserAzureAttribute"

get_user_azure_attribute_query_selection = """
    externalId
    user {
        displayName
        firstName
        lastName
        email
    }
"""
