from cognite.client.data_classes.data_modeling.instances import NodeListWithCursor
from cognite.client.data_classes.data_modeling.query import (
    EdgeResultSetExpression,
    NodeResultSetExpression,
    Query,
    Select,
    SourceSelector,
)
from cognite.client.data_classes.filters import And, Equals, Filter, In, SpaceFilter

from clients.core.cognite_mappers import CogniteDependencyMapper
from clients.core.constants import L<PERSON><PERSON>, SEARCH_LIMIT, DataModelSpaceEnum, ViewEnum
from clients.core.models import ServiceParams
from clients.functional_location.models import FunctionalLocationResult
from clients.functional_location.requests import GetFunctionalLocationRequest


class FunctionalLocationClient:
    """Client for retrieving functional location data from the Cognite Data Model."""

    def __init__(self, params: ServiceParams) -> None:
        """
        Initialize the FunctionalLocationClient with required service parameters.

        Args:
            params (ServiceParams): Configuration object containing the Cognite client instance,
                data model ID, view mappings, and logging utilities.

        """
        self._cognite_client = params.cognite_client
        self._data_model_id = params.data_model.as_id()
        self._functional_location_view = params.get_views()[
            ViewEnum.FUNCTIONAL_LOCATION
        ]
        self.log = params.logging

    def get_functional_locations(
        self,
        request: GetFunctionalLocationRequest,
    ) -> list[FunctionalLocationResult]:
        """
        Retrieve functional location nodes related to the specified reporting units.

        Args:
            request (GetFunctionalLocationRequest): Request object containing filters,
                including one or more reporting unit external IDs.

        Returns:
            list[FunctionalLocationResult]: A list of functional location results matching the request.

        """
        if request.search:
            request.external_ids = self._handle_search(request)

        select_clause = self._build_select_clause()
        with_clause = self._build_with_clause(request)

        select_clause_dict = {
            "functional_locations": select_clause,
        }

        all_data: NodeListWithCursor | None = None
        cursors = None

        while True:
            res = self._cognite_client.data_modeling.instances.query(
                Query(with_=with_clause, select=select_clause_dict, cursors=cursors),
            )

            functional_locations = res.get_nodes("functional_locations")

            if all_data is None:
                all_data = functional_locations
            else:
                all_data.extend(functional_locations)

            if (
                not functional_locations
                or len(functional_locations) < LIMIT
                or not res.cursors.get("functional_locations")
            ):
                break

            cursors = res.cursors

        return list(FunctionalLocationResult.from_node_list(all_data))

    def _handle_search(self, request: GetFunctionalLocationRequest) -> list[str]:
        """
        Perform a text-based search on functional location instances using the search term and search properties provided in the request.

        Args:
            request (GetFunctionalLocationRequest): The request object containing the search term
                and properties to be used for filtering.

        Returns:
            list[str]: A list of external IDs for the functional location instances that match the search.
                       If no matches are found, returns a list with a placeholder ["-"].

        """
        functional_locations = self._cognite_client.data_modeling.instances.search(
            view=self._functional_location_view,
            query=request.search or "",
            filter=SpaceFilter(request.spaces),
            properties=request.search_properties,
            limit=SEARCH_LIMIT,
        )

        normalized_search_term = (request.search or "").lower()
        matching_equipment_ids: list[str] = [
            floc["externalId"]
            for floc in CogniteDependencyMapper.map(functional_locations)
            if any(
                normalized_search_term in str(floc[prop]).lower()
                for prop in request.search_properties
                if prop in floc and isinstance(floc[prop], str)
            )
        ]

        return matching_equipment_ids or ["-"]

    def _build_with_clause(self, request: GetFunctionalLocationRequest) -> dict:
        """
        Build the WITH clause of the data model query, defining traversal logic from reporting units to associated functional locations.

        Args:
            request (GetFunctionalLocationRequest): Request containing a list of reporting unit external IDs.

        Returns:
            dict: A dictionary representing the traversal path from reporting units
                to functional locations via a specific edge type.

        """

        def _build_functional_location_filters() -> list[Filter]:
            filters: list[Filter] = [SpaceFilter(request.spaces)]
            if request.external_ids:
                filters.append(In(["node", "externalId"], request.external_ids))
            return filters

        functional_location_filters = _build_functional_location_filters()

        if not request.reporting_unit_external_ids:
            return {
                "functional_locations": NodeResultSetExpression(
                    filter=And(*functional_location_filters),
                    limit=LIMIT,
                ),
            }

        reporting_unit_filter = In(
            ["node", "externalId"],
            request.reporting_unit_external_ids,
        )

        return {
            "reporting_units": NodeResultSetExpression(
                filter=reporting_unit_filter,
                limit=LIMIT,
            ),
            "reporting_units_to_functional_locations": EdgeResultSetExpression(
                from_="reporting_units",
                filter=Equals(
                    ["edge", "type"],
                    {
                        "externalId": "ReportingUnit.refersTo",
                        "space": DataModelSpaceEnum.ASSET_HIERARCHY_DATA_MODEL_SPACE,
                    },
                ),
                limit=LIMIT,
            ),
            "functional_locations": NodeResultSetExpression(
                from_="reporting_units_to_functional_locations",
                filter=And(*functional_location_filters),
                limit=LIMIT,
            ),
        }

    def _build_select_clause(self) -> Select:
        """
        Build the SELECT clause of the data model query, specifying which properties of functional locations to retrieve.

        Returns:
            Select: A select expression targeting the configured functional location view
                and its 'name' property.

        """
        return Select(
            sources=[
                SourceSelector(
                    source=self._functional_location_view,
                    properties=["name"],
                ),
            ],
        )
