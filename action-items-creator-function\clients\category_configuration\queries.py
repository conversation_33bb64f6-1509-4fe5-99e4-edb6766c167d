GET_CATEGORY_CONFIGURATION_BY_FILTER_QUERY = """
query GetCategoriesConfiguration(
  $cursor: String,
  $pageSize: Int,
  $categoriesConfigurationFilter: _ListCategoriesConfigurationFilter,
) {
  listCategoriesConfiguration(
    after: $cursor
    first: $pageSize
    filter: $categoriesConfigurationFilter
  ) {
    items {
      externalId
      space
      category {
        externalId
        space
        name
      }
      actionItemSubCategory {
        externalId
        space
        name
      }
      siteSpecificCategory {
        externalId
        space
        name
      }
      defaultApprovalRole {
        externalId
        space
        userRoleSites {
          items {
            externalId
            space
            usersComplements(first: 1000) {
              items {
                externalId
                space
                reportingUnits {
                  items {
                    externalId
                    space
                  }
                }
                userAzureAttribute {
                  externalId
                  space
                  user {
                    externalId
                    space
                    lastName
                    firstName
                    email
                  }
                }
              }
            }
          }
        }
      }
      defaultApprovalUser {
        externalId
        space
        lastName
        firstName
        email
      }
      defaultVerificationRole {
        externalId
        space
        userRoleSites {
          items {
            externalId
            space
            usersComplements(first: 1000) {
              items {
                externalId
                space
                reportingUnits {
                  items {
                    externalId
                    space
                  }
                }
                userAzureAttribute {
                  externalId
                  space
                  user {
                    externalId
                    space
                    lastName
                    firstName
                    email
                  }
                }
              }
            }
          }
        }
      }
      defaultVerificationUser {
        externalId
        space
        lastName
        firstName
        email
      }
      defaultExtensionApproverRole {
        externalId
        space
      }
      daysToApproval
      daysToVerification
      daysFromAssignedDate
      attachmentRequired
      isApprovalRequired
      isExtensionsAllowed
      isVerificationRequired
      isExtensionAttachmentRequired
      hasEmailNotification
    }
    pageInfo {
      endCursor
      hasNextPage
    }
  }
}
"""

GET_CATEGORY_BY_FILTER = """
query GetCategory($filter: _ListActionItemCategoryFilter, $sort: _ActionItemCategorySort!) {
  listActionItemCategory(filter: $filter, first: 1000, sort: [$sort]) {
    items {
      externalId
      name
      space
    }
  }
}
"""

GET_SUB_CATEGORY_BY_FILTER = """
query GetSubCategory($filter: _ListActionItemSubCategoryFilter, $sort: _ActionItemSubCategorySort!) {
  listActionItemSubCategory(filter: $filter, first: 1000, sort: [$sort]) {
    items {
      externalId
      space
      name
    }
  }
}
"""

GET_SITE_SPECIFIC_CATEGORY_BY_FILTER = """
query GetSiteSpecificCategory($filter: _ListSiteSpecificCategoryFilter, $sort: _SiteSpecificCategorySort!) {
  listSiteSpecificCategory(filter: $filter, first: 1000, sort: [$sort]) {
    items {
      externalId
      space
      name
      reportingSite {
        externalId
        space
        siteCode
      }
    }
  }
}
"""
