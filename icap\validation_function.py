from cognite.client import CogniteClient
from internal.infra import get_cognite_client, get_config
from internal.services import TeamsNotificationService, ValidationService


def handle(
    client: CogniteClient | None = None,
):
    config = get_config()
    client = client or get_cognite_client(config)
    print(client.config)

    nofication_service = TeamsNotificationService(config.webhook_uri)

    service = ValidationService(client, nofication_service)
    service.validate()
