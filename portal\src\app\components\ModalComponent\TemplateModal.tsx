import { MIN_TEXT_FIELD, MAX_TITLE_TEXT_TEMPLATE_FIELD } from '@/app/common/utils'
import AuthGuardWrapper from '@/app/common/wrapper/AuthGuardWrapper'
import { ClnButton, ClnTextField } from '@celanese/ui-lib'
import { zodResolver } from '@hookform/resolvers/zod'
import { Modal, Box, RadioGroup, FormControlLabel, Radio, Typography } from '@mui/material'
import { useContext, useEffect, useMemo, useState } from 'react'
import { Controller, useForm } from 'react-hook-form'
import { z } from 'zod'
import { UserManagementContext, UserManagementContextState } from '@celanese/celanese-ui'
import { UserSearch } from '../UserComponent/UserSearch/userSearch'
import { TemplateConfiguration } from '@/app/common/models/action'
import { Role } from '@/app/common/models/common/user-management/role'
import { User } from '@/app/common/models/common/user-management/user'
import GenericFieldTitle from '../FieldsComponent/GenericFieldTitle'
import { useSnackbar } from '@/app/common/contexts/SnackbarContext'
import { translate } from '@/app/common/utils/generate-translate'
import { useLoading } from '@/app/common/contexts/LoadingContext'

type TemplateModalProps = {
    siteId?: string
    open: boolean
    actionsTemplateName: string[]
    loadingTemplate: boolean
    handleClose: () => void
    createFunction: (x: any) => void
    modalTitle?: string
    isEdit?: boolean
    templateToEdit?: TemplateConfiguration | null
    users?: User[]
    roles?: Role[]
}

const style = {
    position: 'absolute' as 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    width: '50vw',
    bgcolor: 'background.paper',
    boxShadow: 24,
    p: 4,
}

const formSchema = z.object({
    name: z.string().min(MIN_TEXT_FIELD).max(MAX_TITLE_TEXT_TEMPLATE_FIELD),
    users: z.array(z.string()),
    roles: z.array(z.string()),
    externalId: z.string(),
})

type RequestSchema = z.infer<typeof formSchema>

export default function TemplateModal({
    siteId,
    open,
    actionsTemplateName,
    loadingTemplate,
    handleClose,
    createFunction,
    modalTitle,
    isEdit = false,
    templateToEdit,
}: TemplateModalProps) {
    const { userInfo: activeUser } = useContext<UserManagementContextState>(UserManagementContext)
    const activeUserExternalId = activeUser.externalId

    const { showSnackbar } = useSnackbar()
    const { showLoading } = useLoading()

    const actionsList = useMemo(() => {
        if (actionsTemplateName.length == 0) {
            return []
        }

        return actionsTemplateName
    }, [actionsTemplateName])

    const [usersParam, setUsersParam] = useState<string>('NULL_PARAM')
    const [rolesParam, setRolesParam] = useState<string>('NULL_PARAM')

    const [saveFor, setSaveFor] = useState('person')
    const handleSaveFor = (event: any) => {
        const saveFor = event.target.value as string
        setSaveFor(saveFor)
        setUsersParam('NULL_PARAM')
        setRolesParam('NULL_PARAM')
    }

    const [selectedUsers, setSelectedUsers] = useState<User[]>([])
    const [selectedRoles, setSelectedRoles] = useState<Role[]>([])

    const [changeName, setChangeName] = useState<boolean>(false)

    const [originalUsers, setOriginalUsers] = useState<User[]>([])
    const [originalRoles, setOriginalRoles] = useState<Role[]>([])

    useEffect(() => {
        if (templateToEdit) {
            setValue('name', templateToEdit.name ?? '')
            setValue(
                'users',
                templateToEdit.users.items.map((item) => item.externalId)
            )
            setValue(
                'roles',
                templateToEdit.roles.items.map((item) => item.externalId)
            )
            setOriginalUsers(templateToEdit.users.items)
            setOriginalRoles(templateToEdit.roles.items)
            setSelectedUsers(templateToEdit.users?.items || [])
            setSelectedRoles(templateToEdit.roles?.items || [])
        }
    }, [templateToEdit])

    useEffect(() => {
        if (activeUser && activeUserExternalId && !isEdit) {
            setValue('users', [activeUserExternalId])
        }
    }, [activeUser, activeUserExternalId])

    const changeListUser = (listUsers: string[]) => {
        setValue('users', listUsers)
    }

    const changeListRole = (listRole: string[]) => {
        setValue('roles', listRole)
    }

    const {
        control,
        getValues,
        setValue,
        formState: { errors },
    } = useForm<RequestSchema>({
        resolver: zodResolver(formSchema),
        defaultValues: {
            name: '',
            users: [],
            roles: [],
            externalId: '',
        },
    })

    const closeModal = () => {
        setSelectedUsers(originalUsers)
        setSelectedRoles(originalRoles)

        setChangeName(false)
        handleClose()
    }

    const handleAlert = (message: string, error: boolean = false) => {
        showSnackbar(message, error ? 'error' : 'success', 'template-modal')
    }

    const handleSave: () => Promise<void> = async () => {
        const form = getValues()

        if (!form.name || form.name.length > MAX_TITLE_TEXT_TEMPLATE_FIELD) {
            handleAlert(translate('stepper.form.requiredFieldsErro'), true)
            setChangeName(true)
            return
        }

        if (actionsList.includes(form.name) && !isEdit) {
            handleAlert(translate('alerts.errorOcurred'), true)
            return
        }

        if (form.roles.length === 0 && form.users.length === 0) {
            handleAlert(translate('stepper.form.requiredFieldsErro'), true)
            return
        }

        const previousUsers = originalUsers.map((user) => user.externalId)
        const previousRoles = originalRoles.map((role) => role.externalId)

        if (isEdit) {
            createFunction({
                users: form.users,
                roles: form.roles,
                originalUsers: previousUsers,
                originalRoles: previousRoles,
            })
        } else {
            createFunction({
                name: form.name,
                users: form.users,
                roles: form.roles,
            })
        }

        handleClose()
    }

    const isDisable =
        selectedUsers.length === originalUsers.length &&
        selectedUsers.every((user, index) => user.externalId === originalUsers[index].externalId) &&
        selectedRoles.length === originalRoles.length &&
        selectedRoles.every((role, index) => role.externalId === originalRoles[index].externalId)

    useEffect(() => {
        showLoading(loadingTemplate)
    }, [loadingTemplate])

    return (
        <AuthGuardWrapper componentName={TemplateModal.name}>
            <Modal open={open} onClose={closeModal}>
                <Box sx={style}>
                    <Typography variant="h3" sx={{ fontWeight: 'bold', textAlign: 'center' }}>
                        {modalTitle ? modalTitle : translate('stepper.saveTemplate')}
                    </Typography>
                    <Box
                        sx={{
                            width: '100%',
                            display: 'flex',
                            flexDirection: 'column',
                            alignItems: 'center',
                            justifyContent: 'center',
                            marginTop: '1rem',
                            gap: '1rem',
                            '& .MuiFormControl-root': { maxWidth: '100% !important', width: '100% !important' },
                            '& > *': { width: '100% !important' },
                        }}
                    >
                        <Controller
                            control={control}
                            name="name"
                            render={({ field: { value, onChange } }) => (
                                <ClnTextField
                                    onChange={(e) => {
                                        onChange(e.target.value)
                                    }}
                                    required
                                    value={getValues('name')}
                                    label={translate('stepper.form.template.templateName')}
                                    variant="outlined"
                                    helperText={!isEdit && translate('stepper.form.helperTextTemplateNameSaveTemplate')}
                                    disabled={isEdit}
                                    error={
                                        changeName &&
                                        (getValues('name').length < MIN_TEXT_FIELD ||
                                            getValues('name').length > MAX_TITLE_TEXT_TEMPLATE_FIELD)
                                    }
                                    data-test="new_action_item_flow_3_save_as_template_modal-template_name_field"
                                    data-origin="ui-lib"
                                />
                            )}
                        />
                        <RadioGroup
                            row
                            aria-labelledby="demo-row-radio-buttons-group-label"
                            name="row-radio-buttons-group"
                            value={saveFor}
                            onChange={handleSaveFor}
                        >
                            <FormControlLabel
                                value={'person'}
                                control={<Radio />}
                                label={translate('stepper.form.template.person')}
                                data-test="new_action_item_flow_3_save_as_template_modal-person_radio"
                                data-origin="aim"
                            />
                            <FormControlLabel
                                value={'role'}
                                control={<Radio />}
                                label={translate('stepper.form.template.role')}
                                data-test="new_action_item_flow_3_save_as_template_modal-role_radio"
                                data-origin="aim"
                            />
                        </RadioGroup>
                        {saveFor === 'person' ? (
                            <>
                                <Typography sx={{ fontWeight: 'bold' }}>
                                    {translate('stepper.form.template.selectName')}
                                </Typography>
                                <UserSearch
                                    siteId={siteId}
                                    selectedUsers={selectedUsers}
                                    setSelectedUsers={setSelectedUsers}
                                    changeListUser={changeListUser}
                                    hiddenEmptyTable
                                    hiddenHelperText
                                    maxHeight={150}
                                    searchDefault={usersParam}
                                    setSearchDefault={setUsersParam}
                                    dataTest="new_action_item_flow_3_save_as_template_modal-select_name_search_field"
                                    data-origin="aim"
                                />
                            </>
                        ) : (
                            <>
                                <Typography sx={{ fontWeight: 'bold' }}>
                                    {translate('stepper.form.template.selectRoles')}
                                </Typography>
                                <UserSearch
                                    siteId={siteId}
                                    getUsers={false}
                                    selectedRoles={selectedRoles}
                                    setSelectedRoles={setSelectedRoles}
                                    getRoles
                                    changeListRole={changeListRole}
                                    hiddenEmptyTable
                                    hiddenHelperText
                                    maxHeight={150}
                                    searchDefault={rolesParam}
                                    setSearchDefault={setRolesParam}
                                    dataTest="new_action_item_flow_3_save_as_template_modal-select_roles_search_field"
                                    data-origin="aim"
                                />
                            </>
                        )}
                        <GenericFieldTitle
                            isDetailsExternalId
                            fieldName={translate('stepper.form.helperTextAssignmentSaveTemplate')}
                        />
                    </Box>
                    <Box
                        sx={{
                            position: 'relative',
                            bottom: 0,
                            width: '100%',
                            display: 'flex',
                            justifyContent: 'space-between',
                            gap: '10px',
                            alignItems: 'baseline',
                            marginTop: '1.5rem',
                            zIndex: 1,
                        }}
                    >
                        <div>
                            <ClnButton
                                variant={'outlined'}
                                label={translate('requestModal.cancel')}
                                onClick={() => closeModal()}
                                sxProps={{
                                    width: '10rem',
                                    '@media (max-width:1024px)': {
                                        width: '5rem',
                                    },
                                }}
                                type="button"
                                data-test="new_action_item_flow_3_save_as_template_modal-cancel_button"
                                data-origin="ui-lib"
                            />
                        </div>
                        <div>
                            <ClnButton
                                variant={'contained'}
                                label={translate('stepper.form.save')}
                                onClick={() => {
                                    handleSave()
                                }}
                                sxProps={{
                                    width: '10rem',
                                    '@media (max-width:1024px)': {
                                        width: '5rem',
                                    },
                                }}
                                disabled={isDisable}
                                type="button"
                                data-test="new_action_item_flow_3_save_as_template_modal-save_button"
                                data-origin="ui-lib"
                            />
                        </div>
                    </Box>
                </Box>
            </Modal>
        </AuthGuardWrapper>
    )
}
