import { Box, CSSObject } from '@mui/material'
import { ModalWrapper } from '../ModalComponent/Modal/ModalWrapper'
import { ClnButton, ClnPanel } from '@celanese/ui-lib'
import { User } from '@/app/common/models/common/user-management/user'
import { UserSearch } from './UserSearch/userSearch'
import { Team } from '@/app/common/models/common/user-management/team'
import { Role } from '@/app/common/models/common/user-management/role'
import { useState } from 'react'
import { translate } from '@/app/common/utils/generate-translate'

export interface Viewers {
    users: User[]
    roles: Role[]
    teams: Team[]
}

interface PrivateSettingsModalProps {
    siteId: string
    onClose: () => void
    onSave: (value: Viewers) => void
    selectedUsersViewers: User[]
    selectedRolesViewers: Role[]
    selectedTeamsViewers: Team[]
    labelPrivate: 'Action' | 'Event'
}

export const tabItemsWrapper: CSSObject = {
    backgroundColor: 'primary.white',
    border: '1px solid',
    borderColor: 'otherColor.outlineBorder',
    borderRadius: '8px',
    flexGrow: '1',
    display: 'flex',
    flexDirection: 'column',
    padding: '16px 24px 16px 24px',
    marginBottom: '1rem',
    gap: '10px',
}

export function PrivateSettingsModal({
    siteId,
    selectedUsersViewers,
    selectedRolesViewers,
    selectedTeamsViewers,
    labelPrivate,
    onClose,
    onSave,
}: PrivateSettingsModalProps) {
    const [usersViewers, setUsersViewers] = useState<User[]>(selectedUsersViewers ?? [])
    const [rolesViewers, setRolesViewers] = useState<Role[]>(selectedRolesViewers ?? [])
    const [teamsViewers, setTeamsViewers] = useState<Team[]>(selectedTeamsViewers ?? [])

    const labelTitle = translate('privateComponent.privateSettingsTitle').replace(
        /\{\}/,
        translate(`privateComponent.${labelPrivate}`)
    )

    const labelButtonDelete = translate('privateComponent.deleteAllViewers').replace(
        /\{\}/,
        translate(`privateComponent.${labelPrivate}`)
    )

    const handleClearViewers = () => {
        setUsersViewers([])
        setRolesViewers([])
        setTeamsViewers([])
    }

    const handleSave = () => {
        onSave({
            users: usersViewers,
            roles: rolesViewers,
            teams: teamsViewers,
        })
    }

    return (
        <ModalWrapper
            title={labelTitle}
            openModal={true}
            closeModal={onClose}
            sxProps={{ backgroundColor: 'rgba(0, 0, 0, 0.5)' }}
            whiteBackground
            sxPropsTitle={{ color: 'primary.dark', fontSize: '19px', marginRight: 'auto' }}
            content={
                <Box
                    sx={{
                        padding: '0px 16px',
                        width: '80vw',
                        maxWidth: '600px',
                        '@media (max-width: 600px)': { width: '100%' },
                    }}
                >
                    <ClnPanel
                        sx={{
                            padding: '1rem',
                            display: 'flex',
                            flexDirection: 'column',
                            marginBottom: '1rem',
                        }}
                    >
                        <UserSearch
                            siteId={siteId}
                            maxHeight={250}
                            subHeader={translate('stepper.form.addViewers')}
                            selectedUsers={usersViewers}
                            setSelectedUsers={setUsersViewers}
                            selectedRoles={rolesViewers}
                            setSelectedRoles={setRolesViewers}
                            selectedTeams={teamsViewers}
                            setSelectedTeams={setTeamsViewers}
                            hiddenEmptyTable
                            isFilter
                            isSearch
                            getRoles
                            getTeams
                            dataTest="new_action_item_flow_3_private_event_modal-add_viewers_field"
                        ></UserSearch>
                        <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: '8px', marginTop: '10px' }}>
                            <ClnButton
                                variant="text"
                                label={translate('stepper.form.cancel')}
                                onClick={onClose}
                                data-test="new_action_item_flow_3_private_event_modal-cancel_button"
                                data-origin="ui-lib"
                            />
                            <ClnButton
                                variant="outlined"
                                label={labelButtonDelete}
                                color="error"
                                onClick={handleClearViewers}
                                data-test="new_action_item_flow_3_private_event_modal-delete_all_event_viewers_button"
                                data-origin="aim"
                            />
                            <ClnButton
                                variant="contained"
                                label={translate('requestModal.save')}
                                onClick={handleSave}
                                data-test="new_action_item_flow_3_private_event_modal-save_button"
                                data-origin="ui-lib"
                            />
                        </Box>
                    </ClnPanel>
                </Box>
            }
        />
    )
}
