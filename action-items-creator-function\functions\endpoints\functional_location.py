import json
import logging
from http import HTTPStatus

import azure.functions as func
from pydantic import ValidationError

from clients.functional_location.requests import GetFunctionalLocationRequest
from infra.action_item_client_factory import ActionItemClientFactory
from services.functional_location_service import FunctionalLocationService

bp = func.Blueprint()
logging.basicConfig(format="%(message)s", level=logging.INFO)


@bp.function_name(name="GetFunctionalLocations")
@bp.route(
    "get-functional-locations",
    methods=["get"],
    auth_level=func.AuthLevel.ANONYMOUS,
)
def main(req: func.HttpRequest) -> func.HttpResponse:
    """Handle GET requests to retrieve functional location details based on the provided request parameters."""
    try:
        request_param = req.params.get("functionalLocationRequest", "{}")
        request_data = json.loads(request_param)

        request = GetFunctionalLocationRequest.model_validate(request_data)
        service = FunctionalLocationService(ActionItemClientFactory.retriever())

        locations = service.get_functional_locations(request)

        result = [
            location.model_dump(mode="json", by_alias=True) for location in locations
        ]

        return func.HttpResponse(
            json.dumps(result, default=str),
            status_code=HTTPStatus.OK,
            mimetype="application/json",
        )

    except ValidationError as ve:
        logging.exception("Validation error")
        return func.HttpResponse(
            json.dumps({"error": "Invalid request data", "details": ve.errors()}),
            status_code=HTTPStatus.BAD_REQUEST,
            mimetype="application/json",
        )

    except Exception as e:
        logging.exception("Unhandled exception occurred")
        return func.HttpResponse(
            json.dumps({"error": "Internal server error", "details": str(e)}),
            status_code=HTTPStatus.INTERNAL_SERVER_ERROR,
            mimetype="application/json",
        )
