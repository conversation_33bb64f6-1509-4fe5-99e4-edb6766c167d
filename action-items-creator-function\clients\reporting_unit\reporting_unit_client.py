from typing import Optional

from clients.core.constants import AGGR<PERSON>ATE_LIMIT, DataSpaceEnum, ViewEnum
from clients.core.models import PaginatedData, ServiceParams
from clients.reporting_unit.models import ReportingUnitResult
from clients.reporting_unit.requests import (
    GetReportingUnitsBySitesRequest,
    GetReportingUnitsRequest,
)

from .queries import GET_REPORTING_UNIT_BY_FILTER, GET_SEARCH_REPORTING_UNIT_BY_FILTER


class ReportingUnitClient:
    """Client responsible for handling units and related views in the data model."""

    def __init__(
        self,
        params: ServiceParams,
    ) -> None:
        """Initialize the ReportingUnitClient with the required services and configuration."""
        self._cognite_client = params.cognite_client
        self._data_model_id = params.data_model.as_id()
        self._reporting_unit_view = params.get_views()[ViewEnum.REPORTING_UNIT]

    def get_reporting_units(
        self,
        request: GetReportingUnitsRequest,
    ) -> Optional[PaginatedData[ReportingUnitResult]]:
        """
        Retrieve reporting units based on the provided request.

        Filters the reporting units by site code, active status, and optional description.

        Args:
            request (GetReportingUnitsRequest): The request containing the filtering parameters.

        Returns:
            Optional[PaginatedData[ReportingUnitResult]]: A paginated list of reporting unit results,
                                                        or None if the query fails.

        """
        filters = [
            {"space": {"eq": DataSpaceEnum.REF_DATA_SPACE}},
            {"name": {"prefix": request.site_code}},
            {"isActive": {"eq": True}},
        ]

        if request.description:
            filters.append({"description": {"eq": request.description}})

        variables = {"filter": {"and": filters}, "sort": {"description": "ASC"}}

        try:
            result = self._cognite_client.data_modeling.graphql.query(
                id=self._data_model_id,
                query=GET_REPORTING_UNIT_BY_FILTER,
                variables=variables,
            )

            return PaginatedData[ReportingUnitResult].from_graphql_response(
                result,
                AGGREGATE_LIMIT,
            )
        except Exception:
            return None

    def get_reporting_units_by_sites(
        self,
        request: GetReportingUnitsBySitesRequest,
    ) -> Optional[PaginatedData[ReportingUnitResult]]:
        """
        Retrieve reporting units based on site codes and optional search filters.

        Filters the reporting units by space, site code prefix, active status, and optional search parameters.

        Args:
            request (GetReportingUnitsBySitesRequest): The request containing the site codes
                                                    and optional search parameters.

        Returns:
            Optional[PaginatedData[ReportingUnitResult]]: A paginated list of reporting unit results,
                                                        or None if the query fails.

        """
        filters = [
            {"space": {"eq": DataSpaceEnum.REF_DATA_SPACE}},
            {"or": [{"name": {"prefix": code}} for code in request.site_codes]},
            {"isActive": {"eq": True}},
        ]

        variables = {
            "query": request.search,
            "fields": request.search_properties,
            "filter": {"and": filters},
            "sort": {"description": "ASC"},
        }

        try:

            result = self._cognite_client.data_modeling.graphql.query(
                id=self._data_model_id,
                query=GET_SEARCH_REPORTING_UNIT_BY_FILTER,
                variables=variables,
            )

            return PaginatedData[ReportingUnitResult].from_graphql_response(
                result,
                AGGREGATE_LIMIT,
            )

        except Exception:
            return None
