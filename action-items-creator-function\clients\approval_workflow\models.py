from pydantic import Field

from clients.core.constants import ApprovalWorkflowStepDescriptionEnum
from clients.core.models import (
    BaseEntity,
    Node,
)


class ApprovalWorkflowStepUpdate(BaseEntity):
    approval_condition: Node | None = Field(default=None)
    approval_workflow_consent_type: Node | None = Field(default=None)
    approval_workflow: Node | None = Field(default=None)
    status: Node | None = Field(default=None)
    users: list[Node] | None = Field(default=None, exclude=True)

    step: int | None = Field(default=None)

    start_date: str | None = Field(default=None)
    end_date: str | None = Field(default=None)
    approval_date: str | None = Field(default=None)
    description: str | None = Field(default=None)

    def get_properties_to_include(self):
        properties = self.model_dump(
            by_alias=True,
            exclude={"external_id", "space"},
            exclude_none=True,
        )
        return list(properties.keys())


class ApprovalWorkflowUpdate(BaseEntity):
    status: Node | None
    created_by: Node | None = Field(default=None)

    current_step: int | None = Field(default=None)

    description: str | None = Field(default=None)
    start_date: str | None = Field(default=None)
    end_date: str | None = Field(default=None)

    steps: list[ApprovalWorkflowStepUpdate] | None = Field(default=None, exclude=True)

    def get_properties_to_include(self):
        properties = self.model_dump(
            by_alias=True,
            exclude={"external_id", "space"},
            exclude_none=True,
        )
        return list(properties.keys())

    def get_approver(self):
        return self._get_user_by_step_description(
            ApprovalWorkflowStepDescriptionEnum.APPROVAL.value
        )

    def get_verifier(self):
        return self._get_user_by_step_description(
            ApprovalWorkflowStepDescriptionEnum.VERIFICATION.value
        )

    def _get_user_by_step_description(self, description: str):
        return next(
            (
                step.users[0]
                for step in self.steps or []
                if step.users and step.description == description
            ),
            None,
        )
