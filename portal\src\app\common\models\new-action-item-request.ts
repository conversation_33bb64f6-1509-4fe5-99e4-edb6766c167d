import { ActionSourceTypeExternalIdEnumType } from '@/app/common/enums/ActionSourceTypeEnum'
import { ActionItemLink } from './link'

export type TaskTypeRelatedProps =
    | {
          assignedToId?: string
          assigneeIds?: string[]
          assignmentDate?: string | null
          dueDate?: string | null
      }
    | {
          recurrenceInstance: any
          assigneeIds?: string[]
      }

export type NewActionItemRequest = {
    eventId: string
    title: string
    timeZone: string
    categoryId: string
    subCategoryId: string
    siteSpecificCategoryId?: string
    ownerId: string
    createdById: string
    reportingUnitId: string
    actionItemKindId: string
    reportingLocationId?: string
    links?: ActionItemLink[]
    description: string
    attachmentIds?: string[]
    sourceInformation?: string
    approverId?: string
    verifierId?: string
    evidenceRequired: boolean
    priority?: string
    reportingLineId?: string
    voeActionItem?: string
    assignedToIds: string[]
    applicationId: string
    reportingSiteId: string
    sourceEventId?: string
    sourceTypeId: ActionSourceTypeExternalIdEnumType
    sourceId?: string
    private?: boolean
    viewUsers?: string[]
    viewRoles?: string[]
    viewTeams?: string[]
} & TaskTypeRelatedProps
