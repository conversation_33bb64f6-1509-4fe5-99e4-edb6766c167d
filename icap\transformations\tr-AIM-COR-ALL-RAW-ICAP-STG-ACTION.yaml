# Manifest file downloaded from fusion
externalId: tr-AIM-COR-ALL-RAW-ICAP-STG-ACTION
name: AIM-COR-ALL-RAW-ICAP-STG-ACTION
query: >-
  WITH from_tblActionItem AS (SELECT
    icap_action.key,
    aim_existing_action.externalId AS existing_external_id,
    if(
      coalesce(icap_event.`Private`, icap_action.`Private`),
      'AIM-COR-ALL-PROT',
      utl_reporting_site.aim_space
    ) AS space,
    utl_reporting_site.ah_reporting_site AS reportingSite,
    CASE
      WHEN icap_event.EventCategoryID = 15 THEN node_reference('UMG-COR-ALL-DAT', 'APP-MOCP')
      ELSE node_reference('UMG-COR-ALL-DAT', 'APP-ICAP')
    END AS application,
    cast(icap_action.`ActionDescription` AS STRING) AS title,
    split(cast(icap_action.`ActionStartDate` AS STRING), ' ')[0] AS assignmentDate,
    CASE
      WHEN icap_action.`ActionDueDate` IS NOT NULL THEN cast(split_part(cast(icap_action.`ActionDueDate` AS STRING), ' ', 1) AS DATE)
      WHEN icap_action.`ActionActualCompletionDate` IS NOT NULL THEN cast(split_part(cast(icap_action.`ActionActualCompletionDate` AS STRING), ' ', 1) AS DATE)
      WHEN icap_action.`ActionStatusID` IN (4, 5, 6) THEN cast(split_part(cast(icap_action.`ActionStartDate` AS STRING), ' ', 1) AS DATE)
      WHEN icap_action.`ActionStatusID` IN (1, 2, 3)  THEN cast('2100-01-01' AS DATE)
    END AS dueDate,
    CASE
      WHEN icap_action.`ActionDueDate` IS NOT NULL THEN cast(split_part(cast(icap_action.`ActionDueDate` AS STRING), ' ', 1) AS DATE)
      WHEN icap_action.`ActionActualCompletionDate` IS NOT NULL THEN cast(split_part(cast(icap_action.`ActionActualCompletionDate` AS STRING), ' ', 1) AS DATE)
      WHEN icap_action.`ActionStatusID` IN (4, 5, 6) THEN cast(split_part(cast(icap_action.`ActionStartDate` AS STRING), ' ', 1) AS DATE)
      WHEN icap_action.`ActionStatusID` IN (1, 2, 3)  THEN cast('2100-01-01' AS DATE)
    END AS displayDueDate,
    category_map.aim_category AS category,
    category_map.aim_subcategory AS subCategory,
    if(
        isnotnull(category_map.aim_site_category_external_id_pattern),
        node_reference(
            utl_reporting_site.aim_space,
            replace(
                category_map.aim_site_category_external_id_pattern,
                '<SITE>',
                utl_reporting_site.ah_site_code
            )
        ),
        NULL
    ) AS siteSpecificCategory,
    cast(icap_action.`Cause` AS STRING) AS description,
    node_reference('AIM-COR-ALL-REF', 'ACTK-oneTime') AS actionItemKind,
    utl_user_owner.user_azure_attribute AS createdBy,
    utl_user_owner.user_azure_attribute AS owner,
    coalesce(utl_user_assignee.user_azure_attribute, utl_user_secondary_assignee.user_azure_attribute) AS assignedTo,
    coalesce(aim_existing_action.sourceType, node_reference(utl_source_type.source_type.space, utl_source_type.source_type.externalId)) AS sourceType,
    concat(
      'ICAPContinuousMigration-',
      icap_action.ActionItemID,
      '-Event-',
      icap_action.`EventID`
    ) AS objectType,
    if(
      NOT icap_action.ActionActive,
      named_struct("externalId", 'ACTS-deleted', "space", 'AIM-COR-ALL-REF'),
      if(
        icap_moc.MOCStatusID = 11 AND icap_action.`ActionStatusID` NOT IN (5, 6),
        named_struct("externalId", 'ACTS-cancelled', "space", 'AIM-COR-ALL-REF'),
        utl_status.status
      )
    ) AS currentStatus,
    utl_reporting_unit.ah_reporting_unit AS reportingUnit,
    utl_reporting_unit.ah_reporting_location AS reportingLocation,
    utl_reporting_unit.ah_reporting_line AS reportingLine,
    utl_business_line.ah_business_line AS businessLine,
    icap_event.EventCategoryID IN (21, 27) AS viewOnly,
    icap_action_priority.`ActionPriority` AS priority,
    DATE(icap_action.ActionActualCompletionDate) AS conclusionDate,
    date_format(timestamp(ActionActualCompletionDate), "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'") AS completedDate,
    CASE
      WHEN icap_event.EventCategoryID = 15 THEN coalesce(concat('PSMC-', icap_moc.key), 'Not able to locate the respective MOC')
      WHEN icap_event.EventCategoryID = 21 THEN coalesce(icap_mooc.key, 'Not able to locate the respective MOOC')
      ELSE CAST(icap_action.`EventID` AS STRING)
    END AS sourceInformation,
    icap_action.`EvidenceRequired` AS evidenceRequired,
    coalesce(icap_event.`Private`, icap_action.`Private`) AS isPrivate,
    icap_action.`Comments` AS concatenated_comments,
    if(
      coalesce(icap_event.`Private`, icap_action.`Private`),
      array_except(
        concat(
          ifnull(grouped_event_viewer.viewer_external_ids, array()),
          ifnull(grouped_event_secondary_owner.secondary_owner_external_ids, array()),
          array(
            utl_user_owner.user_azure_attribute.externalId,
            utl_user_assignee.user_azure_attribute.externalId,
            utl_user_secondary_assignee.user_azure_attribute.externalId
          )
        ),
        array(NULL)
    ),
    NULL
    ) AS views
  FROM `ICAP-COR`.`AXN-tblActionItem` AS icap_action
  LEFT JOIN `ICAP-COR`.`EVNT-tblEvent` AS icap_event ON icap_action.`EventID` = icap_event.`EventID`
  LEFT JOIN `AIM-COR`.`ICAP-MAP-ReportingSite` AS utl_reporting_site ON utl_reporting_site.key = cast(icap_event.`SiteID` AS STRING)
  LEFT JOIN `AIM-COR`.`ICAP-MAP-ActionStatus` AS utl_status ON utl_status.key = cast(icap_action.`ActionStatusID` AS STRING)
  LEFT JOIN `AIM-COR`.`ICAP-MAP-User` AS utl_user_owner ON utl_user_owner.key = cast(icap_event.`EventAddedByOwner` AS STRING)
  LEFT JOIN `AIM-COR`.`ICAP-MAP-Category` category_map ON (
    (category_map.icap_action_category_id = icap_action.`ActionCategoryID` AND NOT icap_event.SiteID = 3)
    OR (category_map.icap_event_category_id = icap_event.`EventCategoryID` AND icap_event.SiteID = 3)
  )
  LEFT JOIN `AIM-COR`.`ICAP-MAP-User` AS utl_user_assignee ON utl_user_assignee.key = cast(icap_action.`PPRID` AS STRING)
  LEFT JOIN `AIM-COR`.`ICAP-MAP-User` AS utl_user_secondary_assignee ON utl_user_secondary_assignee.key = cast(icap_action.`SecondaryPPRID` AS STRING)
  LEFT JOIN `AIM-COR`.`ICAP-MAP-SourceType` AS utl_source_type ON utl_source_type.key = cast(icap_event.`EventCategoryID` AS STRING)
  LEFT JOIN `ICAP-COR`.`AXN-tblActionPriority` AS icap_action_priority ON icap_action.`ActionPriorityID` = icap_action_priority.`ActionPriorityID`
  LEFT JOIN `ICAP-COR`.`UNT-tblUnit` AS icap_unit ON icap_unit.key = cast(icap_event.`UnitID` AS STRING) AND icap_event.`SiteID` = icap_unit.`SiteID`
  LEFT JOIN `AIM-COR`.`ICAP-MAP-ReportingUnit&Location` AS utl_reporting_unit ON utl_reporting_unit.key = icap_unit.key
  LEFT JOIN `AIM-COR`.`ICAP-MAP-BusinessLine` AS utl_business_line ON utl_business_line.key = cast(icap_event.BusinessLineID AS STRING)
  LEFT JOIN `ICAP-COR`.`MOC-tblMOC` AS icap_moc ON icap_action.EventID = icap_moc.EventID
  LEFT JOIN `ICAP-COR`.`MOOC-tblMOOC` AS icap_mooc ON icap_action.EventID = icap_mooc.EventID
  LEFT JOIN cdf_data_models("AIM-COR-ALL-DMD", "ActionItemManagementDOM", "6_0_0", "Action") AS aim_existing_action ON (
    split_part(aim_existing_action.objectType, '-', 2) = cast(icap_action.ActionItemID AS STRING)
    AND startswith(aim_existing_action.objectType, 'ICAP')
    AND split_part(aim_existing_action.objectType, '-', -2) = 'Event'
  )
  LEFT JOIN (
    SELECT
      icap_event_viewer.EventID,
      array_agg(utl_user_viewer.user_azure_attribute.externalId) AS viewer_external_ids
    FROM `ICAP-COR`.`EVNT-tblEventViewer` icap_event_viewer
    INNER JOIN `AIM-COR`.`ICAP-MAP-User` utl_user_viewer ON utl_user_viewer.key = cast(icap_event_viewer.ViewerID AS STRING)
    GROUP BY icap_event_viewer.EventID
  ) AS grouped_event_viewer ON grouped_event_viewer.EventID = icap_action.EventID
  LEFT JOIN (
    SELECT
      icap_secondary_owner.EventID,
      array_agg(utl_user_secondary_owner.user_azure_attribute.externalId) AS secondary_owner_external_ids
    FROM `ICAP-COR`.`EVNT-tblEventSecondaryOwners` icap_secondary_owner
    INNER JOIN `AIM-COR`.`ICAP-MAP-User` utl_user_secondary_owner ON utl_user_secondary_owner.key = cast(icap_secondary_owner.OwnerID AS STRING)
    GROUP BY icap_secondary_owner.EventID
  ) AS grouped_event_secondary_owner ON grouped_event_secondary_owner.EventID = icap_action.EventID
  ),

  from_tblRCACPGRActionItem AS (
  SELECT
    concat('RCAC-',icap_action.key) as key,
    aim_existing_action.externalId AS existing_external_id,
    utl_reporting_site.aim_space AS space,
    utl_reporting_site.ah_reporting_site AS reportingSite,
    node_reference('UMG-COR-ALL-DAT', 'APP-ICAP') AS application,
    cast(icap_action.`ActionDescription` AS STRING) AS title,
    split(cast(icap_action.`ActionStartDate` AS STRING), ' ')[0] AS assignmentDate,
    CASE
      WHEN icap_action.`ActionDueDate` IS NOT NULL THEN cast(split_part(cast(icap_action.`ActionDueDate` AS STRING), ' ', 1) AS DATE)
      WHEN icap_action.`ActionActualCompletionDate` IS NOT NULL THEN cast(split_part(cast(icap_action.`ActionActualCompletionDate` AS STRING), ' ', 1) AS DATE)
      WHEN icap_action.`ActionStatusID` IN (4, 5, 6) THEN cast(split_part(cast(icap_action.`ActionStartDate` AS STRING), ' ', 1) AS DATE)
      WHEN icap_action.`ActionStatusID` IN (1, 2, 3)  THEN cast('2100-01-01' AS DATE)
    END AS dueDate,
    CASE
      WHEN icap_action.`ActionDueDate` IS NOT NULL THEN cast(split_part(cast(icap_action.`ActionDueDate` AS STRING), ' ', 1) AS DATE)
      WHEN icap_action.`ActionActualCompletionDate` IS NOT NULL THEN cast(split_part(cast(icap_action.`ActionActualCompletionDate` AS STRING), ' ', 1) AS DATE)
      WHEN icap_action.`ActionStatusID` IN (4, 5, 6) THEN cast(split_part(cast(icap_action.`ActionStartDate` AS STRING), ' ', 1) AS DATE)
      WHEN icap_action.`ActionStatusID` IN (1, 2, 3)  THEN cast('2100-01-01' AS DATE)
    END AS displayDueDate,
    category_map.aim_category AS category,
    category_map.aim_subcategory AS subCategory,
    if(
        isnotnull(category_map.aim_site_category_external_id_pattern),
        node_reference(
            utl_reporting_site.aim_space,
            replace(
                category_map.aim_site_category_external_id_pattern,
                '<SITE>',
                utl_reporting_site.ah_site_code
            )
        ),
        NULL
    ) AS siteSpecificCategory,
    cast(icap_action.`Cause` AS STRING) AS description,
    node_reference('AIM-COR-ALL-REF', 'ACTK-oneTime') AS actionItemKind,
    utl_user_owner.user_azure_attribute AS createdBy,
    utl_user_owner.user_azure_attribute AS owner,
    coalesce(utl_user_assignee.user_azure_attribute, utl_user_secondary_assignee.user_azure_attribute) AS assignedTo,
    aim_existing_action.sourceType AS sourceType,
    concat(
      'ICAPContinuousMigration-',
      icap_action.ActionItemID,
      '-RCAC-WI-',
      rcac.`RCACID`
    ) AS objectType,
    if(
      NOT icap_action.ActionActive,
      named_struct("externalId",'ACTS-deleted', "space", 'AIM-COR-ALL-REF'),
      utl_status.status
    ) AS currentStatus,
    utl_reporting_unit.ah_reporting_unit AS reportingUnit,
    utl_reporting_unit.ah_reporting_location AS reportingLocation,
    utl_reporting_unit.ah_reporting_line AS reportingLine,
    NULL AS businessLine,
    TRUE AS viewOnly,
    icap_action_priority.`ActionPriority` AS priority,
    DATE(icap_action.ActionActualCompletionDate) AS conclusionDate,
    date_format(timestamp(ActionActualCompletionDate), "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'") AS completedDate,
    CAST(rcac.`RCACID` AS STRING) AS sourceInformation,
    icap_action.`EvidenceRequired` AS evidenceRequired,
    coalesce(icap_action.`Private`) AS isPrivate,
    icap_action.`Comments` AS concatenated_comments,
    NULL AS views
  FROM `ICAP-COR`.`RCAC-tblRCACPGRActionItem` AS icap_action
  LEFT JOIN `ICAP-COR`.`RCAC-tblRCAC` AS rcac ON rcac.`RCACID` = icap_action.`TypeID`
  LEFT JOIN `ICAP-COR`.`UNT-tblUnit` AS icap_unit ON icap_unit.key = cast(rcac.`UnitID` AS STRING)
  LEFT JOIN `AIM-COR`.`ICAP-MAP-ReportingSite` AS utl_reporting_site ON utl_reporting_site.key = cast(icap_unit.`SiteID` AS STRING)
  LEFT JOIN `AIM-COR`.`ICAP-MAP-ActionStatus` AS utl_status ON utl_status.key = cast(icap_action.`ActionStatusID` AS STRING)
  LEFT JOIN `AIM-COR`.`ICAP-MAP-User` AS utl_user_owner ON utl_user_owner.key = cast(rcac.`RCACAddedBy` AS STRING)
  LEFT JOIN `AIM-COR`.`ICAP-MAP-Category` category_map ON category_map.icap_action_category_id = 21
  LEFT JOIN `AIM-COR`.`ICAP-MAP-User` AS utl_user_assignee ON utl_user_assignee.key = cast(icap_action.`PPRID` AS STRING)
  LEFT JOIN `AIM-COR`.`ICAP-MAP-User` AS utl_user_secondary_assignee ON utl_user_secondary_assignee.key = cast(icap_action.`SecondaryPPRID` AS STRING)
  LEFT JOIN `ICAP-COR`.`AXN-tblActionPriority` AS icap_action_priority ON icap_action.`ActionPriorityID` = icap_action_priority.`ActionPriorityID`
  LEFT JOIN `AIM-COR`.`ICAP-MAP-ReportingUnit&Location` AS utl_reporting_unit ON utl_reporting_unit.key = icap_unit.key
  LEFT JOIN cdf_data_models("AIM-COR-ALL-DMD", "ActionItemManagementDOM", "6_0_0", "Action") AS aim_existing_action ON (
    split_part(aim_existing_action.objectType, '-', 2) = cast(icap_action.ActionItemID AS STRING)
    AND startswith(aim_existing_action.objectType, 'ICAP')
    AND instr(aim_existing_action.objectType, 'RCAC-WI') > 0
  )
  WHERE icap_action.`Type` = 'RCAC'
  ),

  all_actions AS (
    SELECT * FROM from_tblActionItem
    UNION
    SELECT * FROM from_tblRCACPGRActionItem
  )

  SELECT
    *,
    coalesce(
      existing_external_id,
      concat(
        'ACT-',
        split(split(current_timestamp(), ' ') [0], '-') [0],
        split(split(current_timestamp(), ' ') [0], '-') [1],
        split(split(current_timestamp(), ' ') [0], '-') [2],
        split(split(current_timestamp(), ' ') [1], ':') [0],
        split(split(current_timestamp(), ' ') [1], ':') [1],
        '-',
        ROW_NUMBER() OVER(ORDER BY key ASC)
      )
    ) AS externalId
  FROM all_actions
destination:
  database: AIM-COR
  table: ICAP-STG-Action
  type: raw
ignoreNullFields: true
shared: true
action: upsert
dataSetExternalId: AIM-COR-ALL-DAT
# Specify credentials separately like this:
# authentication:
#   read:
#     clientId: ${READ_CLIENT_ID}
#     clientSecret: ${READ_CLIENT_SECRET}
#     tokenUrl: ${READ_TOKEN_URL}
#     cdfProjectName: ${READ_CDF_PROJECT_NAME}
#     # Optional: If idP requires providing the scopes
#     scopes:
#       - ${READ_SCOPES}
#     # Optional: If idP requires providing the audience
#     audience: ${READ_CDF_AUDIENCE}
#   write:
#     clientId: ${WRITE_CLIENT_ID}
#     clientSecret: ${WRITE_CLIENT_SECRET}
#     tokenUrl: ${WRITE_TOKEN_URL}
#     cdfProjectName: ${WRITE_CDF_PROJECT_NAME}
#     # Optional: If idP requires providing the scopes
#     scopes:
#       - ${WRITE_SCOPES}
#     # Optional: If idP requires providing the audience
#     audience: ${WRITE_CDF_AUDIENCE}
# Or together like this:
# authentication:
#   clientId: ${CLIENT_ID}
#   clientSecret: ${CLIENT_SECRET}
#   tokenUrl: ${TOKEN_URL}
#   # Optional: If idP requires providing the scopes
#   cdfProjectName: ${CDF_PROJECT_NAME}
#   scopes:
#    - ${SCOPES}
#   # Optional: If idP requires providing the audience
#   audience: ${AUDIENCE}
