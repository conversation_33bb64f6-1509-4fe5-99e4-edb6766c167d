import asyncio
from pathlib import Path
from typing import List, Optional
from typing_extensions import Annotated
import typer

import validation_function
import file_migration_function
from internal.infra import get_cognite_client, get_config
from internal.services import (
    FunctionService,
    ReferenceDataService,
    MigrationProgressReportService,
    WorkflowPublisher,
)

app = typer.Typer()


@app.command()
def reference_data():
    config = get_config()
    client = get_cognite_client(config)

    service = ReferenceDataService(client)
    service.handle_reference_data(config)


@app.command()
def validations():
    validation_function.handle()


@app.command()
def file_migration(batch_size: int = 10):
    file_migration_function.handle(None, {"batch_size": batch_size})


@app.command()
def migration_progress_report():
    config = get_config()
    client = get_cognite_client(config)

    service = MigrationProgressReportService(client)
    service.generate()


@app.command()
def deploy_functions():
    config = get_config()
    client = get_cognite_client(config)
    client.config.timeout = 180

    FunctionService(client).deploy_functions(config)


@app.command(
    epilog="""
Examples:
\n
\n python main.py publish-workflows --dry-run
\n python main.py publish-workflows --dry-run --force tr-workflow-external-id-1 tr-workflow-external-id-2
\n python main.py publish-workflows
"""
)
def publish_workflows(
    dry_run: Annotated[
        bool, typer.Option(help="Run the program without making any changes.")
    ] = False,
    force: Annotated[bool, typer.Option(help="Force deploy the workflows.")] = False,
    workflow_ids: Annotated[
        Optional[List[str]],
        typer.Argument(help="ExternalIds of the workflows to force publish."),
    ] = None,
):
    config = get_config()
    client = get_cognite_client(config)
    workflows_directory = Path(__file__).parent.joinpath("workflows")
    publisher = WorkflowPublisher(
        env_settings=config.env_settings,
        client=client,
        directory=workflows_directory,
        valid_prefixes=["AIM-COR-ICAP"],
    )
    force_deploy_ids = (workflow_ids or []) if force else []
    ok, errors = asyncio.run(publisher.publish_workflows(dry_run, force_deploy_ids))
    if not ok and errors:
        print("Errors found")
        for e in errors:
            print(e)


if __name__ == "__main__":
    app()
    # publish_workflows(dry_run=True)
