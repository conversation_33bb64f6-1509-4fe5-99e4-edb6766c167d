import { C<PERSON><PERSON><PERSON><PERSON>, Cln<PERSON>anel } from '@celanese/ui-lib'
import {
    Autocomplete,
    FormControlLabel,
    Grid,
    Radio,
    RadioGroup,
    styled,
    TextField,
    Typography,
    useMediaQuery,
    useTheme,
} from '@mui/material'
import Box from '@mui/material/Box'
import * as React from 'react'
import { useMemo, useState, useContext, useEffect } from 'react'
import { useActionItemCategories } from '@/app/common/hooks/action-item-management/useActionItemCategories'
import { useActionItemSubCategories } from '@/app/common/hooks/action-item-management/useActionItemSubCategories'
import { z } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'
import {
    MAX_DESCRIPTION_TEXT_FIELD,
    MAX_TITLE_TEXT_FIELD,
    MIN_DEFAULT_TEXT_FIELD,
    PREFIX_USER_AZURE_ATTRIBUTE,
} from '@/app/common/utils'
import { Controller, SubmitHand<PERSON>, useForm } from 'react-hook-form'
import { useSiteSpecificCategories } from '@/app/common/hooks/action-item-management/useSiteSpecificCategories'
import { LocationQueryRequest, useReportingLocations } from '@/app/common/hooks/asset-hierarchy/useReportingLocations'
import { useBusinessLines } from '@/app/common/hooks/asset-hierarchy/useBusinessLines'
import { useRouter } from 'next/navigation'
import dayjs, { Dayjs } from 'dayjs'
import { useUsersSearch } from '@/app/common/hooks/user-management/useUsers'
import { useCognite } from '@/app/common/hooks'
import { buildUploadFilesRequest } from '@/app/common/utils/files'
import { UserManagementContext, UserManagementContextState } from '@celanese/celanese-ui'
import { UploadFiles } from '../../UploadFiles/uploadFile'
import { AzureFunctionClient } from '@/app/common/clients/azure-function-client'
import { IconWithTooltip } from '@/app/common/utils/icons-helper'
import EventTable from '../EventTable/eventTable'
import { Team } from '@/app/common/models/common/user-management/team'
import { Role } from '@/app/common/models/common/user-management/role'
import GenericTextField from '../../FieldsComponent/GenericTextField'
import GenericAutocomplete, { AutocompleteOption } from '../../FieldsComponent/GenericAutocomplete'
import { transformOptions } from '@/app/common/utils/transform-options-for-filter'
import GenericFieldTitle from '../../FieldsComponent/GenericFieldTitle'
import PrivateComponent from '../../PrivateComponent/PrivateComponent'
import { User } from '@/app/common/models/common/user-management/user'
import { SourceEvent } from '@/app/common/models/source-event'
import { Viewers } from '../../UserComponent/privateSettingsModal'
import { SecondaryOwners } from '../../UserComponent/secondaryOwnersModal'

import * as styles from './styles'
import GenericDateRangePicker from '../../FieldsComponent/GenericDateRangePicker'
import { Attachment } from '@/app/common/models/action-detail'
import ManageSecondaryOwners from '../../ManageSecondaryOwners/ManageSecondaryOwners'
import { useSnackbar } from '@/app/common/contexts/SnackbarContext'
import { LineQueryRequest, useReportingLines } from '@/app/common/hooks/asset-hierarchy/useReportingLines'
import { translate } from '@/app/common/utils/generate-translate'
import { useLoading } from '@/app/common/contexts/LoadingContext'
import { SimpleEquipment } from '@/app/common/models/common/asset-hierarchy/equipment'
import { DescribableEntity } from '@celanese/celanese-sdk'
import useDebounce, { useDebounceFunction } from '@/app/common/hooks/general-functions/useDebounce'
import { FileSourceStepEnum } from '@/app/common/enums/FileSourceStepEnum'
import { UnitQueryRequest, useReportingUnits } from '@/app/common/hooks/asset-hierarchy/useReportingUnits'
import { SITE_EXTERNAL_ID_REQUIRED_FIELD, SUBCATEGORY1_TO_REMOVE } from '@/app/common/utils/validate-codes'
import { getCodeFromReportingSiteExternalId } from '@/app/common/utils/space-util'

const Form = styled('form')({})

type EventFormsProps = {
    siteId?: string
    onClose: (value: boolean) => void
    showRadioGroup?: boolean
    sourceEvent?: SourceEvent
    fromEdit?: boolean
}

const zodDate = z
    .instanceof(dayjs as unknown as typeof Dayjs)
    .nullable()
    .optional()

const isDueAfterStart = (startDate: Dayjs, dueDate: Dayjs) => {
    return dayjs(dueDate).isSameOrAfter(dayjs(startDate))
}

const formSchema = z
    .object({
        eventTitle: z.string().min(MIN_DEFAULT_TEXT_FIELD).max(MAX_TITLE_TEXT_FIELD),
        description: z.string().min(MIN_DEFAULT_TEXT_FIELD).max(MAX_DESCRIPTION_TEXT_FIELD),
        reportingSite: z.string().min(1),
        category: z.string().min(1),
        subCategory1: z.string().min(1),
        subCategory2: z.string().optional(),
        reportingUnit: z.string().min(1),
        impactedUnits: z.array(z.string()).optional(),
        reportingLocation: z.string().optional(),
        impactedLocations: z.array(z.string()).optional(),
        businessLine: z.string().optional(),
        reportingLine: z.string().optional(),
        owner: z.string().min(1),
        startDate: zodDate,
        dueDate: zodDate,
        functionalLocation: z.string().optional(),
        equipment: z.string().optional(),
    })
    .superRefine((data, ctx) => {
        if (data.startDate && data.dueDate && !isDueAfterStart(data.startDate, data.dueDate)) {
            ctx.addIssue({
                path: ['dueDate'],
                code: z.ZodIssueCode.custom,
            })
        }
    })

type EventFormSchema = z.infer<typeof formSchema>

export default function EventForms({ siteId, onClose, showRadioGroup, sourceEvent, fromEdit }: EventFormsProps) {
    const router = useRouter()
    const azureFunctionClient = new AzureFunctionClient()

    const { cogniteClient } = useCognite()
    const { userInfo: activeUser } = useContext<UserManagementContextState>(UserManagementContext)

    const { showSnackbar } = useSnackbar()
    const { showLoading } = useLoading()

    const theme = useTheme()
    const isMobile = useMediaQuery(theme.breakpoints.down(900))

    const {
        control,
        handleSubmit,
        reset,
        setValue,
        getValues,
        watch,
        formState: { errors },
    } = useForm<EventFormSchema>({
        defaultValues: {
            reportingSite: siteId ?? '',
        },
        resolver: zodResolver(formSchema),
    })

    const reportingSiteOptions =
        activeUser.selectedSites?.map((site) => ({
            value: site.siteId,
            label: site.siteName,
        })) ?? []

    const [newUploadedFiles, setNewUploadedFiles] = useState<File[]>([])
    const [oldUploadedFiles, setOldUploadedFiles] = useState<Attachment[]>([])

    const [isCreateEvent, setIsCreateEvent] = useState('yes')

    const [isPrivateChecked, setIsPrivateChecked] = useState<boolean>(false)

    const selectedSite = watch('reportingSite')
    const selectedUnit = watch('reportingUnit')
    const selectedImpactedUnits = watch('impactedUnits')
    const selectedLocations = watch('impactedLocations')
    const selectedFunctionalLocation = watch('functionalLocation')
    const selectedEquipment = watch('equipment')
    const selectedStartDate = watch('startDate')
    const selectedSubCategory = watch('subCategory1')

    const [selectedUsers, setSelectedUsers] = useState<User[]>([])
    const [selectedRoles, setSelectedRoles] = useState<Role[]>([])
    const [selectedTeams, setSelectedTeams] = useState<Team[]>([])

    const [selectedUsersViewers, setSelectedUsersViewers] = useState<User[]>([])
    const [selectedRolesViewers, setSelectedRolesViewers] = useState<Role[]>([])
    const [selectedTeamsViewers, setSelectedTeamsViewers] = useState<Team[]>([])

    const [owner, setOwner] = useState<User | null>(null)
    const [usersParamByOwner, setUsersParamByOwner] = useState<string>('NULL_PARAM')

    const [titleCount, setTitleCount] = useState<number>(0)
    const [descriptionCount, setDescriptionCount] = useState<number>(0)

    const { users: filteredUsersByOwner } = useUsersSearch(
        useMemo(() => {
            return usersParamByOwner
        }, [usersParamByOwner])
    )

    const { users: activeUserInfo } = useUsersSearch(
        useMemo(() => {
            return activeUser?.email ?? ['-']
        }, [activeUser])
    )

    const { units } = useReportingUnits(
        useMemo(() => {
            const request: UnitQueryRequest = { siteId: selectedSite }
            return request
        }, [selectedSite])
    )

    const { lines: reportingLines } = useReportingLines(
        useMemo(() => {
            const request: LineQueryRequest = { siteIds: [selectedSite], unitId: selectedUnit }
            return request
        }, [selectedUnit, selectedSite])
    )

    const { locations: locationsByUnit } = useReportingLocations(
        useMemo(() => {
            const unitIds = [selectedUnit]
            const request: LocationQueryRequest = { siteId: selectedSite, unitIds: unitIds, onlyActive: true }
            return request
        }, [selectedSite, selectedUnit])
    )

    const { locations: impactedLocationsByUnit } = useReportingLocations(
        useMemo(() => {
            const unitIds = selectedImpactedUnits?.length ? selectedImpactedUnits : ['-']
            const request: LocationQueryRequest = { siteId: selectedSite, unitIds: unitIds, onlyActive: true }
            return request
        }, [selectedImpactedUnits, selectedSite])
    )

    const { businessLine } = useBusinessLines()
    const { categories } = useActionItemCategories()
    const { subCategories } = useActionItemSubCategories()
    const { siteSpecificCategories } = useSiteSpecificCategories({ siteIds: [selectedSite], excludeDeleted: true })

    const unitOptions = transformOptions(units)

    const locationsByUnitOptions = useMemo(() => {
        return transformOptions(locationsByUnit)
    }, [locationsByUnit])

    const impactedLocationsOptions = useMemo(() => {
        return transformOptions(impactedLocationsByUnit)
    }, [impactedLocationsByUnit])

    const reportingLineOptions = useMemo(() => {
        return transformOptions(reportingLines)
    }, [reportingLines])

    const businessLineOptions = transformOptions(businessLine)
    const categoryOptions = transformOptions(categories, 'name', 'name', 'category')
    const subcategory1Options = transformOptions(subCategories, 'name', 'name', 'subCategory1')
    const subcategory2Options = transformOptions(siteSpecificCategories, 'name')

    const [equipments, setEquipments] = useState<SimpleEquipment[]>([])
    const [selectedEquipmentOption, setSelectedEquipmentOption] = useState<AutocompleteOption>()
    const [loadingEquipments, setLoadingEquipments] = useState(false)
    const [equipmentParam, setEquipmentParam] = useState('NULL_PARAM')
    const debouncedEquipmentParam = useDebounce(equipmentParam, 500)

    const equipmentOptions = useMemo(() => transformOptions(equipments, 'label'), [equipments])

    const [functionalLocations, setFunctionalLocations] = useState<DescribableEntity[]>([])
    const [loadingFunctionalLocations, setLoadingFunctionalLocations] = useState(false)

    const functionalLocationOptions = useMemo(
        () => transformOptions(functionalLocations, 'name'),
        [functionalLocations]
    )

    const debouncedFetchFunctionalLocations = useDebounceFunction(async (unitId: string) => {
        setLoadingFunctionalLocations(true)

        try {
            const client = new AzureFunctionClient()
            const result = await client.getFunctionalLocations({
                reportingSiteExternalIds: [selectedSite],
                reportingUnitExternalIds: [unitId],
            })

            setFunctionalLocations(result)
        } catch (err: any) {
            if (err.name !== 'AbortError') {
                showSnackbar(translate('alerts.unexpectedErrorOcurred'), 'error', 'event-form')
            }
        } finally {
            setLoadingFunctionalLocations(false)
        }
    }, 500)

    useEffect(() => {
        if (!selectedUnit) return
        debouncedFetchFunctionalLocations(selectedUnit)
    }, [selectedUnit])

    const debouncedFetchEquipments = useDebounceFunction(
        async (unitId: string, searchParam: string, locationId?: string) => {
            setLoadingEquipments(true)

            try {
                const client = new AzureFunctionClient()

                const result = await client.getEquipments({
                    search: searchParam,
                    reportingSiteExternalIds: [selectedSite],
                    reportingUnitExternalIds: [unitId],
                    functionalLocationExternalIds: locationId ? [locationId] : undefined,
                })

                setEquipments(result)
            } catch (err: any) {
                if (err.name !== 'AbortError') {
                    showSnackbar(translate('alerts.unexpectedErrorOcurred'), 'error', 'action-item-modal')
                }
            } finally {
                setLoadingEquipments(false)
            }
        },
        500
    )

    useEffect(() => {
        if (!selectedUnit || !debouncedEquipmentParam || debouncedEquipmentParam === 'NULL_PARAM') return
        debouncedFetchEquipments(selectedUnit, debouncedEquipmentParam, selectedFunctionalLocation)
    }, [selectedUnit, debouncedEquipmentParam, selectedFunctionalLocation])

    const radioCreateEvent = () => {
        return (
            <Box>
                {showRadioGroup ? (
                    <Box sx={{ marginBottom: '0.5rem' }}>
                        <Typography fontSize={16} color="primary.main">
                            {translate('source-event.changeEvent')}
                        </Typography>
                        <RadioGroup
                            row
                            aria-labelledby="demo-row-radio-buttons-group-label"
                            name="row-radio-buttons-group"
                            value={isCreateEvent}
                            onChange={(e) => {
                                setIsCreateEvent(e.target.value)
                                if (e.target.value === 'no') localStorage.setItem('isCreateNewActions', 'true')
                                else localStorage.removeItem('isCreateNewActions')
                            }}
                        >
                            <FormControlLabel
                                value={'yes'}
                                control={<Radio />}
                                label={translate('common.yes')}
                                data-test="new_action_item_create_new_event-yes_radio"
                                data-origin="aim"
                            />
                            <FormControlLabel
                                value={'no'}
                                control={<Radio />}
                                label={translate('common.no')}
                                data-test="new_action_item_create_new_event-no_radio"
                                data-origin="aim"
                            />
                        </RadioGroup>
                    </Box>
                ) : null}
            </Box>
        )
    }

    const handleFileUploads = async () => {
        if (!newUploadedFiles.length) return []

        const uploadRequest = await buildUploadFilesRequest(
            cogniteClient,
            newUploadedFiles,
            activeUser.displayName,
            selectedSite,
            isPrivateChecked,
            fromEdit ? FileSourceStepEnum.EventInProgress : FileSourceStepEnum.EventCreation
        )

        return (await azureFunctionClient.uploadFiles(uploadRequest)).externalIds
    }

    const mapUsers = (users?: User[]): string[] =>
        users?.filter((u) => !!u.externalId).map((u) => `${PREFIX_USER_AZURE_ATTRIBUTE}${u.externalId}`) ?? []

    const mapRoles = (roles?: Role[]): string[] => roles?.map((r) => r.externalId).filter(Boolean) ?? []

    const mapTeams = (teams?: Team[]): string[] => teams?.map((t) => t.externalId).filter(Boolean) ?? []

    const collectEdgesToDelete = (sourceEvent?: SourceEvent): string[] =>
        Array.from(
            new Set([
                ...(sourceEvent?.reportingUnits?.map((item) => item.externalId).filter(Boolean) ?? []),
                ...(sourceEvent?.impactedReportingLocations?.map((item) => item.externalId) ?? []),
                ...(sourceEvent?.secondaryOwnerUsers?.map((item) => item.externalId) ?? []),
                ...(sourceEvent?.secondaryOwnerRoles?.map((item) => item.externalId) ?? []),
                ...(sourceEvent?.secondaryOwnerTeams?.map((item) => item.externalId) ?? []),
                ...(sourceEvent?.viewUsers?.map((item) => `viewUser-${item.externalId}`) ?? []),
                ...(sourceEvent?.viewRoles?.map((item) => `viewRole-${item.externalId}`) ?? []),
                ...(sourceEvent?.viewTeams?.map((item) => `viewTeam-${item.externalId}`) ?? []),
                ...(sourceEvent?.equipments?.map((item) => item.externalId) ?? []),
                ...(sourceEvent?.functionalLocations?.map((item) => item.externalId) ?? []),
            ])
        ) as string[]

    const collectFilesToDelete = (oldFileIds: string[], sourceEvent?: SourceEvent): string[] =>
        Array.from(
            new Set(
                (sourceEvent?.attachments ?? [])
                    .filter((file) => !oldFileIds.includes(file.externalId))
                    .map((file) => file.externalId)
            )
        )

    const onSubmit: SubmitHandler<EventFormSchema> = async (data: any) => {
        showLoading(true)

        try {
            const newUploadedFilesIds = await handleFileUploads()
            const oldUploadedFilesIds = oldUploadedFiles.map((a) => a.externalId)

            const ownerIds = {
                users: mapUsers(selectedUsers),
                roles: mapRoles(selectedRoles),
                teams: mapTeams(selectedTeams),
            }

            const viewerIds = isPrivateChecked
                ? {
                      users: mapUsers(selectedUsersViewers),
                      roles: mapRoles(selectedRolesViewers),
                      teams: mapTeams(selectedTeamsViewers),
                  }
                : { users: [], roles: [], teams: [] }

            const eventRequest: any = {
                title: data.eventTitle,
                description: data.description,
                dueDate: data.dueDate ? dayjs(data.dueDate).format('YYYY-MM-DD') : undefined,
                assignmentDate: data.startDate ? dayjs(data.startDate).format('YYYY-MM-DD') : undefined,

                categoryId: data.category,
                subCategoryId: data.subCategory1,
                siteSpecificCategoryId: data.subCategory2 || undefined,
                reportingUnitId: data.reportingUnit,
                impactedUnitsId: selectedImpactedUnits || [],
                reportingLocationId: data.reportingLocation || undefined,
                impactedLocationsId: selectedLocations || [],

                applicationId: 'APP-AIM',
                businessLineId: data.businessLine || undefined,
                reportingLineId: data.reportingLine || undefined,
                reportingSiteId: data.reportingSite || undefined,
                attachmentIds:
                    newUploadedFilesIds.length || oldUploadedFilesIds.length
                        ? [...newUploadedFilesIds, ...oldUploadedFilesIds]
                        : undefined,
                createdById: `${PREFIX_USER_AZURE_ATTRIBUTE}${activeUser.externalId}`,
                ownerId: `${PREFIX_USER_AZURE_ATTRIBUTE}${data.owner}`,
                secondaryOwnerUsersId: ownerIds.users,
                secondaryOwnerRolesId: ownerIds.roles,
                secondaryOwnerTeamsId: ownerIds.teams,
                viewUsers: viewerIds.users,
                viewRoles: viewerIds.roles,
                viewTeams: viewerIds.teams,
                views: isPrivateChecked
                    ? Array.from(
                          new Set([
                              `${PREFIX_USER_AZURE_ATTRIBUTE}${data.owner}`,
                              ...ownerIds.users,
                              ...ownerIds.roles,
                              ...ownerIds.teams,
                              ...viewerIds.users,
                              ...viewerIds.roles,
                              ...viewerIds.teams,
                          ])
                      )
                    : undefined,

                private: isPrivateChecked ? isPrivateChecked : undefined,

                equipmentsId: selectedEquipment ? [selectedEquipment] : [],
                functionalLocationsId: selectedFunctionalLocation ? [selectedFunctionalLocation] : [],
            }

            if (fromEdit) {
                eventRequest.space = sourceEvent?.space || ''
                eventRequest.edgesIdsToDelete = collectEdgesToDelete(sourceEvent)
                eventRequest.filesIdsToDelete = collectFilesToDelete(oldUploadedFilesIds, sourceEvent)
            }

            const result = fromEdit
                ? await azureFunctionClient.updateSourceEvent(sourceEvent?.externalId ?? '', eventRequest)
                : await azureFunctionClient.createSourceEvent([eventRequest])

            showAlert(`${translate('alerts.dataSavedWithSuccess')} - ${translate('alerts.redirectToPage')}`, false)
            localStorage.removeItem('isEditForm')

            const siteCode = getCodeFromReportingSiteExternalId(selectedSite)
            router.push(
                `/event-source/details/${sourceEvent?.reportingSite?.siteCode ?? siteCode}/${result.externalIds[0]}`
            )
        } catch (ex) {
            showAlert('alerts.unexpectedErrorOcurred', true)
        } finally {
            showLoading(false)
        }
    }

    const handleTitleChange = (value: string) => {
        setTitleCount(value.length)
        setValue('eventTitle', value)
    }

    const showAlert = (messageKey: string, isError: boolean) => {
        showSnackbar(translate(messageKey), isError ? 'error' : 'success', 'event-forms')
    }

    const handleSavePrivateSettings = (viewers: Viewers) => {
        const { users, roles, teams } = viewers
        setSelectedUsersViewers(users)
        setSelectedRolesViewers(roles)
        setSelectedTeamsViewers(teams)
    }

    const handleSaveSecondaryOwners = (secondaryOwners: SecondaryOwners) => {
        const { users, roles, teams } = secondaryOwners
        setSelectedUsers(users)
        setSelectedRoles(roles)
        setSelectedTeams(teams)
    }

    const clearViewersAndSecondaryOwners = () => {
        setSelectedUsersViewers([])
        setSelectedRolesViewers([])
        setSelectedTeamsViewers([])
        setSelectedUsers([])
        setSelectedRoles([])
        setSelectedTeams([])
    }

    useEffect(() => {
        if (activeUserInfo.length > 0 && !fromEdit) {
            const [firstUser] = activeUserInfo
            setValue('owner', firstUser.externalId)
            setUsersParamByOwner(activeUser?.email)
            setOwner(firstUser)
        }
    }, [activeUserInfo])

    useEffect(() => {
        if (!sourceEvent) return

        const {
            title,
            description,
            category,
            subCategory,
            siteSpecificCategory,
            reportingSite,
            reportingUnit,
            reportingLocation,
            reportingUnits: impactedReportingunits,
            impactedReportingLocations,
            businessLine,
            reportingLine,
            owner,
            secondaryOwnerUsers,
            secondaryOwnerRoles,
            secondaryOwnerTeams,
            viewUsers,
            viewRoles,
            viewTeams,
            assignmentDate,
            dueDate,
            isPrivate,
            attachments,
            equipments,
            functionalLocations,
        } = sourceEvent

        const newValues = {
            eventTitle: title ?? '',
            description: description ?? '',
            category: category?.externalId ?? '',
            subCategory1: subCategory?.externalId ?? '',
            subCategory2: siteSpecificCategory?.externalId ?? '',
            reportingSite: reportingSite?.externalId ?? '',
            reportingUnit: reportingUnit?.externalId ?? '',
            reportingLocation: reportingLocation?.externalId ?? '',
            impactedUnits: impactedReportingunits?.map((unit) => unit.externalId) ?? [],
            impactedLocations: impactedReportingLocations?.map((loc) => loc.externalId) ?? [],
            businessLine: businessLine?.externalId ?? '',
            reportingLine: reportingLine?.externalId ?? '',
            owner: owner?.user.externalId ?? '',
            startDate: assignmentDate ? dayjs(assignmentDate) : null,
            dueDate: dueDate ? dayjs(dueDate) : null,
            equipment: equipments[0]?.externalId ?? '',
            functionalLocation: functionalLocations[0]?.externalId ?? '',
        }

        reset(newValues)

        setUsersParamByOwner(owner?.user.email ?? '')
        setOwner(owner?.user ?? null)

        setSelectedUsers(secondaryOwnerUsers?.map((owner) => owner.user) ?? [])
        setSelectedRoles(secondaryOwnerRoles ?? [])
        setSelectedTeams(secondaryOwnerTeams ?? [])

        setSelectedUsersViewers(viewUsers?.map((u) => u.user) ?? [])
        setSelectedRolesViewers(viewRoles ?? [])
        setSelectedTeamsViewers(viewTeams ?? [])

        setIsPrivateChecked(isPrivate ?? false)

        setOldUploadedFiles(attachments ?? [])

        setSelectedEquipmentOption(
            equipments[0]
                ? {
                      value: equipments[0].externalId ?? '',
                      label: `${equipments[0].number ?? ''} | ${equipments[0].name ?? ''} | ${
                          equipments[0].description ?? ''
                      }`,
                  }
                : undefined
        )
    }, [sourceEvent, reset])

    const renderForm = () => (
        <Form onSubmit={handleSubmit(onSubmit)} id={'forms-new-events'}>
            <Grid container spacing={2.5}>
                <Grid item xs={12} md={6} id={'fields-forms-new-events'}>
                    <Grid item md={12} id={'title-forms-new-events'} sx={styles.itemsForm}>
                        <GenericTextField
                            name={'eventTitle'}
                            control={control}
                            error={Boolean(errors.eventTitle)}
                            valueController={getValues('eventTitle')}
                            label={translate('stepper.form.eventTitle')}
                            helperText={`${titleCount}/${MAX_TITLE_TEXT_FIELD}`}
                            onChange={handleTitleChange}
                            required
                            placeholder=""
                            data-test="new_action_item_create_new_event-title_field"
                            data-origin="aim"
                        />
                    </Grid>
                    <Grid item md={12} id={'description-forms-new-events'} sx={styles.itemsForm}>
                        <GenericTextField
                            name={'description'}
                            control={control}
                            error={Boolean(errors.description)}
                            valueController={getValues('description')}
                            label={translate('stepper.form.eventDescription')}
                            helperText={`${descriptionCount}/${MAX_DESCRIPTION_TEXT_FIELD}`}
                            onChange={(newValue) => {
                                setValue('description', newValue)
                                setDescriptionCount(newValue.length)
                            }}
                            required
                            rows={4}
                            placeholder=""
                            data-test="new_action_item_create_new_event-description_field"
                            data-origin="aim"
                        />
                    </Grid>
                    <Grid item md={12} id={'category-forms-new-events'} sx={styles.itemsForm}>
                        <GenericAutocomplete
                            name="category"
                            control={control}
                            options={categoryOptions}
                            onChange={(newValue) => setValue('category', newValue)}
                            label={`${translate('adminSettings.categories.form.category')} *`}
                            size="small"
                            error={Boolean(errors.category)}
                            multiple={false}
                            disableCloseOnSelect={false}
                            placeholderTextField={translate('adminSettings.categories.form.selectCategory')}
                            inputLabelProps={{ shrink: true }}
                            data-test="new_action_item_create_new_event-category_field"
                            data-origin="aim"
                        />
                    </Grid>
                    <Grid container spacing={isMobile ? 0 : 2} id={'subCategory1-and-2-forms-new-events'}>
                        <Grid item md={6} xs={12} id={'subCategory1-forms-new-events'} sx={styles.itemsForm}>
                            <GenericAutocomplete
                                name="subCategory1"
                                control={control}
                                options={subcategory1Options.filter((option: AutocompleteOption) =>
                                    selectedSite === SITE_EXTERNAL_ID_REQUIRED_FIELD
                                        ? option.value !== SUBCATEGORY1_TO_REMOVE
                                        : true
                                )}
                                onChange={(newValue) => setValue('subCategory1', newValue)}
                                label={`${translate('adminSettings.categories.form.subcategoryOne')} *`}
                                size="small"
                                error={Boolean(errors.subCategory1)}
                                multiple={false}
                                disableCloseOnSelect={false}
                                placeholderTextField={translate('adminSettings.categories.form.selectSubcategory')}
                                inputLabelProps={{ shrink: true }}
                                data-test="new_action_item_create_new_event-subcategory_1_field"
                                data-origin="aim"
                            />
                        </Grid>
                        <Grid item md={6} xs={12} id={'subCategory2-forms-new-events'} sx={styles.itemsForm}>
                            <GenericAutocomplete
                                name="subCategory2"
                                control={control}
                                options={subcategory2Options}
                                onChange={(newValue) => setValue('subCategory2', newValue)}
                                label={translate('adminSettings.categories.form.subcategoryTwo')}
                                size="small"
                                error={Boolean(errors.subCategory2)}
                                multiple={false}
                                disabled={!selectedSite}
                                disableCloseOnSelect={false}
                                placeholderTextField={translate('adminSettings.categories.form.selectSubcategory2')}
                                inputLabelProps={{ shrink: true }}
                                data-test="new_action_item_create_new_event-subcategory_2_field"
                                data-origin="aim"
                            />
                        </Grid>
                    </Grid>
                    <Grid container id={'date-forms-new-events'} sx={styles.itemsDateForm}>
                        <Grid item md={6} xs={12} id={'startDate-forms-new-events'} sx={styles.itemsForm}>
                            <Box sx={{ width: '100%' }} marginRight={isMobile ? 0 : 1}>
                                <Box marginBottom={1}>
                                    <GenericFieldTitle
                                        fieldName={translate('stepper.form.assignment.draweStartDate')}
                                        isSubHeader
                                    />
                                </Box>
                                <GenericDateRangePicker
                                    name="startDate"
                                    control={control}
                                    isRange={false}
                                    error={Boolean(errors.startDate)}
                                    data-test="new_action_item_create_new_event-start_choose_date_button"
                                    data-origin="aim"
                                />
                            </Box>
                        </Grid>
                        <Grid item md={6} xs={12} id={'dueDate-forms-new-events'} sx={styles.itemsForm}>
                            <Box sx={{ width: '100%' }} marginLeft={isMobile ? 0 : 1}>
                                <Box marginBottom={1}>
                                    <GenericFieldTitle
                                        fieldName={translate('stepper.form.assignment.dueDate')}
                                        isSubHeader
                                    />
                                </Box>
                                <GenericDateRangePicker
                                    name="dueDate"
                                    control={control}
                                    isRange={false}
                                    minDate={selectedStartDate?.startOf('day')}
                                    error={Boolean(errors.dueDate)}
                                    data-test="new_action_item_create_new_event-due_choose_date_button"
                                    data-origin="aim"
                                />
                            </Box>
                        </Grid>
                    </Grid>
                    {activeUser.applications?.[0]?.userSites?.length > 1 && (
                        <Grid item md={12} id={'reporting-site-forms-new-events'} sx={styles.itemsForm}>
                            <GenericAutocomplete
                                name="reportingSite"
                                control={control}
                                options={reportingSiteOptions}
                                onChange={(value) => {
                                    setValue('reportingSite', value)
                                    setValue('subCategory2', '')
                                    setValue('reportingUnit', '')
                                    setValue('reportingLocation', '')
                                    setValue('reportingLine', '')
                                    setValue('functionalLocation', '')
                                    setValue('equipment', '')
                                    setValue('impactedUnits', [])
                                    setValue('impactedLocations', [])
                                    setSelectedEquipmentOption(undefined)
                                    setEquipments([])
                                    clearViewersAndSecondaryOwners()

                                    if (
                                        selectedSubCategory === SUBCATEGORY1_TO_REMOVE &&
                                        value === SITE_EXTERNAL_ID_REQUIRED_FIELD
                                    ) {
                                        setValue('subCategory1', '')
                                    }
                                }}
                                label={`${translate('stepper.form.reportingSite')} *`}
                                size="small"
                                error={Boolean(errors.reportingUnit)}
                                multiple={false}
                                disabled={!!siteId || fromEdit}
                                disableCloseOnSelect={false}
                                placeholderTextField={translate('stepper.form.reportingSite')}
                                inputLabelProps={{ shrink: true }}
                                required
                                data-test="new_action_item_flow_1-general_reporting-site_field"
                                data-origin="aim"
                            />
                        </Grid>
                    )}
                    <Grid container spacing={isMobile ? 0 : 2} id={'reportingUnit-location-forms-new-events'}>
                        <Grid item md={6} xs={12} id={'reportingUnit-forms-new-events'} sx={styles.itemsForm}>
                            <GenericAutocomplete
                                name="reportingUnit"
                                control={control}
                                options={unitOptions}
                                onChange={(newValue) => {
                                    setValue('reportingUnit', newValue)
                                    setValue('reportingLocation', '')
                                    setValue('functionalLocation', '')
                                    setValue('equipment', '')
                                    setValue('reportingLine', '')
                                    setSelectedEquipmentOption(undefined)
                                    setEquipments([])
                                }}
                                label={`${translate('stepper.form.unit')} *`}
                                size="small"
                                error={Boolean(errors.reportingUnit)}
                                multiple={false}
                                disabled={!selectedSite}
                                disableCloseOnSelect={false}
                                placeholderTextField={translate('adminSettings.categories.form.reportingUnit')}
                                inputLabelProps={{ shrink: true }}
                                data-test="new_action_item_create_new_event-unit_field"
                                data-origin="aim"
                            />
                        </Grid>
                        <Grid item md={6} xs={12} id={'reportingLocation-forms-new-events'} sx={styles.itemsForm}>
                            <GenericAutocomplete
                                name="reportingLocation"
                                control={control}
                                options={locationsByUnitOptions}
                                onChange={(newValue) => setValue('reportingLocation', newValue)}
                                label={translate('stepper.form.reportingLocation')}
                                size="small"
                                error={Boolean(errors.reportingLocation)}
                                multiple={false}
                                disabled={!selectedUnit}
                                disableCloseOnSelect={false}
                                placeholderTextField={translate('adminSettings.categories.form.selectReportingLoc')}
                                inputLabelProps={{ shrink: true }}
                                data-test="new_action_item_create_new_event-reporting_location_field"
                                data-origin="aim"
                            />
                        </Grid>
                    </Grid>
                    <Grid container spacing={isMobile ? 0 : 2} id={'reportingLine-businessLine-forms-new-events'}>
                        <Grid item md={6} xs={12} id={'reportingLine-forms-new-events'} sx={styles.itemsForm}>
                            <GenericAutocomplete
                                name="reportingLine"
                                control={control}
                                options={reportingLineOptions}
                                onChange={(newValue) => setValue('reportingLine', newValue)}
                                label={translate('stepper.form.reportingLine')}
                                disabled={!selectedUnit}
                                size="small"
                                error={Boolean(errors.reportingLine)}
                                multiple={false}
                                disableCloseOnSelect={false}
                                placeholderTextField={translate('stepper.form.reportingLine')}
                                inputLabelProps={{ shrink: true }}
                                data-test="new_action_item_create_new_event-reporting_line_field"
                                data-origin="aim"
                            />
                        </Grid>
                        <Grid item md={6} xs={12} id={'reportingLocation-forms-new-events'} sx={styles.itemsForm}>
                            <GenericAutocomplete
                                name="businessLine"
                                control={control}
                                options={businessLineOptions}
                                onChange={(newValue) => setValue('businessLine', newValue)}
                                label={translate('stepper.form.businessLine')}
                                size="small"
                                error={Boolean(errors.businessLine)}
                                multiple={false}
                                disableCloseOnSelect={false}
                                placeholderTextField={translate('adminSettings.categories.form.businessLine')}
                                inputLabelProps={{ shrink: true }}
                                data-test="new_action_item_create_new_event-business_line_field"
                                data-origin="aim"
                            />
                        </Grid>
                    </Grid>
                    <Grid container spacing={isMobile ? 0 : 2} id={'impactedReportingUnit-location-forms-new-events'}>
                        <Grid item md={6} xs={12} id={'impactedUnits-forms-new-events'} sx={styles.itemsForm}>
                            <GenericAutocomplete
                                name="impactedUnits"
                                control={control}
                                options={unitOptions}
                                limitTags={10}
                                onChange={(values) => {
                                    const currentLocations = getValues('impactedLocations') ?? []
                                    const filteredLocations = impactedLocationsByUnit
                                        .filter(
                                            (location) =>
                                                values.includes(location.reportingUnit.externalId) &&
                                                currentLocations.includes(location.externalId)
                                        )
                                        .map((location) => location.externalId)
                                    setValue('impactedUnits', values)
                                    setValue('impactedLocations', filteredLocations)
                                }}
                                label={translate('stepper.form.impactedUnit')}
                                size="small"
                                disabled={!selectedSite}
                                error={Boolean(errors.impactedUnits)}
                                placeholderTextField={
                                    !getValues('impactedUnits')?.length
                                        ? translate('adminSettings.categories.form.impactedUnits')
                                        : ''
                                }
                                inputLabelProps={{ shrink: true }}
                                data-test="new_action_item_create_new_event-impacted_units_field"
                                data-origin="aim"
                            />
                        </Grid>
                        <Grid item md={6} xs={12} id={'impactedLocations-forms-new-events'} sx={styles.itemsForm}>
                            <GenericAutocomplete
                                name="impactedLocations"
                                control={control}
                                options={impactedLocationsOptions}
                                limitTags={10}
                                onChange={(values) => setValue('impactedLocations', values)}
                                label={translate('stepper.form.impactedLocation')}
                                size="small"
                                disabled={!selectedSite}
                                error={Boolean(errors.impactedLocations)}
                                placeholderTextField={
                                    !getValues('impactedLocations')?.length
                                        ? translate('adminSettings.categories.form.impactedLocations')
                                        : ''
                                }
                                inputLabelProps={{ shrink: true }}
                                data-test="new_action_item_create_new_event-impacted_locations_field"
                                data-origin="aim"
                            />
                        </Grid>
                    </Grid>
                </Grid>
                <Grid item xs={12} md={6} id={'files-and-configuration-options-forms-new-events'}>
                    <Grid item xs={12} id={'functionalLocation-forms-new-events'} sx={styles.itemsForm}>
                        <GenericAutocomplete
                            name="functionalLocation"
                            control={control}
                            options={functionalLocationOptions}
                            onChange={(newValue) => {
                                setValue('functionalLocation', newValue)
                                if (newValue) {
                                    setValue('equipment', '')
                                    setEquipments([])
                                    setSelectedEquipmentOption(undefined)
                                }
                            }}
                            label={translate('stepper.form.functionalLocation')}
                            disabled={!selectedUnit}
                            size="small"
                            error={Boolean(errors.functionalLocation)}
                            multiple={false}
                            disableCloseOnSelect={false}
                            placeholderTextField={translate('stepper.form.functionalLocation')}
                            inputLabelProps={{ shrink: true }}
                            loading={loadingFunctionalLocations}
                            data-test="new_action_item_create_new_event-functional_location_field"
                            data-origin="aim"
                        />
                    </Grid>
                    <Grid item xs={12} id={'equipment-forms-new-events'} sx={styles.itemsForm}>
                        <GenericAutocomplete
                            name="equipment"
                            control={control}
                            options={equipmentOptions}
                            valueController={selectedEquipmentOption}
                            onChange={(newValue, fieldOption) => {
                                setValue('equipment', newValue)
                                setSelectedEquipmentOption(fieldOption)
                                setEquipmentParam('NULL_PARAM')

                                if (newValue) {
                                    const functionalLocationId =
                                        equipments.find((eq) => eq.externalId === newValue)?.functionalLocationParent
                                            ?.externalId ?? ''
                                    setValue('functionalLocation', functionalLocationId)
                                } else {
                                    setEquipments([])
                                }
                            }}
                            onInputChange={(event, newInputValue) => {
                                if (newInputValue?.length >= 3) {
                                    setEquipmentParam(newInputValue)
                                }
                            }}
                            onBlur={() => setEquipmentParam('NULL_PARAM')}
                            label={translate('stepper.form.equipment')}
                            disabled={!selectedUnit}
                            size="small"
                            error={Boolean(errors.equipment)}
                            multiple={false}
                            disableCloseOnSelect={false}
                            placeholderTextField={translate('stepper.form.typeAtLeast3')}
                            inputLabelProps={{ shrink: true }}
                            loading={loadingEquipments}
                            data-test="new_action_item_create_new_event-equipment_field"
                            data-origin="aim"
                        />
                    </Grid>
                    <ClnPanel
                        sx={{
                            padding: '1rem',
                            display: 'flex',
                            flexDirection: 'column',
                            marginBottom: '1rem',
                        }}
                    >
                        <UploadFiles
                            oldUploadedFiles={oldUploadedFiles}
                            newUploadedFiles={newUploadedFiles}
                            setOldUploadedFiles={(files: Attachment[]) => setOldUploadedFiles(files)}
                            setNewUploadedFiles={(files: File[]) => setNewUploadedFiles(files)}
                            isEditable={true}
                            sxProps={{ maxHeight: '60px', overflowY: 'auto' }}
                        />
                    </ClnPanel>
                    <Grid item xs={12} id={'primary-owner-forms-new-events'}>
                        <ClnPanel
                            sx={{
                                padding: '1rem',
                                display: 'flex',
                                flexDirection: 'column',
                                marginBottom: '1rem',
                            }}
                        >
                            <GenericFieldTitle fieldName={translate('stepper.form.primaryOwner')} isSubHeader />
                            <Controller
                                name="owner"
                                control={control}
                                render={({ field: { value, onChange } }) => (
                                    <Autocomplete
                                        sx={{ width: '100%' }}
                                        id="owner"
                                        value={owner}
                                        options={filteredUsersByOwner}
                                        getOptionLabel={(option) =>
                                            `${option.lastName}, ${option.firstName} (${option.email})` || ''
                                        }
                                        onChange={(e, newValue) => {
                                            if (newValue) {
                                                setOwner(newValue)
                                                onChange(newValue?.externalId ?? '')
                                            } else {
                                                setUsersParamByOwner('NULL_PARAM')
                                                onChange('')
                                                setOwner(null)
                                            }
                                        }}
                                        onInputChange={(event, newInputValue) => {
                                            if (newInputValue.length >= 3) {
                                                setUsersParamByOwner(newInputValue)
                                            }
                                        }}
                                        renderInput={(params) => (
                                            <TextField
                                                error={Boolean(errors.owner)}
                                                {...params}
                                                required
                                                helperText=""
                                                size="small"
                                                label={owner !== null ? '' : translate('stepper.form.primaryOwner')}
                                                sx={{ textAlign: 'center' }}
                                            />
                                        )}
                                        renderOption={(props, option) => (
                                            <li {...props} key={option.externalId} data-value={option.externalId}>
                                                <span>{`${option.lastName}, ${option.firstName} (${option.email})`}</span>
                                            </li>
                                        )}
                                        data-test="new_action_item_create_new_event-primary_owner_field"
                                        data-origin="aim"
                                    />
                                )}
                            />
                        </ClnPanel>
                    </Grid>
                    <Grid item xs={12} id={'secondary-owner-forms-new-events'}>
                        <ClnPanel
                            sx={{
                                padding: '1rem',
                                display: 'flex',
                                flexDirection: 'column',
                                marginBottom: '1rem',
                            }}
                        >
                            <Box
                                sx={{
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'space-between',
                                }}
                            >
                                <GenericFieldTitle
                                    fieldName={translate('stepper.form.secondaryOwners')}
                                    isSubHeader
                                    icon={
                                        <IconWithTooltip
                                            title={`* ${translate('stepper.form.assignment.selectUsersDesc')}`}
                                            fontSizeIcon="24px"
                                        />
                                    }
                                />
                            </Box>
                            <ManageSecondaryOwners
                                siteId={selectedSite}
                                selectedUsers={selectedUsers}
                                selectedRoles={selectedRoles}
                                selectedTeams={selectedTeams}
                                onSave={handleSaveSecondaryOwners}
                            />
                        </ClnPanel>
                    </Grid>
                    <Grid item xs={12} id={'private-forms-new-events'}>
                        <PrivateComponent
                            siteId={selectedSite}
                            disabled={fromEdit}
                            isChecked={isPrivateChecked}
                            setIsChecked={setIsPrivateChecked}
                            selectedUsersViewers={selectedUsersViewers}
                            selectedRolesViewers={selectedRolesViewers}
                            selectedTeamsViewers={selectedTeamsViewers}
                            onSave={handleSavePrivateSettings}
                        />
                    </Grid>
                </Grid>
            </Grid>
        </Form>
    )

    return (
        <Box sx={{ width: '100% !important', marginTop: '0.5rem' }}>
            <Box sx={{ width: '100%', minHeight: '600px' }}>
                {isCreateEvent === 'yes' ? (
                    <>
                        {radioCreateEvent()}
                        {renderForm()}
                    </>
                ) : (
                    <Grid item xs={12}>
                        {radioCreateEvent()}
                        <EventTable activeUser={activeUser} siteId={showRadioGroup ? siteId : undefined} />
                    </Grid>
                )}
            </Box>
            {isCreateEvent === 'yes' ? (
                <React.Fragment>
                    <Box sx={{ display: 'flex', gap: '8px', justifyContent: 'flex-end', marginTop: '35px' }}>
                        <ClnButton
                            variant="outlined"
                            onClick={() => onClose(true)}
                            label={translate('stepper.back')}
                            data-test="new_action_item_create_new_event-back_button"
                            data-origin="ui-lib"
                        ></ClnButton>
                        <ClnButton
                            onClick={handleSubmit(onSubmit)}
                            label={translate('stepper.save')}
                            data-test="new_action_item_create_new_event-save_button"
                            data-origin="ui-lib"
                        ></ClnButton>
                    </Box>
                </React.Fragment>
            ) : null}
        </Box>
    )
}
