import { useEffect, useState } from 'react'
import { useGetAllResultsFunction } from '../general-functions/useGetAllResultFunction'
import { UserByManagement, UserSiteConfiguration } from '../../models/common/user-management/user-site-configuraion'
import { getLocalUserSite } from '@celanese/celanese-ui'

export interface UserSiteConfigurationQueryRequest {
    name?: []
}

const queryUserComplementDashboard = `
    externalId
    user {
        userAzureAttribute {
            externalId
            space
            user {
                externalId
                lastName
                firstName
                email
            }
        }
    }
    onSiteManager {
        externalId
        space
        email
        firstName
        lastName
    }
`

export const useUserSiteConfigurations = (request: UserSiteConfigurationQueryRequest) => {
    const [resultData, setResultData] = useState<{ supervisorManager: UserByManagement[]; loading: boolean }>({
        supervisorManager: [],
        loading: true,
    })

    const { getAllResults: getAllData } = useGetAllResultsFunction<UserSiteConfiguration>(
        queryUserComplementDashboard,
        'listUserSiteConfiguration'
    )

    useEffect(() => {
        getAllData({
            and: [
                {
                    reportingSite: {
                        externalId: {
                            eq: `${getLocalUserSite()?.siteId}`,
                        },
                    },
                },
            ],
        }).then((res) => {
            if (res.length == 0) {
                setResultData({ supervisorManager: [], loading: false })
            } else {
                const usersManagement: UserByManagement[] = []
                res?.forEach((item: any) => {
                    const findManager = usersManagement.find((x) => x.externalId === item.onSiteManager?.externalId)
                    if (findManager) {
                        findManager.employees?.push({
                            space: item.user?.userAzureAttribute?.space,
                            externalId: item.user?.userAzureAttribute?.externalId,
                            email: item.user?.userAzureAttribute?.user?.email ?? '',
                            firstName: item.user?.userAzureAttribute?.user?.firstName ?? '',
                            lastName: item.user?.userAzureAttribute?.user?.lastName ?? '',
                            name: `${item.user?.userAzureAttribute?.user?.lastName ?? ''}, ${
                                item.user?.userAzureAttribute?.user?.firstName ?? ''
                            }`,
                            userAzureAttribute: {
                                space: item.user?.userAzureAttribute?.space,
                                externalId: item.user?.userAzureAttribute?.externalId,
                                user: {
                                    externalId: item.user?.userAzureAttribute?.user?.externalId,
                                    space: item.user?.userAzureAttribute?.user?.space,
                                    email: item.user?.userAzureAttribute?.user?.email ?? '',
                                    firstName: item.user?.userAzureAttribute?.user?.firstName ?? '',
                                    lastName: item.user?.userAzureAttribute?.user?.lastName ?? '',
                                },
                            },
                        })
                    } else {
                        usersManagement.push({
                            externalId: item.onSiteManager?.externalId,
                            space: item.onSiteManager?.space,
                            email: item.onSiteManager?.email,
                            firstName: item.onSiteManager?.firstName,
                            lastName: item.onSiteManager?.lastName,
                            name: `${item.onSiteManager?.lastName}, ${item.onSiteManager?.firstName}`,
                            employees: [
                                {
                                    space: item.user?.userAzureAttribute?.space,
                                    externalId: item.user?.userAzureAttribute?.externalId,
                                    email: item.user?.userAzureAttribute?.user?.email ?? '',
                                    firstName: item.user?.userAzureAttribute?.user?.firstName ?? '',
                                    lastName: item.user?.userAzureAttribute?.user?.lastName ?? '',
                                    name: `${item.user?.userAzureAttribute?.user?.lastName ?? ''}, ${
                                        item.user?.userAzureAttribute?.user?.firstName ?? ''
                                    }`,
                                    userAzureAttribute: {
                                        space: item.user?.userAzureAttribute?.space,
                                        externalId: item.user?.userAzureAttribute?.externalId,
                                        user: {
                                            externalId: item.user?.userAzureAttribute?.user?.externalId,
                                            space: item.user?.userAzureAttribute?.user?.space,
                                            email: item.user?.userAzureAttribute?.user?.email ?? '',
                                            firstName: item.user?.userAzureAttribute?.user?.firstName ?? '',
                                            lastName: item.user?.userAzureAttribute?.user?.lastName ?? '',
                                        },
                                    },
                                },
                            ],
                        })
                    }
                })
                setResultData({ supervisorManager: usersManagement, loading: true })
            }
        })
    }, [])

    return {
        loading: resultData.loading,
        supervisorManager: resultData.supervisorManager,
    }
}
