import { useEffect, useState } from 'react'
import { Filter } from '../../../models/base-hook-request'
import { MDREquipmentRequest } from '@/app/common/models/integration/master-data-request/mdr-equipment-request'
import { useGetAllResultsFunctionFromCustomClient } from '../../general-functions/useGetAllResultFunction'

const queryListAction = `
  externalId
  header {
    requestor {
      externalId
      displayName
      firstName
      lastName
    }
    plantArea {
      externalId
      name
      description
    }
    projectNumber
    fcrOriginator {
      externalId
      displayName
      firstName
      lastName
    }
    installDate
    approvalWorkflow {
      status {
        name
      }
    }
  }
  basicInfo {
    sapNumberEquipment
    keyNoun {
      name
    }
    newKeyNounNeeded
    sapOfSuperiorEquipment
    description
    class {
      name
    }
    additionalInto
    equipmanetCriticality {
      externalId
      name
    }
    typeOfCriticality {
      externalId
      name
    }
    replacingExistingEquipment
    sparePartsRequired
    permit
  }
  generalInfo {
    startupDate
    newManufacturerNeeded
  }
  locationData {
    functionalLocation {
      name
    }
    newFunctionalLocationNeeded
    suggestedTechnicalId
  }
`
const mdmQuery = `
  externalId
  requestType
  header {
    requestor {
      externalId
      displayName
      firstName
      lastName
    }
    plantArea {
      externalId
      name
      description
    }
    safetyPpe
    requestDate
    approvalWorkflow {
      status {
        name
      }
    }
  }
  mdmGeneralInfo {
    bomQuantity
    externalId
    justification
    sapMaterialNumber
    sapEquipmentFunctionalLocationBom
    mrpController {
      name
      externalId
    }
    criticalityType {
      externalId
      name
    }
    componentMaterialCriticality {
      externalId
      name
    }
  }
  modifier {
    name
    externalId
  }
    
`
const pdpmQuery = `
  externalId
  header{
    requestor {
      externalId
      displayName
      firstName
      lastName
      email
    }
    plantArea {
      externalId
      name
      description
    }
    miqaReliabilityLead {
      email
      lastName
      firstName
    }
    requestDate
    approvalWorkflow {
      status {
        name
      }
    }
  }
  basicInfo {
    pdpmType {
      externalId
      name
    }
    activityReason
    newDueDate
    newFrequency
    leadTime
    sapActivityType {
      name
      description
    }
    activityDescription
    activitySkill {
      name
    }
    equipShutdown {
      name
    }
    justification
    criticatily{
      description
    }
    mpDescription
    isthisUrgent
  }
  itemsObjects {
    items{
      functionalLocation {
        name
      }
      isNewEquipment
      objectsToAdd
      itemsToAdd
      equipment {
        externalId
        name
      }
      technicalId
    }
  }
`

export const useMDREquipmentRequest = (externalId: string | undefined) => {
    const [resultData, setResultData] = useState<{ data: MDREquipmentRequest[]; loading: boolean }>({
        data: [],
        loading: true,
    })

    const { getAllItems: getAllData } = useGetAllResultsFunctionFromCustomClient<MDREquipmentRequest>(
        queryListAction,
        'listMDREquipmentRequest',
        'moc'
    )

    const filter: Filter<MDREquipmentRequest> = {
        externalId: {
            eq: externalId ?? '',
        },
    }

    useEffect(() => {
        if (externalId) {
            getAllData(filter).then((res: any) => {
                if (res.length == 0) {
                    setResultData({ data: [], loading: false })
                } else {
                    setResultData({ data: res, loading: false })
                }
            })
        }
    }, [externalId])

    return {
        loading: resultData.loading,
        data: resultData.data,
    }
}

export const useMDRPdpmRequest = (externalId: string | undefined) => {
    const [resultData, setResultData] = useState<{ data: MDREquipmentRequest[]; loading: boolean }>({
        data: [],
        loading: true,
    })

    const { getAllItems: getAllData } = useGetAllResultsFunctionFromCustomClient<MDREquipmentRequest>(
        pdpmQuery,
        'listMDRPdpmRequest',
        'moc'
    )

    const filter: Filter<MDREquipmentRequest> = {
        externalId: {
            eq: externalId ?? '',
        },
    }

    useEffect(() => {
        if (externalId) {
            getAllData(filter).then((res: any) => {
                if (res.length == 0) {
                    setResultData({ data: [], loading: false })
                } else {
                    setResultData({ data: res, loading: false })
                }
            })
        }
    }, [externalId])

    return {
        loading: resultData.loading,
        data: resultData.data,
    }
}

export const useMDRMdmRequest = (externalId: string | undefined) => {
    const [resultData, setResultData] = useState<{ data: MDREquipmentRequest[]; loading: boolean }>({
        data: [],
        loading: true,
    })

    const { getAllItems: getAllData } = useGetAllResultsFunctionFromCustomClient<MDREquipmentRequest>(
        mdmQuery,
        'listMDRMdmRequest',
        'moc'
    )

    const filter: Filter<MDREquipmentRequest> = {
        externalId: {
            eq: externalId ?? '',
        },
    }

    useEffect(() => {
        if (externalId) {
            getAllData(filter).then((res: any) => {
                if (res.length == 0) {
                    setResultData({ data: [], loading: false })
                } else {
                    setResultData({ data: res, loading: false })
                }
            })
        }
    }, [externalId])

    return {
        loading: resultData.loading,
        data: resultData.data,
    }
}
