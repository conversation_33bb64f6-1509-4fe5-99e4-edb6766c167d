import { Box } from '@mui/material'
import { Category, Status } from '@/app/common/models/action'
import { StackedChart } from '../StackedChart'
import { RoundChartWrapper } from '../RoundChart'
import { NoTranslate, TranslationContext, TranslationContextState } from '@celanese/celanese-ui'
import { useCallback, useContext, useEffect, useMemo, useState } from 'react'
import { AzureFunctionClient } from '@/app/common/clients/azure-function-client'
import { ClnButton, MatIcon } from '@celanese/ui-lib'

import { useDebounceFunction } from '@/app/common/hooks/general-functions/useDebounce'
import { FilterInfoProps } from '../../ActionTable/HomeFilter'
import { ActionStatusExternalIdClearEnum } from '@/app/common/enums/ActionItemStatusEnum'
import { toCamelCase } from '@/app/common/utils/transform-options-for-filter'
import { translate } from '@/app/common/utils/generate-translate'
import { getLocalUserSite } from '@celanese/celanese-ui'
import { UserRolesPermission } from '@celanese/celanese-sdk'

type SupervisorChartsProps = {
    categories: Category[]
    allStatus: Status[]
    filterInfo: FilterInfoProps
    client: AzureFunctionClient
    activeUser?: UserRolesPermission
    totalEmployees: number
    handleError: (err: any) => void
    setIsChart: (value: boolean) => void
    setEmployeesListById: (employeesIds: string[]) => void
}

export function SupervisorCharts({
    categories,
    allStatus,
    filterInfo,
    client,
    activeUser,
    totalEmployees,
    handleError,
    setIsChart,
    setEmployeesListById,
}: SupervisorChartsProps) {
    const { siteId: site } = getLocalUserSite() || {}
    const { locale } = useContext<TranslationContextState>(TranslationContext)

    const [actionsByStatusGroup, setActionsByStatusGroup] = useState<any[]>([])
    const [actionsByCategoryGroup, setActionsByCategoryGroup] = useState<any[]>([])
    const [actionsByAssignedGroup, setActionsByAssignedGroup] = useState<any[]>([])

    const [groupsEmployeesChart, setGroupsEmployeesChart] = useState<string[]>([])

    const [itemsPerPage, setItemsPerPage] = useState<number>(10)

    const [loadingEmployeeChart, setLoadingEmployeeChart] = useState<boolean>(false)
    const [loadingCategoryChart, setLoadingCategoryChart] = useState<boolean>(false)

    const statusChart = useMemo(() => {
        if (!actionsByStatusGroup?.length) {
            return []
        }

        const convertedStatusData = actionsByStatusGroup.map((item: any) => ({
            label: allStatus.find((x) => x.externalId === item.group.currentStatus?.externalId)?.name ?? '',
            value: item.aggregates[0].value,
        }))

        return convertedStatusData
            .sort((a: { label: string }, b: { label: string }) => a.label.localeCompare(b.label))
            .map((item: any) => {
                const camelLabel = toCamelCase(item.label)
                const fullLabel = `status.fullName.${camelLabel}`
                const translatedLabel = translate(fullLabel)

                return {
                    ...item,
                    label: translatedLabel,
                }
            })
    }, [actionsByStatusGroup, locale])

    const categoryChart = useMemo(() => {
        if (!actionsByCategoryGroup?.length) {
            return []
        }

        const convertedCategoryData = actionsByCategoryGroup.map((item: any) => {
            const camelLabel = toCamelCase(
                categories.find((x) => x.externalId === item.group?.category?.externalId)?.name ?? ''
            )
            const fullLabel = `category.${camelLabel}`
            const translatedLabel = translate(fullLabel)

            return {
                label: translatedLabel,
                value: item.aggregates?.[0].value,
            }
        })

        return convertedCategoryData.sort((a: { label: string }, b: { label: string }) =>
            a.label.localeCompare(b.label)
        )
    }, [actionsByCategoryGroup])

    const employeesChart = useMemo(() => {
        if (!actionsByAssignedGroup?.length) {
            setGroupsEmployeesChart([])
            setEmployeesListById([])
            return { convertedData: [], employeeExternalIds: [] }
        }

        const employeeExternalIds: any[] = []
        const statusMap: any = {}

        actionsByAssignedGroup.forEach((item: any) => {
            const externalId = item.group.assignedTo.externalId
            if (!employeeExternalIds.includes(externalId)) {
                employeeExternalIds.push(externalId)
            }

            const userId = externalId
            const status = item.group.currentStatus?.externalId
            const value = item.aggregates[0].value

            if (!statusMap[status]) {
                statusMap[status] = {}
            }

            statusMap[status][userId] = value
        })

        const convertedData = Object.keys(statusMap).map((status) => {
            return {
                label: translate(
                    `status.fullName.${toCamelCase(allStatus.find((x) => x.externalId === status)?.name ?? '')}`
                ),
                value: employeeExternalIds.map((unitId: any) => statusMap[status][unitId] || 0),
            }
        })

        return { convertedData: convertedData, employeeExternalIds: employeeExternalIds }
    }, [actionsByAssignedGroup, locale, allStatus])

    const paginetedEmployeeChart = () => {
        if (!loadingEmployeeChart && totalEmployees >= itemsPerPage) {
            setItemsPerPage(itemsPerPage + 10)
        }
    }

    const fetchChartsEmployees = useCallback(async () => {
        try {
            const response = await client.getActionsGroupedBy({
                activeUserEmail: activeUser?.email,
                groupBy: ['assignedTo', 'currentStatus'],
                itemsPerPage,
                filters: {
                    reportingSiteExternalId: site,
                    dueDateGte: filterInfo?.dueDateGte ?? null,
                    dueDateLt: filterInfo?.dueDateLt ?? null,
                    reportingUnitExternalIds: filterInfo.reportingUnitExternalIds,
                    reportingLocationExternalIds: filterInfo.reportingLocationExternalIds,
                    categoryExternalIds: filterInfo.categoryExternalId,
                    statusExternalIds:
                        filterInfo && filterInfo?.statusExternalIds && filterInfo?.statusExternalIds?.length > 0
                            ? filterInfo?.statusExternalIds
                            : Object.values(ActionStatusExternalIdClearEnum),
                    updateStatus: filterInfo.updateStatusDate,
                    assigneeExternalIds: filterInfo.assignedToExternalId,
                    onlyPrivate: filterInfo.onlyPrivate,
                    sourceEventTitleEq: filterInfo.sourceEventTitleEq ?? undefined,
                },
            })

            setActionsByAssignedGroup(response.items ?? {})
        } catch (err) {
            handleError(err)
        } finally {
            setLoadingEmployeeChart(false)
        }
    }, [filterInfo, itemsPerPage])

    const fetchCharts = useCallback(async () => {
        try {
            const filters = {
                reportingSiteExternalId: site,
                dueDateGte: filterInfo?.dueDateGte ?? null,
                dueDateLt: filterInfo?.dueDateLt ?? null,
                reportingUnitExternalIds: filterInfo.reportingUnitExternalIds,
                reportingLocationExternalIds: filterInfo.reportingLocationExternalIds,
                categoryExternalIds: filterInfo.categoryExternalId,
                statusExternalIds:
                    filterInfo && filterInfo?.statusExternalIds && filterInfo?.statusExternalIds?.length > 0
                        ? filterInfo?.statusExternalIds
                        : Object.values(ActionStatusExternalIdClearEnum),
                updateStatus: filterInfo.updateStatusDate,
                assigneeExternalIds: filterInfo.assignedToExternalId,
                onlyPrivate: filterInfo.onlyPrivate,
                sourceEventTitleEq: filterInfo.sourceEventTitleEq ?? undefined,
            }

            const byStatusPromise = client.getActionsGroupedBy({
                activeUserEmail: activeUser?.email,
                groupBy: ['currentStatus'],
                filters,
            })

            const byCategoryPromise = client.getActionsGroupedBy({
                activeUserEmail: activeUser?.email,
                groupBy: ['category'],
                filters,
            })

            const [byStatusResponse, byCategoryResponse] = await Promise.all([byStatusPromise, byCategoryPromise])

            setActionsByStatusGroup(byStatusResponse.items ?? {})
            setActionsByCategoryGroup(byCategoryResponse.items ?? {})
        } catch (err) {
            handleError(err)
        } finally {
            setLoadingCategoryChart(false)
        }
    }, [filterInfo])

    const debouncedFetchEmployeeSupervisor = useCallback(useDebounceFunction(fetchChartsEmployees, 500), [
        fetchChartsEmployees,
    ])
    const debouncedFetchChartSupervisor = useCallback(useDebounceFunction(fetchCharts, 500), [fetchCharts])

    useEffect(() => {
        if (loadingEmployeeChart) return

        setLoadingEmployeeChart(true)
        debouncedFetchEmployeeSupervisor()
    }, [fetchChartsEmployees])

    useEffect(() => {
        if (loadingCategoryChart) return

        setLoadingCategoryChart(true)
        debouncedFetchChartSupervisor()
    }, [fetchCharts])

    useEffect(() => {
        const processEmployeeNames = async () => {
            const formattedNames = employeesChart.employeeExternalIds.map((user) => {
                const nameParts = user?.split('_')[1]?.split('@')[0]?.split('.')
                const firstName = nameParts?.[0]?.charAt(0)?.toUpperCase() + nameParts?.[0]?.slice(1)
                const lastName = nameParts?.[1]?.charAt(0)?.toUpperCase() + nameParts?.[1]?.slice(1)
                return `${lastName}, ${firstName}`
            })

            setGroupsEmployeesChart(formattedNames)
            setEmployeesListById(employeesChart.employeeExternalIds)
        }

        if (employeesChart.employeeExternalIds?.length) {
            processEmployeeNames()
        }
    }, [employeesChart])

    return (
        <Box sx={{ width: '100%', display: 'flex', flexDirection: 'column', gap: '1rem' }} id={'charts-supervisor'}>
            <Box sx={{ width: '100%', display: 'flex', justifyContent: 'flex-end' }}>
                <ClnButton
                    label={translate('table.listView')}
                    variant="outlined"
                    onClick={() => {
                        setIsChart(false)
                    }}
                    startIcon={<MatIcon icon="lists" />}
                />
            </Box>
            <Box
                display="flex"
                flexWrap="wrap"
                justifyContent="space-between"
                sx={{
                    height: '100%',
                    width: '100%',
                    gap: '1rem',
                }}
            >
                <Box flex="1 1 450px">
                    <NoTranslate>
                        <StackedChart
                            key={'employees-chart-supervisor'}
                            dataChart={employeesChart.convertedData}
                            groupsChart={groupsEmployeesChart}
                            type="horizontal"
                            width={'100%'}
                            heightContainer={339}
                            label={translate('dashboards.charts.listOfEmployees')}
                            loading={loadingEmployeeChart}
                            onReachEnd={paginetedEmployeeChart}
                            legendPosition={'top'}
                        />
                    </NoTranslate>
                </Box>
                <Box flex="1 1 300px">
                    <NoTranslate>
                        <RoundChartWrapper
                            key={'status-chart-supervisor'}
                            dataChart={statusChart}
                            type="donut"
                            height={250}
                            width={'100%'}
                            label={translate('dashboards.charts.byStatus')}
                            loading={loadingCategoryChart}
                        />
                    </NoTranslate>
                </Box>
                <Box flex="1 1 300px">
                    <NoTranslate>
                        <RoundChartWrapper
                            key={'category-chart-supervisor'}
                            dataChart={categoryChart}
                            type="pie"
                            height={250}
                            width={'100%'}
                            label={translate('dashboards.charts.actionItemsCategories')}
                            loading={loadingCategoryChart}
                        />
                    </NoTranslate>
                </Box>
            </Box>
        </Box>
    )
}

export default SupervisorCharts
