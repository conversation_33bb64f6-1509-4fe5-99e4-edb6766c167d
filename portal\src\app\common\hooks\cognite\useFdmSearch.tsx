import { useCallback, useState } from 'react'
import { useCognite } from './useCognite'

export type SearchHookResult = [SearchFunction, SearchState]

export type ViewReference = {
    space: string
    externalId: string
    version: string
}

export type SearchFunctionParams = {
    view: ViewReference
    query?: string
    instanceType?: any
    properties?: string[]
    filter?: any
    limit?: number
}

export type SearchFunctionPromise = {
    items: any[]
}

export type SearchFunction = (params: SearchFunctionParams) => Promise<SearchFunctionPromise>

export type SearchState = {
    loading: boolean
    called: boolean
    error: any
}

export function useFdmSearch(): SearchHookResult {
    const { fdmClient: client } = useCognite()
    const [error, setError] = useState<any | undefined>()
    const [loading, setLoading] = useState<boolean>(false)
    const [called, setCalled] = useState<boolean>(false)

    const queryFunction = useCallback(
        ({ view, query, instanceType, properties, filter, limit }: SearchFunctionParams) => {
            setLoading(true)
            setCalled(true)
            return client
                .searchNodesOrEdges({
                    view: {
                        ...view,
                        type: "view"
                    },
                    query,
                    instanceType,
                    properties,
                    filter,
                    limit,
                })
                .catch((error) => setError(error))
                .finally(() => setLoading(false))
        },
        [client]
    )

    return [queryFunction, { error, loading, called }]
}
