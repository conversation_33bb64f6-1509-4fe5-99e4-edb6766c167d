import { useCallback } from 'react'
import { useFdmDeleter, useFdmMutation } from '..'
import { CategoryConfiguration } from '../../models/category-configuration'

export const useCategoryConfigurationMutations = () => {
    const [categoriesConfigurationMutation, categoriesConfigurationMutationStatus] =
        useFdmMutation<CategoryConfiguration>('CategoriesConfiguration', false)
    const [categoriesConfigurationDeleter] = useFdmDeleter()

    const createOrUpdateCategoryConfiguration = useCallback(
        async (categoriesConfiguration: CategoryConfiguration) => {
            const categoriesConfigurationMutationResult = await categoriesConfigurationMutation(
                [categoriesConfiguration],
                []
            )

            return {
                ok: categoriesConfigurationMutationResult.ok,
                data: categoriesConfigurationMutationResult.data,
                error: categoriesConfigurationMutationResult.error,
            }
        },
        [categoriesConfigurationMutation]
    )

    const deleteCategoryConfigurations = useCallback(
        async (data: CategoryConfiguration[]) => {
            const dataToRequest = data.map((x) => x.externalId)

            return await categoriesConfigurationDeleter({ externalIds: dataToRequest })
        },
        [categoriesConfigurationDeleter]
    )

    return [
        createOrUpdateCategoryConfiguration,
        deleteCategoryConfigurations,
        categoriesConfigurationMutationStatus,
    ] as const
}
