import { useEffect, useState } from 'react'
import { Action } from '../../models/action'
import { CategoryConfiguration } from '../../models/category-configuration'
import { useGraphqlQuery } from '../'
import { gql } from '@apollo/client'

const buildCategoryConfigQuery = (externalId?: string): string => {
    const filters: string[] = []
    let queryFilter = '{ }'

    if (externalId) {
        filters.push(`{ siteSpecificCategory: { externalId: { eq: "${externalId}" } } }`)
    }

    if (filters.length > 0) {
        queryFilter = `{ and: [ ${filters.join(', ')} ] }`
    }

    return `
        query GetCategoriesConfiguration {
            listCategoriesConfiguration(
                filter: ${queryFilter}
                , first: 1
            ) {
                items {
                    externalId
                    siteSpecificCategory {
                        externalId
                    }
                }
            }
        }
    `
}

const buildActionQuery = (externalId?: string): string => {
    const filters: string[] = []

    if (externalId) {
        filters.push(`{ siteSpecificCategory: { externalId: { eq: "${externalId}" } } }`)
    }

    filters.push(`{ recurrenceInstance: { externalId: { isNull: false } } }`)

    const queryFilter = `{ and: [ ${filters.join(', ')} ] }`

    return `
        query GetAction {
            listAction(
                filter: ${queryFilter}
                , first: 1
            ) {
                items {
                    externalId
                    siteSpecificCategory {
                        externalId
                    }
                }
            }
        }
    `
}

export const useRecurrenceAndCategoryConfigBySpecificCategory = (externalId?: string) => {
    const [resultData, setResultData] = useState<{ data: boolean; loading: boolean }>({
        data: false,
        loading: true,
    })

    const categoryConfigQuery = buildCategoryConfigQuery(externalId)
    const { data: fdmDataConfig, loading: configLoading } = useGraphqlQuery<CategoryConfiguration>(
        gql(categoryConfigQuery),
        'listCategoriesConfiguration',
        {}
    )

    const queryRecurrence = buildActionQuery(externalId)
    const { data: fdmDataRecurrence, loading: recurrenceLoading } = useGraphqlQuery<Action>(
        gql(queryRecurrence),
        'listAction',
        {}
    )

    useEffect(() => {
        const isConfigNotEmpty = fdmDataConfig?.length > 0
        const isRecurrenceNotEmpty = fdmDataRecurrence?.length > 0

        setResultData({
            data: isConfigNotEmpty || isRecurrenceNotEmpty,
            loading: !(isConfigNotEmpty || isRecurrenceNotEmpty) && (configLoading || recurrenceLoading),
        })
    }, [fdmDataConfig, fdmDataRecurrence, configLoading, recurrenceLoading])

    return {
        loading: resultData.loading,
        hasRecurrenceOrCategoryConfig: resultData.data,
    }
}
