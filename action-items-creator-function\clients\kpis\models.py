from datetime import date
from typing import Optional

from pydantic import BaseModel, ConfigDict, computed_field
from pydantic.alias_generators import to_camel

from clients.category_configuration.models import (
    CategoryConfigurationByFilterResult,
)
from clients.core.constants import WAS_SITE_EXTERNAL_ID
from clients.core.models import Node

from .constants import (
    USER_AZURE_ATTRIBUTE_PREFIX,
    ActionStatusEnum,
)


class ActionFilterConfig(BaseModel):
    """
    Configuration for filtering action items based on various criteria.

    Attributes:
        due_date (Optional[date]): The due date for filtering action items.
        status (Optional[List[str]]): The list of statuses to filter by.
        user_reporting_units_ids (Optional[List[str]]): The list of reporting unit IDs to filter by.
        has_permission_to_extend (Optional[bool]): Whether the user has permission to extend the due date.
        site_external_ids:(Optional[list[str]]): The list of reporting site external ids with has permission to extend the due date.
        user_roles_ids (Optional[List[str]]): The list of user roles to filter by.
        categories_configuration (Optional[List[CategoryConfigurationByFilterResult]]):
            Configuration for filtering by categories.

    """

    due_date: Optional[date] = None
    status: Optional[list[str]] = None
    user_reporting_units_ids: Optional[list[str]] = None
    has_permission_to_extend: Optional[bool] = None
    site_external_ids: Optional[list[str]] = None
    user_roles_ids: Optional[list[str]] = None
    categories_configuration: Optional[list[CategoryConfigurationByFilterResult]] = None


class RelatedToMeConfig(BaseModel):
    """
    Configuration for filtering actions related to a specific user.

    Attributes:
        user_external_id (str): The external ID of the user.
        user_reporting_units_ids (Optional[List[str]]): List of the user's reporting units.
        due_date (Optional[date]): The due date to filter actions by.
        action_by_pending_verification_status (Optional[List[str]]): Statuses for actions pending verification.
        action_by_pending_approval_status (Optional[List[str]]): Statuses for actions pending approval.
        action_by_owner_status (Optional[List[str]]): Statuses for actions owned by the user.
        action_by_assigned_to_status (Optional[List[str]]): Statuses for actions assigned to the user.
        user_roles_ids (Optional[List[str]]): User role IDs for permission-based filtering.
        categories_configuration (Optional[List[CategoryConfigurationByFilterResult]]): Category filter configuration.
        permissions_extend (bool): Whether the user has permission to extend due dates.
        permissions_reassing (bool): Whether the user has permission to reassign actions.
        extension_approval_site_external_ids (list[str]): e list of reporting site external ids with has permission to extend due dates.
        reassignment_approval_site_external_ids (list[str]): e list of reporting site external ids with has permission to reassign actions.

    """

    user_external_id: str
    user_reporting_units_ids: Optional[list[str]] = None
    due_date: Optional[date] = None
    action_by_pending_verification_status: Optional[list[str]] = None
    action_by_pending_approval_status: Optional[list[str]] = None
    action_by_owner_status: Optional[list[str]] = None
    action_by_assigned_to_status: Optional[list[str]] = None
    user_roles_ids: Optional[list[str]] = None
    categories_configuration: Optional[list[CategoryConfigurationByFilterResult]] = None
    permissions_extend: bool = False
    permissions_reassing: bool = False
    extension_approval_site_external_ids: list[str] | None = None
    reassignment_approval_site_external_ids: list[str] | None = None


class ActionResult(Node):
    """
    Represents an action item with details about responsibility, current status, due date, reporting location, and categorization.

    Attributes:
        owner (Node | None): The user who owns or created the action.
        assigned_to (Node | None): The user to whom the action is assigned.
        current_status (Node): The current status of the action.
        display_due_date (date | None): The due date for completing the action.
        reporting_site (Node): The site where the action was reported.
        reporting_unit (Node | None): The business unit responsible for the action.
        category (Node | None): The main category of the action.
        sub_category (Node | None): The subcategory of the action.
        site_specific_category (Node | None): A category specific to the site.

    """

    owner: Node | None = None
    assigned_to: Node | None = None
    current_status: Node
    display_due_date: date | None = None
    reporting_site: Node
    reporting_unit: Node | None = None
    category: Node | None = None
    sub_category: Node | None = None
    site_specific_category: Node | None = None

    def evaluate(
        self,
        evaluate_config: ActionFilterConfig,
    ) -> bool:
        """
        Evaluate if the action item meets the conditions based on the provided evaluation configuration.

        Args:
            evaluate_config (ActionFilterConfig): The configuration containing the evaluation parameters.

        Returns:
            bool: True if the action item meets the conditions, False otherwise.

        """

        def has_extension_permission() -> bool | None:
            """Check if the user has permission to extend the due date based on category configurations."""
            if (
                evaluate_config.status == [ActionStatusEnum.DUE_DATE_EXTENSION_PERIOD]
                and evaluate_config.categories_configuration
            ):
                for config in evaluate_config.categories_configuration:
                    if (
                        config.category == self.category
                        and config.action_item_sub_category == self.sub_category
                        and config.site_specific_category == self.site_specific_category
                        and config.default_extension_approver_role
                    ):
                        return (
                            evaluate_config.user_roles_ids is not None
                            and config.default_extension_approver_role.external_id
                            in evaluate_config.user_roles_ids
                        )

                return None
            return None

        def has_general_permission() -> bool:
            """Check if the user has general permission to evaluate."""
            site_external_ids = [
                site
                for site in (evaluate_config.site_external_ids or [])
                if site != WAS_SITE_EXTERNAL_ID
            ]
            has_permission = (
                evaluate_config.has_permission_to_extend is None
                or evaluate_config.has_permission_to_extend
            )

            reporting_unit_allowed = not evaluate_config.user_reporting_units_ids or (
                self.reporting_unit is not None
                and self.reporting_unit.external_id
                in evaluate_config.user_reporting_units_ids
            )

            was_site_selected = WAS_SITE_EXTERNAL_ID == self.reporting_site.external_id

            has_valid_site = (
                reporting_unit_allowed
                if was_site_selected
                else not site_external_ids
                or self.reporting_site.external_id in site_external_ids
            )

            return has_permission and (reporting_unit_allowed or has_valid_site)

        is_due_date_valid = evaluate_config.due_date is None or (
            self.display_due_date is not None
            and self.display_due_date <= evaluate_config.due_date
        )

        is_status_valid = (
            not evaluate_config.status
            or self.current_status.external_id in evaluate_config.status
        )

        is_site_valid = (
            not evaluate_config.site_external_ids
            or self.reporting_site.external_id in evaluate_config.site_external_ids
        )

        extension_permission = has_extension_permission()
        if extension_permission is False:
            return False
        if extension_permission is True:
            return is_due_date_valid and is_status_valid and is_site_valid

        has_permission = has_general_permission()
        return is_due_date_valid and is_status_valid and has_permission

    @staticmethod
    def apply_filter(
        actions: list["ActionResult"],
        filter_config: ActionFilterConfig,
    ) -> list["ActionResult"]:
        """
        Apply filter on the actions based on the provided filter configuration.

        Args:
            actions (list["ActionResult"]): The list of actions to be filtered.
            filter_config (ActionFilterConfig): Configuration object containing the filter parameters.

        Returns:
            list["ActionResult"]: The filtered list of actions.

        """
        return [
            action
            for action in actions
            if action.evaluate(
                ActionFilterConfig(
                    due_date=filter_config.due_date,
                    status=filter_config.status,
                    user_reporting_units_ids=filter_config.user_reporting_units_ids,
                    has_permission_to_extend=filter_config.has_permission_to_extend,
                    site_external_ids=filter_config.site_external_ids,
                    user_roles_ids=filter_config.user_roles_ids,
                    categories_configuration=filter_config.categories_configuration,
                ),
            )
        ]


class GetActionResponse(BaseModel):
    """
    Represents the response model for retrieving action items.

    This model categorizes actions into three types:
    - Pending verification
    - Pending approval
    - General actions

    It also provides computed properties and utility methods for counting and filtering
    actions by ownership, assignment, approval status, verification status, and more.

    Attributes:
        actions_by_pending_verification (list[ActionResult]):
            List of actions currently awaiting verification.
        actions_by_pending_approval (list[ActionResult]):
            List of actions currently awaiting approval.
        general_actions (list[ActionResult]):
            List of actions that do not fall into the other two categories.

    """

    actions_by_pending_verification: list[ActionResult]
    actions_by_pending_approval: list[ActionResult]
    general_actions: list[ActionResult]

    @computed_field
    @property
    def _all_actions(self) -> list[ActionResult]:
        return (
            self.actions_by_pending_verification
            + self.actions_by_pending_approval
            + self.general_actions
        )

    @computed_field
    @property
    def _actions_by_assigned_to_and_status(
        self,
    ) -> dict[str, dict[str, set[ActionResult]]]:
        result: dict[str, dict[str, set[ActionResult]]] = {}
        for action in self._all_actions:
            if not action.assigned_to:
                continue
            entry_assigned = result.get(action.assigned_to.external_id, {})
            status_assigned = entry_assigned.get(
                action.current_status.external_id,
                set(),
            )
            status_assigned.add(action)
            entry_assigned[action.current_status.external_id] = status_assigned
            result[action.assigned_to.external_id] = entry_assigned
        return result

    @computed_field
    @property
    def _actions_by_owner_and_status(
        self,
    ) -> dict[str, dict[str, set[ActionResult]]]:
        """
        Returns a dictionary of actions grouped by their owner and status.

        This computed property returns a nested dictionary where the top-level key is the owner's external ID,
        and the second-level key is the action's current status external ID. The value is a set of ActionResult objects.

        This structure allows efficient lookup of actions by owner and their respective statuses.

        Returns:
            dict[str, dict[str, set[ActionResult]]]: A dictionary where the top-level key is the owner's external ID,
                                                    the second-level key is the status external ID,
                                                    and the value is a set of actions associated with that status.

        """
        result: dict[str, dict[str, set[ActionResult]]] = {}
        for action in self._all_actions:
            if not action.owner:
                continue
            entry_owner = result.get(action.owner.external_id, {})
            status_owner = entry_owner.get(action.current_status.external_id, set())
            status_owner.add(action)
            entry_owner[action.current_status.external_id] = status_owner
            result[action.owner.external_id] = entry_owner
        return result

    def count_assigned_to(
        self,
        user_external_id: str,
        status: list[str] | None = None,
    ) -> int:
        """Count the number of actions assigned to a user based on the specified statuses."""
        user_azure_attribute = USER_AZURE_ATTRIBUTE_PREFIX + user_external_id

        entry = self._actions_by_assigned_to_and_status.get(user_azure_attribute, {})

        return (
            sum(len(entry.get(status_item, {})) for status_item in status)
            if status
            else sum(len(item) for item in entry.values())
        )

    def count_owner(self, user_external_id: str, status: list[str]) -> int:
        """Count the number of actions owned by a user based on the specified statuses."""
        user_azure_attribute = USER_AZURE_ATTRIBUTE_PREFIX + user_external_id

        entry = self._actions_by_owner_and_status.get(user_azure_attribute, {})

        return sum(len(entry.get(status_item, {})) for status_item in status)

    def count_pending_approval(self, status: list[str] | None = None) -> int:
        """Count the number of actions pending approval, optionally filtering by status."""
        filter_config = ActionFilterConfig(
            status=status,
        )
        return len(
            ActionResult.apply_filter(self.actions_by_pending_approval, filter_config),
        )

    def count_pending_verification(self, status: list[str]) -> int:
        """
        Count the number of actions pending verification, filtered by the provided statuses.

        This method applies a filter to the list of actions pending verification based on the provided statuses.

        Args:
            status (list[str]): The list of action statuses to filter by.

        Returns:
            int: The count of actions pending verification that match the specified statuses.

        """
        filter_config = ActionFilterConfig(
            status=status,
        )
        return len(
            ActionResult.apply_filter(
                self.actions_by_pending_verification,
                filter_config,
            ),
        )

    def count_general_actions(
        self,
        status: list[str],
        user_reporting_units_ids: list[str] | None,
        site_external_ids: list[str] | None = None,
    ) -> int:
        """
        Count the number of general actions, filtered by status and reporting unit IDs.

        This method filters the list of general actions by the provided status and user reporting unit IDs.

        Args:
            status (list[str]): The list of action statuses to filter by.
            user_reporting_units_ids (list[str] | None, optional): The list of reporting unit IDs to filter by.

        Returns:
            int: The count of general actions that match the specified filters.

        """
        filter_config = ActionFilterConfig(
            status=status,
            user_reporting_units_ids=user_reporting_units_ids,
            site_external_ids=site_external_ids,
        )
        return len(
            ActionResult.apply_filter(self.general_actions, filter_config),
        )

    def count_related_to_me(self, config: RelatedToMeConfig) -> int:
        """
        Count the number of actions related to a user, considering various filters.

        Args:
            config (RelatedToMeConfig): Configuration for filtering actions related to the user.

        Returns:
            int: The total number of actions related to the user, considering the provided filters.

        """
        filter_config_approval = ActionFilterConfig(
            due_date=config.due_date,
            status=config.action_by_pending_approval_status,
        )
        filter_config_verification = ActionFilterConfig(
            due_date=config.due_date,
            status=config.action_by_pending_verification_status,
        )
        result = set(
            ActionResult.apply_filter(
                self.actions_by_pending_approval,
                filter_config_approval,
            )
            + ActionResult.apply_filter(
                self.actions_by_pending_verification,
                filter_config_verification,
            ),
        )
        user_azure_attribute = USER_AZURE_ATTRIBUTE_PREFIX + config.user_external_id

        for action in self.general_actions:
            can_process = (
                (
                    action.owner
                    and action.owner.external_id == user_azure_attribute
                    and action.evaluate(
                        ActionFilterConfig(
                            due_date=config.due_date,
                            status=config.action_by_owner_status,
                        ),
                    )
                )
                or (
                    action.assigned_to
                    and action.assigned_to.external_id == user_azure_attribute
                    and action.evaluate(
                        ActionFilterConfig(
                            due_date=config.due_date,
                            status=config.action_by_assigned_to_status,
                        ),
                    )
                )
                or (
                    action.evaluate(
                        ActionFilterConfig(
                            due_date=config.due_date,
                            status=[ActionStatusEnum.DUE_DATE_EXTENSION_PERIOD],
                            user_reporting_units_ids=config.user_reporting_units_ids,
                            has_permission_to_extend=config.permissions_extend,
                            user_roles_ids=config.user_roles_ids,
                            categories_configuration=config.categories_configuration,
                            site_external_ids=config.extension_approval_site_external_ids,
                        ),
                    )
                )
                or (
                    config.permissions_reassing
                    and action.evaluate(
                        ActionFilterConfig(
                            due_date=config.due_date,
                            status=[ActionStatusEnum.REASSIGNMENT_PERIOD],
                            user_reporting_units_ids=config.user_reporting_units_ids,
                            site_external_ids=config.reassignment_approval_site_external_ids,
                        ),
                    )
                )
            )

            if can_process:
                result.add(action)

        return len(result)

    def count_my_extends(
        self,
        status: list[str],
        user_reporting_units_ids: list[str] | None,
        has_permission_to_extend: bool | None = None,
        user_roles_ids: list[str] | None = None,
        categories_configuration: (
            list[CategoryConfigurationByFilterResult] | None
        ) = None,
        site_external_ids: list[str] | None = None,
    ) -> int:
        """
        Count the number of actions that are in the "extend" state based on the given filter configuration.

        This method filters the `general_actions` list using the provided filter criteria (status,
        user reporting units, permission to extend, user roles, and categories configuration)
        and returns the count of actions that match the filter.

        Args:
            status (list[str]): The list of statuses to filter the actions by.
            user_reporting_units_ids (list[str] | None): The list of reporting units IDs to filter the actions by.
            has_permission_to_extend (bool | None): Indicates whether the user has permission to extend the due date.
            user_roles_ids (list[str] | None): The list of user roles to filter the actions by.
            categories_configuration (list[CategoryConfigurationByFilterResult] | None): The category configurations for filtering actions.
            site_external_ids (list[str] | None): The list of reporting site external ids with permissions.

        Returns:
            int: The count of actions that match the filter criteria.

        """
        filter_config = ActionFilterConfig(
            status=status,
            user_reporting_units_ids=user_reporting_units_ids,
            has_permission_to_extend=has_permission_to_extend,
            user_roles_ids=user_roles_ids,
            categories_configuration=categories_configuration,
            site_external_ids=site_external_ids,
        )
        return len(
            ActionResult.apply_filter(self.general_actions, filter_config),
        )


class KpiResponse(BaseModel):
    """
    Represents the response containing key performance indicator (KPI) metrics related to action items.

    This class holds various metrics about action items, such as total items, items assigned to the user,
    pending approvals, pending verifications, overdue actions, and more.

    Attributes:
        total_action_items (int): The total number of action items.
        total_action_closed (int): The total number of closed action items.
        assigned_to_me (int): The number of action items assigned to the current user.
        assigned_to_me_closed (int): The number of closed action items assigned to the current user.
        related_to_me (int): The number of action items related to the current user.
        pending_approvals (int): The number of pending approvals.
        pending_verifications (int): The number of pending verifications.
        overdue (int): The number of overdue action items.
        my_approvals (int): The number of approvals that the current user is responsible for.
        my_verifications (int): The number of verifications that the current user is responsible for.
        my_challenges (int): The number of challenges the current user is involved in.
        my_extends (int): The number of extension requests the current user is involved in.
        my_reassignment (int): The number of reassignments the current user is involved in.

    """

    model_config = ConfigDict(
        alias_generator=to_camel,
        populate_by_name=True,
        from_attributes=True,
    )

    total_action_items: int
    total_action_closed: int

    assigned_to_me: int
    assigned_to_me_closed: int
    related_to_me: int
    pending_approvals: int
    pending_verifications: int
    overdue: int

    my_approvals: int
    my_verifications: int
    my_challenges: int
    my_extends: int
    my_reassignment: int

    @staticmethod
    def empty() -> "KpiResponse":
        """
        Create and returns an empty KpiResponse object with all values set to zero.

        This method is useful for initializing a KpiResponse object when no data is available
        or when you want to reset the KPI metrics.

        Returns:
            KpiResponse: A new instance of KpiResponse with all metrics set to zero.

        """
        return KpiResponse(
            total_action_items=0,
            total_action_closed=0,
            assigned_to_me=0,
            assigned_to_me_closed=0,
            related_to_me=0,
            pending_approvals=0,
            pending_verifications=0,
            overdue=0,
            my_approvals=0,
            my_verifications=0,
            my_challenges=0,
            my_extends=0,
            my_reassignment=0,
        )
