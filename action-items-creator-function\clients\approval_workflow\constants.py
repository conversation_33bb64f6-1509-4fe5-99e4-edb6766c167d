from enum import StrEnum


class HistoryActionUser(StrEnum):
    COMPLETED = "completedBy"
    APPROVED = "approvedBy"
    APPROVED_REJECTED = "approvalRejected"
    VERIFICATION = "verifyBy"
    VERIFICATION_REJECTED = "verificationRejected"
    REASSIGN = "reassignBy"
    REASSIGN_APPROVED = "reassignApprove"
    REASSIGN_REJECTED = "reassignRejected"
    EXTENSION = "extensionBy"
    EXTENSION_APPROVED = "extensionApprove"
    EXTENSION_REJECTED = "extensionRejected"
    CHALLENGE = "challengeBy"
    CHALLENGE_APPROVED = "challengeApprove"
    CANCELLED = "cancelledBy"
    DELETED = "deletedBy"
    EDITED = "editedBy"


class WorkflowStatus(StrEnum):
    APPROVED = "Approved"
    REJECTED = "Rejected"

class ApprovalWorkflowConsentType(StrEnum):
    USER = "APWCT-User"

class ApprovalWorkflowCondition(StrEnum):
    AND = "APWCO-AND"