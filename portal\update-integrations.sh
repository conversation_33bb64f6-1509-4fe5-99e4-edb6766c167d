cd ../..

if [ -d "UserManagement" ]; then
    cd "UserManagement"
    git checkout dev
    upToDate=$(git status -uno | sed -n 's/.*\(up to date\).*/\1/p')

    if [$upToDate -ne 'up to date']; then
        git pull origin dev

        if [ -d ".venv" ]; then
            rm -rf .venv
        fi

        python -m venv .venv
    fi

    cd ..
    echo "UserManagement update with dev"
else
    git clone https://<EMAIL>/CelaneseCorporation/Digital%20Plant/_git/UserManagement
    echo "UserManagement cloned"
fi

if [ -d "Apps Translation" ]; then
    cd "Apps Translation"
    git checkout dev
    git pull origin dev
    cd ..
    echo "Apps Translation update with dev"
else
    git clone https://<EMAIL>/CelaneseCorporation/Digital%20Plant/_git/Apps%20Translation
    echo "Apps Translation cloned"
fi

if [ -d "Notifications" ]; then
    cd "Notifications"
    git checkout dev
    git pull origin dev
    cd ..
    echo "Notifications update with dev"
else
    git clone https://<EMAIL>/CelaneseCorporation/Digital%20Plant/_git/Notifications
    echo "Notifications cloned"
fi

if [ -d "UserManagement" ]; then
    cd "UserManagement"
    cd UserManagement-API
    uvicorn app.main:app --reload -D
    cd ../..
    echo "UserManagement running"
else
    echo "UserManagement not cloned yet"
fi

if [ -d "Apps Translation" ]; then
    cd "Apps Translation"

    if [ -d ".venv" ]; then
        rm -rf .venv
    fi

    python -m venv .venv
    .\.venv\Scripts\activate
    pip install -r requirements.txt
    uvicorn app.main:app --reload --port 8001 -D
    cd ..
    echo "Apps Translation running"
else
    echo "Apps Translation not cloned yet"
fi

if [ -d "Notifications" ]; then
    cd "Notifications"
    cd notification-api

    if [ -d ".venv" ]; then
        rm -rf .venv
    fi

    python -m venv .venv
    .\.venv\Scripts\activate
    pip install -r requirements.txt
    uvicorn app.main:app --reload --port 8002 -D
    cd ../..
    echo "Notifications running"
else
    echo "Notifications not cloned yet"
fi