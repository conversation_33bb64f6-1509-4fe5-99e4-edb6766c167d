import { useCallback } from 'react'
import { AzureFunctionClient } from '../clients/azure-function-client'

export const useDownloadFile = () => {
    const azureFunctionClient = new AzureFunctionClient()

    const downloadFiles = useCallback(
        async (fileIds: string[] | number[]): Promise<void> => {
            const response = await azureFunctionClient.getDownloadUrls({
                externalIds: fileIds,
            })

            if (response?.urls) {
                fileIds.forEach((fileId) => {
                    const downloadUrl = response.urls[fileId]
                    if (downloadUrl) {
                        const anchor = document.createElement('a')
                        anchor.href = downloadUrl
                        anchor.target = '_blank'
                        document.body.appendChild(anchor)
                        anchor.click()
                        anchor.remove()
                    }
                })
            }
        },
        [azureFunctionClient]
    )

    return { downloadFiles }
}
