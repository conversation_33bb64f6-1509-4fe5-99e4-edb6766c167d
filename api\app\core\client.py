from typing import Any

from cognite.client import CogniteClient
from cognite.client.data_classes.data_modeling import DataModelId
from fastapi import Depends

from app.core.constants.data_models import DataModelExternalId
from app.core.constants.spaces import DataModelSpace
from app.core.methods.run_async import run_async
from app.infra.settings import Settings

from .dependencies import get_cognite_client, get_settings


class BaseClient:
    """Represents a base client with a variety of helper methods."""

    def __init__(
        self,
        cognite_client: CogniteClient = Depends(get_cognite_client),
        settings: Settings = Depends(get_settings),
        data_model_id: DataModelId | None = None,
    ) -> None:
        self.cognite_client = cognite_client
        self.settings = settings
        self.data_model_id = data_model_id or DataModelId(
            DataModelSpace.ACTION_ITEM_MANAGEMENT,
            DataModelExternalId.ACTION_ITEM_MANAGEMENT,
            settings.cognite_action_item_management_data_model_version,
        )

    async def query(
        self,
        query: str,
        variables: dict[str, Any] | None = None,
    ) -> dict[str, Any]:
        """Call cognite_client.data_modeling.graphql.query."""
        return await run_async(
            self.cognite_client.data_modeling.graphql.query,
            self.data_model_id,
            query,
            variables,
        )
