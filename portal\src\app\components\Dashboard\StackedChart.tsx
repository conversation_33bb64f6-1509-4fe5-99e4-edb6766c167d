import { useMediaQuery, useTheme } from '@mui/material'
import { BarChart } from './charts/BarChart'
import LoaderCircular from '../Loader'
import { ClnPanel } from '@celanese/ui-lib'
import GenericFieldTitle from '../FieldsComponent/GenericFieldTitle'

type Props = {
    key?: string
    type: 'horizontal' | 'vertical'
    dataChart: any
    groupsChart: string[]
    label: string
    height?: number
    heightContainer?: number
    width?: number | string
    loading?: boolean
    legendPosition?: 'top' | 'right' | 'bottom' | 'left'
    onReachEnd?: () => void
}

export function StackedChart({
    key,
    dataChart,
    legendPosition,
    groupsChart,
    label,
    type,
    height,
    heightContainer,
    width,
    loading,
    onReachEnd,
}: Props) {
    const theme = useTheme()
    const isTabletOrMobile = useMediaQuery(theme.breakpoints.down('md'))

    return (
        <ClnPanel
            sx={{
                padding: '1rem',
                display: 'flex',
                flexDirection: 'column',
                marginBottom: '1rem',
                height: heightContainer ?? '100%',
            }}
            id={key ?? 'stacked-chart-default'}
        >
            <GenericFieldTitle fieldName={label} isSubHeader />
            {loading ? (
                LoaderCircular()
            ) : (
                <BarChart
                    id={label}
                    data={dataChart}
                    groups={groupsChart}
                    isHorizontal={type === 'horizontal'}
                    isStacked
                    // https://apexcharts.com/docs/options/chart/width/
                    // width: Number || String
                    legendPosition={legendPosition}
                    width={width ?? (isTabletOrMobile ? 500 : ('100%' as any))}
                    height={
                        height ??
                        (type === 'horizontal'
                            ? groupsChart?.length < 4
                                ? groupsChart?.length * 150
                                : groupsChart?.length * 80
                            : 200)
                    }
                    onReachEnd={onReachEnd}
                />
            )}
        </ClnPanel>
    )
}
