from pydantic import BaseModel, Field


class NodeReference(BaseModel):
    externalId: str = Field(min_length=1)
    space: str = Field(min_length=1)

    def __hash__(self):
        return hash((self.externalId, self.space))

    def __eq__(self, other):
        return isinstance(other, NodeReference) and (
            self.externalId == other.externalId and self.space == other.space
        )

    def __ne__(self, other):
        return not self.__eq__(other)


class PossiblyNewNode(BaseModel):
    externalId: str | None = Field(min_length=1, default=None)
    space: str | None = Field(min_length=1, default=None)
