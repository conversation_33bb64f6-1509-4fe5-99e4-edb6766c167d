import { useContext, useMemo } from 'react'
import { UserManagementContext, UserManagementContextState } from '@celanese/celanese-ui'
import { useGetUsersByEmail } from './useGetUsersByEmail'

export function useGetCurrentUserExternalId() {
    const { userInfo } = useContext<UserManagementContextState>(UserManagementContext)
    const { users: usersWithSameEmailAsActiveUser } = useGetUsersByEmail(userInfo?.email)
    return useMemo(() => {
        return (
            usersWithSameEmailAsActiveUser.length == 1
                ? usersWithSameEmailAsActiveUser[0]
                : usersWithSameEmailAsActiveUser.find(
                      (user) => user.firstName === userInfo?.firstName && user.lastName === userInfo?.lastName
                  )
        )?.externalId
    }, [userInfo?.email, usersWithSameEmailAsActiveUser.length])
}
