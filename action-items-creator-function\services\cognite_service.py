import json
import os
import sys
from asyncio import sleep
from functools import lru_cache
from typing import Any, Optional, TypeVar

from cognite.client.data_classes import FileMetadata, FileMetadataUpdate
from cognite.client.data_classes.data_modeling import (
    EdgeApply,
    EdgeId,
    NodeApply,
    NodeId,
    NodeOrEdgeData,
    ViewId,
)
from cognite.client.data_classes.data_modeling.instances import InstancesDeleteResult
from cognite.client.data_classes.data_modeling.query import Query, QueryResult

from services.logging_service import LoggingService

script_dir = os.path.dirname(os.path.abspath(__file__))
project_dir = os.path.dirname(script_dir)
sys.path.append(project_dir)
from cognite.client import CogniteClient

from clients.core.constants import FileMetadataKey
from models.action_item import ActionItem, SourceEvent
from models.settings import Settings
from models.upsert_action_item_request import Attachment

T = TypeVar("T")

MANY_TO_MANY_PROP_TO_END_NODE_TYPE_MAP = {
    "assignees": "UserAzureAttribute",
    "viewUsers": "UserAzureAttribute",
    "viewRoles": "Role",
    "viewTeams": "Team",
}

MANY_TO_MANY_EDGE_TYPE_MAP_PREFIX = {
    "viewUsers": "viewUser",
    "viewRoles": "viewRole",
    "viewTeams": "viewTeam",
}

ONE_TO_MANY_PROP_TO_CHILD_TYPE_MAP = {
    "links": "ActionItemLink",
    "statusHistory": "StatusHistoryInstance",
    "sourceEvent": "SourceEvent",
    "sourceEventHistory": "SourceEventHistory",
    "metadatas": "MetadataField",
}

ONE_TO_ONE_PROP_TO_CREATE_CHILD_TYPE_MAP = {
    "approvalWorkflow": "ApprovalWorkflow",
    "recurrenceInstance": "RecurrenceInstance",
}


class CogniteService:
    """Manages interaction with the Cognite Data Fusion (CDF) API, including creating and updating nodes and edges in data models."""

    def __init__(
        self,
        cognite_client: CogniteClient,
        settings: Settings,
        logging_service: LoggingService,
    ) -> None:
        """Initialize the class with a Cognite client, settings, and logging service."""
        self._cognite_client = cognite_client
        self._cognite_client.config.debug = False
        self._settings = settings
        self._log = logging_service
        self._entity_versions = {"AIM": {}, "APW": {}, "RCI": {}}

    def upsert_action_items(self, items: list[ActionItem]) -> tuple[dict, list]:
        """
        Create or updates action items in CDF by identifying and processing the required nodes and edges.

        Args:
            items (list[ActionItem]): list of action items to be upserted.

        Returns:
            tuple[dict, list]: The result of the `_upsert_nodes_and_edges` method, containing updated node data.

        """
        (
            nodes_to_create,
            edges_to_create,
        ) = self._get_nodes_and_edges_from_actions_to_upsert(
            list(filter(lambda x: x._is_new, items)),
        )
        if len(nodes_to_create) > 0:
            res, errors = self._create_empty_nodes(nodes_to_create)
            if len(errors) > 0:
                return res, errors

        (
            nodes_to_update,
            edges_to_update,
        ) = self._get_nodes_and_edges_from_actions_to_upsert(
            list(filter(lambda x: not x._is_new, items)),
        )

        return self._upsert_nodes_and_edges(
            nodes_to_update + nodes_to_create,
            edges_to_update + edges_to_create,
        )

    def _get_nodes_and_edges_from_actions_to_upsert(
        self,
        items: list[ActionItem],
    ) -> tuple[list[NodeApply], list[EdgeApply]]:
        """
        Convert action items into node and edge representations, handling different relationship types (one-to-one, one-to-many, many-to-many).

        Args:
            items (list[ActionItem]): List of action items to be processed.

        Returns:
            tuple[list[NodeApply], list[EdgeApply]]:
                - List of nodes extracted from the action items.
                - List of edges extracted from the action items.

        """
        model_space = self._settings.cognite_graphql_model_space
        aim_versions = self._get_internal_entities_versions()
        apw_versions = self._get_approval_workflow_versions()

        nodes: list[NodeApply] = []
        edges: list[EdgeApply] = []
        for action_item in items:
            item = action_item.model_dump(exclude_none=True, mode="json")
            external_id = item.get("externalId", "")
            instances_space = item.get("space", "")

            props_to_remove = []
            for prop_name, prop_value in item.items():
                is_edge = prop_name in MANY_TO_MANY_PROP_TO_END_NODE_TYPE_MAP
                is_one_to_many = prop_name in ONE_TO_MANY_PROP_TO_CHILD_TYPE_MAP
                is_one_to_one = prop_name in ONE_TO_ONE_PROP_TO_CREATE_CHILD_TYPE_MAP

                if not is_edge and not is_one_to_many and not is_one_to_one:
                    continue

                if is_one_to_one:
                    entity_external_id = prop_value.get("externalId")
                    entity_space = prop_value.get("space")
                    type_name = ONE_TO_ONE_PROP_TO_CREATE_CHILD_TYPE_MAP[prop_name]
                    item[prop_name] = {
                        "externalId": entity_external_id,
                        "space": entity_space,
                    }
                    entity_model_space = model_space
                    entity_data_model_versions = aim_versions
                    if prop_name == "approvalWorkflow":
                        entity_model_space = (
                            self._settings.approval_workflow_data_model_space
                        )
                        entity_data_model_versions = apw_versions
                        for step in prop_value.pop("steps"):
                            step_id = step.get("externalId")
                            step_space = step.get("space")
                            edges.extend(
                                EdgeApply(
                                    space=instances_space,
                                    external_id=f"{step_id}-{user['externalId']}",
                                    type=(
                                        entity_model_space,
                                        "ApprovalWorkflowStep.users",
                                    ),
                                    start_node=(step_space, step_id),
                                    end_node=(user["space"], user["externalId"]),
                                )
                                for user in step.pop("users")
                            )

                            nodes.append(
                                self._get_node_from_entity_dict(
                                    step,
                                    "ApprovalWorkflowStep",
                                    entity_model_space,
                                    entity_data_model_versions,
                                ),
                            )
                    if prop_name == "sourceEvent":
                        entity_model_space = self._settings.cognite_graphql_model_space
                        edges.extend(
                            EdgeApply(
                                space=instances_space,
                                external_id=f"{entity_external_id}-{owner['externalId']}",
                                type=(entity_model_space, "SourceEvent.owners"),
                                start_node=(entity_space, entity_external_id),
                                end_node=(owner["space"], owner["externalId"]),
                            )
                            for owner in prop_value.pop("owners")
                        )

                    nodes.append(
                        self._get_node_from_entity_dict(
                            prop_value,
                            type_name,
                            entity_model_space,
                            entity_data_model_versions,
                        ),
                    )
                    continue

                props_to_remove.append(prop_name)
                entity_list = prop_value

                if len(entity_list) == 0:
                    continue

                for entity in entity_list:
                    entity_instance_space = entity.get("space")
                    entity_external_id = entity.get("externalId")

                    type_name = (
                        MANY_TO_MANY_PROP_TO_END_NODE_TYPE_MAP[prop_name]
                        if is_edge
                        else ONE_TO_MANY_PROP_TO_CHILD_TYPE_MAP[prop_name]
                    )

                    entity_is_internal = entity_instance_space == instances_space
                    if entity_is_internal:
                        nodes.append(
                            self._get_node_from_entity_dict(
                                entity,
                                type_name,
                                model_space,
                                aim_versions,
                            ),
                        )

                    if not is_edge:
                        continue

                    is_edge_external_id = prop_name in MANY_TO_MANY_EDGE_TYPE_MAP_PREFIX
                    external_id_edge_prop = f"{external_id}-{entity_external_id}"
                    if is_edge_external_id:
                        external_id_edge_prop = f"{external_id}-{MANY_TO_MANY_EDGE_TYPE_MAP_PREFIX[prop_name]}-{entity_external_id}"

                    edges.append(
                        EdgeApply(
                            space=instances_space,
                            external_id=external_id_edge_prop,
                            type=(model_space, f"Action.{prop_name}"),
                            start_node=(instances_space, external_id),
                            end_node=(entity_instance_space, entity_external_id),
                        ),
                    )

            for prop_name in props_to_remove:
                item.pop(prop_name)

            nodes.append(
                self._get_node_from_entity_dict(
                    item,
                    "Action",
                    model_space,
                    aim_versions,
                ),
            )

        return nodes, edges

    def upsert_source_events(
        self,
        source_events: list[SourceEvent],
        update_attachments: bool = False,
    ) -> tuple[dict, list]:
        """
        Insert or updates source events in the graph, including nodes and edges.

        Args:
            source_events (list[SourceEvent]): List of source events to process.
            update_attachments (bool, optional): If True, updates the event attachments. Defaults to False.

        Behavior:
            - Converts each event into a JSON dictionary.
            - Creates nodes for events and their history if available.
            - Creates edges for relationships such as reporting units, users, teams, and associated actions.
            - Uses `EdgeApply` and `NodeApply` to construct the graph structure.

        Returns:
            tuple[dict, list]: The result of the `_upsert_nodes_and_edges` method, containing updated node data.

        """
        view_external_id = "SourceEvent"
        history_view_external_id = "SourceEventHistory"
        version = self._get_internal_entities_versions()[view_external_id]
        version_history = self._get_internal_entities_versions()[
            history_view_external_id
        ]
        nodes = []
        edges = []

        for event in source_events:
            item = event.model_dump(exclude_none=True, mode="json")
            if update_attachments:
                item["attachments"] = event.attachments

            external_id = item.pop("externalId")
            instances_space = item.pop("space")

            if item.get("sourceEventHistory"):
                for event_history in item.pop("sourceEventHistory"):
                    history_external_id = event_history.pop("externalId")
                    history_instances_space = event_history.pop("space")
                    nodes.append(
                        NodeApply(
                            external_id=history_external_id,
                            space=history_instances_space,
                            sources=[
                                NodeOrEdgeData(
                                    ViewId(
                                        external_id=history_view_external_id,
                                        space=self._settings.cognite_graphql_model_space,
                                        version=version_history,
                                    ),
                                    event_history,
                                ),
                            ],
                        ),
                    )

            if item.get("reportingUnits"):
                edges.extend(
                    EdgeApply(
                        space=instances_space,
                        external_id=f"{external_id}-{unit['externalId']}",
                        type=(
                            self._settings.cognite_graphql_model_space,
                            "SourceEvent.reportingUnits",
                        ),
                        start_node=(instances_space, external_id),
                        end_node=(unit["space"], unit["externalId"]),
                    )
                    for unit in item.pop("reportingUnits", [])
                )

            if item.get("impactedReportingLocations"):
                edges.extend(
                    EdgeApply(
                        space=instances_space,
                        external_id=f"{external_id}-{loc['externalId']}",
                        type=(
                            self._settings.cognite_graphql_model_space,
                            "SourceEvent.impactedReportingLocations",
                        ),
                        start_node=(instances_space, external_id),
                        end_node=(loc["space"], loc["externalId"]),
                    )
                    for loc in item.pop("impactedReportingLocations", [])
                )

            if item.get("secondaryOwnerUsers"):
                edges.extend(
                    EdgeApply(
                        space=instances_space,
                        external_id=f"{external_id}-{user['externalId']}",
                        type=(
                            self._settings.cognite_graphql_model_space,
                            "SourceEvent.secondaryOwnerUsers",
                        ),
                        start_node=(instances_space, external_id),
                        end_node=(user["space"], user["externalId"]),
                    )
                    for user in item.pop("secondaryOwnerUsers", [])
                )

            if item.get("secondaryOwnerRoles"):
                edges.extend(
                    EdgeApply(
                        space=instances_space,
                        external_id=f"{external_id}-{role['externalId']}",
                        type=(
                            self._settings.cognite_graphql_model_space,
                            "SourceEvent.secondaryOwnerRoles",
                        ),
                        start_node=(instances_space, external_id),
                        end_node=(role["space"], role["externalId"]),
                    )
                    for role in item.pop("secondaryOwnerRoles", [])
                )

            if item.get("secondaryOwnerTeams"):
                edges.extend(
                    EdgeApply(
                        space=instances_space,
                        external_id=f"{external_id}-{team['externalId']}",
                        type=(
                            self._settings.cognite_graphql_model_space,
                            "SourceEvent.secondaryOwnerTeams",
                        ),
                        start_node=(instances_space, external_id),
                        end_node=(team["space"], team["externalId"]),
                    )
                    for team in item.pop("secondaryOwnerTeams", [])
                )

            if item.get("actions"):
                edges.extend(
                    EdgeApply(
                        space=instances_space,
                        external_id=f"{external_id}-{action['externalId']}",
                        type=(
                            self._settings.cognite_graphql_model_space,
                            "SourceEvent.actions",
                        ),
                        start_node=(instances_space, external_id),
                        end_node=(action["space"], action["externalId"]),
                    )
                    for action in item.pop("actions", [])
                )

            if item.get("viewUsers"):
                edges.extend(
                    EdgeApply(
                        space=instances_space,
                        external_id=f"{external_id}-viewUser-{user['externalId']}",
                        type=(
                            self._settings.cognite_graphql_model_space,
                            "SourceEvent.viewUsers",
                        ),
                        start_node=(instances_space, external_id),
                        end_node=(user["space"], user["externalId"]),
                    )
                    for user in item.pop("viewUsers", [])
                )

            if item.get("viewRoles"):
                edges.extend(
                    EdgeApply(
                        space=instances_space,
                        external_id=f"{external_id}-viewRole-{role['externalId']}",
                        type=(
                            self._settings.cognite_graphql_model_space,
                            "SourceEvent.viewRoles",
                        ),
                        start_node=(instances_space, external_id),
                        end_node=(role["space"], role["externalId"]),
                    )
                    for role in item.pop("viewRoles", [])
                )

            if item.get("viewTeams"):
                edges.extend(
                    EdgeApply(
                        space=instances_space,
                        external_id=f"{external_id}-viewTeam-{team['externalId']}",
                        type=(
                            self._settings.cognite_graphql_model_space,
                            "SourceEvent.viewTeams",
                        ),
                        start_node=(instances_space, external_id),
                        end_node=(team["space"], team["externalId"]),
                    )
                    for team in item.pop("viewTeams", [])
                )

            if item.get("equipments"):
                edges.extend(
                    EdgeApply(
                        space=instances_space,
                        external_id=f"{external_id}-{eq['externalId']}",
                        type=(
                            self._settings.cognite_graphql_model_space,
                            "SourceEvent.equipments",
                        ),
                        start_node=(instances_space, external_id),
                        end_node=(eq["space"], eq["externalId"]),
                    )
                    for eq in item.pop("equipments", [])
                )

            if item.get("functionalLocations"):
                edges.extend(
                    EdgeApply(
                        space=instances_space,
                        external_id=f"{external_id}-{floc['externalId']}",
                        type=(
                            self._settings.cognite_graphql_model_space,
                            "SourceEvent.functionalLocations",
                        ),
                        start_node=(instances_space, external_id),
                        end_node=(floc["space"], floc["externalId"]),
                    )
                    for floc in item.pop("functionalLocations", [])
                )

            nodes.append(
                NodeApply(
                    external_id=external_id,
                    space=instances_space,
                    sources=[
                        NodeOrEdgeData(
                            ViewId(
                                external_id=view_external_id,
                                space=self._settings.cognite_graphql_model_space,
                                version=version,
                            ),
                            item,
                        ),
                    ],
                ),
            )

        return self._upsert_nodes_and_edges(nodes, edges)

    def upsert_processed_events(
        self,
        processed_events: list[dict[str, Any]],
        new_processed: bool,
    ) -> tuple[dict, list]:
        """
        Insert or updates processed events in the graph.

        Args:
            processed_events (list[dict[str, Any]]): List of processed events to be inserted or updated.
            new_processed (bool): If True, sets `existing_version` to 0 for new events; otherwise, updates existing events.

        Returns:
            tuple[dict, list]: The result of the `_upsert_nodes_and_edges` method, containing updated node data.

        """
        view_external_id = "ProcessedActionItemEvent"
        version = self._get_internal_entities_versions()[view_external_id]
        events_to_update = []
        for event in processed_events:
            external_id = event.pop("externalId")
            space = event.pop("space")
            events_to_update.append(
                NodeApply(
                    external_id=external_id,
                    space=space,
                    existing_version=0 if new_processed else None,
                    sources=[
                        NodeOrEdgeData(
                            ViewId(
                                external_id=view_external_id,
                                space=self._settings.cognite_graphql_model_space,
                                version=version,
                            ),
                            event,
                        ),
                    ],
                ),
            )

        return self._upsert_nodes_and_edges(events_to_update)

    def _upsert_nodes_and_edges(
        self,
        nodes: Optional[list[NodeApply]] = None,
        edges: Optional[list[EdgeApply]] = None,
    ) -> tuple[dict, list]:
        """
        Insert or updates nodes and edges in the Cognite Data Modeling service.

        Args:
            nodes (Optional[list[NodeApply]], optional): List of nodes to be inserted or updated.
            edges (Optional[list[EdgeApply]], optional): List of edges to be inserted or updated.

        Returns:
            tuple[dict, list]:
                - A dictionary containing the upsert status of nodes and edges with keys:
                    - "nodes": { "upserted": [...], "notUpserted": [...] }
                    - "edges": { "upserted": [...], "notUpserted": [...] }
                - A list of errors encountered during the process (empty if successful).

        """
        if nodes is None and edges is None:
            return {}, []

        len_nodes = len(nodes) if nodes is not None else 0
        len_edges = len(edges) if edges is not None else 0

        node_upsert_status = {
            "upserted": [],
            "notUpserted": [],
        }
        edge_upsert_status = {
            "upserted": [],
            "notUpserted": [],
        }
        result = {
            "nodes": node_upsert_status,
            "edges": edge_upsert_status,
        }

        if len_nodes + len_edges <= 1000:
            try:
                apply_result = self._cognite_client.data_modeling.instances.apply(
                    nodes,
                    edges,
                )
                result["nodes"]["upserted"].extend(apply_result.nodes.as_ids())
                result["edges"]["upserted"].extend(apply_result.edges.as_ids())

            except Exception as e:
                if nodes is not None:
                    result["nodes"]["notUpserted"].extend(nodes)
                if edges is not None:
                    result["edges"]["notUpserted"].extend(edges)

                return result, [str(e)]
            else:
                return result, []

        nodes_pagination = (
            self.__create_default_pagination(nodes) if nodes is not None else []
        )

        edges_pagination = (
            self.__create_default_pagination(edges) if edges is not None else []
        )

        for i in range(len(nodes_pagination)):
            try:
                apply_result = self._cognite_client.data_modeling.instances.apply(
                    nodes=nodes_pagination[i],
                )
                result["nodes"]["upserted"].extend(apply_result.nodes.as_ids())
            except Exception as e:
                for j in range(i, len(nodes_pagination)):
                    result["nodes"]["notUpserted"].extend(nodes_pagination[j])
                return result, [str(e)]

        for i in range(len(edges_pagination)):
            try:
                apply_result = self._cognite_client.data_modeling.instances.apply(
                    edges=edges_pagination[i],
                )
                result["edges"]["upserted"].extend(apply_result.edges.as_ids())
            except Exception as e:
                for j in range(i, len(edges_pagination)):
                    result["edges"]["notUpserted"].extend(edges_pagination[j])
                return result, [str(e)]

        return result, []

    def _create_empty_nodes(self, items: list[NodeApply]) -> tuple[dict, list]:
        return self._upsert_nodes_and_edges(
            [
                NodeApply(
                    external_id=item.external_id,
                    space=item.space,
                    existing_version=0,
                )
                for item in items
            ],
            [],
        )

    def _get_internal_entities_versions(self) -> dict[str, str]:
        return self._get_entities_versions(
            "AIM",
            self._settings.cognite_graphql_model_space,
        )

    def _get_approval_workflow_versions(self) -> dict[str, str]:
        return self._get_entities_versions(
            "APW",
            self._settings.approval_workflow_data_model_space,
        )

    def _get_entities_versions(self, model: str, space: str) -> dict[str, str]:
        if len(self._entity_versions[model]) > 0:
            return self._entity_versions[model]

        self._entity_versions[model] = {
            item.external_id: item.version
            for item in self._cognite_client.data_modeling.views.list(
                space=space,
                limit=1000,
            )
        }
        if not self._entity_versions[model]:
            msg = "Could not retreive the entity versions"
            raise ValueError(msg)

        return self._entity_versions[model]

    @staticmethod
    def _get_node_from_entity_dict(
        entity: dict,
        type_name: str,
        model_space: str,
        versions: dict,
    ) -> NodeApply:
        external_id = entity.pop("externalId")
        space = entity.pop("space")
        return NodeApply(
            space=space,
            external_id=external_id,
            sources=[
                NodeOrEdgeData(
                    ViewId(
                        model_space,
                        type_name,
                        versions[type_name],
                    ),
                    entity,
                ),
            ],
        )

    def get_files_metadata(
        self,
        file_external_ids: list[str],
    ) -> dict[str, FileMetadata]:
        """
        Retrieve metadata for multiple files.

        Args:
            file_external_ids (list[str]): List of file external IDs.

        Returns:
            dict[str, FileMetadata]: A dictionary mapping file external IDs to their metadata.

        """
        if file_external_ids is None or len(file_external_ids) == 0:
            return {}
        files = self._cognite_client.files.retrieve_multiple(
            external_ids=file_external_ids,
            ignore_unknown_ids=True,
        )
        return {
            file.external_id: file for file in files if file.external_id is not None
        }

    def delete_files(self, file_external_ids: list[str]) -> None:
        """
        Delete multiple files by their external IDs.

        Args:
            file_external_ids (list[str]): List of file external IDs to delete.

        """
        for file_external_id in file_external_ids:
            try:
                self._cognite_client.files.delete(external_id=file_external_id)
            except Exception as e:
                self._log.warning(f"Could not delete file {file_external_id}: {e}")
                continue

    def get_file_metadata(self, file_external_id: str) -> Optional[dict[str, str]]:
        """
        Retrieve the metadata of a file.

        Args:
            file_external_id (str): External ID of the file.

        Returns:
            Optional[dict]: File metadata if the file exists, otherwise None.

        """
        res = self._cognite_client.files.retrieve(external_id=file_external_id)
        return res.metadata if res is not None else None

    def set_file_metadata(self, file_external_id: str, metadata: dict) -> None:
        """
        Update the metadata of a file.

        Args:
            file_external_id (str): External ID of the file.
            metadata (dict): New metadata to set.

        """
        self._cognite_client.files.update(
            FileMetadataUpdate(external_id=file_external_id).metadata.set(metadata),
        )

    def duplicate_file_to_target_dataset(self, file: Attachment) -> FileMetadata:
        """
        Duplicate a file to a target dataset.

        Args:
            file (Attachment): File attachment with necessary metadata.

        Returns:
            The uploaded file response.

        Raises:
            Exception: If the file duplication fails.

        """
        try:
            file_in_bytes = self._cognite_client.files.download_bytes(
                external_id=file.received_external_id,
            )
            return self._cognite_client.files.upload_bytes(
                content=file_in_bytes,
                name=file.name or "",
                mime_type=file.mime_type,
                metadata={
                    FileMetadataKey.RELATED_ACTIONS.value: json.dumps(
                        file.action_item_external_ids,
                    ),
                    FileMetadataKey.SOURCE_FILE_EXTERNAL_ID.value: file.received_external_id,
                    FileMetadataKey.FILE_SIZE.value: json.dumps(file.file_size),
                },
                source="action-item-creator-function",
                external_id=file.target_external_id,
                data_set_id=file.target_dataset_id,
            )
        except Exception as e:
            self._log.warning(f"Could not duplicate file {file.received_external_id}")
            self.delete_files([file.target_external_id or ""])
            raise e

    def execute_query(self, query: Query) -> QueryResult:
        """
        Execute a query on the Cognite Data Modeling API.

        Args:
            query (Query): The query object.

        Returns:
            QueryResult: The query results.

        """
        return self._cognite_client.data_modeling.instances.query(query)

    async def get_query_results_list(self, query: Query, list_name: str) -> QueryResult:
        """
        Fetch query results with retry handling.

        Args:
            query (Query): The query object.
            list_name (str): Name of the entity being fetched (for logging).

        Returns:
            QueryResult: The query results.

        Raises:
            Exception: If a non-retryable error occurs or max retries are exceeded.

        """
        result: QueryResult
        retry_count = 0
        max_retries = 5

        while retry_count <= max_retries:
            try:
                response = self.execute_query(query)
                result = response
                break
            except Exception as e:
                if hasattr(e, "response") and e.response.status_code == 429:
                    self._log.error(
                        f"Error 429 (Too Many Requests) while fetching {list_name}: {e}",
                    )
                    retry_count += 1
                    if retry_count > max_retries:
                        self._log.error(
                            f"Max retries reached for {list_name}. Error 429 Cognite",
                        )
                        raise e
                    await sleep(1)
                else:
                    self._log.error(
                        f"Non-retryable error while fetching {list_name}: {e}",
                    )
                    raise e

        return result

    def is_space_valid(self, space: str) -> bool:
        """
        Check if a space exists and is writable by attempting to create and delete a test instance.

        Args:
            space (str): The space to validate.

        Returns:
            bool: True if the space is valid, False otherwise.

        """
        test_id = "validate_space_from_aim_function"
        try:
            self._cognite_client.data_modeling.instances.apply(
                NodeApply(external_id=test_id, space=space),
            )
        except Exception as e:
            self._log.warning(
                f"Space {space} does not exist or there is no permission to write into it. Error: {e}",
            )
            return False

        self._cognite_client.data_modeling.instances.delete(
            NodeId(external_id=test_id, space=space),
        )
        return True

    @lru_cache(maxsize=250)
    def get_dataset_id(self, dataset_external_id: str) -> Optional[int]:
        """
        Retrieve the dataset ID for a given external ID.

        Args:
            dataset_external_id (str): The external ID of the dataset.

        Returns:
            Optional[int]: The dataset ID if found, otherwise None.

        """
        try:
            dataset = self._cognite_client.data_sets.retrieve(
                external_id=dataset_external_id,
            )

        except Exception as e:
            self._log.warning(
                f"Could not retrieve dataset {dataset_external_id}. Error: {e}",
            )
            return None
        else:
            return dataset.id if dataset is not None else None

    def __create_default_pagination(self, resources: list[T]) -> list[list[T]]:
        return [
            resources[1000 * i : 1000 * (i + 1)]
            for i in range(int(len(resources) / 1000) + 1)
        ]

    def delete_edges(self, edges: list[EdgeId]) -> Optional[InstancesDeleteResult]:
        """
        Delete a list of edges from the Cognite Data Modeling API.

        Args:
            edges (list[EdgeId]): The edges to delete.

        Returns:
            Optional[InstancesDeleteResult]: The result of the deletion if successful, otherwise None.

        """
        try:
            return self._cognite_client.data_modeling.instances.delete(edges=edges)
        except Exception as e:
            self._log.warning(f"Could not delete edges. Error: {e}")
            return None
