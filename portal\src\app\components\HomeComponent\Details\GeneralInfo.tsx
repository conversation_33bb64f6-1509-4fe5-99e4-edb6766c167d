'use client'
import { Box, Grid, Icon<PERSON>utton, Typography } from '@mui/material'
import { useEffect, useMemo, useState } from 'react'
import dayjs from 'dayjs'
import { useActionSourceDetailConfigs } from '@/app/common/hooks/action-item-management/useActionSourceDetailConfigs'
import { ActionDetailItem } from '@/app/common/models/action-detail'
import GenericFieldText from '../../FieldsComponent/GenericFieldText'
import { useICAPMOCReport } from '@/app/common/hooks/integration/icap/useICAPMOCReport'
import { useICAPMOOCReport } from '@/app/common/hooks/integration/icap/useICAPMOOCReport'
import { useGapAssessmentExecutionData } from '@/app/common/hooks/integration/celia-gap-assessment/useGapAssessmentExecutionData'
import { ActionSourceTypeExternalIdEnum } from '@/app/common/enums/ActionSourceTypeEnum'
import { GenerateStatusChip } from '../../StatusComponet'
import { ClnChip, MatIcon } from '@celanese/ui-lib'
import { ActionStatusExternalIdClearEnum } from '@/app/common/enums/ActionItemStatusEnum'
import { translate } from '@/app/common/utils/generate-translate'
import { AzureFunctionClient } from '@/app/common/clients/azure-function-client'
import { useSnackbar } from '@/app/common/contexts/SnackbarContext'
import { ExternalSourceDetails } from '@/app/common/models/external-source-details'
import { formatDisplayDueDate } from '@/app/common/utils/date'
import { RCA_SOURCE_TYPES } from '../ActionItemModal/utils'
import { RCA_SOURCE_TYPE_PREFIX } from '@/app/common/utils'

type Props = {
    actionItem?: ActionDetailItem
    isMobile?: boolean
}

export function GeneralInfo(params: Props) {
    const { showSnackbar } = useSnackbar()

    const { actionItem, isMobile } = params
    const sourceTypeId = actionItem?.sourceType?.externalId
    const sourceId = actionItem?.sourceId

    const [rcaEventData, setRcaEventData] = useState<ExternalSourceDetails>()

    const { actionSourceDetailConfigs } = useActionSourceDetailConfigs(
        useMemo(
            () => ({
                sourceTypeId: actionItem?.sourceType?.externalId ?? '-',
            }),
            [actionItem]
        )
    )

    const { data: ICAPMOCReportData } = useICAPMOCReport(
        useMemo(
            () => (sourceTypeId === ActionSourceTypeExternalIdEnum.ICAPMOCReport ? sourceId : undefined),
            [sourceId, sourceTypeId]
        )
    )

    const { data: ICAPMOOCReportData } = useICAPMOOCReport(
        useMemo(
            () => (sourceTypeId === ActionSourceTypeExternalIdEnum.ICAPMOOCReport ? sourceId : undefined),
            [sourceId, sourceTypeId]
        )
    )

    const { data: gapAssessmentExecutionData } = useGapAssessmentExecutionData(
        useMemo(
            () => (sourceTypeId === ActionSourceTypeExternalIdEnum.GapAssessmentExecutionData ? sourceId : undefined),
            [sourceId, sourceTypeId]
        )
    )

    useEffect(() => {
        if (!sourceTypeId || !sourceId || !sourceTypeId.startsWith(RCA_SOURCE_TYPE_PREFIX)) return

        const controller = new AbortController()
        const timeout = setTimeout(async () => {
            try {
                const client = new AzureFunctionClient()

                const result = await client.getExternalSourceDetails({
                    sourceType: sourceTypeId,
                    sourceId: sourceId,
                })

                setRcaEventData({
                    general: result.general,
                    specific: result.specific,
                })
            } catch (err) {
                showSnackbar(translate('alerts.unexpectedErrorOcurred'), 'error', 'action-item-modal')
            }
        }, 500)

        return () => {
            clearTimeout(timeout)
            controller.abort()
        }
    }, [sourceTypeId, sourceId, showSnackbar])

    const formatData = (date: string) => {
        const inputDate = dayjs(date)
        return inputDate.format('MM/DD/YYYY')
    }

    const link = useMemo(() => {
        if (!sourceTypeId) return ''

        const getIdAndSpace = (): { id: string; space: string; siteCode: string } => {
            const base = { id: '', space: '', siteCode: '' }
            if (sourceTypeId === ActionSourceTypeExternalIdEnum.AIMEvent) {
                return {
                    ...base,
                    id: sourceId ?? '',
                    siteCode: actionItem?.reportingSite?.siteCode ?? '',
                }
            }
            if (sourceTypeId === ActionSourceTypeExternalIdEnum.ICAPMOCReport) {
                return {
                    ...base,
                    id: ICAPMOCReportData[0]?.number?.split('-')[0] ?? '',
                }
            }

            if (sourceTypeId === ActionSourceTypeExternalIdEnum.ICAPMOOCReport) {
                return {
                    ...base,
                    id: ICAPMOOCReportData[0]?.event?.number ?? '',
                }
            }

            if (sourceTypeId === ActionSourceTypeExternalIdEnum.GapAssessmentExecutionData) {
                return {
                    ...base,
                    id: gapAssessmentExecutionData[0]?.gapAssessmentExecutions?.items[0]?.externalId ?? '',
                }
            }

            if (RCA_SOURCE_TYPES.has(sourceTypeId)) {
                return {
                    ...base,
                    id: sourceId?.replace('EVEN-', '') ?? '',
                    space: rcaEventData?.general?.space ?? '',
                }
            }

            return {
                ...base,
                id: sourceId ?? '',
            }
        }

        const { id, space, siteCode } = getIdAndSpace()
        const baseUrl = actionSourceDetailConfigs?.[0]?.properties?.url || ''

        return baseUrl.replace('{id}', id).replace('{space}', space).replace('{siteCode}', siteCode)
    }, [
        sourceTypeId,
        actionSourceDetailConfigs,
        sourceId,
        actionItem?.reportingSite?.siteCode,
        ICAPMOCReportData,
        ICAPMOOCReportData,
        gapAssessmentExecutionData,
        rcaEventData?.general?.space,
    ])

    return (
        <Box id="action-details-general-info" paddingTop={2}>
            <Box
                sx={{
                    display: 'flex',
                    flexGrow: 1,
                    flexDirection: 'row',
                    gap: '1rem',
                    overflowWrap: 'break-word',
                    whiteSpace: 'normal',
                    alignItems: 'center',
                }}
            >
                {!isMobile && (
                    <Typography
                        variant="h3"
                        sx={{
                            fontWeight: 'bold',
                            wordBreak: 'break-word',
                            whiteSpace: 'normal',
                            overflowWrap: 'break-word',
                            flexShrink: 1,
                        }}
                    >
                        {actionItem?.title}
                    </Typography>
                )}
                {actionItem && actionItem?.isPrivate && <GenerateStatusChip statusId={'private'} />}
                {actionItem && actionItem?.viewOnly && <GenerateStatusChip statusId={'viewOnly'} />}
                {actionItem && (
                    <GenerateStatusChip
                        statusId={actionItem?.currentStatus?.externalId ?? ActionStatusExternalIdClearEnum.Assigned}
                    />
                )}
                <ClnChip
                    icon={<MatIcon icon="location_on" color="warning.main" />}
                    label={actionItem?.reportingSite?.name}
                    size="small"
                    sxProps={{
                        backgroundColor: 'warning.100',
                        color: 'warning.main',
                        '&:hover': {
                            backgroundColor: 'warning.100',
                            color: 'warning.main',
                        },
                    }}
                    variant="filled"
                />
            </Box>
            <Grid container sx={{ rowGap: 1 }} paddingTop={2}>
                <Grid item xs={12} sm={6} md={4} lg={2.4}>
                    <GenericFieldText fieldName={translate('table.headers.id')} value={actionItem?.externalId ?? ''} />
                </Grid>
                <Grid item xs={12} sm={6} md={4} lg={2.4}>
                    <GenericFieldText
                        fieldName={translate('details.fields.unit')}
                        value={actionItem?.reportingUnit?.description ?? '-'}
                    />
                </Grid>
                <Grid item xs={12} sm={6} md={4} lg={2.4}>
                    <GenericFieldText
                        fieldName={translate('details.fields.reportingLocation')}
                        value={actionItem?.reportingLocation?.description ?? '-'}
                    />
                </Grid>
                <Grid item xs={12} sm={6} md={4} lg={2.4}>
                    <GenericFieldText
                        fieldName={translate('details.fields.owner')}
                        value={
                            actionItem?.owner
                                ? `${actionItem.owner?.user?.lastName}, ${actionItem.owner?.user?.firstName}`
                                : '-'
                        }
                    />
                </Grid>
                <Grid item xs={12} sm={6} md={4} lg={2.4}>
                    <GenericFieldText
                        fieldName={translate('details.fields.dueDate')}
                        value={actionItem?.dueDate ? formatDisplayDueDate(actionItem?.dueDate, sourceTypeId) : '-'}
                    />
                </Grid>
                <Grid item xs={12} sm={6} md={4} lg={2.4}>
                    <GenericFieldText
                        fieldName={translate('details.fields.priority')}
                        value={actionItem?.priority ?? '-'}
                    />
                </Grid>
                <Grid item xs={12} sm={6} md={4} lg={2.4}>
                    <GenericFieldText
                        fieldName={translate('details.fields.assignee')}
                        value={
                            actionItem?.assignedTo
                                ? `${actionItem.assignedTo?.user?.lastName}, ${actionItem.assignedTo?.user?.firstName}`
                                : '-'
                        }
                    />
                </Grid>
                <Grid item xs={12} sm={6} md={4} lg={2.4}>
                    <GenericFieldText
                        fieldName={translate('details.fields.approvalRequired')}
                        value={actionItem?.approver ? 'Yes' : 'No'}
                    />
                </Grid>
                <Grid item xs={12} sm={6} md={4} lg={2.4}>
                    <GenericFieldText
                        fieldName={translate('details.fields.approvalDate')}
                        value={actionItem?.approvalDate ? formatData(actionItem.approvalDate) : '-'}
                    />
                </Grid>
                <Grid item xs={12} sm={6} md={4} lg={2.4}>
                    <GenericFieldText
                        fieldName={translate('details.fields.approver')}
                        value={
                            actionItem?.approver
                                ? `${actionItem.approver?.lastName}, ${actionItem.approver?.firstName}`
                                : '-'
                        }
                    />
                </Grid>
                <Grid item xs={12} sm={6} md={4} lg={2.4}>
                    <GenericFieldText
                        fieldName={translate('details.fields.verificationRequired')}
                        value={actionItem?.verifier ? 'Yes' : 'No'}
                    />
                </Grid>
                <Grid item xs={12} sm={6} md={4} lg={2.4}>
                    <GenericFieldText
                        fieldName={translate('details.fields.verificationDate')}
                        value={actionItem?.verificationDate ? formatData(actionItem.verificationDate) : '-'}
                    />
                </Grid>
                <Grid item xs={12} sm={6} md={4} lg={2.4}>
                    <GenericFieldText
                        fieldName={translate('details.fields.verifier')}
                        value={
                            actionItem?.verifier
                                ? `${actionItem.verifier?.lastName}, ${actionItem.verifier?.firstName}`
                                : '-'
                        }
                    />
                </Grid>
                <Grid item xs={12} sm={6} md={4} lg={2.4}>
                    <GenericFieldText
                        fieldName={translate('details.fields.conclusionDate')}
                        value={actionItem?.conclusionDate ? formatData(actionItem.conclusionDate) : '-'}
                    />
                </Grid>
                <Grid item xs={12} sm={6} md={4} lg={2.4}>
                    <GenericFieldText
                        fieldName={translate('details.fields.category')}
                        value={actionItem?.category?.name ?? '-'}
                    />
                </Grid>
                <Grid item xs={12} sm={6} md={4} lg={2.4}>
                    <GenericFieldText
                        fieldName={translate('details.fields.subcategoryOne')}
                        value={actionItem?.subCategory?.name ?? '-'}
                    />
                </Grid>
                <Grid item xs={12} sm={6} md={4} lg={2.4}>
                    <GenericFieldText
                        fieldName={translate('details.fields.subcategoryTwo')}
                        value={actionItem?.siteSpecificCategory?.name ?? '-'}
                    />
                </Grid>
                <Grid item xs={12} sm={6} md={4} lg={2.4}>
                    <GenericFieldText
                        fieldName={translate('details.fields.eventLink')}
                        value={link}
                        icon={
                            actionSourceDetailConfigs && actionSourceDetailConfigs.length > 0 && link !== '-' ? (
                                <IconButton color="default" sx={{ paddingLeft: 0 }}>
                                    <MatIcon icon="link" onClick={() => window.location.assign(link)} />
                                </IconButton>
                            ) : undefined
                        }
                        color={
                            actionSourceDetailConfigs && actionSourceDetailConfigs.length > 0 && link !== '-'
                                ? 'info.main'
                                : undefined
                        }
                        ellipsis
                    />
                </Grid>
            </Grid>
            <Box>
                <Typography color="text.secondary" fontSize={'12px'} paddingTop={'1em'}>
                    {'*' + translate('details.fields.allDatesExpressed') + 'MM/DD/YYYY'}
                </Typography>
            </Box>
        </Box>
    )
}

export default GeneralInfo
