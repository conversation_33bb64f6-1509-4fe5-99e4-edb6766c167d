'use client'
import { Box } from '@mui/material'
import LoaderCircular from '../Loader'
import { ClnCard } from '@celanese/ui-lib'

type Props = {
    amount: string
    label: string
    id?: string
    loading?: boolean
}

export function KPICard({ amount, label, id, loading }: Props) {
    return (
        <Box sx={{ width: '100%' }} id={id ?? 'kpi-default'}>
            {loading ? (
                LoaderCircular()
            ) : (
                <ClnCard
                    cardHeader={amount}
                    subheader={label}
                    type="performance"
                    sxProps={{
                        '& .big-icons': { display: 'none' },
                        '&.MuiPaper-root.MuiCard-root': { margin: 0 },
                        width: '100%',
                        display: 'flex',
                        flexDirection: 'column',
                        alignItems: 'center',
                        justifyContent: 'center',
                        height: '7rem',
                        margin: '0px',
                    }}
                    sxHeader={{
                        display: 'flex',
                        flexDirection: 'column',
                        alignItems: 'center',
                        justifyContent: 'center',
                    }}
                />
            )}
        </Box>
    )
}
