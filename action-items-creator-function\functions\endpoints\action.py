import json
import logging
from http import HTT<PERSON>tatus

import azure.functions as func

from clients.actions.requests import (
    ActionEditNotificationRequest,
    CancelActionRequest,
    DeleteActionRequest,
    ExportActionsRequest,
    GetActionByIdRequest,
    GetActionRequest,
    UpdateActionApprovalWorkflowRequest,
    UpdateActionAssigneeRequest,
    UpdateActionRequest,
)
from clients.core.constants import APPLICATION_JSON
from infra.action_item_client_factory import ActionItemClientFactory
from infra.action_item_creator_factory import ActionItemCreatorFactory
from services.action_service import ActionService

bp = func.Blueprint()
logging.basicConfig(format="%(message)s", level=logging.INFO)


@bp.function_name(name="GetActionsTable")
@bp.route("get-actions-table", methods=["get"], auth_level=func.AuthLevel.ANONYMOUS)
async def get_home_table(req: func.HttpRequest) -> func.HttpResponse:
    try:
        auth_header = req.headers.get("Authorization")
        if auth_header and auth_header.startswith("Bearer "):
            token_request = auth_header[len("Bearer ") :]
        else:
            token_request = auth_header

        action_items_request_param = req.params.get("actionItemRequest", "{}")
        action_items_request = json.loads(action_items_request_param)

        logging.info("Function GetActionsTable started")

        request = GetActionRequest.model_validate(action_items_request)

        service = ActionService(
            ActionItemClientFactory.retriever(override_token=token_request),
        )
        actions, total = await service.get_actions_by_site(request)

        logging.info("Finishing execution - GetActionsTable")
        items_dict = actions.model_dump(mode="json", by_alias=True)
        items_dict["totalActions"] = total
        response_body = json.dumps(items_dict)

        return func.HttpResponse(
            response_body,
            mimetype=APPLICATION_JSON,
            status_code=HTTPStatus.OK,
        )
    except Exception as e:
        logging.exception("Exception found")
        return func.HttpResponse(
            f"Error: {e}",
            status_code=HTTPStatus.INTERNAL_SERVER_ERROR,
        )


@bp.function_name(name="GetActionItemById")
@bp.route("get-action-item-by-id", methods=["get"], auth_level=func.AuthLevel.ANONYMOUS)
async def get_action_by_id_request(req: func.HttpRequest) -> func.HttpResponse:
    try:
        auth_header = req.headers.get("Authorization")
        if auth_header and auth_header.startswith("Bearer "):
            token_request = auth_header[len("Bearer ") :]
        else:
            token_request = auth_header

        action_item_request_param = req.params.get("actionItemRequest", "{}")
        action_item_request = json.loads(action_item_request_param)

        logging.info("Function GetActionItemById started")

        request = GetActionByIdRequest.model_validate(action_item_request)

        service = ActionService(
            ActionItemClientFactory.retriever(override_token=token_request),
        )
        action, permissions = await service.get_action_by_id(request=request)

        logging.info(
            "Finishing execution - Items Retrieved: %s",
            action.external_id if action else None,
        )
        logging.info("Access permission: %s", action is not None)

        response_body = json.dumps(
            {
                "data": action.model_dump(mode="json", by_alias=True) if action else {},
                "access": action is not None,
                "permissions": permissions,
            },
        )

        return func.HttpResponse(
            response_body,
            mimetype=APPLICATION_JSON,
            status_code=HTTPStatus.OK,
        )
    except Exception as e:
        logging.exception("Exception found")
        return func.HttpResponse(f"Error: {e}", status_code=500)


@bp.function_name(name="UpdateActionApprovalWorkflow")
@bp.route(
    "update-action-approval-workflow/{id}",
    methods=["patch"],
    auth_level=func.AuthLevel.ANONYMOUS,
)
async def update_action_approval_workflow(req: func.HttpRequest) -> func.HttpResponse:
    logging.info("Function UpdateActionApprovalWorkflow started")
    try:
        auth_header = req.headers.get("Authorization")
        if auth_header and auth_header.startswith("Bearer "):
            token_request = auth_header[len("Bearer ") :]
        else:
            token_request = auth_header

        body = req.get_json()

        request = UpdateActionApprovalWorkflowRequest.model_validate(body)

        logging.info("Updating action approval workflow in Cognite")
        service = ActionService(
            ActionItemClientFactory.retriever(override_token=token_request),
        )
        external_id, errors = await service.update_action_approval_workflow(
            request=request,
        )

        logging.info("UpdateActionApprovalWorkflow executed successfully")

        return func.HttpResponse(
            json.dumps({"externalId": external_id, "errors": errors}),
            status_code=(
                HTTPStatus.OK if not errors else HTTPStatus.INTERNAL_SERVER_ERROR
            ),
            mimetype=APPLICATION_JSON,
        )
    except Exception as e:
        logging.exception("Error in UpdateActionApprovalWorkflow")
        return func.HttpResponse(
            f"Error: {e}",
            status_code=HTTPStatus.INTERNAL_SERVER_ERROR,
            mimetype=APPLICATION_JSON,
        )


@bp.function_name(name="UpdateAction")
@bp.route(
    "update-action/{id}",
    methods=["patch"],
    auth_level=func.AuthLevel.ANONYMOUS,
)
async def update_action(req: func.HttpRequest) -> func.HttpResponse:
    logging.info("Function UpdateAction started")

    try:
        auth_header = req.headers.get("Authorization")
        if auth_header and auth_header.startswith("Bearer "):
            token_request = auth_header[len("Bearer ") :]
        else:
            token_request = auth_header

        body = req.get_json()

        request = UpdateActionRequest.model_validate(body)

        logging.info("Updating action in Cognite")
        service = ActionService(
            ActionItemClientFactory.retriever(override_token=token_request),
        )
        external_id, errors = await service.update_action(request=request)

        logging.info("UpdateAction executed successfully")

        return func.HttpResponse(
            json.dumps({"externalId": external_id, "errors": errors}),
            status_code=(
                HTTPStatus.OK if not errors else HTTPStatus.INTERNAL_SERVER_ERROR
            ),
            mimetype=APPLICATION_JSON,
        )

    except Exception as e:
        logging.exception("Error in UpdateAction")
        return func.HttpResponse(
            f"Error: {e}",
            status_code=HTTPStatus.INTERNAL_SERVER_ERROR,
            mimetype=APPLICATION_JSON,
        )

@bp.function_name(name="UpdateMultipleActions")
@bp.route(
    "update-multiple-actions",
    methods=["patch"],
    auth_level=func.AuthLevel.ANONYMOUS,
)
async def update_multiple_actions(req: func.HttpRequest) -> func.HttpResponse:
    
    logging.info("Function UpdateMultipleActions started")

    try:
        auth_header = req.headers.get("Authorization")
        if auth_header and auth_header.startswith("Bearer "):
            token_request = auth_header[len("Bearer ") :]
        else:
            token_request = auth_header

        body = req.get_json()

        # Validate that body contains a list of requests
        if not isinstance(body, list):
            return func.HttpResponse(
                json.dumps({"error": "Request body must be an array of UpdateActionRequest objects"}),
                status_code=HTTPStatus.BAD_REQUEST,
                mimetype=APPLICATION_JSON,
            )

        # Parse each request in the array
        requests = []
        for i, request_data in enumerate(body):
            try:
                request = UpdateActionRequest.model_validate(request_data)
                requests.append(request)
            except Exception as e:
                return func.HttpResponse(
                    json.dumps({"error": f"Invalid request at index {i}: {str(e)}"}),
                    status_code=HTTPStatus.BAD_REQUEST,
                    mimetype=APPLICATION_JSON,
                )

        logging.info(f"Updating {len(requests)} actions in Cognite")
        service = ActionService(
            ActionItemClientFactory.retriever(override_token=token_request),
        )
        external_ids, errors = await service.update_multiple_actions(requests=requests)

        logging.info("UpdateMultipleActions executed successfully")

        return func.HttpResponse(
            json.dumps({
                "externalIds": external_ids,
                "errors": errors,
                "processedCount": len(external_ids),
                "errorCount": len(errors)
            }),
            status_code=(
                HTTPStatus.OK if not errors else HTTPStatus.PARTIAL_CONTENT
            ),
            mimetype=APPLICATION_JSON,
        )

    except Exception as e:
        logging.exception("Error in UpdateMultipleActions")
        return func.HttpResponse(
            f"Error: {e}",
            status_code=HTTPStatus.INTERNAL_SERVER_ERROR,
            mimetype=APPLICATION_JSON,
        )


@bp.function_name(name="UpdateAssigneeRequest")
@bp.route(
    "update-action-assignee-request/{id}",
    methods=["patch"],
    auth_level=func.AuthLevel.ANONYMOUS,
)
async def update_action_assignee_request(req: func.HttpRequest) -> func.HttpResponse:
    logging.info("Function UpdateAssigneeRequest started")
    try:
        auth_header = req.headers.get("Authorization")
        if auth_header and auth_header.startswith("Bearer "):
            token_request = auth_header[len("Bearer ") :]
        else:
            token_request = auth_header

        body = req.get_json()

        request = UpdateActionAssigneeRequest.model_validate(body)

        logging.info("Updating action assignee request in Cognite")
        service = ActionService(
            ActionItemClientFactory.retriever(override_token=token_request),
        )
        external_id, errors = await service.update_action_assignee_request(
            request=request,
        )

        logging.info("UpdateAssigneeRequest executed successfully")

        return func.HttpResponse(
            json.dumps({"externalId": external_id, "errors": errors}),
            status_code=(
                HTTPStatus.OK if not errors else HTTPStatus.INTERNAL_SERVER_ERROR
            ),
            mimetype=APPLICATION_JSON,
        )
    except Exception as e:
        logging.exception("Error in UpdateAssigneeRequest")
        return func.HttpResponse(
            f"Error: {e}",
            status_code=HTTPStatus.INTERNAL_SERVER_ERROR,
            mimetype=APPLICATION_JSON,
        )


@bp.function_name(name="DeleteActions")
@bp.route(
    "delete-actions",
    methods=["put"],
    auth_level=func.AuthLevel.ANONYMOUS,
)
async def delete_actions(req: func.HttpRequest) -> func.HttpResponse:
    logging.info("Function DeleteActions started")
    try:
        auth_header = req.headers.get("Authorization")
        if auth_header and auth_header.startswith("Bearer "):
            token_request = auth_header[len("Bearer ") :]
        else:
            token_request = auth_header

        body = req.get_json()

        request = DeleteActionRequest.model_validate(body)

        logging.info("Soft delete action in Cognite")
        service = ActionService(
            ActionItemClientFactory.retriever(override_token=token_request),
        )
        external_ids, errors = await service.delete_actions(request=request)

        logging.info("DeleteActions executed successfully")

        return func.HttpResponse(
            json.dumps({"externalIds": external_ids, "errors": errors}),
            status_code=(
                HTTPStatus.OK if not errors else HTTPStatus.INTERNAL_SERVER_ERROR
            ),
            mimetype=APPLICATION_JSON,
        )
    except Exception as e:
        logging.exception("Error in DeleteActions")
        return func.HttpResponse(
            f"Error: {e}",
            status_code=HTTPStatus.INTERNAL_SERVER_ERROR,
            mimetype=APPLICATION_JSON,
        )


@bp.function_name(name="CancelActions")
@bp.route(
    "cancel-actions",
    methods=["put"],
    auth_level=func.AuthLevel.ANONYMOUS,
)
async def cancel_actions(req: func.HttpRequest) -> func.HttpResponse:
    logging.info("Function CancelActions started")
    try:
        auth_header = req.headers.get("Authorization")
        if auth_header and auth_header.startswith("Bearer "):
            token_request = auth_header[len("Bearer ") :]
        else:
            token_request = auth_header

        body = req.get_json()

        request = CancelActionRequest.model_validate(body)

        logging.info("Soft cancel action in Cognite")
        service = ActionService(
            ActionItemClientFactory.retriever(override_token=token_request),
        )
        external_ids, errors = await service.cancel_actions(request=request)

        logging.info("CancelActions executed successfully")

        return func.HttpResponse(
            json.dumps({"externalIds": external_ids, "errors": errors}),
            status_code=(
                HTTPStatus.OK if not errors else HTTPStatus.INTERNAL_SERVER_ERROR
            ),
            mimetype=APPLICATION_JSON,
        )
    except Exception as e:
        logging.exception("Error in CancelActions")
        return func.HttpResponse(
            f"Error: {e}",
            status_code=HTTPStatus.INTERNAL_SERVER_ERROR,
            mimetype=APPLICATION_JSON,
        )


@bp.function_name(name="BulkUploadActions")
@bp.route(
    "bulk-upload-actions",
    methods=["post"],
    auth_level=func.AuthLevel.ANONYMOUS,
)
async def bulk_upload_actions(req: func.HttpRequest) -> func.HttpResponse:
    logging.info("Function BulkUploadActions started")
    try:
        auth_header = req.headers.get("Authorization", "")
        token_request = (
            auth_header.replace("Bearer ", "").strip()
            if auth_header.startswith("Bearer ")
            else auth_header
        )

        uploaded_file = req.files.get("file")
        site_id = req.form.get("reportingSiteExternalId")
        columns = json.loads(req.form.get("columns"))
        user_id = req.form.get("userId")
        event_id = req.form.get("eventId")

        service = ActionService(
            ActionItemClientFactory.retriever(override_token=token_request),
        )
        action_items_request, success_count, error_count, errors_list = (
            service.validate_bulk_upload_actions(
                site_id,
                uploaded_file,
                columns,
                user_id,
                event_id,
            )
        )

        if action_items_request:
            logging.info("Starting creation of action items in bulk")
            _, _, actions_ids = await ActionItemCreatorFactory.create().execute(
                action_items_request,
                run_validations_only=False,
                bypass_validations=True,
                send_notifications=False,
            )
            logging.info("Finishing execution")
        else:
            actions_ids = []

        total_rows = success_count + error_count
        result_summary = {
            "successCount": success_count,
            "errorCount": error_count,
            "totalRows": total_rows,
            "errors": errors_list,
            "actionsIds": actions_ids,
        }

        return func.HttpResponse(
            json.dumps(result_summary, ensure_ascii=False),
            status_code=HTTPStatus.OK,
            mimetype="application/json; charset=utf-8",
        )

    except Exception as e:
        logging.exception("Exception found")
        return func.HttpResponse(
            f"Error: {e}",
            status_code=HTTPStatus.INTERNAL_SERVER_ERROR,
        )


@bp.function_name(name="CreateActionEditNotification")
@bp.route(
    "create-action-edit-notification",
    methods=["post"],
    auth_level=func.AuthLevel.ANONYMOUS,
)
async def create_action_edit_notification(req: func.HttpRequest) -> func.HttpResponse:
    try:
        body = req.get_json()
        request = ActionEditNotificationRequest.model_validate(body)

        service = ActionService(ActionItemClientFactory.retriever())
        await service.create_action_edit_notification(request)

        return func.HttpResponse(
            json.dumps({"message": "Notification created successfully"}),
            status_code=HTTPStatus.OK,
            mimetype=APPLICATION_JSON,
        )
    except Exception as e:
        logging.exception("Error in CreateActionEditNotification")
        return func.HttpResponse(
            json.dumps({"error": str(e)}),
            status_code=HTTPStatus.INTERNAL_SERVER_ERROR,
            mimetype=APPLICATION_JSON,
        )


@bp.function_name(name="CreateActionItem")
@bp.route("create-actions", methods=["post"], auth_level=func.AuthLevel.ANONYMOUS)
async def create(req: func.HttpRequest) -> func.HttpResponse:
    logging.info("Function CreateActionItemTrigger started")

    try:
        body = req.get_json()
        if not isinstance(body, list):
            return func.HttpResponse(
                "Request should be a list.",
                status_code=HTTPStatus.BAD_REQUEST,
            )

        logging.info("Getting started creating an action item in Cognite")
        _, errors, actions_ids = await ActionItemCreatorFactory.create().execute(
            body,
            run_validations_only=False,
            bypass_validations=False,
            send_notifications=True,
        )

        if any(len(errors[key]) > 0 for key in errors):
            return func.HttpResponse(
                f"Errors: {errors}",
                status_code=HTTPStatus.INTERNAL_SERVER_ERROR,
            )

        logging.info("Finishing execution")

        return func.HttpResponse(
            json.dumps(
                {
                    "actionsIds": actions_ids,
                },
            ),
            status_code=HTTPStatus.OK,
            mimetype=APPLICATION_JSON,
        )
    except Exception as e:
        logging.exception("Error during action item creation")
        return func.HttpResponse(
            f"Error: {e}",
            status_code=HTTPStatus.INTERNAL_SERVER_ERROR,
        )


@bp.function_name(name="GetExportActions")
@bp.route(
    "get-export-actions",
    methods=["post"],
    auth_level=func.AuthLevel.ANONYMOUS,
)
async def get_export_actions(req: func.HttpRequest) -> func.HttpResponse:
    try:
        auth_header = req.headers.get("Authorization", "")
        token_request = (
            auth_header.replace("Bearer ", "").strip()
            if auth_header.startswith("Bearer ")
            else auth_header
        )

        logging.info("Function GetExportActions started")

        try:
            req_body = req.get_json()
        except ValueError:
            return func.HttpResponse(
                "Invalid JSON body",
                status_code=HTTPStatus.BAD_REQUEST,
            )

        request = ExportActionsRequest.model_validate(req_body)

        service = ActionService(
            ActionItemClientFactory.retriever(override_token=token_request),
        )

        return await service.get_export_actions(request)

    except Exception as e:
        logging.exception(f"Error generating export actions: {e!s}")
        return func.HttpResponse(
            f"Error generating export actions: {e!s}",
            status_code=HTTPStatus.INTERNAL_SERVER_ERROR,
        )
