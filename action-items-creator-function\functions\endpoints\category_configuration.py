import json as json
import logging
from http import HTTPStatus

import azure.functions as func

from clients.category_configuration.requests import (
    GetCategoryConfigurationByFilterRequest,
    GetSiteSpecificCategoriesBySitesRequest,
)
from clients.core.constants import APPLICATION_JSON
from infra.action_item_client_factory import ActionItemClientFactory
from services.category_configuration_service import CategoryConfigurationService

bp = func.Blueprint()


@bp.function_name(name="GetAllCategoryConfigurations")
@bp.route(
    "get-all-category-configurations",
    methods=["get"],
    auth_level=func.AuthLevel.ANONYMOUS,
)
async def main(req: func.HttpRequest) -> func.HttpResponse:
    try:
        auth_header = req.headers.get("Authorization")

        if auth_header and auth_header.startswith("Bearer "):
            token_request = auth_header[len("Bearer ") :]
        else:
            token_request = auth_header

        category_config_request_param = req.params.get(
            "categoryConfigurationRequest",
            "{}",
        )
        category_config_request = json.loads(category_config_request_param)

        logging.info("Function GetAllCategoryConfigurations started")

        request = GetCategoryConfigurationByFilterRequest.model_validate(
            category_config_request,
        )

        service = CategoryConfigurationService(
            ActionItemClientFactory.retriever(override_token=token_request),
        )
        category_config = service.get_category_configurations_by_site(request)

        logging.info("Finishing execution - GetAllCategoryConfigurations")
        if category_config is not None:
            response_body = json.dumps(
                [
                    item.model_dump(mode="json", by_alias=True)
                    for item in category_config
                ],
            )
        else:
            response_body = json.dumps(None)

        return func.HttpResponse(
            response_body,
            mimetype=APPLICATION_JSON,
            status_code=HTTPStatus.OK,
        )
    except Exception as e:
        logging.error(f"Exception found: {e}", exc_info=True)
        return func.HttpResponse(
            f"Error: {e}",
            status_code=HTTPStatus.INTERNAL_SERVER_ERROR,
        )


@bp.function_name(name="GetCategoryConfigurationByConfig")
@bp.route(
    "get-category-configuration-by-config",
    methods=["get"],
    auth_level=func.AuthLevel.ANONYMOUS,
)
def get_category_config_by_config(req: func.HttpRequest) -> func.HttpResponse:
    try:
        auth_header = req.headers.get("Authorization")

        if auth_header and auth_header.startswith("Bearer "):
            token_request = auth_header[len("Bearer ") :]
        else:
            token_request = auth_header

        category_config_request_param = req.params.get(
            "categoryConfigurationRequest",
            "{}",
        )
        category_config_request = json.loads(category_config_request_param)

        logging.info("Function GetCategoryConfigurationByConfig started")

        request = GetCategoryConfigurationByFilterRequest.model_validate(
            category_config_request,
        )

        service = CategoryConfigurationService(
            ActionItemClientFactory.retriever(override_token=token_request),
        )
        category_config = service.get_category_configuration_by_filter(request)

        logging.info("Finishing execution - GetCategoryConfigurationByConfig")
        response_body = (
            json.dumps(category_config.model_dump(mode="json", by_alias=True))
            if category_config is not None
            else json.dumps(None)
        )

        return func.HttpResponse(
            response_body,
            mimetype=APPLICATION_JSON,
            status_code=HTTPStatus.OK,
        )
    except Exception as e:
        logging.error(f"Exception found: {e}", exc_info=True)
        return func.HttpResponse(
            f"Error: {e}",
            status_code=HTTPStatus.INTERNAL_SERVER_ERROR,
        )


@bp.function_name(name="GetSiteSpecificCategoriesBySites")
@bp.route(
    "get-site-specific-categories-by-sites",
    methods=["get"],
    auth_level=func.AuthLevel.ANONYMOUS,
)
def main(req: func.HttpRequest) -> func.HttpResponse:
    """Handle GET requests to retrieve reporting units by site based on request parameters."""
    logging.info("Function GetSiteSpecificCategoriesBySites started")

    try:
        auth_header = req.headers.get("Authorization")
        if auth_header and auth_header.startswith("Bearer "):
            token_request = auth_header[len("Bearer ") :]
        else:
            token_request = auth_header

        site_specific_categories_request_param = req.params.get(
            "siteSpecificCategoriesRequest", "{}"
        )
        site_specific_categories_request = json.loads(
            site_specific_categories_request_param
        )

        request = GetSiteSpecificCategoriesBySitesRequest.model_validate(
            site_specific_categories_request
        )

        client = ActionItemClientFactory.retriever(override_token=token_request)

        response = client.category_configuration.get_site_specific_categories_by_sites(
            request
        )

        items = response.data if response is not None else []

        items_dict = [i.model_dump(mode="json", by_alias=True) for i in items]
        response_body = json.dumps(items_dict)

        logging.info("Finishing execution - GetSiteSpecificCategoriesBySites")

        return func.HttpResponse(
            response_body,
            status_code=HTTPStatus.OK,
            mimetype=APPLICATION_JSON,
        )
    except Exception as e:
        logging.error(f"Exception found: {e}", exc_info=True)
        return func.HttpResponse(
            f"Error: {e}",
            status_code=HTTPStatus.INTERNAL_SERVER_ERROR,
        )
