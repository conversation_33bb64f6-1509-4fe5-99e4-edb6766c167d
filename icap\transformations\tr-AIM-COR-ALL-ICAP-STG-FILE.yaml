# Manifest file downloaded from fusion
externalId: tr-AIM-COR-ALL-ICAP-STG-FILE
name: AIM-COR-ALL-ICAP-STG-FILE
query: >-
  SELECT file_to_migrate.*

  FROM (
    SELECT
      cdf_file.externalId AS key,
      cdf_file.externalId AS source_external_id,
      concat('AIM-', cdf_file.externalId) AS target_external_id,
      dataset_id(if(icap_event.Private, 'AIM-COR-ALL-PROT', utl_reporting_site.aim_space)) AS target_data_set_id,
    	icap_event.Private AS is_private,
      0 AS processed_count,
    	NULL AS reason_to_fail
    FROM `ICAP-COR`.`EVNT-tblEventEvidence` icap_event_evidence
    INNER JOIN `_cdf`.`files` cdf_file ON icap_event_evidence.fileName = cdf_file.name AND icap_event_evidence.EventID = split_part(cdf_file.directory, '/', -1)
    INNER JOIN `ICAP-COR`.`EVNT-tblEvent` icap_event ON icap_event_evidence.EventID = icap_event.EventID
    INNER JOIN `AIM-COR`.`ICAP-MAP-ReportingSite` utl_reporting_site ON utl_reporting_site.key = cast(icap_event.SiteID AS STRING)
    WHERE cdf_file.dataSetId = dataset_id('ICAP-COR-ALL-DAT')
    	AND NOT icap_event.Private
    	AND icap_event_evidence.active
  ) file_to_migrate

  LEFT JOIN `_cdf`.`files` cdf_file ON file_to_migrate.target_external_id = cdf_file.externalId AND file_to_migrate.target_data_set_id = cdf_file.dataSetId

  LEFT JOIN `AIM-COR`.`ICAP-STG-File` file_with_error ON file_with_error.key = file_to_migrate.key

  LEFT JOIN `AIM-COR`.`ICAP-STG-File-DLQ` file_to_migrate_dlq ON file_to_migrate_dlq.key = file_to_migrate.key

  WHERE cdf_file.externalId IS NULL
    	AND file_with_error.key IS NULL
  	AND file_to_migrate_dlq.key IS NULL
destination:
  database: AIM-COR
  table: ICAP-STG-File
  type: raw
ignoreNullFields: true
shared: true
action: upsert
dataSetExternalId: AIM-COR-ALL-DAT
# Specify credentials separately like this:
# authentication:
#   read:
#     clientId: ${READ_CLIENT_ID}
#     clientSecret: ${READ_CLIENT_SECRET}
#     tokenUrl: ${READ_TOKEN_URL}
#     cdfProjectName: ${READ_CDF_PROJECT_NAME}
#     # Optional: If idP requires providing the scopes
#     scopes:
#       - ${READ_SCOPES}
#     # Optional: If idP requires providing the audience
#     audience: ${READ_CDF_AUDIENCE}
#   write:
#     clientId: ${WRITE_CLIENT_ID}
#     clientSecret: ${WRITE_CLIENT_SECRET}
#     tokenUrl: ${WRITE_TOKEN_URL}
#     cdfProjectName: ${WRITE_CDF_PROJECT_NAME}
#     # Optional: If idP requires providing the scopes
#     scopes:
#       - ${WRITE_SCOPES}
#     # Optional: If idP requires providing the audience
#     audience: ${WRITE_CDF_AUDIENCE}
# Or together like this:
# authentication:
#   clientId: ${CLIENT_ID}
#   clientSecret: ${CLIENT_SECRET}
#   tokenUrl: ${TOKEN_URL}
#   # Optional: If idP requires providing the scopes
#   cdfProjectName: ${CDF_PROJECT_NAME}
#   scopes:
#    - ${SCOPES}
#   # Optional: If idP requires providing the audience
#   audience: ${AUDIENCE}