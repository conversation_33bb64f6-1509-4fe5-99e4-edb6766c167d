﻿import React from 'react'
import { Box, Typography } from '@mui/material'
import Link from 'next/link'
import { MatIcon } from '@celanese/ui-lib'
import { translate } from '@/app/common/utils/generate-translate'

export default function ErrorScreen() {
    return (
        <Box
            sx={{
                display: 'flex',
                height: '84vh',
                justifyContent: 'center',
                alignItems: 'center',
                flexDirection: 'column',
                gap: '0.75rem',
            }}
        >
            <MatIcon icon="cancel" color="error.dark" fontSize="10rem" sx={{ fontVariationSettings: "'FILL' 1" }} />
            <Typography
                sx={{
                    fontSize: '1.25rem',
                    textAlign: 'center',
                    color: 'text.secondary',
                    fontWeight: 'bold',
                    opacity: '70%',
                }}
            >
                {translate('alerts.noAccess')}
            </Typography>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: '6px' }}>
                <Typography
                    sx={{
                        fontSize: '1.25rem',
                        textAlign: 'center',
                        color: 'text.secondary',
                        fontWeight: 'bold',
                        opacity: '70%',
                    }}
                >
                    {translate('alerts.redirect')}
                </Typography>
                <Link href="https://usermgmt.celanese.com/user-settings/new-request">
                    <Typography
                        sx={{
                            fontSize: '1.25rem',
                            textAlign: 'center',
                            color: 'primary.dark',
                            textDecoration: 'underline',
                            fontWeight: 'bold',
                        }}
                    >
                        {translate('alerts.here')}
                    </Typography>
                </Link>
            </Box>
        </Box>
    )
}
