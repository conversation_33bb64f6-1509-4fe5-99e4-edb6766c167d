class DataModelQueryError(Exception):
    """Exception raised when querying the data model fails."""

    def __init__(self, source_id: str, message: str) -> None:
        """
        Initialize the DataModelQueryError exception.

        Args:
            source_id (str): The external source identifier related to the failed query.
            message (str): Description of the error that occurred.

        """
        full_message = f"[DataModelQueryError] source_id={source_id} - {message}"
        super().__init__(full_message)
        self.source_id = source_id
        self.message = message
