'use client'
import '../common/utils/polyfills'
import dynamic from 'next/dynamic'
import AuthGuardWrapper from '../common/wrapper/AuthGuardWrapper'
import { Box, ClnButton, ClnPage, ClnPanel, styled } from '@celanese/ui-lib'
import { environment } from '../common/configurations/environment'
import { useContext, useEffect, useMemo, useState } from 'react'
import { AimTabs } from '../components/Tabs'
import {
    TranslationContext,
    TranslationContextState,
    UserManagementContext,
    UserManagementContextState,
} from '@celanese/celanese-ui'
import { useAuthGuard } from '../common/hooks/useAuthGuard'
import { z } from 'zod'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import GenericTextField from '../components/FieldsComponent/GenericTextField'
import { UserRolesPermission } from '@celanese/celanese-sdk'
import { UserExternalContext, UserExternalContextState } from '../common/contexts/UserExternalContext'
import PageHeader from '../components/PageHeader'

const ClnActionItemTable = dynamic(() => import('@celanese/celanese-ui').then((mod) => mod.ClnActionItemTable), {
    ssr: false,
})
const ClnActionItemDetails = dynamic(() => import('@celanese/celanese-ui').then((mod) => mod.ClnActionItemDetails), {
    ssr: false,
})
const ClnActionItemDrawer = dynamic(() => import('@celanese/celanese-ui').then((mod) => mod.ClnActionItemDrawer), {
    ssr: false,
})

const Form = styled('form')({
    width: '100% !important',
    display: 'flex',
    flexDirection: 'column',
    gap: '1rem',
})

const templateSchema = z.object({
    actionExternalId: z.string(),
})

type SDKDetailsSchema = z.infer<typeof templateSchema>

export default function NewActionItemPage() {
    const { userInfo } = useContext<UserManagementContextState>(UserManagementContext)
    const { userExternalInfo } = useContext<UserExternalContextState>(UserExternalContext)
    const { checkPermissionsFromComponents } = useAuthGuard()
    const { locale } = useContext<TranslationContextState>(TranslationContext)

    const [activeUser, setActiveUser] = useState<UserRolesPermission>()
    const [currentTab, setCurrentTab] = useState(0)
    const [selectTemplate, setSelectTemplate] = useState<string | undefined>()

    const tabs = useMemo(
        () => [
            {
                label: 'Table',
                auth: 'SDKTable',
                content: '',
            },
            {
                label: 'Details',
                auth: 'SDKDetails',
                content: '',
            },
            {
                label: 'New Action',
                auth: 'SDKNewAction',
                content: '',
            },
        ],
        [locale]
    )
    const authorizedTabs = tabs.filter((item) => checkPermissionsFromComponents(item.auth).isAuthorized)

    const { setValue, getValues, handleSubmit, reset, control } = useForm<SDKDetailsSchema>({
        defaultValues: { actionExternalId: undefined },
        resolver: zodResolver(templateSchema),
    })

    const handleConfirm = () => {
        const { actionExternalId } = getValues()
        if (actionExternalId) {
            setSelectTemplate(actionExternalId)
        }
    }

    useEffect(() => {
        if (
            userExternalInfo.externalId &&
            userExternalInfo.externalId !== '' &&
            (activeUser?.externalId !== userExternalInfo.externalId ||
                activeUser?.applications !== userInfo.applications)
        ) {
            setActiveUser({
                ...userInfo,
                externalId: userExternalInfo.externalId,
                roles: userExternalInfo.roles,
                teams: userExternalInfo.teams,
            })
        }
    }, [userInfo, userExternalInfo])

    if (!environment.featureEnableSDKPage) {
        return <AuthGuardWrapper componentName={'UnknownComponent'}>{null}</AuthGuardWrapper>
    }

    const renderSelectActionExternalId = () => {
        return (
            <Form onSubmit={handleSubmit(handleConfirm)} id="template-selector-form">
                <GenericTextField
                    name="actionExternalId"
                    control={control}
                    onChange={(newValue) => setValue('actionExternalId', newValue)}
                    label="Action external Id"
                    required
                />
                <Box id="find-action-button" sx={{ width: '100%', display: 'flex', justifyContent: 'flex-end' }}>
                    <ClnButton
                        type="submit"
                        size="small"
                        variant="contained"
                        label="Find Action"
                        data-test="new_action_item_menu_template_modal-create_from_template_button"
                        data-origin="ui-lib"
                    />
                </Box>
            </Form>
        )
    }

    const renderCurrentTab = () => {
        if (currentTab === 0) {
            return (
                <Box p={2}>
                    <ClnActionItemTable
                        applicationCode={environment.userManagementAppCode!}
                        activeUserEmail={activeUser?.email ?? ''}
                        siteId={activeUser?.selectedSites?.[0].siteId!}
                        defaultOptionsFilter={[
                            {
                                filterId: 'assignedToExternalId',
                                hidden: false,
                                defaultValue: [],
                            },
                        ]}
                    />
                </Box>
            )
        }

        if (currentTab === 1) {
            return (
                <Box p={2}>
                    {selectTemplate ? (
                        <ClnActionItemDetails
                            externalId={selectTemplate!}
                            activeUserEmail={activeUser?.email ?? ''}
                            siteId={activeUser?.selectedSites?.[0].siteId!}
                            closeDetails={() => setSelectTemplate(undefined)}
                        />
                    ) : (
                        renderSelectActionExternalId()
                    )}
                </Box>
            )
        }

        return (
            <Box p={2}>
                <ClnActionItemDrawer
                    applicationCode={environment.userManagementAppCode!}
                    siteId={activeUser?.selectedSites?.[0].siteId!}
                />
            </Box>
        )
    }

    return (
        <AuthGuardWrapper componentName={NewActionItemPage.name}>
            <ClnPage>
                <PageHeader title="AIM SDK" />
                <ClnPanel id="sdk-panel" sx={{ display: 'flex', width: '100%' }}>
                    {activeUser?.email && (
                        <Box sx={{ display: 'flex', flexDirection: 'column', width: '100%' }}>
                            <AimTabs
                                value={currentTab}
                                tabs={authorizedTabs}
                                onChange={(_e, value) => setCurrentTab(value)}
                            />
                            <Box>{renderCurrentTab()}</Box>
                        </Box>
                    )}
                </ClnPanel>
            </ClnPage>
        </AuthGuardWrapper>
    )
}
