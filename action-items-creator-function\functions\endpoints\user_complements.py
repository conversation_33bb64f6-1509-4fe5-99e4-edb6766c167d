import azure.functions as func
from http import HTTPStatus
import json as json
import logging

from clients.core.constants import APPLICATION_JSON
from clients.user_complement.requests import (
    GetUserRolesAndTeamsRequest,
    GetUserComplementByLocationsRequest,
    GetUserComplementByUnitsRequest,
)
from infra.action_item_client_factory import ActionItemClientFactory

bp = func.Blueprint()


@bp.function_name(name="GetUserComplementsByUnits")
@bp.route(
    "get-user-complements_by-units",
    methods=["get"],
    auth_level=func.AuthLevel.ANONYMOUS,
)
def main(req: func.HttpRequest) -> func.HttpResponse:
    logging.info(f"Function GetUserComplementsByUnits started")

    try:
        auth_header = req.headers.get("Authorization")
        if auth_header and auth_header.startswith("Bearer "):
            token_request = auth_header[len("Bearer "):]
        else:
            token_request = auth_header

        user_complement_request_param = req.params.get("userComplementRequest", "{}")
        user_complement_request = json.loads(user_complement_request_param)

        request = GetUserComplementByUnitsRequest.model_validate(
            user_complement_request
        )

        retriever = ActionItemClientFactory.retriever(override_token=token_request)

        items = retriever.user_complement.get_user_complements_by_units(request)

        logging.info(f"Finishing execution - GetUserComplementsByUnits")

        response_body = json.dumps(items)

        return func.HttpResponse(
            response_body,
            status_code=HTTPStatus.OK,
            mimetype=APPLICATION_JSON,
        )
    except Exception as e:
        logging.error(f"Exception found: {e}", exc_info=True)
        return func.HttpResponse(
            f"Error: {e}", status_code=HTTPStatus.INTERNAL_SERVER_ERROR
        )


@bp.function_name(name="GetUserComplementsByLocations")
@bp.route(
    "get-user-complements_by-locations",
    methods=["get"],
    auth_level=func.AuthLevel.ANONYMOUS,
)
def main(req: func.HttpRequest) -> func.HttpResponse:
    logging.info(f"Function GetUserComplementsByLocations started")

    try:
        auth_header = req.headers.get("Authorization")
        if auth_header and auth_header.startswith("Bearer "):
            token_request = auth_header[len("Bearer "):]
        else:
            token_request = auth_header

        user_complement_request_param = req.params.get("userComplementRequest", "{}")
        user_complement_request = json.loads(user_complement_request_param)

        request = GetUserComplementByLocationsRequest.model_validate(
            user_complement_request
        )

        retriever = ActionItemClientFactory.retriever(override_token=token_request)

        items = retriever.user_complement.get_user_complements_by_locations(request)

        logging.info(f"Finishing execution - GetUserComplementsByLocations")

        response_body = json.dumps(items)

        return func.HttpResponse(
            response_body,
            status_code=HTTPStatus.OK,
            mimetype=APPLICATION_JSON,
        )
    except Exception as e:
        logging.error(f"Exception found: {e}", exc_info=True)
        return func.HttpResponse(
            f"Error: {e}", status_code=HTTPStatus.INTERNAL_SERVER_ERROR
        )


@bp.function_name(name="GetUserRolesAndTeams")
@bp.route(
    "get-user-roles-and-teams",
    methods=["get"],
    auth_level=func.AuthLevel.ANONYMOUS,
)
def main(req: func.HttpRequest) -> func.HttpResponse:
    logging.info(f"Function GetUserRolesAndTeams started")

    try:
        auth_header = req.headers.get("Authorization")
        if auth_header and auth_header.startswith("Bearer "):
            token_request = auth_header[len("Bearer "):]
        else:
            token_request = auth_header

        user_request_param = req.params.get("userRequest", "{}")
        user_request = json.loads(user_request_param)

        request = GetUserRolesAndTeamsRequest.model_validate(user_request)

        retriever = ActionItemClientFactory.retriever(override_token=token_request)

        items = retriever.user_complement.get_user_roles_and_teams(request)

        logging.info(f"Finishing execution - GetUserRolesAndTeams")

        items_dict = items.model_dump(mode="json", by_alias=True)
        response_body = json.dumps(items_dict)

        return func.HttpResponse(
            response_body,
            status_code=HTTPStatus.OK,
            mimetype=APPLICATION_JSON,
        )
    except Exception as e:
        logging.error(f"Exception found: {e}", exc_info=True)
        return func.HttpResponse(
            f"Error: {e}", status_code=HTTPStatus.INTERNAL_SERVER_ERROR
        )
