import pymsteams
from pydantic import BaseModel


class TeamsNotificationInput(BaseModel):
    title: str
    text: str
    activity_messages: list[str]
    button_text: str | None = None
    button_action_link: str | None = None

    def to_card_sections(self):
        for activity_message in self.activity_messages:
            section = pymsteams.cardsection()
            section.activityText(activity_message)
            yield section


class TeamsNotificationService:
    def __init__(self, uri: str) -> None:
        self._uri = uri

    def send_message(self, message: str | TeamsNotificationInput) -> bool:
        if not self._uri:
            return False
        try:
            connector = pymsteams.connectorcard(self._uri)

            if isinstance(message, str):
                connector.text(message)
            else:
                for section in message.to_card_sections():
                    connector.addSection(section)
                connector.text(message.text)
                connector.title(message.title)
                if message.button_text and message.button_action_link:
                    connector.addLinkButton(
                        buttontext=message.button_text,
                        buttonurl=message.button_action_link,
                    )
            return connector.send()
        except Exception:
            return False
