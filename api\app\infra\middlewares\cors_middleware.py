from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

from app.core.dependencies import get_settings


def add(app: FastAPI) -> None:
    """Add cors middleware to the app."""
    app.add_middleware(
        CORSMiddleware,
        allow_origin_regex=get_settings().valid_origins_regex,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
