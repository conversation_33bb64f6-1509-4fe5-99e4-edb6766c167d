import { gql } from '@apollo/client'
import { useEffect, useState } from 'react'
import { useGraphqlQuery } from '../'
import { CategoryConfiguration } from '../../models/category-configuration'
import { EntityType, GetSpace } from '../../utils/space-util'
import { getLocalUserSite } from '@celanese/celanese-ui'

const buildCategoryConfigurationQuery = (): string => {
    const filters: string[] = []

    filters.push(`{ space: { eq: "${GetSpace(EntityType.Instance, getLocalUserSite()?.siteCode)}" } }`)

    const queryFilter = `{ and: [ ${filters.join(', ')} ] }`

    return `
        query GetCategoriesConfiguration {
            listCategoriesConfiguration(
                filter: ${queryFilter}
                , first: 1000
            ) {
                items {
                    externalId
                    space
                    category{
                        externalId
                        name
                        description
                    }
                    actionItemSubCategory{
                        externalId
                        name
                        description
                    }
                    siteSpecificCategory{
                        externalId
                        name
                        description
                    }
                    daysFromAssignedDate
                    hasEmailNotification
                    attachmentRequired
                    isApprovalRequired
                    defaultApprovalRole {
                        name
                        externalId
                    }
                    daysToApproval
                    isVerificationRequired
                    defaultVerificationRole {
                        name
                        externalId
                    }
                    defaultExtensionApproverRole {
                        name
                      	externalId
                    }
                    daysToVerification
                    isExtensionsAllowed
                    defaultExtensionApproverRole {
                        name
                    }
                    isExtensionAttachmentRequired
                    defaultApprovalUser {
                        externalId
                        space
                        email
                    }
                    defaultVerificationUser {
                        externalId
                        space
                        email
                    }
                }
            }
        }
    `
}

export const useCategoryConfigurations = () => {
    const query = buildCategoryConfigurationQuery()
    const { data: fdmData, refetch } = useGraphqlQuery<CategoryConfiguration>(
        gql(query),
        'listCategoriesConfiguration',
        {}
    )

    const [resultData, setResultData] = useState<{ data: CategoryConfiguration[]; loading: boolean }>({
        data: [],
        loading: true,
    })
    useEffect(() => {
        if (fdmData.length == 0) {
            setResultData({ data: [], loading: false })
        } else {
            setResultData({ data: fdmData, loading: false })
        }
    }, [fdmData])

    return {
        loading: resultData.loading,
        categoryConfig: resultData.data,
        refetchConfig: refetch,
    }
}
