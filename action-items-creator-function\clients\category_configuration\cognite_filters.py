from typing import Any

from clients.core.constants import (
    DataSpaceEnum,
)
from clients.core.filters import (
    in_filter,
    is_null_filter,
    node_id_filter,
)

from .requests import GetCategoryConfigurationByFilterRequest


def _get_categories_configuration_space_filter(
    request: GetCategoryConfigurationByFilterRequest,
):
    return in_filter("space", request.get_filter_spaces())


def _get_categories_configuration_filter(
    request: GetCategoryConfigurationByFilterRequest,
    filter_only_by_site: bool = False,
):
    filters: list[Any] = [
        _get_categories_configuration_space_filter(request),
    ]

    if request.non_null_extension_approver_role:
        filters.append(
            {"defaultExtensionApproverRole": is_null_filter("externalId", False)}
        )

    if filter_only_by_site:
        return {"and": filters}

    if request.category_id:
        filters.append(
            {
                "category": node_id_filter(
                    request.category_id, DataSpaceEnum.AIM_REF_DATA_SPACE
                )
            }
        )

    if request.sub_category_id:
        filters.append(
            {
                "actionItemSubCategory": node_id_filter(
                    request.sub_category_id, DataSpaceEnum.AIM_REF_DATA_SPACE
                )
            }
        )

    if request.site_specific_category_id:
        filters.append(
            {
                "siteSpecificCategory": node_id_filter(
                    request.site_specific_category_id,
                    request.get_filter_spaces(True),
                )
            }
        )
    elif not request.site_specific_category_id:
        filters.append({"siteSpecificCategory": is_null_filter("externalId", True)})

    return {"and": filters}


def get_categories_configuration_filters(
    request: GetCategoryConfigurationByFilterRequest,
    filter_only_by_site: bool = False,
):
    return {
        "pageSize": request.page_size,
        "cursor": request.cursor,
        "categoriesConfigurationFilter": _get_categories_configuration_filter(
            request, filter_only_by_site
        ),
    }
