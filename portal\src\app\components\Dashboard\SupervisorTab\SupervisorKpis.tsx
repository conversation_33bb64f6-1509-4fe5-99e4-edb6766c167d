import { TranslationContext, TranslationContextState } from '@celanese/celanese-ui'
import { Box } from '@mui/material'
import { useCallback, useContext, useEffect, useMemo, useState } from 'react'
import { AzureFunctionClient } from '@/app/common/clients/azure-function-client'
import { ActionStatusExternalIdClearEnum } from '@/app/common/enums/ActionItemStatusEnum'
import { KPICard } from '../KPICard'
import LoaderCircular from '../../Loader'
import { useDebounceFunction } from '@/app/common/hooks/general-functions/useDebounce'
import { FilterInfoProps } from '../../ActionTable/HomeFilter'
import { SupervisorTabKpisResponseData } from '@/app/common/models/kpis_response'
import { translate } from '@/app/common/utils/generate-translate'
import { getLocalUserSite } from '@celanese/celanese-ui'
import { UserRolesPermission } from '@celanese/celanese-sdk'

type SupervisorKpisProps = {
    filterInfo: FilterInfoProps
    client: AzureFunctionClient
    activeUser?: UserRolesPermission
    setTotalEmployees: (value: number) => void
    handleError: (err: any) => void
}

export const stylesSite = {
    kpiWrapper: {
        gap: '1rem',
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(180px, 1fr))',
        '@media (max-width:800px)': {
            gridTemplateColumns: 'repeat(1, 1fr)',
        },
    },
}

export function SupervisorKpis({
    filterInfo,
    client,
    activeUser,
    setTotalEmployees,
    handleError,
}: SupervisorKpisProps) {
    const { siteId: site } = getLocalUserSite() || {}
    const { locale } = useContext<TranslationContextState>(TranslationContext)

    const [kpisValues, setKpisValues] = useState<SupervisorTabKpisResponseData>()
    const [loadingKpisFilter, setLoadingKpisFilter] = useState<boolean>(false)

    const kpisParams = useMemo(() => {
        return [
            {
                key: 'kpi-employees-supervisor',
                label: translate('dashboards.kpis.employees'),
                value: `${kpisValues?.employees ?? 0}`,
            },
            {
                key: 'kpi-overdue-supervisor',
                label: translate('dashboards.kpis.overdue'),
                value: `${kpisValues?.overdue ?? 0}`,
            },
            {
                key: 'kpi-within-7-days-actions',
                label: translate('dashboards.kpis.taskWithin7Days'),
                value: `${kpisValues?.tasksWithin7Days ?? 0}`,
            },
            {
                key: 'kpi-within-30-days-actions',
                label: translate('dashboards.kpis.tasksWithinThisMonth'),
                value: `${kpisValues?.tasksWithinThisMonth ?? 0}`,
            },
            {
                key: 'kpi-current-tasks-actions',
                label: translate('dashboards.kpis.dueDateOver30Days'),
                value: `${kpisValues?.dueDateOver30Days ?? 0}`,
            },
            {
                key: 'kpi-pending-approvals-actions',
                label: translate('dashboards.kpis.pendingApproval'),
                value: `${kpisValues?.pendingApproval ?? 0}`,
            },
            {
                key: 'kpi-pending-verifications-actions',
                label: translate('dashboards.kpis.pendingVerification'),
                value: `${kpisValues?.pendingVerification ?? 0}`,
            },
        ]
    }, [translate, locale, kpisValues])

    const fetchKpi = useCallback(async () => {
        if (activeUser?.email === undefined) return undefined
        try {
            const response = await client.getSupervisorTabKpis({
                activeUserEmail: activeUser?.email,
                tab: 'Supervisor',
                filters: {
                    reportingSiteExternalId: site,
                    dueDateGte: filterInfo?.dueDateGte ?? null,
                    dueDateLt: filterInfo?.dueDateLt ?? null,
                    reportingUnitExternalIds: filterInfo.reportingUnitExternalIds,
                    reportingLocationExternalIds: filterInfo.reportingLocationExternalIds,
                    categoryExternalIds: filterInfo.categoryExternalId,
                    statusExternalIds:
                        filterInfo && filterInfo?.statusExternalIds && filterInfo?.statusExternalIds?.length > 0
                            ? filterInfo?.statusExternalIds
                            : Object.values(ActionStatusExternalIdClearEnum),
                    updateStatus: filterInfo.updateStatusDate,
                    assigneeExternalIds: filterInfo.assignedToExternalId,
                    onlyPrivate: filterInfo.onlyPrivate,
                    sourceEventTitleEq: filterInfo.sourceEventTitleEq ?? undefined,
                },
            })

            setKpisValues(response)
            setTotalEmployees(response.employees)
        } catch (err) {
            handleError(err)
        } finally {
            setLoadingKpisFilter(false)
        }
    }, [filterInfo])

    const debouncedFetchKpiSupervisor = useCallback(useDebounceFunction(fetchKpi, 500), [fetchKpi])

    useEffect(() => {
        if (loadingKpisFilter) return

        setLoadingKpisFilter(true)
        debouncedFetchKpiSupervisor()
    }, [fetchKpi])

    return (
        <Box sx={stylesSite.kpiWrapper} id={'kpis-site'}>
            {loadingKpisFilter ? (
                LoaderCircular()
            ) : (
                <>
                    {kpisParams.map((x) => (
                        <KPICard key={x.key} id={x.key} label={x.label} amount={x.value} />
                    ))}
                </>
            )}
        </Box>
    )
}

export default SupervisorKpis
