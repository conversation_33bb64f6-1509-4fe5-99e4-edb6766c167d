# Manifest file downloaded from fusion
externalId: tr-AIM-COR-ALL-ICAP-ACT-SOURCEID
name: AIM-COR-ALL-ICAP-ACT-SOURCEID
query: >-
  WITH cte AS (
    SELECT 
      aim_action.space AS space,
      aim_action.externalId AS externalId,
      if(
    		isnotnull(icap_moc.externalId), 
      	node_reference('AIM-COR-ALL-REF', 'AST-ICAP-ICAPMOCReport'), 
      	if(
    			isnotnull(icap_mooc.externalId), 
      		node_reference('AIM-COR-ALL-REF', 'AST-ICAP-ICAPMOOCReport'), 
      		aim_action.sourceType
      	)
      ) AS sourceType,
      coalesce(
    		icap_moc.externalId, 
    		icap_mooc.externalId, 
    		aim_event.externalId,
    		NULL
    	) AS sourceId,
    	ROW_NUMBER() OVER (PARTITION BY aim_action.externalId, aim_action.space ORDER BY aim_action.space) AS row_num
    FROM cdf_data_models('AIM-COR-ALL-DMD', 'ActionItemManagementDOM', '6_0_0', 'Action') AS aim_action
    LEFT JOIN cdf_data_models('ICAP-COR-ALL-DMD', 'ICAPDOM', '1_4_2', 'ICAPActionReport') AS icap_action_report 
    	ON concat(split(aim_action.objectType, '-')[1], '') = concat(cast(icap_action_report.`number` AS STRING), '')
    LEFT JOIN cdf_data_models('ICAP-COR-ALL-DMD', 'ICAPDOM', '1_4_2', 'ICAPMOCReport') AS icap_moc
    	ON concat(icap_action_report.externalId, '') = concat(icap_moc.actionReport.externalId, '')
    LEFT JOIN cdf_data_models('ICAP-COR-ALL-DMD', 'ICAPDOM', '1_4_2', 'ICAPMOOCReport', 'actionReports') AS icap_moc_to_action_report 
    	ON concat(icap_moc_to_action_report.endNode.externalId, '') = concat(icap_action_report.externalId, '')
    LEFT JOIN cdf_data_models('ICAP-COR-ALL-DMD', 'ICAPDOM', '1_4_2', 'ICAPMOOCReport') AS icap_mooc 
    	ON concat(icap_moc_to_action_report.startNode.externalId, '') = concat(icap_mooc.externalId, '')
    LEFT JOIN `ICAP-COR`.`AXN-tblActionItem` AS icap_action ON split(aim_action.objectType, '-')[1] = icap_action.ActionItemID
    LEFT JOIN cdf_data_models('AIM-COR-ALL-DMD', 'ActionItemManagementDOM', '6_0_0', 'SourceEvent') aim_event
      ON aim_event.iCAPEventID = split_part(aim_action.objectType, '-', 4) AND aim_event.space = aim_action.space
    WHERE startswith(aim_action.objectType, 'ICAP') 
    	AND aim_action.sourceType IS NOT NULL
    	AND aim_action.viewOnly
  )

  SELECT 
  	externalId,
  	space,
  	sourceType,
  	sourceId
  FROM cte

  WHERE row_num = 1
destination:
  dataModel:
    space: AIM-COR-ALL-DMD
    externalId: ActionItemManagementDOM
    version: "6_0_0"
    destinationType: Action
  type: instances
ignoreNullFields: false
shared: true
action: upsert
dataSetExternalId: AIM-COR-ALL-DAT
# Specify credentials separately like this:
# authentication:
#   read:
#     clientId: ${READ_CLIENT_ID}
#     clientSecret: ${READ_CLIENT_SECRET}
#     tokenUrl: ${READ_TOKEN_URL}
#     cdfProjectName: ${READ_CDF_PROJECT_NAME}
#     # Optional: If idP requires providing the scopes
#     scopes:
#       - ${READ_SCOPES}
#     # Optional: If idP requires providing the audience
#     audience: ${READ_CDF_AUDIENCE}
#   write:
#     clientId: ${WRITE_CLIENT_ID}
#     clientSecret: ${WRITE_CLIENT_SECRET}
#     tokenUrl: ${WRITE_TOKEN_URL}
#     cdfProjectName: ${WRITE_CDF_PROJECT_NAME}
#     # Optional: If idP requires providing the scopes
#     scopes:
#       - ${WRITE_SCOPES}
#     # Optional: If idP requires providing the audience
#     audience: ${WRITE_CDF_AUDIENCE}
# Or together like this:
# authentication:
#   clientId: ${CLIENT_ID}
#   clientSecret: ${CLIENT_SECRET}
#   tokenUrl: ${TOKEN_URL}
#   # Optional: If idP requires providing the scopes
#   cdfProjectName: ${CDF_PROJECT_NAME}
#   scopes:
#    - ${SCOPES}
#   # Optional: If idP requires providing the audience
#   audience: ${AUDIENCE}