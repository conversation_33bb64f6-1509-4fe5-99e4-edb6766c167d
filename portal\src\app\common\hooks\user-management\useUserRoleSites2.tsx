import { useEffect, useState } from 'react'
import { UserRoleSite } from '../../models/user-role-site'
import { useGraphqlQuery } from '..'
import { gql } from '@apollo/client'
import { getLocalUserSite } from '@celanese/celanese-ui'

export interface UserRoleSiteRequest {
    roleId?: string
}

const buildUserRoleSiteQuery = (request: UserRoleSiteRequest | null): string => {
    const filters: string[] = []

    filters.push(`{ reportingSite: { externalId: { eq: "${getLocalUserSite()?.siteId ?? ''}" } } }`)
    filters.push(`{ role: { roleCategory: { externalId: { eq: "RoleSite" } } } }`)

    if (request?.roleId) {
        filters.push(`{ role: { externalId: { eq: "${request.roleId}" } } }`)
    }

    const queryFilter = `{ and: [ ${filters.join(', ')} ] }`

    const userComplementFilter: string[] = []

    userComplementFilter.push(
        `{ employeeStatus: { or: [ { not: { externalId: { eq: "EMST_INACTIVE" } } }, { externalId: { isNull: true } } ] } }`
    )
    userComplementFilter.push(`{ userAzureAttribute: { user: { active: { eq: true } } } }`)

    const userComplementQueryFilter = `{ and: [ ${userComplementFilter.join(', ')} ] }`

    return `
        query GetUserRoleSite {
            listUserRoleSite(
                filter: ${queryFilter}
                , first: 1000
            ) {
                items {
                    externalId
                    role {
                        externalId
                        space
                    }
                    reportingSite {
                        externalId
                        space
                    }
                    usersComplements(
                        filter: ${userComplementQueryFilter}
                        , first: 1000
                    ) {
                        items {
                            userAzureAttribute {
                                user {
                                    externalId
                                    lastName
                                    firstName
                                    email
                                    active
                                }
                            }
                            reportingUnits {
                                items {
                                    externalId
                                    space
                                }
                            }
                        }
                    }
                }
            }
        }
    `
}

export const useUserRoleSites = (request: UserRoleSiteRequest) => {
    const query = buildUserRoleSiteQuery(request)

    const { data: fdmData } = useGraphqlQuery<UserRoleSite>(gql(query), 'listUserRoleSite', {})

    const [resultData, setResultData] = useState<{ data: UserRoleSite[]; loading: boolean }>({
        data: [],
        loading: true,
    })

    useEffect(() => {
        if (fdmData.length == 0) {
            setResultData({ data: [], loading: false })
        } else {
            setResultData({ data: fdmData, loading: false })
        }
    }, [fdmData])

    return {
        loading: resultData.loading,
        userRoleSite: resultData.data,
    }
}
