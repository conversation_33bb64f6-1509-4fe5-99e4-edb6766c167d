import azure.functions as func
from clients.core.constants import APPLICATION_JSON

bp = func.Blueprint()


@bp.function_name(name="CreateActionItemTrigger")
@bp.route(
    "create-action-item-trigger", methods=["post"], auth_level=func.AuthLevel.ANONYMOUS
)
async def main(req: func.HttpRequest) -> func.HttpResponse:
    import logging
    import os
    import sys
    import json as json

    sys.path.append(
        os.path.abspath(os.path.join(os.path.dirname(__file__), "..", ".."))
    )
    from infra.action_item_creator_factory import ActionItemCreatorFactory

    logging.info("Function CreateActionItemTrigger started")

    try:
        body = req.get_json()
        action_item_request = body.get("actionItemRequest")

        if not action_item_request:
            raise ValueError("Missing 'actionItemRequest' in request body")

        logging.info("Getting started creating an action item in Cognite")
        _, errors, actions_ids = await ActionItemCreatorFactory.create().execute(
            action_item_request, False, True, False
        )

        if any(len(errors[key]) > 0 for key in errors):
            return func.HttpResponse(f"Errors: {errors}", status_code=500)

        logging.info("Finishing execution")

        return func.HttpResponse(
            json.dumps(
                {
                    "actionsIds": actions_ids,
                }
            ),
            status_code=200,
            mimetype=APPLICATION_JSON,
        )
    except Exception as e:
        logging.error(f"Error during action item creation: {e}", exc_info=True)
        return func.HttpResponse(f"Error: {e}", status_code=500)
