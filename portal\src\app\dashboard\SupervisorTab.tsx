'use client'
import { Box, useMediaQuery, useTheme } from '@mui/material'
import { useState } from 'react'
import { ClnPanel } from '@celanese/ui-lib'
import { Category, Status } from '../common/models/action'
import { styles, stylesSupervisor } from './styles'
import AuthGuardWrapper from '../common/wrapper/AuthGuardWrapper'
import SupervisorTable from '../components/Dashboard/SupervisorTab/SupervisorTable'
import SupervisorKpis from '../components/Dashboard/SupervisorTab/SupervisorKpis'
import SupervisorCharts from '../components/Dashboard/SupervisorTab/SupervisorCharts'
import { AzureFunctionClient } from '../common/clients/azure-function-client'
import { FilterInfoProps } from '../components/ActionTable/HomeFilter'
import GenericFieldTitle from '../components/FieldsComponent/GenericFieldTitle'
import { useSnackbar } from '../common/contexts/SnackbarContext'
import { translate } from '../common/utils/generate-translate'
import { UserRolesPermission } from '@celanese/celanese-sdk'

interface SupervisorTabProps {
    siteId: string
    categories: Category[]
    allStatus: Status[]
    activeUser?: UserRolesPermission
    isSupervisorTab: boolean
    filterInfo: FilterInfoProps
    setEmployeesListById: (employeesIds: string[]) => void
    currentPage: number
    setCurrentPage: (value: number) => void
}

export const SupervisorTab = ({
    siteId,
    categories,
    allStatus,
    activeUser,
    isSupervisorTab,
    filterInfo,
    setEmployeesListById,
    currentPage,
    setCurrentPage,
}: SupervisorTabProps) => {
    const theme = useTheme()
    const isMobile = useMediaQuery(theme.breakpoints.down('md'))

    const client = new AzureFunctionClient()

    const { showSnackbar } = useSnackbar()

    const [isChart, setIsChart] = useState(true)
    const [totalEmployees, setTotalEmployees] = useState(0)

    const handleError = (err: any) => {
        const errorMessage = err instanceof Error ? err.message : `${translate('alerts.unexpectedErrorOcurred')}`
        showSnackbar(
            `${translate('alerts.unexpectedErrorOcurred')}: ${errorMessage}`,
            'error',
            'dashboard-supervisor-tab'
        )
    }

    const renderChartFragment = () => {
        return isChart ? (
            <SupervisorCharts
                categories={categories}
                allStatus={allStatus}
                client={client}
                filterInfo={filterInfo}
                activeUser={activeUser}
                handleError={handleError}
                setIsChart={setIsChart}
                setEmployeesListById={setEmployeesListById}
                totalEmployees={totalEmployees}
            />
        ) : (
            <Box id={'dashboard-supervisor-table'}>
                <SupervisorTable
                    client={client}
                    siteId={siteId}
                    filterInfo={filterInfo}
                    activeUser={activeUser}
                    currentPage={currentPage}
                    handleError={handleError}
                    setIsChart={setIsChart}
                    setCurrentPage={setCurrentPage}
                />
            </Box>
        )
    }

    return (
        <AuthGuardWrapper componentName={SupervisorTab.name}>
            {isSupervisorTab && (
                <Box sx={styles.container}>
                    <Box sx={stylesSupervisor.kpiWrapper} id={'kpis-supervisor'}>
                        <SupervisorKpis
                            client={client}
                            filterInfo={filterInfo}
                            activeUser={activeUser}
                            handleError={handleError}
                            setTotalEmployees={setTotalEmployees}
                        />
                    </Box>
                    {!isMobile && (
                        <ClnPanel
                            sx={{
                                padding: '1rem',
                                display: 'flex',
                                flexDirection: 'column',
                            }}
                            id={'dashboard-supervisor-tab-graphc-and-table-panel'}
                        >
                            <GenericFieldTitle fieldName={translate('dashboards.tabs.supervisor')} isSubHeader />
                            {renderChartFragment()}
                        </ClnPanel>
                    )}

                    {isMobile && <>{renderChartFragment()}</>}
                </Box>
            )}
        </AuthGuardWrapper>
    )
}

export default SupervisorTab
