export const ActionSourceTypeExternalIdEnum = {
    AIMScratch: 'AST-AIM-AIMScratch',
    AIMEvent: 'AST-AIM-AIMEvent',
    MDREquipmentRequest: 'AST-MOC-MDREquipmentRequest',
    MDRPdpmRequest: 'AST-MOC-MDRPdpmRequest',
    MDRMdmRequest: 'AST-MOC-MDRMdmRequest',
    ICAPMOCReport: 'AST-ICAP-ICAPMOCReport',
    ICAPMOOCReport: 'AST-ICAP-ICAPMOOCReport',
    GapAssessmentExecutionData: 'AST-CIA-GapAssessmentExecutionData',
    OFWAEvent: 'AST-OFWA-OFWAEvent',
    EmergingRisk: 'AST-CTW-EmergingRisk',
    PSMCDesignReviewChecklistItem: 'AST-MOCP-PSMC-DR',
    PSMCPSSRChecklistItem: 'AST-MOCP-PSMC-PSSR',
    PSMCMSRPreStartupItem: 'AST-MOCP-PSMC-MSR-PRE',
    RCAEvnInvRCACEvent: 'AST-RCA-EVN-INV-RCACEVENT',
    RCAEvnInvRCAABEvent: 'AST-RCA-EVN-INV-RCAABEVENT',
    RCAQrEvtyCCI: 'AST-RCA-Q&R-EVTY-CCI',
    RCAQrEvtyRAR: 'AST-RCA-Q&R-EVTY-RAR',
    RCAQrEvtyQAR: 'AST-RCA-Q&R-EVTY-QAR',
    RCAWiEvtyADM: 'AST-RCA-WI-EVTY-ADM',
    RCAWiEvtyENG: 'AST-RCA-WI-EVTY-ENG',
    RCAWiEvtyMNTC: 'AST-RCA-WI-EVTY-MNTC',
    RCAWiEvtyQA: 'AST-RCA-WI-EVTY-QA',
    RCAWiSapQ1: 'AST-RCA-SAP-EVTY-Q1',
    RCAWiSapQ3: 'AST-RCA-SAP-EVTY-Q3',
} as const

export type ActionSourceTypeExternalIdEnumType =
    (typeof ActionSourceTypeExternalIdEnum)[keyof typeof ActionSourceTypeExternalIdEnum]

export const validSourceTypeIdsForDate: ActionSourceTypeExternalIdEnumType[] = [
    ActionSourceTypeExternalIdEnum.PSMCMSRPreStartupItem,
    ActionSourceTypeExternalIdEnum.PSMCDesignReviewChecklistItem,
    ActionSourceTypeExternalIdEnum.PSMCPSSRChecklistItem,
]

export function isActionSourceTypeExternalIdEnumType(value: string): value is ActionSourceTypeExternalIdEnumType {
    return Object.values(ActionSourceTypeExternalIdEnum).includes(value as ActionSourceTypeExternalIdEnumType)
}
