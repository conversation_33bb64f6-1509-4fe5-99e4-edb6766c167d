from typing import Optional

from .queries import GET_USER_ROLE_SITES

from clients.core.constants import AGGR<PERSON>ATE_LIMIT
from clients.core.filters import eq_filter, in_filter
from clients.core.models import PaginatedData, ServiceParams
from clients.user_role_site.models import UserRoleSiteResult
from clients.user_role_site.requests import GetUserRoleSitesRequest


class UserRoleSiteClient:
    def __init__(
        self,
        params: ServiceParams,
    ):
        self._cognite_client = params.cognite_client
        self._data_model_id = params.data_model.as_id()

    def get_user_role_sites(
        self,
        request: GetUserRoleSitesRequest,
    ) -> Optional[PaginatedData[UserRoleSiteResult]]:
        variables = {
            "filter": (
                in_filter("externalId", request.role_ids) if request.role_ids else None
            ),
            "reportingSiteFilter": eq_filter("externalId", request.reporting_site_id),
        }

        try:
            result = self._cognite_client.data_modeling.graphql.query(
                id=self._data_model_id,
                query=GET_USER_ROLE_SITES,
                variables=variables,
            )

            return PaginatedData[UserRoleSiteResult].from_graphql_response(
                result, AGGREGATE_LIMIT
            )
        except Exception:
            return None
