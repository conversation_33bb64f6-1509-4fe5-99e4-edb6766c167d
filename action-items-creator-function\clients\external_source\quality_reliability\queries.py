GET_QUALITY_REALIABILITY_EVENT = """
query ListQualityReliabilityEvent(
  $filter: _ListQualityReliabilityEventFilter
) {
  listQualityReliabilityEvent(filter: $filter) {
    items {
      externalId
      space
      reportingUnit {
        externalId
        space
        description
      }
      eventDate
      eventType {
        externalId
        space
        description
      }
      reportTitle
      reportDate
      problemCategory {
        externalId
        space
        description
      }
      initiator {
        externalId
        space
        displayName
      }
      qualityDetail {
        externalId
        space
        immediateActionTaken
        batchNumber
        equipment {
          externalId
          space
          description
        }
        proceduresInvolved
        reportingLocation {
          externalId
          space
          description
        }
      }
      reliabilityDetail {
        externalId
        space
        immediateActionTaken
        equipment {
          externalId
          space
          description
        }
        reportingLocation {
          externalId
          space
          description
        }
      }
      customerComplaintDetail {
        externalId
        space
        problemDescription
        batchNumber
      }
    }
  }
}
"""
