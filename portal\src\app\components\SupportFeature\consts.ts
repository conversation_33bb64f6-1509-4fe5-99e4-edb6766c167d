export const impactOptions = [
    { value: '1', label: 'P1 - Large scale outage with no workaround' },
    {
        value: '2',
        label: 'P2 - Production outage for a certain group or application',
    },
    {
        value: '3',
        label: 'P3 - Production outage with a workaround possible',
    },
    { value: '4', label: 'P4 - Low priority or non-prod issue' },
]
export interface AppInformationProps {
	titlePrefix: string
	useCase: string
    areaPath: string
}