import { useCallback, useEffect, useState } from 'react'
import { AzureFunctionClient } from '../../clients/azure-function-client'
import { ReportingUnit, ReportingUnitRequest } from '../../models/common/asset-hierarchy/reporting-unit'
import { useDebounceFunction } from '../general-functions/useDebounce'

export const useFetchSearchUnit = (request: ReportingUnitRequest) => {
    const client = new AzureFunctionClient()
    const [resultData, setResultData] = useState<{
        data: ReportingUnit[]
        loading: boolean
    }>({
        data: [],
        loading: true,
    })

    const fetchUnits = useCallback(async () => {
        if (request.search === 'NULL_PARAM') {
            setResultData({ data: [], loading: false })
            return
        }
        setResultData((prev) => ({ ...prev, loading: true }))
        try {
            const result: ReportingUnit[] = await client.getReportingUnits(request)
            setResultData({ data: result, loading: false })
        } catch (err) {
            setResultData({ data: [], loading: false })
        }
    }, [request])

    const debouncedFetchUnits = useCallback(useDebounceFunction(fetchUnits, 800), [fetchUnits])

    useEffect(() => {
        debouncedFetchUnits()
    }, [debouncedFetchUnits])

    return {
        loading: resultData.loading,
        units: resultData.data,
    }
}
