import asyncio
import os
import sys
from collections import defaultdict
from datetime import date, timed<PERSON>ta
from typing import Any, Dict, Optional

script_dir = os.path.dirname(os.path.abspath(__file__))
project_dir = os.path.dirname(script_dir)
sys.path.append(project_dir)

from clients.action_item_client import ActionItemClient
from clients.actions.models import ActionResult
from clients.actions.requests import GetActionRequest
from clients.core.constants import (
    AGGREGATE_LIMIT,
    ActionStatusEnum,
    ApprovalWorkflowStepDescriptionEnum,
)
from clients.notification.constants import NotificationTypes, SendToTypes
from clients.notification_config.models import NotificationConfigResult
from clients.notification_config.requests import GetNotificationConfigsRequest
from clients.reporting_site.models import ReportingSiteResult
from clients.user_role_site.requests import GetUserRoleSitesRequest
from models.notification import Notification

BATCH_SIZE = 10
MAX_NUMBER_OF_ACTIONS_PER_EMAIL = 8

DUE_IN_N_DAYS_NOTIFICATION_TYPE_EXTERNAL_ID = "NTYPE-ActionDueInNDays"
OVERDUE_ALERT_NOTIFICATION_TYPE_EXTERNAL_ID = "NTYPE-OverdueAlert"

NO_NOTIFICATION_TO_SEND = "No notification to send."
NO_SITES_OR_NOTIFICATION_CONFIGS = "No sites or notification configurations found."

SEMAPHORE = asyncio.Semaphore(5)


class OverdueNotifierService:
    def __init__(
        self,
        action_item_client: ActionItemClient,
    ):
        self._log = action_item_client.actions._logging
        self._settings = action_item_client.actions._settings
        self._notification_service = action_item_client.actions._notification_service
        self._action_client = action_item_client.actions
        self._notification_config_client = action_item_client.notification_config
        self._reporting_site_client = action_item_client.reporting_site
        self._user_role_site_client = action_item_client.user_role_site

    # region: Common
    def _get_approver_or_verifier(
        self,
        action_item: ActionResult,
        description: str,
    ) -> Optional[str]:
        """
        Retrieves the email of the approver or verifier for a given action item and description.

        This function inspects the approval workflow steps of an action item and looks for
        a step whose description matches the provided description. If found, it returns
        the email of the first user assigned to that step.

        Args:
            action_item (ActionResult): The action item containing approval workflow data.
            description (str): The description of the step to look for in the approval workflow.

        Returns:
            Optional[str]: The email of the approver or verifier if found, otherwise None.

        """
        approval_workflow = action_item.approval_workflow

        if approval_workflow:
            steps = approval_workflow.steps or []
            for step in steps:
                if step.description == description and step.users:
                    return step.users[0].email

        return None

    def _extract_action_item_info(self, action_item: ActionResult) -> Dict[str, Any]:
        """
        Extracts relevant information from an action item.

        This function retrieves various fields from an action item and organizes them into
        a structured dictionary for easier access. This includes information such as owner,
        assigned user, current status, category, and others.

        Args:
            action_item (ActionResult): The action item from which information will be extracted.

        Returns:
            Dict[str, Any]: A dictionary containing extracted details from the action item.

        """
        owner = action_item.owner
        assigned_to = action_item.assigned_to
        current_status = action_item.current_status
        reporting_site = action_item.reporting_site
        category = action_item.category
        sub_category = action_item.sub_category
        site_specific_category = action_item.site_specific_category

        return {
            "externalId": action_item.external_id,
            "title": action_item.title,
            "owner": {
                "name": (owner.user.display_name if owner and owner.user else None),
            },
            "assignedTo": {
                "name": (
                    assigned_to.user.display_name
                    if assigned_to and assigned_to.user
                    else None
                ),
            },
            "currentStatus": {"name": current_status.name if current_status else None},
            "reportingSite": {
                "externalId": (reporting_site.external_id if reporting_site else None),
                "siteCode": (reporting_site.site_code if reporting_site else None),
            },
            "category": {"name": category.name if category else None},
            "subCategory": {"name": sub_category.name if sub_category else None},
            "siteSpecificCategory": {
                "description": (
                    site_specific_category.description
                    if site_specific_category
                    else None
                ),
            },
        }

    def _send_notifications_in_batches(self, notifications: list[Notification]) -> None:
        """
        Sends notifications in batches.

        This function processes the list of notifications in batches of a specified size,
        sending each batch to the notification service. It logs success or failure for each batch.

        Args:
            notifications (list[Notification]): The list of notifications to be sent.

        """
        for i in range(0, len(notifications), BATCH_SIZE):
            batch = notifications[i : i + BATCH_SIZE]
            try:
                self._notification_service.send_notifications(batch)
                self._log.info(
                    f"Successfully sent notification batch {i // BATCH_SIZE + 1}.",
                )
            except Exception as e:
                self._log.error(
                    f"Error sending notification batch {i // BATCH_SIZE + 1}: {e}",
                )

    # endregion

    # region: Overdue
    def notify_users_of_all_overdue_actions(self) -> None:
        """
        Asynchronously notifies users of all overdue action items.

        This method gathers overdue action items and sends notifications to users
        involved (owners, approvers, verifiers, assignees) if any action items are overdue.
        It performs the following steps:
        1. Fetches all overdue action items.
        2. Groups these items by user.
        3. Creates notifications for each user.
        4. Sends notifications in batches.

        Returns:
            None

        """
        all_overdue_action = self._get_all_overdue_actions()

        if not all_overdue_action:
            self._log.info("No overdue action item to send notification")
            return

        all_overdue_actions_grouped_by_user = self._group_all_overdue_actions_by_user(
            all_overdue_action,
        )

        notifications = self._create_notifications_for_all_overdue_actions_by_user(
            all_overdue_actions_grouped_by_user,
        )

        if not notifications:
            self._log.info(NO_NOTIFICATION_TO_SEND)
            return

        self._send_notifications_in_batches(notifications)

    def _get_all_overdue_actions(self) -> list[ActionResult]:
        """
        Asynchronously fetches all overdue action items from the database.

        This method queries the GraphQL service to retrieve action items that are overdue
        based on specific statuses. The overdue actions are filtered by status and due date.

        Returns:
            list[ActionResult]: A list of overdue action items.

        """
        self._log.info("Fetching overdue action items")

        status_external_ids = [
            status.value
            for status in [
                ActionStatusEnum.ASSIGNED,
                ActionStatusEnum.PENDING_APPROVAL,
                ActionStatusEnum.PENDING_VERIFICATION,
                ActionStatusEnum.CHALLENGE_PERIOD,
            ]
        ]

        request = GetActionRequest(
            status_external_ids=status_external_ids,
            due_date_lt=date.today(),
            page_size=AGGREGATE_LIMIT,
        )

        result = self._action_client.get_actions(request)

        if result is None:
            self._log.error("Error getting all overdue action items")
            return []

        return result.data

    def _group_all_overdue_actions_by_user(
        self,
        action_items: list[ActionResult],
    ) -> Dict[str, list[Dict[str, Any]]]:
        """
        Groups overdue action items by the users involved in the actions (owner, assignee, approver, verifier).

        This method processes a list of overdue action items, extracts relevant information,
        and organizes these items by user. It considers different roles (owner, assignee, approver, verifier)
        for each action item and groups them accordingly.

        Parameters
        ----------
            action_items (list[ActionResult]): A list of overdue action items to be grouped.

        Returns
        -------
            Dict[str, list[Dict[str, Any]]]: A dictionary where the keys are user emails,
            and the values are lists of overdue action items for each user.

        """
        all_overdue_actions_grouped_by_user = defaultdict(list)

        for action_item in action_items:
            action_item_info = self._extract_action_item_info(action_item)

            try:
                users_to_notify = self._get_users_to_notify(action_item)
                for user in users_to_notify:
                    all_overdue_actions_grouped_by_user[user].append(action_item_info)
            except Exception as e:
                self._log.error(
                    f"Error gathering users for notification: {e}. "
                    f"Action Item ID: {action_item.external_id}",
                )

        return all_overdue_actions_grouped_by_user

    def _get_users_to_notify(self, action_item: ActionResult) -> list[str]:
        """
        Returns a list of users to notify for a given overdue action item.

        This method determines which users need to be notified for a particular overdue
        action item based on the action's current status (e.g., assigned, pending approval, pending verification).
        It collects the emails of users who are owners, assignees, approvers, or verifiers.

        Parameters
        ----------
            action_item (ActionResult): An overdue action item.

        Returns
        -------
            list[str]: A list of user emails to be notified.

        """
        self._log.info("Gather users for notification")
        users = set()

        owner = action_item.owner
        if owner and owner.user:
            users.add(owner.user.email)

        current_status = action_item.current_status
        status_external_id = current_status.external_id if current_status else ""

        if status_external_id in [
            ActionStatusEnum.ASSIGNED,
            ActionStatusEnum.CHALLENGE_PERIOD,
        ]:
            assigned_to = action_item.assigned_to
            if assigned_to and assigned_to.user:
                users.add(assigned_to.user.email)

        elif status_external_id == ActionStatusEnum.PENDING_APPROVAL:
            approver = self._get_approver_or_verifier(
                action_item,
                ApprovalWorkflowStepDescriptionEnum.APPROVAL,
            )
            if approver:
                users.add(approver)

        elif status_external_id == ActionStatusEnum.PENDING_VERIFICATION:
            verifier = self._get_approver_or_verifier(
                action_item,
                ApprovalWorkflowStepDescriptionEnum.VERIFICATION,
            )
            if verifier:
                users.add(verifier)

        return list(users)

    def _create_notifications_for_all_overdue_actions_by_user(
        self,
        action_items_grouped_by_user: Dict[str, list[Dict[str, Any]]],
    ) -> list[Notification]:
        """
        Creates notification objects for all overdue action items grouped by user.

        This method creates notification objects for each user based on the overdue actions
        they are involved in. It uses the grouped overdue action items and generates a notification
        request with an email body describing the overdue items.

        Parameters
        ----------
            action_items_grouped_by_user (Dict[str, list[Dict[str, Any]]]): A dictionary of users grouped by overdue action items.

        Returns
        -------
            list[Notification]: A list of notification objects to be sent to users.

        """
        notifications = []
        for user, action_items in action_items_grouped_by_user.items():
            try:
                email_body = self._create_email_body_for_overdue_actions_by_user(
                    action_items,
                )

                site_external_id = next(
                    (
                        item.get("reportingSite", {}).get("externalId")
                        for item in action_items
                        if item.get("reportingSite")
                        and item.get("reportingSite", {}).get("externalId")
                    ),
                    "",
                )

                notification = Notification.create_notification_request(
                    {"description": email_body, "site": site_external_id},
                    [user],
                    NotificationTypes.OVERDUE,
                )
                notifications.append(notification)

            except Exception as e:
                self._log.error(f"Error creating notification for user {user}: {e}")

        return notifications

    def _create_email_body_for_overdue_actions_by_user(
        self,
        action_items: list[Dict[str, Any]],
    ):
        """
        Generates the email body for overdue action notifications.

        This method generates the body of an email that lists overdue actions associated with a user.
        The email contains action item details, including the external ID, title, owner name, assignee name,
        and current status. If there are too many actions, only the longest overdue actions are included in the email.

        Parameters
        ----------
            action_items (list[Dict[str, Any]]): A list of overdue action items.

        Returns
        -------
            str: The email body as a string.

        """
        action_items_length = len(action_items)
        email_body = f"\nThere are {action_items_length} actions with you as Owner, Approver, Verifier, or Assignee."

        if action_items_length > MAX_NUMBER_OF_ACTIONS_PER_EMAIL:
            email_body += f" Following are the {MAX_NUMBER_OF_ACTIONS_PER_EMAIL} that are past their due date the longest:"
            action_items = action_items[:MAX_NUMBER_OF_ACTIONS_PER_EMAIL]
        else:
            email_body += " Following is an updated summary of all of them:"

        for item in action_items:
            external_id = item.get("externalId", "")
            site_code = item.get("reportingSite", {}).get("siteCode")
            title = item.get("title", "")
            owner_name = item.get("owner", {}).get("name", "")
            assignee_name = item.get("assignedTo", {}).get("name", "")
            current_status_name = item.get("currentStatus", {}).get("name", "")

            email_body += (
                f"\n\nAction Item ID: {external_id}\n"
                f"Title: {title}\n"
                f"Owner: {owner_name}\n"
                f"Assignee: {assignee_name}\n"
                f"Status: {current_status_name}\n"
                f"Link: {self._settings.aim_portal_uri}/action-item/details/{site_code}/{external_id} "
            )

        if action_items_length > MAX_NUMBER_OF_ACTIONS_PER_EMAIL:
            email_body += f"\n\nPlease visit the Action Item Management portal at {self._settings.aim_portal_uri} to see all other overdue actions."
        else:
            email_body += "\n\nPlease review and take the necessary actions."

        return email_body

    # endregion

    # region: Due in [N] days and Overdue Alert
    async def notify_users_of_upcoming_due_actions(self) -> None:
        """
        Asynchronously notifies users of action items that will be due in the upcoming days.

        This method sends notifications to users about action items that are approaching their due date.
        It calls the helper function `_notify_users_of_due_actions`, passing the notification type for upcoming actions.

        Returns:
            None

        """
        await self._notify_users_of_due_actions(
            DUE_IN_N_DAYS_NOTIFICATION_TYPE_EXTERNAL_ID,
        )

    async def notify_users_of_overdue_actions(self) -> None:
        """
        Asynchronously notifies users of overdue action items.

        This method sends notifications to users about action items that have already passed their due date.
        It calls the helper function `_notify_users_of_due_actions`, passing the notification type for overdue actions.

        Returns:
            None

        """
        await self._notify_users_of_due_actions(
            OVERDUE_ALERT_NOTIFICATION_TYPE_EXTERNAL_ID,
        )

    async def _safe_process_reporting_site(
        self,
        site: ReportingSiteResult,
        notification_configs: list[NotificationConfigResult],
        is_due_in_n_days: bool,
    ):
        """
        Safely processes a reporting site to handle overdue or upcoming actions.

        Parameters
        ----------
            site (ReportingSiteResult): The reporting site data.
            notification_configs (list[NotificationConfigResult]): List of notification configurations.
            is_due_in_n_days (bool): A flag indicating whether the actions are upcoming or overdue.

        Returns
        -------
            list[Any]: The list of notifications for the site.

        """
        async with SEMAPHORE:
            return await asyncio.to_thread(
                self._process_reporting_site,
                site,
                notification_configs,
                is_due_in_n_days,
            )

    async def _notify_users_of_due_actions(self, notification_type: str) -> None:
        """
        Notifies users of due actions, either upcoming or overdue.

        This method gathers the reporting sites and notification configurations, then processes each site
        to determine which actions are due and creates the corresponding notifications.
        The method calls `_safe_process_reporting_site` to process each site asynchronously.

        Parameters
        ----------
            notification_type (str): The type of notification (upcoming or overdue).

        Returns
        -------
            None

        """
        reporting_sites = self._reporting_site_client.get_reporting_sites()
        notification_configs = (
            self._notification_config_client.get_notification_configs(
                GetNotificationConfigsRequest(notification_type=notification_type),
            )
        )

        if not reporting_sites or not notification_configs:
            self._log.info(NO_SITES_OR_NOTIFICATION_CONFIGS)
            return

        notifications = await asyncio.gather(
            *[
                self._safe_process_reporting_site(
                    site,
                    notification_configs.data,
                    notification_type == DUE_IN_N_DAYS_NOTIFICATION_TYPE_EXTERNAL_ID,
                )
                for site in reporting_sites.data
            ],
        )
        notifications = [
            notification for sublist in notifications for notification in sublist
        ]

        if not notifications:
            self._log.info(NO_NOTIFICATION_TO_SEND)
            return

        self._send_notifications_in_batches(notifications)

    def _process_reporting_site(
        self,
        reporting_site: ReportingSiteResult,
        notification_configs: list[NotificationConfigResult],
        is_upcoming: bool,
    ) -> list[Notification]:
        """
        Processes a reporting site to retrieve and create notifications for overdue or upcoming actions.

        This method checks the notification configuration for the site and fetches the overdue or upcoming action items
        based on the number of days specified in the configuration. It then creates notifications for the corresponding actions.

        Parameters
        ----------
            reporting_site (ReportingSiteResult): The reporting site data.
            notification_configs (list[NotificationConfigResult]): List of notification configurations.
            is_upcoming (bool): A flag indicating whether the actions are upcoming (due in N days) or overdue.

        Returns
        -------
            list[Notification]: The list of notifications for overdue or upcoming actions for the reporting site.

        """
        notification_config = next(
            (
                config
                for config in notification_configs
                if config.reporting_site
                and config.reporting_site.external_id == reporting_site.external_id
            ),
            notification_configs[0],
        )

        site_id = reporting_site.external_id
        number_of_days = (
            notification_config.number_of_days or 7
            if is_upcoming
            else -(notification_config.number_of_days or 1)
        )

        due_action_items = self._get_due_actions_by_site_id(site_id, number_of_days)

        if not due_action_items:
            self._log.info(
                (
                    f"No action will be overdue in {number_of_days} days for site {site_id}."
                    if is_upcoming
                    else f"No action became overdue yesterday for site {site_id}."
                ),
            )
            return []

        return self._create_due_actions_notifications(
            due_action_items,
            notification_config,
            is_upcoming,
        )

    def _get_due_actions_by_site_id(
        self,
        site_external_id: str,
        number_of_days: int,
    ) -> list[ActionResult]:
        """
        Retrieves due action items for a specific site within the provided number of days.

        Parameters
        ----------
        - site_external_id (str): The external ID of the site.
        - number_of_days (int): Number of days from the current date. Can be positive or negative.

        Returns
        -------
        - list[ActionResult]: List of action items with "ASSIGNED" status and due date as provided.

        """
        self._log.info("Getting due action items")
        request = GetActionRequest(
            reporting_site_external_id=site_external_id,
            status_external_ids=[ActionStatusEnum.ASSIGNED],
            due_date_eq=date.today() + timedelta(days=number_of_days),
            page_size=AGGREGATE_LIMIT,
        )

        result = self._action_client.get_actions(request)

        if result is None:
            self._log.error("Error getting due action items")
            return []

        return result.data

    def _create_due_actions_notifications(
        self,
        upcoming_due_action_items: list[ActionResult],
        notification_config: NotificationConfigResult,
        is_upcoming: bool,
    ) -> list[Notification]:
        """
        Creates notifications for due actions (either upcoming or overdue).

        Parameters
        ----------
        - upcoming_due_action_items (ActionResult): List of action items to notify.
        - notification_config (NotificationConfigResult): Configuration for notifications.
        - is_upcoming (bool): Indicates whether the actions are upcoming (True) or overdue (False).

        Returns
        -------
        - list[Notification]: List of created notifications.

        """
        notifications: list[Notification] = []
        for action_item in upcoming_due_action_items:
            try:
                users_to_notify = self._gather_users_for_due_actions_notifications(
                    action_item,
                    notification_config,
                )

                action_item_info = self._extract_action_item_info(action_item)

                email_body = (
                    self._create_upcoming_due_actions_email_body(
                        action_item_info,
                        notification_config,
                    )
                    if is_upcoming
                    else self._create_overdue_alert_email_body(action_item_info)
                )

                site_id = (
                    action_item.reporting_site.external_id
                    if action_item.reporting_site
                    else ""
                )

                notification = Notification.create_notification_request(
                    {"description": email_body, "site": site_id},
                    users_to_notify,
                    (
                        NotificationTypes.DUE_IN_N_DAYS
                        if is_upcoming
                        else NotificationTypes.OVERDUE_ALERT
                    ),
                )
                notifications.append(notification)
            except Exception as e:
                self._log.error(
                    f"Error creating notification for action item {action_item.external_id}: {e}",
                )

        return notifications

    def _gather_users_for_due_actions_notifications(
        self,
        action_item: ActionResult,
        notification_config: NotificationConfigResult,
    ) -> list[str]:
        """
        Gathers the email addresses of users who need to be notified about due actions.

        Parameters
        ----------
        - action_item (ActionResult): The action item in question.
        - notification_config (NotificationConfigResult): Notification configuration that defines which types of users should be notified.

        Returns
        -------
        - list[str]: List of email addresses of users who need to be notified.

        """
        self._log.info("Gathering users for notification")
        users = set()

        current_status = action_item.current_status
        if (
            not current_status
            or action_item.current_status.external_id != ActionStatusEnum.ASSIGNED
        ):
            return list(users)

        send_to_type_ids = {
            item.external_id for item in notification_config.assigned_send_to_types
        }

        if SendToTypes.ASSIGNEE in send_to_type_ids:
            assigned_to = action_item.assigned_to
            if assigned_to and assigned_to.user:
                users.add(assigned_to.user.email)

        if SendToTypes.OWNER in send_to_type_ids:
            owner = action_item.owner
            if owner and owner.user:
                users.add(owner.user.email)

        if SendToTypes.APPROVER in send_to_type_ids:
            approver = self._get_approver_or_verifier(action_item, "AIM-approval")
            if approver:
                users.add(approver)

        if SendToTypes.VERIFIER in send_to_type_ids:
            verifier = self._get_approver_or_verifier(action_item, "AIM-verification")
            if verifier:
                users.add(verifier)

        if SendToTypes.ROLE in send_to_type_ids:
            roles = notification_config.assigned_roles or []
            roles_ids = [role.external_id for role in roles]
            site_id = (
                action_item.reporting_site.external_id
                if action_item.reporting_site
                else None
            )
            if roles_ids and site_id:
                users_by_role = self._get_users_by_role_and_site(roles_ids, site_id)
                users.update(users_by_role)

        return list(users)

    def _get_users_by_role_and_site(
        self,
        role_ids: list[str],
        site_id: str,
    ) -> list[str]:
        """
        Retrieves users associated with specific roles at a particular site.

        Parameters
        ----------
        - role_ids (list[str]): List of role IDs.
        - site_id (str): The site ID to which the users are associated.

        Returns
        -------
        - list[str]: List of email addresses of users found.

        """
        self._log.info("Getting users by roles and site...")

        if not role_ids or not site_id:
            self._log.info("Role IDs or site ID are missing.")
            return []

        request = GetUserRoleSitesRequest(reporting_site_id=site_id, role_ids=role_ids)

        result = self._user_role_site_client.get_user_role_sites(request)

        if result is None:
            self._log.error("Error getting users by roles and site")
            return []

        users = set()
        for role_site in result.data:
            user_complements = role_site.user_complements
            for item in user_complements:
                if item.user_azure_attribute and item.user_azure_attribute.user:
                    users.add(item.user_azure_attribute.user.email)

        return list(users)

    def _create_upcoming_due_actions_email_body(
        self,
        action_item: Dict[str, Any],
        notification_config: NotificationConfigResult,
    ) -> str:
        """
        Creates the body for an email notification regarding an upcoming due action item.

        Parameters
        ----------
        - action_item (Dict[str, Any]): Information about the action item.
        - notification_config (Dict[str, Any]NotificationConfigResult): Configuration for notification settings, including the number of days.

        Returns
        -------
        - str: The formatted email body.

        """
        number_of_days = notification_config.number_of_days
        external_id = action_item.get("externalId", "N/A")
        site_code = action_item.get("reportingSite", {}).get("siteCode", "N/A")
        title = action_item.get("title", "N/A")
        owner_name = action_item.get("owner", {}).get("name", "N/A")
        assignee_name = action_item.get("assignedTo", {}).get("name", "N/A")
        current_status_name = action_item.get("currentStatus", {}).get("name", "N/A")

        email_body = (
            f"\nThe following action will be due in {number_of_days} days."
            f"\n\nAction Item ID: {external_id}"
            f"\nTitle: {title}"
            f"\nOwner: {owner_name}"
            f"\nAssignee: {assignee_name}"
            f"\nStatus: {current_status_name}"
            f"\nLink: {self._settings.aim_portal_uri}/action-item/details/{site_code}/{external_id}"
        )

        return email_body

    def _create_overdue_alert_email_body(self, action_item: Dict[str, Any]):
        """
        Creates the body for an email notification regarding an overdue action item.

        Parameters
        ----------
        - action_item (Dict[str, Any]): Information about the action item.

        Returns
        -------
        - str: The formatted email body.

        """
        external_id = action_item.get("externalId")
        title = action_item.get("title", "N/A")
        owner_name = action_item.get("owner", {}).get("name", "N/A")
        assignee_name = action_item.get("assignedTo", {}).get("name", "N/A")
        current_status_name = action_item.get("currentStatus", {}).get("name", "N/A")
        category_name = action_item.get("category", {}).get("name", "N/A")
        sub_category_name = action_item.get("subCategory", {}).get("name", "N/A")
        site_specific_category_description = action_item.get(
            "siteSpecificCategory",
            {},
        ).get("description", "-")

        email_body = (
            f"\nThis email is to inform you that the action item {external_id} is overdue."
            f"\n\nAction Item ID: {external_id}"
            f"\nTitle: {title}"
            f"\nOwner: {owner_name}"
            f"\nAssignee: {assignee_name}"
            f"\nStatus: {current_status_name}"
            f"\nCategory: {category_name}"
            f"\nSubcategory 1: {sub_category_name}"
            f"\nSubcategory 2: {site_specific_category_description}"
            f"\nLink: {self._settings.aim_portal_uri}/action-item/details/{external_id}"
        )

        return email_body

    # endregion
