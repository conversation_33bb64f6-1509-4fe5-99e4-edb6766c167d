from datetime import date

from pydantic import Field

from clients.core.models import Node


class QualityNotificationResponse(Node):
    """Represents a quality notification response."""

    notification_number: str | None = Field(default=None)
    notification_description: str | None = Field(default=None)
    notification_date: date | None = Field(default=None)
    batch_number: str | None = Field(default=None)
    defect_desc: str | None = Field(default=None)
    severity: str | None = Field(default=None)
    material: str | None = Field(default=None)
    returned_quantity: float | None = Field(default=None)
    subject_code: str | None = Field(default=None)
    system_status: str | None = Field(default=None)
