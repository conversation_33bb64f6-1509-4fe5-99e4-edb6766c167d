import dayjs from 'dayjs'

export const extractIcapActionIdFromObjectType = (inputString: string) => {
    const parts = inputString.split('-')

    if (parts.length >= 3 && parts[0] === 'ICAPContinuousMigration') {
        return parts[1]
    } else {
        return ''
    }
}

export const compareObjects = <T extends object, U extends object>(
    oldObj: T,
    newObj: U
): Partial<Record<string, { before: any; after: any }>> => {
    const differences: Partial<Record<string, { before: any; after: any }>> = {}

    const commonKeys = Object.keys(oldObj).filter((key) => key in newObj)

    commonKeys.forEach((key) => {
        const oldValue = (oldObj as any)[key]
        const newValue = (newObj as any)[key]

        if (Array.isArray(oldValue) && Array.isArray(newValue)) {
            if (JSON.stringify(oldValue) !== JSON.stringify(newValue)) {
                differences[key] = { before: oldValue, after: newValue }
            }
        } else if (dayjs.isDayjs(oldValue) && dayjs.isDayjs(newValue)) {
            if (!oldValue.isSame(newValue)) {
                differences[key] = { before: oldValue, after: newValue }
            }
        } else if (oldValue !== newValue) {
            differences[key] = { before: oldValue, after: newValue }
        }
    })

    return differences
}
