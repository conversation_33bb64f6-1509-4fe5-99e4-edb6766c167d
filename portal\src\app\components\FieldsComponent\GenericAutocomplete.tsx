import React from 'react'
import { Autocomplete, TextField, Chip, useTheme, useMediaQuery, TextFieldProps, InputLabelProps } from '@mui/material'
import { Controller } from 'react-hook-form'
import { AutocompleteRenderGetTagProps, AutocompleteRenderOptionState } from '@mui/material/Autocomplete'
import LoaderCircular from '../Loader'
import { translate } from '@/app/common/utils/generate-translate'

export type AutocompleteOption = {
    value: string
    label: string
}

type AutocompleteProps = {
    loading?: boolean
    name: string
    control: any
    options: AutocompleteOption[]
    getOptionLabel?: (option: AutocompleteOption) => string
    valueController?: AutocompleteOption | AutocompleteOption[]
    defaultValue?: AutocompleteOption | AutocompleteOption[]
    onChange: (newValue: any, fieldOption?: any) => void
    label?: string
    limitTags?: number
    disableCloseOnSelect?: boolean
    placeholderTextField?: string
    customTagRender?: (value: AutocompleteOption[], getTagProps: AutocompleteRenderGetTagProps) => JSX.Element
    renderOption?: (
        props: React.HTMLProps<HTMLLIElement>,
        option: AutocompleteOption,
        state: AutocompleteRenderOptionState
    ) => JSX.Element
    renderInputProps?: TextFieldProps
    onInputChange?: (event: React.SyntheticEvent, value: string) => void
    multiple?: boolean
    disabled?: boolean
    error?: boolean
    helperText?: string
    size?: 'small' | 'medium'
    noOptionsText?: string
    inputLabelProps?: InputLabelProps
    onBlur?: () => void
    [key: string]: any
}

const GenericAutocomplete = ({
    loading,
    name,
    control,
    options,
    getOptionLabel,
    valueController,
    defaultValue,
    onChange,
    label,
    limitTags = 1,
    placeholderTextField,
    renderOption,
    disableCloseOnSelect = true,
    customTagRender,
    onInputChange,
    multiple = true,
    disabled,
    error,
    helperText,
    size,
    noOptionsText,
    inputLabelProps,
    onBlur,
    ...rest
}: AutocompleteProps) => {
    const theme = useTheme()
    const isMobile = useMediaQuery(theme.breakpoints.down('md'))

    return (
        <Controller
            name={name}
            control={control}
            render={({ field: { value, onChange: onChangeField } }) => (
                <Autocomplete
                    multiple={multiple}
                    sx={{ width: '100%' }}
                    disabled={disabled}
                    limitTags={limitTags}
                    options={options}
                    getOptionLabel={(option) => option.label}
                    defaultValue={defaultValue}
                    value={
                        multiple
                            ? valueController ??
                              (options.filter((option) => (value ?? []).includes(option.value)) || [])
                            : valueController ?? (options.find((option) => option.value === value) || null)
                    }
                    onChange={(event, newValue) => {
                        const selectedValues = multiple
                            ? (newValue as AutocompleteOption[]).map((option) => option.value)
                            : (newValue as AutocompleteOption)?.value || ''

                        onChange(selectedValues, newValue)
                        onChangeField(selectedValues)
                    }}
                    disableCloseOnSelect={disableCloseOnSelect}
                    renderTags={(value, getTagProps) => {
                        if (!multiple) return null

                        if (customTagRender) {
                            return customTagRender(value, getTagProps)
                        }
                        if (value.length > limitTags) {
                            return (
                                <>
                                    {value.slice(0, limitTags).map((option, index) => (
                                        <Chip
                                            label={option.label}
                                            {...getTagProps({ index })}
                                            key={option.value}
                                            sx={{
                                                maxWidth: isMobile
                                                    ? 'calc(100% - 110px) !important'
                                                    : 'calc(100% - 85px) !important',
                                            }}
                                        />
                                    ))}
                                    <Chip
                                        label={`+${value.length - limitTags}`}
                                        key="more-chips"
                                        style={{ backgroundColor: 'transparent' }}
                                        sx={{ maxWidth: '100% !important' }}
                                    />
                                </>
                            )
                        }
                        return value.map((option, index) => (
                            <Chip
                                label={option.label}
                                {...getTagProps({ index })}
                                key={option.value}
                                sx={{
                                    maxWidth: isMobile
                                        ? 'calc(100% - 110px) !important'
                                        : 'calc(100% - 45px) !important',
                                }}
                            />
                        ))
                    }}
                    renderInput={(params) => (
                        <TextField
                            {...params}
                            label={label}
                            placeholder={
                                (multiple ? value?.length === 0 : !value)
                                    ? placeholderTextField ?? translate('table.filter.all')
                                    : placeholderTextField ?? ''
                            }
                            size={size ?? 'medium'}
                            sx={{ textAlign: 'center', display: 'flex', padding: '0px' }}
                            error={error ?? false}
                            helperText={helperText ?? ''}
                            InputLabelProps={inputLabelProps}
                            onBlur={() => onBlur?.()}
                        />
                    )}
                    renderOption={renderOption}
                    onInputChange={onInputChange}
                    loading={loading}
                    loadingText={LoaderCircular()}
                    noOptionsText={noOptionsText ?? translate('adminSettings.table.filter.noOptions')}
                    {...rest}
                />
            )}
        />
    )
}

export default GenericAutocomplete
