parameters:
  - name: azureSubscription
    type: string
  - name: functionAppName
    type: string
  - name: vmImageName
    type: string
  - name: workingDirectory
    type: string

stages:
  - stage: Build
    displayName: Build stage

    jobs:
      - job: Build
        displayName: Build
        pool:
          vmImage: ${{ parameters.vmImageName }}

        steps:
          - bash: |
              if [ -f extensions.csproj ]
              then
                  dotnet build extensions.csproj --runtime ubuntu.16.04-x64 --output ./bin
              fi
            workingDirectory: ${{ parameters.workingDirectory }}
            displayName: "Build extensions"

          - task: UsePythonVersion@0
            displayName: "Use Python 3.11"
            inputs:
              versionSpec: 3.11

          - bash: |
              pip install --target="./.python_packages/lib/site-packages" -r ./requirements.txt
            workingDirectory: ${{ parameters.workingDirectory }}
            displayName: "Install application dependencies"

          - bash: |
              sed -i "s/^AUTH_CLIENT_ID=.*/AUTH_CLIENT_ID=$(AUTH_CLIENT_ID)/" .env
              sed -i "s/^AUTH_SECRET=.*/AUTH_SECRET=$(AUTH_SECRET)/" .env
              sed -i "s/^NOTIFICATIONS_CLIENT_ID=.*/NOTIFICATIONS_CLIENT_ID=$(NOTIFICATIONS_CLIENT_ID)/" .env
              sed -i "s/^NOTIFICATIONS_SECRET=.*/NOTIFICATIONS_SECRET=$(NOTIFICATIONS_SECRET)/" .env

              cat .env
            workingDirectory: ${{ parameters.workingDirectory }}
            displayName: "Fill environment variables"

          - task: ArchiveFiles@2
            displayName: "Archive files"
            inputs:
              rootFolderOrFile: "${{ parameters.workingDirectory }}"
              includeRootFolder: false
              archiveType: zip
              archiveFile: $(Build.ArtifactStagingDirectory)/$(Build.BuildId).zip
              replaceExistingArchive: true

          - task: PublishPipelineArtifact@1
            displayName: "Publish pipeline artifact"
            inputs:
              targetPath: "$(Pipeline.Workspace)"
              artifact: "AIMFunctionApp"
              publishLocation: "pipeline"

  - stage: Deploy
    displayName: Deploy stage
    dependsOn: Build
    condition: succeeded()

    jobs:
      - job: Deploy
        displayName: Deploy
        pool:
          name: "GST-Backend-Linux"

        steps:
          - task: DownloadPipelineArtifact@2
            inputs:
              buildType: "current"
              targetPath: "$(Pipeline.Workspace)"
              artifactName: "AIMFunctionApp"
          - task: AzureFunctionApp@2
            displayName: "Azure functions app deploy"
            inputs:
              azureSubscription: "${{ parameters.azureSubscription }}"
              appType: functionAppLinux
              appName: ${{ parameters.functionAppName }}
              package: "$(Pipeline.Workspace)/a/$(Build.BuildId).zip"
