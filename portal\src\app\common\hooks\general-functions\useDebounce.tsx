import { useState, useEffect, useRef, useCallback } from 'react'

function useDebounce(value: any, delay: any) {
    const [debouncedValue, setDebouncedValue] = useState(value)

    useEffect(() => {
        const handler = setTimeout(() => {
            setDebouncedValue(value)
        }, delay)

        return () => {
            clearTimeout(handler)
        }
    }, [value, delay])

    return debouncedValue
}

export const useDebounceFunction = <T extends (...args: any[]) => any>(func: T, delay: number) => {
    const timeoutRef = useRef<NodeJS.Timeout | null>(null)

    return useCallback(
        (...args: Parameters<T>) => {
            if (timeoutRef.current) {
                clearTimeout(timeoutRef.current)
            }
            timeoutRef.current = setTimeout(() => {
                func(...args)
            }, delay)
        },
        [func, delay]
    )
}

export default useDebounce
