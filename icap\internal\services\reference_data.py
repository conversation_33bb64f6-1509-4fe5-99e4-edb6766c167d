import json

import pandas as pd
from cognite.client import CogniteClient

from ..configs import Config, ReferenceDataConfig, WorkbookConfig
from ..constants import REFERENCE_DATA_TRANSFORMATIONS, REFERENCE_DATA_FOLDER


class ReferenceDataService:
    def __init__(self, cognite_client: CogniteClient):
        self._cognite_client = cognite_client

    def handle_reference_data(self, config: Config):
        for item in config.reference_data:
            existing_tables = self._get_data_base_tables(item)
            for workbook in item.sheet.workbooks:
                print(f"Processing {item.sheet.file} | {workbook.name}")
                self._process_workbook(item, existing_tables, workbook)

            for table_to_create in item.ensure_exists:
                if table_to_create in existing_tables:
                    print(f"{item.database}.{table_to_create} already exists")
                    continue

                self._cognite_client.raw.tables.create(item.database, table_to_create)
                print(f"{item.database}.{table_to_create} was created")
        for transformation, wait in REFERENCE_DATA_TRANSFORMATIONS:
            print(f"Running transformation {transformation}")
            self._cognite_client.transformations.run(
                transformation_external_id=transformation, wait=wait
            )

    def _process_workbook(
        self,
        item: ReferenceDataConfig,
        existing_tables: list[str],
        workbook: WorkbookConfig,
    ):
        df = pd.read_excel(
            f"{REFERENCE_DATA_FOLDER}/{item.sheet.file}",
            sheet_name=workbook.name,
        )
        df = self._handle_columns(df, workbook)

        table_name = workbook.destination_table or workbook.name
        if workbook.mode == "override" and table_name in existing_tables:
            self._delete_table(item, workbook)

        self._cognite_client.raw.rows.insert_dataframe(
            item.database, table_name, df, True
        )

    def _handle_columns(self, df: pd.DataFrame, workbook: WorkbookConfig):
        df["key"] = df[workbook.key]
        df = df.set_index("key")

        for column in workbook.struct_columns:
            df[column] = df[column].apply(self._parse_column_as_json)

        return df

    def _delete_table(self, item: ReferenceDataConfig, workbook: WorkbookConfig):
        self._cognite_client.raw.tables.delete(
            item.database,
            workbook.destination_table or workbook.name,
        )

    def _get_data_base_tables(self, item: ReferenceDataConfig):
        existing_tables = self._cognite_client.raw.tables.list(item.database, -1)
        return existing_tables.as_names()

    @staticmethod
    def _parse_column_as_json(data):
        try:
            return json.loads(data)
        except:  # noqa: E722
            return data
