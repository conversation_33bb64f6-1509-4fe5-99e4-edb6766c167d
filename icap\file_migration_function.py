import asyncio

from cognite.client import CogniteClient
from internal.infra import get_cognite_client, get_config
from internal.services import FileMigrationService


def handle(client: CogniteClient | None = None, data: dict = {}):
    config = get_config()
    client = client or get_cognite_client(config)
    print(client.config)

    service = FileMigrationService(client)
    asyncio.run(service.migrate(data.get("batch_size")))
