import { gql } from '@apollo/client'
import { useEffect, useState } from 'react'
import { useGraphqlQuery } from '../'
import { SourceEventType } from '../../models/source-event'

const buildSourceEventTypeQuery = (): string => {
    const filters: string[] = []

    filters.push(`{ not: { externalId: { eq: "SEVTY-External" } } }`)

    const queryFilter = `{ and: [ ${filters.join(', ')} ] }`

    return `
        query GetSourceEventType {
            listSourceEventType(
                filter: ${queryFilter}
                , first: 1000
                , sort: { sourceEventTypeName: ASC }
            ) {
                items {
                    externalId
                    sourceEventTypeName
                }
            }
        }
    `
}

export const useSourceEventTypes = () => {
    const query = buildSourceEventTypeQuery()
    const { data: fdmData } = useGraphqlQuery<SourceEventType>(gql(query), 'listSourceEventType', {})

    const [resultData, setResultData] = useState<{ data: SourceEventType[]; loading: boolean }>({
        data: [],
        loading: true,
    })

    useEffect(() => {
        if (fdmData.length == 0) {
            setResultData({ data: [], loading: false })
        } else {
            setResultData({ data: fdmData, loading: false })
        }
    }, [fdmData])

    return {
        loading: resultData.loading,
        sourceEventType: resultData.data,
    }
}
