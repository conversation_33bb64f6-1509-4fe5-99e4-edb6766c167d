from enum import StrEnum

STATUS_ID_TO_STATUS_NAME: dict[str, str] = {
    "ACTS-completed": "Completed",
    "ACTS-pendingApproval": "Pending Approval",
    "ACTS-pendingVerification": "Pending Verification",
    "ACTS-approvalRejected": "Approval Rejected",
    "ACTS-verificationRejected": "Verification Rejected",
    "ACTS-dueDateExtensionPeriod": "Due Date Extension",
    "ACTS-reassignmentPeriod": "Reassignment",
    "ACTS-challengePeriod": "Challenge",
}


class NotificationTypes(StrEnum):
    DUE_DATE_EXTENSION = "Due Date Extension"
    REASSIGNMENT = "Reassignment"
    CHALLENGE = "Challenge"
    PENDING_APPROVAL = "Pending Approval"
    PENDING_VERIFICATION = "Pending Verification"
    APPROVAL_REJECTED = "Approval Rejected"
    VERIFICATION_REJECTED = "Verification Rejected"
    CANCELLED = "Cancelled"
    COMPLETED = "Completed"


class BulkUploadErrorMessageTypes(StrEnum):
    MISSING_FIELD = "bulkUploadActionItems.errors.missingField"
    INVALID_TITLE = "bulkUploadActionItems.errors.invalidTitle"
    INVALID_DESCRIPTION = "bulkUploadActionItems.errors.invalidDescription"
    INVALID_SOURCE_INFORMATION = "bulkUploadActionItems.errors.invalidSourceInformation"
    INVALID_EMAIL = "bulkUploadActionItems.errors.invalidEmail"
    NOT_FOUND_EMAIL = "bulkUploadActionItems.errors.notFoundEmail"
    INVALID_UNIT = "bulkUploadActionItems.errors.invalidUnit"
    INVALID_LOCATION = "bulkUploadActionItems.errors.invalidLocation"
    INVALID_UNIT_AND_LOCATION = "bulkUploadActionItems.errors.invalidUnitAndLocation"
    INVALID_CATEGORY = "bulkUploadActionItems.errors.invalidCategory"
    INVALID_SUB_CATEGORY = "bulkUploadActionItems.errors.invalidSubCategory"
    INVALID_SITE_SPECIFIC_CATEGORY = (
        "bulkUploadActionItems.errors.invalidSiteSpecificCategory"
    )
    INVALID_CATEGORY_CONFIGURATION = (
        "bulkUploadActionItems.errors.invalidCategoryConfiguration"
    )
    INVALID_PRIORITY = "bulkUploadActionItems.errors.invalidPriority"
    INVALID_DATE_TYPE = "bulkUploadActionItems.errors.invalidDateType"
    INVALID_DUE_DATE = "bulkUploadActionItems.errors.invalidDueDate"
    INVALID_DATE = "bulkUploadActionItems.errors.invalidDate"


class RecurrenceTypeExternalId(StrEnum):
    CUSTOM = "RCT-custom"
    DAILY = "RCT-daily"
    WEEKLY = "RCT-weekly"
    MONTHLY = "RCT-monthly"
    QUARTERLY = "RCT-quarterly"
    YEARLY = "RCT-yearly"
    BIENALLY = "RCT-biennially"
    TRIENNIALLY = "RCT-triennially"
    QUINQUENNIALLY = "RCT-quinquennially"


ACTION_EXPORT_FIELD_MAP = {
    "externalId": "external_id",
    "title": "title",
    "currentStatus": "current_status",
    "assignedTo": "assignee",
    "application": "application",
    "assignmentDate": "assignment_date",
    "dueDate": "due_date",
    "conclusionDate": "conclusion_date",
    "reportingSite": "reporting_site",
    "reportingUnit": "reporting_unit",
    "reportingLocation": "reporting_location",
    "category": "category",
    "subCategory": "sub_category",
    "siteSpecificCategory": "site_specific_category",
    "owner": "owner",
    "approver": "approver",
    "approvalDate": "approval_date",
    "verifier": "verifier",
    "verificationDate": "verification_date",
    "sourceInformation": "source_information",
    "sourceEventTitle": "source_event_title",
    "isPrivate": "is_private",
    "objectType": "icap_action_id",
    "priority": "priority",
    "ext": "ext",
    "manager": "manager",
    "overdue": "overdue",
}
