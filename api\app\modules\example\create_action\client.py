from datetime import UTC, datetime

from app.core.client import BaseClient
from app.core.methods.spaces import get_transactional_space

from .models import CreateActionRequest, CreateActionResponse


class CreateActionClient(BaseClient):
    """Represents a simplified version of a CreateActionClient."""

    def create(self, request: CreateActionRequest) -> CreateActionResponse:
        """Simulate an action item creation."""
        return CreateActionResponse(
            external_id="ACT-" + datetime.now(UTC).strftime("%Y-%m-%d %H:%M:%S") + "-1",
            space=get_transactional_space(request.reporting_site_external_id),
        )
