# Manifest file downloaded from fusion
externalId: tr-AIM-COR-ALL-RAW-ICAP-VAL-EVENT
name: AIM-COR-ALL-RAW-ICAP-VAL-EVENT
query: >-
  WITH validation_1 AS (
      SELECT
          key,
          reportingSite.externalId AS reporting_site,
          CASE
              WHEN category IS NULL THEN 1
              ELSE 0
          END AS category_null,
          CASE
              WHEN subCategory IS NULL THEN 1
              ELSE 0
          END AS subCategory_null,
          CASE
              WHEN owner IS NULL THEN 1
              ELSE 0
          END AS owner_null,
          CASE
              WHEN createdBy IS NULL THEN 1
              ELSE 0
          END AS createdBy_null,
          CASE
              WHEN status IS NULL THEN 1
              ELSE 0
          END AS status_null,
          CASE
              WHEN reportingUnit IS NULL THEN 1
              ELSE 0
          END AS reportingUnit_null,
          CASE
              WHEN reportingSite IS NOT NULL
              AND reportingUnit IS NOT NULL
              AND not startswith(
                  reportingUnit.externalId,
                  replace(reportingSite.externalId, "STS-", "UNT-")
              ) THEN 1
              ELSE 0
          END AS reportingUnit_from_another_site,
          CASE
              WHEN isPrivate THEN 1
              ELSE 0
          END AS isPrivate
      FROM
          `AIM-COR`.`ICAP-STG-Event`
  ),

  validation_2 AS (
      SELECT
          validation_1.*,
          CASE
              WHEN (
                  category_null = 1
                  OR subCategory_null = 1
                  OR owner_null = 1
                  OR createdBy_null = 1
                  OR createdBy_null = 1
                  OR status_null = 1
                  OR reportingUnit_null = 1
                  OR reportingUnit_from_another_site = 1
              ) THEN 1
              ELSE 0
          END AS has_error
      FROM
         validation_1 
  ),

  validation_3 AS (
      SELECT 
          validation_2.*,
          CASE
              WHEN (
                  has_error = 1
                  AND isPrivate = 1
              ) THEN 1
              ELSE 0
          END AS has_error_and_is_private,
          CASE
              WHEN (
                  has_error = 1
                  AND isPrivate = 0
              ) THEN 1
              ELSE 0
          END AS has_error_and_is_not_private
      FROM
          validation_2
  )
      
  SELECT
      "ALL" AS key,
      COUNT(key) AS `1_total_events`,
      SUM(isPrivate) AS `2_total_isPrivate`,
      SUM(has_error) AS `3_total_events_with_errors`,
      100 * (SUM(has_error) / COUNT(key)) AS `4_percentage_events_with_errors`,
      SUM(has_error_and_is_not_private) AS `5_total_non_private_events_with_errors`,
      100 * (SUM(has_error_and_is_not_private) / COUNT(key)) AS `6_percentage_non_private_events_with_errors`,
      SUM(has_error_and_is_private) AS `7_total_private_events_with_errors`,
      100 * (SUM(has_error_and_is_private) / COUNT(key)) AS `8_percentage_private_events_with_errors`,
      "ALL" AS `9_reporting_site`,
      SUM(owner_null) AS `10_total_owner_null`,
      SUM(createdBy_null) AS `11_total_createdBy_null`,
      SUM(status_null) AS `12_total_status_null`,
      SUM(category_null) AS `13_total_category_null`,
      SUM(subCategory_null) AS `14_total_subCategory_null`,
      SUM(reportingUnit_null) AS `15_total_reportingUnit_null`,
      SUM(reportingUnit_from_another_site) AS `16_total_reportingUnit_from_another_site`
  FROM
      validation_3

  UNION


  SELECT
      coalesce(reporting_site, "-") AS key,
      COUNT(key) AS `1_total_events`,
      SUM(isPrivate) AS `2_total_isPrivate`,
      SUM(has_error) AS `3_total_events_with_errors`,
      100 * (SUM(has_error) / COUNT(key)) AS `4_percentage_events_with_errors`,
      SUM(has_error_and_is_not_private) AS `5_total_non_private_events_with_errors`,
      100 * (SUM(has_error_and_is_not_private) / COUNT(key)) AS `6_percentage_non_private_events_with_errors`,
      SUM(has_error_and_is_private) AS `7_total_private_events_with_errors`,
      100 * (SUM(has_error_and_is_private) / COUNT(key)) AS `8_percentage_private_events_with_errors`,
      reporting_site AS `9_reporting_site`,
      SUM(owner_null) AS `10_total_owner_null`,
      SUM(createdBy_null) AS `11_total_createdBy_null`,
      SUM(status_null) AS `12_total_status_null`,
      SUM(category_null) AS `13_total_category_null`,
      SUM(subCategory_null) AS `14_total_subCategory_null`,
      SUM(reportingUnit_null) AS `15_total_reportingUnit_null`,
      SUM(reportingUnit_from_another_site) AS `16_total_reportingUnit_from_another_site`
  FROM
      validation_3
  GROUP BY
      reporting_site
destination:
  database: AIM-COR
  table: ICAP-VAL-Event
  type: raw
ignoreNullFields: true
shared: true
action: upsert
dataSetExternalId: AIM-COR-ALL-DAT
# Specify credentials separately like this:
# authentication:
#   read:
#     clientId: ${READ_CLIENT_ID}
#     clientSecret: ${READ_CLIENT_SECRET}
#     tokenUrl: ${READ_TOKEN_URL}
#     cdfProjectName: ${READ_CDF_PROJECT_NAME}
#     # Optional: If idP requires providing the scopes
#     scopes:
#       - ${READ_SCOPES}
#     # Optional: If idP requires providing the audience
#     audience: ${READ_CDF_AUDIENCE}
#   write:
#     clientId: ${WRITE_CLIENT_ID}
#     clientSecret: ${WRITE_CLIENT_SECRET}
#     tokenUrl: ${WRITE_TOKEN_URL}
#     cdfProjectName: ${WRITE_CDF_PROJECT_NAME}
#     # Optional: If idP requires providing the scopes
#     scopes:
#       - ${WRITE_SCOPES}
#     # Optional: If idP requires providing the audience
#     audience: ${WRITE_CDF_AUDIENCE}
# Or together like this:
# authentication:
#   clientId: ${CLIENT_ID}
#   clientSecret: ${CLIENT_SECRET}
#   tokenUrl: ${TOKEN_URL}
#   # Optional: If idP requires providing the scopes
#   cdfProjectName: ${CDF_PROJECT_NAME}
#   scopes:
#    - ${SCOPES}
#   # Optional: If idP requires providing the audience
#   audience: ${AUDIENCE}