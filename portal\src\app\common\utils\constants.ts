export const MAX_COGNITE_GRAPHQL_BATCH_SIZE = 1000

export const MAX_FILE_SIZE = 10 * 1024 * 1024 // 10 MB

//Text Fields Min and Max Length
export const MIN_DEFAULT_TEXT_FIELD = 10
export const MIN_TEXT_FIELD = 1
export const MAX_DEFAULT_TEXT_FIELD = 300
export const MAX_VOE_TEXT_FIELD = 500
export const MAX_DESCRIPTION_TEXT_FIELD = 1000
export const MAX_TITLE_TEXT_FIELD = 200
export const MAX_TITLE_TEXT_TEMPLATE_FIELD = 100
export const MAX_LINK_LENGTH = 30
export const MAX_LINK_LENGTH_CREATION_ACTION = 100

export const ROWS_PER_PAGE_OPTIONS = [5, 10, 25, 75, 100]
export const ROWS_PER_PAGE_OPTIONS_ADMIN_SCREEN = [15, 20, 25]

export const DEFAULT_SORT_FIELD = 'createdAt'
export const DEFAULT_SORT_DIRECTION = 'desc'
export const ASC_DESC_MAPPER = {
    asc: 'ascending',
    desc: 'descending',
}

export const APP_NAME = 'Action Item Management'
export const AZURE_APP_NAME_DP = 'Digital Plant'
export const AZURE_APP_ORGANIZATION = 'CelaneseCorporation'
export const AZURE_ACTION_PATH = 'Digital Plant\\25. Action Item Management'
export const AZURE_APP_CODE = 'AIM'

export const PRIVATE_SPACE = 'AIM-COR-ALL-PROT'
export const PREFIX_USER_AZURE_ATTRIBUTE = 'UserAzureAttribute_'

//Support Screen
export const applicationArea = 'Digital Plant'
export const continuosEnIIAreaPath = 'Digital Plant\\20. Continuous Enh. II'
export const continuosEnIAreaPath = 'Digital Plant\\19. Continuous Enh. I'
export const continuosEnIVAreaPath = 'Digital Plant\\22. Continuous Enh. IV'
export const continuousEnIIIAreaPath = 'Digital Plant\\21. Continuous Enh. III'
export const aimAreaPath = 'Digital Plant\\25. Action Item Management'

export const RCA_SOURCE_TYPE_PREFIX = 'AST-RCA'
