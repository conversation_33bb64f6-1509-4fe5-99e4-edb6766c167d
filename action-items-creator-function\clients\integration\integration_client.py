import json
from typing import Optional
import requests
from clients.integration.requests import BaseIntegrationRequest, UserByNodeAccessRequest
from clients.integration.models import UserByNodeAccessResponse, UserIntegrationResponse
from clients.core.models import ServiceParams


class IntegrationClient:
    def __init__(
        self,
        params: ServiceParams,
    ):
        self._base_url = params.settings.user_management_api_uri
        self._log = params.logging
        self._token = params.token

    def get_user_by_request(
        self, request: BaseIntegrationRequest
    ) -> Optional[UserIntegrationResponse]:
        """
        Fetches user integration information from the User Management (UM) API.

        This method sends a POST request to the UM API to retrieve user information
        based on the provided site code and application code in the request. It parses
        the response into a `UserIntegrationResponse` model if the request is successful.

        Args:
            request (BaseIntegrationRequest):
                The request object containing the following fields:
                - site_code (str): The site code for the user query.
                - application_code (str): The application code for the integration.

        Returns:
            Optional[UserIntegrationResponse]:
                The parsed response containing user integration details if the request is successful.
                Returns `None` if an error occurs or if the response status is not `200 OK`.

        Raises:
            requests.exceptions.RequestException:
                Raised if there is an issue during the API call, such as connectivity or timeout.

        Logging:
            Logs the status and any encountered errors.

        Workflow:
            1. Constructs the API endpoint URL and the request body.
            2. Sends a POST request to the UM API.
            3. If the response status is `200 OK`:
                - Parses the JSON response into a `UserIntegrationResponse` object.
            4. If the response status is not `200 OK`:
                - Calls `response.raise_for_status()` to raise an HTTP exception.
            5. Handles any exceptions that occur during the request.
        """
        self._log.info(f"Get User by token")

        url = f"{self._base_url}/integration/user"

        try:
            body = {
                "site_code": request.site_code,
                "application_code": request.application_code,
            }
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self._token}",
            }
            response = requests.post(url, headers=headers, data=json.dumps(body))

            if response.status_code == 200:
                return UserIntegrationResponse(**response.json())
            else:
                response.raise_for_status()
        except requests.exceptions.RequestException as e:
            print(f"An error occurred: {e}")
            return None

    def get_user_by_feature_request(
        self, request: UserByNodeAccessRequest, feature_code: str
    ) -> list[UserByNodeAccessResponse]:
        """
        Fetches users associated with a specific feature code from the integration service.

        This method sends a POST request to the integration service to retrieve users
        based on the given `feature_code` and the details provided in the `request`.
        The response is parsed into a list of `UserByNodeAccessResponse` objects.

        Args:
            request (UserByNodeAccessRequest):
                The request object containing details such as `site_code`, `application_code`,
                and `type` to be used in the request body.
            feature_code (str):
                The specific feature code for which users should be retrieved.

        Returns:
            list[UserByNodeAccessResponse]:
                A list of `UserByNodeAccessResponse` objects representing users associated
                with the provided feature code. Returns `None` if an error occurs.

        Raises:
            requests.exceptions.RequestException:
                Raised if there is an issue with the HTTP request.

        Example:
            request = UserByNodeAccessRequest(
                site_code="Site123",
                application_code="App456",
                type="EditAccess"
            )
            feature_code = "Feature1"
            result = get_user_by_feature_request(request, feature_code)
            # Example result:
            # [
            #     UserByNodeAccessResponse(external_id="user1", ...),
            #     UserByNodeAccessResponse(external_id="user2", ...)
            # ]

        Notes:
            - Logs the request being made for debugging purposes.
            - The method assumes a JSON response from the server with a list of user data.
            - Raises an exception if the response status code is not 200.
        """
        self._log.info(f"Get Users by feature")

        url = f"{self._base_url}/integration/get_users_by_node_access"

        try:
            body = {
                "site": request.site_code,
                "application": request.application_code,
                "type": request.type,
                "code": feature_code,
            }
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self._token}",
            }
            response = requests.post(url, headers=headers, data=json.dumps(body))

            if response.status_code == 200:
                response_data = response.json()
                return [UserByNodeAccessResponse(**item) for item in response_data]
            else:
                response.raise_for_status()
        except requests.exceptions.RequestException as e:
            print(f"An error occurred: {e}")
            return None
