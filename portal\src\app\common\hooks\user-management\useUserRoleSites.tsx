import { gql } from '@apollo/client'
import { useEffect, useState } from 'react'
import { useGraphqlQuery } from '../cognite/useGraphqlQuery'
import { UserRoleSite } from '../../models/common/user-management/user-role-site'
import { EntityType, GetSpace } from '../../utils/space-util'
import { getLocalUserSite } from '@celanese/celanese-ui'

export interface UserRoleSiteQueryRequest {
    siteId?: string
    roleIds?: string[]
}

const buildUserRoleSiteQuery = (request: UserRoleSiteQueryRequest): string => {
    const filters: string[] = []

    const siteId = request.siteId || getLocalUserSite()?.siteId || '-'

    filters.push(
        `{ space: { eq: "${GetSpace(EntityType.UMG)}" } }`,
        `{ reportingSite: { externalId: { eq: "${siteId}" } } }`,
        `{ role: { roleCategory: { externalId: { eq: "RoleSite" } } } }`
    )

    if (request.roleIds?.length) {
        const externalIds = request.roleIds.map((id) => `"${id}"`).join(', ')
        filters.push(`{ role: { externalId: { in: [${externalIds}] } } }`)
    }

    const queryFilter = `{ and: [${filters.join(', ')}] }`

    const userComplementFilter = `{ and: [
        {
            employeeStatus: {
                or: [
                    { not: { externalId: { eq: "EMST_INACTIVE" } } },
                    { externalId: { isNull: true } }
                ]
            }
        },
        {
            userAzureAttribute: {
                user: {
                    active: { eq: true }
                }
            }
        }
    ]}`

    return `
        query QueryListUserRoleSites {
            listUserRoleSite(
                filter: ${queryFilter}
                , first: 1000
            ) {
                items {
                    externalId
                    role {
                        externalId
                        name
                        roleCategory {
                            externalId
                        }
                        application {
                            externalId
                        }
                    }
                    usersComplements(
                        filter: ${userComplementFilter}
                        , first: 1000
                    ) {
                        items {
                            userAzureAttribute {
                                externalId
                                user {
                                    email
                                    firstName
                                    lastName
                                    active
                                }
                            }
                            employeeStatus {
                                externalId
                            }
                        }
                    }
                }
            }
        }
    `
}

export const useUserRoleSites = (request: UserRoleSiteQueryRequest) => {
    const query = buildUserRoleSiteQuery(request)
    const { data: fdmData } = useGraphqlQuery<any>(gql(query), 'listUserRoleSite', {})

    const [resultData, setResultData] = useState<{ userRoleSites: UserRoleSite[]; loading: boolean }>({
        userRoleSites: [],
        loading: true,
    })

    useEffect(() => {
        if (fdmData.length == 0 || (request?.roleIds && request.roleIds.length === 0)) {
            setResultData({ userRoleSites: [], loading: false })
        } else {
            const userRoleSites = fdmData?.map((item: any) => {
                const userRoleSite: UserRoleSite = {
                    externalId: item.externalId,
                    roleId: item.role?.externalId,
                    roleCategoryId: item.role?.roleCategory?.externalId ?? '',
                    aplicationId: item.role?.application?.externalId ?? '',
                    name: item.role?.name,
                    usersComplements: item.usersComplements.items.map((userComplement: any) => {
                        return {
                            externalId: userComplement.userAzureAttribute?.externalId,
                            email: userComplement.userAzureAttribute?.user?.email ?? '',
                            firstName: userComplement.userAzureAttribute?.user?.firstName ?? '',
                            lastName: userComplement.userAzureAttribute?.user?.lastName ?? '',
                            name: userComplement.userAzureAttribute?.user
                                ? `${userComplement.userAzureAttribute?.user.lastName || ''}, ${
                                      userComplement.userAzureAttribute?.user.firstName || ''
                                  }`
                                : '',
                        }
                    }),
                }

                return userRoleSite
            })

            setResultData({ userRoleSites: userRoleSites, loading: false })
        }
    }, [fdmData])

    return {
        loading: resultData.loading,
        userRoleSites: resultData.userRoleSites,
    }
}
