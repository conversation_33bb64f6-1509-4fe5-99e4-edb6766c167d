'use client'
import '../common/utils/polyfills'
import { Box, Grid, Typography, useTheme } from '@mui/material'
import { useAuthGuard } from '../common/hooks/useAuthGuard'
import AuthGuardWrapper from '../common/wrapper/AuthGuardWrapper'
import { useCallback, useContext, useState } from 'react'
import { ClnPage, ClnPanel, MatIcon } from '@celanese/ui-lib'
import MessageModal from '../components/ModalComponent/Modal/MessageModal'
import { useRouter } from 'next/navigation'
import EventForms from '../components/EventsComponent/EventForm/eventForms'
import { BackArrow } from '../common/utils/backArrow'
import { TemplateSelectorModal } from '../components/ModalComponent/TemplateSelectorModal'
import { AzureFunctionClient } from '../common/clients/azure-function-client'
import { translate } from '../common/utils/generate-translate'
import { UserManagementContext, UserManagementContextState } from '@celanese/celanese-ui'
import { ActionItemForms } from '../components/ActionItemForm'
import { ActionDetailItem } from '../common/models/action-detail'
import { useLoading } from '../common/contexts/LoadingContext'
import { useSnackbar } from '../common/contexts/SnackbarContext'
import { UserAzureAttribute } from '../common/models/common/user-management/user-azure-attribute'
import { WorkflowStep } from '../common/models/common/approval-workflow/workflow-step'
import { ApprovalWorkflowStepDescriptionEnum } from '../common/enums/ApprovalWorkflowStepEnum'
import dayjs from 'dayjs'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import GenericAutocomplete from '../components/FieldsComponent/GenericAutocomplete'
import GenericFieldTitle from '../components/FieldsComponent/GenericFieldTitle'
import { PREFIX_USER_AZURE_ATTRIBUTE } from '../common/utils'

type Card = {
    route: string
    label: string
    onClick: () => void
    dataTest: string
}

const filterSchema = z.object({
    reportingSiteIds: z.string(),
})

type FilterSchema = z.infer<typeof filterSchema>

export default function NewActionItemPage() {
    const { checkPermissionsFromRoutes } = useAuthGuard()
    const router = useRouter()

    const theme = useTheme()
    const { showLoading } = useLoading()
    const { showSnackbar } = useSnackbar()

    const { userInfo } = useContext<UserManagementContextState>(UserManagementContext)

    const { reset, setValue, control, watch } = useForm<FilterSchema>({
        defaultValues: {
            reportingSiteIds: userInfo.selectedSites?.length == 1 ? userInfo.selectedSites[0].siteId : '',
        },
        resolver: zodResolver(filterSchema),
    })
    const reportingSiteIds = watch('reportingSiteIds')
    const reportingSiteOptions =
        userInfo?.selectedSites?.map((site) => ({
            value: site.siteId,
            label: site.siteName,
        })) ?? []

    const [isActionFormVisible, setIsActionFormVisible] = useState<boolean>(false)
    const [isEventFormVisible, setIsEventFormVisible] = useState<boolean>(false)

    const [isCancelModalOpen, setIsCancelModalOpen] = useState<boolean>(false)
    const [isTemplateSelectorModalOpen, setIsTemplateSelectorModalOpen] = useState<boolean>(false)

    const [actionItem, setActionItem] = useState<ActionDetailItem | undefined>()

    const cards: Card[] = [
        {
            route: '/new-action-item',
            label: translate('new-action-item.scratch'),
            onClick: () => {
                setIsActionFormVisible(true)
                localStorage.setItem('isEditForm', 'true')
            },
            dataTest: 'new_action_item_menu-create_from_scratch_card',
        },
        {
            route: '/new-action-item',
            label: translate('new-action-item.template'),
            onClick: () => {
                setIsTemplateSelectorModalOpen(true)
            },
            dataTest: 'new_action_item_menu-create_from_template_card',
        },
        {
            route: '/new-action-item',
            label: translate('new-action-item.events'),
            onClick: () => {
                setIsActionFormVisible(true)
                setIsEventFormVisible(true)
                localStorage.setItem('isEditForm', 'true')
            },
            dataTest: 'new_action_item_menu-create_from_events_card',
        },
    ]
        .filter((item): item is Card => Boolean(item))
        .filter((item) => checkPermissionsFromRoutes(item?.route ?? ''))

    const handleCloseForm = () => {
        setIsActionFormVisible(false)
        reset()
        router.push('/')
    }

    const handleLeaveCancelModal = () => {
        localStorage.removeItem('isEditForm')
        setActionItem(undefined)
        setIsActionFormVisible(false)
        setIsEventFormVisible(false)
        setIsCancelModalOpen(false)
        reset()
    }

    const fetchActionDetails = useCallback(async (templateConfigId: string) => {
        showLoading(true)
        try {
            const client = new AzureFunctionClient()

            const result = await client.getActionByTemplateConfigId({
                templateConfigId: templateConfigId,
            })

            const template: ActionDetailItem = {
                ...result.items[0].listAction,
                actionItemLink: result.items[0].listActionItemLink,
                assignees: result.items[0].listAction.assignees?.items?.map((x: UserAzureAttribute) => ({
                    ...x.user,
                    externalId: x.user.externalId.startsWith(PREFIX_USER_AZURE_ATTRIBUTE)
                        ? x.user.externalId
                        : `${PREFIX_USER_AZURE_ATTRIBUTE}${x.user.externalId}`,
                })),
                assignmentDate: dayjs().startOf('day'),
                dueDate: dayjs().startOf('day'),
                approver:
                    result.items[0].listApprovalWorkflowStep?.find(
                        (x: WorkflowStep) => x.description === ApprovalWorkflowStepDescriptionEnum.Approval
                    )?.users?.items?.[0] ?? undefined,
                verifier:
                    result.items[0].listApprovalWorkflowStep?.find(
                        (x: WorkflowStep) => x.description === ApprovalWorkflowStepDescriptionEnum.Verification
                    )?.users?.items?.[0] ?? undefined,
            }

            localStorage.setItem('isEditForm', 'true')
            setActionItem(template)
        } catch (err) {
            showSnackbar(translate('alerts.unexpectedErrorOcurred'), 'error', 'get-template-action')
        } finally {
            showLoading(false)
        }
    }, [])

    return (
        <AuthGuardWrapper componentName={NewActionItemPage.name}>
            <ClnPage id="cln-page-new-action" sx={{ height: 'calc(100vh - 48px)' }}>
                {!isActionFormVisible ? (
                    <Box id="box-new-action" sx={{ height: '100%' }}>
                        {BackArrow(translate('new-action-item.back'), () => router.push('/'))}
                        <Box
                            sx={{
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                height: {
                                    sm: 'auto',
                                    md: '100%',
                                },
                            }}
                        >
                            <ClnPanel
                                id="cln-panel-new-action"
                                sx={{
                                    padding: '3rem',
                                    display: 'flex',
                                    flexDirection: 'column',
                                    gap: '1.5rem',
                                    width: '766px',
                                }}
                            >
                                {userInfo && userInfo.applications?.[0]?.userSites?.length > 1 && (
                                    <Box
                                        sx={{
                                            display: 'flex',
                                            flexDirection: 'column',
                                            gap: '1.5rem',
                                        }}
                                    >
                                        <Typography variant="h3" color="primary.main" textTransform="uppercase">
                                            {translate('new-action-item.selectSite')}
                                        </Typography>
                                        <Grid container width={'100%'}>
                                            <Grid item xs={12} md={8} id={'reporting-site-field'}>
                                                <GenericAutocomplete
                                                    name="reportingSiteIds"
                                                    control={control}
                                                    options={reportingSiteOptions}
                                                    onChange={(value) => {
                                                        setValue('reportingSiteIds', value)
                                                    }}
                                                    multiple={false}
                                                    label={translate('new-action-item.reportingSite')}
                                                    size="small"
                                                    disableCloseOnSelect={false}
                                                />
                                            </Grid>
                                        </Grid>
                                    </Box>
                                )}
                                <Box
                                    sx={{
                                        display: 'flex',
                                        flexDirection: 'column',
                                        gap: '1.5rem',
                                    }}
                                >
                                    <Typography variant="h3" color="primary.main" textTransform="uppercase">
                                        {translate('new-action-item.description')}
                                    </Typography>
                                    <Box
                                        sx={{
                                            display: 'flex',
                                            flexDirection: { xs: 'column', sm: 'row' },
                                            justifyContent: 'space-around',
                                            gap: '0.4rem',
                                            flexWrap: 'wrap',
                                            alignContent: 'center',
                                        }}
                                    >
                                        <Grid container width={'100%'} spacing={1}>
                                            {cards.map((card, index) => (
                                                <Grid
                                                    item
                                                    xs={12}
                                                    md={4}
                                                    id={'reporting-site-field'}
                                                    key={`${card.label}-${index}`}
                                                    sx={{ display: 'flex', justifyContent: 'center' }}
                                                >
                                                    <ClnPanel
                                                        key={`${card.label}-${index}`}
                                                        sx={{
                                                            cursor: !reportingSiteIds ? 'not-allowed' : 'pointer',
                                                            display: 'flex',
                                                            width: '218px',
                                                            height: '218px',
                                                            padding: '1.5rem',
                                                            flexDirection: 'column',
                                                            alignItems: 'flex-start',
                                                            gap: '0.625rem',
                                                            justifyContent: 'space-between',
                                                        }}
                                                        onClick={!reportingSiteIds ? undefined : card.onClick}
                                                        data-test={card.dataTest}
                                                        data-origin="aim"
                                                    >
                                                        <Box
                                                            sx={{
                                                                display: 'flex',
                                                                flexDirection: 'column',
                                                                alignItems: 'flex-start',
                                                            }}
                                                        >
                                                            <Typography variant="h4" color="primary.main">
                                                                {translate('new-action-item.titleCard')}
                                                            </Typography>
                                                            <Typography
                                                                variant="subtitle1"
                                                                color="text.secondary"
                                                                sx={{
                                                                    textDecoration: 'underline',
                                                                }}
                                                            >
                                                                {card.label}
                                                            </Typography>
                                                        </Box>
                                                        <Box
                                                            style={{
                                                                display: 'flex',
                                                                padding: '0.75rem',
                                                                alignItems: 'flex-start',
                                                                gap: '0.625rem',
                                                                borderRadius: '5.625rem',
                                                                background: !reportingSiteIds
                                                                    ? theme.palette.primary[100]
                                                                    : theme.palette.primary.main,
                                                            }}
                                                        >
                                                            <MatIcon icon="add" color="primary.contrastText" />
                                                        </Box>
                                                    </ClnPanel>
                                                </Grid>
                                            ))}
                                        </Grid>
                                    </Box>
                                </Box>
                            </ClnPanel>
                        </Box>
                    </Box>
                ) : (
                    <ClnPanel
                        sx={{
                            padding: '1.5rem',
                            display: 'flex',
                            flexDirection: 'column',
                        }}
                    >
                        <Box>
                            {isEventFormVisible && (
                                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                                    {BackArrow(translate('source-event.create'), () => setIsCancelModalOpen(true))}

                                    <Box
                                        id="title-location"
                                        sx={{
                                            display: 'flex',
                                            justifyContent: 'center',
                                            alignItems: 'center',
                                            flexDirection: 'row',
                                            gap: '4px',
                                        }}
                                    >
                                        <GenericFieldTitle fieldName={translate('home.showLocation')} isParagraphText />
                                        <GenericFieldTitle
                                            fieldName={
                                                reportingSiteOptions.find((x) => x.value === reportingSiteIds)?.label ??
                                                ''
                                            }
                                            isLocation
                                        />
                                    </Box>
                                </Box>
                            )}

                            <Box>
                                {isEventFormVisible ? (
                                    <EventForms
                                        onClose={setIsCancelModalOpen}
                                        showRadioGroup
                                        siteId={reportingSiteIds}
                                    />
                                ) : (
                                    userInfo && (
                                        <ActionItemForms
                                            siteId={reportingSiteIds}
                                            pageTitle={translate('table.newActionItem')}
                                            actionItem={actionItem}
                                            activeUser={userInfo}
                                            finishSteppedFunction={handleCloseForm}
                                            clickLeaveCancelModal={handleLeaveCancelModal}
                                        />
                                    )
                                )}
                                <MessageModal
                                    name=""
                                    text={translate('requestModal.closeQuestion')}
                                    open={isCancelModalOpen}
                                    isCancelModal
                                    handleClose={() => setIsCancelModalOpen(false)}
                                    handleLeave={handleLeaveCancelModal}
                                />
                            </Box>
                        </Box>
                    </ClnPanel>
                )}
                {isTemplateSelectorModalOpen && (
                    <TemplateSelectorModal
                        siteId={reportingSiteIds}
                        onSelectTemplate={(templateConfigId) => {
                            setIsTemplateSelectorModalOpen(false)
                            setIsActionFormVisible(true)
                            fetchActionDetails(templateConfigId)
                        }}
                        onCancel={() => {
                            setIsTemplateSelectorModalOpen(false)
                        }}
                    />
                )}
            </ClnPage>
        </AuthGuardWrapper>
    )
}
