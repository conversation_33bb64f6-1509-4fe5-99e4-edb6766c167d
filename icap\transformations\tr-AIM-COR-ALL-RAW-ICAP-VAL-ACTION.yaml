# Manifest file downloaded from fusion
externalId: tr-AIM-COR-ALL-RAW-ICAP-VAL-ACTION
name: AIM-COR-ALL-RAW-ICAP-VAL-ACTION
query: >-
  WITH validation_1 AS (
      SELECT
          key,
          reportingSite.externalId AS reporting_site,
          CASE
              WHEN category IS NULL THEN 1
              ELSE 0
          END AS category_null,
          CASE
              WHEN subCategory IS NULL THEN 1
              ELSE 0
          END AS subCategory_null,
          CASE
              WHEN dueDate IS NULL THEN 1
              ELSE 0
          END AS dueDate_null,
          CASE
              WHEN displayDueDate IS NULL THEN 1
              ELSE 0
          END AS displayDueDate_null,
          CASE
              WHEN assignmentDate IS NULL THEN 1
              ELSE 0
          END AS assignmentDate_null,
          CASE
              WHEN assignedTo IS NULL THEN 1
              ELSE 0
          END AS assignedTo_null,
          CASE
              WHEN owner IS NULL THEN 1
              ELSE 0
          END AS owner_null,
          CASE
              WHEN createdBy IS NULL THEN 1
              ELSE 0
          END AS createdBy_null,
          CASE
              WHEN currentStatus IS NULL THEN 1
              ELSE 0
          END AS currentStatus_null,
          CASE
              WHEN reportingUnit IS NULL THEN 1
              ELSE 0
          END AS reportingUnit_null,
          CASE
              WHEN reportingSite IS NOT NULL
              AND reportingUnit IS NOT NULL
              AND not startswith(
                  reportingUnit.externalId,
                  replace(reportingSite.externalId, "STS-", "UNT-")
              ) THEN 1
              ELSE 0
          END AS reportingUnit_from_another_site,
          CASE
              WHEN reportingSite IS NOT NULL
              AND reportingLocation IS NOT NULL
              AND NOT startswith(
                  reportingLocation.externalId,
                  replace(reportingSite.externalId, "STS-", "LOC-")
              ) THEN 1
              ELSE 0
          END AS reportingLocation_from_another_site,
          CASE
              WHEN isPrivate THEN 1
              ELSE 0
          END AS isPrivate
      FROM
          `AIM-COR`.`ICAP-STG-Action`
  ),

  validation_2 AS (
      SELECT
          validation_1.*,
          CASE
              WHEN (
                  category_null == 1
                  OR subCategory_null == 1
                  OR dueDate_null == 1
                  OR displayDueDate_null == 1
                  OR assignmentDate_null == 1
                  OR assignedTo_null == 1
                  OR owner_null == 1
                  OR createdBy_null == 1
                  OR currentStatus_null == 1
                  OR reportingUnit_null == 1
                  OR reportingUnit_from_another_site == 1
                  OR reportingLocation_from_another_site == 1
              ) THEN 1
              ELSE 0
          END AS has_error
      FROM
          validation_1
  ),

  validation_3 AS (
      SELECT 
          validation_2.*,
          CASE
              WHEN (
                  has_error = 1
                  AND isPrivate = 1
              ) THEN 1
              ELSE 0
          END AS has_error_and_is_private,
          CASE
              WHEN (
                  has_error = 1
                  AND isPrivate = 0
              ) THEN 1
              ELSE 0
          END AS has_error_and_is_not_private
      FROM
          validation_2
  )


  SELECT
      "ALL" AS key,
      COUNT(key) AS `1_total_actions`,
      SUM(isPrivate) AS `2_total_isPrivate`,
      SUM(has_error) AS `3_total_actions_with_errors`,
      100 * (SUM(has_error) / COUNT(key)) AS `4_percentage_actions_with_errors`,
      SUM(has_error_and_is_not_private) AS `5_total_non_private_actions_with_errors`,
      100 * (SUM(has_error_and_is_not_private) / COUNT(key)) AS `6_percentage_non_private_actions_with_errors`,
      SUM(has_error_and_is_private) AS `7_total_private_actions_with_errors`,
      100 * (SUM(has_error_and_is_private) / COUNT(key)) AS `8_percentage_private_actions_with_errors`,
      "ALL" AS `9_reporting_site`,
      SUM(owner_null) AS `10_total_owner_null`,
      SUM(assignedTo_null) AS `11_total_assignedTo_null`,
      SUM(createdBy_null) AS `12_total_createdBy_null`,
      SUM(currentStatus_null) AS `13_total_currentStatus_null`,
      SUM(category_null) AS `14_total_category_null`,
      SUM(subCategory_null) AS `15_total_subCategory_null`,
      SUM(reportingLocation_from_another_site) AS `16_total_reportingLocation_from_another_site`,
      SUM(reportingUnit_null) AS `17_total_reportingUnit_null`,
      SUM(reportingUnit_from_another_site) AS `18_total_reportingUnit_from_another_site`,
      SUM(assignmentDate_null) AS `19_total_assignmentDate_null`,
      SUM(displayDueDate_null) AS `20_total_displayDueDate_null`,
      SUM(dueDate_null) AS `21_total_dueDate_null`
  FROM
      validation_3

  UNION


  SELECT
      coalesce(reporting_site, "-") AS key,
      COUNT(key) AS `1_total_actions`,
      SUM(isPrivate) AS `2_total_isPrivate`,
      SUM(has_error) AS `3_total_actions_with_errors`,
      100 * (SUM(has_error) / COUNT(key)) AS `4_percentage_actions_with_errors`,
      SUM(has_error_and_is_not_private) AS `5_total_non_private_actions_with_errors`,
      100 * (SUM(has_error_and_is_not_private) / COUNT(key)) AS `6_percentage_non_private_actions_with_errors`,
      SUM(has_error_and_is_private) AS `7_total_private_actions_with_errors`,
      100 * (SUM(has_error_and_is_private) / COUNT(key)) AS `8_percentage_private_actions_with_errors`,
      reporting_site AS `9_reporting_site`,
      SUM(owner_null) AS `10_total_owner_null`,
      SUM(assignedTo_null) AS `11_total_assignedTo_null`,
      SUM(createdBy_null) AS `12_total_createdBy_null`,
      SUM(currentStatus_null) AS `13_total_currentStatus_null`,
      SUM(category_null) AS `14_total_category_null`,
      SUM(subCategory_null) AS `15_total_subCategory_null`,
      SUM(reportingLocation_from_another_site) AS `16_total_reportingLocation_from_another_site`,
      SUM(reportingUnit_null) AS `17_total_reportingUnit_null`,
      SUM(reportingUnit_from_another_site) AS `18_total_reportingUnit_from_another_site`,
      SUM(assignmentDate_null) AS `19_total_assignmentDate_null`,
      SUM(displayDueDate_null) AS `20_total_displayDueDate_null`,
      SUM(dueDate_null) AS `21_total_dueDate_null`
  FROM
      validation_3
  GROUP BY
      reporting_site
destination:
  database: AIM-COR
  table: ICAP-VAL-Action
  type: raw
ignoreNullFields: true
shared: true
action: upsert
dataSetExternalId: AIM-COR-ALL-DAT
# Specify credentials separately like this:
# authentication:
#   read:
#     clientId: ${READ_CLIENT_ID}
#     clientSecret: ${READ_CLIENT_SECRET}
#     tokenUrl: ${READ_TOKEN_URL}
#     cdfProjectName: ${READ_CDF_PROJECT_NAME}
#     # Optional: If idP requires providing the scopes
#     scopes:
#       - ${READ_SCOPES}
#     # Optional: If idP requires providing the audience
#     audience: ${READ_CDF_AUDIENCE}
#   write:
#     clientId: ${WRITE_CLIENT_ID}
#     clientSecret: ${WRITE_CLIENT_SECRET}
#     tokenUrl: ${WRITE_TOKEN_URL}
#     cdfProjectName: ${WRITE_CDF_PROJECT_NAME}
#     # Optional: If idP requires providing the scopes
#     scopes:
#       - ${WRITE_SCOPES}
#     # Optional: If idP requires providing the audience
#     audience: ${WRITE_CDF_AUDIENCE}
# Or together like this:
# authentication:
#   clientId: ${CLIENT_ID}
#   clientSecret: ${CLIENT_SECRET}
#   tokenUrl: ${TOKEN_URL}
#   # Optional: If idP requires providing the scopes
#   cdfProjectName: ${CDF_PROJECT_NAME}
#   scopes:
#    - ${SCOPES}
#   # Optional: If idP requires providing the audience
#   audience: ${AUDIENCE}