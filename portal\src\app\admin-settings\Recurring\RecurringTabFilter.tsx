import { Box, styled } from '@mui/material'
import { z } from 'zod'
import { Controller, SubmitHandler, useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { ClnButton, ClnDatePicker } from '@celanese/ui-lib'
import dayjs, { Dayjs } from 'dayjs'
import { FilterOptionsRecurring, FilterSelectedRecurring } from '../../common/models/admin-settings/filter-recurring'
import { useEffect } from 'react'
import { translate } from '@/app/common/utils/generate-translate'
import GenericAutocomplete, { AutocompleteOption } from '@/app/components/FieldsComponent/GenericAutocomplete'

const zodDate = z.instanceof(dayjs as unknown as typeof Dayjs).nullable()

const categoryFilterSchema = z.object({
    unit: z.array(z.string()),
    owner: z.array(z.string()),
    updateBy: z.array(z.string()),
    category: z.array(z.string()),
    startDate: zodDate,
    frequency: z.array(z.string()),
    status: z.array(z.string()),
    noEndDate: z.string().nullable(),
})

type RecurringFilterSchema = z.infer<typeof categoryFilterSchema>

type RecurringTabFilterProps = {
    data: FilterOptionsRecurring
    isAdminPage?: boolean
    defaultFilter?: FilterSelectedRecurring
    onSubmit: (filters: any) => void
}

const Form = styled('form')({
    display: 'flex',
    flexDirection: 'column',
    width: '18rem',
    padding: '1.5rem',
    gap: '1rem',
})

export function RecurringTabFilter({ data, isAdminPage, defaultFilter, onSubmit }: RecurringTabFilterProps) {
    const cleanFilter = {
        unit: [],
        owner: [],
        updateBy: [],
        category: [],
        startDate: null,
        frequency: [],
        status: [],
        noEndDate: null,
    }

    const { reset, setValue, handleSubmit, control } = useForm<RecurringFilterSchema>({
        defaultValues: cleanFilter,
        resolver: zodResolver(categoryFilterSchema),
    })

    const filterOptions = [
        { name: 'unit', label: 'adminSettings.table.filter.unit' },
        { name: 'category', label: 'adminSettings.table.filter.category' },
        { name: 'frequency', label: 'adminSettings.table.filter.frequency' },
        { name: 'owner', label: 'adminSettings.table.filter.owner' },
        { name: 'noEndDate', label: 'adminSettings.table.filter.noEndDate', multiple: false },
        { name: 'status', label: 'adminSettings.table.filter.status' },
        ...(isAdminPage ? [{ name: 'updateBy', label: 'adminSettings.table.headers.updateBy' }] : []),
    ]

    const clearFunction = () => {
        reset(cleanFilter)
    }

    const defaultFunction = () => {
        reset(cleanFilter)
    }

    const submitFn: SubmitHandler<RecurringFilterSchema> = (data) => {
        onSubmit(data)
    }

    useEffect(() => {
        const transformedFilter = {
            ...cleanFilter,
            ...defaultFilter,
            startDate: defaultFilter?.startDate ? dayjs(defaultFilter?.startDate) : (null as Dayjs | null),
        }
        reset({ ...transformedFilter })
    }, [defaultFilter])

    return (
        <Box>
            <Form onSubmit={handleSubmit(submitFn)}>
                <Box sx={{ display: 'flex', gap: '8px', justifyContent: 'flex-end' }}>
                    <ClnButton
                        size="small"
                        variant="outlined"
                        label={translate('table.filter.clear')}
                        onClick={() => {
                            clearFunction()
                        }}
                    />
                    <ClnButton
                        size="small"
                        variant="outlined"
                        label={translate('table.filter.default')}
                        onClick={() => {
                            defaultFunction()
                        }}
                    />
                </Box>
                <Controller
                    name="startDate"
                    control={control}
                    render={({ field: { value, onChange } }) => (
                        <ClnDatePicker
                            value={value}
                            onChange={(e) => {
                                onChange(e)
                            }}
                            slotProps={{
                                textField: {
                                    size: 'small',
                                },
                            }}
                            label={translate('adminSettings.table.filter.startDate')}
                        />
                    )}
                />
                {filterOptions.map(({ name, label, multiple = true }) => (
                    <GenericAutocomplete
                        key={name}
                        name={name}
                        size="small"
                        control={control}
                        options={data[name as keyof FilterOptionsRecurring] as AutocompleteOption[]}
                        onChange={(newValue) => setValue(name as keyof FilterOptionsRecurring, newValue)}
                        label={translate(label)}
                        noOptionsText={translate('adminSettings.table.filter.noOptions')}
                        multiple={multiple}
                    />
                ))}
                <Box
                    sx={{
                        display: 'flex',
                        width: '100%',
                        justifyContent: 'center',
                        margin: '20px 0 10px 0',
                        div: {
                            width: '100%',
                        },
                        button: {
                            width: '100%',
                        },
                    }}
                >
                    <ClnButton
                        type="submit"
                        size="small"
                        variant="contained"
                        label={translate('table.filter.applyFilter')}
                    />
                </Box>
            </Form>
        </Box>
    )
}
