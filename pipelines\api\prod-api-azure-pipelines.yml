trigger:
  branches:
    include:
      - dev
  paths:
    include:
      - api/*

variables:
  - group: components-licenses-keys
  - group: templates-common
  - name: azureSubscription
    value: 'Action Item Management Deploy - PROD'
  - name: webAppName
    value: 'app-dplantactionitemmgmtapi-p-ussc-01'
  - name: rootFolderOrFile
    value: 'api'
  - name: appName
    value: 'aim'
  - name: appType
    value: 'api'
  - name: environment
    value: 'prod'

pool:
  name: "GST-Backend-Linux"

resources:
  repositories:
    - repository: templates
      type: git
      name: Templates
      ref: dev
      clean: true

stages:
- template: deploy/template-deploy-all-container.yml@templates
