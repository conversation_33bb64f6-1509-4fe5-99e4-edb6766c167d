import { MAX_DEFAULT_TEXT_FIELD, MIN_DEFAULT_TEXT_FIELD } from '@/app/common/utils'
import AuthGuardWrapper from '@/app/common/wrapper/AuthGuardWrapper'
import { ClnButton } from '@celanese/ui-lib'
import { zodResolver } from '@hookform/resolvers/zod'
import { Modal, Box, Typography } from '@mui/material'
import { useForm } from 'react-hook-form'
import { z } from 'zod'
import GenericTextField from '../FieldsComponent/GenericTextField'
import { translate } from '@/app/common/utils/generate-translate'

type DetailDeleteModalProps = {
    name: string
    alertText: string
    activeAlertText: boolean
    open: boolean
    handleClose: () => void
    deleteFunction: (onSuccess?: () => void) => void
}

const styles = {
    modal: {
        position: 'absolute' as 'absolute',
        top: '50%',
        left: '50%',
        transform: 'translate(-50%, -50%)',
        width: 400,
        bgcolor: 'background.paper',
        border: '2px solid #000',
        borderRadius: '8px',
        boxShadow: 24,
        padding: 3,
    },
    formContainer: {
        width: '100%',
        display: 'flex',
        flexDirection: 'column' as 'column',
        alignItems: 'center',
        marginTop: '1rem',
        '& .MuiFormControl-root': { maxWidth: '100%', width: '100%' },
        '& > *': { width: '100%' },
    },
    buttonContainer: {
        width: '100%',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'baseline',
        marginTop: '2rem',
    },
}

const formSchema = z.object({
    comments: z.string().min(MIN_DEFAULT_TEXT_FIELD).max(MAX_DEFAULT_TEXT_FIELD),
})

type RequestSchema = z.infer<typeof formSchema>

export default function DetailDeleteModal({
    name,
    alertText,
    activeAlertText,
    open,
    handleClose,
    deleteFunction,
}: DetailDeleteModalProps) {
    const {
        control,
        handleSubmit,
        getValues,
        reset,
        formState: { errors },
    } = useForm<RequestSchema>({
        resolver: zodResolver(formSchema),
        defaultValues: { comments: '' },
    })

    const handleDelete = async () => deleteFunction(() => handleCloseAndReset())

    const handleCloseAndReset = () => {
        reset()
        handleClose()
    }

    return (
        <AuthGuardWrapper componentName={DetailDeleteModal.name}>
            <Modal open={open} onClose={handleCloseAndReset}>
                <Box sx={styles.modal}>
                    <Typography sx={{ fontWeight: 'bold' }}>
                        {translate('requestModal.deleteQuestion')} ({name})
                    </Typography>
                    {activeAlertText && (
                        <Typography sx={{ fontWeight: 'bold', color: 'error.main' }}>{alertText}</Typography>
                    )}
                    <Box sx={styles.formContainer}>
                        <GenericTextField
                            required
                            name="comments"
                            control={control}
                            valueController={getValues('comments')}
                            rows={5}
                            label={translate('requestModal.comments')}
                            helperText={translate('requestModal.textHelperComments')}
                            error={Boolean(errors.comments)}
                        />
                    </Box>
                    <Box sx={styles.buttonContainer}>
                        <ClnButton
                            variant="outlined"
                            label={translate('requestModal.cancel')}
                            onClick={handleCloseAndReset}
                            sx={{ width: '10rem', '@media (max-width:1024px)': { width: '5rem' } }}
                        />
                        <ClnButton
                            variant="contained"
                            label={translate('requestModal.delete')}
                            onClick={handleSubmit(handleDelete)}
                            color="error"
                        />
                    </Box>
                </Box>
            </Modal>
        </AuthGuardWrapper>
    )
}
