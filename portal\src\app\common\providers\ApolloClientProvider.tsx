import { createApolloClient } from '@/app/common/factories/apollo-client-factory'
import { ApolloProvider } from '@apollo/client'
import React, { PropsWithChildren } from 'react'
import { useAuthToken } from '../hooks'

export const ApolloClientProvider = ({ children }: PropsWithChildren) => {
    const { getAuthToken } = useAuthToken()
    const client = React.useMemo(() => createApolloClient(getAuthToken), [getAuthToken])

    return <ApolloProvider client={client}>{children}</ApolloProvider>
}
