import { Category, Status } from '@/app/common/models/action'
import {
    ActionStatusExternalIdClearEnum,
    ActionStatusExternalIdHomeClosedDefaultEnum,
    ActionStatusExternalIdHomeDefaultEnum,
} from '@/app/common/enums/ActionItemStatusEnum'
import { ClnButton } from '@celanese/ui-lib'
import { zodResolver } from '@hookform/resolvers/zod'
import { Box, Grid, styled, Typography, useMediaQuery, useTheme } from '@mui/material'
import dayjs, { Dayjs } from 'dayjs'
import { useCallback, useEffect, useMemo, useState } from 'react'
import { SubmitHandler, useForm } from 'react-hook-form'
import { z } from 'zod'
import { useUsersSearch } from '@/app/common/hooks/user-management/useUsers'
import { Application } from '@/app/common/models/application'
import { SiteSpecificCategoryRequest } from '@/app/common/models/site-specific-category'
import GenericFieldTitle from '../../FieldsComponent/GenericFieldTitle'
import GenericDateRangePicker from '../../FieldsComponent/GenericDateRangePicker'
import GenericTextField from '../../FieldsComponent/GenericTextField'
import GenericAutocomplete, { AutocompleteOption } from '../../FieldsComponent/GenericAutocomplete'
import { transformOptions, transformOptionsForUser } from '@/app/common/utils/transform-options-for-filter'
import { ActionItemStateStatus, ActionItemStateStatusEnum } from '@/app/common/enums/KpiStatusEnum'
import { translate } from '@/app/common/utils/generate-translate'
import { transformDataToFormInput } from '@/app/common/utils/transform-input'
import { PageType, PageTypeValue } from '@/app/common/enums/PageTypeEnum'
import { UserRolesPermission } from '@celanese/celanese-sdk'
import { useAsyncAutocomplete } from '@/app/common/hooks/general-functions/useAsyncAutocomplete'
import { ReportingUnitRequest } from '@/app/common/models/common/asset-hierarchy/reporting-unit'
import { ReportingLocationRequest } from '@/app/common/models/common/asset-hierarchy/reporting-location'
import { ActionItemSubCategory } from '@/app/common/models/sub-category'

interface BaseFieldConfig {
    id: string
    fieldType: 'text' | 'autocomplete' | 'dateRange'
    label: string
    name: string
    size?: 'small' | 'medium'
    dataTest?: string
    dataOrigin?: string
    preText?: string
    disabled?: boolean
}

interface AutocompleteFieldConfig extends BaseFieldConfig {
    fieldType: 'autocomplete'
    options: any[]
    valueController?: any
    loading?: boolean
    placeholder?: string
    multiple?: boolean
    onChange: (newValue: any, fieldOption?: any) => void
    onInputChange?: (event: React.ChangeEvent<any>, newInputValue: string) => void
}

interface DateRangeFieldConfig extends BaseFieldConfig {
    fieldType: 'dateRange'
}

type FieldConfig = BaseFieldConfig | AutocompleteFieldConfig | DateRangeFieldConfig

type FormSchema = z.infer<typeof formSchema>

const zodDate = z.instanceof(dayjs as unknown as typeof Dayjs).nullable()

const formSchema = z.object({
    externalIdPrefix: z.string().optional(),
    titlePrefix: z.string().optional(),
    dueDate: z.tuple([zodDate.optional(), zodDate]),
    statusExternalIds: z.array(z.string()).optional(),
    onlyPrivate: z.string().optional(),
    categoryExternalId: z.array(z.string()).optional(),
    subcategoryExternalId: z.array(z.string()).optional(),
    siteSpecificCategoryExternalId: z.array(z.string()).optional(),
    ownerExternalId: z.array(z.string()).optional(),
    assignedToExternalId: z.array(z.string()).optional(),
    reportingSiteExternalIds: z.array(z.string()).optional(),
    reportingUnitExternalIds: z.array(z.string()).optional(),
    reportingLocationExternalIds: z.array(z.string()).optional(),
    applicationExternalId: z.array(z.string()).optional(),
    sourceInfoPrefix: z.string().optional(),
    icapActionIdPrefix: z.string().optional(),
    sourceEventTitlePrefix: z.string().optional(),
})

export interface FilterInfoProps {
    reportingSiteExternalId: string | string[]
    statusExternalIds?: string[]
    dueDateGte?: Dayjs | string | null
    dueDateLt?: Dayjs | string | null
    updateStatusDate?: (Dayjs | null)[][]
    onlyPrivate?: boolean
    reportingSiteExternalIds?: string[]
    reportingUnitExternalIds?: string[]
    reportingLocationExternalIds?: string[]
    ownerExternalId?: string[]
    assignedToExternalId?: string[]
    onSiteManagerToExternalId?: string[]
    categoryExternalId?: string[]
    subcategoryExternalId?: string[]
    siteSpecificCategoryExternalId?: string[]
    applicationExternalId?: string[]
    externalIdPrefix?: string
    titlePrefix?: string
    sourceInfoPrefix?: string
    icapActionIdPrefix?: string
    search?: string
    approvalWorkflowIds?: string[]
    kpiFilter?: ActionItemStateStatus
    permissionsExtend?: boolean
    extensionApprovalSiteExternalIds?: string[]
    permissionsReassing?: boolean
    reassignmentApprovalSiteExternalIds?: string[]

    permissionsReportingUnitExternalIds?: string[]
    pageSize: number
    sortBy?: string
    direction?: string
    cursor?: string
    activeUserEmail?: string
    activeUser?: string
    activeUserRolesIds?: string[]
    activeUserTeamsIds?: string[]
    sourceEventExternalId?: string
    searchProperties?: string[]
    externalIds?: string[]
    sourceEventTitlePrefix?: string
    sourceEventTitleEq?: string
}

export interface FilterDefault {
    status: (string | undefined)[]
    owner: (string | undefined)[]
    assignee: (string | undefined)[]
}

interface ActionItemFilterProps {
    onFilter: (dataInfo: FilterInfoProps) => void
    categoriesForFilter?: Category[]
    subcategoriesForFilter?: ActionItemSubCategory[]
    applicationsForFilter?: Application[]
    statusForFilter?: Status[]
    filterInfo: FilterInfoProps
    defaultActiveUser?: AutocompleteOption[]
    activeUserExternalId: string
    isToDoTab?: boolean
    pageType?: PageTypeValue

    defaultOwnerName?: AutocompleteOption[]
    setDefaultOwnerName?: (value: AutocompleteOption[]) => void
    defaultAssigneeName?: AutocompleteOption[]
    defaultSiteSpecificCategories?: AutocompleteOption[]
    defaultReportingUnits?: AutocompleteOption[]
    defaultReportingLocations?: AutocompleteOption[]
    setDefaultSiteSpecificCategories?: (value: AutocompleteOption[]) => void
    setDefaultReportingLocations?: (value: AutocompleteOption[]) => void
    setDefaultReportingUnits?: (value: AutocompleteOption[]) => void
    setPage: (value: number) => void
    setDefaultAssigneeName?: (value: AutocompleteOption[]) => void

    icapActionIdFilterAvailable?: boolean
    sourceEventTitleFilterAvailable?: boolean

    infoMessage?: string

    disableDefaultFilterFields?: boolean
    defaultFilterInfo?: FilterInfoProps
    activeUser: UserRolesPermission
}

const Form = styled('form')({
    padding: '1rem',
})

export function HomeFilter({
    onFilter,
    categoriesForFilter = [],
    subcategoriesForFilter = [],
    applicationsForFilter = [],
    statusForFilter = [],
    filterInfo,
    defaultActiveUser,
    activeUserExternalId,
    isToDoTab,
    pageType,
    defaultOwnerName,
    setDefaultOwnerName,
    defaultAssigneeName,
    setDefaultAssigneeName,
    setPage,
    icapActionIdFilterAvailable = false,
    sourceEventTitleFilterAvailable = false,
    infoMessage,
    disableDefaultFilterFields,
    defaultFilterInfo,
    activeUser,
    defaultSiteSpecificCategories,
    defaultReportingUnits,
    defaultReportingLocations,
    setDefaultSiteSpecificCategories,
    setDefaultReportingLocations,
    setDefaultReportingUnits,
}: ActionItemFilterProps) {
    const theme = useTheme()
    const isTablet = useMediaQuery(theme.breakpoints.down('md'))
    const isMobile = useMediaQuery(theme.breakpoints.down('sm'))

    const statusOptions = transformOptions(statusForFilter, 'name', 'name', 'status.fullName')
    const applicationOptions = transformOptions(applicationsForFilter, 'alias', 'name')
    const categoryOptions = transformOptions(categoriesForFilter, 'name', 'name', 'category')
    const subcategory1Options = transformOptions(subcategoriesForFilter, 'name', 'name', 'subCategory1')
    const isPrivateOptions = transformOptions(
        [
            { externalId: 'true', value: translate('common.yes') },
            { externalId: 'false', value: translate('common.no') },
        ],
        'value'
    )
    const reportingSiteOptions =
        activeUser.selectedSites?.map((site) => ({
            value: site.siteId,
            label: site.siteName,
        })) ?? []
    const selectedReportingSites = useMemo(
        () => activeUser.selectedSites?.map((site) => site.siteId) ?? [],
        [activeUser.selectedSites]
    )

    const [ownerName, setOwnerName] = useState<AutocompleteOption[]>(
        filterInfo.ownerExternalId ? defaultOwnerName ?? [] : []
    )

    const [assigneeName, setAssigneeName] = useState<AutocompleteOption[]>(
        filterInfo.assignedToExternalId ? defaultAssigneeName ?? [] : []
    )

    const [usersParam, setUsersParam] = useState<string>('NULL_PARAM')
    const { users: filtredUsers, loading } = useUsersSearch(
        useMemo(() => {
            return usersParam
        }, [usersParam])
    )

    const sortedFiltredUsers = transformOptionsForUser([...filtredUsers])
    const sortedOwnerOptions = Array.from(
        new Map([...sortedFiltredUsers, ...ownerName].map((item) => [item.value, item])).values()
    ).sort((a, b) => a.label.localeCompare(b.label))

    const sortedAssigneeOptions = Array.from(
        new Map([...sortedFiltredUsers, ...assigneeName].map((item) => [item.value, item])).values()
    ).sort((a, b) => a.label.localeCompare(b.label))

    const { control, setValue, handleSubmit, getValues, reset } = useForm<FormSchema>({
        resolver: zodResolver(formSchema),
        defaultValues: {
            externalIdPrefix: '',
            dueDate: [null, null],
            statusExternalIds: filterInfo.statusExternalIds ?? [],
            onlyPrivate: undefined,
            categoryExternalId: [],
            subcategoryExternalId: [],
            siteSpecificCategoryExternalId: [],
            ownerExternalId: [],
            assignedToExternalId: filterInfo.assignedToExternalId ?? [],
            reportingSiteExternalIds: [],
            reportingUnitExternalIds: [],
            reportingLocationExternalIds: [],
            applicationExternalId: [],
            sourceInfoPrefix: '',
            icapActionIdPrefix: '',
            sourceEventTitlePrefix: '',
        },
    })

    const buildSiteSpecificCategoryRequest = useCallback(
        (search: string): SiteSpecificCategoryRequest => ({
            search,
            reportingSiteExternalIds: selectedReportingSites,
        }),
        [selectedReportingSites]
    )
    const buildReportingUnitRequest = useCallback(
        (search: string): ReportingUnitRequest => ({
            search,
            reportingSiteExternalIds: selectedReportingSites,
        }),
        [selectedReportingSites]
    )
    const buildReportingLocationRequest = useCallback(
        (search: string): ReportingUnitRequest => ({
            search,
            reportingSiteExternalIds: selectedReportingSites,
        }),
        [selectedReportingSites]
    )

    const {
        selectedOptions: selectedSiteSpecificCategoryOptions,
        setSelectedOptions: setSelectedSiteSpecificCategoryOptions,
        setSearch: setSiteSpecificCategorySearch,
        options: siteSpecificCategoryOptions,
        loading: isLoadingSiteSpecificCategories,
    } = useAsyncAutocomplete(
        filterInfo?.siteSpecificCategoryExternalId ? defaultSiteSpecificCategories ?? [] : [],
        (client, req: SiteSpecificCategoryRequest) => client.getSiteSpecificCategories(req),
        buildSiteSpecificCategoryRequest,
        (req) => req.search === 'NULL_PARAM',
        'name',
        500,
        reportingSiteOptions.length == 1
    )

    const {
        selectedOptions: selectedReportingUnitOptions,
        setSelectedOptions: setSelectedReportingUnitOptions,
        setSearch: setReportingUnitSearch,
        options: reportingUnitOptions,
        loading: isLoadingReportingUnits,
    } = useAsyncAutocomplete(
        filterInfo?.reportingUnitExternalIds ? defaultReportingUnits ?? [] : [],
        (client, req: ReportingUnitRequest) => client.getReportingUnits(req),
        buildReportingUnitRequest,
        (req) => req.search === 'NULL_PARAM',
        'description',
        500,
        reportingSiteOptions.length == 1
    )

    const {
        selectedOptions: selectedReportingLocationOptions,
        setSelectedOptions: setSelectedReportingLocationOptions,
        setSearch: setReportingLocationSearch,
        options: reportingLocationOptions,
        loading: isLoadingReportingLocations,
    } = useAsyncAutocomplete(
        filterInfo?.reportingLocationExternalIds ? defaultReportingLocations ?? [] : [],
        (client, req: ReportingLocationRequest) => client.getReportingLocations(req),
        buildReportingLocationRequest,
        (req) => req.search === 'NULL_PARAM',
        'description',
        500,
        reportingSiteOptions.length == 1
    )

    const getStatusExternalIds = () => {
        if (isToDoTab) return Object.values(ActionStatusExternalIdHomeDefaultEnum)
        if (pageType === PageType.EventDetails) return Object.values(ActionStatusExternalIdClearEnum)
        return Object.values(ActionStatusExternalIdHomeClosedDefaultEnum)
    }

    const formSubmit: SubmitHandler<FormSchema> = (data) => {
        setDefaultOwnerName && setDefaultOwnerName(ownerName)
        setDefaultAssigneeName && setDefaultAssigneeName(assigneeName)
        setDefaultSiteSpecificCategories && setDefaultSiteSpecificCategories(selectedSiteSpecificCategoryOptions)
        setDefaultReportingUnits && setDefaultReportingUnits(selectedReportingUnitOptions)
        setDefaultReportingLocations && setDefaultReportingLocations(selectedReportingLocationOptions)

        const transformedData: FilterInfoProps = {
            ...filterInfo,
            cursor: undefined,
            externalIdPrefix: transformDataToFormInput(data.externalIdPrefix),
            titlePrefix: transformDataToFormInput(data.titlePrefix),
            statusExternalIds: transformDataToFormInput(data.statusExternalIds) ?? getStatusExternalIds(),
            categoryExternalId: transformDataToFormInput(data.categoryExternalId),
            subcategoryExternalId: transformDataToFormInput(data.subcategoryExternalId),
            siteSpecificCategoryExternalId: transformDataToFormInput(data.siteSpecificCategoryExternalId),
            ownerExternalId: transformDataToFormInput(data.ownerExternalId),
            assignedToExternalId: transformDataToFormInput(data.assignedToExternalId),
            reportingSiteExternalIds: transformDataToFormInput(data.reportingSiteExternalIds),
            reportingUnitExternalIds: transformDataToFormInput(data.reportingUnitExternalIds),
            reportingLocationExternalIds: transformDataToFormInput(data.reportingLocationExternalIds),
            applicationExternalId: transformDataToFormInput(data.applicationExternalId),
            sourceInfoPrefix: transformDataToFormInput(data.sourceInfoPrefix),
            dueDateGte: data.dueDate[0] ? dayjs(data.dueDate[0]).format('YYYY-MM-DD') : undefined,
            dueDateLt: data.dueDate[1] ? dayjs(data.dueDate[1]).format('YYYY-MM-DD') : undefined,
            onlyPrivate: data.onlyPrivate ? data.onlyPrivate === 'true' : undefined,
            icapActionIdPrefix: transformDataToFormInput(data.icapActionIdPrefix),
            sourceEventTitlePrefix: transformDataToFormInput(data.sourceEventTitlePrefix),
        }

        onFilter({ ...transformedData })
        setPage(0)
    }

    const clearFunction = () => {
        setAssigneeName([])
        setSelectedSiteSpecificCategoryOptions([])
        setSelectedReportingUnitOptions([])
        setSelectedReportingLocationOptions([])
        reset({
            externalIdPrefix: '',
            titlePrefix: '',
            dueDate: [null, null],
            statusExternalIds: [],
            onlyPrivate: undefined,
            categoryExternalId: [],
            ownerExternalId: [],
            assignedToExternalId: [],
            reportingSiteExternalIds: [],
            reportingUnitExternalIds: [],
            reportingLocationExternalIds: [],
            applicationExternalId: [],
            sourceInfoPrefix: '',
            icapActionIdPrefix: '',
            sourceEventTitlePrefix: '',
        })
    }

    const defaultFunction = () => {
        setSelectedSiteSpecificCategoryOptions([])
        setSelectedReportingUnitOptions([])
        setSelectedReportingLocationOptions([])

        const hasDefaultFilters = !!disableDefaultFilterFields && !!defaultFilterInfo
        const onlyPrivateAsString: string | undefined =
            defaultFilterInfo?.onlyPrivate !== undefined ? String(defaultFilterInfo.onlyPrivate) : undefined

        const baseFilters = {
            externalIdPrefix: '',
            titlePrefix: '',
            statusExternalIds: getStatusExternalIds(),
            categoryExternalId: [],
            ownerExternalId: [],
            assignedToExternalId: hasDefaultFilters || pageType === PageType.EventDetails ? [] : [activeUserExternalId],
            reportingSiteExternalIds: [],
            reportingUnitExternalIds: [],
            reportingLocationExternalIds: [],
            applicationExternalId: [],
            sourceInfoPrefix: '',
            icapActionIdPrefix: '',
            sourceEventTitlePrefix: '',
            onlyPrivateAsString: undefined,
            onlyPrivate: onlyPrivateAsString,
            dueDate: [null, null] as [Dayjs | null, Dayjs | null],
        }

        if (hasDefaultFilters) {
            if (!defaultFilterInfo.ownerExternalId) setOwnerName([])
            if (!defaultFilterInfo.assignedToExternalId) setAssigneeName([])
            reset({
                ...baseFilters,
                ...defaultFilterInfo,
                onlyPrivate: onlyPrivateAsString,
                dueDate: [
                    defaultFilterInfo?.dueDateGte ? dayjs(defaultFilterInfo.dueDateGte) : null,
                    defaultFilterInfo?.dueDateLt ? dayjs(defaultFilterInfo.dueDateLt) : null,
                ],
            })
        } else {
            setAssigneeName(pageType === PageType.EventDetails ? [] : defaultActiveUser ?? [])
            reset({ ...baseFilters, statusExternalIds: getStatusExternalIds() })
        }
    }

    const renderField = (config: FieldConfig, control: any) => {
        switch (config.fieldType) {
            case 'text':
                return (
                    <>
                        {config.preText && <Typography>{config.preText}</Typography>}
                        <GenericTextField
                            name={config.name}
                            control={control}
                            valueController={getValues(config.name as any) as string}
                            isSearchIcon
                            data-test={config.dataTest}
                            data-origin={config.dataOrigin}
                            disabled={config.disabled}
                        />
                    </>
                )
            case 'autocomplete': {
                const acConfig = config as AutocompleteFieldConfig
                return (
                    <GenericAutocomplete
                        name={acConfig.name}
                        control={control}
                        options={acConfig.options}
                        valueController={acConfig.valueController}
                        size={acConfig.size}
                        onChange={acConfig.onChange}
                        loading={acConfig.loading}
                        placeholderTextField={acConfig.placeholder}
                        onInputChange={acConfig.onInputChange}
                        data-test={acConfig.dataTest}
                        data-origin={acConfig.dataOrigin}
                        multiple={acConfig.multiple}
                        disabled={config.disabled}
                    />
                )
            }
            case 'dateRange':
                return (
                    <GenericDateRangePicker
                        name={config.name}
                        control={control}
                        size={config.size}
                        data-test={config.dataTest}
                        data-origin={config.dataOrigin}
                        disabled={config.disabled}
                        maxDate={
                            defaultFilterInfo?.kpiFilter === ActionItemStateStatusEnum.Overdue
                                ? dayjs().endOf('day')
                                : undefined
                        }
                    />
                )
            default:
                return null
        }
    }

    const fields = useMemo(() => {
        const baseFields: FieldConfig[] = [
            {
                id: 'externalIdPrefix',
                fieldType: 'text',
                label: translate('table.headers.id'),
                name: 'externalIdPrefix',
                preText: translate('table.headers.startWith'),
                dataTest: 'home_filters_modal-id_start_with_field',
                dataOrigin: 'aim',
                disabled: disableDefaultFilterFields && !!defaultFilterInfo?.externalIdPrefix,
            },
            {
                id: 'ownerExternalId',
                fieldType: 'autocomplete',
                label: translate('table.headers.owner'),
                name: 'ownerExternalId',
                options: sortedOwnerOptions,
                valueController: ownerName,
                size: 'small',
                onChange: (newValue, fieldOption) => {
                    setValue('ownerExternalId', newValue)
                    setOwnerName(fieldOption ?? [])
                },
                loading: loading,
                placeholder: translate('stepper.form.typeAtLeast3'),
                onInputChange: (event, newInputValue) => {
                    if (newInputValue.length >= 3) setUsersParam(newInputValue)
                },
                dataTest: 'home_filters_modal-owner_field',
                dataOrigin: 'aim',
                disabled: disableDefaultFilterFields && !!defaultFilterInfo?.ownerExternalId,
            },
            {
                id: 'dueDate',
                fieldType: 'dateRange',
                label: translate('table.headers.dueDate'),
                name: 'dueDate',
                size: 'small',
                dataTest: 'home_filters_modal-due_date_field',
                dataOrigin: 'aim',
                disabled:
                    disableDefaultFilterFields && (!!defaultFilterInfo?.dueDateGte || !!defaultFilterInfo?.dueDateLt),
            },
            {
                id: 'assignedToExternalId',
                fieldType: 'autocomplete',
                label: translate('table.headers.assignee'),
                name: 'assignedToExternalId',
                options: sortedAssigneeOptions,
                valueController: assigneeName,
                size: 'small',
                onChange: (newValue, fieldOption) => {
                    setValue('assignedToExternalId', newValue)
                    setAssigneeName(fieldOption ?? [])
                },
                loading: loading,
                placeholder: translate('stepper.form.typeAtLeast3'),
                onInputChange: (event, newInputValue) => {
                    if (newInputValue.length >= 3) setUsersParam(newInputValue)
                },
                dataTest: 'home_filters_modal-assignee_field',
                dataOrigin: 'aim',
                disabled: disableDefaultFilterFields && !!defaultFilterInfo?.assignedToExternalId,
            },
            {
                id: 'statusExternalIds',
                fieldType: 'autocomplete',
                label: translate('table.headers.status'),
                name: 'statusExternalIds',
                options: statusOptions,
                size: 'small',
                onChange: (newValue) => setValue('statusExternalIds', newValue),
                dataTest: 'home_filters_modal-status_field',
                dataOrigin: 'aim',
                disabled: disableDefaultFilterFields && !!defaultFilterInfo?.statusExternalIds,
            },
            {
                id: 'reportingUnitExternalIds',
                fieldType: 'autocomplete',
                label: translate('table.headers.unit'),
                name: 'reportingUnitExternalIds',
                valueController: selectedReportingUnitOptions,
                options: reportingUnitOptions,
                size: 'small',
                onChange: (newValue, fieldOption) => {
                    setValue('reportingUnitExternalIds', newValue)
                    setSelectedReportingUnitOptions(fieldOption ?? [])
                    setReportingUnitSearch(reportingSiteOptions.length == 1 ? '' : 'NULL_PARAM')
                },
                loading: isLoadingReportingUnits,
                placeholder: translate('stepper.form.typeAtLeast3'),
                onInputChange: (event, newInputValue) => {
                    if (newInputValue.length >= 3) setReportingUnitSearch(newInputValue)
                },
                dataTest: 'home_filters_modal-unit_field',
                dataOrigin: 'aim',
                disabled:
                    disableDefaultFilterFields &&
                    !!defaultFilterInfo?.reportingUnitExternalIds &&
                    pageType !== PageType.Home,
            },
            {
                id: 'reportingLocationExternalIds',
                fieldType: 'autocomplete',
                label: translate('table.headers.location'),
                name: 'reportingLocationExternalIds',
                valueController: selectedReportingLocationOptions,
                options: reportingLocationOptions,
                size: 'small',
                onChange: (newValue, fieldOption) => {
                    setValue('reportingLocationExternalIds', newValue)
                    setSelectedReportingLocationOptions(fieldOption ?? [])
                    setReportingLocationSearch(reportingSiteOptions.length == 1 ? '' : 'NULL_PARAM')
                },
                loading: isLoadingReportingLocations,
                placeholder: translate('stepper.form.typeAtLeast3'),
                onInputChange: (event, newInputValue) => {
                    if (newInputValue.length >= 3) setReportingLocationSearch(newInputValue)
                },
                dataTest: 'home_filters_modal-location_field',
                dataOrigin: 'aim',
                disabled: disableDefaultFilterFields && !!defaultFilterInfo?.reportingLocationExternalIds,
            },
            {
                id: 'onlyPrivate',
                fieldType: 'autocomplete',
                label: translate('table.headers.private'),
                name: 'onlyPrivate',
                options: isPrivateOptions,
                size: 'small',
                multiple: false,
                onChange: (newValue) => setValue('onlyPrivate', newValue),
                dataTest: 'home_filters_modal-private_field',
                dataOrigin: 'aim',
                disabled: disableDefaultFilterFields && defaultFilterInfo?.onlyPrivate !== undefined,
            },
            {
                id: 'categoryExternalId',
                fieldType: 'autocomplete',
                label: translate('table.headers.category'),
                name: 'categoryExternalId',
                options: categoryOptions,
                size: 'small',
                onChange: (newValue) => setValue('categoryExternalId', newValue),
                dataTest: 'home_filters_modal-category_field',
                dataOrigin: 'aim',
                disabled: disableDefaultFilterFields && !!defaultFilterInfo?.categoryExternalId,
            },
            {
                id: 'subcategoryExternalId',
                fieldType: 'autocomplete',
                label: translate('table.headers.subcategoryOne'),
                name: 'subcategoryExternalId',
                options: subcategory1Options,
                size: 'small',
                onChange: (newValue) => setValue('subcategoryExternalId', newValue),
                dataTest: 'home_filters_modal-subcategory_1_field',
                dataOrigin: 'aim',
                disabled: disableDefaultFilterFields && !!defaultFilterInfo?.subcategoryExternalId,
            },
            {
                id: 'siteSpecificCategoryExternalId',
                fieldType: 'autocomplete',
                label: translate('table.headers.subcategoryTwo'),
                name: 'siteSpecificCategoryExternalId',
                valueController: selectedSiteSpecificCategoryOptions,
                options: siteSpecificCategoryOptions,
                size: 'small',
                onChange: (newValue, fieldOption) => {
                    setValue('siteSpecificCategoryExternalId', newValue)
                    setSelectedSiteSpecificCategoryOptions(fieldOption ?? [])
                    setSiteSpecificCategorySearch(reportingSiteOptions.length == 1 ? '' : 'NULL_PARAM')
                },
                onInputChange: (_, newInputValue) => {
                    if (newInputValue.length >= 3) {
                        setSiteSpecificCategorySearch(newInputValue)
                    }
                },
                loading: isLoadingSiteSpecificCategories,
                placeholder: translate('stepper.form.typeAtLeast3'),
                dataTest: 'home_filters_modal-subcategory_2_field',
                dataOrigin: 'aim',
                disabled: disableDefaultFilterFields && !!defaultFilterInfo?.siteSpecificCategoryExternalId,
            },
            {
                id: 'applicationExternalId',
                fieldType: 'autocomplete',
                label: translate('table.headers.app'),
                name: 'applicationExternalId',
                options: applicationOptions,
                size: 'small',
                onChange: (newValue) => setValue('applicationExternalId', newValue),
                dataTest: 'home_filters_modal-application_field',
                dataOrigin: 'aim',
                disabled: disableDefaultFilterFields && !!defaultFilterInfo?.applicationExternalId,
            },
            {
                id: 'titlePrefix',
                fieldType: 'text',
                label: translate('table.headers.title'),
                name: 'titlePrefix',
                preText: translate('table.headers.startWith'),
                dataTest: 'home_filters_modal-title_start_with_field',
                dataOrigin: 'aim',
                disabled: disableDefaultFilterFields && !!defaultFilterInfo?.titlePrefix,
            },
            {
                id: 'sourceInfoPrefix',
                fieldType: 'text',
                label: translate('table.headers.sourceInformation'),
                name: 'sourceInfoPrefix',
                preText: translate('table.headers.startWith'),
                dataTest: 'home_filters_modal-source_information_start_with_field',
                dataOrigin: 'aim',
                disabled: disableDefaultFilterFields && !!defaultFilterInfo?.sourceInfoPrefix,
            },
        ]

        if (activeUser.applications?.[0]?.userSites?.length > 1) {
            const siteField: FieldConfig = {
                id: 'reportingSiteExternalIds',
                fieldType: 'autocomplete',
                label: translate('table.headers.site'),
                name: 'reportingSiteExternalIds',
                options: reportingSiteOptions,
                size: 'small',
                onChange: (newValue) => setValue('reportingSiteExternalIds', newValue),
                dataTest: 'home_filters_modal-site_field',
                dataOrigin: 'aim',
                disabled: disableDefaultFilterFields && !!defaultFilterInfo?.reportingSiteExternalIds,
            }

            const unitIndex = baseFields.findIndex((f) => f.id === 'reportingUnitExternalIds')

            if (unitIndex !== -1) {
                baseFields.splice(unitIndex, 0, siteField)
            } else {
                baseFields.push(siteField)
            }
        }

        if (icapActionIdFilterAvailable) {
            baseFields.push({
                id: 'icapActionIdPrefix',
                fieldType: 'text',
                label: translate('table.headers.icapActionId'),
                name: 'icapActionIdPrefix',
                preText: translate('table.headers.startWith'),
                dataTest: 'home_filters_modal_icap_action_id_start_with_field',
                dataOrigin: 'aim',
                disabled: disableDefaultFilterFields && !!defaultFilterInfo?.icapActionIdPrefix,
            })
        }

        if (sourceEventTitleFilterAvailable) {
            baseFields.push({
                id: 'sourceEventTitlePrefix',
                fieldType: 'text',
                label: translate('table.headers.sourceEventTitle'),
                name: 'sourceEventTitlePrefix',
                preText: translate('table.headers.startWith'),
                dataTest: 'home_filters_modal_source_event_title_start_with_field',
                dataOrigin: 'aim',
                disabled: disableDefaultFilterFields && !!defaultFilterInfo?.sourceEventTitlePrefix,
            })
        }

        return baseFields
    }, [
        disableDefaultFilterFields,
        defaultFilterInfo,
        sortedOwnerOptions,
        ownerName,
        loading,
        sortedAssigneeOptions,
        assigneeName,
        statusOptions,
        selectedReportingUnitOptions,
        reportingUnitOptions,
        isLoadingReportingUnits,
        selectedReportingLocationOptions,
        reportingLocationOptions,
        isLoadingReportingLocations,
        isPrivateOptions,
        categoryOptions,
        subcategory1Options,
        selectedSiteSpecificCategoryOptions,
        siteSpecificCategoryOptions,
        isLoadingSiteSpecificCategories,
        applicationOptions,
        icapActionIdFilterAvailable,
        sourceEventTitleFilterAvailable,
        reportingSiteOptions,
        sortedOwnerOptions,
        ownerName,
        loading,
        sortedAssigneeOptions,
        assigneeName,
        statusOptions,
        selectedReportingUnitOptions,
        reportingUnitOptions,
        isLoadingReportingUnits,
        selectedReportingLocationOptions,
        reportingLocationOptions,
        isLoadingReportingLocations,
        isPrivateOptions,
        categoryOptions,
        subcategory1Options,
        selectedSiteSpecificCategoryOptions,
        siteSpecificCategoryOptions,
        isLoadingSiteSpecificCategories,
        applicationOptions,
        icapActionIdFilterAvailable,
        sourceEventTitleFilterAvailable,
        reportingSiteOptions,
    ])

    const renderForm = () => (
        <Form onSubmit={handleSubmit(formSubmit)} id="filter-action-home">
            <Grid
                container
                sx={{
                    height: '100%',
                    width: isMobile ? '100%' : '600px',
                    flexDirection: 'column',
                    gap: 2,
                }}
            >
                <Grid item id="button-mobile-filter-action-home">
                    <Box sx={{ display: 'flex', gap: '8px', justifyContent: 'flex-end' }}>
                        {!disableDefaultFilterFields && (
                            <ClnButton
                                size="medium"
                                variant="outlined"
                                label={translate('table.filter.clear')}
                                onClick={clearFunction}
                                data-test="home_filters_modal-clear_button"
                                data-origin="aim"
                            />
                        )}
                        <ClnButton
                            size="medium"
                            variant="outlined"
                            label={translate('table.filter.default')}
                            onClick={defaultFunction}
                            data-test="home_filters_modal-default_button"
                            data-origin="aim"
                        />
                    </Box>
                </Grid>

                {infoMessage && (
                    <Typography
                        variant="subtitle1"
                        color="error.main"
                        sx={{
                            wordBreak: 'break-word',
                            whiteSpace: 'normal',
                            order: isMobile ? 2 : 1,
                        }}
                        data-test="home_filter_modal-info-message"
                        data-origin="aim"
                    >
                        {infoMessage}
                    </Typography>
                )}

                <Grid
                    item
                    sx={{
                        padding: '1rem 0',
                        flexGrow: 1,
                        overflowY: 'auto',
                        width: '100%',
                        height: '250px',
                        order: isMobile ? 1 : 2,
                    }}
                    id="options-filter-action-home"
                >
                    <Grid container sx={{ width: '100%' }}>
                        {fields.map((fieldConfig) => (
                            <Grid
                                key={fieldConfig.id}
                                item
                                md={6}
                                xs={12}
                                sx={{
                                    paddingRight: isTablet ? 0 : 2,
                                    paddingLeft: 0,
                                }}
                            >
                                <GenericFieldTitle isBorder fieldName={fieldConfig.label} />
                                {renderField(fieldConfig, control)}
                            </Grid>
                        ))}
                    </Grid>
                </Grid>

                <Grid item id="button-mobile-apply-filter-action-home" sx={{ order: 3 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'center', width: '100%' }}>
                        <ClnButton
                            type="submit"
                            size="medium"
                            variant="contained"
                            label={translate('table.filter.applyFilter')}
                            data-test="home_filters_modal-apply_filter_button"
                            data-origin="ui-lib"
                        />
                    </Box>
                </Grid>
            </Grid>
        </Form>
    )

    useEffect(() => {
        const onlyPrivateAsString = filterInfo.onlyPrivate ? 'true' : 'false'
        const transformedFilter = {
            ...filterInfo,
            onlyPrivate: filterInfo.onlyPrivate === undefined ? undefined : onlyPrivateAsString,
            dueDate: [
                filterInfo.dueDateGte ? dayjs(filterInfo.dueDateGte) : null,
                filterInfo.dueDateLt ? dayjs(filterInfo.dueDateLt) : null,
            ] as [Dayjs | null, Dayjs | null],
        }

        reset(transformedFilter)
    }, [filterInfo])

    return <Box>{renderForm()}</Box>
}
