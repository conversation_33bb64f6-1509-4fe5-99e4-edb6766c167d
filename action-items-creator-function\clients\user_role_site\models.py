from typing import Annotated, Optional
from clients.core.models import Node
from clients.core.validators import edge_unwraper_validator

class _User(Node):
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    email: Optional[str] = None


class _UserAzureAttribute(Node):
    user: Optional[_User] = None


class _UserComplement(Node):
    user_azure_attribute: _UserAzureAttribute | None

class UserRoleSiteResult(Node):
    role: Node | None
    reporting_site: Node | None
    user_complements: Annotated[list[_UserComplement], edge_unwraper_validator] = []