import { ExternalEntity } from '../../common'

export interface OFWAEvent extends ExternalEntity {
    refUnit?: Unit
    refSubProcess?: NamedEntity
    createdBy: string
    refCategory?: { items: NamedEntity[] }
    observationStatus?: string
    refOFWAProcess?: { items: NamedEntity[] }
    date?: string
}

interface NamedEntity extends ExternalEntity {
    name?: string
}

interface Unit extends ExternalEntity {
    description?: string
}
