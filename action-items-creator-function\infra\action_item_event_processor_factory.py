from typing import Optional
import os
import sys
from uuid import uuid4


script_dir = os.path.dirname(os.path.abspath(__file__))
project_dir = os.path.dirname(script_dir)
sys.path.append(project_dir)
from infra.action_item_creator_factory import ActionItemCreatorFactory
from infra.graphql_client_factory import GraphqlClientFactory
from infra.cognite_client_factory import CogniteClientFactory
from models.settings import Settings
from services.action_item_event_processor import ActionItemEventProcessor
from services.logging_service import LoggingService
from services.cognite_service import CogniteService
from services.graphql_service import GraphqlService


class ActionItemEventProcessorFactory:
    @staticmethod
    def create(
        settings: Settings = Settings.from_env(),
        call_id: str = str(uuid4()),
        override_token: Optional[str] = None,
    ) -> ActionItemEventProcessor:
        log = LoggingService(call_id)
        cognite_client = CogniteClientFactory.create(settings, override_token)
        graphl_client = GraphqlClientFactory.create(cognite_client, settings)
        cognite_service = CogniteService(cognite_client, settings, log)
        graphql_service = GraphqlService(graphl_client, log)

        return ActionItemEventProcessor(
            cognite_service,
            graphql_service,
            ActionItemCreatorFactory.create(settings, call_id, override_token),
            settings,
            log,
        )


if __name__ == "__main__":
    import asyncio
    import logging

    logging.basicConfig(filename="local_event_processor_log.log", level=logging.INFO)

    asyncio.run(ActionItemEventProcessorFactory.create().execute())
