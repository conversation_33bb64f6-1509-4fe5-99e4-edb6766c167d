import asyncio
from asyncio import sleep

from gql import Client, gql
from services.logging_service import LoggingService
from utils.general_utils import generate_query_only_with_node_reference


class GraphqlService:
    def __init__(
        self,
        client: Client,
        logging_service: LoggingService,
    ):
        self._client = client
        self._log = logging_service
        self._session = None

    async def get_all_results_list(
        self, query: str, list_name: str, filter: dict = {}, disable_log: bool = False
    ):
        session = await self._get_graphql_session()
        variables = {
            "filter": filter,
            "first": 1000,
            "after": None,
        }
        items = []
        retry_count = 0
        while True:
            result = []
            try:
                if disable_log:
                    self._log.disable_logs_temporarily()
                response = await session.execute(gql(query), variable_values=variables)
                result = response[list_name]
                if disable_log:
                    self._log.enable_logs()
            except Exception as e:
                if disable_log:
                    self._log.enable_logs()
                self._log.error(f"Error while fetching {list_name}: {e}")
                if retry_count > 5:
                    self._log.error(f"Max retries reached for {list_name}")
                    raise e
                retry_count += 1
                await sleep(1)

                continue

            if result is None or "items" not in result:
                break

            items.extend(self._unwrap_query_result(item) for item in result["items"])

            pageInfo = result["pageInfo"]

            if not pageInfo["hasNextPage"] or pageInfo["endCursor"] is None:
                break

            variables["after"] = pageInfo["endCursor"]

        return items

    async def get_results_list(
        self,
        query: str,
        list_name: str,
        filter: dict = {},
        fullresponse: bool | None = None,
        fields_to_process: list[str] | None = None,
    ):
        session = await self._get_graphql_session()
        variables = {
            "filter": filter,
            "first": 1000,
            "after": None,
        }
        items = []
        retry_count = 0
        response = None
        while True:
            result = []
            try:
                self._log.disable_logs_temporarily()
                response = await session.execute(gql(query), variable_values=variables)
                result = response[list_name]
                self._log.enable_logs()
                if result is None or "items" not in result:
                    break

                items.extend(
                    self._unwrap_query_result(item, fields_to_process)
                    for item in result["items"]
                )
                break
            except Exception as e:
                self._log.enable_logs()
                self._log.error(f"Error while fetching {list_name}: {e}")
                if retry_count > 5:
                    self._log.error(f"Max retries reached for {list_name}")
                    raise e
                retry_count += 1
                await sleep(1)

                continue

        return response if fullresponse else items

    async def _get_graphql_session(self):
        if self._session is None:
            self._session = await self._client.connect_async(recconecting=True)

        return self._session

    async def cleanup(self):
        if self._session is not None:
            await self._client.close_async()

    def _unwrap_query_result(
        self, result: dict, fields_to_process: list[str] | None = None
    ):
        if result is None:
            return None

        if fields_to_process is None:
            fields_to_process = []

        unwrapped_result = {}
        for key, value in result.items():
            if not isinstance(value, dict):
                unwrapped_result[key] = value
                continue

            if key in fields_to_process:
                unwrapped_result[key] = {
                    "edges": [
                        self._unwrap_query_result(v, fields_to_process)
                        for v in value.get("edges", [])
                    ],
                    "items": [
                        self._unwrap_query_result(v, fields_to_process)
                        for v in value.get("items", [])
                    ],
                }
            elif "items" in value:
                unwrapped_result[key] = [
                    self._unwrap_query_result(v, fields_to_process)
                    for v in value.get("items", [])
                ]
            else:
                unwrapped_result[key] = self._unwrap_query_result(
                    value, fields_to_process
                )

        return unwrapped_result

    async def get_existing_entities(self, user_ids: list[str] = []):
        wrapper = QueryWraper()
        unfiltered_queries = [
            wrapper.categories,
            wrapper.sub_categories,
            wrapper.site_specific_category,
            wrapper.applications,
            wrapper.reporting_units,
            wrapper.reporting_locations,
            wrapper.reporting_sites,
            wrapper.business_lines,
            wrapper.reporting_lines,
            wrapper.source_types,
            wrapper.metadata_field_types,
        ]
        filtered_queries = [
            (
                *wrapper.users,
                # {}
                # NOTE: uncomment line above and comment line below when expecting more than 5000 users (usually when running locally)
                {"externalId": {"in": user_ids}} if len(user_ids) > 0 else {},
            ),
        ]

        results = await asyncio.gather(
            *[
                self.get_all_results_list(query, list_name)
                for query, list_name in unfiltered_queries
            ]
            + [
                self.get_all_results_list(query, list_name, filter=filter)
                for query, list_name, filter in filtered_queries
            ]
        )

        return [{node["externalId"]: node for node in result} for result in results]


class QueryWraper:
    def _get_return_tuple(self, list_name: str, extra_selection: str = None):
        return (
            generate_query_only_with_node_reference(list_name, extra_selection),
            list_name,
        )

    @property
    def categories(self):
        return self._get_return_tuple("listActionItemCategory", "name")

    @property
    def sub_categories(self):
        return self._get_return_tuple("listActionItemSubCategory", "name")

    @property
    def site_specific_category(self):
        return self._get_return_tuple("listSiteSpecificCategory", "description")

    @property
    def applications(self):
        return self._get_return_tuple("listApplication", "name")

    @property
    def reporting_units(self):
        return self._get_return_tuple("listReportingUnit", "description")

    @property
    def reporting_locations(self):
        return self._get_return_tuple("listReportingLocation", "description")

    @property
    def reporting_sites(self):
        extra_selection = """
            description
            reportingUnits {
                items {
                    externalId
                    space
                }
            }
            reportingLocations {
                items {
                    externalId
                    space
                }
            }
        """
        return self._get_return_tuple("listReportingSite", extra_selection)

    @property
    def business_lines(self):
        return self._get_return_tuple("listBusinessLine", "description")

    @property
    def reporting_lines(self):
        return self._get_return_tuple("listReportingLine", "description")

    @property
    def users(self):
        extra_selection = """
            user {
                displayName
            }
        """
        return self._get_return_tuple("listUserAzureAttribute", extra_selection)

    @property
    def source_types(self):
        return self._get_return_tuple("listActionSourceType", "name")

    @property
    def metadata_field_types(self):
        extra_selection = """
            name
            possibleValues
        """
        return self._get_return_tuple("listMetadataFieldType", extra_selection)


if __name__ == "__main__":
    service = GraphqlService(Client())
    test = {
        "item1": 1,
        "item2": {"items": [{"item3": 3}]},
        "item4": {
            "item5": 5,
            "item6": {
                "items": [
                    {"item7": 7, "item8": {"items": [{"item9": 9}]}},
                    {"item10": 10},
                ]
            },
        },
        "item11": {"items": []},
    }
    print(service._unwrap_query_result(test))
