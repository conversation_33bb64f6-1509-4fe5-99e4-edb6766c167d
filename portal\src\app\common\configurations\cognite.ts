import { environment } from '@/app/common/configurations/environment'

export const cognite = {
    appId: environment.cogniteAppId ?? '',
    project: environment.cogniteProject ?? '',
    baseUrl: environment.cogniteBaseUrl ?? '',
    cogniteFdmGlobalSiteSpace: environment.cogniteFdmGlobalSiteSpace ?? '',
    cogniteFdmGlobalUnitSpace: environment.cogniteFdmGlobalUnitSpace ?? '',
    cogniteFdmProjectCode: environment.cogniteFdmProjectCode ?? '',
    cogniteFdmModel: environment.cogniteFdmModel ?? '',
    cogniteFdmSuffixModelSpace: environment.cogniteFdmSuffixModelSpace ?? '',
    cogniteFdmSuffixInstancesSpace: environment.cogniteFdmSuffixInstancesSpace ?? '',
    cogniteFdmSuffixProtectedSpace: environment.cogniteFdmSuffixProtectedSpace ?? '',
    cogniteFdmSuffixStaticSpace: environment.cogniteFdmSuffixStaticSpace ?? '',
    cogniteFusionUrl: environment.cogniteFusionUrl ?? '',
    cogniteApiVersion: environment.cogniteApiVersion ?? '',
    cogniteXCdpApp: environment.cogniteXCdpApp ?? '',
    exploreTimeSeriesUrl: '',
    exploreDataSetUrl: '',
    exploreAssetUrl: '',
    exploreEventUrl: '',
    defaultGraphQlListLimit: Number(environment.cogniteDefaultGraphQlListLimit ?? 1000),
    dataSetId: Number(environment.cogniteDataSetId ?? 0),
}

cognite.exploreTimeSeriesUrl = `${cognite.cogniteFusionUrl}/explore/timeSeries`
cognite.exploreDataSetUrl = `${cognite.cogniteFusionUrl}/data-sets/data-set`
cognite.exploreAssetUrl = `${cognite.cogniteFusionUrl}/explore/asset`
cognite.exploreEventUrl = `${cognite.cogniteFusionUrl}/explore/event`
