import os
import sys
from typing import Annotated

from annotated_types import Len
from pydantic import BaseModel, Field

script_dir = os.path.dirname(os.path.abspath(__file__))
project_dir = os.path.dirname(script_dir)
sys.path.append(project_dir)


class Attachment(BaseModel):
    received_external_id: str = Field(min_length=1)
    target_external_id: str | None = Field(min_length=1, default=None)
    source_event_external_ids: Annotated[list[str], Len(min_length=1)] = Field(
        default_factory=list,
    )
    file_size: str | None = None
    name: str | None = Field(min_length=1, default=None)
    mime_type: str | None = Field(min_length=1, default=None)
    target_dataset_id: int | None = None
    user: str | None = Field(min_length=1, default=None)
