export const ActionUpdateStatusEnum = {
    CurrentTasks: 'Current tasks',
    DueWithin30Days: 'Due Within 30 days',
    DueWithin7Days: 'Due Within 7 days',
    Overdue: 'Overdue',
} as const

export const AllActionStatusExternalIdEnum = {
    ApprovalRejected: 'ACTS-approvalRejected',
    Assigned: 'ACTS-assigned',
    ChallengePeriod: 'ACTS-challengePeriod',
    DueDateExtension: 'ACTS-dueDateExtensionPeriod',
    PendingApproval: 'ACTS-pendingApproval',
    PendingVerification: 'ACTS-pendingVerification',
    ReassignmentPeriod: 'ACTS-reassignmentPeriod',
    VerificationRejected: 'ACTS-verificationRejected',
    Completed: 'ACTS-completed',
    Cancelled: 'ACTS-cancelled',
    Active: 'ACTS-active',
    Inactive: 'ACTS-inactive',
    Draft: 'ACTS-draft',
} as const

export const ActionStatusExternalIdClearEnum = {
    ApprovalRejected: 'ACTS-approvalRejected',
    Assigned: 'ACTS-assigned',
    ChallengePeriod: 'ACTS-challengePeriod',
    DueDateExtension: 'ACTS-dueDateExtensionPeriod',
    PendingApproval: 'ACTS-pendingApproval',
    PendingVerification: 'ACTS-pendingVerification',
    ReassignmentPeriod: 'ACTS-reassignmentPeriod',
    VerificationRejected: 'ACTS-verificationRejected',
    Completed: 'ACTS-completed',
    Cancelled: 'ACTS-cancelled',
} as const

export const ActionStatusExternalIdHomeDefaultEnum = {
    ApprovalRejected: 'ACTS-approvalRejected',
    Assigned: 'ACTS-assigned',
    ChallengePeriod: 'ACTS-challengePeriod',
    DueDateExtension: 'ACTS-dueDateExtensionPeriod',
    PendingApproval: 'ACTS-pendingApproval',
    PendingVerification: 'ACTS-pendingVerification',
    ReassignmentPeriod: 'ACTS-reassignmentPeriod',
    VerificationRejected: 'ACTS-verificationRejected',
} as const

export const ActionStatusExternalIdHomeClosedDefaultEnum = {
    Completed: 'ACTS-completed',
    Cancelled: 'ACTS-cancelled',
} as const

export enum RestrictedStatus {
    INACTIVE = 'Inactive',
    ACTIVE = 'Active',
    COMPLETED = 'Completed',
    DELETED = 'Deleted',
    CANCELLED = 'Cancelled',
}

export enum ActionStatusQuery {
    INCLUDE = 'Include',
    EXCLUDE = 'Exclude',
}
