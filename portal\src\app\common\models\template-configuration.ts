import { ExternalEntity } from './common'
import { NodeOf } from './node-of'
import { UpsertBaseWithName } from './upsert/upsertActionItemChangeRequest'

export interface TemplateConfiguration extends ExternalEntity {
    name: string
    description?: string
    users?: NodeOf<UpsertBaseWithName | undefined>[] | null
    roles?: NodeOf<UpsertBaseWithName | undefined>[] | null
}
export interface TemplateConfigForm {
    name: string
    description?: string
    userName?: UserName
    roles?: string[]
    users?: string[]
    originalUsers?: string[]
    originalRoles?: string[]
}
export interface UserName {
    id: string
    label?: string
}
