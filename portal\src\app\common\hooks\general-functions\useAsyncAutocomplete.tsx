import { useState, useMemo } from 'react'
import { AzureFunctionClient } from '../../clients/azure-function-client'
import useDebounce from './useDebounce'
import { useDebouncedFetch } from './useDebouncedFetch'
import { transformOptions } from '../../utils/transform-options-for-filter'
import { AutocompleteOption } from '@/app/components/FieldsComponent/GenericAutocomplete'

export function useAsyncAutocomplete<TRequest>(
    defaultSelected: AutocompleteOption[],
    fetchFunction: (client: AzureFunctionClient, request: TRequest) => Promise<any>,
    buildRequest: (search: string) => TRequest,
    skipCondition: (request: TRequest) => boolean,
    labelKey: string = 'description',
    debounceDelay = 500,
    singleSite: boolean = false
) {
    const [selectedOptions, setSelectedOptions] = useState<AutocompleteOption[]>(defaultSelected)
    const [search, setSearch] = useState<string>(singleSite ? '' : 'NULL_PARAM')

    const debouncedSearch = useDebounce(search, debounceDelay)

    const request = useMemo(() => buildRequest(debouncedSearch), [buildRequest, debouncedSearch])

    const { data, loading } = useDebouncedFetch({
        fetchFunction,
        param: request,
        skipCondition,
        debounceDelay,
    })

    const options = useMemo(() => {
        const combined = [...transformOptions(data ?? [], labelKey), ...selectedOptions]
        const uniqueMap = new Map(combined.map((opt) => [opt.value, opt]))
        return Array.from(uniqueMap.values()).sort((a, b) => a.label.localeCompare(b.label))
    }, [data, labelKey, selectedOptions])

    return {
        selectedOptions,
        setSelectedOptions,
        search,
        setSearch,
        options,
        loading,
    }
}
