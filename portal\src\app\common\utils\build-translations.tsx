import { translate } from '@celanese/celanese-ui'
import { toCamelCase } from './transform-options-for-filter'

export const buildTranslations = <T extends { externalId: string; name: string }>(
    items: T[],
    prefix: string
): Record<string, string> =>
    items.reduce(
        (acc, item) => {
            acc[item.externalId] = translate(`${prefix}.${toCamelCase(item.name)}`)
            return acc
        },
        {} as Record<string, string>
    )
