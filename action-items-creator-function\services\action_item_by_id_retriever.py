from typing import Any

from pydantic import ValidationError

from models.settings import Settings
from models.upsert_details_request import UpsertDetailsRequest
from services.cognite_service import CogniteService
from services.graphql_service import GraphqlService
from services.logging_service import LoggingService


class ActionItemByIdRetriever:
    """Retrieve action item details using Cognite and GraphQL services."""

    def __init__(
        self,
        cognite_service: CogniteService,
        cognite_service_function: CogniteService,
        graphql_service: GraphqlService,
        graphql_service_function: GraphqlService,
        settings: Settings,
        logging_service: LoggingService,
    ) -> None:
        """Initialize the ActionItemByIdRetriever."""
        self._cognite_service_function = cognite_service_function
        self._cognite_service = cognite_service
        self._graphql_service_function = graphql_service_function
        self._graphql_service = graphql_service
        self._log = logging_service
        self._settings = settings

    async def get_action_item_by_template_id(
        self,
        action_item_request: Any,
    ) -> tuple[dict, bool]:
        """
        Retrieve action item details using its template configuration ID.

        Args:
            action_item_request: Request data containing the template configuration ID.

        Returns:
            A tuple containing the action item data and a boolean status.

        """
        item_request: UpsertDetailsRequest
        try:
            item_request = UpsertDetailsRequest(**action_item_request)
        except ValidationError as err:
            return self.handle_exception(
                "validate action item by template id request",
                err,
                self._log,
            )

        action_details = await self._get_action_by_id(
            template_config_id=item_request.template_config_id,
        )

        approval_workflow_external_id = (
            approval_workflow.get("externalId")
            if (
                approval_workflow := action_details.get("listAction", {}).get(
                    "approvalWorkflow",
                )
            )
            else None
        )

        action_details_step = await self._get_workflow_step(
            approval_workflow_external_id,
        )

        action_details["listApprovalWorkflowStep"] = action_details_step

        return action_details, True

    async def _get_action_by_id(
        self,
        template_config_id: str | None = None,
    ) -> dict:
        """
        Fetch action item details based on template configuration ID.

        Args:
            template_config_id: The external ID of the template configuration.

        Returns:
            A dictionary with action item data and associated item links.

        """
        if template_config_id:
            query_filter = {
                "templateConfiguration": {"externalId": {"eq": template_config_id}},
            }
        else:
            return {"listAction": [], "listActionItemLink": []}

        action_data = await self._graphql_service.get_results_list(
            ACTION_ITEM_ID,
            "listAction",
            query_filter,
            fullresponse=True,
        )

        if not action_data or len(action_data) == 0:
            return {"listAction": [], "listActionItemLink": []}

        if isinstance(action_data, list):
            action_data = action_data[0]

        list_action = action_data.get("listAction", {}).get("items", [])
        list_action_item_link = action_data.get("listActionItemLink", {}).get(
            "items",
            [],
        )

        return {
            "listAction": list_action[0] if list_action else {},
            "listActionItemLink": list_action_item_link,
        }

    async def _get_workflow_step(
        self,
        external_id: str | None,
    ) -> dict[str, Any] | list:
        """
        Retrieve approval workflow steps by workflow external ID.

        Args:
            external_id: The external ID of the approval workflow.

        Returns:
            A list or dictionary of workflow steps.

        """
        if external_id is None:
            return []

        query_filter = {"approvalWorkflow": {"externalId": {"eq": external_id}}}

        approval_workflow_step_data = await self._graphql_service.get_results_list(
            APPROVAL_WORKFLOW_STEP,
            "listApprovalWorkflowStep",
            query_filter,
            fields_to_process=["users", "roles"],
        )

        if not approval_workflow_step_data or len(approval_workflow_step_data) == 0:
            return []

        return approval_workflow_step_data

    def handle_exception(
        self,
        err: str,
        e: ValidationError,
        logger: LoggingService,
    ) -> tuple[dict, bool]:
        """
        Handle a validation exception and logs the error.

        Args:
            err: Context message for the error.
            e: The validation error raised.
            logger: Logging service used to log the error.

        Returns:
            A tuple with an empty dictionary and a `False` status.

        """
        msg = f"The {err} operation failed with error: {e}"
        logger.error(msg)
        return {}, False


ACTION_ITEM_ID = """
query listAction($filter: _ListActionFilter, $after: String) {
  listAction(filter: $filter, first: 1, after: $after) {
    items {
      externalId
      space
      title
      assignmentDate
      dueDate
      displayDueDate
      approvalDate
      verificationDate
      conclusionDate
      description
      attachments {
        externalId
        name
        mimeType
        uploadedTime
        metadata
        source
      }
      actionItemKind{
        externalId
        space
      }
      reportingUnit {
        externalId
        description
        name
        space
      }
      currentStatus {
        externalId
        name
        description
      }
      owner {
        azureUserId
        companyName
        department
        externalId
        space
        user {
          email
          lastName
          space
          externalId
          firstName
        }
      }
      assignees {
        items {
          user {
            externalId
            space
            email
            lastName
            firstName
          }
        }
      }
      assignedTo {
        azureUserId
        companyName
        externalId
        space
        user {
          externalId
          firstName
          lastName
          space
          email
        }
      }
      sourceInformation
      voeActionItem
      approvalWorkflow {
        externalId
        space
        description
        currentStep
        status {
          externalId
          space
          name
          description
        }
      }
      category {
        externalId
        space
        name
        description
      }
      subCategory {
        externalId
        name
        description
      }
      siteSpecificCategory {
        externalId
        name
        description
      }
      application {
        externalId
        name
        alias
      }
      reportingSite {
        externalId
        space
        siteCode
        name
      }
      reportingLocation {
        externalId
        name
        description
      }
      assignmentDate
      assigneeComment
      actionItemKind {
        name
        description
      }
      recurrenceInstance {
        externalId
        space
        description
        recurrenceType {
          externalId
          space
          name
          description
        }
        weekDays
        months
        dayOfTheMonth
        quarters
        monthOfTheYear
        nextDates
        startDate
        endDate
      }
      estimatedCost
      estimatedGrade
      priceCurrencyKey
      isPlantShutdownRequired
      actionTaken
      priority
      reportingLine {
        externalId
        name
        description
      }
      sourceId
      sourceType {
        externalId
        name
      }
      isTemplate
      templateConfiguration {
        externalId
      }
      evidenceRequired
      viewOnly
      isPrivate
    }
  }
  listActionItemLink(filter: {action: $filter}, first: 100, after: $after) {
    items {
      externalId
      link
      description
      action {
        externalId
      }
    }
  }
}
"""

APPROVAL_WORKFLOW_STEP = """
query listApprovalWorkflowStep($filter: _ListApprovalWorkflowStepFilter, $after: String) {
  listApprovalWorkflowStep(filter: $filter, first: 100, after: $after) {
    items {
        externalId
        space
        description
        approvalCondition {
            externalId
            space
            name
        }
        approvalWorkflowConsentType{
            externalId
            space
            name
        }
        roles {
            items {
                externalId
                space
                name
            }
        }
        status {
            externalId
            name
            description
            space
        }
        users {
            edges {
                externalId
                space
            }
            items {
                externalId
                email
                firstName
                lastName
            }
        }
    }
  }
}
"""
