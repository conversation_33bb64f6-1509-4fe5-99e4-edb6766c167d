'use client'
import { Box, useMediaQuery, useTheme } from '@mui/material'
import { useState } from 'react'
import { useAuthGuard } from '@/app/common/hooks/useAuthGuard'
import { ActionStatusExternalIdClearEnum, RestrictedStatus } from '@/app/common/enums/ActionItemStatusEnum'
import { AzureFunctionClient } from '@/app/common/clients/azure-function-client'
import ActionFormDetailsEvent from './ActionFormDetailsEvent'
import GenericFieldTitle from '../../FieldsComponent/GenericFieldTitle'
import { SourceEvent } from '@/app/common/models/source-event'
import { ActionButton, ClnButtonProps, MatIcon } from '@celanese/ui-lib'
import { FilterInfoProps } from '../../ActionTable/HomeFilter'
import { translate } from '@/app/common/utils/generate-translate'
import { AutocompleteOption } from '../../FieldsComponent/GenericAutocomplete'
import { ActionTable } from '../../ActionTable'
import { UserRolesPermission } from '@celanese/celanese-sdk'
import { PageType } from '@/app/common/enums/PageTypeEnum'
import ActionDetails from '../../ActionDetails'

type ActionsTabProps = {
    id: string
    sourceEvent?: SourceEvent
    activeUser?: UserRolesPermission
    showForm: boolean
    isActionDetailsOpen: boolean
    setIsActionDetailsOpen: (value: boolean) => void
    setShowForm: (value: boolean) => void
    handleError: (err: any) => void
}

export default function ActionsTab({
    id,
    sourceEvent,
    activeUser,
    showForm,
    isActionDetailsOpen,
    setIsActionDetailsOpen,
    handleError,
    setShowForm,
}: ActionsTabProps) {
    const client = new AzureFunctionClient()

    const theme = useTheme()
    const isMobile = useMediaQuery(theme.breakpoints.down('md'))

    const { checkPermissionsFromRoutes } = useAuthGuard()

    const [currentPage, setCurrentPage] = useState<number>(0)
    const [memoryPage, setMemoryPage] = useState<number>(0)
    const [refetchActions, setRefetchActions] = useState<boolean>(false)

    const [detailsActionId, setDetailsActionId] = useState<string>()

    const storedFilterInfo = sessionStorage.getItem(`event-details-filterInfo-action-tab`)
    const parsedFilterInfo = storedFilterInfo ? JSON.parse(storedFilterInfo) : {}

    const [siteSpecificCategories, setSiteSpecificCategories] = useState<AutocompleteOption[]>(
        parsedFilterInfo?.siteSpecificCategories || []
    )
    const [reportingUnits, setReportingUnits] = useState<AutocompleteOption[]>(parsedFilterInfo?.reportingUnits || [])
    const [reportingLocations, setReportingLocations] = useState<AutocompleteOption[]>(
        parsedFilterInfo?.reportingLocations || []
    )

    const [ownerName, setOwnerName] = useState<AutocompleteOption[]>(
        parsedFilterInfo?.ownerName ? parsedFilterInfo?.ownerName : []
    )
    const [assigneeName, setAssigneeName] = useState<AutocompleteOption[]>(
        parsedFilterInfo?.assignedName ? parsedFilterInfo?.assignedName : []
    )

    const [filterInfo, setFilterInfo] = useState<FilterInfoProps>({
        reportingSiteExternalId: sourceEvent?.reportingSite?.externalId ?? '-',
        statusExternalIds: Object.values(ActionStatusExternalIdClearEnum),
        pageSize: 10,
        activeUserEmail: activeUser?.email,
        searchProperties: ['title'],
        ...parsedFilterInfo?.filter,
        sourceEventExternalId: id,
        search: '',
    })

    const additionalButtons: ClnButtonProps[] = [
        {
            route: '/new-action-item',
            label: isMobile ? '' : translate('table.newActionItem'),
            startIcon: isMobile ? <MatIcon icon="add" /> : undefined,
            variant: isMobile ? 'text' : 'contained',
            sxProps: isMobile
                ? {
                      color: 'primary.main !important',
                      padding: '8px 12px',
                      minWidth: '12px !important',
                      '& .MuiButton-startIcon': {
                          marginRight: 0,
                      },
                  }
                : undefined,
            onClick: () => {
                setShowForm(true)
                localStorage.setItem('isEditForm', 'true')
            },
        },
    ].filter((item) => checkPermissionsFromRoutes(item.route)) as ActionButton[]

    const onCloseDetails = () => {
        setIsActionDetailsOpen(false)
        setDetailsActionId(undefined)
        localStorage.removeItem('eventDetails')
    }

    const onRefetchDetails = () => {
        setIsActionDetailsOpen(false)
        setDetailsActionId(undefined)
        localStorage.removeItem('eventDetails')
        setRefetchActions(true)
    }

    const renderContent = () => {
        if (showForm) {
            if (!activeUser) {
                return null
            }

            return (
                <ActionFormDetailsEvent
                    id={id}
                    sourceEvent={sourceEvent}
                    activeUser={activeUser}
                    sourceInformation={undefined}
                    closeForm={setShowForm}
                    setRefatchActions={() => {
                        setRefetchActions(true)
                        setShowForm(false)
                    }}
                />
            )
        }

        if (isActionDetailsOpen) {
            return (
                <ActionDetails
                    id={detailsActionId ?? ''}
                    siteId={sourceEvent?.reportingSite?.externalId}
                    activeUser={activeUser!}
                    onClose={onCloseDetails}
                    onRefetch={onRefetchDetails}
                />
            )
        }

        return (
            <Box
                id={'action-item-tab-event-details'}
                sx={{
                    width: '100%',
                    height: '100%',
                    display: 'grid',
                    alignItems: 'start',
                    gridTemplateColumns: '1fr',
                    marginRight: '10px',
                    marginTop: '1rem',
                }}
            >
                <Box sx={{ overflow: 'auto' }}>
                    <Box paddingLeft={1}>
                        <GenericFieldTitle fieldName={translate('source-event.action-items')} isSubHeader />
                    </Box>
                    <ActionTable
                        id={`event-details-action-tab`}
                        sourceId={id}
                        setFilterInfo={setFilterInfo}
                        filterInfo={filterInfo}
                        statusNameListForIncludeOrExclude={[
                            RestrictedStatus.INACTIVE,
                            RestrictedStatus.ACTIVE,
                            RestrictedStatus.DELETED,
                        ]}
                        setCurrentPage={setCurrentPage}
                        currentPage={currentPage}
                        activeUser={activeUser!}
                        client={client}
                        siteIds={[sourceEvent?.reportingSite?.externalId ?? '-']}
                        handleFetchFailure={handleError}
                        memoryPage={memoryPage}
                        setMemoryPage={setMemoryPage}
                        ownerName={ownerName}
                        setOwnerName={setOwnerName}
                        assigneeName={assigneeName}
                        setAssigneeName={setAssigneeName}
                        showBulkUpload={false}
                        additionalButtons={additionalButtons}
                        refetchActions={refetchActions}
                        setRefetchActions={setRefetchActions}
                        showLink={false}
                        pageType={PageType.EventDetails}
                        siteSpecificCategories={siteSpecificCategories}
                        reportingUnits={reportingUnits}
                        reportingLocations={reportingLocations}
                        setSiteSpecificCategories={setSiteSpecificCategories}
                        setReportingLocations={setReportingUnits}
                        setReportingUnits={setReportingLocations}
                    />
                </Box>
            </Box>
        )
    }

    return <>{renderContent()}</>
}
