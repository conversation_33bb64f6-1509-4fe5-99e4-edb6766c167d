import { gql } from '@apollo/client'
import { useState, useEffect } from 'react'
import { useGraphqlQuery } from '../cognite/useGraphqlQuery'
import { SourceEventStatus } from '../../models/source-event'
import { SourceEventStatusDeletedEnum } from '../../enums/SourceEventStatusEnum'

const buildEventStatusQuery = (): string => {
    const filters: string[] = []

    filters.push(`{ not: { externalId: { eq: "${SourceEventStatusDeletedEnum.deleted}" } } }`)

    const queryFilter = `{ and: [ ${filters.join(', ')} ] }`

    return `
        query GetAllEventStatus {
            listSourceEventStatus(
                filter: ${queryFilter}
                , first: 1000
                , sort: { name: ASC }
            ) {
                items {
                    name
                    externalId
                    space
                }
            }
        }
    `
}

export const useSourceEventStatuses = () => {
    const query = buildEventStatusQuery()
    const { data: fdmData } = useGraphqlQuery<SourceEventStatus>(gql(query), 'listSourceEventStatus', {})

    const [resultData, setResultData] = useState<{ data: SourceEventStatus[]; loading: boolean }>({
        data: [],
        loading: true,
    })

    useEffect(() => {
        if (fdmData.length == 0) {
            setResultData({ data: [], loading: false })
        } else {
            setResultData({ data: fdmData, loading: false })
        }
    }, [fdmData])

    return {
        eventStatus: resultData.data,
    }
}
